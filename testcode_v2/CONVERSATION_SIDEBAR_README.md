# 多轮分析会话侧边栏功能

## 功能概述

在多轮分析页面的左侧新增了会话侧边栏，提供以下功能：

### 主要功能

1. **历史会话列表**
   - 显示当前项目的所有活跃会话
   - 按最后更新时间倒序排列
   - 显示会话标题、最后查询内容、轮次数量等信息

2. **新建会话**
   - 快速创建新的分析会话
   - 支持从侧边栏和主界面两种方式创建

3. **会话搜索**
   - 支持按会话标题和查询内容搜索
   - 实时过滤显示匹配结果

4. **会话管理**
   - 重命名会话标题
   - 删除不需要的会话
   - 切换到不同的会话

5. **侧边栏折叠**
   - 支持折叠/展开侧边栏
   - 折叠状态下显示简化的操作按钮

## 界面设计

### 展开状态
- 宽度：320px
- 包含完整的会话列表和搜索功能
- 显示会话详细信息

### 折叠状态
- 宽度：60px
- 显示展开按钮、新建按钮和会话数量

### 会话项目显示
- 会话标题（支持重命名）
- 最后查询内容预览
- 更新时间（智能显示：今天显示时间，昨天显示"昨天"，更早显示天数）
- 轮次数量标签
- 当前会话标识

## 技术实现

### 前端组件
- `ConversationSidebar.tsx` - 主要侧边栏组件
- `ConversationSidebar.css` - 样式文件
- 集成到 `MultiRoundAnalysis.tsx` 中

### API接口
- `GET /conversations/projects/{projectId}/conversations` - 获取项目会话列表
- `PUT /conversations/{conversationId}` - 更新会话信息
- `DELETE /conversations/{conversationId}` - 删除会话

### 状态管理
- 使用React Hooks管理组件状态
- 支持实时搜索和过滤
- 自动刷新会话列表

## 用户体验优化

1. **视觉反馈**
   - 当前会话高亮显示
   - 悬停效果和动画过渡
   - 加载状态指示

2. **交互优化**
   - 点击会话项快速切换
   - 右键菜单支持重命名和删除
   - 搜索框支持清空和实时搜索

3. **响应式设计**
   - 支持不同屏幕尺寸
   - 移动端友好的交互方式

## 使用方法

1. **查看历史会话**
   - 在多轮分析页面左侧查看所有会话
   - 点击任意会话项切换到该会话

2. **创建新会话**
   - 点击侧边栏顶部的"新建"按钮
   - 或点击折叠状态下的"+"按钮

3. **搜索会话**
   - 在搜索框中输入关键词
   - 支持搜索会话标题和查询内容

4. **管理会话**
   - 点击会话项右侧的"..."菜单
   - 选择"重命名"或"删除"操作

5. **折叠侧边栏**
   - 点击侧边栏标题右侧的"←"按钮
   - 或点击折叠状态下的展开按钮

## 注意事项

1. **数据同步**
   - 会话列表会在项目切换时自动刷新
   - 删除会话后会自动更新列表

2. **权限控制**
   - 只显示当前用户有权限访问的会话
   - 删除操作需要确认

3. **性能优化**
   - 使用虚拟滚动处理大量会话
   - 搜索结果实时过滤，无需额外请求

## 后续优化计划

1. **功能增强**
   - 会话分组和标签功能
   - 会话导出和分享
   - 会话模板功能

2. **性能优化**
   - 分页加载历史会话
   - 缓存机制优化

3. **用户体验**
   - 拖拽排序功能
   - 快捷键支持
   - 更多自定义选项 