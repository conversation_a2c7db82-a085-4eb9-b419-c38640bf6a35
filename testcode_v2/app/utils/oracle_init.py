import os
import logging
import cx_Oracle

# 配置日志
logger = logging.getLogger(__name__)

# 标记Oracle客户端是否已初始化
_is_oracle_initialized = False
_oracle_client_version = None

def init_oracle_client():
    """
    初始化Oracle客户端库。
    这个函数会检查客户端是否已经初始化，如果已初始化则跳过。
    
    Returns:
        bool: 是否成功初始化
    """
    global _is_oracle_initialized, _oracle_client_version
    
    # 如果已经初始化，直接返回
    if _is_oracle_initialized:
        logger.info(f"Oracle客户端已初始化，版本: {_oracle_client_version}")
        return True
    
    try:
        # 检查环境变量中是否设置了Oracle客户端路径
        oracle_client_path = os.environ.get('ORACLE_HOME')
        if oracle_client_path:
            logger.info(f"使用环境变量配置的Oracle客户端路径: {oracle_client_path}")
            # 如果有自定义配置，使用cx_Oracle.init_oracle_client来显式初始化
            cx_Oracle.init_oracle_client(lib_dir=oracle_client_path)
            logger.info("成功初始化Oracle客户端库")
            _is_oracle_initialized = True
        else:
            # 尝试使用系统默认路径初始化
            try:
                cx_Oracle.init_oracle_client()
                logger.info("使用系统默认路径成功初始化Oracle客户端库")
                _is_oracle_initialized = True
            except Exception as e:
                logger.warning(f"无法使用默认路径初始化Oracle客户端: {str(e)}")
                # 检查常见的Oracle客户端路径
                common_oracle_paths = [
                    "/opt/oracle/instantclient",
                    "/opt/oracle/instantclient_23_8",
                    "/opt/oracle/instantclient_21_8",
                    "/opt/oracle/instantclient_21_1",
                    "/opt/oracle/instantclient_19_8"
                ]
                
                for path in common_oracle_paths:
                    if os.path.exists(path):
                        try:
                            cx_Oracle.init_oracle_client(lib_dir=path)
                            logger.info(f"使用路径 {path} 成功初始化Oracle客户端库")
                            _is_oracle_initialized = True
                            break
                        except Exception as e:
                            logger.warning(f"尝试使用路径 {path} 初始化Oracle客户端失败: {str(e)}")
        
        # 记录客户端版本信息
        if _is_oracle_initialized:
            _oracle_client_version = ".".join(map(str, cx_Oracle.clientversion()))
            logger.info(f"Oracle客户端版本: {_oracle_client_version}")
            return True
        else:
            logger.error("所有初始化尝试都失败了")
            return False
            
    except Exception as e:
        logger.error(f"初始化Oracle客户端时发生错误: {str(e)}")
        return False

def get_oracle_client_version():
    """获取Oracle客户端版本
    
    Returns:
        str: 客户端版本字符串，如果未初始化则返回None
    """
    return _oracle_client_version

def is_oracle_initialized():
    """检查Oracle客户端是否已初始化
    
    Returns:
        bool: 是否已初始化
    """
    return _is_oracle_initialized

def reconnect_oracle(host, port, service_name, username, password):
    """
    尝试初始化Oracle客户端并重新连接
    
    Args:
        host: Oracle服务器主机名或IP
        port: 端口号
        service_name: 服务名
        username: 用户名
        password: 密码
        
    Returns:
        tuple: (connection, cursor) 如果成功，否则 (None, None)
    """
    global _is_oracle_initialized
    
    try:
        # 确保Oracle客户端已初始化
        if not _is_oracle_initialized and not init_oracle_client():
            raise ValueError("无法初始化Oracle客户端库")
        
        # 创建连接
        dsn = cx_Oracle.makedsn(host, port, service_name=service_name)
        connection = cx_Oracle.connect(user=username, password=password, dsn=dsn)
        cursor = connection.cursor()
        
        return connection, cursor
    except cx_Oracle.DatabaseError as e:
        error_msg = str(e)
        if "DPI-1047" in error_msg:
            # 尝试重新初始化
            _is_oracle_initialized = False
            logger.warning("尝试重新初始化Oracle客户端...")
            
            # 如果重新初始化成功，再次尝试连接
            if init_oracle_client():
                try:
                    dsn = cx_Oracle.makedsn(host, port, service_name=service_name)
                    connection = cx_Oracle.connect(user=username, password=password, dsn=dsn)
                    cursor = connection.cursor()
                    return connection, cursor
                except Exception as e2:
                    logger.exception(f"初始化成功但连接失败: {str(e2)}")
        
        logger.exception(f"Oracle连接失败: {error_msg}")
        return None, None 