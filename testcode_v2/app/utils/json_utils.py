from decimal import Decimal
import json
from datetime import datetime, date, time

# 添加自定义JSON编码器，处理Decimal、datetime和Oracle LOB类型
class DecimalEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, Decimal):
            return float(obj)  # 将Decimal转换为float
        elif isinstance(obj, (datetime, date)):
            return obj.isoformat()  # 将日期时间转换为ISO 8601格式字符串
        elif isinstance(obj, time):
            return obj.isoformat()  # 将时间转换为ISO 8601格式字符串
        # 处理Oracle LOB对象
        elif str(type(obj)).find('cx_Oracle.LOB') >= 0:
            try:
                # 尝试将LOB读取为文本
                if hasattr(obj, 'read'):
                    content = obj.read()
                    # 尝试将二进制内容解码为字符串
                    if isinstance(content, bytes):
                        try:
                            return content.decode('utf-8')
                        except UnicodeDecodeError:
                            return f"[二进制LOB数据，长度: {len(content)}字节]"
                    return str(content)
                return "[LOB对象]"
            except Exception as e:
                return f"[无法读取LOB对象: {str(e)}]"
        return super(DecimalEncoder, self).default(obj) 