from typing import Generator, Optional

from fastapi import Depends, Request
from sqlalchemy.orm import Session

from app.core.middleware.auth import get_current_user
from app.core.security import decode_token
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from app.core.exceptions import UnauthorizedException
from app.db.session import get_db
from app.models.administrator import Administrator
from app.services import administrator as admin_service

security = HTTPBearer()


def get_request_id(request: Request) -> Optional[str]:
    """
    获取请求ID
    """
    return getattr(request.state, "request_id", None)


def get_current_user_id(current_user = Depends(get_current_user)) -> str:
    """
    获取当前用户ID
    """
    return current_user.get("sub")


def get_db_with_request_id(
    request: Request,
    db = Depends(get_db)
) -> Generator:
    """
    获取数据库会话，并将request_id注入到会话上下文中
    """
    # 获取请求ID
    request_id = get_request_id(request)
    
    # 将request_id添加到数据库会话属性中
    if hasattr(db, "info") and request_id:
        db.info.update({"request_id": request_id})
    
    yield db


def get_current_admin(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> Administrator:
    """
    获取当前管理员
    验证当前用户是否为管理员
    """
    try:
        # 解码token
        payload = decode_token(credentials.credentials)
        username: str = payload.get("sub")
        is_admin: bool = payload.get("is_admin", False)
        
        if username is None or not is_admin:
            raise UnauthorizedException("无效的管理员凭证")
            
        # 查询管理员信息
        admin = admin_service.get_admin_by_username(db, username=username)
        if admin is None:
            raise UnauthorizedException("管理员不存在")
        if not admin.is_active:
            raise UnauthorizedException("管理员账户已被禁用")
            
        return admin
    except Exception as e:
        raise UnauthorizedException("无效的管理员凭证")
