from typing import Any
from datetime import datetime
import uuid

from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.orm import as_declarative, declared_attr
from sqlalchemy import Column, DateTime, String

@as_declarative()
class Base:
    """
    SQLAlchemy 基础模型类
    """
    id: Any
    __name__: str
    # 为每个表自动生成表名
    @declared_attr
    def __tablename__(cls) -> str:
        return cls.__name__.lower() 