from typing import Any, Optional, Union

import redis
from redis import Redis
from redis.connection import ConnectionPool

from app.core.config import settings
from app.core.logger import log

# 创建Redis连接池
redis_pool = ConnectionPool(
    host=settings.REDIS_HOST,
    port=settings.REDIS_PORT,
    db=settings.REDIS_DB,
    password=settings.REDIS_PASSWORD,
    decode_responses=True,  # 自动将字节解码为字符串
    max_connections=100,
    socket_timeout=5,
)


class RedisClient:
    """
    Redis客户端工具类
    提供常用的Redis操作方法
    使用连接池管理连接
    """
    _client: Optional[Redis] = None
    
    @classmethod
    def get_client(cls) -> Redis:
        """
        获取Redis客户端连接
        """
        if cls._client is None:
            cls._client = redis.Redis(connection_pool=redis_pool)
        return cls._client
    
    @classmethod
    def get_value(cls, key: str) -> Any:
        """
        获取字符串类型的值
        """
        try:
            return cls.get_client().get(key)
        except Exception as e:
            log.error(f"Redis Get Error: {str(e)}")
            return None
    
    @classmethod
    def set_value(
        cls, 
        key: str, 
        value: Union[str, bytes, int, float], 
        ex: Optional[int] = None,
        nx: bool = False
    ) -> bool:
        """
        设置字符串类型的值
        
        Args:
            key: 键
            value: 值
            ex: 过期时间（秒）
            nx: 如果为True，则只有在key不存在时才设置值
        """
        try:
            return cls.get_client().set(key, value, ex=ex, nx=nx)
        except Exception as e:
            log.error(f"Redis Set Error: {str(e)}")
            return False
    
    @classmethod
    def delete(cls, key: str) -> bool:
        """
        删除键
        """
        try:
            return bool(cls.get_client().delete(key))
        except Exception as e:
            log.error(f"Redis Delete Error: {str(e)}")
            return False
    
    @classmethod
    def exists(cls, key: str) -> bool:
        """
        检查键是否存在
        """
        try:
            return bool(cls.get_client().exists(key))
        except Exception as e:
            log.error(f"Redis Exists Error: {str(e)}")
            return False
    
    @classmethod
    def expire(cls, key: str, seconds: int) -> bool:
        """
        设置键的过期时间
        """
        try:
            return bool(cls.get_client().expire(key, seconds))
        except Exception as e:
            log.error(f"Redis Expire Error: {str(e)}")
            return False
    
    @classmethod
    def hash_set(cls, name: str, key: str, value: str) -> bool:
        """
        设置哈希表中的字段值
        """
        try:
            return bool(cls.get_client().hset(name, key, value))
        except Exception as e:
            log.error(f"Redis HSet Error: {str(e)}")
            return False
    
    @classmethod
    def hash_get(cls, name: str, key: str) -> Any:
        """
        获取哈希表中的字段值
        """
        try:
            return cls.get_client().hget(name, key)
        except Exception as e:
            log.error(f"Redis HGet Error: {str(e)}")
            return None
    
    @classmethod
    def hash_delete(cls, name: str, key: str) -> bool:
        """
        删除哈希表中的字段
        """
        try:
            return bool(cls.get_client().hdel(name, key))
        except Exception as e:
            log.error(f"Redis HDel Error: {str(e)}")
            return False
    
    @classmethod
    def list_push(cls, name: str, value: str) -> bool:
        """
        将值插入到列表头部
        """
        try:
            return bool(cls.get_client().lpush(name, value))
        except Exception as e:
            log.error(f"Redis LPush Error: {str(e)}")
            return False
    
    @classmethod
    def list_pop(cls, name: str) -> Any:
        """
        从列表尾部弹出一个值
        """
        try:
            return cls.get_client().rpop(name)
        except Exception as e:
            log.error(f"Redis RPop Error: {str(e)}")
            return None
    
    @classmethod
    def scan_keys(cls, pattern: str) -> list:
        """
        使用SCAN命令扫描匹配模式的键
        
        Args:
            pattern: 模式字符串，如 "analysis_context:*"
            
        Returns:
            list: 匹配的键列表
        """
        try:
            keys = []
            client = cls.get_client()
            cursor = 0
            
            while True:
                cursor, partial_keys = client.scan(cursor=cursor, match=pattern, count=100)
                keys.extend(partial_keys)
                if cursor == 0:
                    break
            
            return keys
        except Exception as e:
            log.error(f"Redis Scan Keys Error: {str(e)}")
            return []


# 创建一个全局Redis实例
redis_client = RedisClient()
