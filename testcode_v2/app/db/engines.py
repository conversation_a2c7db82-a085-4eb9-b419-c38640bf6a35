import logging
from typing import Dict, Any
from sqlalchemy import create_engine
from urllib.parse import quote_plus
from contextlib import contextmanager

logger = logging.getLogger(__name__)

def engine_factory(db_type: str, config: Dict[str, Any]):
    """根据数据库类型和配置创建SQLAlchemy引擎
    
    Args:
        db_type: 数据库类型，如'oracle', 'mysql', 'postgresql', 'mssql'
        config: 数据库配置，包含连接信息
        
    Returns:
        SQLAlchemy Engine实例
    """
    db_type = db_type.lower()
    
    # 为不同数据库类型创建连接URL
    if db_type == 'oracle':
        # Oracle连接格式: oracle+cx_oracle://username:password@host:port/service_name
        user = config.get('username', '')
        password = config.get('password', '')
        host = config.get('host', 'localhost')
        port = config.get('port', 1521)
        service_name = config.get('service_name', '')
        
        connection_url = f"oracle+cx_oracle://{user}:{quote_plus(password)}@{host}:{port}/?service_name={service_name}"
    
    elif db_type == 'mysql':
        # MySQL连接格式: mysql+pymysql://username:password@host:port/database
        user = config.get('username', '')
        password = config.get('password', '')
        host = config.get('host', 'localhost')
        port = config.get('port', 3306)
        database = config.get('database', '')
        
        connection_url = f"mysql+pymysql://{user}:{quote_plus(password)}@{host}:{port}/{database}"
    
    elif db_type == 'postgresql':
        # PostgreSQL连接格式: postgresql+psycopg2://username:password@host:port/database
        user = config.get('username', '')
        password = config.get('password', '')
        host = config.get('host', 'localhost')
        port = config.get('port', 5432)
        database = config.get('database', '')
        
        connection_url = f"postgresql+psycopg2://{user}:{quote_plus(password)}@{host}:{port}/{database}"
    
    elif db_type == 'mssql':
        # MS SQL Server连接格式: mssql+pyodbc://username:password@host:port/database?driver=ODBC+Driver+17+for+SQL+Server
        user = config.get('username', '')
        password = config.get('password', '')
        host = config.get('host', 'localhost')
        port = config.get('port', 1433)
        database = config.get('database', '')
        driver = config.get('driver', 'ODBC+Driver+17+for+SQL+Server')
        
        connection_url = f"mssql+pyodbc://{user}:{quote_plus(password)}@{host}:{port}/{database}?driver={driver}"
    
    else:
        raise ValueError(f"不支持的数据库类型: {db_type}")
    
    # 创建引擎
    try:
        engine = create_engine(
            connection_url,
            echo=False,  # 不输出SQL日志
            pool_pre_ping=True,
            pool_size=5,
            max_overflow=10,
            pool_recycle=3600
        )
        return engine
    except Exception as e:
        logger.error(f"创建数据库引擎失败: {str(e)}")
        raise 