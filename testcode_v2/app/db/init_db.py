import logging
from sqlalchemy.orm import Session

from app.core.config import settings
from app.core.security import pwd_context
from app.services.user import get_user_by_username, create_user

logger = logging.getLogger(__name__)


def init_db(db: Session) -> None:
    """
    初始化数据库，创建默认用户
    """
    # 创建默认管理员用户
    if not get_user_by_username(db, username=settings.FIRST_SUPERUSER):
        logger.info("创建默认超级用户")
        create_user(
            db=db,
            username=settings.FIRST_SUPERUSER,
            password=settings.FIRST_SUPERUSER_PASSWORD,
            email=settings.FIRST_SUPERUSER_EMAIL,
            is_superuser=True,
        )
        logger.info("默认超级用户创建成功")
    else:
        logger.info("默认超级用户已存在") 