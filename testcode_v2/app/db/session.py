import uuid
from typing import Generator

from sqlalchemy import create_engine, event, text
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

from app.core.config import settings
from app.core.logger import sql_log

# 创建SQLAlchemy引擎
engine = create_engine(
    settings.DATABASE_URL,
    pool_pre_ping=True,
    pool_size=settings.DB_POOL_SIZE,
    max_overflow=settings.DB_MAX_OVERFLOW,
    pool_recycle=3600,
    echo=settings.DB_ECHO,
)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 导入统一的Base类
from app.db.base import Base




# 注册SQL事件监听器以记录SQL日志
@event.listens_for(engine, "before_cursor_execute", named=True)
def before_cursor_execute(**kw):
    if not settings.LOG_PRINT_SQL:
        return
    
    conn = kw['conn']
    cursor = kw['cursor']
    statement = kw['statement']
    parameters = kw['parameters']
    context = kw['context']
    
    # 获取当前的request_id，如果在上下文中存在的话
    request_id = getattr(context, "request_id", None)
    if not request_id:
        request_id = str(uuid.uuid4())
    
    # 记录SQL日志
    sql_log.log_sql(statement, {"parameters": parameters})


def get_db() -> Generator:
    """
    获取数据库会话的依赖函数
    """
    db = SessionLocal()
    try:
        yield db
    except SQLAlchemyError as e:
        db.rollback()
        raise
    finally:
        db.close()


def init_db() -> None:
    """
    初始化数据库表
    """
    # 确保所有模型都被导入
    from app.models import (
        Project, DataSource, Tool, Analysis, ToolExecution, User,
        Conversation, ConversationContext
    )
    
    Base.metadata.create_all(bind=engine)
