from sqlalchemy import Column, String, DateTime, Integer, Text, ForeignKey, JSON, Enum, TypeDecorator
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid
import enum
import json
from datetime import datetime
from logging import getLogger

from app.db.base import Base
from app.utils.time_utils import get_shanghai_time
from app.schemas.data_source import DataSourceType

# 导入SelectedTable用于关系定义
from app.models.selected_table import SelectedTable

log = getLogger("app.models.data_source")

# 自定义类型转换器，处理数据库中的字符串值
class CaseInsensitiveEnumType(TypeDecorator):
    """大小写不敏感的枚举类型"""
    impl = String
    cache_ok = True
    
    def __init__(self, enumtype, *args, **kwargs):
        super(CaseInsensitiveEnumType, self).__init__(*args, **kwargs)
        self._enumtype = enumtype
    
    def process_bind_param(self, value, dialect):
        """将Python值转换为数据库值"""
        if value is None:
            return None
        
        # 如果已经是字符串，直接返回
        if isinstance(value, str):
            return value
        
        # 如果是枚举对象，返回其值
        if isinstance(value, self._enumtype):
            return value.value
        
        # 其他情况，尝试转换为字符串
        return str(value)
    
    def process_result_value(self, value, dialect):
        """将数据库值转换为Python值"""
        if value is None:
            return None
        
        log.info(f"从数据库加载枚举值: {value}")
        
        # 尝试查找匹配的枚举值
        if isinstance(value, str):
            try:
                # 直接匹配，大小写不敏感
                value_lower = value.lower()
                for member in self._enumtype:
                    if member.name.lower() == value_lower or member.value.lower() == value_lower:
                        log.info(f"直接匹配枚举值成功: {value} -> {member}")
                        return member
                
                # 如果直接匹配失败，使用枚举类的_missing_方法
                log.info(f"直接匹配失败，尝试使用_missing_方法: {value}")
                return self._enumtype(value)
            except (ValueError, TypeError) as e:
                log.warning(f"枚举值转换失败: {value}, 错误: {str(e)}")
        
        # 如果已经是枚举对象，直接返回
        if isinstance(value, self._enumtype):
            return value
            
        # 其他情况返回默认值
        log.warning(f"无法处理的枚举值类型: {type(value)}，使用OTHER作为默认")
        return self._enumtype.OTHER

class DataSource(Base):
    """数据源模型"""
    __tablename__ = "data_sources"
    
    id = Column(String(36), primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    name = Column(String(100), nullable=False, comment="数据源名称")
    description = Column(Text, nullable=True, comment="数据源描述")
    # 使用自定义类型转换器替代标准Enum类型
    type = Column(CaseInsensitiveEnumType(DataSourceType), nullable=False, comment="数据源类型")
    config = Column(JSON, nullable=False, comment="数据源配置")
    db_version = Column(String(200), nullable=True, comment="数据库版本信息")
    project_id = Column(String(36), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    created_at = Column(DateTime, default=get_shanghai_time, comment="创建时间")
    updated_at = Column(DateTime, default=get_shanghai_time, onupdate=get_shanghai_time, comment="更新时间")
    
    # 关联关系
    project = relationship("Project", back_populates="data_sources")
    tools = relationship("Tool", back_populates="data_source", cascade="all, delete-orphan")
    selected_tables = relationship("SelectedTable", back_populates="data_source", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<DataSource {self.name} ({self.type})>"
    
    # 添加加载和转换方法，确保type字段类型正确
    @property
    def safe_type(self):
        """确保返回有效的枚举类型"""
        if self.type is None:
            return DataSourceType.OTHER
            
        # 如果已经是枚举类型，直接返回
        if isinstance(self.type, DataSourceType):
            return self.type
            
        # 如果是字符串，尝试转换
        if isinstance(self.type, str):
            try:
                return DataSourceType(self.type)
            except (ValueError, TypeError):
                log.warning(f"无法将字符串转换为枚举值: {self.type}")
                return DataSourceType.OTHER
                
        # 其他情况
        log.warning(f"未知类型的数据源类型值: {type(self.type)}")
        return DataSourceType.OTHER
    
    @property
    def config_dict(self):
        """确保返回字典格式的配置"""
        if self.config is None:
            return {}
        
        if isinstance(self.config, dict):
            return self.config
        
        # 如果是字符串，尝试解析为字典
        if isinstance(self.config, str):
            try:
                return json.loads(self.config)
            except json.JSONDecodeError:
                log.warning(f"无法解析config字符串: {self.config}")
                return {}
        
        return {}
        
    def set_config(self, config_data):
        """设置配置数据，确保是合法的JSON格式"""
        if isinstance(config_data, dict):
            self.config = config_data
        elif isinstance(config_data, str):
            try:
                self.config = json.loads(config_data)
            except json.JSONDecodeError:
                log.warning(f"无效的config字符串: {config_data}")
                self.config = {}
        else:
            self.config = {}

    def to_api_model(self):
        """转换为API响应模型格式"""
        try:
            # 处理type字段，确保是字符串
            type_value = self.safe_type
            if hasattr(type_value, 'value'):
                type_value = type_value.value
                
            data = {
                "id": self.id,
                "name": self.name,
                "description": self.description,
                "type": type_value,
                "config": self.config_dict,  # 使用属性确保是字典
                "db_version": self.db_version,  # 添加数据库版本字段
                "project_id": self.project_id,
                "created_at": self.created_at,
                "updated_at": self.updated_at
            }
            return data
        except Exception as e:
            log.error(f"转换数据源模型时出错: {str(e)}")
            # 返回基本信息，避免完全失败
            return {
                "id": self.id,
                "name": self.name,
                "description": getattr(self, "description", None),
                "type": "OTHER",
                "config": {},
                "db_version": getattr(self, "db_version", None),
                "project_id": getattr(self, "project_id", ""),
                "created_at": getattr(self, "created_at", get_shanghai_time()),
                "updated_at": getattr(self, "updated_at", get_shanghai_time())
            } 