from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, BigInteger
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.base import Base


class TokenUsage(Base):
    """Token使用记录表"""
    __tablename__ = "token_usages"

    id = Column(String(36), primary_key=True, index=True)
    user_id = Column(String(36), nullable=False)
    org_id = Column(Integer, nullable=False)
    project_id = Column(String(36), nullable=False)
    analysis_id = Column(String(36), nullable=False)
    model_id = Column(String(36), nullable=False)
    model_name = Column(String(100), nullable=False)
    
    # 详细的Token用量统计
    prompt_tokens = Column(Integer, nullable=False, default=0, comment="输入Token数量")
    completion_tokens = Column(Integer, nullable=False, default=0, comment="输出Token数量")
    total_tokens = Column(Integer, nullable=False, default=0, comment="总Token数量")

    created_at = Column(DateTime, server_default=func.now())

    # 简化版本，移除外键关系以避免复杂性 