from datetime import datetime
from sqlalchemy import Column, String, Integer, DateTime, Boolean, BigInteger, Text
from sqlalchemy.orm import relationship
from app.db.base import Base


class Organization(Base):
    """
    企业/组织模型
    """
    __tablename__ = "organizations"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True, comment='企业ID')
    name = Column(String(100), nullable=False, unique=True, comment='企业名称')
    description = Column(Text, nullable=True, comment='企业描述')
    
    # 限额配置和用量统计
    token_limit = Column(BigInteger, default=1000000, comment='Token消耗限额（总）')
    total_token_usage = Column(BigInteger, nullable=False, default=0, server_default="0", comment='Token总用量累计')
    user_limit = Column(Integer, default=50, comment='用户数量限额')
    project_limit = Column(Integer, default=20, comment='项目数量限额')
    
    # 状态管理
    status = Column(Integer, default=1, comment='状态：1=正常 2=停用')
    expire_at = Column(DateTime, nullable=True, comment='过期时间')
    
    # 联系信息
    contact_email = Column(String(100), nullable=True, comment='联系邮箱')
    contact_phone = Column(String(20), nullable=True, comment='联系电话')
    address = Column(Text, nullable=True, comment='地址')
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')

    # 关系 - 移除外键关系


    def __repr__(self):
        return f"<Organization {self.name}>"

    @property
    def is_active(self):
        """企业是否处于活跃状态"""
        return self.status == 1

    @property
    def is_expired(self):
        """企业是否已过期"""
        if self.expire_at is None:
            return False
        return datetime.utcnow() > self.expire_at 
