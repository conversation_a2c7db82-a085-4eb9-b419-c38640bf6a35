import uuid
from sqlalchemy import Column, String, Text, DateTime, Boolean, JSON, func

from app.db.base import Base


class LLMModel(Base):
    """LLM模型配置模型"""
    __tablename__ = "llm_models"
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), index=True)
    name = Column(String(100), nullable=False, comment="模型名称")
    description = Column(Text, nullable=True, comment="模型描述")

    model_name = Column(String(100), nullable=False, comment="模型标识符")
    api_key = Column(String(500), nullable=False, comment="API密钥")
    base_url = Column(String(500), nullable=True, comment="API基础URL", default="https://api.openai.com/v1")
    config = Column(JSON, nullable=True, comment="额外配置参数")
    is_active = Column(Boolean, default=True, comment="是否启用")
    created_at = Column(DateTime, server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    def __repr__(self):
        return f"<LLMModel {self.name}>"
