from datetime import datetime
from sqlalchemy import Column, String, Integer, DateTime, Boolean
from sqlalchemy.orm import relationship

from app.db.base import Base
from app.utils.time_utils import get_shanghai_time


class User(Base):
    """
    用户模型
    """
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=True)
    hashed_password = Column(String(100), nullable=False)
    is_active = Column(Boolean, default=True)
    is_superuser = Column(Boolean, default=False)
    pid = Column(Integer, nullable=False, default=0, comment="父级ID")
    # 新增权限控制字段
    role_id = Column(Integer, default=3, comment="系统角色ID，默认为普通用户")
    org_id = Column(Integer, comment="所属企业ID")
    can_create_project = Column(Boolean, default=True, comment="是否可以创建项目")
    language_preference = Column(String(10), default='zh-CN', comment="用户语言偏好")
    created_at = Column(DateTime, default=get_shanghai_time)
    updated_at = Column(DateTime, default=get_shanghai_time, onupdate=get_shanghai_time)

    # 关联关系
    conversations = relationship("Conversation", back_populates="user")

    def __repr__(self):
        return f"<User {self.username}>" 
    
    def has_permission(self, permission: str, db=None) -> bool:
        """检查用户是否有指定权限"""
        if not self.role_id or not db:
            return False
        
        from app.models.role import Role
        role = db.query(Role).filter(Role.id == self.role_id).first()
        if not role:
            return False
            
        return role.has_permission(permission) 