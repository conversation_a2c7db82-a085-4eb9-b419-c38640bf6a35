from sqlalchemy import Column, String, DateTime, Integer, Text, Bo<PERSON>an, Foreign<PERSON>ey, Enum
from sqlalchemy.sql import func, expression
from sqlalchemy.orm import relationship
import uuid

from app.db.base import Base

class Project(Base):
    """项目模型"""
    __tablename__ = "projects"
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), index=True)
    name = Column(String(100), nullable=False, comment="项目名称")
    description = Column(Text, nullable=True, comment="项目描述")
    user_id = Column(Integer, nullable=False, default=0, comment="创建人ID")
    # 新增权限控制字段
    owner_id = Column(Integer, comment="项目所有者ID")
    visibility = Column(
        Enum("PRIVATE", "PUBLIC", name="project_visibility"), 
        default="PRIVATE", 
        comment="项目可见性"
    )
    org_id = Column(Integer, comment="所属企业ID")
    report_prompt = Column(Text, comment="项目自定义报告生成提示词")
    created_at = Column(DateTime, server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 关联关系
    data_sources = relationship("DataSource", back_populates="project", cascade="all, delete-orphan")
    analyses = relationship("Analysis", back_populates="project", cascade="all, delete-orphan")
    conversations = relationship("Conversation", back_populates="project", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Project {self.name}>" 