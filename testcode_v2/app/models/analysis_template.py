from sqlalchemy import Column, String, Text, Boolean, Integer, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from app.db.base import Base
from app.utils.time_utils import get_shanghai_time
import uuid


class AnalysisTemplate(Base):
    """分析模板模型"""
    __tablename__ = 'analysis_templates'

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String(100), nullable=False)  # 模板名称
    description = Column(Text)  # 模板描述
    content = Column(Text, nullable=False)  # 模板内容（提示词）

    # 分类和标签
    category = Column(String(50))  # 模板分类（销售分析、用户分析等）
    tags = Column(Text)  # 标签，JSON格式存储

    # 模板类型和配置
    template_type = Column(String(20), default='query')  # 模板类型：query, workflow, specification
    complexity_level = Column(String(20))  # 复杂度：basic, intermediate, advanced
    applicable_data_types = Column(Text)  # 适用数据类型，JSON格式存储

    # 元数据
    project_id = Column(String, ForeignKey('projects.id'))
    created_by = Column(Integer, ForeignKey('users.id'))  # 创建人ID
    is_public = Column(Boolean, default=False)  # 是否公开
    is_system = Column(Boolean, default=False)  # 是否系统预置
    usage_count = Column(Integer, default=0)  # 使用次数

    created_at = Column(DateTime, default=get_shanghai_time)
    updated_at = Column(DateTime, default=get_shanghai_time, onupdate=get_shanghai_time)

    # 关联关系
    project = relationship("Project", backref="templates")
    creator = relationship("User", backref="created_templates")
    
    def __repr__(self):
        return f"<AnalysisTemplate(id={self.id}, name={self.name})>"
