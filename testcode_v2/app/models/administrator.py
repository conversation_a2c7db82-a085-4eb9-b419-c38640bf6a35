from datetime import datetime
from sqlalchemy import Column, String, Integer, DateTime, Boolean
from app.db.base import Base


class Administrator(Base):
    """
    管理员模型 - 独立的管理员认证系统
    """
    __tablename__ = "administrators"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    username = Column(String(50), unique=True, index=True, nullable=False, comment="管理员用户名")
    email = Column(String(100), unique=True, index=True, nullable=True, comment="管理员邮箱")
    hashed_password = Column(String(100), nullable=False, comment="密码哈希")
    is_active = Column(Boolean, default=True, comment="是否激活")
    last_login_at = Column(DateTime, nullable=True, comment="最后登录时间")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    def __repr__(self):
        return f"<Administrator {self.username}>" 