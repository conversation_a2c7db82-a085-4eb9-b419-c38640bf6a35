from sqlalchemy import Column, String, Text, DateTime, JSON, ForeignKey, UniqueConstraint
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from app.db.base import Base

class SelectedTable(Base):
    """用户选择的数据表模型"""
    __tablename__ = "selected_tables"
    
    id = Column(String(36), primary_key=True, index=True)
    data_source_id = Column(String(36), ForeignKey("data_sources.id", ondelete="CASCADE"), nullable=False)
    table_name = Column(String(100), nullable=False)
    table_description = Column(Text, nullable=True)
    table_schema = Column(JSON, nullable=False)
    sample_data = Column(JSON, nullable=False)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 数据源关系
    data_source = relationship("DataSource", back_populates="selected_tables")
    
    # 定义唯一约束
    __table_args__ = (
        UniqueConstraint('data_source_id', 'table_name', name='idx_datasource_table'),
    ) 