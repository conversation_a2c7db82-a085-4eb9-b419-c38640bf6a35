from sqlalchemy import Column, Integer, String, BigInteger, Enum, DateTime
from sqlalchemy.sql import func
from app.db.base import Base


class ProjectMember(Base):
    """项目成员表"""
    __tablename__ = "project_members"

    id = Column(BigInteger, primary_key=True, index=True, comment="成员ID")
    project_id = Column(String(36), nullable=False, comment="项目ID")
    user_id = Column(Integer, nullable=False, comment="用户ID")
    project_role_id = Column(Integer, nullable=False, comment="项目角色ID")
    invited_by = Column(Integer, comment="邀请人ID")
    joined_at = Column(DateTime, default=func.now(), comment="加入时间")
    status = Column(
        Enum("ACTIVE", "INACTIVE", name="member_status"), 
        default="ACTIVE", 
        comment="成员状态"
    ) 