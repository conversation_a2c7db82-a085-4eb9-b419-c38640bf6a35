from datetime import datetime
from sqlalchemy import Column, String, Integer, DateTime, Boolean, Text, JSON, ForeignKey
from sqlalchemy.orm import relationship
from app.db.base import Base


class Role(Base):
    """
    角色模型
    """
    __tablename__ = "roles"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True, comment='角色ID')
    name = Column(String(50), nullable=False, comment='角色名称')
    description = Column(Text, nullable=True, comment='角色描述')
    level = Column(Integer, nullable=False, default=0, comment='角色层级：0=系统级 1=企业级 2=项目级')
    
    # 权限配置（JSON格式，保留原有逻辑）
    permissions = Column(JSON, nullable=True, comment='权限配置JSON')
    
    # 状态管理
    is_active = Column(Boolean, default=True, comment='是否启用')
    is_system = Column(Boolean, default=False, comment='是否系统内置角色（不可删除）')
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')

    # 关系 - 移除外键关系

    def __repr__(self):
        return f"<Role {self.name}>"

    @property
    def is_system_role(self):
        """是否系统级角色"""
        return self.level == 0

    @property
    def is_org_role(self):
        """是否企业级角色"""
        return self.level == 1

    @property
    def is_user_role(self):
        """是否用户级角色"""
        return self.level == 2

    def has_permission(self, permission: str) -> bool:
        """检查是否有指定权限"""
        if not self.permissions:
            return False
        
        if isinstance(self.permissions, list):
            # 检查完全匹配
            if permission in self.permissions:
                return True
            
            # 检查通配符权限
            for perm in self.permissions:
                if perm.endswith(':*'):
                    prefix = perm[:-1]  # 移除 '*'
                    if permission.startswith(prefix):
                        return True
                        
        return False 