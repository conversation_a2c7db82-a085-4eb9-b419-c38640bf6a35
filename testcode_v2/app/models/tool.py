from sqlalchemy import Column, String, DateTime, Integer, Text, ForeignKey, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from sqlalchemy.types import TypeDecorator
import uuid
import enum
import json

from app.db.base import Base

# 添加自定义TypeDecorator用于处理小写枚举值
class LowerCaseEnum(TypeDecorator):
    impl = String(50)
    cache_ok = True

    def __init__(self, enum_cls, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.enum_cls = enum_cls

    def process_bind_param(self, value, dialect):
        if value is None:
            return None
        raw = value.value if isinstance(value, enum.Enum) else value
        return raw.lower()

    def process_result_value(self, value, dialect):
        if value is None:
            return None
        return self.enum_cls(value)

class ToolType(str, enum.Enum):
    """工具类型枚举"""
    SQL = "SQL"  # SQL查询工具
    API = "API"  # API调用工具
    PYTHON = "PYTHON"  # Python脚本工具
    GRAPH = "GRAPH"  # 图数据查询工具
    VECTOR = "VECTOR"  # 向量检索工具
    CUSTOM = "CUSTOM"  # 自定义工具
    AUTO_SQL = "AUTO_SQL"  # 自动SQL生成和执行工具（系统内置）
    CLARIFICATION = "CLARIFICATION"  # 用户信息澄清工具（系统内置）
    CHART_GENERATION = "CHART_GENERATION"  # 图表生成工具（系统内置）
    
    @classmethod
    def _missing_(cls, value):
        """处理大小写不敏感的枚举值查找"""
        if value is None:
            return cls.CUSTOM
            
        if isinstance(value, str):
            # 尝试大小写不敏感匹配
            for member in cls:
                if member.value.lower() == value.lower():
                    return member
        
        # 如果没有找到匹配项，返回CUSTOM
        return cls.CUSTOM

class DisplayFormat(str, enum.Enum):
    """工具结果展示格式枚举"""
    JSON = "json"  # 默认JSON格式
    TABLE = "table"  # 表格展示
    GRAPH = "graph"  # 图形展示
    TEXT = "text"   # 文本展示
    CHART = "chart"  # 图表展示

class Tool(Base):
    """工具模型"""
    __tablename__ = "tools"
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), index=True)
    name = Column(String(100), nullable=False, comment="工具名称")
    description = Column(Text, nullable=True, comment="工具描述")
    tool_type = Column(LowerCaseEnum(ToolType), nullable=False, comment="工具类型")
    template = Column(Text, nullable=False, comment="工具模板，可以是SQL、API调用格式、Python代码等")
    parameters = Column(JSON, nullable=False, comment="工具参数定义，JSON格式")
    created_at = Column(DateTime, server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 外键关系
    data_source_id = Column(String(36), ForeignKey("data_sources.id"), nullable=True)  # 修改为nullable=True
    
    # 关联关系
    data_source = relationship("DataSource", back_populates="tools")
    executions = relationship("ToolExecution", back_populates="tool", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Tool {self.name} ({self.tool_type})>"
    
    @property
    def parameters_dict(self):
        """确保返回字典格式的参数定义"""
        if self.parameters is None:
            return []
        
        if isinstance(self.parameters, list):
            return self.parameters
        
        # 如果是字符串，尝试解析
        if isinstance(self.parameters, str):
            try:
                return json.loads(self.parameters)
            except json.JSONDecodeError:
                return []
        
        return []
    
    def to_api_model(self):
        """转换为API响应模型格式"""
        import json
        print(f"Tool.to_api_model - 原始参数: {self.parameters}, 类型: {type(self.parameters)}")
        
        # 处理参数，确保返回列表格式
        parameters = self.parameters_dict
        print(f"Tool.to_api_model - 处理后的参数: {parameters}")
        
        # 修复工具类型转换逻辑：如果是枚举则取其值，确保保留原始类型而不是默认为custom
        if self.tool_type is None:
            tool_type_str = "custom"  # 只有在真正为None的情况下才使用custom作为默认值
        elif hasattr(self.tool_type, 'value'):
            tool_type_str = self.tool_type.value.lower()  # 枚举取值并转小写
        else:
            tool_type_str = str(self.tool_type).lower()  # 其他类型转字符串并小写
        
        # 调试输出
        print(f"Tool.to_api_model - 原始类型: {self.tool_type}, 转换后类型: {tool_type_str}")
        
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "tool_type": tool_type_str,
            "template": self.template,
            "parameters": parameters,
            "data_source_id": self.data_source_id,
            "created_at": self.created_at,
            "updated_at": self.updated_at
        } 