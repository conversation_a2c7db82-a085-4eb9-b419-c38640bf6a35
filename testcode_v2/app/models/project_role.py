from sqlalchemy import Column, Integer, String, Text, DateTime, JSON
from sqlalchemy.sql import func
from app.db.base import Base


class ProjectRole(Base):
    """项目角色表"""
    __tablename__ = "project_roles"

    id = Column(Integer, primary_key=True, index=True, comment="项目角色ID")
    name = Column(String(50), nullable=False, comment="项目角色名称")
    code = Column(String(50), unique=True, nullable=False, comment="项目角色代码")
    description = Column(Text, comment="角色描述")
    permissions = Column(JSON, nullable=False, comment="权限列表")
    created_at = Column(DateTime, default=func.now(), comment="创建时间") 