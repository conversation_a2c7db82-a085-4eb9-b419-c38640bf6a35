from sqlalchemy import Column, String, DateTime, Enum, ForeignKey, Text, Integer, Float, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.db.base import Base
from datetime import datetime
import enum

class ConversationStatus(str, enum.Enum):
    ACTIVE = "active"
    ARCHIVED = "archived"
    PAUSED = "paused"

class ContextType(str, enum.Enum):
    QUERY = "query"
    RESULT = "result"
    INSIGHT = "insight"
    DATA_SOURCE = "data_source"
    TOOL_USAGE = "tool_usage"
    
    @classmethod
    def _missing_(cls, value):
        """处理大小写不敏感的枚举值查找"""
        if value is None:
            return cls.QUERY
            
        if isinstance(value, str):
            # 尝试大小写不敏感匹配
            for member in cls:
                if member.value.lower() == value.lower():
                    return member
        
        # 如果没有找到匹配项，返回默认值
        return cls.QUERY

class Conversation(Base):
    __tablename__ = "conversations"

    # 覆盖基类的id字段以使用String(50)
    id = Column(String(50), primary_key=True, index=True)
    project_id = Column(String(50), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, comment="会话创建者ID")
    title = Column(String(500), nullable=True, comment="会话标题")
    context_summary = Column(Text, nullable=True, comment="会话级别的上下文摘要")
    total_rounds = Column(Integer, default=0, comment="总轮次数")
    total_tokens_used = Column(Integer, default=0, comment="总消耗token数")
    status = Column(Enum(ConversationStatus), default=ConversationStatus.ACTIVE, comment="会话状态")
    
    # 覆盖基类的时间字段以使用数据库默认值
    created_at = Column(DateTime, server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 关联关系
    project = relationship("Project", back_populates="conversations")
    user = relationship("User", back_populates="conversations")
    analyses = relationship("Analysis", back_populates="conversation", cascade="all, delete-orphan")
    contexts = relationship("ConversationContext", back_populates="conversation", cascade="all, delete-orphan")

class ConversationContext(Base):
    """会话上下文表"""
    __tablename__ = "conversation_contexts"

    # 覆盖基类的id字段以使用Integer自增
    id = Column(Integer, primary_key=True, autoincrement=True, index=True)
    conversation_id = Column(String(50), ForeignKey("conversations.id", ondelete="CASCADE"), nullable=False)
    round_number = Column(Integer, nullable=False, comment="轮次编号")
    context_type = Column(String(50), nullable=False, comment="上下文类型")
    context_data = Column(JSON, nullable=False, comment="上下文数据")
    relevance_score = Column(Float, default=1.0, comment="上下文相关性评分")
    
    # 覆盖基类的时间字段
    created_at = Column(DateTime, server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 关联关系
    conversation = relationship("Conversation", back_populates="contexts")

    # 索引
    __table_args__ = (
        {'mysql_engine': 'InnoDB', 'mysql_charset': 'utf8mb4'},
    ) 