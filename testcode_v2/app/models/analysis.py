from sqlalchemy import Column, String, DateTime, Integer, Text, ForeignKey, JSON, Float
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid

from app.db.base import Base

class Analysis(Base):
    """分析记录模型"""
    __tablename__ = "analyses"
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), index=True)
    query = Column(Text, nullable=False, comment="用户查询")
    intent_analysis = Column(JSON, nullable=True, comment="意图分析结果")
    result = Column(Text, nullable=True, comment="分析报告")
    created_at = Column(DateTime, server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now(), comment="更新时间")
    # 外键关系
    project_id = Column(String(36), ForeignKey("projects.id"), nullable=False)
    conversation_id = Column(String(50), ForeignKey("conversations.id"), nullable=True, comment="所属会话ID")
    round_number = Column(Integer, default=1, comment="轮次编号")
    context_tokens_used = Column(Integer, default=0, comment="上下文消耗的token数")
    context_summary = Column(Text, nullable=True, comment="本轮次使用的上下文摘要")
    
    # 新增：完整的分析状态保存
    analysis_steps = Column(JSON, nullable=True, comment="分析步骤详情")
    tool_results = Column(JSON, nullable=True, comment="工具执行结果映射")
    mcp_results = Column(JSON, nullable=True, comment="MCP执行结果")
    status = Column(String(20), default='running', comment="分析状态: running/completed/failed")
    execution_time = Column(Float, nullable=True, comment="总执行时间(秒)")
    context_relevance = Column(Float, nullable=True, comment="上下文相关性评分")
    llm_summary = Column(Text, nullable=True, comment="LLM生成的本轮次总结")
    
    # 关联关系
    project = relationship("Project", back_populates="analyses")
    conversation = relationship("Conversation", back_populates="analyses")
    tool_executions = relationship("ToolExecution", back_populates="analysis", cascade="all, delete-orphan")
    step_details = relationship("AnalysisStepDetail", back_populates="analysis", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Analysis {self.id}>"

class ToolExecution(Base):
    """工具执行记录模型"""
    __tablename__ = "tool_executions"
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), index=True)
    parameters = Column(JSON, nullable=False, comment="执行参数")
    result = Column(JSON, nullable=True, comment="执行结果")
    execution_time = Column(Float, nullable=True, comment="执行时间(秒)")
    created_at = Column(DateTime, server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now(), comment="更新时间")
    step_id = Column(String(50), nullable=True, comment="分析步骤ID")
    
    # 新增：完整的执行状态保存
    status = Column(String(20), default='success', comment="执行状态: success/failed/running")
    error_message = Column(Text, nullable=True, comment="错误信息")
    step_order = Column(Integer, nullable=True, comment="步骤顺序")
    reasoning = Column(Text, nullable=True, comment="执行推理过程")
    
    # 外键关系
    analysis_id = Column(String(36), ForeignKey("analyses.id"), nullable=False)
    tool_id = Column(String(36), ForeignKey("tools.id"), nullable=False)
    
    # 关联关系
    analysis = relationship("Analysis", back_populates="tool_executions")
    tool = relationship("Tool", back_populates="executions")
    
    def __repr__(self):
        return f"<ToolExecution {self.id}>"

class AnalysisStepDetail(Base):
    """分析步骤详情模型 - 用于存储完整的分析时间轴"""
    __tablename__ = "analysis_step_details"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    analysis_id = Column(String(36), ForeignKey("analyses.id"), nullable=False, comment="所属分析ID")
    step_id = Column(String(100), nullable=False, comment="步骤ID")
    step_name = Column(Text, nullable=False, comment="步骤名称")
    step_type = Column(String(20), nullable=False, comment="步骤类型: system/tool/planning/report/error/interaction/evaluation")
    status = Column(String(20), nullable=False, comment="步骤状态: process/finish/error/wait")
    step_order = Column(Integer, nullable=False, comment="步骤顺序")
    start_time = Column(DateTime, nullable=False, comment="开始时间")
    end_time = Column(DateTime, nullable=True, comment="结束时间")
    execution_time = Column(Float, nullable=True, comment="执行时间(毫秒)")
    description = Column(Text, nullable=True, comment="步骤描述")
    tool_name = Column(String(200), nullable=True, comment="工具名称(如果是工具步骤)")
    parameters = Column(JSON, nullable=True, comment="执行参数(如果是工具步骤)")
    has_result = Column(Integer, default=0, comment="是否有执行结果")
    result_key = Column(String(100), nullable=True, comment="结果键值")
    has_intent_data = Column(Integer, default=0, comment="是否有意图分析数据")
    has_report = Column(Integer, default=0, comment="是否有报告数据")
    plan_data = Column(JSON, nullable=True, comment="规划数据")
    reasoning = Column(Text, nullable=True, comment="执行推理过程")
    error_message = Column(Text, nullable=True, comment="错误信息")
    
    # 新增：交互相关字段
    has_chart_data = Column(Integer, default=0, comment="是否有图表数据")
    interaction_type = Column(String(50), nullable=True, comment="交互类型: intent_confirmation/clarification/chart_display")
    interaction_data = Column(JSON, nullable=True, comment="交互相关数据")
    
    created_at = Column(DateTime, server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 关联关系
    analysis = relationship("Analysis", back_populates="step_details")
    
    def __repr__(self):
        return f"<AnalysisStepDetail {self.id}: {self.step_name}>"
    
    def to_dict(self):
        """转换为字典格式，用于前端显示"""
        # 处理参数数据，避免返回过大的数据结构
        parameters = self.parameters
        if parameters and isinstance(parameters, dict):
            # 对于意图确认步骤，精简参数数据
            if self.interaction_type == 'intent_confirmation':
                parameters = {
                    'original_query': parameters.get('original_query', ''),
                    'user_adjustment': parameters.get('user_adjustment', ''),
                    'has_user_modification': parameters.get('has_user_modification', False),
                    'confirmation_completed': parameters.get('confirmation_completed', False),
                    'intent_summary': parameters.get('intent_summary', ''),
                    'needs_clarification': parameters.get('needs_clarification', False),
                    'question_count': parameters.get('question_count', 0)
                }

        # 处理交互数据，对于意图确认步骤保留完整数据供前端渲染
        interaction_data = self.interaction_data
        if interaction_data and isinstance(interaction_data, dict):
            # 对于意图确认步骤，保留完整的交互数据（包含完整的意图分析）
            if self.interaction_type == 'intent_confirmation':
                # 保留完整的交互数据，前端需要这些信息来正确渲染意图确认界面
                interaction_data = {
                    'original_query': interaction_data.get('original_query', ''),
                    'user_adjustment': interaction_data.get('user_adjustment', ''),
                    'has_user_modification': interaction_data.get('has_user_modification', False),
                    'confirmation_completed': interaction_data.get('confirmation_completed', False),
                    'intent_summary': interaction_data.get('intent_summary', ''),
                    'clarification_count': interaction_data.get('clarification_count', 0),
                    # 保留完整的意图分析数据供前端渲染
                    'intent_analysis': interaction_data.get('intent_analysis', {}),
                    'clarification_answers': interaction_data.get('clarification_answers', {}),
                    # 保留其他必要字段
                    'message': interaction_data.get('message', ''),
                    'user_query': interaction_data.get('user_query', ''),
                    'needs_clarification': interaction_data.get('needs_clarification', False),
                    'question_count': interaction_data.get('question_count', 0)
                }

        return {
            "id": self.step_id,
            "name": self.step_name,
            "status": self.status,
            "time": self.start_time.isoformat() if self.start_time else None,
            "description": self.description,
            "tool_name": self.tool_name,
            "parameters": parameters,
            "hasResult": bool(self.has_result),
            "resultKey": self.result_key,
            "hasIntentData": bool(self.has_intent_data),
            "hasReport": bool(self.has_report),
            "hasChartData": bool(self.has_chart_data),
            "planData": self.plan_data,
            "reasoning": self.reasoning,
            "executionTime": self.execution_time,
            "errorMessage": self.error_message,
            "interactionType": self.interaction_type,
            "interactionData": interaction_data
        }