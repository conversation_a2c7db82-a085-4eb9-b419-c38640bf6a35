from fastapi import APIRouter

# 导入管理员相关的端点
from app.api.v1.endpoints import (
    admin_auth, 
    admin_users, 
    admin_organizations, 
    admin_roles, 
    admin_statistics
)

# 创建管理员专用路由
router = APIRouter()

# 注册管理员路由
router.include_router(admin_auth.router, prefix="/auth", tags=["管理员认证"])
router.include_router(admin_users.router, prefix="", tags=["管理员-用户管理"])
router.include_router(admin_organizations.router, prefix="", tags=["管理员-企业管理"])
router.include_router(admin_roles.router, prefix="", tags=["管理员-角色管理"])
router.include_router(admin_statistics.router, prefix="/statistics", tags=["管理员-统计分析"]) 