from fastapi import APIRouter

# 导入各个模块的路由
# from app.api.v1.endpoints import user, auth, example
from app.api.v1.endpoints import projects, data_sources, tools, analysis, project_wizard, llm_analysis, llm_models, auth, conversations, users, project_members, project_intelligence, templates

# 创建主路由
router = APIRouter()

# 注册各个模块的路由
# router.include_router(auth.router, prefix="/auth", tags=["认证"])
# router.include_router(user.router, prefix="/users", tags=["用户"])
router.include_router(auth.router, prefix="/auth", tags=["认证"])
router.include_router(projects.router, prefix="/projects", tags=["项目管理"])
router.include_router(data_sources.router, prefix="/data-sources", tags=["数据源管理"])
router.include_router(tools.router, prefix="/tools", tags=["工具管理"])
router.include_router(llm_models.router, prefix="/llm-models", tags=["LLM模型管理"])
router.include_router(llm_analysis.router, prefix="/analysis", tags=["LLM数据分析"])
router.include_router(analysis.router, prefix="/analysis", tags=["数据分析"])
router.include_router(conversations.router, prefix="/conversations", tags=["会话管理"])

# 新增项目向导路由
router.include_router(
    project_wizard.router, 
    prefix="/project-wizard", 
    tags=["项目向导"]
)

# 子账号管理路由
router.include_router(
    users.router,
    prefix="/users",
    tags=["用户管理"]
)

# 项目成员管理路由
router.include_router(
    project_members.router,
    prefix="/projects",
    tags=["项目成员管理"]
)

# 项目智能理解路由
router.include_router(
    project_intelligence.router,
    prefix="/project-intelligence",
    tags=["项目智能理解"]
)

# 分析模板路由
router.include_router(
    templates.router,
    prefix="/templates",
    tags=["分析模板"]
)
