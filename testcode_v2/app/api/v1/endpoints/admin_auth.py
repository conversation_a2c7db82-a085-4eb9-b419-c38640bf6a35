from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.core.security import create_access_token
from app.db.session import get_db
from app.services import administrator as admin_service
from app.schemas.administrator import (
    AdminLoginRequest,
    Admin<PERSON>oken,
    Administrator
)
from app.utils.deps import get_current_admin

router = APIRouter()


@router.post("/login", response_model=AdminToken, summary="管理员登录")
async def admin_login(
    login_data: AdminLoginRequest,
    db: Session = Depends(get_db)
):
    """
    管理员登录
    
    - **username**: 管理员用户名
    - **password**: 管理员密码
    """
    # 验证管理员凭证
    admin = admin_service.authenticate_admin(
        db, username=login_data.username, password=login_data.password
    )
    
    if not admin:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 创建访问令牌，添加管理员标识
    access_token = create_access_token(
        subject=admin.username,
        data={"is_admin": True, "admin_id": admin.id}
    )
    
    return AdminToken(
        access_token=access_token,
        token_type="bearer",
        admin_info=Administrator.model_validate(admin)
    )


@router.get("/me", response_model=Administrator, summary="获取当前管理员信息")
async def get_current_admin_info(
    current_admin: Administrator = Depends(get_current_admin)
):
    """
    获取当前登录管理员的信息
    """
    return current_admin


@router.post("/logout", summary="管理员登出")
async def admin_logout():
    """
    管理员登出
    
    注意：由于JWT token是无状态的，实际的登出逻辑需要在客户端处理
    """
    return {"message": "登出成功"}


def get_super_admin(
    current_admin: Administrator = Depends(get_current_admin)
) -> Administrator:
    """
    获取超级管理员（权限检查）
    """
    if not current_admin.is_super_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要超级管理员权限"
        )
    return current_admin 