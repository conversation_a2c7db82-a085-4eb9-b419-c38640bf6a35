from typing import List, Any, Dict
from fastapi import APIRouter, Depends, HTTPException, Query, Request, status
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.schemas.user import UserCreate, UserUpdate, User
from app.models.user import User as UserModel
from app.models.organization import Organization
from app.schemas.base import Response, PageData, PageInfo, PageResponse
from app.core.middleware.auth import get_current_user_with_permissions
from app.services.user import create_user, get_user_by_id, get_user_by_username, get_user_by_email, get_all_users, get_users_by_parent_id, load_users_relations, load_user_relations
from app.core.security import get_password_hash
from app.core.logger import log as logger

router = APIRouter()

@router.get("/", response_model=PageResponse[User], summary="获取子账号列表")
async def get_sub_accounts(
    request: Request,
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    current_user: User = Depends(get_current_user_with_permissions),
    db: Session = Depends(get_db)
):
    """获取子账号列表（只有企业管理员可访问）"""
    # 只有企业管理员可以访问本企业的普通用户
    if current_user.role_id != 2:  # 企业管理员
        raise HTTPException(status_code=403, detail="只有企业管理员才能访问此功能")
    
    # 查询本企业的普通用户（子账号）
    sub_accounts_query = db.query(UserModel).filter(
        UserModel.org_id == current_user.org_id,
        UserModel.role_id == 3,  # 只显示普通用户
        UserModel.id != current_user.id  # 排除自己
    ).order_by(UserModel.created_at.desc())
    
    # 获取总数
    total = sub_accounts_query.count()
    
    # 分页查询
    sub_accounts = sub_accounts_query.offset(skip).limit(limit).all()
    
    # 手动加载关联数据
    for user in sub_accounts:
        user = load_user_relations(db, user)
    
    # 构建分页信息
    page_info = PageInfo(
        page=skip // limit + 1,
        page_size=limit,
        total=total,
        total_page=(total + limit - 1) // limit
    )
    
    # 构建分页数据
    page_data = PageData(
        items=sub_accounts,
        page_info=page_info
    )
    
    return PageResponse(
        data=page_data,
        message="获取子账号列表成功",
        request_id=request.state.request_id
    )

@router.put("/language-preference", response_model=Response[Dict], summary="更新语言偏好")
async def update_language_preference(
    request: Request,
    language_data: dict,
    current_user: UserModel = Depends(get_current_user_with_permissions),
    db: Session = Depends(get_db)
):
    """更新用户语言偏好"""
    language = language_data.get('language', 'zh-CN')

    # 验证语言代码
    valid_languages = ['zh-CN', 'en-US']
    if language not in valid_languages:
        raise HTTPException(status_code=400, detail="不支持的语言")

    # 更新用户语言偏好
    current_user.language_preference = language
    db.commit()

    return Response(
        data={"language": language},
        message="语言偏好更新成功",
        request_id=request.state.request_id
    )

@router.get("/language-preference", response_model=Response[Dict], summary="获取语言偏好")
async def get_language_preference(
    request: Request,
    current_user: UserModel = Depends(get_current_user_with_permissions)
):
    """获取用户语言偏好"""
    return Response(
        data={"language": current_user.language_preference or 'zh-CN'},
        message="获取语言偏好成功",
        request_id=request.state.request_id
    )

@router.get("/{user_id}", response_model=Response[User], summary="获取用户详情")
async def get_user(
    user_id: int,
    request: Request,
    current_user: User = Depends(get_current_user_with_permissions),
    db: Session = Depends(get_db)
):
    """获取指定用户详情"""
    user = get_user_by_id(db, user_id=user_id)
    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在")
    
    # 权限检查：企业管理员只能查看本企业的用户
    if current_user.role_id == 2:  # 企业管理员
        if user.org_id != current_user.org_id:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="无权限访问")
    elif current_user.role_id == 3:  # 普通用户
        # 普通用户只能查看自己
        if user.id != current_user.id:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="无权限访问")
    # 超级管理员可以查看所有用户
    
    # 手动加载关联数据
    user = load_user_relations(db, user)
    
    return Response(
        data=user,
        message="获取用户详情成功",
        request_id=request.state.request_id
    )

@router.post("/", response_model=Response[User], summary="创建子用户")
async def create_sub_user(
    request: Request,
    user_data: UserCreate,
    current_user: User = Depends(get_current_user_with_permissions),
    db: Session = Depends(get_db)
):
    """创建子账号（只有企业管理员可访问）"""
    
    logger.info(f"企业管理员 {current_user.id}({current_user.username}) 尝试创建子账号: {user_data.username}")
    
    if current_user.role_id != 2:
        raise HTTPException(status_code=403, detail="只有企业管理员才能创建子账号")
    
    # 检查企业用户数量限制
    if current_user.org_id:
        logger.info(f"检查企业 {current_user.org_id} 的用户数量限制")
        
        # 获取企业信息
        organization = db.query(Organization).filter(Organization.id == current_user.org_id).first()
        if organization and organization.status == 1:  # 企业状态正常
            # 统计当前企业已有的用户数量（包括企业管理员和普通用户）
            current_user_count = db.query(UserModel).filter(
                UserModel.org_id == current_user.org_id,
                UserModel.is_active == True  # 只统计活跃用户
            ).count()
            
            logger.info(f"企业 {organization.name}(ID:{organization.id}) 当前用户数量: {current_user_count}/{organization.user_limit}")
            
            # 检查是否达到用户数量上限
            if current_user_count >= organization.user_limit:
                logger.warning(f"企业 {organization.name} 用户数量已达上限: {current_user_count}/{organization.user_limit}")
                raise HTTPException(
                    status_code=403,
                    detail=f"企业用户数量已达到上限({organization.user_limit}个)，请联系管理员升级或删除部分用户"
                )
        elif organization and organization.status == 0:
            # 企业已被禁用
            logger.warning(f"企业 {organization.name}(ID:{organization.id}) 已被禁用，管理员 {current_user.username} 无法创建子账号")
            raise HTTPException(
                status_code=403,
                detail="所属企业已被禁用，无法创建子账号"
            )
        elif not organization:
            # 企业不存在
            logger.error(f"企业管理员 {current_user.username} 所属企业 {current_user.org_id} 不存在")
            raise HTTPException(
                status_code=403,
                detail="所属企业不存在，请联系管理员"
            )
    # 检查用户名是否已存在
    existing_user = get_user_by_username(db, username=user_data.username)
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名已存在"
        )
    
    # 检查邮箱是否已存在
    if user_data.email:
        existing_email_user = db.query(UserModel).filter(UserModel.email == user_data.email).first()
        if existing_email_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已存在"
            )
    
    # 创建子用户，设置父级ID为当前用户
    hashed_password = get_password_hash(user_data.password)
    new_user = UserModel(
        username=user_data.username,
        email=user_data.email,
        hashed_password=hashed_password,
        is_active=user_data.is_active,
        is_superuser=False,  # 子账号不能是超级用户
        pid=current_user.id,  # 设置父级ID（保持兼容性）
        role_id=3,  # 新创建的子账号都是普通用户
        org_id=current_user.org_id,  # 继承创建者的企业ID
        can_create_project=user_data.can_create_project  # 设置项目创建权限
    )
    
    db.add(new_user)
    db.commit()
    db.refresh(new_user)

    logger.info(f"子账号创建成功: {new_user.username}(ID:{new_user.id})，创建者: {current_user.username}，企业: {current_user.org_id}")
    
    return Response(
        data=new_user,
        message="子账号创建成功",
        request_id=request.state.request_id
    )

@router.get("/sub-accounts/{user_id}", response_model=Response[User], summary="获取子账号详情")
async def get_sub_account(
    user_id: int,
    request: Request,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user_with_permissions)
):
    """获取子账号详情（只有企业管理员可访问）"""
    if current_user.role_id != 2:
        raise HTTPException(status_code=403, detail="只有企业管理员才能访问此功能")
    
    user = get_user_by_id(db, user_id)
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    # 企业管理员只能查看本企业的普通用户
    if user.org_id != current_user.org_id or user.role_id != 3:
        raise HTTPException(status_code=403, detail="只能查看本企业的普通用户")
    
    return Response(
        data=user,
        request_id=request.state.request_id
    )

@router.put("/{user_id}", response_model=Response[User], summary="更新子账号")
async def update_sub_account(
    user_id: int,
    user_data: UserUpdate,
    request: Request,
    current_user: User = Depends(get_current_user_with_permissions),
    db: Session = Depends(get_db)
):
    """更新子账号（只有企业管理员可访问）"""
    if current_user.role_id != 2:
        raise HTTPException(status_code=403, detail="只有企业管理员才能修改子账号")
    
    # 获取目标用户
    target_user = get_user_by_id(db, user_id=user_id)
    if not target_user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在")
    
    # 企业管理员只能修改本企业的普通用户
    if target_user.org_id != current_user.org_id or target_user.role_id != 3:
        raise HTTPException(status_code=403, detail="只能修改本企业的普通用户")
    
    # 检查用户名重复
    if user_data.username and user_data.username != target_user.username:
        existing_user = get_user_by_username(db, username=user_data.username)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户名已存在"
            )
    
    # 检查邮箱重复
    if user_data.email and user_data.email != target_user.email:
        existing_email_user = db.query(UserModel).filter(
            UserModel.email == user_data.email,
            UserModel.id != user_id
        ).first()
        if existing_email_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已存在"
            )
    
    # 更新用户
    update_data = user_data.dict(exclude_unset=True)
    
    # 更新字段
    for field, value in update_data.items():
        if hasattr(target_user, field) and value is not None:
            if field == "password":
                setattr(target_user, field, get_password_hash(value))
            else:
                setattr(target_user, field, value)
    
    db.commit()
    db.refresh(target_user)
    
    # 手动加载关联数据
    target_user = load_user_relations(db, target_user)
    
    return Response(
        data=target_user,
        message="子账号更新成功",
        request_id=request.state.request_id
    )

@router.delete("/{user_id}", response_model=Response[Dict], summary="删除子账号")
async def delete_sub_account(
    user_id: int,
    request: Request,
    current_user: User = Depends(get_current_user_with_permissions),
    db: Session = Depends(get_db)
):
    """删除子账号（只有企业管理员可访问）"""
    if current_user.role_id != 2:
        raise HTTPException(status_code=403, detail="只有企业管理员才能删除子账号")
    
    # 获取目标用户
    target_user = get_user_by_id(db, user_id=user_id)
    if not target_user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在")
    
    # 权限检查：不能删除自己
    if target_user.id == current_user.id:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="不能删除自己")
    
    # 企业管理员只能删除本企业的普通用户
    if target_user.org_id != current_user.org_id or target_user.role_id != 3:
        raise HTTPException(status_code=403, detail="只能删除本企业的普通用户")
    
    # 检查是否有子用户
    child_users = get_users_by_parent_id(db, pid=user_id, skip=0, limit=1)
    if child_users:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无法删除拥有子用户的用户，请先处理子用户"
        )
    
    # 删除用户
    db.delete(target_user)
    db.commit()
    
    return Response(
        data={"message": "用户删除成功"},
        message="子账号删除成功",
        request_id=request.state.request_id
    )

@router.put("/{user_id}/toggle-status", response_model=Response[Dict], summary="切换子账号状态")
async def toggle_sub_account_status(
    user_id: int,
    request: Request,
    current_user: User = Depends(get_current_user_with_permissions),
    db: Session = Depends(get_db)
):
    """切换子账号的启用/禁用状态（只有企业管理员可访问）"""
    if current_user.role_id != 2:
        raise HTTPException(status_code=403, detail="只有企业管理员才能修改子账号状态")
    
    target_user = get_user_by_id(db, user_id)
    if not target_user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    # 企业管理员只能修改本企业的普通用户状态
    if target_user.org_id != current_user.org_id or target_user.role_id != 3:
        raise HTTPException(status_code=403, detail="只能修改本企业的普通用户状态")
    
    # 切换状态
    target_user.is_active = not target_user.is_active
    db.commit()
    
    status_text = "激活" if target_user.is_active else "禁用"
    return Response(
        data={"status": target_user.is_active, "message": f"用户已{status_text}"},
        message=f"用户已{status_text}",
        request_id=request.state.request_id
    )

