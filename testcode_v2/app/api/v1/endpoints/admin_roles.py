from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.core.exceptions import BadRequestException, NotFoundException, ServerException
from app.db.session import get_db
from app.schemas.role import RoleCreate, RoleUpdate, Role, RoleList
from app.services.role import RoleService
from app.utils.deps import get_current_admin

router = APIRouter()

@router.get("/roles", response_model=RoleList)
async def get_roles(
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    search: Optional[str] = Query(None),
    level: Optional[int] = Query(None),
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin)
):
    """获取角色列表"""
    try:
        role_service = RoleService(db)
        result = role_service.get_roles(
            skip=skip,
            limit=limit,
            search=search,
            level=level
        )
        return result
    except BadRequestException as e:
        raise HTTPException(status_code=400, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取角色列表失败")

@router.post("/roles", response_model=Role)
async def create_role(
    role_data: RoleCreate,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin)
):
    """创建角色"""
    try:
        role_service = RoleService(db)
        role = role_service.create_role(role_data)
        return role
    except BadRequestException as e:
        raise HTTPException(status_code=400, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail="创建角色失败")

@router.get("/roles/{role_id}", response_model=Role)
async def get_role(
    role_id: int,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin)
):
    """获取角色详情"""
    try:
        role_service = RoleService(db)
        role = role_service.get_role_by_id(role_id)
        if not role:
            raise HTTPException(status_code=404, detail="角色不存在")
        return role
    except NotFoundException as e:
        raise HTTPException(status_code=404, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取角色详情失败")

@router.put("/roles/{role_id}", response_model=Role)
async def update_role(
    role_id: int,
    role_data: RoleUpdate,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin)
):
    """更新角色"""
    try:
        role_service = RoleService(db)
        role = role_service.update_role(role_id, role_data)
        return role
    except BadRequestException as e:
        raise HTTPException(status_code=400, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail="更新角色失败")

@router.delete("/roles/{role_id}")
async def delete_role(
    role_id: int,
    db: Session = Depends(get_db),
    current_admin = Depends(get_current_admin)
):
    """删除角色"""
    try:
        role_service = RoleService(db)
        role_service.delete_role(role_id)
        return {"message": "删除成功"}
    except BadRequestException as e:
        raise HTTPException(status_code=400, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail="删除角色失败")

 