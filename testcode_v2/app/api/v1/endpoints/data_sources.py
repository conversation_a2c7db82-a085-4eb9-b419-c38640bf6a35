from typing import List, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Request, BackgroundTasks
from sqlalchemy.orm import Session
import sqlalchemy.exc
from sqlalchemy import text
import json
from datetime import datetime

from app.db.session import get_db
from app.schemas import DataSource, DataSourceCreate, DataSourceUpdate, DataSourceDetail, DataSourceTestConnection
from app.models.data_source import DataSource as DataSourceModel
from app.models.project import Project as ProjectModel
from app.schemas.base import Response, PageData, PageInfo
from app.core.logger import log
from app.models.data_source import DataSourceType
from app.utils.oracle_init import init_oracle_client, reconnect_oracle, is_oracle_initialized
from app.utils.time_utils import get_shanghai_time

router = APIRouter()

@router.post("/", response_model=Response[DataSource], summary="创建数据源")
async def create_data_source(
    data_source_in: DataSourceCreate,
    request: Request,
    db: Session = Depends(get_db)
):
    """创建新数据源"""
    try:
        # 检查项目是否存在
        project = db.query(ProjectModel).filter(ProjectModel.id == data_source_in.project_id).first()
        if not project:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        data_source_dict = data_source_in.dict()
        
        # 特别处理type字段，确保它是有效的枚举值
        if 'type' in data_source_dict and data_source_dict['type']:
            try:
                # 记录原始类型
                original_type = data_source_dict['type']
                log.info(f"处理数据源类型: {original_type}, 类型: {type(original_type)}")
                
                # 如果不是DataSourceType类型，尝试转换
                if not isinstance(data_source_dict['type'], DataSourceType):
                    # 先转换为字符串再转为大写，以确保能正确匹配
                    if isinstance(original_type, str):
                        # 直接匹配查找，大小写不敏感
                        for enum_member in DataSourceType:
                            if enum_member.value.lower() == original_type.lower():
                                data_source_dict['type'] = enum_member
                                log.info(f"成功匹配枚举值: {enum_member}")
                                break
                        else:
                            # 如果尝试直接匹配失败，再尝试通过构造函数转换
                            try:
                                data_source_dict['type'] = DataSourceType(original_type.upper())
                                log.info(f"通过构造函数转换成功: {data_source_dict['type']}")
                            except ValueError:
                                log.warning(f"无法匹配枚举值，尝试规范化处理...")
                                # 试着规范化处理
                                cleaned_type = original_type.upper().replace(" ", "_").replace("-", "_").strip()
                                for enum_member in DataSourceType:
                                    cleaned_enum = enum_member.value.upper().replace(" ", "_").replace("-", "_")
                                    if cleaned_enum == cleaned_type:
                                        data_source_dict['type'] = enum_member
                                        log.info(f"通过规范化匹配成功: {enum_member}")
                                        break
                                else:
                                    # 如果所有尝试都失败，使用OTHER作为默认值
                                    log.warning(f"所有匹配尝试失败，使用OTHER作为默认值")
                                    data_source_dict['type'] = DataSourceType.OTHER
            except Exception as e:
                # 如果转换过程中出现任何错误，使用OTHER作为默认值
                log.warning(f"数据源类型转换过程中出错: {str(e)}，使用OTHER代替")
                data_source_dict['type'] = DataSourceType.OTHER
            
            log.info(f"最终数据源类型: {data_source_dict['type']}")
        
        # 确保config是字典类型
        if 'config' in data_source_dict:
            if isinstance(data_source_dict['config'], str):
                try:
                    data_source_dict['config'] = json.loads(data_source_dict['config'])
                except json.JSONDecodeError:
                    log.warning(f"无效的config JSON格式: {data_source_dict['config']}")
                    data_source_dict['config'] = {}
        
        data_source = DataSourceModel(**data_source_dict)
        db.add(data_source)
        db.commit()
        db.refresh(data_source)
        
        # 处理返回的数据
        processed_data_source = process_data_source(data_source)
        
        return Response(data=processed_data_source, request_id=request.state.request_id)
    except LookupError as e:
        log.error(f"创建数据源时枚举类型查找错误: {str(e)}")
        db.rollback()
        return Response(
            code=500,
            message=f"创建数据源失败: {str(e)}",
            request_id=request.state.request_id
        )
    except Exception as e:
        log.error(f"创建数据源失败: {str(e)}")
        db.rollback()
        return Response(
            code=500,
            message=f"创建数据源失败: {str(e)}",
            request_id=request.state.request_id
        )

@router.get("/", response_model=Response[PageData[DataSource]], summary="获取数据源列表")
async def get_data_sources(
    request: Request,
    project_id: str = Query(None, description="项目ID"),
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(10, ge=1, le=100, description="返回记录数"),
    db: Session = Depends(get_db)
):
    """获取数据源列表"""
    try:
        log.info(f"开始查询数据源列表: project_id={project_id}, skip={skip}, limit={limit}")
        
        # 使用原始SQL查询避免ORM映射问题
        query = """
            SELECT 
                id, name, description, type, config, project_id, created_at, updated_at 
            FROM 
                data_sources
        """
        params = {}
        
        if project_id:
            query += " WHERE project_id = :project_id"
            params["project_id"] = project_id
            
        query += " LIMIT :limit OFFSET :skip"
        params["limit"] = limit
        params["skip"] = skip
        
        # 执行查询
        log.info(f"执行SQL查询: {query}")
        result = db.execute(text(query), params)
        
        # 获取列名
        columns = result.keys()
        log.info(f"查询列: {columns}")
        
        # 获取行数据
        rows = result.fetchall()
        log.info(f"获取到{len(rows)}条记录")
        
        # 计算总数
        count_query = "SELECT COUNT(*) FROM data_sources"
        if project_id:
            count_query += " WHERE project_id = :project_id"
            
        total_result = db.execute(text(count_query), {"project_id": project_id} if project_id else {})
        total = total_result.scalar()
        log.info(f"总记录数: {total}")
        
        # 处理结果
        processed_data_sources = []
        for row in rows:
            try:
                data_dict = dict(zip(columns, row))
                
                # 特别处理type字段
                if 'type' in data_dict and data_dict['type']:
                    type_value = data_dict['type']
                    log.info(f"处理数据源类型: {type_value}")
                    
                    if isinstance(type_value, str):
                        # 尝试匹配枚举值
                        for enum_member in DataSourceType:
                            if enum_member.name.lower() == type_value.lower() or enum_member.value.lower() == type_value.lower():
                                data_dict['type'] = enum_member.value
                                log.info(f"成功匹配枚举值: {enum_member.value}")
                                break
                        else:
                            # 如果没有找到匹配，使用原始值的大写形式
                            data_dict['type'] = type_value.upper()
                            log.info(f"使用大写形式: {data_dict['type']}")
                
                # 处理config字段
                if 'config' in data_dict and data_dict['config']:
                    config_value = data_dict['config']
                    if isinstance(config_value, str):
                        try:
                            data_dict['config'] = json.loads(config_value)
                        except json.JSONDecodeError:
                            log.warning(f"无法解析config字段: {config_value}")
                            data_dict['config'] = {}
                
                processed_data_sources.append(data_dict)
            except Exception as e:
                log.error(f"处理数据源记录出错: {str(e)}")
        
        # 计算总页数
        total_page = (total + limit - 1) // limit if limit > 0 else 0
        
        # 创建分页信息
        page_info = PageInfo(
            page=skip // limit + 1 if limit > 0 else 1,
            page_size=limit,
            total=total,
            total_page=total_page
        )
        
        # 创建分页数据
        page_data = PageData(
            items=processed_data_sources,
            page_info=page_info
        )
        
        log.info(f"数据源列表查询成功，返回{len(processed_data_sources)}条记录")
        return Response(data=page_data, request_id=request.state.request_id)
    except Exception as e:
        log.error(f"获取数据源列表失败: {str(e)}")
        try:
            # 优雅降级：使用ORM方式再试一次
            log.info("尝试使用ORM方式获取数据")
            query = db.query(DataSourceModel)
            
            if project_id:
                query = query.filter(DataSourceModel.project_id == project_id)
            
            total = query.count()
            data_sources = query.offset(skip).limit(limit).all()
            
            processed_data_sources = []
            for ds in data_sources:
                try:
                    processed_ds = process_data_source(ds)
                    processed_data_sources.append(processed_ds)
                except Exception as ds_error:
                    log.error(f"处理单个数据源失败: {str(ds_error)}")
                    # 手动构造一个基本数据对象
                    processed_data_sources.append({
                        "id": getattr(ds, "id", "unknown"),
                        "name": getattr(ds, "name", "未知数据源"),
                        "description": getattr(ds, "description", ""),
                        "type": "OTHER",
                        "config": {},
                        "project_id": getattr(ds, "project_id", ""),
                        "created_at": getattr(ds, "created_at", get_shanghai_time()),
                        "updated_at": getattr(ds, "updated_at", get_shanghai_time())
                    })
            
            # 计算总页数
            total_page = (total + limit - 1) // limit if limit > 0 else 0
            
            # 创建分页信息
            page_info = PageInfo(
                page=skip // limit + 1,
                page_size=limit,
                total=total,
                total_page=total_page
            )
            
            # 创建分页数据
            page_data = PageData(
                items=processed_data_sources,
                page_info=page_info
            )
            
            log.info(f"使用ORM方式成功获取数据源列表，返回{len(processed_data_sources)}条记录")
            return Response(data=page_data, request_id=request.state.request_id)
        except Exception as orm_error:
            log.error(f"ORM方式获取数据源列表也失败: {str(orm_error)}")
            return Response(
                code=500,
                message=f"获取数据源列表失败: {str(e)}",
                request_id=request.state.request_id
            )

@router.get("/{data_source_id}", response_model=Response[DataSourceDetail], summary="获取数据源详情")
async def get_data_source(
    data_source_id: str,
    request: Request,
    db: Session = Depends(get_db)
):
    """获取数据源详情"""
    try:
        log.info(f"获取数据源详情: {data_source_id}")
        
        # 使用原始SQL查询避免ORM枚举转换问题
        query = """
            SELECT 
                ds.id, ds.name, ds.description, ds.type, ds.config, ds.project_id, ds.created_at, ds.updated_at,
                p.name as project_name
            FROM 
                data_sources ds
            LEFT JOIN
                projects p ON ds.project_id = p.id
            WHERE 
                ds.id = :data_source_id
        """
        
        # 执行查询
        result = db.execute(text(query), {"data_source_id": data_source_id})
        
        # 获取单条记录
        row = result.fetchone()
        if not row:
            log.warning(f"数据源不存在: {data_source_id}")
            raise HTTPException(status_code=404, detail="数据源不存在")
        
        # 将行数据转换为字典
        columns = result.keys()
        data_dict = dict(zip(columns, row))
        
        # 特别处理type字段
        if 'type' in data_dict and data_dict['type']:
            type_value = data_dict['type']
            log.info(f"处理数据源类型: {type_value}")
            
            if isinstance(type_value, str):
                # 尝试匹配枚举值
                for enum_member in DataSourceType:
                    if enum_member.name.lower() == type_value.lower() or enum_member.value.lower() == type_value.lower():
                        data_dict['type'] = enum_member.value
                        log.info(f"成功匹配枚举值: {enum_member.value}")
                        break
                else:
                    # 如果没有找到匹配，使用原始值的大写形式
                    data_dict['type'] = type_value.upper()
                    log.info(f"使用大写形式: {data_dict['type']}")
        
        # 处理config字段
        if 'config' in data_dict and data_dict['config']:
            config_value = data_dict['config']
            if isinstance(config_value, str):
                try:
                    data_dict['config'] = json.loads(config_value)
                except json.JSONDecodeError:
                    log.warning(f"无法解析config字段: {config_value}")
                    data_dict['config'] = {}
        
        # 查询关联的工具
        tools_query = """
            SELECT 
                id, name, description, type, template, datasource_id, project_id, created_at, updated_at
            FROM 
                tools
            WHERE 
                datasource_id = :data_source_id
        """
        
        tools_result = db.execute(text(tools_query), {"data_source_id": data_source_id})
        tools_rows = tools_result.fetchall()
        
        # 处理工具数据
        tools = []
        for tool_row in tools_rows:
            tool_columns = tools_result.keys()
            tool_dict = dict(zip(tool_columns, tool_row))
            
            # 处理tool字段
            if 'template' in tool_dict and isinstance(tool_dict['template'], str):
                try:
                    tool_dict['template'] = json.loads(tool_dict['template'])
                except json.JSONDecodeError:
                    tool_dict['template'] = {}
            
            tools.append(tool_dict)
        
        # 添加工具列表到数据源详情
        data_dict['tools'] = tools
        
        log.info(f"数据源详情查询成功，关联工具数量：{len(tools)}")
        return Response(data=data_dict, request_id=request.state.request_id)
    except HTTPException:
        # 直接重新抛出HTTP异常
        raise
    except Exception as e:
        log.error(f"获取数据源详情失败: {str(e)}")
        try:
            # 降级：尝试使用ORM方式
            log.info("尝试使用ORM方式获取数据源详情")
            data_source = db.query(DataSourceModel).filter(DataSourceModel.id == data_source_id).first()
            
            if not data_source:
                log.warning(f"数据源不存在: {data_source_id}")
                raise HTTPException(status_code=404, detail="数据源不存在")
            
            # 处理返回的数据
            try:
                processed_data_source = process_data_source(data_source)
                log.info("ORM方式获取数据源详情成功")
                return Response(data=processed_data_source, request_id=request.state.request_id)
            except Exception as process_error:
                log.error(f"处理数据源对象失败: {str(process_error)}")
                # 手动构造基本数据
                basic_data = {
                    "id": data_source.id,
                    "name": getattr(data_source, "name", "未知数据源"),
                    "description": getattr(data_source, "description", ""),
                    "type": "OTHER",
                    "config": {},
                    "project_id": getattr(data_source, "project_id", ""),
                    "created_at": getattr(data_source, "created_at", get_shanghai_time()),
                    "updated_at": getattr(data_source, "updated_at", get_shanghai_time()),
                    "tools": []
                }
                return Response(data=basic_data, request_id=request.state.request_id)
        except HTTPException:
            # 直接重新抛出HTTP异常
            raise
        except Exception as orm_error:
            log.error(f"ORM方式获取数据源详情也失败: {str(orm_error)}")
            return Response(
                code=500,
                message=f"获取数据源详情失败: {str(e)}",
                request_id=request.state.request_id
            )

@router.put("/{data_source_id}", response_model=Response[DataSource], summary="更新数据源")
async def update_data_source(
    data_source_id: str,
    data_source_in: DataSourceUpdate,
    request: Request,
    db: Session = Depends(get_db)
):
    """更新数据源"""
    try:
        log.info(f"尝试更新数据源: {data_source_id}")
        data_source = db.query(DataSourceModel).filter(DataSourceModel.id == data_source_id).first()
        if not data_source:
            log.warning(f"数据源不存在: {data_source_id}")
            raise HTTPException(status_code=404, detail="数据源不存在")
        
        # 检查用户是否有权限修改此数据源(通过项目关联)
        project = db.query(ProjectModel).filter(ProjectModel.id == data_source.project_id).first()
        if not project:
            log.warning(f"数据源关联的项目不存在: {data_source.project_id}")
            raise HTTPException(status_code=404, detail="数据源关联的项目不存在")
        
        update_data = data_source_in.dict(exclude_unset=True)
        
        # 特别处理type字段，确保它是有效的枚举值
        if 'type' in update_data and update_data['type']:
            try:
                # 记录原始类型
                original_type = update_data['type']
                log.info(f"处理数据源类型: {original_type}, 类型: {type(original_type)}")
                
                # 如果不是DataSourceType类型，尝试转换
                if not isinstance(update_data['type'], DataSourceType):
                    # 先转换为字符串再进行处理
                    if isinstance(original_type, str):
                        # 尝试直接匹配查找，大小写不敏感
                        matched = False
                        for enum_member in DataSourceType:
                            if enum_member.name.lower() == original_type.lower() or enum_member.value.lower() == original_type.lower():
                                update_data['type'] = enum_member
                                log.info(f"成功匹配枚举值: {enum_member}")
                                matched = True
                                break
                                
                        if not matched:
                            # 如果尝试直接匹配失败，再尝试通过构造函数转换
                            try:
                                update_data['type'] = DataSourceType(original_type.upper())
                                log.info(f"通过构造函数转换成功: {update_data['type']}")
                            except ValueError as ve:
                                log.warning(f"通过构造函数转换失败: {str(ve)}")
                                # 试着规范化处理
                                try:
                                    cleaned_type = original_type.upper().replace(" ", "_").replace("-", "_").strip()
                                    matched_by_clean = False
                                    for enum_member in DataSourceType:
                                        cleaned_enum_name = enum_member.name.upper().replace(" ", "_").replace("-", "_")
                                        cleaned_enum_value = enum_member.value.upper().replace(" ", "_").replace("-", "_")
                                        
                                        if cleaned_enum_name == cleaned_type or cleaned_enum_value == cleaned_type:
                                            update_data['type'] = enum_member
                                            log.info(f"通过规范化匹配成功: {enum_member}")
                                            matched_by_clean = True
                                            break
                                            
                                    if not matched_by_clean:
                                        # 如果所有尝试都失败，使用OTHER作为默认值
                                        log.warning(f"所有匹配尝试失败，使用OTHER作为默认值. 原始值: {original_type}")
                                        update_data['type'] = DataSourceType.OTHER
                                except Exception as e2:
                                    log.warning(f"规范化处理过程中出错: {str(e2)}，使用OTHER代替")
                                    update_data['type'] = DataSourceType.OTHER
            except Exception as e:
                # 如果转换过程中出现任何错误，使用OTHER作为默认值
                log.warning(f"数据源类型转换过程中出错: {str(e)}，使用OTHER代替")
                update_data['type'] = DataSourceType.OTHER
            
            log.info(f"最终数据源类型: {update_data['type']}")
        
        # 确保config是字典类型
        if 'config' in update_data:
            if isinstance(update_data['config'], str):
                try:
                    update_data['config'] = json.loads(update_data['config'])
                except json.JSONDecodeError:
                    log.warning(f"无效的config JSON格式: {update_data['config']}")
                    update_data['config'] = {}
        
        for field, value in update_data.items():
            setattr(data_source, field, value)
        
        db.commit()
        db.refresh(data_source)
        
        # 处理返回的数据
        processed_data_source = process_data_source(data_source)
        
        log.info(f"数据源更新成功: {data_source_id}")
        return Response(data=processed_data_source, request_id=request.state.request_id)
    except LookupError as e:
        log.error(f"更新数据源时枚举类型查找错误: {str(e)}")
        db.rollback()
        return Response(
            code=500,
            message=f"更新数据源失败: {str(e)}",
            request_id=request.state.request_id
        )
    except HTTPException as he:
        # 直接抛出HTTP异常
        raise he
    except Exception as e:
        log.error(f"更新数据源失败: {str(e)}")
        db.rollback()
        return Response(
            code=500,
            message=f"更新数据源失败: {str(e)}",
            request_id=request.state.request_id
        )

@router.delete("/{data_source_id}", response_model=Response, summary="删除数据源")
async def delete_data_source(
    data_source_id: str,
    request: Request,
    db: Session = Depends(get_db)
):
    """删除数据源"""
    try:
        # 查询数据源
        data_source = db.query(DataSourceModel).filter(DataSourceModel.id == data_source_id).first()
        
        if not data_source:
            raise HTTPException(status_code=404, detail="数据源不存在")
        
        # 执行删除
        db.delete(data_source)
        db.commit()
        
        return Response(message="数据源删除成功", request_id=request.state.request_id)
    except HTTPException:
        # 直接抛出HTTP异常
        raise
    except Exception as e:
        log.error(f"删除数据源失败: {str(e)}")
        db.rollback()
        return Response(
            code=500,
            message=f"删除数据源失败: {str(e)}",
            request_id=request.state.request_id
        )

@router.post("/{data_source_id}/test-connection", response_model=Response)
async def test_connection(
    data_source_id: str,
    request: Request,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
):
    """测试数据源连接"""
    try:
        import cx_Oracle
        # 获取数据源信息
        data_source = db.query(DataSource).filter(DataSource.id == data_source_id).first()
        if not data_source:
            raise HTTPException(status_code=404, detail="数据源不存在")
        
        print(f"测试数据源连接，ID: {data_source_id}，类型: {data_source.safe_type}")
        
        # 测试连接
        if data_source.safe_type == DataSourceType.ORACLE.value:
            try:
                config = data_source.parsed_config
                print(f"Oracle连接配置: {config}")
                
                # 提取连接参数
                host = config.get("host")
                port = config.get("port")
                sid = config.get("sid")
                service_name = config.get("service_name")
                username = config.get("username")
                password = config.get("password")
                
                # 检查必要参数
                if not host or not port or not username or not password or (not sid and not service_name):
                    return Response(
                        code=400,
                        message="Oracle连接配置不完整",
                        request_id=request.state.request_id
                    )
                
                # 确保Oracle客户端已初始化
                if not is_oracle_initialized():
                    if not init_oracle_client():
                        return Response(
                            code=500,
                            message="Oracle客户端初始化失败，请检查Oracle Instant Client是否正确安装",
                            request_id=request.state.request_id
                        )
                
                # 使用重连方法创建连接
                if sid:
                    # 如果提供了SID，则使用SID连接
                    dsn = cx_Oracle.makedsn(host, port, sid=sid)
                    print(f"Oracle DSN (使用SID): {dsn}")
                    connection = cx_Oracle.connect(username, password, dsn)
                else:
                    # 否则使用service_name连接
                    connection, cursor = reconnect_oracle(
                        host=host,
                        port=port,
                        service_name=service_name,
                        username=username,
                        password=password
                    )
                    
                    if connection is None or cursor is None:
                        return Response(
                            code=500,
                            message="Oracle连接失败，无法创建连接",
                            request_id=request.state.request_id
                        )
                
                # 如果使用的是SID连接，需要手动创建游标
                if sid:
                    cursor = connection.cursor()
                
                # 执行简单查询测试
                cursor.execute("SELECT 1 FROM DUAL")
                result = cursor.fetchone()
                
                print(f"Oracle连接测试结果: {result}")
                
                # 关闭连接
                cursor.close()
                connection.close()
                
                # 测试成功
                return Response(
                    data={"message": "Oracle连接成功", "result": result[0] if result else None},
                    request_id=request.state.request_id
                )
            except Exception as e:
                print(f"Oracle连接测试失败: {str(e)}")
                return Response(
                    code=500,
                    message=f"Oracle连接失败: {str(e)}",
                    request_id=request.state.request_id
                )
        
        elif data_source.safe_type == DataSourceType.MYSQL.value:
            try:
                import mysql.connector
                
                config = data_source.parsed_config
                print(f"MySQL连接配置: {config}")
                
                # 提取连接参数
                host = config.get("host")
                port = config.get("port", 3306)
                username = config.get("username")
                password = config.get("password")
                database = config.get("database")
                
                # 检查必要参数
                if not host or not username or not password:
                    return Response(
                        code=400,
                        message="MySQL连接配置不完整",
                        request_id=request.state.request_id
                    )
                
                # 连接测试
                conn_params = {
                    "host": host,
                    "port": int(port),
                    "user": username,
                    "password": password
                }
                
                if database:
                    conn_params["database"] = database
                
                connection = mysql.connector.connect(**conn_params)
                cursor = connection.cursor()
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                
                print(f"MySQL连接测试结果: {result}")
                
                # 关闭连接
                cursor.close()
                connection.close()
                
                # 测试成功
                return Response(
                    data={"message": "MySQL连接成功", "result": result[0] if result else None},
                    request_id=request.state.request_id
                )
            except Exception as e:
                print(f"MySQL连接测试失败: {str(e)}")
                return Response(
                    code=500,
                    message=f"MySQL连接失败: {str(e)}",
                    request_id=request.state.request_id
                )
        
        elif data_source.safe_type == DataSourceType.POSTGRESQL.value:
            try:
                import psycopg2
                
                config = data_source.parsed_config
                print(f"PostgreSQL连接配置: {config}")
                
                # 提取连接参数
                host = config.get("host")
                port = config.get("port", 5432)
                username = config.get("username")
                password = config.get("password")
                database = config.get("database")
                
                # 检查必要参数
                if not host or not username or not password or not database:
                    return Response(
                        code=400,
                        message="PostgreSQL连接配置不完整",
                        request_id=request.state.request_id
                    )
                
                # 连接测试
                connection = psycopg2.connect(
                    host=host,
                    port=port,
                    user=username,
                    password=password,
                    dbname=database
                )
                cursor = connection.cursor()
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                
                print(f"PostgreSQL连接测试结果: {result}")
                
                # 关闭连接
                cursor.close()
                connection.close()
                
                # 测试成功
                return Response(
                    data={"message": "PostgreSQL连接成功", "result": result[0] if result else None},
                    request_id=request.state.request_id
                )
            except Exception as e:
                print(f"PostgreSQL连接测试失败: {str(e)}")
                return Response(
                    code=500,
                    message=f"PostgreSQL连接失败: {str(e)}",
                    request_id=request.state.request_id
                )
        
        elif data_source.safe_type == DataSourceType.HTTP_API.value:
            try:
                import requests
                
                config = data_source.parsed_config
                print(f"HTTP API连接配置: {config}")
                
                # 提取连接参数
                base_url = config.get("base_url")
                test_endpoint = config.get("test_endpoint", "/")
                
                # 检查必要参数
                if not base_url:
                    return Response(
                        code=400,
                        message="HTTP API连接配置不完整",
                        request_id=request.state.request_id
                    )
                
                # 如果base_url不以/结尾且test_endpoint不以/开头，添加/
                if not base_url.endswith("/") and not test_endpoint.startswith("/"):
                    test_url = f"{base_url}/{test_endpoint}"
                else:
                    test_url = f"{base_url}{test_endpoint}"
                
                print(f"测试URL: {test_url}")
                
                # 连接测试
                response = requests.get(test_url, timeout=10)
                response.raise_for_status()  # 如果状态码不是200，会抛出异常
                
                # 测试成功
                return Response(
                    data={
                        "message": "HTTP API连接成功",
                        "status_code": response.status_code,
                        "content": response.text[:1000]  # 只返回前1000字符
                    },
                    request_id=request.state.request_id
                )
            except Exception as e:
                print(f"HTTP API连接测试失败: {str(e)}")
                return Response(
                    code=500,
                    message=f"HTTP API连接失败: {str(e)}",
                    request_id=request.state.request_id
                )
        
        else:
            return Response(
                code=400,
                message=f"不支持的数据源类型: {data_source.safe_type}",
                request_id=request.state.request_id
            )
    except cx_Oracle.Error as e:
        print(f"Oracle连接测试过程中出错: {str(e)}")
        return Response(
            code=500,
            message=f"Oracle连接测试过程中出错: {str(e)}",
            request_id=request.state.request_id
        )
    except Exception as e:
        print(f"测试连接过程中出错: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return Response(
            code=500,
            message=f"测试连接失败: {str(e)}",
            request_id=request.state.request_id
        )

def convert_lob_objects(data):
    """
    递归处理数据，将cx_Oracle.LOB对象转换为字符串
    """
    import cx_Oracle
    
    if data is None:
        return None
    elif isinstance(data, cx_Oracle.LOB):
        try:
            # 读取LOB对象内容
            return data.read()
        except Exception as e:
            print(f"读取LOB对象失败: {str(e)}")
            return str(data)
    elif isinstance(data, dict):
        return {k: convert_lob_objects(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [convert_lob_objects(item) for item in data]
    elif hasattr(data, '__dict__'):  # 处理自定义对象
        try:
            return convert_lob_objects(data.__dict__)
        except:
            return str(data)
    else:
        return data

def process_data_source(data_source):
    """处理数据源对象，确保config字段是字典类型"""
    if hasattr(data_source, 'to_api_model'):
        # 使用模型自带方法转换
        return data_source.to_api_model()
    
    # 如果config是字符串，尝试解析为字典
    if hasattr(data_source, 'config') and isinstance(data_source.config, str):
        try:
            data_source.config = json.loads(data_source.config)
        except json.JSONDecodeError:
            log.warning(f"无法解析数据源config字段: {data_source.config}")
    
    return data_source 