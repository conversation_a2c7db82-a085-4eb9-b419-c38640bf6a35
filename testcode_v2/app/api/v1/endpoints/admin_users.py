from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session, joinedload

from app.db.session import get_db
from app.services import administrator as admin_service
from app.services.user import load_users_relations, load_user_relations
from app.schemas.administrator import Administrator
from app.schemas.user import User, UserCreate, UserUpdate, AdminUserDetail
from app.models.user import User as UserModel
from app.models.organization import Organization
from app.api.v1.endpoints.admin_auth import get_current_admin

router = APIRouter()


@router.get("/users-count", response_model=dict, summary="获取用户总数")
async def get_users_count(
    org_id: Optional[int] = Query(None, description="企业ID，用于筛选企业用户"),
    username: Optional[str] = Query(None, description="用户名搜索"),
    is_active: Optional[bool] = Query(None, description="账户状态筛选"),
    current_admin: Administrator = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """获取用户总数（支持筛选条件）"""
    # 构建查询条件
    query = db.query(UserModel)
    
    # 添加筛选条件
    if org_id is not None:
        query = query.filter(UserModel.org_id == org_id)
    
    if username is not None:
        query = query.filter(UserModel.username.ilike(f"%{username}%"))
    
    if is_active is not None:
        query = query.filter(UserModel.is_active == is_active)
    
    count = query.count()
    return {"count": count}


@router.get("/organizations-simple", response_model=List[dict], summary="获取企业简化列表")
async def get_organizations_simple(
    current_admin: Administrator = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """获取企业简化列表（用于下拉选择）"""
    organizations = db.query(Organization.id, Organization.name).filter(Organization.status == 1).all()
    return [{"id": org.id, "name": org.name} for org in organizations]


@router.get("/users", response_model=List[AdminUserDetail], summary="获取用户列表")
async def get_users(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    pid: Optional[int] = Query(None, description="父级用户ID，用于筛选子用户"),
    org_id: Optional[int] = Query(None, description="企业ID，用于筛选企业用户"),
    username: Optional[str] = Query(None, description="用户名搜索"),
    is_active: Optional[bool] = Query(None, description="账户状态筛选"),
    current_admin: Administrator = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """获取用户列表（包含企业和角色信息）"""
    if pid is not None:
        users = admin_service.get_users_by_parent_id(db, pid=pid, skip=skip, limit=limit)
        # 为子用户列表添加父级信息
        result = []
        for user in users:
            user_dict = AdminUserDetail.from_orm(user).dict()
            # 子用户的父级用户名和子账号数量
            if user.pid != 0:
                parent = admin_service.get_user_by_id_for_admin(db, user.pid)
                user_dict['parent_username'] = parent.username if parent else None
            user_dict['children_count'] = admin_service.get_children_count_by_user_id(db, user.id)
            result.append(user_dict)
        return result
    else:
        # 构建查询条件
        query = db.query(UserModel)
        
        # 添加筛选条件
        if org_id is not None:
            query = query.filter(UserModel.org_id == org_id)
        
        if username is not None:
            query = query.filter(UserModel.username.ilike(f"%{username}%"))
        
        if is_active is not None:
            query = query.filter(UserModel.is_active == is_active)
        
        # 添加排序：主账号优先(pid=0)，然后按创建时间倒序(新创建的在前)
        query = query.order_by(
            UserModel.pid.asc(),  # pid=0的主账号排在前面
            UserModel.created_at.desc()  # 创建时间倒序，新的在前
        )
        
        # 执行查询
        users = query.offset(skip).limit(limit).all()
        
        # 手动加载关联数据
        users = load_users_relations(db, users)
        
        # 构造包含父级用户名和子账号数量的响应
        result = []
        for user in users:
            user_dict = AdminUserDetail.from_orm(user).dict()
            
            # 获取父级用户名
            if user.pid != 0:
                parent = db.query(UserModel).filter(UserModel.id == user.pid).first()
                user_dict['parent_username'] = parent.username if parent else None
            else:
                user_dict['parent_username'] = None
            
            # 获取子账号数量
            user_dict['children_count'] = db.query(UserModel).filter(UserModel.pid == user.id).count()
            
            result.append(user_dict)
        return result


@router.get("/users/{user_id}", response_model=AdminUserDetail, summary="获取用户详情")
async def get_user(
    user_id: int,
    current_admin: Administrator = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """获取指定用户的详细信息（包含企业和角色信息）"""
    user = admin_service.get_user_by_id_for_admin(db, user_id=user_id)
    if user is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在")
    
    # 手动加载关联数据
    user = load_user_relations(db, user)
    
    # 构造包含父级用户名和子账号数量的响应
    user_dict = AdminUserDetail.from_orm(user).dict()
    
    # 获取父级用户名
    if user.pid != 0:
        parent = db.query(UserModel).filter(UserModel.id == user.pid).first()
        user_dict['parent_username'] = parent.username if parent else None
    else:
        user_dict['parent_username'] = None
    
    # 获取子账号数量
    user_dict['children_count'] = db.query(UserModel).filter(UserModel.pid == user.id).count()
    
    return user_dict


@router.post("/users", response_model=AdminUserDetail, summary="创建用户")
async def create_user(
    user_data: UserCreate,
    current_admin: Administrator = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """创建新用户（只能创建父级用户，pid自动设为0）"""
    user = admin_service.create_user_by_admin(
        db=db,
        username=user_data.username,
        password=user_data.password,
        email=user_data.email,
        pid=0,  # 强制设置为0，后台管理只能创建父级用户
        org_id=user_data.org_id,
        role_id=user_data.role_id,
        is_active=user_data.is_active if user_data.is_active is not None else True
    )
    # 手动加载关联数据
    user = load_user_relations(db, user)
    return user


@router.put("/users/{user_id}", response_model=AdminUserDetail, summary="更新用户")
async def update_user(
    user_id: int,
    user_data: UserUpdate,
    current_admin: Administrator = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """更新用户信息"""
    update_data = user_data.dict(exclude_unset=True)
    # 如果更新数据中包含pid，强制设为0（后台管理不允许创建子用户）
    if 'pid' in update_data:
        update_data['pid'] = 0
        
    updated_user = admin_service.update_user_by_admin(db=db, user_id=user_id, **update_data)
    
    if not updated_user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在")
    
    # 手动加载关联数据
    updated_user = load_user_relations(db, updated_user)
    
    return updated_user


@router.delete("/users/{user_id}", summary="删除用户")
async def delete_user(
    user_id: int,
    current_admin: Administrator = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """删除用户"""
    success = admin_service.delete_user_by_admin(db, user_id=user_id)
    if not success:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在")
    
    return {"message": "用户删除成功"} 