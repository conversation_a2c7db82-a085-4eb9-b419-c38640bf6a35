from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.services.statistics import StatisticsService
from app.schemas.administrator import Administrator
from app.api.v1.endpoints.admin_auth import get_current_admin

router = APIRouter()


@router.get("/system-overview", summary="获取系统总览")
async def get_system_overview(
    current_admin: Administrator = Depends(get_current_admin),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """获取系统总览统计数据"""
    try:
        overview = StatisticsService.get_system_overview(db)
        return overview
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取系统总览失败: {str(e)}"
        )


@router.get("/organization-ranking", summary="获取企业排行榜")
async def get_organization_ranking(
    limit: int = Query(10, ge=1, le=50, description="返回的记录数"),
    current_admin: Administrator = Depends(get_current_admin),
    db: Session = Depends(get_db)
) -> List[Dict[str, Any]]:
    """获取企业排行榜"""
    try:
        ranking = StatisticsService.get_organization_ranking(db, limit=limit)
        return ranking
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取企业排行榜失败: {str(e)}"
        )


@router.get("/user-growth-trend", summary="获取用户增长趋势")
async def get_user_growth_trend(
    days: int = Query(30, ge=7, le=365, description="统计天数"),
    current_admin: Administrator = Depends(get_current_admin),
    db: Session = Depends(get_db)
) -> List[Dict[str, Any]]:
    """获取用户增长趋势"""
    try:
        trend = StatisticsService.get_user_growth_trend(db, days=days)
        return trend
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取用户增长趋势失败: {str(e)}"
        )


@router.get("/role-distribution", summary="获取角色分布统计")
async def get_role_distribution(
    current_admin: Administrator = Depends(get_current_admin),
    db: Session = Depends(get_db)
) -> List[Dict[str, Any]]:
    """获取角色分布统计"""
    try:
        distribution = StatisticsService.get_role_distribution(db)
        return distribution
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取角色分布统计失败: {str(e)}"
        )


@router.get("/organization-health", summary="获取企业健康状况")
async def get_organization_health_check(
    current_admin: Administrator = Depends(get_current_admin),
    db: Session = Depends(get_db)
) -> List[Dict[str, Any]]:
    """获取企业健康状况检查"""
    try:
        health_data = StatisticsService.get_organization_health_check(db)
        return health_data
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取企业健康状况失败: {str(e)}"
        )


@router.get("/system-alerts", summary="获取系统告警")
async def get_system_alerts(
    current_admin: Administrator = Depends(get_current_admin),
    db: Session = Depends(get_db)
) -> List[Dict[str, Any]]:
    """获取系统告警信息"""
    try:
        alerts = StatisticsService.get_system_alerts(db)
        return alerts
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取系统告警失败: {str(e)}"
        ) 