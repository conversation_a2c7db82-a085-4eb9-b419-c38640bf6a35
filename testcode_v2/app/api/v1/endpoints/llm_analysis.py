"""
基于LLM的流式分析接口
=================
提供基于LLM动态规划的分析流程
"""

from fastapi import APIRouter, Depends, HTTPException, Query, Request, BackgroundTasks
from fastapi.responses import StreamingResponse, JSONResponse
from sqlalchemy.orm import Session
from typing import Optional
import uuid
from app.core.logger import log, AnalysisLogger
from datetime import datetime
from app.utils.time_utils import get_shanghai_time

from app.db.session import get_db
from app.utils.deps import get_current_user
from app.schemas import AnalysisRequest
from app.models.project import Project as ProjectModel
from app.services.llm_analyzer import LLMStreamAnalyzer

router = APIRouter()

@router.get("/llm-stream", summary="执行LLM流式分析(GET)")
async def execute_llm_analysis_stream_get(
    request: Request,
    background_tasks: BackgroundTasks,
    project_id: str = Query(..., description="项目ID"),
    query: str = Query(..., description="查询内容"),
    intent_adjustment: Optional[str] = Query(None, description="意图调整/补充信息"),
    continue_analysis_id: Optional[str] = Query(None, description="继续分析的ID（用于澄清后继续）"),
    conversation_id: Optional[str] = Query(None, description="会话ID（用于多轮对话）"),
    max_planning_rounds: int = Query(25, description="最大规划轮数", ge=1, le=50),
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """执行基于LLM的流式分析（GET方法）
    
    使用LLM动态规划分析步骤，并按照流式方式返回结果。
    支持通过continue_analysis_id参数继续之前的分析。
    
    Args:
        request: 请求对象
        project_id: 项目ID
        query: 查询内容
        intent_adjustment: 可选，意图调整/补充信息
        continue_analysis_id: 可选，继续分析的ID（用于澄清后继续）
        conversation_id: 可选，会话ID（用于多轮对话）
        max_planning_rounds: 最大规划轮数，默认25轮
        db: 数据库会话
        
    Returns:
        StreamingResponse: 流式响应
    """
    # 获取请求ID
    req_id = getattr(request.state, "request_id", "-")
    
    # 记录接收到的请求信息
    log.info(f"[请求ID:{req_id}] 接收LLM流式分析请求: project_id={project_id}, continue_analysis_id={continue_analysis_id}")
    log.debug(f"[请求ID:{req_id}] 查询内容: {query}, 最大规划轮数: {max_planning_rounds}")
    
    # 详细记录用户输入信息
    user_request_info = {
        "request_id": req_id,
        "project_id": project_id,
        "query": query,
        "query_length": len(query),
        "continue_analysis_id": continue_analysis_id,
        "conversation_id": conversation_id,
        "max_planning_rounds": max_planning_rounds,
        "is_continuation": continue_analysis_id is not None,
        "client_ip": request.client.host if request.client else "unknown",
        "user_agent": request.headers.get("user-agent", "unknown"),
        "timestamp": get_shanghai_time().isoformat()
    }
    
    # 记录详细的用户请求信息
    log.info(f"[请求ID:{req_id}] 用户请求详细信息: {user_request_info}")
    
    # 检查项目是否存在
    project = db.query(ProjectModel).filter(ProjectModel.id == project_id).first()
    if not project:
        log.error(f"[请求ID:{req_id}] 项目不存在: {project_id}")
        return JSONResponse(
            status_code=404,
            content={"code": 404, "message": "项目不存在", "request_id": req_id}
        )
    
    # 判断是否为继续分析
    if continue_analysis_id:
        log.info(f"[请求ID:{req_id}] 继续分析模式: 项目名称={project.name}, 项目ID={project_id}, 分析ID={continue_analysis_id}")
        
        # 记录澄清继续的详细信息
        clarification_info = {
            **user_request_info,
            "analysis_mode": "clarification_continuation",
            "project_name": project.name,
            "original_analysis_id": continue_analysis_id
        }
        log.info(f"[请求ID:{req_id}] 澄清继续分析详细信息: {clarification_info}")
        
        # 重点记录：特别记录用户回复的内容，确保在日志中可见
        log.info(f"[请求ID:{req_id}] 用户澄清回复: \"{query}\"")
        
        # 查找相关的工具执行记录，记录对应关系
        try:
            from app.models.analysis import Analysis, ToolExecution
            from app.models.tool import Tool
            
            # 查询分析记录
            analysis = db.query(Analysis).filter(Analysis.id == continue_analysis_id).first()
            if analysis:
                # 查询澄清工具
                clarification_tool = db.query(Tool).filter(
                    Tool.name == "智能交互",
                    Tool.project_id == project_id
                ).first()
                
                if clarification_tool:
                    # 查询最后一次澄清工具执行
                    last_clarification = db.query(ToolExecution).filter(
                        ToolExecution.analysis_id == continue_analysis_id,
                        ToolExecution.tool_id == clarification_tool.id
                    ).order_by(ToolExecution.created_at.desc()).first()
                    
                    if last_clarification:
                        log.info(f"[请求ID:{req_id}] 找到对应的澄清工具执行记录，ID: {last_clarification.id}")
                        # 跟踪用户回复与澄清问题的对应关系
                        if last_clarification.result and isinstance(last_clarification.result, dict):
                            if "data" in last_clarification.result and "questions" in last_clarification.result["data"]:
                                questions = last_clarification.result["data"]["questions"]
                                questions_text = "; ".join([q.get("question", "") for q in questions if isinstance(q, dict)])
                                log.info(f"[请求ID:{req_id}] 澄清问题: \"{questions_text}\"")
                                log.info(f"[请求ID:{req_id}] 用户回复: \"{query}\" 对应问题: \"{questions_text}\"")
        except Exception as e:
            log.warning(f"[请求ID:{req_id}] 记录用户回复与问题对应关系时出错: {str(e)}")
    else:
        log.info(f"[请求ID:{req_id}] 新分析模式: 项目名称={project.name}, 项目ID={project_id}")
        
        # 记录新分析的详细信息
        new_analysis_info = {
            **user_request_info,
            "analysis_mode": "new_analysis",
            "project_name": project.name
        }
        log.info(f"[请求ID:{req_id}] 新分析详细信息: {new_analysis_info}")
    
    # 创建LLM流式分析器
    analyzer = LLMStreamAnalyzer(db=db)
    log.debug(f"[请求ID:{req_id}] LLM流式分析器已创建")
    
    # 返回符合SSE标准的流式响应
    headers = {
        "Cache-Control": "no-cache",
        "Connection": "keep-alive",
        "X-Accel-Buffering": "no"  # 针对Nginx禁用缓冲
    }
    
    # 创建用于包装流内容并添加日志的异步生成器
    async def log_wrapped_stream():
        try:
            # 调用generate_stream，传入所有必需参数
            stream_gen = analyzer.generate_stream(
                query=query, 
                project_id=project_id,
                user_id=current_user.id,
                background_tasks=background_tasks,
                max_planning_rounds=max_planning_rounds,
                continue_analysis_id=continue_analysis_id,
                conversation_id=conversation_id,
                intent_adjustment=intent_adjustment
            )
            async for event in stream_gen:
                yield event
            log.info(f"[请求ID:{req_id}] LLM流式分析已完成")
        except Exception as e:
            log.error(f"[请求ID:{req_id}] LLM流式分析出错: {str(e)}")
            # 返回错误事件
            import json
            error_event = json.dumps({
                "event": "error", 
                "data": {
                    "message": f"分析出错: {str(e)}",
                    "request_id": req_id
                }
            })
            yield f'data: {error_event}\n\n'
    
    # 创建流式响应
    log.info(f"[请求ID:{req_id}] 开始返回流式分析结果")
    return StreamingResponse(
        content=log_wrapped_stream(),
        media_type="text/event-stream",
        headers=headers
    )

@router.post("/llm/{analysis_id}/cancel", summary="取消LLM分析")
async def cancel_llm_analysis(
    analysis_id: str,
    request: Request,
    db: Session = Depends(get_db)
):
    """取消正在进行的LLM分析
    
    Args:
        analysis_id: 分析ID
        request: 请求对象
        db: 数据库会话
        
    Returns:
        Dict: 取消结果
    """
    # 获取请求ID
    req_id = getattr(request.state, "request_id", "-")
    
    log.info(f"[请求ID:{req_id}] 接收取消LLM分析请求: analysis_id={analysis_id}")
    
    try:
        from app.models.analysis import Analysis as AnalysisModel
        
        # 验证分析记录是否存在
        analysis = db.query(AnalysisModel).filter(AnalysisModel.id == analysis_id).first()
        if not analysis:
            log.warning(f"[请求ID:{req_id}] 分析记录不存在: {analysis_id}")
            return {
                "code": 1,
                "message": "分析记录不存在",
                "data": None,
                "request_id": req_id
            }
        
        # 设置取消信号到Redis
        success = LLMStreamAnalyzer.set_cancel_signal(analysis_id)
        
        if success:
            log.info(f"[请求ID:{req_id}] 成功设置LLM分析取消信号: {analysis_id}")
            return {
                "code": 0,
                "message": "取消信号已发送",
                "data": {
                    "analysis_id": analysis_id,
                    "status": "cancel_requested"
                },
                "request_id": req_id
            }
        else:
            log.error(f"[请求ID:{req_id}] 设置取消信号失败: {analysis_id}")
            return {
                "code": 1,
                "message": "取消信号发送失败，请稍后重试",
                "data": None,
                "request_id": req_id
            }
    
    except Exception as e:
        log.error(f"[请求ID:{req_id}] 取消LLM分析时发生错误: {str(e)}")
        return {
            "code": 1,
            "message": f"取消分析失败: {str(e)}",
            "data": None,
            "request_id": req_id
        }
@router.get("/context-cache/stats", summary="获取上下文缓存统计信息")
async def get_context_cache_stats():
    """获取上下文缓存统计信息
    
    Returns:
        dict: 缓存统计信息
    """
    from app.core.context_store import context_store
    
    try:
        stats = await context_store.get_stats()
        return {
            "code": 200,
            "message": "获取缓存统计信息成功",
            "data": stats
        }
    except Exception as e:
        log.error(f"获取缓存统计信息失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={
                "code": 500,
                "message": f"获取缓存统计信息失败: {str(e)}"
            }
        )

@router.get("/context-cache/{analysis_id}", summary="获取指定分析的上下文缓存")
async def get_analysis_context_cache(analysis_id: str):
    """获取指定分析的上下文缓存
    
    Args:
        analysis_id: 分析ID
        
    Returns:
        dict: 上下文信息或错误信息
    """
    from app.core.context_store import context_store
    
    try:
        context = await context_store.get(analysis_id)
        if context is None:
            return JSONResponse(
                status_code=404,
                content={
                    "code": 404,
                    "message": f"未找到分析ID {analysis_id} 的上下文缓存"
                }
            )
        
        # 返回简化的上下文信息（避免返回过大的数据）
        simplified_context = {
            "analysis_id": context.get("analysis_id"),
            "user_query": context.get("user_query", "")[:200] + "..." if len(context.get("user_query", "")) > 200 else context.get("user_query", ""),
            "task_state": context.get("task_state"),
            "planning_rounds": context.get("planning_rounds"),
            "max_planning_rounds": context.get("max_planning_rounds"),
            "execution_history_count": len(context.get("execution_history", [])),
            "execution_results_count": len(context.get("execution_results", {}))
        }
        
        return {
            "code": 200,
            "message": "获取上下文缓存成功",
            "data": simplified_context
        }
    except Exception as e:
        log.error(f"获取上下文缓存失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={
                "code": 500,
                "message": f"获取上下文缓存失败: {str(e)}"
            }
        )

@router.delete("/context-cache/{analysis_id}", summary="清除指定分析的上下文缓存")
async def clear_analysis_context_cache(analysis_id: str):
    """清除指定分析的上下文缓存
    
    Args:
        analysis_id: 分析ID
        
    Returns:
        dict: 操作结果
    """
    from app.core.context_store import context_store
    
    try:
        removed = await context_store.remove(analysis_id)
        if removed:
            return {
                "code": 200,
                "message": f"成功清除分析ID {analysis_id} 的上下文缓存"
            }
        else:
            return {
                "code": 404,
                "message": f"未找到分析ID {analysis_id} 的上下文缓存"
            }
    except Exception as e:
        log.error(f"清除上下文缓存失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={
                "code": 500,
                "message": f"清除上下文缓存失败: {str(e)}"
            }
        )

@router.delete("/context-cache", summary="清空所有上下文缓存")
async def clear_all_context_cache():
    """清空所有上下文缓存
    
    Returns:
        dict: 操作结果
    """
    from app.core.context_store import context_store
    
    try:
        cleared = await context_store.clear_all()
        if cleared:
            return {
                "code": 200,
                "message": "成功清空所有上下文缓存"
            }
        else:
            return {
                "code": 500,
                "message": "清空上下文缓存失败"
            }
    except Exception as e:
        log.error(f"清空所有上下文缓存失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={
                "code": 500,
                "message": f"清空所有上下文缓存失败: {str(e)}"
            }
    )


@router.post("/llm/{analysis_id}/interrupt", summary="打断LLM分析")
async def interrupt_llm_analysis(
    analysis_id: str,
    request: Request,
    db: Session = Depends(get_db)
):
    """打断正在进行的LLM分析
    
    Args:
        analysis_id: 分析ID
        request: 请求对象
        db: 数据库会话
        
    Returns:
        Dict: 打断结果
    """
    # 获取请求ID
    req_id = getattr(request.state, "request_id", "-")
    
    log.info(f"[请求ID:{req_id}] 接收打断LLM分析请求: analysis_id={analysis_id}")
    
    try:
        from app.models.analysis import Analysis as AnalysisModel
        from app.utils.time_utils import get_shanghai_time
        import uuid
        
        # 验证分析记录是否存在
        analysis = db.query(AnalysisModel).filter(AnalysisModel.id == analysis_id).first()
        if not analysis:
            log.warning(f"[请求ID:{req_id}] 分析记录不存在: {analysis_id}")
            return {
                "code": 1,
                "message": "分析记录不存在",
                "data": None,
                "request_id": req_id
            }
        
        # 生成打断ID用于Redis信号
        interrupt_id = str(uuid.uuid4())
        log.info(f"[请求ID:{req_id}] 生成打断ID: {interrupt_id}")
        
        # 设置打断信号到Redis
        success = LLMStreamAnalyzer.set_interrupt_signal(
            analysis_id, 
            'interrupt', 
            'unknown',  # 阶段会在主循环中确定
            interrupt_id
        )
        
        if success:
            log.info(f"[请求ID:{req_id}] 成功设置LLM分析打断信号: {analysis_id}")
            return {
                "code": 0,
                "message": "打断信号已发送，等待用户反馈",
                "data": {
                    "analysis_id": analysis_id,
                    "interrupt_id": interrupt_id,
                    "status": "interrupt_requested"
                },
                "request_id": req_id
            }
        else:
            log.error(f"[请求ID:{req_id}] 设置打断信号失败: {analysis_id}")
            return {
                "code": 1,
                "message": "打断信号发送失败，请稍后重试",
                "data": None,
                "request_id": req_id
            }
    
    except Exception as e:
        log.error(f"[请求ID:{req_id}] 打断LLM分析时发生错误: {str(e)}")
        return {
            "code": 1,
            "message": f"打断分析失败: {str(e)}",
            "data": None,
            "request_id": req_id
        }


@router.post("/llm/{analysis_id}/feedback", summary="提交用户反馈")
async def submit_user_feedback(
    analysis_id: str,
    feedback_data: dict,
    request: Request,
    db: Session = Depends(get_db)
):
    """提交用户反馈以调整分析
    
    Args:
        analysis_id: 分析ID
        feedback_data: 反馈数据
        request: 请求对象
        db: 数据库会话
        
    Returns:
        Dict: 提交结果
    """
    # 获取请求ID
    req_id = getattr(request.state, "request_id", "-")
    
    log.info(f"[请求ID:{req_id}] 接收用户反馈: analysis_id={analysis_id}")
    
    try:
        from app.models.analysis import Analysis as AnalysisModel
        
        # 验证分析记录是否存在
        analysis = db.query(AnalysisModel).filter(AnalysisModel.id == analysis_id).first()
        if not analysis:
            log.warning(f"[请求ID:{req_id}] 分析记录不存在: {analysis_id}")
            return {
                "code": 1,
                "message": "分析记录不存在",
                "data": None,
                "request_id": req_id
            }
        
        # 验证必要字段
        required_fields = ['interrupt_id', 'adjustment_type', 'feedback']
        for field in required_fields:
            if field not in feedback_data:
                return {
                    "code": 1,
                    "message": f"缺少必要字段: {field}",
                    "data": None,
                    "request_id": req_id
                }
        
        interrupt_id = feedback_data.get('interrupt_id')
        
        # 设置用户反馈到Redis
        success = LLMStreamAnalyzer.set_user_feedback(
            analysis_id,
            feedback_data,
            interrupt_id
        )
        
        if success:
            # 更新打断信号为adjust类型
            LLMStreamAnalyzer.set_interrupt_signal(
                analysis_id,
                'adjust',
                'feedback_received',
                interrupt_id
            )
            
            log.info(f"[请求ID:{req_id}] 成功设置用户反馈: {analysis_id}")
            return {
                "code": 0,
                "message": "用户反馈已提交，分析将继续",
                "data": {
                    "analysis_id": analysis_id,
                    "interrupt_id": interrupt_id,
                    "status": "feedback_submitted"
                },
                "request_id": req_id
            }
        else:
            log.error(f"[请求ID:{req_id}] 设置用户反馈失败: {analysis_id}")
            return {
                "code": 1,
                "message": "用户反馈提交失败，请稍后重试",
                "data": None,
                "request_id": req_id
            }
    
    except Exception as e:
        log.error(f"[请求ID:{req_id}] 提交用户反馈时发生错误: {str(e)}")
        return {
            "code": 1,
            "message": f"提交用户反馈失败: {str(e)}",
            "data": None,
            "request_id": req_id
        }

@router.post("/llm/{analysis_id}/cancel-interrupt", summary="取消打断，继续分析")
async def cancel_interrupt_continue_analysis(
    analysis_id: str,
    request: Request,
    db: Session = Depends(get_db)
):
    """取消打断，让分析继续进行
    
    Args:
        analysis_id: 分析ID
        request: 请求对象
        db: 数据库会话
        
    Returns:
        Dict: 操作结果
    """
    # 获取请求ID
    req_id = getattr(request.state, "request_id", "-")
    
    log.info(f"[请求ID:{req_id}] 接收取消打断请求: analysis_id={analysis_id}")
    
    try:
        from app.models.analysis import Analysis as AnalysisModel
        
        # 验证分析记录是否存在
        analysis = db.query(AnalysisModel).filter(AnalysisModel.id == analysis_id).first()
        if not analysis:
            log.warning(f"[请求ID:{req_id}] 分析记录不存在: {analysis_id}")
            return {
                "code": 1,
                "message": "分析记录不存在",
                "data": None,
                "request_id": req_id
            }
        
        # 生成一个临时的interrupt_id用于信号传递
        temp_interrupt_id = str(uuid.uuid4())
        
        # 设置继续分析的信号到Redis
        success = LLMStreamAnalyzer.set_interrupt_signal(
            analysis_id,
            'continue',  # 新的打断类型：继续分析
            'user_cancelled_feedback',
            temp_interrupt_id
        )
        
        if success:
            log.info(f"[请求ID:{req_id}] 成功设置继续分析信号: {analysis_id}")
            return {
                "code": 0,
                "message": "已取消打断，分析将继续进行",
                "data": {
                    "analysis_id": analysis_id,
                    "interrupt_id": temp_interrupt_id,
                    "status": "interrupt_cancelled"
                },
                "request_id": req_id
            }
        else:
            log.error(f"[请求ID:{req_id}] 设置继续分析信号失败: {analysis_id}")
            return {
                "code": 1,
                "message": "取消打断失败，请稍后重试",
                "data": None,
                "request_id": req_id
            }
    
    except Exception as e:
        log.error(f"[请求ID:{req_id}] 取消打断时发生错误: {str(e)}")
        return {
            "code": 1,
            "message": f"取消打断失败: {str(e)}",
            "data": None,
            "request_id": req_id
        }


@router.post("/generate-structured-report", summary="生成结构化报告")
async def generate_structured_report(
    request: Request,
    analysis_id: str = Query(..., description="分析ID"),
    format_type: str = Query("structured", description="报告格式类型"),
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    生成结构化报告（JSON格式）
    
    Args:
        analysis_id: 分析ID
        format_type: 报告格式类型，默认为"structured"
        
    Returns:
        结构化报告数据
    """
    req_id = str(uuid.uuid4())
    
    try:
        log.info(f"[请求ID:{req_id}] 开始生成结构化报告，分析ID: {analysis_id}")
        
        # 这里可以根据analysis_id获取分析结果，然后生成结构化报告
        # 目前先返回演示数据
        sample_structured_report = {
            "components": [
                {
                    "type": "header",
                    "data": {
                        "title": "演示分析报告",
                        "subtitle": "基于结构化数据的智能分析",
                        "generated_at": get_shanghai_time().strftime("%Y-%m-%d %H:%M:%S"),
                        "tags": ["演示", "结构化报告"]
                    }
                },
                {
                    "type": "executive_summary",
                    "data": {
                        "title": "执行摘要",
                        "content": "这是一个演示用的结构化报告，展示了新的报告渲染系统的功能。"
                    }
                }
            ],
            "metadata": {
                "generated_at": get_shanghai_time().isoformat(),
                "analysis_id": analysis_id,
                "format": "structured"
            }
        }
        
        log.info(f"[请求ID:{req_id}] 结构化报告生成成功")
        
        return {
            "code": 0,
            "message": "结构化报告生成成功",
            "data": {
                "report": sample_structured_report,
                "format": "structured",
                "generated_at": get_shanghai_time().isoformat()
            },
            "request_id": req_id
        }
        
    except Exception as e:
        log.error(f"[请求ID:{req_id}] 生成结构化报告时发生错误: {str(e)}")
        return {
            "code": 1,
            "message": f"生成结构化报告失败: {str(e)}",
            "data": None,
            "request_id": req_id
        }
