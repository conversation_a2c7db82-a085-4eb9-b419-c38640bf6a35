from typing import List, Any, Dict, AsyncGenerator
from fastapi import APIRouter, Depends, HTTPException, Query, Request
from fastapi.responses import StreamingResponse, JSONResponse
from sqlalchemy.orm import Session
import logging
import json
import asyncio
from decimal import Decimal

from app.db.session import get_db
from app.schemas import AnalysisRequest, Analysis, AnalysisBrief, ToolExecution
from app.models.analysis import Analysis as AnalysisModel
from app.models.project import Project as ProjectModel
from app.services.analyzer import AnalyzerService
from app.schemas.base import Response, PageData, PageInfo
from app.services.executor.auto_sql_executor import AutoSQLExecutor
from app.utils.json_utils import DecimalEncoder
from app.services.system_tools import SYSTEM_TOOLS
from app.core.logger import AnalysisLogger

router = APIRouter()

async def _generate_analysis_events(
    query: str, 
    project_id: str,
    analyzer: AnalyzerService,
    db: Session
) -> AsyncGenerator[str, None]:
    """生成分析事件流
    
    这是一个内部函数，用于生成符合SSE标准的事件流
    """
    try:
        # 发送初始事件
        yield f"data: {json.dumps({'event': 'start', 'data': {'message': '分析开始', 'query': query}}, cls=DecimalEncoder)}\n\n"
        
        # 创建分析记录 - 使用数据库模型而不是Pydantic模型
        analysis = AnalysisModel(
            query=query,
            project_id=project_id
        )
        db.add(analysis)
        db.commit()
        db.refresh(analysis)
        
        # 初始化分析日志记录器(现在有了analysis_id)
        analysis_logger = AnalysisLogger(project_id=project_id, analysis_id=str(analysis.id))
        
        # 记录分析开始
        analysis_logger.info(f"开始分析任务", extra={
            "event": "analysis_start",
            "query": query,
            "project_id": project_id
        })
        
        # 记录分析记录创建
        analysis_logger.info("分析记录已创建", extra={
            "event": "analysis_created",
            "analysis_id": analysis.id
        })
        
        # 发送分析记录创建事件
        yield f"data: {json.dumps({'event': 'analysis_created', 'data': {'id': analysis.id, 'query': query, 'project_id': project_id}}, cls=DecimalEncoder)}\n\n"
        
        # 获取项目中的工具列表
        all_tools = await analyzer.get_available_tools(project_id)
        
        # 记录工具加载
        analysis_logger.info(f"已加载 {len(all_tools)} 个工具", extra={
            "event": "tools_loaded",
            "tool_count": len(all_tools),
            "tools": [tool.get("name", tool.get("id")) for tool in all_tools]
        })
        
        # 发送工具列表事件
        yield f"data: {json.dumps({'event': 'tools_loaded', 'data': {'tool_count': len(all_tools)}}, cls=DecimalEncoder)}\n\n"
        
        # 创建自动SQL执行器
        auto_sql_executor = AutoSQLExecutor(db=db)
        
        # 记录SQL执行器初始化
        analysis_logger.info("SQL执行器已初始化", extra={
            "event": "sql_executor_initialized"
        })
        
        # 发送SQL执行器初始化事件
        yield f"data: {json.dumps({'event': 'sql_executor_initialized', 'data': {'schema_loaded': True}}, cls=DecimalEncoder)}\n\n"
        
        # 发送开始分析用户意图事件
        yield f"data: {json.dumps({'event': 'intent_analysis_started', 'data': {'message': '开始分析用户意图'}}, cls=DecimalEncoder)}\n\n"
        
        # 记录开始时间
        intent_analysis_start_time = asyncio.get_event_loop().time()
        
        # 记录意图分析开始
        analysis_logger.info("开始分析用户意图", extra={
            "event": "intent_analysis_started"
        })
        
        # 分析用户意图
        intent_analysis = await analyzer.analyze_intent_stream(query, all_tools, project_id)
        
        # 计算耗时
        intent_analysis_time = round((asyncio.get_event_loop().time() - intent_analysis_start_time) * 1000) # 毫秒
        
        # 记录意图分析结果
        analysis_logger.info(f"用户意图分析完成，耗时 {intent_analysis_time}ms", extra={
            "event": "intent_analyzed",
            "execution_time": intent_analysis_time,
            "intent_complexity": intent_analysis.get("intent_complexity", "unknown"),
            "analysis_steps": len(intent_analysis.get("analysis_steps", []))
        })
        
        print(f"intent_analysis---: {intent_analysis}")
        # 更新分析记录
        analysis.intent_analysis = intent_analysis
        db.commit()
        
        # 发送意图分析事件，包含耗时
        yield f"data: {json.dumps({'event': 'intent_analyzed', 'data': {'intent_analysis': intent_analysis, 'execution_time': intent_analysis_time}}, cls=DecimalEncoder)}\n\n"
        
        # 获取意图复杂度，但暂不做判断
        intent_complexity = intent_analysis.get("intent_complexity", "complex")
        
        execution_results = []

        # 执行每个步骤，并实时返回结果
        for step in intent_analysis.get("analysis_steps", []):
            step_id = step.get("step_id")
            tool_name = step.get("tool_name")
            
            # 记录步骤开始
            analysis_logger.log_step(f"步骤开始: {tool_name}", {
                "step_id": step_id,
                "tool_name": tool_name,
                "parameters": step.get("parameters", {})
            })
            
            # 发送步骤开始事件
            yield f"data: {json.dumps({'event': 'step_started', 'data': {'step_id': step_id, 'tool_name': tool_name}}, cls=DecimalEncoder)}\n\n"
            
            # 如果是AUTO_SQL类型的步骤，并且我们之前成功生成了SQL，使用生成的SQL
            if tool_name == "自动SQL查询" and "generated_sql" in analysis.intent_analysis:
                # 修改步骤参数，加入生成的SQL
                if "parameters" not in step:
                    step["parameters"] = {}
                
                step["parameters"]["sql"] = analysis.intent_analysis["generated_sql"]["sql"]
                step["parameters"]["parameters"] = analysis.intent_analysis["generated_sql"]["parameters"]
                
                # 记录使用生成的SQL
                analysis_logger.info(f"使用生成的SQL执行步骤 {step_id}", extra={
                    "event": "using_generated_sql",
                    "step_id": step_id,
                    "sql": step["parameters"]["sql"]
                })
                
                # 发送通知
                yield f"data: {json.dumps({'event': 'using_generated_sql', 'data': {'step_id': step_id}}, cls=DecimalEncoder)}\n\n"
            
            try:
                # 执行步骤
                step_start_time = asyncio.get_event_loop().time()
                # 暂时使用默认语言，后续可根据项目配置或用户偏好调整
                result = await analyzer.execute_tool_step(analysis, step, all_tools, user_language='zh-CN')
                step_execution_time = round((asyncio.get_event_loop().time() - step_start_time) * 1000)
                
                execution_results.append(result)
                
                # 记录步骤完成
                analysis_logger.log_step(f"步骤完成: {tool_name}", {
                    "step_id": step_id,
                    "tool_name": tool_name,
                    "execution_time": step_execution_time,
                    "result_summary": str(result)[:200] if result else "无结果"
                })
                
                # 发送步骤完成事件
                yield f"data: {json.dumps({'event': 'step_completed', 'data': {'step_id': step_id, 'tool_name': tool_name, 'result': result}}, cls=DecimalEncoder)}\n\n"
                
            except Exception as step_error:
                # 记录步骤执行错误
                analysis_logger.error(f"步骤执行失败: {tool_name}", extra={
                    "event": "step_error",
                    "step_id": step_id,
                    "tool_name": tool_name,
                    "error": str(step_error)
                })
                raise
            
            # 短暂暂停，以便客户端处理
            await asyncio.sleep(0.1)
        
        # 执行完所有步骤后，根据意图复杂度决定是否生成报告
        if intent_complexity == "simple":
            # 记录简单意图完成
            analysis_logger.info("简单意图分析完成，无需生成报告", extra={
                "event": "analysis_completed",
                "intent_complexity": "simple"
            })
            
            # 如果是简单意图，跳过生成报告步骤
            yield f"data: {json.dumps({'event': 'completed', 'data': {'id': analysis.id, 'message': '简单意图，分析完成，无需报告'}}, cls=DecimalEncoder)}\n\n"
            return  # 提前结束事件流
            
        # 对于复杂意图，继续生成报告
        # 发送开始生成报告事件
        yield f"data: {json.dumps({'event': 'report_generation_started', 'data': {'message': '开始生成分析报告'}}, cls=DecimalEncoder)}\n\n"
        
        # 记录开始时间
        report_start_time = asyncio.get_event_loop().time()
        
        # 记录报告生成开始
        analysis_logger.info("开始生成分析报告", extra={
            "event": "report_generation_started"
        })
        
        # 生成最终报告
        report = await analyzer.generate_report_stream(query, intent_analysis, execution_results, project_id)
        
        # 计算耗时
        report_generation_time = round((asyncio.get_event_loop().time() - report_start_time) * 1000) # 毫秒
        
        # 更新分析记录
        analysis.result = report
        db.commit()
        
        # 记录报告生成完成
        analysis_logger.info(f"分析报告生成完成，耗时 {report_generation_time}ms", extra={
            "event": "report_generated", 
            "execution_time": report_generation_time,
            "report_length": len(str(report))
        })
        
        # 发送最终报告事件，包含耗时
        yield f"data: {json.dumps({'event': 'report_generated', 'data': {'report': report, 'execution_time': report_generation_time}}, cls=DecimalEncoder)}\n\n"
        
        # 记录分析完成
        analysis_logger.info("分析任务全部完成", extra={
            "event": "analysis_completed",
            "total_steps": len(execution_results),
            "intent_complexity": intent_complexity
        })
        
        # 发送分析完成事件
        yield f"data: {json.dumps({'event': 'completed', 'data': {'id': analysis.id, 'message': '分析已完成'}}, cls=DecimalEncoder)}\n\n"
        
    except Exception as e:
        # 记录分析失败
        analysis_logger.error(f"分析执行失败: {str(e)}", extra={
            "event": "analysis_error",
            "error": str(e),
            "error_type": type(e).__name__
        })
        
        logging.exception(f"流式分析执行失败: {str(e)}")
        yield f"data: {json.dumps({'event': 'error', 'data': {'message': f'分析执行失败: {str(e)}'}}, cls=DecimalEncoder)}\n\n"

@router.post("/stream", summary="执行流式分析")
async def execute_analysis_stream(
    analysis_request: AnalysisRequest,
    request: Request,
    db: Session = Depends(get_db)
):
    """执行数据分析（流式响应）
    
    将分析过程的每一步以流式方式返回给客户端
    """
    # 增加日志记录
    logger = logging.getLogger("app.api.analysis")
    logger.info(f"接收到流式分析请求: query={analysis_request.query}, project_id={analysis_request.project_id}")
    
    # 检查项目是否存在
    project = db.query(ProjectModel).filter(ProjectModel.id == analysis_request.project_id).first()
    if not project:
        logger.error(f"项目不存在: {analysis_request.project_id}")
        return JSONResponse(
            status_code=404,
            content={"code": 404, "message": "项目不存在", "request_id": request.state.request_id}
        )
    
    # 创建分析服务
    analyzer = AnalyzerService(db=db)
    
    # 返回符合SSE标准的流式响应
    headers = {
        "Cache-Control": "no-cache",
        "Connection": "keep-alive",
        "X-Accel-Buffering": "no"  # 针对Nginx禁用缓冲
    }
    
    return StreamingResponse(
        _generate_analysis_events(
            query=analysis_request.query,
            project_id=analysis_request.project_id,
            analyzer=analyzer,
            db=db
        ),
        media_type="text/event-stream",
        headers=headers
    )

@router.get("/stream", summary="执行流式分析(GET)")
async def execute_analysis_stream_get(
    request: Request,
    project_id: str = Query(..., description="项目ID"),
    query: str = Query(..., description="查询内容"),
    db: Session = Depends(get_db)
):
    """执行数据分析（流式响应，GET方法）
    
    将分析过程的每一步以流式方式返回给客户端
    通过URL查询参数传递project_id和query
    
    示例: GET /api/v1/analysis/stream?project_id=xxxx&query=xxxx
    """
    # 增加日志记录
    logger = logging.getLogger("app.api.analysis")
    logger.info(f"接收到GET流式分析请求: query={query}, project_id={project_id}")
    
    # 检查项目是否存在
    project = db.query(ProjectModel).filter(ProjectModel.id == project_id).first()
    if not project:
        logger.error(f"项目不存在: {project_id}")
        return JSONResponse(
            status_code=404,
            content={"code": 404, "message": "项目不存在", "request_id": request.state.request_id}
        )
    
    # 创建分析服务
    analyzer = AnalyzerService(db=db)
    
    # 返回符合SSE标准的流式响应
    headers = {
        "Cache-Control": "no-cache",
        "Connection": "keep-alive",
        "X-Accel-Buffering": "no"  # 针对Nginx禁用缓冲
    }
    
    return StreamingResponse(
        _generate_analysis_events(
            query=query,
            project_id=project_id,
            analyzer=analyzer,
            db=db
        ),
        media_type="text/event-stream",
        headers=headers
    )

@router.get("/", response_model=Response[PageData[AnalysisBrief]], summary="获取分析记录列表")
async def get_analyses(
    request: Request,
    project_id: str = Query(None, description="项目ID"),
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(10, ge=1, le=100, description="返回记录数"),
    db: Session = Depends(get_db)
):
    """获取分析记录列表"""
    query = db.query(AnalysisModel)
    
    if project_id:
        query = query.filter(AnalysisModel.project_id == project_id)
    
    # 查询总数
    total = query.count()
    
    # 查询分页数据
    analyses = query.order_by(AnalysisModel.created_at.desc()).offset(skip).limit(limit).all()
    
    # 计算总页数
    total_page = (total + limit - 1) // limit if limit > 0 else 0
    
    # 创建分页信息
    page_info = PageInfo(
        page=skip // limit + 1,
        page_size=limit,
        total=total,
        total_page=total_page
    )
    
    # 创建分页数据
    page_data = PageData(
        items=analyses,
        page_info=page_info
    )
    
    return Response(
        data=page_data,
        request_id=request.state.request_id
    )

@router.get("/{analysis_id}", response_model=Response[Analysis], summary="获取分析记录详情")
async def get_analysis(
    analysis_id: str,
    request: Request,
    db: Session = Depends(get_db)
):
    """获取分析记录详情"""
    analysis = db.query(AnalysisModel).filter(AnalysisModel.id == analysis_id).first()
    if not analysis:
        raise HTTPException(status_code=404, detail="分析记录不存在")
    return Response(
        data=analysis,
        request_id=request.state.request_id
    )

@router.delete("/{analysis_id}", response_model=Response, summary="删除分析记录")
async def delete_analysis(
    analysis_id: str,
    request: Request,
    db: Session = Depends(get_db)
):
    """删除分析记录"""
    analysis = db.query(AnalysisModel).filter(AnalysisModel.id == analysis_id).first()
    if not analysis:
        raise HTTPException(status_code=404, detail="分析记录不存在")
    
    db.delete(analysis)
    db.commit()
    
    return Response(
        message="分析记录删除成功",
        request_id=request.state.request_id
    )

@router.get("/{analysis_id}/executions", response_model=Response[List[ToolExecution]], summary="获取工具执行记录")
async def get_tool_executions(
    analysis_id: str,
    request: Request,
    db: Session = Depends(get_db)
):
    """获取工具执行记录"""
    analysis = db.query(AnalysisModel).filter(AnalysisModel.id == analysis_id).first()
    if not analysis:
        raise HTTPException(status_code=404, detail="分析记录不存在")
    # 将工具执行记录转换为 Pydantic 模型，并填充系统工具信息
    tool_execution_schemas: List[ToolExecution] = []
    for execution in analysis.tool_executions:
        # 使用 Pydantic 模型转换 ORM 实例
        tool_schema = ToolExecution.from_orm(execution)
        # 如果没有关联的 tool 信息，尝试使用系统工具列表补充
        if not tool_schema.tool:
            matching_tools = [t for t in SYSTEM_TOOLS if t["id"] == execution.tool_id]
            if matching_tools:
                tool_schema.tool = matching_tools[0]
        tool_execution_schemas.append(tool_schema)
    # 返回 Pydantic 模型列表
    return Response(
        data=tool_execution_schemas,
        request_id=request.state.request_id
    )