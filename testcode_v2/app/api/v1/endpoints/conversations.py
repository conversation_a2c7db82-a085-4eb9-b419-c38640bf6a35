from fastapi import APIRouter, Depends, HTTPException, status, Query, BackgroundTasks
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import timezone, timedelta, datetime
import uuid

from app.db.session import get_db
from app.models.conversation import Conversation
from app.models.analysis import Analysis
from app.schemas.conversation import (
    ConversationCreate, ConversationUpdate, ConversationResponse,
    ConversationListResponse, ConversationAnalysisResponse,
    ConversationRoundRequest, ConversationContextResponse
)
from app.core.middleware.auth import get_current_user, get_current_user_with_permissions
from app.models.user import User
from app.services.conversation_service import ConversationService
from app.services.multi_round_analyzer import MultiRoundLLMAnalyzer
from app.services.permission_service import get_permission_service

router = APIRouter()

@router.post("", response_model=ConversationResponse)
async def create_conversation(
    conversation_data: ConversationCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user_with_permissions)
):
    """创建新会话"""
    # 检查用户是否有权限访问该项目
    permission_service = get_permission_service(db)
    if not permission_service.check_project_access(current_user, conversation_data.project_id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权在此项目中创建会话"
        )
    
    conversation_service = ConversationService(db)
    
    conversation = conversation_service.create_conversation(
        project_id=conversation_data.project_id,
        initial_query=conversation_data.initial_query,
        user_id=current_user.id
    )
    
    return ConversationResponse(
        id=conversation.id,
        project_id=conversation.project_id,
        user_id=conversation.user_id,
        title=conversation.title,
        context_summary=conversation.context_summary,
        total_rounds=conversation.total_rounds,
        total_tokens_used=conversation.total_tokens_used,
        created_at=conversation.created_at,
        updated_at=conversation.updated_at,
        status=conversation.status,
        round_count=0
    )

@router.get("/{conversation_id}", response_model=ConversationResponse)
async def get_conversation(
    conversation_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user_with_permissions)
):
    """获取会话详情"""
    conversation = db.query(Conversation).filter(Conversation.id == conversation_id).first()
    
    if not conversation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="会话不存在"
        )
    
    # 检查用户是否有权限访问该会话
    permission_service = get_permission_service(db)
    if not permission_service.check_conversation_access(current_user, conversation):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权访问此会话"
        )
    
    # 统计轮次数量
    round_count = db.query(Analysis).filter(Analysis.conversation_id == conversation_id).count()
    
    return ConversationResponse(
        id=conversation.id,
        project_id=conversation.project_id,
        user_id=conversation.user_id,
        title=conversation.title,
        context_summary=conversation.context_summary,
        total_rounds=conversation.total_rounds,
        total_tokens_used=conversation.total_tokens_used,
        created_at=conversation.created_at,
        updated_at=conversation.updated_at,
        status=conversation.status,
        round_count=round_count
    )

@router.get("/{conversation_id}/analyses")
async def get_conversation_analyses(
    conversation_id: str,
    include_full_state: bool = Query(True, description="是否包含完整的分析状态"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user_with_permissions)
):
    """获取会话的所有分析记录（包含完整状态用于还原）"""
    conversation = db.query(Conversation).filter(Conversation.id == conversation_id).first()
    
    if not conversation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="会话不存在"
        )
    
    # 检查用户是否有权限访问该会话
    permission_service = get_permission_service(db)
    if not permission_service.check_conversation_access(current_user, conversation):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权访问此会话"
        )
    
    if include_full_state:
        # 使用会话服务获取完整状态
        conversation_service = ConversationService(db)
        analyses_with_state = conversation_service.get_conversation_analyses(conversation_id)
        
        return {
            "code": 0,
            "data": analyses_with_state,
            "message": "获取会话分析记录成功（包含完整状态）"
        }
    else:
        # 只返回基本信息
        analyses = db.query(Analysis).filter(
            Analysis.conversation_id == conversation_id
        ).order_by(Analysis.round_number.asc()).all()
        
        return {
            "code": 0,
            "data": [
                {
                    "analysis_id": analysis.id,
                    "conversation_id": analysis.conversation_id,
                    "round_number": analysis.round_number,
                    "query": analysis.query,
                    "status": analysis.status,
                    "created_at": analysis.created_at.isoformat(),
                    "updated_at": analysis.updated_at.isoformat(),
                    "final_report": analysis.result
                }
                for analysis in analyses
            ],
            "message": "获取会话分析记录成功（基本信息）"
        }

# 新增：多轮分析专用接口
@router.post("/{conversation_id}/rounds/stream")
async def execute_conversation_round_stream(
    conversation_id: str,
    request: ConversationRoundRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user_with_permissions)
):
    """在会话中执行新轮次的流式分析"""
    
    # 验证会话存在
    conversation = db.query(Conversation).filter(Conversation.id == conversation_id).first()
    
    if not conversation:
        raise HTTPException(status_code=404, detail="会话不存在")
    
    # 检查用户是否有权限访问该会话
    permission_service = get_permission_service(db)
    if not permission_service.check_conversation_access(current_user, conversation):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权访问此会话"
        )
    
    # 创建多轮分析器
    analyzer = MultiRoundLLMAnalyzer(db)
    
    # 返回流式响应
    return StreamingResponse(
        analyzer.generate_conversation_stream(
            conversation_id=conversation_id,
            query=request.query,
            round_number=request.round_number,
            project_id=conversation.project_id,
            user_id=current_user.id,
            background_tasks=background_tasks,
            max_planning_rounds=request.max_planning_rounds,
            use_full_context=request.use_full_context,
            context_depth=request.context_depth,
            continue_analysis_id=request.continue_analysis_id,
            intent_adjustment=request.intent_adjustment
        ),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no"
        }
    )

@router.get("/{conversation_id}/context")
async def get_conversation_context(
    conversation_id: str,
    max_rounds: int = Query(10, ge=1, le=50),
    context_types: List[str] = Query(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user_with_permissions)
):
    """获取会话的结构化上下文"""
    
    conversation = db.query(Conversation).filter(Conversation.id == conversation_id).first()
    
    if not conversation:
        raise HTTPException(status_code=404, detail="会话不存在")
    
    # 检查用户是否有权限访问该会话
    permission_service = get_permission_service(db)
    if not permission_service.check_conversation_access(current_user, conversation):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权访问此会话"
        )
    
    conversation_service = ConversationService(db)
    context_data = conversation_service.get_conversation_context(
        conversation_id, max_rounds, context_types
    )
    
    return {
        "code": 0,
        "data": ConversationContextResponse(
            conversation_id=conversation_id,
            context_data=context_data,
            total_rounds=conversation.total_rounds,
            last_updated=conversation.updated_at
        ),
        "message": "获取会话上下文成功"
    }

@router.put("/{conversation_id}", response_model=ConversationResponse)
async def update_conversation(
    conversation_id: str,
    conversation_data: ConversationUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user_with_permissions)
):
    """更新会话信息"""
    conversation = db.query(Conversation).filter(Conversation.id == conversation_id).first()
    
    if not conversation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="会话不存在"
        )
    
    # 检查用户是否有权限访问该会话
    permission_service = get_permission_service(db)
    if not permission_service.check_conversation_access(current_user, conversation):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权修改此会话"
        )
    
    if conversation_data.title is not None:
        conversation.title = conversation_data.title
    if conversation_data.status is not None:
        conversation.status = conversation_data.status
    
    beijing_tz = timezone(timedelta(hours=8))
    conversation.updated_at = datetime.now(beijing_tz)
    
    db.commit()
    db.refresh(conversation)
    
    # 统计轮次数量
    round_count = db.query(Analysis).filter(Analysis.conversation_id == conversation_id).count()
    
    return ConversationResponse(
        id=conversation.id,
        project_id=conversation.project_id,
        user_id=conversation.user_id,
        title=conversation.title,
        context_summary=conversation.context_summary,
        total_rounds=conversation.total_rounds,
        total_tokens_used=conversation.total_tokens_used,
        created_at=conversation.created_at,
        updated_at=conversation.updated_at,
        status=conversation.status,
        round_count=round_count
    )

@router.delete("/{conversation_id}")
async def delete_conversation(
    conversation_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user_with_permissions)
):
    """删除会话"""
    conversation = db.query(Conversation).filter(Conversation.id == conversation_id).first()
    
    if not conversation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="会话不存在"
        )
    
    # 检查用户是否有权限访问该会话
    permission_service = get_permission_service(db)
    if not permission_service.check_conversation_access(current_user, conversation):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权删除此会话"
        )
    
    try:
        # 先手动删除相关记录，避免级联删除时的枚举值问题
        
        # 1. 删除会话上下文记录（使用原始SQL避免枚举问题）
        from sqlalchemy import text
        db.execute(text("DELETE FROM conversation_contexts WHERE conversation_id = :conv_id"), 
                  {"conv_id": conversation_id})
        
        # 2. 删除会话相关的所有分析记录
        db.query(Analysis).filter(Analysis.conversation_id == conversation_id).delete()
        
        # 3. 删除会话
        db.delete(conversation)
        db.commit()
        
        return {"message": "会话删除成功"}
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除会话失败: {str(e)}"
        )

@router.get("/projects/{project_id}/conversations")
async def get_project_conversations(
    project_id: str,
    page: int = Query(1, ge=1, description="页码，从1开始"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量，最大100"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user_with_permissions)
):
    """获取项目的所有会话（分页）"""
    # 检查用户是否有权限访问该项目
    permission_service = get_permission_service(db)
    if not permission_service.check_project_access(current_user, project_id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权访问此项目的会话"
        )
    from app.models.conversation import ConversationStatus
    from sqlalchemy import func
    
    # 使用子查询获取每个会话的最后分析时间，并按此排序
    subquery = db.query(
        Analysis.conversation_id,
        func.max(Analysis.created_at).label('last_analysis_time')
    ).group_by(Analysis.conversation_id).subquery()
    
    # 获取会话访问过滤条件
    conversation_filter = permission_service.get_accessible_conversations_filter(current_user)
    
    # 查询会话并按最后分析时间排序
    conversations_query = db.query(Conversation).filter(
        Conversation.project_id == project_id,
        Conversation.status == ConversationStatus.ACTIVE
    )
    
    # 添加会话访问权限过滤
    if conversation_filter is not True:  # 如果不是超级管理员（True表示无限制）
        conversations_query = conversations_query.filter(conversation_filter)
    
    conversations_query = conversations_query.outerjoin(
        subquery, Conversation.id == subquery.c.conversation_id
    ).order_by(
        # 优先按最后分析时间排序，如果没有分析则按创建时间排序
        func.coalesce(subquery.c.last_analysis_time, Conversation.created_at).desc()
    )
    
    # 获取总数
    total_count = conversations_query.count()
    
    # 分页查询
    offset = (page - 1) * page_size
    conversations = conversations_query.offset(offset).limit(page_size).all()
    
    result = []
    for conversation in conversations:
        # 统计轮次数量
        round_count = db.query(Analysis).filter(Analysis.conversation_id == conversation.id).count()
        
        # 获取最后一次分析时间
        last_analysis = db.query(Analysis).filter(
            Analysis.conversation_id == conversation.id
        ).order_by(Analysis.created_at.desc()).first()
        
        # 获取最后一次查询
        last_query = last_analysis.query if last_analysis else None
        
        result.append({
            "id": conversation.id,
            "project_id": conversation.project_id,
            "title": conversation.title,
            "created_at": conversation.created_at.isoformat(),
            "updated_at": conversation.updated_at.isoformat(),
            "status": conversation.status.value if hasattr(conversation.status, 'value') else conversation.status,
            "rounds_count": round_count,
            "last_query": last_query,
            "last_analysis_at": last_analysis.created_at.isoformat() if last_analysis else conversation.created_at.isoformat()
        })
    
    # 计算分页信息
    total_pages = (total_count + page_size - 1) // page_size
    has_next = page < total_pages
    has_prev = page > 1
    
    return {
        "code": 0,
        "data": {
            "conversations": result,
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total_count": total_count,
                "total_pages": total_pages,
                "has_next": has_next,
                "has_prev": has_prev
            }
        },
        "message": "获取会话列表成功"
    }

@router.post("/{conversation_id}/intent-confirmation/cancel")
async def cancel_intent_confirmation(
    conversation_id: str,
    analysis_id: str = Query(..., description="分析ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user_with_permissions)
):
    """取消意图确认"""
    
    # 验证会话存在
    conversation = db.query(Conversation).filter(Conversation.id == conversation_id).first()
    if not conversation:
        raise HTTPException(status_code=404, detail="会话不存在")
    
    # 检查用户是否有权限访问该会话
    permission_service = get_permission_service(db)
    if not permission_service.check_conversation_access(current_user, conversation):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权操作此会话"
        )
    
    # 验证分析记录存在
    analysis = db.query(Analysis).filter(Analysis.id == analysis_id).first()
    if not analysis:
        raise HTTPException(status_code=404, detail="分析记录不存在")
    
    try:
        # 导入分析步骤服务
        from app.services.analysis_step_service import AnalysisStepService
        step_service = AnalysisStepService(db)
        
        # 更新意图确认步骤状态为已取消
        success = step_service.update_step_status(
            analysis_id=analysis_id,
            step_id='intent_confirmation_request',
            status='error',
            step_name='等待用户确认意图 (已取消)',
            description='用户取消了意图确认'
        )
        
        if success:
            # 同时更新分析记录状态为失败
            analysis.status = 'failed'
            analysis.updated_at = datetime.now(timezone(timedelta(hours=8)))
            db.commit()
            
            return {
                "code": 0,
                "message": "意图确认已取消",
                "data": {
                    "conversation_id": conversation_id,
                    "analysis_id": analysis_id,
                    "status": "cancelled"
                }
            }
        else:
            raise HTTPException(status_code=500, detail="更新步骤状态失败")
            
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500, 
            detail=f"取消意图确认失败: {str(e)}"
        )

@router.get("/accessible")
async def get_accessible_conversations(
    page: int = Query(1, ge=1, description="页码，从1开始"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量，最大100"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user_with_permissions)
):
    """获取用户可以访问的所有会话（基于权限隔离）"""
    from app.models.conversation import ConversationStatus
    from app.models.project import Project
    from sqlalchemy import func
    
    permission_service = get_permission_service(db)
    
    # 获取用户可以访问的项目ID列表
    accessible_project_ids = permission_service.get_accessible_projects(current_user)
    
    if not accessible_project_ids:
        # 如果用户没有可访问的项目，返回空列表
        return {
            "code": 0,
            "data": {
                "conversations": [],
                "pagination": {
                    "page": page,
                    "page_size": page_size,
                    "total_count": 0,
                    "total_pages": 0,
                    "has_next": False,
                    "has_prev": False
                }
            },
            "message": "获取可访问会话列表成功"
        }
    
    # 获取会话访问过滤条件
    conversation_filter = permission_service.get_accessible_conversations_filter(current_user)
    
    # 使用子查询获取每个会话的最后分析时间，并按此排序
    subquery = db.query(
        Analysis.conversation_id,
        func.max(Analysis.created_at).label('last_analysis_time')
    ).group_by(Analysis.conversation_id).subquery()
    
    # 构建查询条件
    conversations_query = db.query(Conversation).join(
        Project, Conversation.project_id == Project.id
    ).filter(
        Conversation.project_id.in_(accessible_project_ids),
        Conversation.status == ConversationStatus.ACTIVE
    )
    
    # 添加会话访问权限过滤
    if conversation_filter is not True:  # 如果不是超级管理员（True表示无限制）
        conversations_query = conversations_query.filter(conversation_filter)
    
    conversations_query = conversations_query.outerjoin(
        subquery, Conversation.id == subquery.c.conversation_id
    ).order_by(
        # 优先按最后分析时间排序，如果没有分析则按创建时间排序
        func.coalesce(subquery.c.last_analysis_time, Conversation.created_at).desc()
    )
    
    # 获取总数
    total_count = conversations_query.count()
    
    # 分页查询
    offset = (page - 1) * page_size
    conversations = conversations_query.offset(offset).limit(page_size).all()
    
    result = []
    for conversation in conversations:
        # 获取项目信息
        project = db.query(Project).filter(Project.id == conversation.project_id).first()
        
        # 统计轮次数量
        round_count = db.query(Analysis).filter(Analysis.conversation_id == conversation.id).count()
        
        # 获取最后一次分析时间
        last_analysis = db.query(Analysis).filter(
            Analysis.conversation_id == conversation.id
        ).order_by(Analysis.created_at.desc()).first()
        
        # 获取最后一次查询
        last_query = last_analysis.query if last_analysis else None
        
        result.append({
            "id": conversation.id,
            "project_id": conversation.project_id,
            "project_name": project.name if project else "未知项目",
            "title": conversation.title,
            "created_at": conversation.created_at.isoformat(),
            "updated_at": conversation.updated_at.isoformat(),
            "status": conversation.status.value if hasattr(conversation.status, 'value') else conversation.status,
            "rounds_count": round_count,
            "last_query": last_query,
            "last_analysis_at": last_analysis.created_at.isoformat() if last_analysis else conversation.created_at.isoformat()
        })
    
    # 计算分页信息
    total_pages = (total_count + page_size - 1) // page_size
    has_next = page < total_pages
    has_prev = page > 1
    
    return {
        "code": 0,
        "data": {
            "conversations": result,
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total_count": total_count,
                "total_pages": total_pages,
                "has_next": has_next,
                "has_prev": has_prev
            }
        },
        "message": "获取可访问会话列表成功"
    } 