from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.services.organization import OrganizationService
from app.schemas.organization import (
    Organization, OrganizationCreate, OrganizationUpdate, 
    OrganizationList, OrganizationStats
)
from app.schemas.administrator import Administrator
from app.api.v1.endpoints.admin_auth import get_current_admin

router = APIRouter()


@router.get("/organizations", response_model=OrganizationList, summary="获取企业列表")
async def get_organizations(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    status: Optional[int] = Query(None, ge=1, le=2, description="企业状态筛选"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    current_admin: Administrator = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """获取企业列表"""
    try:
        organizations, total = OrganizationService.get_organizations(
            db=db, skip=skip, limit=limit, status=status, search=search
        )
        
        # 计算分页信息
        page = (skip // limit) + 1
        
        return OrganizationList(
            items=organizations,
            total=total,
            page=page,
            size=limit
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取企业列表失败: {str(e)}"
        )


@router.get("/organizations/{org_id}", response_model=Organization, summary="获取企业详情")
async def get_organization(
    org_id: int,
    current_admin: Administrator = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """获取指定企业的详细信息"""
    organization = OrganizationService.get_organization(db, org_id=org_id)
    if organization is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, 
            detail="企业不存在"
        )
    return organization


@router.post("/organizations", response_model=Organization, summary="创建企业")
async def create_organization(
    org_data: OrganizationCreate,
    current_admin: Administrator = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """创建新企业"""
    try:
        organization = OrganizationService.create_organization(db=db, org_data=org_data)
        return organization
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建企业失败: {str(e)}"
        )


@router.put("/organizations/{org_id}", response_model=Organization, summary="更新企业")
async def update_organization(
    org_id: int,
    org_data: OrganizationUpdate,
    current_admin: Administrator = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """更新企业信息"""
    try:
        updated_organization = OrganizationService.update_organization(
            db=db, org_id=org_id, org_data=org_data
        )
        
        if not updated_organization:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, 
                detail="企业不存在"
            )
        
        return updated_organization
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新企业失败: {str(e)}"
        )


@router.delete("/organizations/{org_id}", summary="删除企业")
async def delete_organization(
    org_id: int,
    current_admin: Administrator = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """删除企业"""
    try:
        success = OrganizationService.delete_organization(db, org_id=org_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, 
                detail="企业不存在"
            )
        
        return {"message": "企业删除成功"}
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除企业失败: {str(e)}"
        )


@router.get("/organizations/{org_id}/stats", response_model=OrganizationStats, summary="获取企业统计")
async def get_organization_stats(
    org_id: int,
    current_admin: Administrator = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """获取企业统计信息"""
    stats = OrganizationService.get_organization_stats(db, org_id=org_id)
    if stats is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, 
            detail="企业不存在"
        )
    return stats


@router.get("/organizations-stats", response_model=List[OrganizationStats], summary="获取所有企业统计")
async def get_all_organizations_stats(
    current_admin: Administrator = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """获取所有企业的统计信息"""
    try:
        stats_list = OrganizationService.get_all_organization_stats(db)
        return stats_list
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取企业统计失败: {str(e)}"
        )


@router.get("/organizations/{org_id}/token-usage", summary="获取企业Token用量详情")
async def get_organization_token_usage(
    org_id: int,
    current_admin: Administrator = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """获取企业Token用量详细信息"""
    try:
        token_info = OrganizationService.get_token_usage_info(db, org_id=org_id)
        return token_info
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取Token用量失败: {str(e)}"
        )


@router.get("/organizations/{org_id}/check-limits", summary="检查企业各项限额")
async def check_organization_limits(
    org_id: int,
    current_admin: Administrator = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """检查企业的用户、项目、Token限额情况"""
    try:
        user_ok = OrganizationService.check_user_limit(db, org_id)
        project_ok = OrganizationService.check_project_limit(db, org_id)
        token_ok = OrganizationService.check_token_limit(db, org_id)
        
        return {
            "user_limit_ok": user_ok,
            "project_limit_ok": project_ok,
            "token_limit_ok": token_ok,
            "all_limits_ok": user_ok and project_ok and token_ok
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"检查限额失败: {str(e)}"
        ) 