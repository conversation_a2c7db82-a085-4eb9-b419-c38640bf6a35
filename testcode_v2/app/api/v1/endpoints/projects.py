from typing import List, Any, Dict
from fastapi import APIRouter, Depends, HTTPException, Query, Request, status
from sqlalchemy.orm import Session
from sqlalchemy import and_

from app.db.session import get_db
from app.schemas import Project, ProjectCreate, ProjectUpdate, ProjectDetail
from app.models.project import Project as ProjectModel
from app.models.project_member import ProjectMember
from app.models.data_source import DataSource
from app.models.selected_table import SelectedTable
from app.models.analysis import Analysis
from app.models.user import User
from app.models.organization import Organization
from app.schemas.base import Response, PageData, PageInfo
from app.core.middleware.auth import get_current_user_with_permissions
from app.services.permission_service import get_permission_service
# 保持旧的权限函数以便逐步迁移
from app.services.user import get_accessible_user_ids
from app.core.logger import AppLogger

router = APIRouter()

@router.post("/", response_model=Response[Project], summary="创建项目")
async def create_project(
    project_in: ProjectCreate,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user_with_permissions)
):
    """创建新项目"""
    # 初始化日志记录器
    logger = AppLogger(request_id=request.state.request_id)
    
    logger.info(f"用户 {current_user.id}({current_user.username}) 尝试创建项目: {project_in.name}")
    
    # 检查用户是否有创建项目的权限
    # 超级管理员(role_id=1)和企业管理员(role_id=2)总是可以创建项目
    # 普通用户(role_id=3)需要检查can_create_project字段
    if current_user.role_id == 3 and not current_user.can_create_project:
        raise HTTPException(
            status_code=403,
            detail="您没有创建项目的权限，请联系管理员"
        )
    
    # 检查企业项目数量限制
    if current_user.org_id:
        logger.info(f"检查企业 {current_user.org_id} 的项目数量限制")
        
        # 获取企业信息
        organization = db.query(Organization).filter(Organization.id == current_user.org_id).first()
        if organization and organization.status == 1:  # 企业状态正常
            # 统计当前企业已有的项目数量
            current_project_count = db.query(ProjectModel).filter(
                ProjectModel.org_id == current_user.org_id
            ).count()
            
            logger.info(f"企业 {organization.name}(ID:{organization.id}) 当前项目数量: {current_project_count}/{organization.project_limit}")
            
            # 检查是否达到项目数量上限
            if current_project_count >= organization.project_limit:
                logger.warning(f"企业 {organization.name} 项目数量已达上限: {current_project_count}/{organization.project_limit}")
                raise HTTPException(
                    status_code=403,
                    detail=f"企业项目数量已达到上限({organization.project_limit}个)，请联系管理员升级或删除部分项目"
                )
        elif organization and organization.status == 0:
            # 企业已被禁用
            logger.warning(f"企业 {organization.name}(ID:{organization.id}) 已被禁用，用户 {current_user.username} 无法创建项目")
            raise HTTPException(
                status_code=403,
                detail="所属企业已被禁用，无法创建项目"
            )
        elif not organization:
            # 企业不存在
            logger.error(f"用户 {current_user.username} 所属企业 {current_user.org_id} 不存在")
            raise HTTPException(
                status_code=403,
                detail="所属企业不存在，请联系管理员"
            )
    
    permission_service = get_permission_service(db)
    
    # 获取当前用户可以访问的项目列表
    accessible_project_ids = permission_service.get_accessible_projects(current_user)
    accessible_projects = db.query(ProjectModel).filter(
        ProjectModel.id.in_(accessible_project_ids)
    ).all()
    
    # 检查项目名称在可见范围内是否已存在
    existing_project = None
    for project in accessible_projects:
        if project.name == project_in.name:
            existing_project = project
            break
    
    if existing_project:
        return Response(
            code=400,
            message="项目名称已存在，请使用其他名称",
            request_id=request.state.request_id
        )
    
    # 创建项目时设置创建者和所有者为当前用户
    project_data = project_in.dict()
    project_data['user_id'] = current_user.id
    project_data['owner_id'] = current_user.id
    project_data['org_id'] = current_user.org_id  # 继承用户的企业ID
    
    project = ProjectModel(**project_data)
    db.add(project)
    db.commit()
    db.refresh(project)
    
    # 为项目所有者创建成员记录
    owner_member = ProjectMember(
        project_id=project.id,
        user_id=current_user.id,
        project_role_id=permission_service.PROJECT_OWNER
    )
    db.add(owner_member)
    db.commit()
    
    logger.info(f"项目创建成功: {project.name}(ID:{project.id})，所有者: {current_user.username}，企业: {current_user.org_id}")
    
    return Response(
        data=project,
        request_id=request.state.request_id
    )

@router.get("/", response_model=Response[PageData[Project]], summary="获取项目列表")
async def get_projects(
    request: Request,
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(10, ge=1, le=100, description="返回记录数"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user_with_permissions)
):
    """获取项目列表（基于权限过滤）"""
    permission_service = get_permission_service(db)
    
    # 获取当前用户可以访问的项目ID列表
    accessible_project_ids = permission_service.get_accessible_projects(current_user)
    
    # 基于权限过滤项目
    projects_query = db.query(ProjectModel).filter(
        ProjectModel.id.in_(accessible_project_ids)
    ).order_by(ProjectModel.created_at.desc())
    
    projects = projects_query.offset(skip).limit(limit).all()
    total = projects_query.count()
    
    # 计算总页数
    total_page = (total + limit - 1) // limit if limit > 0 else 0
    
    # 创建分页信息
    page_info = PageInfo(
        page=skip // limit + 1,
        page_size=limit,
        total=total,
        total_page=total_page
    )
    
    # 创建分页数据
    page_data = PageData(
        items=projects,
        page_info=page_info
    )
    
    return Response(
        data=page_data,
        request_id=request.state.request_id
    )

@router.get("/{project_id}", response_model=Response[Any], summary="获取项目详情")
async def get_project(
    project_id: str,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user_with_permissions)
):
    """获取项目详情（需要权限验证）"""
    permission_service = get_permission_service(db)
    
    project = db.query(ProjectModel).filter(ProjectModel.id == project_id).first()
    if not project:
        raise HTTPException(status_code=404, detail="项目不存在")
    
    # 检查权限
    if not permission_service.check_project_access(current_user, project_id):
        raise HTTPException(status_code=403, detail="无权访问此项目")
    
    # 获取项目关联的数据源
    data_sources = db.query(DataSource).filter(DataSource.project_id == project_id).all()
    
    # 获取项目关联的分析记录
    analyses = db.query(Analysis).filter(Analysis.project_id == project_id)\
                .order_by(Analysis.created_at.desc())\
                .limit(10)\
                .all()
    
    # 检查用户操作权限
    can_edit = permission_service.check_project_operation(current_user, project_id, "edit")
    can_delete = permission_service.check_project_operation(current_user, project_id, "delete")
    can_manage_members = permission_service.can_invite_member(current_user, project_id)
    
    # 获取项目所有者的用户名
    owner_username = None
    if project.owner_id:
        owner = db.query(User).filter(User.id == project.owner_id).first()
        if owner:
            owner_username = owner.username
    
    # 构建响应数据
    result = {
        "id": project.id,
        "name": project.name,
        "description": project.description,
        "user_id": project.user_id,
        "owner_id": project.owner_id,
        "owner_username": owner_username,  # 添加所有者用户名
        "org_id": project.org_id,
        "visibility": project.visibility,
        "report_prompt": project.report_prompt,  # 添加报告提示词字段
        "created_at": project.created_at,
        "updated_at": project.updated_at,
        "data_sources": [],
        "analyses": [],
        # 用户权限信息
        "permissions": {
            "can_edit": can_edit,
            "can_delete": can_delete,
            "can_manage_members": can_manage_members
        }
    }
    
    # 添加数据源信息
    for ds in data_sources:
        ds_dict = {
            "id": ds.id,
            "name": ds.name,
            "description": ds.description,
            "type": ds.type,
            "config": ds.config,
            "project_id": ds.project_id,
            "created_at": ds.created_at,
            "updated_at": ds.updated_at
        }
        result["data_sources"].append(ds_dict)
    
    # 添加分析记录信息
    for analysis in analyses:
        analysis_dict = {
            "id": analysis.id,
            "query": analysis.query,
            "created_at": analysis.created_at
        }
        result["analyses"].append(analysis_dict)
    
    return Response(
        data=result,
        request_id=request.state.request_id
    )

@router.put("/{project_id}", response_model=Response[Project], summary="更新项目")
async def update_project(
    project_id: str,
    project_in: ProjectUpdate,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user_with_permissions)
):
    """更新项目（需要权限验证）"""
    project = db.query(ProjectModel).filter(ProjectModel.id == project_id).first()
    if not project:
        raise HTTPException(status_code=404, detail="项目不存在")
    
    # 检查权限
    permission_service = get_permission_service(db)
    if not permission_service.check_project_operation(current_user, project_id, "edit"):
        raise HTTPException(status_code=403, detail="无权修改此项目")
    
    # 如果要更新名称，检查是否与可见范围内的其他项目重名
    if project_in.name and project_in.name != project.name:
        # 获取当前用户可以访问的项目ID列表
        accessible_project_ids = permission_service.get_accessible_projects(current_user)
        
        existing_project = db.query(ProjectModel).filter(
            ProjectModel.name == project_in.name, 
            ProjectModel.id != project_id,
            ProjectModel.id.in_(accessible_project_ids)
        ).first()
        if existing_project:
            return Response(
                code=400,
                message="项目名称已存在，请使用其他名称",
                request_id=request.state.request_id
            )
    
    update_data = project_in.dict(exclude_unset=True)
    
    for field, value in update_data.items():
        setattr(project, field, value)
    
    db.commit()
    db.refresh(project)
    
    return Response(
        data=project,
        request_id=request.state.request_id
    )

@router.delete("/{project_id}", response_model=Response, summary="删除项目")
async def delete_project(
    project_id: str,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user_with_permissions)
):
    """删除项目（需要权限验证）"""
    project = db.query(ProjectModel).filter(ProjectModel.id == project_id).first()
    if not project:
        raise HTTPException(status_code=404, detail="项目不存在")
    
    # 检查权限
    permission_service = get_permission_service(db)
    if not permission_service.check_project_operation(current_user, project_id, "delete"):
        raise HTTPException(status_code=403, detail="无权删除此项目")
    
    db.delete(project)
    db.commit()
    
    return Response(
        message="项目删除成功",
        request_id=request.state.request_id
    )

@router.get("/{project_id}/tables", response_model=Response[List[Any]], summary="获取项目关联的表")
async def get_project_tables(
    project_id: str,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user_with_permissions)
):
    """获取项目关联的所有表（需要权限验证）"""
    project = db.query(ProjectModel).filter(ProjectModel.id == project_id).first()
    if not project:
        raise HTTPException(status_code=404, detail="项目不存在")
    
    # 检查权限
    permission_service = get_permission_service(db)
    if not permission_service.check_project_access(current_user, project_id):
        raise HTTPException(status_code=403, detail="无权访问此项目")
    
    # 获取项目关联的所有数据源
    data_sources = db.query(DataSource).filter(DataSource.project_id == project_id).all()
    
    # 获取所有数据源关联的表
    tables = []
    for data_source in data_sources:
        ds_tables = db.query(SelectedTable).filter(SelectedTable.data_source_id == data_source.id).all()
        # 将模型实例转换为字典以便序列化
        for table in ds_tables:
            table_dict = {
                "id": table.id,
                "data_source_id": table.data_source_id,
                "table_name": table.table_name,
                "table_description": table.table_description,
                "table_schema": table.table_schema,
                "sample_data": table.sample_data,
                "created_at": table.created_at,
                "updated_at": table.updated_at
            }
            tables.append(table_dict)
    
    return Response(
        data=tables,
        request_id=request.state.request_id
    )

@router.get("/{project_id}/selected-tables", response_model=Response[List[Any]], summary="获取项目选定的表")
async def get_project_selected_tables(
    project_id: str,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user_with_permissions)
):
    """获取项目选定的所有表，用于表描述步骤（需要权限验证）"""
    project = db.query(ProjectModel).filter(ProjectModel.id == project_id).first()
    if not project:
        raise HTTPException(status_code=404, detail="项目不存在")
    
    # 检查权限
    permission_service = get_permission_service(db)
    if not permission_service.check_project_access(current_user, project_id):
        raise HTTPException(status_code=403, detail="无权访问此项目")
    
    # 获取项目关联的所有数据源
    data_sources = db.query(DataSource).filter(DataSource.project_id == project_id).all()
    
    # 获取所有数据源关联的表
    tables = []
    for data_source in data_sources:
        ds_tables = db.query(SelectedTable).filter(SelectedTable.data_source_id == data_source.id).all()
        # 将模型实例转换为字典以便序列化
        for table in ds_tables:
            table_dict = {
                "id": table.id,
                "data_source_id": table.data_source_id,
                "table_name": table.table_name,
                "table_description": table.table_description,
                "table_schema": table.table_schema,
                "created_at": table.created_at,
                "updated_at": table.updated_at
            }
            tables.append(table_dict)
    
    return Response(
        data=tables,
        request_id=request.state.request_id
    )

@router.put("/{project_id}/selected-tables/{table_id}", response_model=Response[Any], summary="更新表描述")
async def update_table_description(
    project_id: str,
    table_id: str,
    table_update: Dict[str, Any],
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user_with_permissions)
):
    """更新表描述信息（需要权限验证）"""
    project = db.query(ProjectModel).filter(ProjectModel.id == project_id).first()
    if not project:
        raise HTTPException(status_code=404, detail="项目不存在")
    
    # 检查权限
    permission_service = get_permission_service(db)
    if not permission_service.check_project_operation(current_user, project_id, "edit"):
        raise HTTPException(status_code=403, detail="无权修改此项目")
    
    table = db.query(SelectedTable).filter(SelectedTable.id == table_id).first()
    if not table:
        raise HTTPException(status_code=404, detail="表不存在")
    
    # 检查该表是否属于该项目的数据源
    data_source = db.query(DataSource).filter(
        DataSource.id == table.data_source_id,
        DataSource.project_id == project_id
    ).first()
    if not data_source:
        raise HTTPException(status_code=403, detail="无权访问此表")
    
    # 更新表描述
    if "table_description" in table_update:
        table.table_description = table_update["table_description"]
        db.commit()
        db.refresh(table)
    
    return Response(
        data={
            "id": table.id,
            "table_name": table.table_name,
            "table_description": table.table_description,
            "updated_at": table.updated_at
        },
        message="表描述更新成功",
        request_id=request.state.request_id
    ) 