from typing import List, Optional

from fastapi import APIRouter, Depends, Request
from pydantic import BaseModel

from app.core.middleware.auth import get_current_user
from app.schemas.base import Response
from app.core.logger import log

router = APIRouter()


class ExampleData(BaseModel):
    id: int
    name: str
    description: Optional[str] = None


@router.get("/list", response_model=Response[List[ExampleData]])
async def get_examples(request: Request):
    """
    获取示例列表
    """
    examples = [
        ExampleData(id=1, name="示例1", description="这是示例1的描述"),
        ExampleData(id=2, name="示例2", description="这是示例2的描述"),
        ExampleData(id=3, name="示例3", description="这是示例3的描述"),
    ]
    log.info("获取示例列表", extra={"aaaa":"bbbb"})
    return Response(data=examples, request_id=request.state.request_id)


@router.get("/{example_id}", response_model=Response[ExampleData])
async def get_example(example_id: int, request: Request):
    """
    获取示例详情
    """
    example = ExampleData(id=example_id, name=f"示例{example_id}", description=f"这是示例{example_id}的描述")
    
    return Response(data=example, request_id=request.state.request_id)


@router.post("/", response_model=Response[ExampleData])
async def create_example(
    example: ExampleData, 
    request: Request,
    current_user = Depends(get_current_user)
):
    """
    创建示例（需要认证）
    """
    return Response(data=example, request_id=request.state.request_id)


@router.put("/{example_id}", response_model=Response[ExampleData])
async def update_example(
    example_id: int, 
    example: ExampleData, 
    request: Request,
    current_user = Depends(get_current_user)
):
    """
    更新示例（需要认证）
    """
    example.id = example_id
    
    return Response(data=example, request_id=request.state.request_id)


@router.delete("/{example_id}", response_model=Response)
async def delete_example(
    example_id: int, 
    request: Request,
    current_user = Depends(get_current_user)
):
    """
    删除示例（需要认证）
    """
    return Response(message="删除成功", request_id=request.state.request_id)
