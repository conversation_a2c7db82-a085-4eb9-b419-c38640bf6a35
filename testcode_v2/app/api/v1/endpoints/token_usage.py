from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.utils.deps import get_current_active_user
from app.models.user import User
from app.services.token_usage_service import TokenUsageService
from app.models.organization import Organization

router = APIRouter()


@router.get("/usage/stats", response_model=Dict[str, Any])
async def get_token_usage_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取当前用户所属组织的Token用量统计信息
    
    Returns:
        Dict[str, Any]: 包含用量统计的字典
    """
    try:
        # 获取用户所属的组织
        if not current_user.org_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户未关联任何组织"
            )
        
        token_service = TokenUsageService(db)
        stats = token_service.get_usage_stats(current_user.org_id)
        
        return {
            "success": True,
            "data": stats,
            "message": "Token用量统计获取成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取Token用量统计失败: {str(e)}"
        )


@router.get("/usage/check-limit")
async def check_token_limit(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    检查当前用户所属组织是否还有可用的Token额度
    
    Returns:
        Dict[str, Any]: 检查结果
    """
    try:
        if not current_user.org_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户未关联任何组织"
            )
        
        token_service = TokenUsageService(db)
        has_remaining = token_service.has_tokens_remaining(current_user.org_id)
        stats = token_service.get_usage_stats(current_user.org_id)
        
        return {
            "success": True,
            "data": {
                "has_tokens_remaining": has_remaining,
                "stats": stats
            },
            "message": "Token限额检查完成"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"检查Token限额失败: {str(e)}"
        )


@router.put("/limit/{organization_id}")
async def update_token_limit(
    organization_id: str,
    new_limit: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    更新组织的Token限额（管理员功能）
    
    Args:
        organization_id: 组织ID
        new_limit: 新的Token限额
        
    Returns:
        Dict[str, Any]: 更新结果
    """
    try:
        # 这里应该添加管理员权限检查
        # if not current_user.is_admin:
        #     raise HTTPException(status_code=403, detail="权限不足")
        
        # 获取组织
        org = db.query(Organization).filter(Organization.id == organization_id).first()
        if not org:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="组织不存在"
            )
        
        # 更新限额
        old_limit = org.token_limit
        org.token_limit = new_limit
        db.commit()
        
        return {
            "success": True,
            "data": {
                "organization_id": organization_id,
                "old_limit": old_limit,
                "new_limit": new_limit
            },
            "message": "Token限额更新成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新Token限额失败: {str(e)}"
        )


@router.post("/usage/reset/{organization_id}")
async def reset_token_usage(
    organization_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    重置组织的Token用量（管理员功能）
    
    Args:
        organization_id: 组织ID
        
    Returns:
        Dict[str, Any]: 重置结果
    """
    try:
        # 这里应该添加管理员权限检查
        # if not current_user.is_admin:
        #     raise HTTPException(status_code=403, detail="权限不足")
        
        # 获取组织
        org = db.query(Organization).filter(Organization.id == organization_id).first()
        if not org:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="组织不存在"
            )
        
        # 重置用量
        old_usage = org.total_token_usage
        org.total_token_usage = 0
        db.commit()
        
        return {
            "success": True,
            "data": {
                "organization_id": organization_id,
                "old_usage": old_usage,
                "new_usage": 0
            },
            "message": "Token用量重置成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"重置Token用量失败: {str(e)}"
        ) 