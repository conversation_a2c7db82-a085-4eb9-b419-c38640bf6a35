from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import or_, and_
from typing import List, Optional
from datetime import datetime
import json

from app.db.session import get_db
from app.models.analysis_template import AnalysisTemplate
from app.models.user import User
from app.core.middleware.auth import get_current_user_with_permissions
from pydantic import BaseModel

router = APIRouter()


class TemplateResponse(BaseModel):
    id: str
    name: str
    description: Optional[str]
    content: str
    category: Optional[str]
    tags: Optional[List[str]]
    template_type: str
    complexity_level: Optional[str]
    applicable_data_types: Optional[List[str]]
    is_public: bool
    is_system: bool
    usage_count: int
    created_at: datetime
    created_by: Optional[int]
    creator_name: Optional[str]
    can_edit: bool  # 当前用户是否可以编辑此模板
    can_delete: bool  # 当前用户是否可以删除此模板

    class Config:
        from_attributes = True


class TemplateCreateRequest(BaseModel):
    name: str
    description: Optional[str] = None
    content: str
    category: Optional[str] = None
    tags: Optional[List[str]] = None
    template_type: Optional[str] = 'query'
    complexity_level: Optional[str] = None
    applicable_data_types: Optional[List[str]] = None
    is_public: bool = False


class TemplateUpdateRequest(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    content: Optional[str] = None
    is_public: Optional[bool] = None


@router.get("/templates", response_model=List[TemplateResponse])
async def get_templates(
    project_id: str = Query(..., description="项目ID"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user_with_permissions)
):
    """获取模板列表"""
    
    # 权限控制的查询：
    # 1. 系统模板（所有人可见）
    # 2. 公开模板（所有人可见）
    # 3. 当前用户创建的私有模板（只有创建者可见）
    query = db.query(AnalysisTemplate).filter(
        or_(
            AnalysisTemplate.is_system == True,                    # 系统模板
            AnalysisTemplate.is_public == True,                    # 公开模板
            and_(                                                  # 私有模板
                AnalysisTemplate.is_public == False,
                AnalysisTemplate.created_by == current_user.id
            )
        )
    )

    if project_id:
        query = query.filter(AnalysisTemplate.project_id == project_id)

    # 搜索筛选
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            or_(
                AnalysisTemplate.name.like(search_term),
                AnalysisTemplate.description.like(search_term)
            )
        )
    
    # 排序：系统模板优先，然后按使用次数，最后按创建时间
    templates = query.order_by(
        AnalysisTemplate.is_system.desc(),
        AnalysisTemplate.usage_count.desc(),
        AnalysisTemplate.created_at.desc()
    ).all()
    
    # 构建响应
    result = []
    for template in templates:
        # 判断当前用户的权限
        can_edit = False
        can_delete = False
        creator_name = None

        if not template.is_system:  # 系统模板不能编辑删除
            if template.created_by == current_user.id or current_user.role_id == 1 or current_user.role_id == 2:  # 创建者及企业管理员可以编辑删除
                can_edit = True
                can_delete = True

        # 获取创建者姓名
        if template.creator:
            creator_name = template.creator.username

        # 解析JSON字段
        tags = []
        if template.tags:
            try:
                tags = json.loads(template.tags)
            except:
                tags = []

        applicable_data_types = []
        if template.applicable_data_types:
            try:
                applicable_data_types = json.loads(template.applicable_data_types)
            except:
                applicable_data_types = []

        result.append(TemplateResponse(
            id=template.id,
            name=template.name,
            description=template.description,
            content=template.content,
            category=template.category,
            tags=tags,
            template_type=template.template_type or 'query',
            complexity_level=template.complexity_level,
            applicable_data_types=applicable_data_types,
            is_public=template.is_public,
            is_system=template.is_system,
            usage_count=template.usage_count,
            created_at=template.created_at,
            created_by=template.created_by,
            creator_name=creator_name,
            can_edit=can_edit,
            can_delete=can_delete
        ))

    return result


@router.post("/templates", response_model=TemplateResponse)
async def create_template(
    template_data: TemplateCreateRequest,
    project_id: str = Query(..., description="项目ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user_with_permissions)
):
    """创建模板"""
    
    # 检查模板名称是否重复（在同一项目内）
    existing = db.query(AnalysisTemplate).filter(
        and_(
            AnalysisTemplate.name == template_data.name,
            AnalysisTemplate.project_id == project_id
        )
    ).first()
    
    if existing:
        raise HTTPException(status_code=400, detail="模板名称已存在")
    
    # 创建模板
    template = AnalysisTemplate(
        name=template_data.name,
        description=template_data.description,
        content=template_data.content,
        category=template_data.category,
        tags=json.dumps(template_data.tags) if template_data.tags else None,
        template_type=template_data.template_type or 'query',
        complexity_level=template_data.complexity_level,
        applicable_data_types=json.dumps(template_data.applicable_data_types) if template_data.applicable_data_types else None,
        project_id=project_id,
        created_by=current_user.id,
        is_public=template_data.is_public
    )
    
    db.add(template)
    db.commit()
    db.refresh(template)
    
    # 解析JSON字段
    tags = []
    if template.tags:
        try:
            tags = json.loads(template.tags)
        except:
            tags = []

    applicable_data_types = []
    if template.applicable_data_types:
        try:
            applicable_data_types = json.loads(template.applicable_data_types)
        except:
            applicable_data_types = []

    return TemplateResponse(
        id=template.id,
        name=template.name,
        description=template.description,
        content=template.content,
        category=template.category,
        tags=tags,
        template_type=template.template_type or 'query',
        complexity_level=template.complexity_level,
        applicable_data_types=applicable_data_types,
        is_public=template.is_public,
        is_system=template.is_system,
        usage_count=template.usage_count,
        created_at=template.created_at,
        created_by=template.created_by,
        creator_name=current_user.username,
        can_edit=True,  # 创建者可以编辑
        can_delete=True  # 创建者可以删除
    )


@router.put("/templates/{template_id}", response_model=TemplateResponse)
async def update_template(
    template_id: str,
    template_data: TemplateUpdateRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user_with_permissions)
):
    """更新模板"""
    
    template = db.query(AnalysisTemplate).filter(
        AnalysisTemplate.id == template_id
    ).first()
    
    if not template:
        raise HTTPException(status_code=404, detail="模板不存在")
    
    # 权限检查：只有创建者可以修改（系统模板除外）
    if template.is_system:
        raise HTTPException(status_code=403, detail="系统模板不能修改")

    if template.created_by != current_user.id:
        raise HTTPException(status_code=403, detail="只能修改自己创建的模板")
    
    # 更新字段
    if template_data.name is not None:
        template.name = template_data.name
    if template_data.description is not None:
        template.description = template_data.description
    if template_data.content is not None:
        template.content = template_data.content
    if template_data.is_public is not None:
        template.is_public = template_data.is_public
    
    db.commit()
    db.refresh(template)
    
    return TemplateResponse(
        id=template.id,
        name=template.name,
        description=template.description,
        content=template.content,
        is_public=template.is_public,
        is_system=template.is_system,
        usage_count=template.usage_count,
        created_at=template.created_at,
        created_by=template.created_by,
        creator_name=current_user.username,
        can_edit=True,  # 创建者可以编辑
        can_delete=True  # 创建者可以删除
    )


@router.delete("/templates/{template_id}")
async def delete_template(
    template_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user_with_permissions)
):
    """删除模板"""
    
    template = db.query(AnalysisTemplate).filter(
        AnalysisTemplate.id == template_id
    ).first()
    
    if not template:
        raise HTTPException(status_code=404, detail="模板不存在")
    
    # 权限检查
    if template.is_system:
        raise HTTPException(status_code=403, detail="系统模板不能删除")

    if template.created_by != current_user.id:
        raise HTTPException(status_code=403, detail="只能删除自己创建的模板")
    
    db.delete(template)
    db.commit()
    
    return {"message": "模板删除成功"}


@router.post("/templates/{template_id}/use")
async def use_template(
    template_id: str,
    db: Session = Depends(get_db)
):
    """记录模板使用（增加使用次数）"""
    
    template = db.query(AnalysisTemplate).filter(
        AnalysisTemplate.id == template_id
    ).first()
    
    if not template:
        raise HTTPException(status_code=404, detail="模板不存在")
    
    template.usage_count += 1
    db.commit()
    
    return {"message": "使用次数已更新", "usage_count": template.usage_count}


# 移除了模板分类接口，因为不再使用分类功能
