import logging
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import Dict, Any

from app.db.session import get_db
from app.core.middleware.auth import get_current_user
from typing import Dict
from app.services.project_intelligence import ProjectIntelligenceService
from app.schemas.base import Response

router = APIRouter()
logger = logging.getLogger(__name__)

@router.post("/start/{project_id}", response_model=Dict[str, str])
async def start_intelligence_session(
    project_id: str,
    db: Session = Depends(get_db),
    current_user: Dict = Depends(get_current_user)
):
    """启动智能理解会话"""
    try:
        logger.info(f"API: 收到启动智能理解会话请求，项目ID: {project_id}")
        service = ProjectIntelligenceService(db)
        user_id = int(current_user.get("sub"))  # JWT payload中的用户ID
        session_id = await service.start_intelligence_session(project_id, user_id)
        logger.info(f"API: 成功创建会话，返回session_id: {session_id}")
        return {"session_id": session_id}
    except ValueError as e:
        logger.error(f"API: 启动智能理解会话失败 (ValueError): {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"API: 启动智能理解会话失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"启动智能理解会话失败: {str(e)}"
        )

@router.post("/analyze/{session_id}")
async def analyze_business_understanding(
    session_id: str,
    db: Session = Depends(get_db)
):
    """分析业务理解"""
    try:
        logger.info(f"API: 收到分析业务理解请求，session_id: {session_id}")
        service = ProjectIntelligenceService(db)
        understanding = await service.analyze_business_understanding(session_id)
        logger.info(f"API: 业务理解分析完成，session_id: {session_id}")
        return understanding
    except ValueError as e:
        logger.error(f"API: 业务理解分析失败 (ValueError): {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"API: 业务理解分析失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"业务理解分析失败: {str(e)}"
        )

@router.post("/questions/{session_id}")
async def generate_questions(
    session_id: str,
    db: Session = Depends(get_db)
):
    """生成智能问题"""
    try:
        service = ProjectIntelligenceService(db)
        questions = await service.generate_intelligent_questions(session_id)
        return {"questions": questions}
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"生成智能问题失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"生成智能问题失败: {str(e)}"
        )

@router.post("/answers/{session_id}")
async def save_answers(
    session_id: str,
    answers: Dict[str, Any],
    db: Session = Depends(get_db)
):
    """保存用户回答"""
    try:
        service = ProjectIntelligenceService(db)
        success = await service.save_user_answers(session_id, answers)
        return {"success": success}
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"保存用户回答失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"保存用户回答失败: {str(e)}"
        )

@router.post("/predict/{session_id}")
async def predict_scenarios(
    session_id: str,
    db: Session = Depends(get_db)
):
    """预测场景"""
    try:
        service = ProjectIntelligenceService(db)
        predictions = await service.predict_scenarios(session_id)
        return {"scenarios": predictions}
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"场景预测失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"场景预测失败: {str(e)}"
        )

@router.post("/confirmations/{session_id}")
async def save_scenario_confirmations(
    session_id: str,
    confirmations: Dict[str, Any],
    db: Session = Depends(get_db)
):
    """保存场景确认信息"""
    try:
        service = ProjectIntelligenceService(db)
        success = await service.save_scenario_confirmations(session_id, confirmations)
        return {"success": success}
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"保存场景确认失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"保存场景确认失败: {str(e)}"
        )

@router.post("/complete/{session_id}")
async def complete_session(
    session_id: str,
    db: Session = Depends(get_db)
):
    """完成智能理解会话"""
    try:
        logger.info(f"API: 开始完成智能理解会话，session_id: {session_id}")
        service = ProjectIntelligenceService(db)
        
        # 执行智能理解完成流程（包含智能处理）
        summary = await service.complete_intelligence_session(session_id)
        
        logger.info(f"API: 智能理解会话完成，session_id: {session_id}")
        return {
            "success": True,
            "message": "智能理解完成",
            "data": summary
        }
    except ValueError as e:
        logger.error(f"API: 完成智能理解会话失败 (ValueError): {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"API: 完成智能理解会话失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"完成智能理解会话失败: {str(e)}"
        )

@router.get("/session/{session_id}")
async def get_session_status(
    session_id: str,
    db: Session = Depends(get_db)
):
    """获取会话状态"""
    try:
        service = ProjectIntelligenceService(db)
        session_data = service.get_session_status(session_id)
        if not session_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="会话不存在或已过期"
            )
        return session_data
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取会话状态失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取会话状态失败: {str(e)}"
        ) 