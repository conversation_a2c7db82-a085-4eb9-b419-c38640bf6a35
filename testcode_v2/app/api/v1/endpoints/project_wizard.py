import uuid
import logging
from fastapi import APIRouter, Depends, HTTPException, status, Request, Query
from sqlalchemy.orm import Session
from typing import List, Dict, Any

from app.db.session import get_db
from app.models.project import Project
from app.models.data_source import DataSource
from app.services.datasource import DataSourceService
from app.schemas.project import ProjectCreate
from app.schemas.data_source import (
    DataSourceCreate, 
    ConnectionTest,
    ConnectionTestResult,
    DataSourceType,
    DataSourceCreateTemp
)
from app.schemas.data_source import TableSaveRequest
from app.schemas.base import Response
from app.core.middleware.auth import get_current_user, get_current_user_with_permissions
from app.services.user import get_accessible_user_ids
from app.models.user import User

router = APIRouter()
logger = logging.getLogger(__name__)

@router.post("/create_project", status_code=status.HTTP_201_CREATED, response_model=Dict[str, Any])
def create_project(
    project_data: ProjectCreate,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user_with_permissions)
):
    """创建项目（步骤1）"""
    try:
        # 检查用户是否有创建项目的权限
        # 超级管理员(role_id=1)和企业管理员(role_id=2)总是可以创建项目
        # 普通用户(role_id=3)需要检查can_create_project字段
        if current_user.role_id == 3 and not current_user.can_create_project:
            return {
                "success": False,
                "message": "您没有创建项目的权限，请联系管理员",
            }
        # 获取当前用户可以访问的用户ID列表
        accessible_user_ids = get_accessible_user_ids(db, current_user.id)
        
        # 检查项目名称在可见范围内是否已存在
        existing_project = db.query(Project).filter(
            Project.name == project_data.name,
            Project.user_id.in_(accessible_user_ids)
        ).first()
        if existing_project:
            return {
                "success": False,
                "message": "项目名称已存在，请使用其他名称",
            }
        
        # 创建新项目
        # current_user现在是User对象，直接使用id属性
        user_id = current_user.id
        
        new_project = Project(
            id=str(uuid.uuid4()),
            name=project_data.name,
            description=project_data.description,
            user_id=user_id,
            owner_id=user_id,  # 设置项目所有者为当前用户
            org_id=current_user.org_id,  # 设置项目所属企业
            visibility=project_data.visibility or "PRIVATE"  # 设置项目可见性
        )
        
        db.add(new_project)
        db.commit()
        db.refresh(new_project)
        
        return {
            "success": True,
            "message": "项目创建成功",
            "project_id": new_project.id,
            "project": {
                "id": new_project.id,
                "name": new_project.name,
                "description": new_project.description,
                "created_at": new_project.created_at,
                "updated_at": new_project.updated_at
            }
        }
    except Exception as e:
        logger.error(f"创建项目失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建项目失败: {str(e)}"
        )

@router.post("/add_datasource", status_code=status.HTTP_201_CREATED, response_model=Dict[str, Any])
def add_datasource(
    datasource_data: DataSourceCreateTemp,
    request: Request,
    db: Session = Depends(get_db)
):
    """添加数据源（步骤2）"""
    try:
        # 检查项目是否存在
        project = db.query(Project).filter(Project.id == datasource_data.project_id).first()
        if not project:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"项目 {datasource_data.project_id} 不存在"
            )
        
        # 优化用户体验：不在这里获取数据库版本，避免阻塞用户操作
        # 数据库版本将在后续获取表列表时异步获取
        db_version = None
        
        # 创建新数据源
        new_datasource = DataSource(
            id=str(uuid.uuid4()),
            name=datasource_data.name,
            description=datasource_data.description,
            type=datasource_data.type,
            config=datasource_data.config,
            db_version=db_version,  # 暂时设为None，后续异步获取
            project_id=datasource_data.project_id
        )
        
        db.add(new_datasource)
        db.commit()
        db.refresh(new_datasource)
        
        return {
            "success": True,
            "message": "数据源添加成功",
            "datasource_id": new_datasource.id,
            "datasource": {
                "id": new_datasource.id,
                "name": new_datasource.name,
                "description": new_datasource.description,
                "type": new_datasource.type,
                "db_version": new_datasource.db_version,
                "project_id": new_datasource.project_id,
                "created_at": new_datasource.created_at,
                "updated_at": new_datasource.updated_at
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"添加数据源失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"添加数据源失败: {str(e)}"
        )

@router.post("/test_connection", response_model=ConnectionTestResult)
def test_connection(test_data: ConnectionTest):
    """测试数据源连接（步骤2）"""
    try:
        logger.info(f"接收到测试连接请求：{test_data}")
        
        # 验证参数
        if not test_data.type:
            return ConnectionTestResult(
                success=False,
                message="缺少数据源类型参数",
                tables=None,
                db_version=None
            )
        
        if not test_data.config or not isinstance(test_data.config, dict):
            return ConnectionTestResult(
                success=False,
                message="缺少或无效的数据源配置参数",
                tables=None,
                db_version=None
            )
        
        # 调用连接测试服务
        result = DataSourceService.test_connection(
            test_data.type,
            test_data.config
        )
        
        return ConnectionTestResult(
            success=result["success"],
            message=result["message"],
            tables=result.get("tables"),
            db_version=result.get("db_version")
        )
    except Exception as e:
        logger.error(f"测试连接失败: {str(e)}")
        return ConnectionTestResult(
            success=False,
            message=f"测试连接失败: {str(e)}",
            tables=None,
            db_version=None
        )

@router.get("/datasource_tables/{datasource_id}", response_model=List[str])
def get_datasource_tables(
    datasource_id: str,
    request: Request,
    db: Session = Depends(get_db)
):
    """获取数据源中的表列表（步骤3）"""
    try:
        # 获取数据源信息
        datasource = db.query(DataSource).filter(DataSource.id == datasource_id).first()
        if not datasource:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"数据源 {datasource_id} 不存在"
            )
        
        # 测试连接并获取表列表
        result = DataSourceService.test_connection(
            datasource.type,
            datasource.config
        )
        
        if not result["success"]:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"连接数据源失败: {result['message']}"
            )
        
        # 如果数据源还没有版本信息，且本次获取到了版本信息，则更新数据源
        if not datasource.db_version and result.get("db_version"):
            try:
                datasource.db_version = result.get("db_version")
                db.commit()
                logger.info(f"更新数据源 {datasource_id} 的版本信息: {datasource.db_version}")
            except Exception as e:
                logger.warning(f"更新数据源版本信息失败: {str(e)}")
                db.rollback()
        
        return result.get("tables", [])
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取数据源表列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取数据源表列表失败: {str(e)}"
        )

@router.post("/save_tables/{datasource_id}", response_model=Dict[str, Any])
def save_tables(
    datasource_id: str,
    table_data: TableSaveRequest,
    request: Request,
    db: Session = Depends(get_db),
    max_field_length: int = Query(200, description="字段值的最大长度，超过此长度将被截断")
):
    """保存选定的表（步骤3）
    
    Args:
        datasource_id: 数据源ID
        table_data: 表数据请求体
        request: 请求对象
        db: 数据库会话
        max_field_length: 样本数据字段值的最大长度，超过将被截断为占位符
    
    Returns:
        保存结果，包含成功和失败的表计数
    """
    try:
        # 获取数据源信息
        datasource = db.query(DataSource).filter(DataSource.id == datasource_id).first()
        if not datasource:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"数据源 {datasource_id} 不存在"
            )
        
        # 保存选定的表
        results = DataSourceService.save_selected_tables(
            db,
            datasource_id,
            table_data.tables,
            datasource.type,
            datasource.config,
            max_field_length
        )
        
        # 统计成功和失败的表
        success_count = sum(1 for result in results if result["success"])
        failed_count = sum(1 for result in results if not result["success"])
        
        return {
            "success": True,
            "message": f"已成功保存 {success_count} 个表，失败 {failed_count} 个表",
            "results": results
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"保存选定表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"保存选定表失败: {str(e)}"
        )

@router.post("/save_selected_tables/{project_id}", response_model=Dict[str, Any])
def save_selected_tables(
    project_id: str,
    table_data: TableSaveRequest,
    request: Request,
    db: Session = Depends(get_db)
):
    """保存选定的表并继续到表描述步骤"""
    try:
        # 检查项目是否存在
        project = db.query(Project).filter(Project.id == project_id).first()
        if not project:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"项目 {project_id} 不存在"
            )
            
        # 获取项目所有数据源
        data_sources = db.query(DataSource).filter(DataSource.project_id == project_id).all()
        if not data_sources:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"项目 {project_id} 没有关联的数据源"
            )
            
        # 获取最新添加的数据源（通常是当前正在操作的）
        latest_datasource = max(data_sources, key=lambda x: x.created_at)
        
        # 使用现有的save_tables逻辑保存选定的表
        results = DataSourceService.save_selected_tables(
            db,
            latest_datasource.id,
            table_data.tables,
            latest_datasource.type,
            latest_datasource.config,
            200  # 使用默认的max_field_length
        )
        
        # 统计成功和失败的表
        success_count = sum(1 for result in results if result["success"])
        failed_count = sum(1 for result in results if not result["success"])
        
        return {
            "success": True,
            "message": f"已成功保存 {success_count} 个表，失败 {failed_count} 个表",
            "results": results
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"保存选定表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"保存选定表失败: {str(e)}"
        ) 