"""
项目成员管理API接口
"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import and_

from app.db.session import get_db
from app.models.user import User
from app.models.project import Project
from app.models.project_member import ProjectMember
from app.models.project_role import ProjectRole
from app.schemas.project_member import (
    ProjectMemberList, ProjectMemberWithDetails, InviteMemberRequest,
    ProjectMemberUpdate
)
from app.schemas.user import User as UserSchema
from app.services.permission_service import get_permission_service
from app.core.middleware.auth import get_current_user_with_permissions
from app.core.exceptions import PermissionDeniedException


router = APIRouter()


@router.get("/{project_id}/members", response_model=ProjectMemberList)
def get_project_members(
    project_id: str,
    current_user: User = Depends(get_current_user_with_permissions),
    db: Session = Depends(get_db)
):
    """获取项目成员列表"""
    permission_service = get_permission_service(db)
    
    # 检查访问权限
    if not permission_service.check_project_access(current_user, project_id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权限访问该项目"
        )
    
    # 获取项目信息
    project = db.query(Project).filter(Project.id == project_id).first()
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="项目不存在"
        )
    
    members_with_details = []
    
    # 1. 添加项目所有者（如果存在）
    if project.owner_id:
        owner = db.query(User).filter(User.id == project.owner_id).first()
        if owner:
            owner_detail = ProjectMemberWithDetails(
                id=0,  # 虚拟ID，表示这不是真正的项目成员记录
                project_id=project_id,
                user_id=owner.id,
                project_role_id=permission_service.PROJECT_OWNER,
                invited_by=None,
                joined_at=project.created_at,  # 使用项目创建时间
                status='ACTIVE',
                username=owner.username,
                email=owner.email,
                role_name='项目所有者',
                invited_by_username=None
            )
            members_with_details.append(owner_detail)
    
    # 2. 添加有权限访问该项目的企业管理员（同企业的企业管理员）
    org_admins = db.query(User).filter(
        and_(
            User.org_id == project.org_id,
            User.role_id == permission_service.ORG_ADMIN,
            User.is_active == True,
            User.id != project.owner_id  # 排除已经作为所有者添加的用户
        )
    ).all()
    
    for admin in org_admins:
        admin_detail = ProjectMemberWithDetails(
            id=0,  # 虚拟ID
            project_id=project_id,
            user_id=admin.id,
            project_role_id=permission_service.ORG_ADMIN,  # 使用企业管理员角色ID
            invited_by=None,
            joined_at=project.created_at,
            status='ACTIVE',
            username=admin.username,
            email=admin.email,
            role_name='企业管理员',
            invited_by_username=None
        )
        members_with_details.append(admin_detail)
    
    # 3. 获取正式的项目成员（被邀请的成员）
    members_query = db.query(
        ProjectMember,
        User.username,
        User.email,
        ProjectRole.name.label('role_name')
    ).join(
        User, ProjectMember.user_id == User.id
    ).join(
        ProjectRole, ProjectMember.project_role_id == ProjectRole.id
    ).filter(
        and_(
            ProjectMember.project_id == project_id,
            ProjectMember.status == 'ACTIVE'
        )
    )
    
    members_data = members_query.all()
    
    # 获取邀请人信息
    for member, username, email, role_name in members_data:
        invited_by_username = None
        if member.invited_by:
            inviter = db.query(User.username).filter(User.id == member.invited_by).first()
            if inviter:
                invited_by_username = inviter.username
        
        member_detail = ProjectMemberWithDetails(
            id=member.id,
            project_id=member.project_id,
            user_id=member.user_id,
            project_role_id=member.project_role_id,
            invited_by=member.invited_by,
            joined_at=member.joined_at,
            status=member.status,
            username=username,
            email=email,
            role_name=role_name,
            invited_by_username=invited_by_username
        )
        members_with_details.append(member_detail)
    
    return ProjectMemberList(
        total=len(members_with_details),
        members=members_with_details
    )


@router.post("/{project_id}/members")
def invite_project_member(
    project_id: str,
    request: InviteMemberRequest,
    current_user: User = Depends(get_current_user_with_permissions),
    db: Session = Depends(get_db)
):
    """邀请项目成员"""
    permission_service = get_permission_service(db)
    
    # 检查邀请权限
    if not permission_service.can_invite_member(current_user, project_id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权限邀请项目成员"
        )
    
    # 验证项目角色
    if not permission_service.validate_project_role_for_invitation(request.project_role_id):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="只能邀请用户成为协作者或观察者"
        )
    
    # 检查被邀请用户是否存在且为同企业普通用户
    invited_user = db.query(User).filter(User.id == request.user_id).first()
    if not invited_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 获取项目信息
    project = db.query(Project).filter(Project.id == project_id).first()
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="项目不存在"
        )
    
    # 验证是否为同企业用户
    if invited_user.org_id != project.org_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="只能邀请同企业的用户"
        )
    
    # 验证被邀请用户是否为普通用户
    if invited_user.role_id != permission_service.NORMAL_USER:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="只能邀请普通用户"
        )
    
    # 检查是否已经是项目成员
    existing_member = db.query(ProjectMember).filter(
        and_(
            ProjectMember.project_id == project_id,
            ProjectMember.user_id == request.user_id,
            ProjectMember.status == 'ACTIVE'
        )
    ).first()
    
    if existing_member:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户已经是项目成员"
        )
    
    # 创建项目成员
    new_member = ProjectMember(
        project_id=project_id,
        user_id=request.user_id,
        project_role_id=request.project_role_id,
        invited_by=current_user.id
    )
    
    db.add(new_member)
    db.commit()
    db.refresh(new_member)
    
    return {"message": "成员邀请成功", "member_id": new_member.id}


@router.put("/{project_id}/members/{user_id}")
def update_project_member(
    project_id: str,
    user_id: int,
    request: ProjectMemberUpdate,
    current_user: User = Depends(get_current_user_with_permissions),
    db: Session = Depends(get_db)
):
    """更新项目成员角色"""
    permission_service = get_permission_service(db)
    
    # 检查管理权限
    if not permission_service.can_invite_member(current_user, project_id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权限管理项目成员"
        )
    
    # 查找项目成员
    member = db.query(ProjectMember).filter(
        and_(
            ProjectMember.project_id == project_id,
            ProjectMember.user_id == user_id,
            ProjectMember.status == 'ACTIVE'
        )
    ).first()
    
    if not member:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="项目成员不存在"
        )
    
    # 更新角色
    if request.project_role_id:
        if not permission_service.validate_project_role_for_invitation(request.project_role_id):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="只能设置为协作者或观察者角色"
            )
        member.project_role_id = request.project_role_id
    
    # 更新状态
    if request.status:
        member.status = request.status
    
    db.commit()
    
    return {"message": "成员信息更新成功"}


@router.delete("/{project_id}/members/{user_id}")
def remove_project_member(
    project_id: str,
    user_id: int,
    current_user: User = Depends(get_current_user_with_permissions),
    db: Session = Depends(get_db)
):
    """移除项目成员"""
    permission_service = get_permission_service(db)
    
    # 检查管理权限
    if not permission_service.can_invite_member(current_user, project_id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权限管理项目成员"
        )
    
    # 查找项目成员
    member = db.query(ProjectMember).filter(
        and_(
            ProjectMember.project_id == project_id,
            ProjectMember.user_id == user_id,
            ProjectMember.status == 'ACTIVE'
        )
    ).first()
    
    if not member:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="项目成员不存在"
        )
    
    # 不能移除项目所有者
    project = db.query(Project).filter(Project.id == project_id).first()
    if project and project.owner_id == user_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能移除项目所有者"
        )
    
    # 标记为非活跃状态（软删除）
    member.status = 'INACTIVE'
    db.commit()
    
    return {"message": "成员移除成功"}


@router.get("/{project_id}/invitable-users", response_model=List[UserSchema])
def get_invitable_users(
    project_id: str,
    current_user: User = Depends(get_current_user_with_permissions),
    db: Session = Depends(get_db)
):
    """获取可邀请的用户列表"""
    permission_service = get_permission_service(db)
    
    # 检查邀请权限
    if not permission_service.can_invite_member(current_user, project_id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权限邀请项目成员"
        )
    
    invitable_users = permission_service.get_invitable_users(current_user, project_id)
    
    return [
        UserSchema(
            id=user.id,
            username=user.username,
            email=user.email,
            is_active=user.is_active,
            is_superuser=user.is_superuser,
            created_at=user.created_at,
            updated_at=user.updated_at
        )
        for user in invitable_users
    ] 