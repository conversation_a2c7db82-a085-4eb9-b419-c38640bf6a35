from datetime import timed<PERSON><PERSON>
from typing import Any, Optional

from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import OAuth2PasswordRequestForm, HTTPAuthorizationCredentials, HTTPBearer
from sqlalchemy.orm import Session

from app.core.security import create_access_token, verify_password
from app.core.config import settings
from app.core.middleware.auth import get_current_user, get_current_user_with_permissions
from app.schemas.user import Token, User, LoginRequest
from app.schemas.base import Response
from app.services.user import get_user_by_username, load_user_relations
from app.services.token_service import TokenService
from app.utils.deps import get_db

router = APIRouter()
security = HTTPBearer(auto_error=False)

@router.post("/login", response_model=Response[Token])
async def login_access_token(
    request: Request,
    login_data: LoginRequest,
    db: Session = Depends(get_db)
) -> Any:
    """
    获取访问令牌
    """
    # 获取用户
    user = get_user_by_username(db, username=login_data.username)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
        )
    
    # 验证密码
    if not verify_password(login_data.password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
        )
    
    # 检查用户是否启用
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="账户已被禁用",
        )
    
    # 生成访问令牌
    token = create_access_token(
        subject=user.id,
        expires_minutes=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES,
        data={"username": user.username}
    )
    
    # 将token存储到Redis（实现单点登录）
    user_info = {
        "id": user.id,
        "username": user.username,
        "email": user.email,
        "is_active": user.is_active,
        "is_superuser": user.is_superuser,
        "pid": user.pid,
        "role_id": user.role_id,
        "org_id": user.org_id,
        "can_create_project": user.can_create_project
    }
    
    token_stored = TokenService.store_token(
        user_id=user.id,
        token=token,
        user_info=user_info,
        expires_minutes=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES
    )
    
    if not token_stored:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录失败，请稍后重试",
        )
    
    return Response(
        data={"access_token": token, "token_type": "bearer"},
        request_id=getattr(request.state, "request_id", None)
    )


@router.get("/me", response_model=Response[User])
async def read_users_me(
    request: Request,
    current_user = Depends(get_current_user_with_permissions)
) -> Any:
    """
    获取当前用户信息
    """
    return Response(
        data=current_user,
        request_id=getattr(request.state, "request_id", None)
    )


@router.post("/logout", response_model=Response[dict])
async def logout(
    request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> Any:
    """
    退出登录（撤销token）
    """
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="未提供认证凭证",
        )
    
    # 撤销token
    token_revoked = TokenService.revoke_token(credentials.credentials)
    
    if not token_revoked:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="退出登录失败，请稍后重试",
        )
    
    return Response(
        data={"message": "退出登录成功"},
        request_id=getattr(request.state, "request_id", None)
    )


 