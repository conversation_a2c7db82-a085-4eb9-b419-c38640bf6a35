from typing import List, Any
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.schemas.llm_model import (
    LLMModel, LLMModelSafe, LLMModelCreate, LLMModelUpdate, 
    LLMModelTestResult, LLMModelTestConfig
)
from app.schemas.base import Response
from app.services.llm_model_service import LLMModelService

router = APIRouter()


@router.get("/", response_model=Response[List[LLMModelSafe]])
def get_llm_models(
    skip: int = 0,
    limit: int = 100,
    active_only: bool = False,
    db: Session = Depends(get_db)
):
    """获取LLM模型列表"""
    service = LLMModelService(db)
    models = service.get_models(skip=skip, limit=limit, active_only=active_only)
    
    # 转换为安全的Schema，移除敏感信息
    safe_models = []
    for model in models:
        safe_model = LLMModelSafe(
            id=model.id,
            name=model.name,
            description=model.description,
            model_name=model.model_name,
            base_url=model.base_url,
            config=model.config,
            is_active=model.is_active,
            created_at=model.created_at,
            updated_at=model.updated_at
        )
        safe_models.append(safe_model)
    
    return Response(
        code=200,
        message="获取模型列表成功",
        data=safe_models
    )


@router.get("/{model_id}", response_model=Response[LLMModelSafe])
def get_llm_model(
    model_id: str,
    db: Session = Depends(get_db)
):
    """获取单个LLM模型"""
    service = LLMModelService(db)
    model = service.get_model(model_id)
    
    if not model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="模型不存在"
        )
    
    # 转换为安全的Schema，移除敏感信息
    safe_model = LLMModelSafe(
        id=model.id,
        name=model.name,
        description=model.description,
        model_name=model.model_name,
        base_url=model.base_url,
        config=model.config,
        is_active=model.is_active,
        created_at=model.created_at,
        updated_at=model.updated_at
    )
    
    return Response(
        code=200,
        message="获取模型成功",
        data=safe_model
    )


@router.post("/", response_model=Response[LLMModelSafe])
def create_llm_model(
    model_data: LLMModelCreate,
    db: Session = Depends(get_db)
):
    """创建LLM模型"""
    service = LLMModelService(db)
    model = service.create_model(model_data)
    
    # 转换为安全的Schema，移除敏感信息
    safe_model = LLMModelSafe(
        id=model.id,
        name=model.name,
        description=model.description,
        model_name=model.model_name,
        base_url=model.base_url,
        config=model.config,
        is_active=model.is_active,
        created_at=model.created_at,
        updated_at=model.updated_at
    )
    
    return Response(
        code=200,
        message="创建模型成功",
        data=safe_model
    )


@router.put("/{model_id}", response_model=Response[LLMModelSafe])
def update_llm_model(
    model_id: str,
    model_data: LLMModelUpdate,
    db: Session = Depends(get_db)
):
    """更新LLM模型"""
    service = LLMModelService(db)
    model = service.update_model(model_id, model_data)
    
    if not model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="模型不存在"
        )
    
    # 转换为安全的Schema，移除敏感信息
    safe_model = LLMModelSafe(
        id=model.id,
        name=model.name,
        description=model.description,
        model_name=model.model_name,
        base_url=model.base_url,
        config=model.config,
        is_active=model.is_active,
        created_at=model.created_at,
        updated_at=model.updated_at
    )
    
    return Response(
        code=200,
        message="更新模型成功",
        data=safe_model
    )


@router.delete("/{model_id}", response_model=Response[bool])
def delete_llm_model(
    model_id: str,
    db: Session = Depends(get_db)
):
    """删除LLM模型"""
    service = LLMModelService(db)
    success = service.delete_model(model_id)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="模型不存在"
        )
    
    return Response(
        code=200,
        message="删除模型成功",
        data=True
    )








@router.post("/test-config", response_model=Response[LLMModelTestResult])
async def test_llm_model_config(
    config_data: LLMModelTestConfig,
    db: Session = Depends(get_db)
):
    """使用配置直接测试LLM模型（无需保存）"""
    service = LLMModelService(db)
    result = await service.test_model_with_config(config_data)
    
    return Response(
        code=200,
        message="模型配置测试完成",
        data=result
    )



