from typing import List, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Request
from sqlalchemy.orm import Session
from sqlalchemy import text
import time
import json

from app.db.session import get_db
from app.schemas import Tool, ToolCreate, ToolUpdate, ToolTest
from app.schemas.tool import convert_tool_type_case
from app.models.tool import Tool as ToolModel
from app.models.data_source import DataSource as DataSourceModel
from app.services.executor.factory import ExecutorFactory
from app.schemas.base import Response, PageData, PageInfo
from app.utils.json_utils import DecimalEncoder

router = APIRouter()

def convert_tool_type_case(tool_type: str) -> str:
    """
    统一处理工具类型的大小写，转换为小写
    
    Args:
        tool_type: 原始工具类型字符串
        
    Returns:
        转换后的小写工具类型
    """
    if not tool_type:
        return ""
        
    # 确保我们不丢失原始值，只是统一大小写
    return tool_type.lower()

@router.post("/", response_model=Response[Tool], summary="创建工具")
async def create_tool(
    tool_in: ToolCreate,
    request: Request,
    db: Session = Depends(get_db)
):
    """创建新工具"""
    try:
        # 获取项目关联的数据源
        from app.models.project import Project
        project = db.query(Project).filter(Project.id == tool_in.project_id).first()
        if not project:
            raise HTTPException(status_code=404, detail="项目不存在")
            
        # 获取项目关联的数据源
        data_source = db.query(DataSourceModel).filter(DataSourceModel.project_id == tool_in.project_id).first()
        if not data_source:
            raise HTTPException(status_code=404, detail="项目未关联数据源")
        
        # 确保工具类型大写
        tool_dict = tool_in.dict()
        # 将project_id替换为data_source_id
        tool_dict.pop("project_id")
        tool_dict["data_source_id"] = data_source.id
        
        if 'tool_type' in tool_dict and tool_dict['tool_type']:
            if hasattr(tool_dict['tool_type'], 'value'):
                tool_dict['tool_type'] = tool_dict['tool_type'].value.upper()
            elif isinstance(tool_dict['tool_type'], str):
                tool_dict['tool_type'] = tool_dict['tool_type'].upper()
        
        # 创建并保存工具
        tool = ToolModel(**tool_dict)
        db.add(tool)
        db.commit()
        db.refresh(tool)
        
        # 使用原始SQL查询获取创建的工具
        return await get_tool(tool.id, request, db)
    except Exception as e:
        db.rollback()
        print(f"创建工具时出错: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return Response(
            code=500,
            message=f"创建工具失败: {str(e)}",
            request_id=request.state.request_id
        )

@router.get("/", response_model=Response[PageData[Tool]], summary="获取工具列表")
async def get_tools(
    request: Request,
    data_source_id: str = Query(None, description="数据源ID"),
    project_id: str = Query(None, description="项目ID"),
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(10, ge=1, le=100, description="返回记录数"),
    db: Session = Depends(get_db)
):
    """获取工具列表"""
    try:
        print(f"获取工具列表，参数：data_source_id={data_source_id}, project_id={project_id}")

        # 使用原始SQL查询，绕过SQLAlchemy的枚举处理
        from sqlalchemy import text
        
        # 构建查询条件
        conditions = []
        params = {}
        
        if data_source_id:
            conditions.append("t.data_source_id = :data_source_id")
            params["data_source_id"] = data_source_id
            
        if project_id:
            conditions.append("ds.project_id = :project_id")
            params["project_id"] = project_id
        
        # 组合条件语句
        where_clause = " AND ".join(conditions)
        if where_clause:
            where_clause = "WHERE " + where_clause
            
        # 计算总数
        count_sql = f"""
        SELECT COUNT(*) AS total
        FROM tools t
        JOIN data_sources ds ON t.data_source_id = ds.id
        {where_clause}
        """
        
        result = db.execute(text(count_sql), params).first()
        total = result[0] if result else 0
        print(f"符合条件的工具总数：{total}")
        
        # 查询工具列表
        tools_sql = f"""
        SELECT 
            t.id, t.name, t.description, t.tool_type, t.template, 
            t.parameters, t.created_at, t.updated_at, t.data_source_id
        FROM tools t
        JOIN data_sources ds ON t.data_source_id = ds.id
        {where_clause}
        ORDER BY t.created_at DESC
        LIMIT :limit OFFSET :skip
        """
        
        params["limit"] = limit
        params["skip"] = skip
        
        tools_raw = db.execute(text(tools_sql), params).fetchall()
        
        # 手动创建Tool对象
        from app.models.tool import Tool as ToolModel, ToolType
        import json
        from datetime import datetime
        
        processed_tools = []
        for row in tools_raw:
            try:
                # 将row转换为字典
                row_dict = dict(row._mapping)
                
                # 处理类型 - 确保不丢失原始类型
                tool_type_raw = row_dict.get('tool_type', '')
                print(f"工具 {row_dict.get('name')} 的原始类型: {tool_type_raw}")
                
                # 只转换大小写，不更改原始值
                if tool_type_raw:
                    tool_type_str = convert_tool_type_case(tool_type_raw)
                    print(f"工具类型转换: {tool_type_raw} -> {tool_type_str}")
                else:
                    # 只有在真正为None或空时才使用custom
                    tool_type_str = "custom"
                    print(f"工具类型为空，使用默认值: {tool_type_str}")
                
                # 处理parameters
                parameters = row_dict.get('parameters', '[]')
                print(f"原始参数: {parameters}")
                
                # 确保parameters是有效的JSON格式
                if isinstance(parameters, str):
                    try:
                        parameters = json.loads(parameters)
                        print(f"解析后的参数: {parameters}")
                    except Exception as e:
                        print(f"参数解析失败: {str(e)}")
                        parameters = []
                
                # 如果parameters不是列表，将其转换为列表形式
                if not isinstance(parameters, list):
                    if isinstance(parameters, dict):
                        # 如果是字典，转换为参数列表
                        try:
                            param_list = []
                            for key, value in parameters.items():
                                if isinstance(value, dict):
                                    param_list.append({"name": key, **value})
                                else:
                                    param_list.append({
                                        "name": key,
                                        "type": "string",
                                        "description": str(value) if value else key,
                                        "required": False
                                    })
                            parameters = param_list
                            print(f"字典转换为参数列表: {parameters}")
                        except Exception as e:
                            print(f"转换参数失败: {str(e)}")
                            parameters = []
                    else:
                        print(f"参数不是列表或字典: {type(parameters)}")
                        parameters = []
                
                print(f"最终参数: {parameters}")
                
                # 创建API响应对象
                tool_data = {
                    "id": row_dict.get('id'),
                    "name": row_dict.get('name'),
                    "description": row_dict.get('description'),
                    "tool_type": tool_type_str,
                    "template": row_dict.get('template'),
                    "parameters": parameters,
                    "data_source_id": row_dict.get('data_source_id'),
                    "created_at": row_dict.get('created_at'),
                    "updated_at": row_dict.get('updated_at')
                }
                
                print(f"工具处理结果: {tool_data['name']}, 类型: {tool_data['tool_type']}")
                processed_tools.append(tool_data)
            except Exception as e:
                print(f"处理工具数据时出错: {str(e)}")
                import traceback
                print(traceback.format_exc())
        
        # 计算总页数
        total_page = (total + limit - 1) // limit if limit > 0 else 0
        
        # 创建分页信息
        page_info = PageInfo(
            page=skip // limit + 1,
            page_size=limit,
            total=total,
            total_page=total_page
        )
        
        # 创建分页数据
        page_data = PageData(
            items=processed_tools,
            page_info=page_info
        )
        
        # 打印返回数据结构
        print(f"返回工具列表: 共{len(processed_tools)}个工具")
        
        return Response(data=page_data, request_id=request.state.request_id)
    except Exception as e:
        print(f"获取工具列表时出错: {str(e)}")
        # 添加更详细的错误日志
        import traceback
        print(traceback.format_exc())
        return Response(
            code=500,
            message=f"获取工具列表失败: {str(e)}",
            request_id=request.state.request_id
        )

@router.get("/{tool_id}", response_model=Response[Tool], summary="获取工具详情")
async def get_tool(
    tool_id: str,
    request: Request,
    db: Session = Depends(get_db)
):
    """获取工具详情"""
    try:
        print(f"获取工具详情，ID: {tool_id}")
        
        # 添加详细日志
        import json
        import traceback
        
        # 直接使用ORM模型获取
        try:
            # 先尝试使用ORM方式
            tool_model = db.query(ToolModel).filter(ToolModel.id == tool_id).first()
            if tool_model:
                print(f"使用ORM查询获取到工具: {tool_model.name}")
                print(f"原始参数类型: {type(tool_model.parameters)}, 值: {tool_model.parameters}")
                print(f"原始工具类型: {tool_model.tool_type}")
                
                # 使用模型的to_api_model方法
                tool_data = tool_model.to_api_model()
                print(f"转换后的工具数据: {tool_data}")
                
                # 检查参数
                if 'parameters' in tool_data:
                    print(f"返回的参数: {tool_data['parameters']}")
                else:
                    print("返回的数据中没有parameters字段")
                
                return Response(data=tool_data, request_id=request.state.request_id)
        except Exception as e:
            print(f"ORM查询失败: {str(e)}")
            print(traceback.format_exc())
            # 失败后使用原始SQL查询作为备选
        
        # 使用原始SQL查询作为备选方案
        print("使用原始SQL查询作为备选方案")
        query = text(
            """
            SELECT t.id, t.name, t.description, t.tool_type, t.template, t.parameters, t.data_source_id, 
                   t.created_at, t.updated_at
            FROM tools t
            WHERE t.id = :tool_id
            """
        )
        
        # 执行查询
        result = db.execute(query, {"tool_id": tool_id})
        row = result.fetchone()
        
        if not row:
            raise HTTPException(status_code=404, detail="工具不存在")
        
        # 将row转换为字典
        row_dict = dict(row._mapping)
        print(f"原始SQL查询结果: {row_dict}")
        
        # 处理类型 - 确保不丢失原始类型
        tool_type_raw = row_dict.get('tool_type', '')
        print(f"工具 {row_dict.get('name')} 的原始类型: {tool_type_raw}")
        
        # 只转换大小写，不更改原始值
        if tool_type_raw:
            tool_type_str = convert_tool_type_case(tool_type_raw)
            print(f"工具类型转换: {tool_type_raw} -> {tool_type_str}")
        else:
            # 只有在真正为None或空时才使用custom
            tool_type_str = "custom"
            print(f"工具类型为空，使用默认值: {tool_type_str}")
        
        # 处理parameters
        parameters = row_dict.get('parameters', '[]')
        print(f"原始参数: {parameters}")
        
        # 确保parameters是有效的JSON格式
        if isinstance(parameters, str):
            try:
                parameters = json.loads(parameters)
                print(f"解析后的参数: {parameters}, 类型: {type(parameters)}")
            except Exception as e:
                print(f"参数解析失败: {str(e)}")
                print(f"参数内容: {parameters}")
                try:
                    # 尝试去掉转义字符后解析
                    clean_params = parameters.replace('\\', '')
                    parameters = json.loads(clean_params)
                    print(f"清理转义字符后解析成功: {parameters}")
                except Exception as e2:
                    print(f"二次参数解析失败: {str(e2)}")
                    parameters = []
        
        # 如果parameters不是列表，将其转换为列表形式
        if not isinstance(parameters, list):
            if isinstance(parameters, dict):
                # 如果是字典，转换为参数列表
                try:
                    param_list = []
                    for key, value in parameters.items():
                        if isinstance(value, dict):
                            param_list.append({"name": key, **value})
                        else:
                            param_list.append({
                                "name": key,
                                "type": "string",
                                "description": str(value) if value else key,
                                "required": False
                            })
                    parameters = param_list
                    print(f"字典转换为参数列表: {parameters}")
                except Exception as e:
                    print(f"转换参数失败: {str(e)}")
                    parameters = []
            else:
                print(f"参数不是列表或字典: {type(parameters)}")
                parameters = []
        
        print(f"最终参数: {parameters}")
        
        # 创建工具数据对象
        tool_data = {
            "id": row_dict.get('id'),
            "name": row_dict.get('name'),
            "description": row_dict.get('description'),
            "tool_type": tool_type_str,
            "template": row_dict.get('template'),
            "parameters": parameters,
            "data_source_id": row_dict.get('data_source_id'),
            "created_at": row_dict.get('created_at'),
            "updated_at": row_dict.get('updated_at')
        }
        
        print(f"返回工具详情: {tool_data}")
        return Response(data=tool_data, request_id=request.state.request_id)
    except HTTPException:
        raise
    except Exception as e:
        print(f"获取工具详情时出错: {str(e)}")
        # 添加更详细的错误日志
        import traceback
        print(traceback.format_exc())
        return Response(
            code=500,
            message=f"获取工具详情失败: {str(e)}",
            request_id=request.state.request_id
        )

@router.put("/{tool_id}", response_model=Response[Tool], summary="更新工具")
async def update_tool(
    tool_id: str,
    tool_in: ToolUpdate,
    request: Request,
    db: Session = Depends(get_db)
):
    """更新工具"""
    try:
        # 使用原始SQL查询先检查工具是否存在
        from sqlalchemy import text
        
        check_sql = "SELECT id FROM tools WHERE id = :tool_id"
        result = db.execute(text(check_sql), {"tool_id": tool_id}).first()
        
        if not result:
            raise HTTPException(status_code=404, detail="工具不存在")
        
        # 获取需要更新的字段
        update_data = tool_in.dict(exclude_unset=True)
        
        # 特别处理工具类型，确保大写
        if 'tool_type' in update_data and update_data['tool_type']:
            if hasattr(update_data['tool_type'], 'value'):
                update_data['tool_type'] = update_data['tool_type'].value.upper()
            elif isinstance(update_data['tool_type'], str):
                update_data['tool_type'] = update_data['tool_type'].upper()
        
        # 构建更新语句
        set_clauses = []
        params = {"tool_id": tool_id}
        
        for field, value in update_data.items():
            # 如果是字符串值（如JSON字符串），尝试解析为对象
            if isinstance(value, str) and (value.startswith('{') or value.startswith('[')):
                try:
                    value = json.loads(value)
                except:
                    pass  # 解析失败，保持原值
            # 如果是结构化数据，转为JSON字符串
            elif not isinstance(value, str) and not isinstance(value, (int, float, bool, type(None))):
                value = json.dumps(value, cls=DecimalEncoder)
            
            set_clauses.append(f"{field} = :{field}")
            params[field] = value
        
        if not set_clauses:
            # 如果没有需要更新的字段，直接返回工具详情
            return await get_tool(tool_id, request, db)
        
        # 构建并执行更新SQL
        update_sql = f"""
        UPDATE tools
        SET {', '.join(set_clauses)}
        WHERE id = :tool_id
        """
        
        db.execute(text(update_sql), params)
        db.commit()
        
        # 返回更新后的工具详情
        return await get_tool(tool_id, request, db)
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        print(f"更新工具时出错: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return Response(
            code=500,
            message=f"更新工具失败: {str(e)}",
            request_id=request.state.request_id
        )

@router.delete("/{tool_id}", response_model=Response, summary="删除工具")
async def delete_tool(
    tool_id: str,
    request: Request,
    db: Session = Depends(get_db)
):
    """删除工具"""
    try:
        # 使用原始SQL查询先检查工具是否存在
        from sqlalchemy import text
        
        check_sql = "SELECT id FROM tools WHERE id = :tool_id"
        result = db.execute(text(check_sql), {"tool_id": tool_id}).first()
        
        if not result:
            raise HTTPException(status_code=404, detail="工具不存在")
        
        # 执行删除操作
        delete_sql = "DELETE FROM tools WHERE id = :tool_id"
        db.execute(text(delete_sql), {"tool_id": tool_id})
        db.commit()
        
        return Response(message="工具删除成功", request_id=request.state.request_id)
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        print(f"删除工具时出错: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return Response(
            code=500,
            message=f"删除工具失败: {str(e)}",
            request_id=request.state.request_id
        )

@router.post("/{tool_id}/test", response_model=Response, summary="测试工具")
async def test_tool(
    tool_id: str,
    test_data: ToolTest,
    request: Request,
    db: Session = Depends(get_db)
):
    """测试工具"""
    try:
        print(f"测试工具: {tool_id}")
        
        # 获取工具信息
        tool = db.query(ToolModel).filter(ToolModel.id == tool_id).first()
        if not tool:
            return Response(
                code=404,
                message="工具不存在",
                request_id=request.state.request_id
            )
        
        print(f"找到工具: {tool.name}, 类型: {tool.tool_type}")
        
        # 获取数据源信息
        data_source = db.query(DataSourceModel).filter(DataSourceModel.id == tool.data_source_id).first()
        if not data_source:
            return Response(
                code=404,
                message="工具关联的数据源不存在",
                request_id=request.state.request_id
            )
        
        print(f"数据源信息: {data_source.name}, 类型: {data_source.type}, 连接参数: {data_source.config}")
        
        # 获取执行器
        try:
            # 使用普通执行器，不使用格式化功能
            executor = ExecutorFactory.get_executor(tool.tool_type)
            print(f"获取到执行器: {executor.__class__.__name__}")
        except Exception as e:
            print(f"获取执行器失败: {str(e)}")
            return Response(
                code=500,
                message=f"不支持的工具类型: {tool.tool_type}",
                request_id=request.state.request_id
            )
        
        # 执行测试
        try:
            start_time = time.time()
            
            # 添加详细日志
            import json
            print(f"执行参数: {test_data.parameters}")
            
            # 转换参数
            parameters = test_data.parameters
            if not isinstance(parameters, dict):
                try:
                    parameters = json.loads(parameters)
                except:
                    parameters = {}
            
            # 构建工具配置
            tool_config = {
                "type": data_source.type,
                "config": data_source.config
            }
            
            # 执行工具
            result = await executor.execute(tool.template, parameters, tool_config)
            
            end_time = time.time()
            
            # 处理LOB对象，确保可以JSON序列化
            result = convert_lob_objects(result)
            
            # 添加执行时间
            result["execution_time"] = end_time - start_time
            
            print(f"工具执行完成，耗时: {end_time - start_time:.2f}秒")
            print(f"数据类型: {type(result)}")
            print(f"数据键: {list(result.keys()) if isinstance(result, dict) else '非字典类型'}")
            
            # 不再引用display_format字段
            result["display_format"] = ""
            
            return Response(
                data=result,
                request_id=request.state.request_id
            )
        except Exception as e:
            import traceback
            # 记录完整错误到日志，但不返回给用户
            full_error = f"工具执行出错: {str(e)}"
            print(full_error)
            print(traceback.format_exc())
            
            # 只返回简化的错误信息给用户
            return Response(
                code=500,
                message=f"工具执行失败: {str(e)}",
                data={
                    "error": str(e)
                    # 移除traceback字段，不返回堆栈信息
                },
                request_id=request.state.request_id
            )
    except Exception as e:
        import traceback
        print(f"测试工具接口出错: {str(e)}")
        print(traceback.format_exc())
        
        return Response(
            code=500,
            message=f"测试工具失败: {str(e)}",
            request_id=request.state.request_id
        )

def convert_lob_objects(data):
    """
    递归处理数据，将cx_Oracle.LOB对象转换为字符串
    """
    import cx_Oracle
    
    if data is None:
        return None
    elif isinstance(data, cx_Oracle.LOB):
        try:
            # 读取LOB对象内容
            if isinstance(data, cx_Oracle.CLOB):
                return data.read()
            elif isinstance(data, cx_Oracle.BLOB):
                # 对于BLOB，先转为bytes，然后尝试解码为字符串
                # 如果无法解码，则返回bytes的十六进制表示
                blob_data = data.read()
                try:
                    return blob_data.decode('utf-8')
                except UnicodeDecodeError:
                    return blob_data.hex()
            else:
                # 其他LOB类型，尝试直接读取
                return str(data.read())
        except Exception as e:
            print(f"读取LOB对象失败: {str(e)}")
            # 返回空字符串而不是引发错误
            return ""
    elif isinstance(data, dict):
        return {k: convert_lob_objects(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [convert_lob_objects(item) for item in data]
    elif hasattr(data, '__dict__'):  # 处理自定义对象
        try:
            return convert_lob_objects(data.__dict__)
        except:
            return str(data)
    else:
        return data 