"""
分布式上下文缓存模块
=================
基于Redis的分布式上下文缓存，支持多进程共享和高并发访问
支持多轮澄清历史追踪和完整上下文恢复
"""

import asyncio
import json
import pickle
import redis
from typing import Dict, Optional, Any, List
from dataclasses import asdict
from datetime import datetime, timedelta
from app.utils.time_utils import get_shanghai_time
import logging

from app.core.logger import log
from app.db.redis import RedisClient

# 设置日志
logger = logging.getLogger(__name__)


class DistributedAnalysisContextStore:
    """分布式分析上下文存储器
    
    使用Redis缓存 ExecutionContext，支持多进程/多服务器部署
    增强支持多轮澄清历史追踪
    """
    
    def __init__(self):
        self.redis_client = RedisClient
        self.context_prefix = "analysis_context:"
        self.access_time_prefix = "analysis_access:"
        self.clarification_prefix = "analysis_clarification:"
        self.stats_key = "analysis_context_stats"
        self.default_ttl = 7200  # 2小时过期时间（秒）
        self.cleanup_interval = 3600  # 清理间隔（秒）
        
        logger.info("DistributedAnalysisContextStore 初始化完成")
    
    def _get_context_key(self, analysis_id: str) -> str:
        """获取上下文在Redis中的键名"""
        return f"{self.context_prefix}{analysis_id}"
    
    def _get_access_time_key(self, analysis_id: str) -> str:
        """获取访问时间在Redis中的键名"""
        return f"{self.access_time_prefix}{analysis_id}"
    
    def _get_clarification_key(self, analysis_id: str) -> str:
        """获取澄清历史在Redis中的键名"""
        return f"{self.clarification_prefix}{analysis_id}"
    
    async def set(self, analysis_id: str, context: Any) -> bool:
        """设置分析上下文
        
        Args:
            analysis_id: 分析ID
            context: ExecutionContext 对象
            
        Returns:
            bool: 是否设置成功
        """
        try:
            # 将 ExecutionContext 转换为可序列化的字典
            if hasattr(context, '__dict__'):
                context_dict = asdict(context) if hasattr(context, '__dataclass_fields__') else context.__dict__
            else:
                context_dict = context
            
            # 序列化上下文数据
            serialized_context = json.dumps(context_dict, ensure_ascii=False, default=str)
            
            # 存储到Redis
            context_key = self._get_context_key(analysis_id)
            access_time_key = self._get_access_time_key(analysis_id)
            
            # 使用pipeline批量操作
            success = True
            try:
                # 设置上下文数据
                self.redis_client.set_value(context_key, serialized_context, ex=self.default_ttl)
                
                # 设置访问时间
                self.redis_client.set_value(access_time_key, get_shanghai_time().isoformat(), ex=self.default_ttl)
                
                # 初始化澄清历史（如果不存在）
                clarification_key = self._get_clarification_key(analysis_id)
                if not self.redis_client.exists(clarification_key):
                    self.redis_client.set_value(clarification_key, json.dumps([]), ex=self.default_ttl)
                
                logger.debug(f"上下文已缓存到Redis [分析ID: {analysis_id}]")
                
            except Exception as e:
                logger.error(f"Redis操作失败: {str(e)}")
                success = False
            
            return success
                
        except Exception as e:
            logger.error(f"设置分析上下文失败 [分析ID: {analysis_id}]: {str(e)}")
            return False
    
    async def add_clarification_round(self, analysis_id: str, clarification_data: Dict[str, Any]) -> bool:
        """添加澄清轮次记录
        
        Args:
            analysis_id: 分析ID
            clarification_data: 澄清数据，包含原始查询、澄清问题、用户回复等
            
        Returns:
            bool: 是否添加成功
        """
        try:
            clarification_key = self._get_clarification_key(analysis_id)
            
            # 获取当前澄清历史
            current_history_json = self.redis_client.get_value(clarification_key) or "[]"
            current_history = json.loads(current_history_json)
            
            # 获取当前上下文中的待处理澄清问题
            context_key = self._get_context_key(analysis_id)
            context_json = self.redis_client.get_value(context_key)
            
            if context_json:
                current_context = json.loads(context_json)
                pending_clarification = current_context.get('pending_clarification')
                
                # 如果有待处理的澄清问题，将其与用户回复关联
                if pending_clarification and 'user_response' in clarification_data:
                    clarification_questions = pending_clarification.get('clarification_questions', [])
                    clarification_questions_detail = pending_clarification.get('clarification_questions_detail', [])
                    clarification_context = pending_clarification.get('context', {})
                    
                    logger.debug(f"关联待处理澄清问题到用户回复 [分析ID: {analysis_id}]")
                    
                    # 更新澄清数据
                    enhanced_clarification_data = {
                        **clarification_data,
                        "clarification_questions": clarification_questions,
                        "clarification_questions_detail": clarification_questions_detail,
                        "clarification_context": clarification_context,
                        "question_response_paired": True,
                        "pending_clarification_timestamp": pending_clarification.get('timestamp')
                    }
                    clarification_data = enhanced_clarification_data
            
            clarification_round = {
                "round_number": len(current_history) + 1,
                "timestamp": get_shanghai_time().isoformat(),
                **clarification_data
            }
            
            current_history.append(clarification_round)
            
            # 保存更新后的澄清历史
            updated_history_json = json.dumps(current_history, ensure_ascii=False)
            self.redis_client.set_value(clarification_key, updated_history_json, ex=self.default_ttl)
            
            logger.debug(f"添加澄清轮次到Redis [分析ID: {analysis_id}, 轮次: {clarification_round['round_number']}]")
            
            return True
                
        except Exception as e:
            logger.error(f"添加澄清轮次失败 [分析ID: {analysis_id}]: {str(e)}")
            return False
    
    async def get_clarification_history(self, analysis_id: str) -> List[Dict[str, Any]]:
        """获取澄清历史
        
        Args:
            analysis_id: 分析ID
            
        Returns:
            List[Dict[str, Any]]: 澄清历史列表
        """
        try:
            clarification_key = self._get_clarification_key(analysis_id)
            history_json = self.redis_client.get_value(clarification_key)
            
            if history_json:
                return json.loads(history_json)
            else:
                return []
                
        except Exception as e:
            logger.error(f"获取澄清历史失败 [分析ID: {analysis_id}]: {str(e)}")
            return []
    
    async def get_full_context(self, analysis_id: str) -> Optional[Dict[str, Any]]:
        """获取完整的分析上下文，包括澄清历史
        
        Args:
            analysis_id: 分析ID
            
        Returns:
            Optional[Dict[str, Any]]: 完整上下文或 None
        """
        try:
            context_key = self._get_context_key(analysis_id)
            context_json = self.redis_client.get_value(context_key)
            
            if not context_json:
                logger.debug(f"完整上下文未找到 [分析ID: {analysis_id}]")
                return None
            
            # 更新访问时间
            access_time_key = self._get_access_time_key(analysis_id)
            self.redis_client.set_value(access_time_key, get_shanghai_time().isoformat(), ex=self.default_ttl)
            
            # 解析上下文
            context_dict = json.loads(context_json)
            
            # 获取澄清历史
            clarification_history = await self.get_clarification_history(analysis_id)
            context_dict['clarification_history'] = clarification_history
            
            logger.debug(f"从Redis获取完整上下文 [分析ID: {analysis_id}]")
            return context_dict
                
        except Exception as e:
            logger.error(f"获取完整上下文失败 [分析ID: {analysis_id}]: {str(e)}")
            return None
    
    async def get(self, analysis_id: str) -> Optional[Any]:
        """获取分析上下文（保持向后兼容）
        
        Args:
            analysis_id: 分析ID
            
        Returns:
            Optional[Any]: ExecutionContext 字典或 None
        """
        return await self.get_full_context(analysis_id)
    
    async def remove(self, analysis_id: str) -> bool:
        """移除分析上下文
        
        Args:
            analysis_id: 分析ID
            
        Returns:
            bool: 是否移除成功
        """
        try:
            context_key = self._get_context_key(analysis_id)
            access_time_key = self._get_access_time_key(analysis_id)
            clarification_key = self._get_clarification_key(analysis_id)
            
            # 删除相关的所有键
            removed_count = 0
            for key in [context_key, access_time_key, clarification_key]:
                if self.redis_client.delete(key):
                    removed_count += 1
            
            if removed_count > 0:
                logger.debug(f"上下文已从Redis移除 [分析ID: {analysis_id}, 删除键数: {removed_count}]")
                return True
            else:
                logger.debug(f"上下文不存在，无需移除 [分析ID: {analysis_id}]")
                return False
                
        except Exception as e:
            logger.error(f"移除分析上下文失败 [分析ID: {analysis_id}]: {str(e)}")
            return False
    
    async def exists(self, analysis_id: str) -> bool:
        """检查分析上下文是否存在
        
        Args:
            analysis_id: 分析ID
            
        Returns:
            bool: 是否存在
        """
        try:
            context_key = self._get_context_key(analysis_id)
            return self.redis_client.exists(context_key)
        except Exception as e:
            logger.error(f"检查分析上下文存在性失败 [分析ID: {analysis_id}]: {str(e)}")
            return False
    
    async def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 获取所有上下文键
            context_pattern = f"{self.context_prefix}*"
            context_keys = self.redis_client.scan_keys(context_pattern)
            
            stats = {
                "total_contexts": len(context_keys),
                "context_ids": [],
                "oldest_access": None,
                "newest_access": None,
                "clarification_stats": {},
                "redis_info": {
                    "connected": True,
                    "memory_usage": "N/A"  # 可以扩展获取Redis内存使用情况
                }
            }
            
            access_times = []
            
            for context_key in context_keys:
                # 提取分析ID
                analysis_id = context_key.replace(self.context_prefix, "")
                stats["context_ids"].append(analysis_id)
                
                # 获取访问时间
                access_time_key = self._get_access_time_key(analysis_id)
                access_time_str = self.redis_client.get_value(access_time_key)
                if access_time_str:
                    access_times.append(access_time_str)
                
                # 获取澄清统计
                clarification_history = await self.get_clarification_history(analysis_id)
                if clarification_history:
                    stats["clarification_stats"][analysis_id] = {
                        "total_rounds": len(clarification_history),
                        "last_clarification": clarification_history[-1]["timestamp"] if clarification_history else None
                    }
            
            if access_times:
                stats["oldest_access"] = min(access_times)
                stats["newest_access"] = max(access_times)
            
            return stats
                
        except Exception as e:
            logger.error(f"获取缓存统计信息失败: {str(e)}")
            return {
                "error": str(e),
                "redis_info": {
                    "connected": False,
                    "error": str(e)
                }
            }
    
    async def clear_all(self) -> bool:
        """清空所有缓存的上下文
        
        Returns:
            bool: 是否清空成功
        """
        try:
            # 删除所有相关的键
            patterns = [
                f"{self.context_prefix}*",
                f"{self.access_time_prefix}*", 
                f"{self.clarification_prefix}*"
            ]
            
            total_deleted = 0
            for pattern in patterns:
                keys = self.redis_client.scan_keys(pattern)
                for key in keys:
                    if self.redis_client.delete(key):
                        total_deleted += 1
            
            logger.info(f"已清空所有缓存上下文，共删除 {total_deleted} 个键")
            return True
                
        except Exception as e:
            logger.error(f"清空缓存失败: {str(e)}")
            return False


# 创建全局分布式上下文存储实例
context_store = DistributedAnalysisContextStore() 