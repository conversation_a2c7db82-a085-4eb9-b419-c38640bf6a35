#!/usr/bin/env python3
"""
分析生命周期日志记录器
详细记录分析的每一步信息，包括提示词、LLM返回结果、评估过程等
"""

import json
import os
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List
from pathlib import Path

try:
    from app.utils.time_utils import get_shanghai_time
except ImportError:
    # 降级方案：使用标准datetime
    from datetime import datetime
    def get_shanghai_time():
        return datetime.now()

class AnalysisLifecycleLogger:
    """分析生命周期日志记录器"""
    
    def __init__(self, analysis_id: str, project_id: str = None):
        self.analysis_id = analysis_id
        self.project_id = project_id
        self.log_file_path = self._create_log_file()
        self.session_data = {
            "analysis_id": analysis_id,
            "project_id": project_id,
            "start_time": get_shanghai_time().isoformat(),
            "steps": [],
            "metadata": {}
        }
        
        # 初始化日志文件
        self._write_session_header()
    
    def _create_log_file(self) -> str:
        """创建日志文件路径"""
        # 获取当前日期
        current_date = get_shanghai_time().strftime("%Y-%m-%d")

        # 创建按日期分组的日志目录
        log_dir = Path("logs/analysis_lifecycle") / current_date
        log_dir.mkdir(parents=True, exist_ok=True)

        # 生成日志文件名：analysis_id.log (每个会话一个文件)
        log_filename = f"{self.analysis_id}.log"

        return str(log_dir / log_filename)
    
    def _write_session_header(self):
        """写入会话头部信息"""
        header = f"""
{'='*80}
分析生命周期日志
{'='*80}
分析ID: {self.analysis_id}
项目ID: {self.project_id}
开始时间: {self.session_data['start_time']}
日志文件: {self.log_file_path}
{'='*80}

"""
        self._write_to_file(header)
    
    def _write_to_file(self, content: str):
        """写入内容到日志文件"""
        try:
            with open(self.log_file_path, 'a', encoding='utf-8') as f:
                f.write(content)
                f.flush()  # 立即刷新到磁盘
        except Exception as e:
            logging.error(f"写入分析生命周期日志失败: {str(e)}")
    
    def log_step_start(self, step_type: str, step_id: str, description: str, **kwargs):
        """记录步骤开始"""
        timestamp = get_shanghai_time().isoformat()
        
        step_data = {
            "step_id": step_id,
            "step_type": step_type,
            "description": description,
            "start_time": timestamp,
            "status": "started",
            "details": kwargs
        }
        
        self.session_data["steps"].append(step_data)
        
        content = f"""
[{timestamp}] 🚀 步骤开始
步骤ID: {step_id}
步骤类型: {step_type}
描述: {description}
详细信息: {json.dumps(kwargs, ensure_ascii=False, indent=2)}
{'-'*60}
"""
        self._write_to_file(content)
    
    def log_llm_call(self, step_id: str, call_type: str, user_prompt: str, 
                     system_prompt: str, llm_response: Any, usage_data: Dict = None,
                     temperature: float = None, **kwargs):
        """记录LLM调用详情"""
        timestamp = get_shanghai_time().isoformat()
        
        # 更新步骤数据
        for step in self.session_data["steps"]:
            if step["step_id"] == step_id:
                if "llm_calls" not in step:
                    step["llm_calls"] = []
                step["llm_calls"].append({
                    "call_type": call_type,
                    "timestamp": timestamp,
                    "user_prompt": user_prompt[:500] + "..." if len(user_prompt) > 500 else user_prompt,
                    "system_prompt": system_prompt[:500] + "..." if len(system_prompt) > 500 else system_prompt,
                    "response": str(llm_response)[:1000] + "..." if len(str(llm_response)) > 1000 else str(llm_response),
                    "usage_data": usage_data,
                    "temperature": temperature
                })
                break
        
        content = f"""
[{timestamp}] 🤖 LLM调用 - {call_type}
步骤ID: {step_id}
温度: {temperature}

📝 用户提示词:
{user_prompt}

🔧 系统提示词:
{system_prompt}

💬 LLM响应:
{json.dumps(llm_response, ensure_ascii=False, indent=2)}

📊 使用统计:
{json.dumps(usage_data, ensure_ascii=False, indent=2) if usage_data else "无"}

其他参数: {json.dumps(kwargs, ensure_ascii=False, indent=2)}
{'-'*60}
"""
        self._write_to_file(content)
    
    def log_tool_execution(self, step_id: str, tool_name: str, parameters: Dict,
                          result: Any, success: bool, execution_time: float = None,
                          error_message: str = None):
        """记录工具执行"""
        timestamp = get_shanghai_time().isoformat()
        
        # 更新步骤数据
        for step in self.session_data["steps"]:
            if step["step_id"] == step_id:
                step["tool_execution"] = {
                    "tool_name": tool_name,
                    "parameters": parameters,
                    "result": str(result)[:1000] + "..." if len(str(result)) > 1000 else str(result),
                    "success": success,
                    "execution_time": execution_time,
                    "error_message": error_message,
                    "timestamp": timestamp
                }
                break
        
        status_icon = "✅" if success else "❌"
        content = f"""
[{timestamp}] {status_icon} 工具执行 - {tool_name}
步骤ID: {step_id}
执行时间: {execution_time}ms

🔧 执行参数:
{json.dumps(parameters, ensure_ascii=False, indent=2)}

📊 执行结果:
{json.dumps(result, ensure_ascii=False, indent=2)}

状态: {"成功" if success else "失败"}
{f"错误信息: {error_message}" if error_message else ""}
{'-'*60}
"""
        self._write_to_file(content)
    
    def log_evaluation(self, step_id: str, evaluator_type: str, evaluation_input: Dict,
                      evaluation_result: Dict, reasoning: str = None):
        """记录评估过程"""
        timestamp = get_shanghai_time().isoformat()
        
        # 更新步骤数据
        for step in self.session_data["steps"]:
            if step["step_id"] == step_id:
                step["evaluation"] = {
                    "evaluator_type": evaluator_type,
                    "input": evaluation_input,
                    "result": evaluation_result,
                    "reasoning": reasoning,
                    "timestamp": timestamp
                }
                break
        
        content = f"""
[{timestamp}] 🧠 智能评估 - {evaluator_type}
步骤ID: {step_id}

📥 评估输入:
{json.dumps(evaluation_input, ensure_ascii=False, indent=2)}

🎯 评估结果:
{json.dumps(evaluation_result, ensure_ascii=False, indent=2)}

💭 评估推理:
{reasoning or "无"}
{'-'*60}
"""
        self._write_to_file(content)
    
    def log_planning(self, round_number: int, planning_prompt: str, planning_result: Dict,
                    context_info: Dict = None):
        """记录规划过程（包含完整的LLM调用信息）"""
        timestamp = get_shanghai_time().isoformat()

        # 从context_info中提取LLM调用相关信息
        system_prompt = context_info.get('system_prompt', '') if context_info else ''
        usage_data = context_info.get('usage_data', {}) if context_info else {}
        temperature = context_info.get('temperature', 0.2) if context_info else 0.2

        # 添加规划记录到会话数据
        if "planning_rounds" not in self.session_data:
            self.session_data["planning_rounds"] = []

        self.session_data["planning_rounds"].append({
            "round": round_number,
            "timestamp": timestamp,
            "user_prompt": planning_prompt[:500] + "..." if len(planning_prompt) > 500 else planning_prompt,
            "system_prompt": system_prompt[:500] + "..." if len(system_prompt) > 500 else system_prompt,
            "result": planning_result,
            "usage_data": usage_data,
            "temperature": temperature,
            "context": context_info
        })

        content = f"""
[{timestamp}] 📋 规划轮次 #{round_number}

🎯 规划用户提示词:
{planning_prompt}

� 规划系统提示词:
{system_prompt}

�📊 规划结果:
{json.dumps(planning_result, ensure_ascii=False, indent=2)}

📈 使用统计:
{json.dumps(usage_data, ensure_ascii=False, indent=2) if usage_data else "无"}

🔍 上下文信息:
{json.dumps(context_info, ensure_ascii=False, indent=2) if context_info else "无"}
{'-'*60}
"""
        self._write_to_file(content)

    def log_analysis_result(self, step_id: str, analysis_type: str, insights_count: int = 0,
                           should_continue: bool = True, completion_confidence: float = 0.0,
                           evaluation_conclusion: str = "", next_analysis_stage: str = "",
                           specific_guidance: str = ""):
        """记录分析结果"""
        timestamp = get_shanghai_time().isoformat()

        # 更新步骤数据
        for step in self.session_data["steps"]:
            if step["step_id"] == step_id:
                step["analysis_result"] = {
                    "analysis_type": analysis_type,
                    "insights_count": insights_count,
                    "should_continue": should_continue,
                    "completion_confidence": completion_confidence,
                    "evaluation_conclusion": evaluation_conclusion,
                    "next_analysis_stage": next_analysis_stage,
                    "specific_guidance": specific_guidance,
                    "timestamp": timestamp
                }
                break

        content = f"""
[{timestamp}] 📊 分析结果 - {analysis_type}
步骤ID: {step_id}

🔍 分析统计:
- 洞察数量: {insights_count}
- 是否继续: {should_continue}
- 完成度: {completion_confidence:.2f}
- 下一阶段: {next_analysis_stage}

💡 评估结论:
{evaluation_conclusion}

🎯 具体指导:
{specific_guidance}
{'-'*60}
"""
        self._write_to_file(content)

    def log_step_complete(self, step_id: str, final_result: Any = None, insights: List = None):
        """记录步骤完成"""
        timestamp = get_shanghai_time().isoformat()
        
        # 更新步骤状态
        for step in self.session_data["steps"]:
            if step["step_id"] == step_id:
                step["end_time"] = timestamp
                step["status"] = "completed"
                step["final_result"] = str(final_result)[:500] + "..." if final_result and len(str(final_result)) > 500 else str(final_result)
                step["insights"] = insights
                break
        
        content = f"""
[{timestamp}] ✅ 步骤完成
步骤ID: {step_id}

🎯 最终结果:
{json.dumps(final_result, ensure_ascii=False, indent=2) if final_result else "无"}

💡 发现洞察:
{json.dumps(insights, ensure_ascii=False, indent=2) if insights else "无"}
{'-'*60}
"""
        self._write_to_file(content)
    
    def log_error(self, step_id: str, error_type: str, error_message: str, 
                  stack_trace: str = None, context: Dict = None):
        """记录错误信息"""
        timestamp = get_shanghai_time().isoformat()
        
        # 更新步骤状态
        for step in self.session_data["steps"]:
            if step["step_id"] == step_id:
                step["status"] = "error"
                step["error"] = {
                    "type": error_type,
                    "message": error_message,
                    "stack_trace": stack_trace,
                    "context": context,
                    "timestamp": timestamp
                }
                break
        
        content = f"""
[{timestamp}] ❌ 错误发生
步骤ID: {step_id}
错误类型: {error_type}

错误信息: {error_message}

堆栈跟踪:
{stack_trace or "无"}

错误上下文:
{json.dumps(context, ensure_ascii=False, indent=2) if context else "无"}
{'-'*60}
"""
        self._write_to_file(content)
    
    def log_session_end(self, final_status: str, summary: Dict = None):
        """记录会话结束"""
        timestamp = get_shanghai_time().isoformat()
        
        self.session_data["end_time"] = timestamp
        self.session_data["final_status"] = final_status
        self.session_data["summary"] = summary
        
        # 计算统计信息
        total_steps = len(self.session_data["steps"])
        completed_steps = len([s for s in self.session_data["steps"] if s["status"] == "completed"])
        error_steps = len([s for s in self.session_data["steps"] if s["status"] == "error"])
        
        content = f"""
[{timestamp}] 🏁 分析会话结束
最终状态: {final_status}

📊 执行统计:
- 总步骤数: {total_steps}
- 完成步骤: {completed_steps}
- 错误步骤: {error_steps}
- 成功率: {(completed_steps/total_steps*100):.1f}% if total_steps > 0 else 0%

📋 会话摘要:
{json.dumps(summary, ensure_ascii=False, indent=2) if summary else "无"}

💾 完整会话数据:
{json.dumps(self.session_data, ensure_ascii=False, indent=2)}

{'='*80}
分析生命周期日志结束
{'='*80}
"""
        self._write_to_file(content)
    
    def get_log_file_path(self) -> str:
        """获取日志文件路径"""
        return self.log_file_path
    
    def get_session_summary(self) -> Dict[str, Any]:
        """获取会话摘要"""
        return {
            "analysis_id": self.analysis_id,
            "project_id": self.project_id,
            "log_file": self.log_file_path,
            "total_steps": len(self.session_data["steps"]),
            "completed_steps": len([s for s in self.session_data["steps"] if s["status"] == "completed"]),
            "error_steps": len([s for s in self.session_data["steps"] if s["status"] == "error"]),
            "planning_rounds": len(self.session_data.get("planning_rounds", [])),
            "start_time": self.session_data["start_time"],
            "end_time": self.session_data.get("end_time"),
            "final_status": self.session_data.get("final_status")
        }
