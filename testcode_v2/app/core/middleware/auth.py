import uuid
from typing import Callable, List, Optional

from fastapi import Depends, FastAPI, HTTPException, Request, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from starlette.types import ASGIApp, Receive, Scope, Send
from sqlalchemy.orm import Session

from app.core.exceptions import UnauthorizedException
from app.core.security import decode_token
from app.db.session import get_db
from app.models.user import User
from app.services.user import get_user_by_id
from app.services.token_service import TokenService


security = HTTPBearer(auto_error=False)


class JWTAuthMiddleware:
    """
    JWT认证中间件
    """
    def __init__(
        self, 
        app: ASGIApp,
        exclude_paths: Optional[List[str]] = None,
    ):
        self.app = app
        self.exclude_paths = exclude_paths or []
        
    async def __call__(self, scope: Scope, receive: Receive, send: Send):
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return

        request = Request(scope)

        # 跳过OPTIONS请求（CORS预检请求）
        if request.method == "OPTIONS":
            await self.app(scope, receive, send)
            return

        # 排除某些路径，如登录、注册等
        for path in self.exclude_paths:
            if request.url.path.startswith(path):
                await self.app(scope, receive, send)
                return
        
        # 验证JWT令牌
        token = self._get_token_from_request(request)
        
        # 如果没有令牌，继续处理请求，由依赖函数进行细粒度的权限控制
        if not token:
            await self.app(scope, receive, send)
            return
        
        # 验证令牌
        try:
            # 1. 先验证JWT格式和签名
            payload = decode_token(token)
            
            # 2. 验证token是否在Redis中存在（防止已被撤销的token）
            if not TokenService.is_token_valid(token):
                pass  # token已被撤销，不设置用户信息
            else:
                request.state.user = payload
                request.state.user_id = payload.get("sub")
        except UnauthorizedException:
            pass  # 令牌无效，不设置用户信息，由依赖函数处理
        
        await self.app(scope, receive, send)
    
    def _get_token_from_request(self, request: Request) -> Optional[str]:
        """
        从请求中获取令牌
        """
        auth_header = request.headers.get("Authorization")
        if not auth_header:
            return None
        
        parts = auth_header.split()
        if len(parts) != 2 or parts[0].lower() != "bearer":
            return None
        
        return parts[1]


def setup_auth_middleware(
    app: FastAPI, 
    exclude_paths: Optional[List[str]] = None
) -> None:
    """
    设置认证中间件
    """
    app.add_middleware(
        JWTAuthMiddleware,
        exclude_paths=exclude_paths or ["/api/v1/auth", "/docs", "/redoc", "/openapi.json"],
    )


# 用于依赖注入的认证函数
async def get_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: Session = Depends(get_db)
):
    """
    获取当前用户JWT信息，包含用户状态验证
    可以作为依赖注入使用，例如：
    
    @router.get("/users/me")
    async def get_user_me(current_user = Depends(get_current_user)):
        return current_user
    """
    if not credentials:
        raise UnauthorizedException("未提供认证凭证")
    
    try:
        # 1. 验证JWT格式和签名
        payload = decode_token(credentials.credentials)
        
        # 2. 验证token是否在Redis中存在
        if not TokenService.is_token_valid(credentials.credentials):
            raise UnauthorizedException("Token已失效，请重新登录")
        
        # 3. 验证用户是否仍然有效（检查用户状态）
        user_id = int(payload.get("sub"))
        user = get_user_by_id(db, user_id)
        if not user:
            raise UnauthorizedException("用户不存在")
        
        if not user.is_active:
            raise UnauthorizedException("账户已被禁用")
        
        return payload
    except ValueError:
        raise UnauthorizedException("无效的用户ID")
    except UnauthorizedException as e:
        raise e
    except Exception as e:
        raise UnauthorizedException(str(e))


async def get_current_user_with_permissions(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """
    获取当前用户完整信息（包含权限信息）
    用于需要权限控制的接口
    
    Returns:
        User: 完整的用户模型对象
    """
    if not credentials:
        raise UnauthorizedException("未提供认证凭证")
    
    try:
        # 1. 验证JWT格式和签名
        payload = decode_token(credentials.credentials)
        
        # 2. 验证token是否在Redis中存在
        if not TokenService.is_token_valid(credentials.credentials):
            raise UnauthorizedException("Token已失效，请重新登录")
        
        user_id = int(payload.get("sub"))
        
        user = get_user_by_id(db, user_id)
        if not user:
            raise UnauthorizedException("用户不存在")
        
        if not user.is_active:
            raise UnauthorizedException("用户已被禁用")
            
        return user
    except ValueError:
        raise UnauthorizedException("无效的用户ID")
    except UnauthorizedException as e:
        raise e
    except Exception as e:
        raise UnauthorizedException(str(e))
