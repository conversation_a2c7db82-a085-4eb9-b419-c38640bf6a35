import json
import time
import uuid
from typing import Callable, Dict, Optional

from fastapi import FastAPI, Request, Response
from starlette.types import ASGIApp, Receive, Scope, Send

from app.core.logger import log


class RequestLoggingMiddleware:
    """
    请求日志中间件
    记录请求和响应信息
    """
    def __init__(self, app: ASGIApp):
        self.app = app
    
    async def __call__(self, scope: Scope, receive: Receive, send: Send):
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return
            
        request = Request(scope)
        
        # 为每个请求生成唯一ID
        request_id = request.headers.get("X-Request-ID") or str(uuid.uuid4())
        
        # 添加请求ID到请求对象中
        request.state.request_id = request_id
        
        # 设置请求ID到日志对象
        log.set_request_id(request_id)
        
        # 记录请求开始时间
        start_time = time.time()
        
        # 获取请求信息（不读取请求体）
        request_info = self._get_basic_request_info(request)
        
        # 记录请求日志
        log.info(
            f"请求 {request.method} {request.url.path}",
            request_info
        )
        
        # 创建一个自定义的receive函数来捕获请求体
        body_chunks = []
        
        async def receive_wrapper():
            message = await receive()
            if message["type"] == "http.request":
                body_chunks.append(message.get("body", b""))
            return message
        
        # 保存响应状态码
        response_status = 200  # 默认值
        
        # 修改send函数以记录响应信息
        async def send_wrapper(message):
            nonlocal response_status  # 使用nonlocal关键字以便可以修改外层作用域的变量
            
            if message["type"] == "http.response.start":
                # 记录响应开始
                response_status = message.get("status", 200)
                
                # 设置响应头
                headers = dict(message.get("headers", {}))
                headers[b"x-request-id"] = request_id.encode()
                headers[b"x-process-time"] = str(time.time() - start_time).encode()
                message["headers"] = [(k, v) for k, v in headers.items()]
            
            elif message["type"] == "http.response.body" and message.get("more_body", False) is False:
                # 记录响应完成
                process_time = time.time() - start_time
                
                # 尝试获取响应体
                body = message.get("body", b"")
                response_body = None
                
                if body:
                    try:
                        response_body = json.loads(body.decode())
                    except:
                        response_body = {"raw": str(body)}
                
                # 仅记录响应信息，不重复请求信息
                log.info(
                    f"响应 {request.method} {request.url.path} [{response_status}] - {process_time:.4f}s",
                    {
                        "status_code": response_status,
                        "process_time": process_time,
                        "response": response_body
                    }
                )
            
            # 发送原始消息
            await send(message)
        
        # 处理请求并捕获异常
        try:
            await self.app(scope, receive_wrapper, send_wrapper)
        except Exception as e:
            # 记录异常日志
            process_time = time.time() - start_time
            
            log.error(
                f"异常 {request.method} {request.url.path} - {str(e)}",
                {
                    "process_time": process_time,
                    "exception": str(e)
                }
            )
            
            raise
    
    def _get_basic_request_info(self, request: Request) -> Dict:
        """
        获取基本请求信息
        """
        headers = dict(request.headers)
        
        # 敏感信息脱敏
        if "authorization" in headers:
            headers["authorization"] = "******"
        if "cookie" in headers:
            headers["cookie"] = "******"
        
        # 创建请求日志信息
        request_info = {
            "method": request.method,
            "path": request.url.path,
            "headers": headers,
            "query_params": dict(request.query_params),
            "path_params": dict(request.path_params),
            "client_ip": request.client.host
        }
        
        return request_info


def setup_logging_middleware(app: FastAPI) -> None:
    """
    设置日志中间件
    """
    app.add_middleware(RequestLoggingMiddleware)
