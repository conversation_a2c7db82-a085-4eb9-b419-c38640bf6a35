import json
import logging
import os
import time
from datetime import datetime
from app.utils.time_utils import get_shanghai_time
from logging.handlers import TimedRotatingFileHandler
from typing import Dict, Optional, Union, Any
from decimal import Decimal

from app.core.config import settings
from app.utils.json_utils import DecimalEncoder

# 创建日志目录
os.makedirs(settings.APP_LOG_DIR, exist_ok=True)
os.makedirs(settings.SQL_LOG_DIR, exist_ok=True)
# 创建分析日志目录
ANALYSIS_LOG_DIR = os.path.join(settings.LOG_DIR, "analysis")
os.makedirs(ANALYSIS_LOG_DIR, exist_ok=True)


class JsonFormatter(logging.Formatter):
    """
    JSON格式的日志格式化器
    """
    def format(self, record: logging.LogRecord) -> str:
        log_data = {
            "timestamp": datetime.fromtimestamp(record.created).strftime("%Y-%m-%d %H:%M:%S.%f"),
            "level": record.levelname,
            "logger": record.name,
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
            "message": record.getMessage(),
        }
        
        # 添加request_id
        if hasattr(record, "request_id"):
            log_data["request_id"] = record.request_id
            
        # 添加exception信息
        if record.exc_info:
            log_data["exception"] = self.formatException(record.exc_info)
            
        # 添加其他自定义字段
        if hasattr(record, "extra") and record.extra:
            for key, value in record.extra.items():
                log_data[key] = value
        
        return json.dumps(log_data, ensure_ascii=False, cls=DecimalEncoder)


class RequestIdFilter(logging.Filter):
    """
    为日志添加request_id的过滤器
    """
    def __init__(self, request_id: Optional[str] = None):
        super().__init__()
        self.request_id = request_id
        
    def filter(self, record: logging.LogRecord) -> bool:
        if not hasattr(record, "request_id"):
            record.request_id = self.request_id
        return True


class DailyFileHandler(TimedRotatingFileHandler):
    """
    按日期创建新文件的处理器
    文件名格式为：YYYY-MM-DD_{basename}.log（日期在前面，便于排序）
    """
    def __init__(self, filename, when='midnight', interval=1, backupCount=0, encoding=None, delay=False, utc=False, atTime=None):
        # 将文件名分割为目录和基础名
        directory, basename = os.path.split(filename)
        # 基础名不要有扩展名，我们会自动添加日期和.log
        if '.' in basename:
            basename = basename.split('.')[0]
            
        # 生成今天的日期字符串
        today = get_shanghai_time().strftime('%Y-%m-%d')
        
        # 创建包含日期的文件名（日期在前面）
        dated_filename = os.path.join(directory, f"{today}_{basename}.log")
        
        # 初始化父类
        super().__init__(
            filename=dated_filename,
            when=when,
            interval=interval,
            backupCount=backupCount,
            encoding=encoding,
            delay=delay,
            utc=utc, 
            atTime=atTime
        )
        
        # 保存基本信息，用于生成新的文件名
        self.directory = directory
        self.basename = basename
    
    def doRollover(self):
        """
        重写滚动方法，在日期变更时生成新的文件名
        """
        # 关闭当前文件
        if self.stream:
            self.stream.close()
            self.stream = None
            
        # 获取新的日期
        new_date = get_shanghai_time().strftime('%Y-%m-%d')
        
        # 创建新的文件名（日期在前面）
        self.baseFilename = os.path.join(self.directory, f"{new_date}_{self.basename}.log")
        
        # 创建新文件
        if not self.delay:
            self.stream = self._open()


class Logger:
    """
    日志管理类
    """
    _app_logger = None
    _sql_logger = None
    _analysis_loggers = {}  # 存储不同projectId的logger实例
    
    @classmethod
    def get_app_logger(cls) -> logging.Logger:
        """
        获取应用日志记录器
        """
        if cls._app_logger is None:
            logger = logging.getLogger("app")
            logger.setLevel(getattr(logging, settings.LOG_LEVEL))
            
            # 防止日志重复输出
            if not logger.handlers:
                # 文件处理器
                file_handler = DailyFileHandler(
                    os.path.join(settings.APP_LOG_DIR, "app"),
                    when="midnight",
                    interval=1,
                    backupCount=30,
                    encoding="utf-8"
                )
                
                # 设置日志格式
                if settings.LOG_JSON_FORMAT:
                    file_handler.setFormatter(JsonFormatter())
                else:
                    file_handler.setFormatter(
                        logging.Formatter(
                            "%(asctime)s - %(levelname)s - [%(request_id)s] - %(module)s:%(funcName)s:%(lineno)d - %(message)s"
                        )
                    )
                
                # 添加处理器
                logger.addHandler(file_handler)
                
                # 如果是开发环境，同时输出到控制台
                if settings.DEBUG:
                    console_handler = logging.StreamHandler()
                    if settings.LOG_JSON_FORMAT:
                        console_handler.setFormatter(JsonFormatter())
                    else:
                        console_handler.setFormatter(
                            logging.Formatter(
                                "%(asctime)s - %(levelname)s - [%(request_id)s] - %(module)s:%(funcName)s:%(lineno)d - %(message)s"
                            )
                        )
                    logger.addHandler(console_handler)
            
            cls._app_logger = logger
        
        return cls._app_logger
    
    @classmethod
    def get_sql_logger(cls) -> logging.Logger:
        """
        获取SQL日志记录器
        """
        if cls._sql_logger is None:
            logger = logging.getLogger("sql")
            logger.setLevel(getattr(logging, settings.LOG_LEVEL))
            
            # 防止日志重复输出
            if not logger.handlers:
                # 文件处理器
                file_handler = DailyFileHandler(
                    os.path.join(settings.SQL_LOG_DIR, "sql"),
                    when="midnight",
                    interval=1,
                    backupCount=30,
                    encoding="utf-8"
                )
                
                # 设置日志格式
                if settings.LOG_JSON_FORMAT:
                    file_handler.setFormatter(JsonFormatter())
                else:
                    file_handler.setFormatter(
                        logging.Formatter(
                            "%(asctime)s - %(levelname)s - [%(request_id)s] - %(message)s"
                        )
                    )
                
                # 添加处理器
                logger.addHandler(file_handler)
            
            cls._sql_logger = logger
        
        return cls._sql_logger

    @classmethod
    def get_analysis_logger(cls, project_id: str, analysis_id: str) -> logging.Logger:
        """
        获取分析日志记录器（基于项目ID和分析ID）
        每个分析(对话)有自己的独立日志文件
        """
        logger_key = f"{project_id}_{analysis_id}"
        
        if logger_key not in cls._analysis_loggers:
            logger_name = f"analysis.{project_id}.{analysis_id}"
            logger = logging.getLogger(logger_name)
            logger.setLevel(getattr(logging, settings.LOG_LEVEL))
            logger.propagate = False  # 防止日志向上传播
            
            # 创建项目专属的日志目录
            project_log_dir = os.path.join(ANALYSIS_LOG_DIR, project_id)
            os.makedirs(project_log_dir, exist_ok=True)
            
            # 防止日志重复输出
            if not logger.handlers:
                # 生成基于时间戳和分析ID的文件名（时间戳在前面，便于排序）
                timestamp = get_shanghai_time().strftime("%Y-%m-%d_%H%M%S")
                log_filename = f"{timestamp}_{analysis_id}.log"
                log_filepath = os.path.join(project_log_dir, log_filename)
                
                # 使用普通的FileHandler，每个分析一个文件
                file_handler = logging.FileHandler(log_filepath, encoding="utf-8")
                
                # 设置日志格式
                if settings.LOG_JSON_FORMAT:
                    file_handler.setFormatter(JsonFormatter())
                else:
                    file_handler.setFormatter(
                        logging.Formatter(
                            "%(asctime)s - %(levelname)s - [%(request_id)s] - %(module)s:%(funcName)s:%(lineno)d - %(message)s"
                        )
                    )
                
                # 添加处理器
                logger.addHandler(file_handler)
            
            cls._analysis_loggers[logger_key] = logger
            
            # 清理30天前的旧日志文件
            cls._cleanup_old_logs(project_log_dir)
        
        return cls._analysis_loggers[logger_key]
    
    @classmethod
    def _cleanup_old_logs(cls, project_log_dir: str):
        """清理30天前的日志文件"""
        try:
            import os
            import time
            from datetime import datetime, timedelta
            
            # 计算30天前的时间戳
            cutoff_time = time.time() - (30 * 24 * 60 * 60)  # 30天前
            
            # 遍历项目日志目录
            for filename in os.listdir(project_log_dir):
                filepath = os.path.join(project_log_dir, filename)
                
                # 只处理.log文件
                if filename.endswith('.log') and os.path.isfile(filepath):
                    # 获取文件的修改时间
                    file_mtime = os.path.getmtime(filepath)
                    
                    # 如果文件超过30天，删除它
                    if file_mtime < cutoff_time:
                        try:
                            os.remove(filepath)
                            print(f"已删除过期日志文件: {filepath}")
                        except Exception as e:
                            print(f"删除日志文件失败 {filepath}: {e}")
        except Exception as e:
            print(f"清理旧日志时出错: {e}")


class AppLogger:
    """
    应用日志类，提供简便的日志记录方法
    """
    def __init__(self, request_id: Optional[str] = None):
        self.request_id = request_id
        self.logger = Logger.get_app_logger()
    
    def _log(self, level: str, message: str, extra: Optional[Dict[str, Any]] = None):
        """
        记录日志的通用方法
        """
        log_method = getattr(self.logger, level)
        
        # 添加request_id过滤器
        for handler in self.logger.handlers:
            handler.addFilter(RequestIdFilter(self.request_id))
        
        # 不管有没有extra参数，都创建LogRecord并设置request_id
        record = logging.LogRecord(
            name=self.logger.name,
            level=getattr(logging, level.upper()),
            pathname="",
            lineno=0,
            msg=message,
            args=(),
            exc_info=None,
        )
        
        if extra:
            record.extra = extra
        record.request_id = self.request_id
        
        for handler in self.logger.handlers:
            handler.handle(record)
    
    def debug(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """
        记录DEBUG级别日志
        """
        self._log("debug", message, extra)
    
    def info(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """
        记录INFO级别日志
        """
        self._log("info", message, extra)
    
    def warning(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """
        记录WARNING级别日志
        """
        self._log("warning", message, extra)
    
    def error(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """
        记录ERROR级别日志
        """
        self._log("error", message, extra)
    
    def critical(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """
        记录CRITICAL级别日志
        """
        self._log("critical", message, extra)
    
    def set_request_id(self, request_id: str):
        """
        设置request_id
        """
        self.request_id = request_id


class SQLLogger:
    """
    SQL日志类
    """
    def __init__(self, request_id: Optional[str] = None):
        self.request_id = request_id
        self.logger = Logger.get_sql_logger()
    
    def log_sql(self, sql: str, params: Optional[Any] = None):
        """
        记录SQL语句
        """
        if not settings.LOG_PRINT_SQL:
            return
        
        # 添加request_id过滤器
        for handler in self.logger.handlers:
            handler.addFilter(RequestIdFilter(self.request_id))
        
        # 记录SQL语句和参数
        message = f"SQL: {sql}"
        if params:
            message += f"\nParams: {params}"
        
        self.logger.info(message)
    
    def set_request_id(self, request_id: str):
        """
        设置request_id
        """
        self.request_id = request_id


class AnalysisLogger:
    """
    分析日志类，提供分析级别的日志记录
    """
    def __init__(self, project_id: str, analysis_id: str, request_id: Optional[str] = None):
        self.project_id = project_id
        self.analysis_id = analysis_id
        self.request_id = request_id
        self.logger = Logger.get_analysis_logger(project_id, analysis_id)
    
    def _log(self, level: str, message: str, extra: Optional[Dict[str, Any]] = None):
        """
        记录日志的通用方法
        """
        log_method = getattr(self.logger, level)
        
        # 添加request_id过滤器
        for handler in self.logger.handlers:
            handler.addFilter(RequestIdFilter(self.request_id))
        
        # 创建LogRecord并设置request_id和project_id
        record = logging.LogRecord(
            name=self.logger.name,
            level=getattr(logging, level.upper()),
            pathname="",
            lineno=0,
            msg=message,
            args=(),
            exc_info=None,
        )
        
        if extra is None:
            extra = {}
        extra['project_id'] = self.project_id
        extra['analysis_id'] = self.analysis_id
        record.extra = extra
        record.request_id = self.request_id
        
        for handler in self.logger.handlers:
            handler.handle(record)
    
    def debug(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """记录DEBUG级别日志"""
        self._log("debug", message, extra)
    
    def info(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """记录INFO级别日志"""
        self._log("info", message, extra)
    
    def warning(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """记录WARNING级别日志"""
        self._log("warning", message, extra)
    
    def error(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """记录ERROR级别日志"""
        self._log("error", message, extra)
    
    def critical(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """记录CRITICAL级别日志"""
        self._log("critical", message, extra)
    
    def log_step(self, step_name: str, step_data: Dict[str, Any], status: str = "info"):
        """记录分析步骤"""
        message = f"[{step_name}] {step_data.get('message', '')}"
        extra = {
            "step_name": step_name,
            "step_data": step_data
        }
        self._log(status, message, extra)
    
    def set_request_id(self, request_id: str):
        """设置request_id"""
        self.request_id = request_id


# 创建默认的日志记录器实例
log = AppLogger()
sql_log = SQLLogger()


# 兼容旧的日志接口
def log_method(
    message: str, 
    level: str = "info", 
    request_id: Optional[str] = None, 
    extra: Optional[Dict[str, Any]] = None
) -> None:
    """
    记录应用日志（兼容旧接口）
    """
    logger = AppLogger(request_id)
    getattr(logger, level.lower())(message, extra)


# SQL日志记录函数（兼容旧接口）
def log_sql(sql: str, params: Optional[Any] = None, request_id: Optional[str] = None) -> None:
    """
    记录SQL日志（兼容旧接口）
    """
    logger = SQLLogger(request_id)
    logger.log_sql(sql, params)


def format_log_data(log_data: Dict[str, Any]) -> str:
    """将日志数据格式化为JSON字符串"""
    try:
        return json.dumps(log_data, ensure_ascii=False, cls=DecimalEncoder)
    except:
        return str(log_data)
