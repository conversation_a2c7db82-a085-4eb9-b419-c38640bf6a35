import os
import sys
from typing import Any, Dict, List, Optional, Union
from pathlib import Path
from functools import lru_cache

from pydantic import Field, field_validator, model_validator
from pydantic_settings import BaseSettings, SettingsConfigDict
from dotenv import load_dotenv


class Settings(BaseSettings):
    """
    应用配置类，使用pydantic进行类型和值的校验
    """
    model_config = SettingsConfigDict(env_file=".env.dev", env_file_encoding="utf-8", extra="ignore")

    # 应用环境配置
    APP_ENV: str = "dev"
    DEBUG: bool = True

    # 服务配置
    APP_NAME: str = "DecisionAI 2.0 Beta"
    APP_HOST: str = "0.0.0.0"
    APP_PORT: int = 8000

    # 数据库配置
    DB_HOST: str = "localhost"
    DB_PORT: int = 3306
    DB_USER: str = "root"
    DB_PASSWORD: str = "password"
    DB_NAME: str = "ai_data_qa"
    DB_POOL_SIZE: int = 5
    DB_MAX_OVERFLOW: int = 10
    DB_ECHO: bool = True

    # LLM配置
    OPENAI_API_KEY: str = "your-api-key"
    OPENAI_BASE_URL: str = "https://api.openai.com/v1"
    OPENAI_MODEL: str = "gpt-3.5-turbo"
    
    # 图表生成配置
    USE_SUPER_SCRIPT_MODE: bool = False  # 是否启用超级脚本模式（一体化图表生成）

    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_DIR: str = "./logs"
    LOG_APP_DIR: str = "app"
    LOG_SQL_DIR: str = "sql"
    LOG_PRINT_SQL: bool = True
    LOG_JSON_FORMAT: bool = True
    APP_LOG_DIR: str = "./logs/app"
    SQL_LOG_DIR: str = "./logs/sql"

    @property
    def DATABASE_URL(self) -> str:
        """
        获取数据库URL
        """
        return f"mysql+pymysql://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"

    # Redis配置
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: Optional[str] = None

    # JWT配置
    JWT_SECRET_KEY: str = "your-secret-key"
    JWT_ALGORITHM: str = "HS256"
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # CORS配置
    CORS_ORIGINS: List[str] = ["*"]
    CORS_METHODS: List[str] = ["*"]
    CORS_HEADERS: List[str] = ["*"]
    
    # 超级用户配置
    FIRST_SUPERUSER: str = "admin"
    FIRST_SUPERUSER_PASSWORD: str = "admin123"
    FIRST_SUPERUSER_EMAIL: str = "<EMAIL>"
    
    @field_validator("LOG_LEVEL")
    @classmethod
    def validate_log_level(cls, v: str) -> str:
        """
        验证日志级别
        """
        allowed_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in allowed_levels:
            return "INFO"
        return v.upper()

    @model_validator(mode='after')
    def validate_environment(self) -> 'Settings':
        """根据环境加载不同配置"""
        env_file = f".env.{self.APP_ENV}"
        if Path(env_file).exists():
            self.model_config["env_file"] = env_file
        return self


@lru_cache()
def get_settings() -> Settings:
    """
    获取应用配置
    根据APP_ENV环境变量加载对应的环境配置文件
    """
    # 获取环境变量
    app_env = os.getenv("APP_ENV", "dev")
    
    # 加载默认.env文件（如果存在）
    if os.path.exists(".env"):
        load_dotenv(dotenv_path=".env", override=True)
        print("已加载基础配置: .env")
    
    # 加载对应环境的配置文件
    env_file = f".env.{app_env}"
    if os.path.exists(env_file):
        load_dotenv(dotenv_path=env_file, override=True)
        print(f"已加载环境配置: {env_file}")
    else:
        print(f"警告: 环境配置文件 {env_file} 不存在")
    
    return Settings()


# 全局配置对象
settings = get_settings()
