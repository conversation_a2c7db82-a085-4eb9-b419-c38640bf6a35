from fastapi import Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from pydantic import ValidationError
from sqlalchemy.exc import SQLAlchemyError

from app.core.logger import log


class BaseException(Exception):
    """
    自定义基础异常类
    """
    def __init__(self, code: int, message: str):
        self.code = code
        self.message = message
        super().__init__(self.message)


class NotFoundException(BaseException):
    """
    资源不存在异常
    """
    def __init__(self, message: str = "资源不存在"):
        super().__init__(code=404, message=message)


class BadRequestException(BaseException):
    """
    请求参数错误异常
    """
    def __init__(self, message: str = "请求参数错误"):
        super().__init__(code=400, message=message)


class UnauthorizedException(BaseException):
    """
    未授权异常
    """
    def __init__(self, message: str = "未授权"):
        super().__init__(code=401, message=message)


class ForbiddenException(BaseException):
    """
    禁止访问异常
    """
    def __init__(self, message: str = "禁止访问"):
        super().__init__(code=403, message=message)


class PermissionDeniedException(BaseException):
    """
    权限不足异常
    """
    def __init__(self, message: str = "权限不足"):
        super().__init__(code=403, message=message)


class ServerException(BaseException):
    """
    服务器内部错误异常
    """
    def __init__(self, message: str = "服务器内部错误"):
        super().__init__(code=500, message=message)


class DatabaseException(BaseException):
    """
    数据库错误异常
    """
    def __init__(self, message: str = "数据库错误"):
        super().__init__(code=500, message=message)


# 异常处理器
async def base_exception_handler(request: Request, exc: BaseException) -> JSONResponse:
    """
    自定义异常处理器
    """
    request_id = request.headers.get("X-Request-ID", "")
    log.error(
        f"请求异常: {exc.message}",
        {"error_code": exc.code, "path": request.url.path}
    )
    
    # 对于401未授权异常，返回真正的HTTP 401状态码
    if exc.code == 401:
        return JSONResponse(
            status_code=status.HTTP_401_UNAUTHORIZED,
            content={
                "code": exc.code,
                "message": exc.message,
                "data": None,
                "request_id": request_id
            }
        )
    
    # 其他异常仍然返回HTTP 200，在响应体中包含错误码
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            "code": exc.code,
            "message": exc.message,
            "data": None,
            "request_id": request_id
        }
    )


async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """
    请求参数验证异常处理器
    """
    request_id = request.headers.get("X-Request-ID", "")
    errors = exc.errors()
    error_details = []
    
    for error in errors:
        error_details.append({
            "loc": error["loc"],
            "msg": error["msg"],
            "type": error["type"]
        })
    
    log.error(
        "参数验证错误",
        {"errors": error_details, "path": request.url.path}
    )
    
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            "code": 400,
            "message": "请求参数错误",
            "data": error_details,
            "request_id": request_id
        }
    )


async def sqlalchemy_exception_handler(request: Request, exc: SQLAlchemyError) -> JSONResponse:
    """
    数据库异常处理器
    """
    request_id = request.headers.get("X-Request-ID", "")
    error_message = str(exc)
    
    log.error(
        f"数据库错误: {error_message}",
        {"path": request.url.path}
    )
    
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            "code": 500,
            "message": "数据库操作错误",
            "data": None,
            "request_id": request_id
        }
    )


async def unhandled_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """
    未处理异常处理器
    """
    request_id = request.headers.get("X-Request-ID", "")
    error_message = str(exc)
    
    log.error(
        f"未处理异常: {error_message}",
        {"exception": error_message, "path": request.url.path}
    )
    
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={
            "code": 500,
            "message": "服务器内部错误",
            "data": None,
            "request_id": request_id
        }
    )
