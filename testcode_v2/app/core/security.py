from datetime import datetime, timedelta
from typing import Any, Dict, Optional, Union

from jose import JWTError, jwt
from passlib.context import CryptContext

from app.core.config import settings
from app.utils.time_utils import get_shanghai_time
from app.core.exceptions import UnauthorizedException


# 密码上下文，用于密码哈希和验证
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    验证密码
    """
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """
    获取密码哈希
    """
    return pwd_context.hash(password)


def create_access_token(
    subject: Union[str, int], 
    expires_minutes: Optional[int] = None,
    data: Dict[str, Any] = {}
) -> str:
    """
    创建JWT访问令牌
    
    Args:
        subject: 令牌主题，通常是用户ID
        expires_minutes: 令牌过期时间（分钟），为None则使用配置的默认值
        data: 附加数据，将被包含在令牌中
    """
    expires_minutes = expires_minutes or settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES
    expire = get_shanghai_time() + timedelta(minutes=expires_minutes)
    
    # 构建令牌数据
    to_encode = {
        "exp": expire,
        "sub": str(subject),
        "iat": get_shanghai_time(),
        **data
    }
    
    # 使用算法和密钥签名令牌
    encoded_jwt = jwt.encode(
        to_encode, 
        settings.JWT_SECRET_KEY, 
        algorithm=settings.JWT_ALGORITHM
    )
    
    return encoded_jwt


def decode_token(token: str) -> Dict[str, Any]:
    """
    解码JWT令牌
    
    如果令牌无效或已过期，则抛出UnauthorizedException
    """
    try:
        payload = jwt.decode(
            token,
            settings.JWT_SECRET_KEY,
            algorithms=[settings.JWT_ALGORITHM]
        )
        
        return payload
    except JWTError:
        raise UnauthorizedException("无效的认证凭证")


def get_token_data(token: str) -> Dict[str, Any]:
    """
    获取令牌中的数据
    """
    return decode_token(token)
