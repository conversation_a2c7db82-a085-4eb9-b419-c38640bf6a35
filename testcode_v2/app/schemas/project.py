from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime

# 避免循环引用，使用类型注解字符串
from typing import TYPE_CHECKING, ForwardRef

# 导入DataSource和AnalysisBrief的前向引用
DataSourceRef = ForwardRef('DataSource')
AnalysisBriefRef = ForwardRef('AnalysisBrief')

# 基础模型
class ProjectBase(BaseModel):
    name: str = Field(..., description="项目名称", example="金融风险分析")
    description: Optional[str] = Field(None, description="项目描述", example="分析金融机构的风险数据")
    user_id: int = Field(default=0, description="创建人ID")
    owner_id: Optional[int] = Field(None, description="项目所有者ID")
    org_id: Optional[int] = Field(None, description="所属企业ID")
    visibility: Optional[str] = Field(default="PRIVATE", description="项目可见性", example="PRIVATE")
    report_prompt: Optional[str] = Field(None, description="项目自定义报告生成提示词")

# 创建请求
class ProjectCreate(ProjectBase):
    pass

# 更新请求
class ProjectUpdate(BaseModel):
    name: Optional[str] = Field(None, description="项目名称", example="金融风险分析(更新)")
    description: Optional[str] = Field(None, description="项目描述", example="分析金融机构的风险数据(更新)")
    visibility: Optional[str] = Field(None, description="项目可见性", example="PRIVATE")
    user_id: Optional[int] = Field(None, description="创建人ID")
    report_prompt: Optional[str] = Field(None, description="项目自定义报告生成提示词")

# 数据库模型转换
class ProjectInDBBase(ProjectBase):
    id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# 响应模型
class Project(ProjectInDBBase):
    pass

# 带数据源和分析记录的详细响应模型
class ProjectDetail(Project):
    data_sources: List[DataSourceRef] = []
    analyses: List[AnalysisBriefRef] = []

# 解析前向引用
from app.schemas.data_source import DataSource
from app.schemas.analysis import AnalysisBrief
ProjectDetail.update_forward_refs()

# 用于多步骤创建流程
class ProjectCreateTemp(ProjectBase):
    pass 