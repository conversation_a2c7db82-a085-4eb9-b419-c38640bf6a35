from pydantic import BaseModel, Field, validator
from typing import Optional, Dict, Any, List
from datetime import datetime
from enum import Enum
import json

# 工具类型枚举
class ToolType(str, Enum):
    SQL = "sql"  # SQL查询工具
    API = "api"  # API调用工具
    PYTHON = "python"  # Python脚本工具
    GRAPH = "graph"  # 图数据查询工具
    VECTOR = "vector"  # 向量检索工具
    CUSTOM = "custom"  # 自定义工具
    
    @classmethod
    def _missing_(cls, value):
        """处理大小写不敏感的枚举值查找"""
        if value is None:
            return cls.CUSTOM
            
        if isinstance(value, str):
            # 尝试大小写不敏感匹配
            for member in cls:
                if member.value.lower() == value.lower():
                    return member
        
        # 如果没有找到匹配项，返回CUSTOM
        return cls.CUSTOM

# 响应工具类型字段，处理输入为大写的情况
def convert_tool_type_case(tool_type):
    """转换工具类型大小写"""
    if isinstance(tool_type, str):
        # 将大写转为小写，确保响应验证通过
        return tool_type.lower()
    return tool_type

# 参数模型
class ToolParameter(BaseModel):
    name: str = Field(..., description="参数名称", example="company_name")
    type: str = Field(..., description="参数类型", example="string")
    description: str = Field(..., description="参数描述", example="公司名称，使用%查询所有公司")
    required: bool = Field(True, description="是否必填")
    default: Optional[Any] = Field(None, description="默认值")

# 基础模型
class ToolBase(BaseModel):
    name: str = Field(..., description="工具名称", example="公司基本信息查询")
    description: Optional[str] = Field(None, description="工具描述", example="查询公司的基本信息，包括注册信息、经营状态、风险信息等")
    tool_type: ToolType = Field(..., description="工具类型")
    template: str = Field(..., description="工具模板", example="SELECT * FROM company_info WHERE name LIKE :company_name")
    parameters: List[Dict[str, Any]] = Field(..., description="工具参数定义")

# 创建请求
class ToolCreate(ToolBase):
    project_id: str = Field(..., description="所属项目ID")
    
    @validator('template')
    def validate_template(cls, v, values):
        tool_type = values.get('tool_type')
        if tool_type == ToolType.SQL and not v.strip().lower().startswith(('select', 'with')):
            raise ValueError("SQL工具模板必须是SELECT或WITH开头的查询语句")
        return v
    
    @validator('parameters')
    def validate_parameters(cls, v):
        """验证参数格式"""
        # 如果是字符串，尝试解析为JSON
        if isinstance(v, str):
            try:
                v = json.loads(v)
            except json.JSONDecodeError:
                raise ValueError("参数必须是有效的JSON格式")
        
        # 如果是字典，转换为列表
        if isinstance(v, dict):
            param_list = []
            for key, value in v.items():
                if isinstance(value, dict):
                    param_list.append({"name": key, **value})
                else:
                    param_list.append({
                        "name": key,
                        "type": "string",
                        "description": str(value) if value else key,
                        "required": False
                    })
            v = param_list
        
        # 确保是列表类型
        if not isinstance(v, list):
            raise ValueError("参数必须是列表类型")
        
        # 验证每个参数的格式
        for param in v:
            if not isinstance(param, dict):
                raise ValueError("每个参数必须是字典类型")
            if 'name' not in param:
                raise ValueError("每个参数必须有name字段")
        
        return v

# 更新请求
class ToolUpdate(BaseModel):
    name: Optional[str] = Field(None, description="工具名称", example="公司基本信息查询(更新)")
    description: Optional[str] = Field(None, description="工具描述", example="查询公司的基本信息(更新)")
    template: Optional[str] = Field(None, description="工具模板")
    parameters: Optional[List[Dict[str, Any]]] = Field(None, description="工具参数定义")
    
    @validator('parameters')
    def validate_parameters(cls, v):
        """验证参数格式"""
        if v is None:
            return v
            
        # 如果是字符串，尝试解析为JSON
        if isinstance(v, str):
            try:
                v = json.loads(v)
            except json.JSONDecodeError:
                raise ValueError("参数必须是有效的JSON格式")
        
        # 如果是字典，转换为列表
        if isinstance(v, dict):
            param_list = []
            for key, value in v.items():
                if isinstance(value, dict):
                    param_list.append({"name": key, **value})
                else:
                    param_list.append({
                        "name": key,
                        "type": "string",
                        "description": str(value) if value else key,
                        "required": False
                    })
            v = param_list
        
        # 确保是列表类型
        if not isinstance(v, list):
            raise ValueError("参数必须是列表类型")
        
        # 验证每个参数的格式
        for param in v:
            if not isinstance(param, dict):
                raise ValueError("每个参数必须是字典类型")
            if 'name' not in param:
                raise ValueError("每个参数必须有name字段")
        
        return v

# 测试工具请求
class ToolTest(BaseModel):
    parameters: Dict[str, Any] = Field(..., description="测试参数")

# 数据库模型转换
class ToolInDBBase(ToolBase):
    id: str
    data_source_id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# 响应模型
class Tool(ToolInDBBase):
    @validator('parameters', pre=True)
    def ensure_parameters(cls, v):
        """确保参数是列表格式"""
        import json
        print(f"Tool响应模型参数验证 - 原始参数: {v}, 类型: {type(v)}")
        
        # 如果为空，返回空列表
        if v is None:
            return []
            
        # 如果是字符串，尝试解析为JSON
        if isinstance(v, str):
            try:
                v = json.loads(v)
                print(f"参数字符串解析结果: {v}")
            except Exception as e:
                print(f"参数解析失败: {str(e)}")
                return []
        
        # 如果是字典，转换为列表
        if isinstance(v, dict):
            try:
                param_list = []
                for key, value in v.items():
                    if isinstance(value, dict):
                        param_list.append({"name": key, **value})
                    else:
                        param_list.append({
                            "name": key,
                            "type": "string",
                            "description": str(value) if value else key,
                            "required": False
                        })
                print(f"字典转换为参数列表: {param_list}")
                return param_list
            except Exception as e:
                print(f"转换参数失败: {str(e)}")
                return []
        
        # 如果不是列表，返回空列表
        if not isinstance(v, list):
            print(f"参数不是列表类型: {type(v)}")
            return []
            
        print(f"最终参数: {v}")
        return v 