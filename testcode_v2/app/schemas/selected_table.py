from typing import List, Optional, Dict, Any
from pydantic import BaseModel
from datetime import datetime


class SelectedTableBase(BaseModel):
    table_name: str
    table_description: Optional[str] = None


class SelectedTableCreate(SelectedTableBase):
    data_source_id: str
    table_schema: Dict[str, Any]
    sample_data: List[Dict[str, Any]]


class SelectedTableUpdate(BaseModel):
    table_description: Optional[str] = None
    table_schema: Optional[Dict[str, Any]] = None
    sample_data: Optional[List[Dict[str, Any]]] = None


class SelectedTableInDB(SelectedTableBase):
    id: str
    data_source_id: str
    table_schema: Dict[str, Any]
    sample_data: List[Dict[str, Any]]
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True


class SelectedTable(SelectedTableInDB):
    pass 