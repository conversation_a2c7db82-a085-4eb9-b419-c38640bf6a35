from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from datetime import datetime

class ConversationCreate(BaseModel):
    project_id: str
    initial_query: str

class ConversationUpdate(BaseModel):
    title: Optional[str] = None
    status: Optional[str] = None

class ConversationResponse(BaseModel):
    id: str
    project_id: str
    user_id: int
    title: str
    context_summary: Optional[str] = None
    total_rounds: int
    total_tokens_used: int
    created_at: datetime
    updated_at: datetime
    status: str
    round_count: int

    class Config:
        from_attributes = True

class ConversationListResponse(BaseModel):
    id: str
    project_id: str
    user_id: int
    title: str
    created_at: datetime
    updated_at: datetime
    status: str
    round_count: int
    last_analysis_at: datetime

    class Config:
        from_attributes = True

class ConversationAnalysisResponse(BaseModel):
    id: str
    conversation_id: str
    round_number: int
    query: str
    status: str
    created_at: datetime
    updated_at: datetime
    final_report: Optional[str] = None

    class Config:
        from_attributes = True

# 新增：多轮分析相关Schema
class ConversationRoundRequest(BaseModel):
    """会话轮次分析请求"""
    query: str
    round_number: int
    max_planning_rounds: int = 25
    use_full_context: bool = True
    context_depth: int = 5
    continue_analysis_id: Optional[str] = None
    intent_adjustment: Optional[str] = None  # 用户的意图补充信息

class AnalysisResult(BaseModel):
    """分析结果"""
    round_number: int
    query: str
    summary: str
    key_data: List[Any] = []
    insights: List[str] = []
    timestamp: str

class ConversationContextData(BaseModel):
    """会话上下文数据"""
    previous_queries: List[str] = []
    previous_results: List[AnalysisResult] = []
    key_findings: List[str] = []
    data_sources_used: List[str] = []
    tools_used: List[str] = []
    user_preferences: Dict[str, Any] = {}

class ConversationContextResponse(BaseModel):
    """会话上下文响应"""
    conversation_id: str
    context_data: ConversationContextData
    total_rounds: int
    last_updated: datetime

    class Config:
        from_attributes = True

class ConversationContextRecord(BaseModel):
    """上下文记录"""
    id: int
    conversation_id: str
    round_number: int
    context_type: str
    context_data: Dict[str, Any]
    relevance_score: float
    created_at: datetime

    class Config:
        from_attributes = True 