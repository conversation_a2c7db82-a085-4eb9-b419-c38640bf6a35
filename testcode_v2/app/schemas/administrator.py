from datetime import datetime
from typing import Optional
from pydantic import BaseModel, EmailStr


class AdministratorBase(BaseModel):
    """管理员基础模型"""
    username: str
    email: Optional[EmailStr] = None
    is_active: bool = True


class AdministratorCreate(AdministratorBase):
    """管理员创建模型"""
    password: str


class AdministratorUpdate(BaseModel):
    """管理员更新模型"""
    username: Optional[str] = None
    email: Optional[EmailStr] = None
    password: Optional[str] = None
    is_active: Optional[bool] = None


class AdministratorInDB(AdministratorBase):
    """数据库管理员模型"""
    id: int
    last_login_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class Administrator(AdministratorInDB):
    """管理员API响应模型"""
    pass


class AdminToken(BaseModel):
    """管理员令牌模型"""
    access_token: str
    token_type: str = "bearer"
    admin_info: Administrator


class AdminTokenData(BaseModel):
    """管理员令牌数据"""
    username: Optional[str] = None
    is_admin: bool = True


class AdminLoginRequest(BaseModel):
    """管理员登录请求"""
    username: str
    password: str 