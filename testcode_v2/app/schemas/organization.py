from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field, validator


class OrganizationBase(BaseModel):
    """组织基础Schema"""
    name: str = Field(..., min_length=1, max_length=100, description="企业名称")
    description: Optional[str] = Field(None, description="企业描述")
    
    # 限额配置
    token_limit: int = Field(1000000, ge=0, description="Token消耗限额（月）")
    user_limit: int = Field(50, ge=1, description="用户数量限额")
    project_limit: int = Field(20, ge=1, description="项目数量限额")
    
    # 状态管理
    status: int = Field(1, ge=1, le=2, description="状态：1=正常 2=停用")
    expire_at: Optional[datetime] = Field(None, description="过期时间")
    
    # 联系信息
    contact_email: Optional[str] = Field(None, max_length=100, description="联系邮箱")
    contact_phone: Optional[str] = Field(None, max_length=20, description="联系电话")
    address: Optional[str] = Field(None, description="地址")


class OrganizationCreate(OrganizationBase):
    """创建组织Schema"""
    pass


class OrganizationUpdate(BaseModel):
    """更新组织Schema"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="企业名称")
    description: Optional[str] = Field(None, description="企业描述")
    
    # 限额配置
    token_limit: Optional[int] = Field(None, ge=0, description="Token消耗限额（月）")
    user_limit: Optional[int] = Field(None, ge=1, description="用户数量限额")
    project_limit: Optional[int] = Field(None, ge=1, description="项目数量限额")
    
    # 状态管理
    status: Optional[int] = Field(None, ge=1, le=2, description="状态：1=正常 2=停用")
    expire_at: Optional[datetime] = Field(None, description="过期时间")
    
    # 联系信息
    contact_email: Optional[str] = Field(None, max_length=100, description="联系邮箱")
    contact_phone: Optional[str] = Field(None, max_length=20, description="联系电话")
    address: Optional[str] = Field(None, description="地址")


class Organization(OrganizationBase):
    """组织Schema"""
    id: int
    created_at: datetime
    updated_at: datetime
    
    # 计算属性
    is_active: bool
    is_expired: bool

    class Config:
        from_attributes = True


class OrganizationList(BaseModel):
    """组织列表Schema"""
    items: List[Organization]
    total: int
    page: int
    size: int


class OrganizationStats(BaseModel):
    """组织统计Schema"""
    id: int
    name: str
    status: int
    
    # 统计数据
    user_count: int = Field(0, description="用户数量")
    project_count: int = Field(0, description="项目数量")
    token_used: int = Field(0, description="Token使用量")
    
    # 限额信息
    user_limit: int
    project_limit: int
    token_limit: int
    
    # 使用率
    user_usage_rate: float = Field(0.0, ge=0.0, le=1.0, description="用户使用率")
    project_usage_rate: float = Field(0.0, ge=0.0, le=1.0, description="项目使用率")
    token_usage_rate: float = Field(0.0, ge=0.0, le=1.0, description="Token使用率")

    class Config:
        from_attributes = True 
