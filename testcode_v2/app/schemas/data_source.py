from pydantic import BaseModel, Field, validator
from typing import Optional, Dict, Any, List, ClassVar, ForwardRef
from datetime import datetime
from enum import Enum, auto
from logging import getLogger
import re

# 创建Tool的前向引用
ToolRef = ForwardRef('Tool')

# 数据源类型枚举
class DataSourceType(str, Enum):
    """数据源类型枚举"""
    ORACLE = "ORACLE"
    MYSQL = "MYSQL"
    POSTGRESQL = "POSTGRESQL"
    MSSQL = "MSSQL"
    HTTP_API = "HTTP_API"
    OTHER = "OTHER"
    
    def __new__(cls, value):
        obj = str.__new__(cls, value)
        obj._value_ = value
        return obj
        
    def __eq__(self, other):
        if isinstance(other, str):
            return self.value.lower() == other.lower()
        return super().__eq__(other)
    
    # 保持可哈希性，使用父类的hash方法
    def __hash__(self):
        return super().__hash__()
    
    @classmethod
    def _missing_(cls, value):
        """处理大小写不敏感的枚举值查找"""
        log = getLogger("app")
        
        if value is None:
            log.info(f"枚举值为None，返回默认值OTHER")
            return cls.OTHER
            
        if not isinstance(value, str):
            try:
                value = str(value)
                log.info(f"非字符串枚举值，转换为字符串: {value}")
            except Exception as e:
                log.warning(f"无法将枚举值转换为字符串: {str(e)}，使用OTHER作为默认")
                return cls.OTHER
                
        # 记录尝试匹配的值
        log.info(f"尝试匹配枚举值: '{value}'")
        
        # 尝试直接匹配，大小写不敏感
        value_lower = value.lower()
        for member in cls:
            if member.name.lower() == value_lower or member.value.lower() == value_lower:
                log.info(f"直接匹配成功: '{value}' -> {member}")
                return member
        
        # 尝试简单转大写匹配
        value_upper = value.upper()
        for member in cls:
            if member.name == value_upper or member.value == value_upper:
                log.info(f"大写匹配成功: '{value}' -> {member}")
                return member
        
        # 尝试更宽松的匹配
        value_normalized = value_lower.replace("_", "").replace("-", "").replace(" ", "")
        for member in cls:
            member_name_normalized = member.name.lower().replace("_", "").replace("-", "").replace(" ", "")
            member_value_normalized = member.value.lower().replace("_", "").replace("-", "").replace(" ", "")
            
            if member_name_normalized == value_normalized or member_value_normalized == value_normalized:
                log.info(f"规范化匹配成功: '{value}' -> {member}")
                return member
        
        # 特殊情况处理
        special_cases = {
            "oracle": cls.ORACLE,
            "mysql": cls.MYSQL,
            "postgresql": cls.POSTGRESQL,
            "postgres": cls.POSTGRESQL,
            "mssql": cls.MSSQL,
            "sqlserver": cls.MSSQL,
            "httpapi": cls.HTTP_API,
            "http-api": cls.HTTP_API,
            "api": cls.HTTP_API
        }
        
        if value_lower in special_cases:
            matched_enum = special_cases[value_lower]
            log.info(f"特殊情况匹配成功: '{value}' -> {matched_enum}")
            return matched_enum
            
        # 记录找不到的枚举值
        log.warning(f"找不到匹配的数据源类型枚举值: '{value}'，使用OTHER作为默认")
        
        # 如果没有找到匹配项，返回OTHER
        return cls.OTHER

# 基础模型
class DataSourceBase(BaseModel):
    name: str = Field(..., description="数据源名称", example="金融数据库")
    description: Optional[str] = Field(None, description="数据源描述", example="金融数据的Oracle数据库")
    type: DataSourceType = Field(..., description="数据源类型")
    config: Dict[str, Any] = Field(..., description="数据源配置")
    db_version: Optional[str] = Field(None, description="数据库版本信息", example="Oracle Database 19c Enterprise Edition")
    
    class Config:
        use_enum_values = True

# 创建请求
class DataSourceCreate(DataSourceBase):
    project_id: str = Field(..., description="所属项目ID")
    
    @validator('config')
    def validate_config(cls, v, values):
        src_type = values.get('type')
        # 把类型统一转为小写再判断，保证兼容性
        src_type_lower = src_type.lower() if src_type else ""
        
        # 根据不同类型验证配置
        if src_type_lower == "oracle":
            required_fields = ['host', 'port', 'service_name', 'username', 'password']
        elif src_type_lower in ["mysql", "postgresql", "mssql"]:
            required_fields = ['host', 'port', 'database', 'username', 'password']
        elif src_type_lower == "http_api":
            required_fields = ['base_url', 'headers']
        else:
            return v
            
        for field in required_fields:
            if field not in v:
                raise ValueError(f"数据源类型 {src_type} 需要配置 {field}")
        return v

# 更新请求
class DataSourceUpdate(BaseModel):
    name: Optional[str] = Field(None, description="数据源名称", example="金融数据库(更新)")
    description: Optional[str] = Field(None, description="数据源描述", example="金融数据的Oracle数据库(更新)")
    config: Optional[Dict[str, Any]] = Field(None, description="数据源配置")

# 测试连接请求
class DataSourceTestConnection(BaseModel):
    type: DataSourceType = Field(..., description="数据源类型")
    config: Dict[str, Any] = Field(..., description="数据源配置")
    
    class Config:
        use_enum_values = True

# 数据库模型转换
class DataSourceInDBBase(DataSourceBase):
    id: str
    project_id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
        use_enum_values = True

# 响应模型
class DataSource(DataSourceInDBBase):
    pass

# 带工具列表的详细响应模型
class DataSourceDetail(DataSource):
    tools: List[ToolRef] = []

# 解析前向引用
from app.schemas.tool import Tool
DataSourceDetail.update_forward_refs()

# 用于测试数据库连接
class ConnectionTest(BaseModel):
    type: str = Field(..., description="数据源类型", example="mysql")
    config: dict = Field(..., description="数据源配置", example={"host": "localhost", "port": 3306})

class ConnectionTestResult(BaseModel):
    success: bool = Field(..., description="连接是否成功", example=True)
    message: str = Field(..., description="连接结果消息", example="连接成功")
    tables: Optional[List[str]] = Field(None, description="数据库中的表列表")
    db_version: Optional[str] = Field(None, description="数据库版本信息", example="Oracle Database 19c Enterprise Edition")

# 数据源创建临时请求
class DataSourceCreateTemp(DataSourceCreate):
    project_id: str = Field(..., description="项目ID", example="2f3e1718-7a2e-4c67-a573-eb7ae93a7ca9")

# 表保存请求
class TableSaveRequest(BaseModel):
    tables: List[str] = Field(..., description="要保存的表名列表", example=["users", "orders"]) 