# 导入所有模型以解决循环导入问题
from app.schemas.project import Project, ProjectCreate, ProjectUpdate, ProjectDetail
from app.schemas.data_source import DataSource, DataSourceCreate, DataSourceUpdate, DataSourceDetail, DataSourceType, DataSourceTestConnection
from app.schemas.llm_model import LLMModel, LLMModelCreate, LLMModelUpdate, LLMModelTestResult
from app.schemas.tool import Tool, ToolCreate, ToolUpdate, ToolType, ToolParameter, ToolTest
from app.schemas.analysis import Analysis, AnalysisBrief, AnalysisRequest, AnalysisReport, AnalysisStep, IntentAnalysis, ToolExecution, ToolExecutionBrief

# 导出所有模式
__all__ = [
    "Project", "ProjectCreate", "ProjectUpdate", "ProjectDetail",
    "DataSource", "DataSourceCreate", "DataSourceUpdate", "DataSourceDetail", "DataSourceType", "DataSourceTestConnection",
    "Tool", "ToolCreate", "ToolUpdate", "ToolType", "ToolTest",
    "AnalysisRequest", "IntentAnalysis", "AnalysisStep", "Analysis", "AnalysisBrief", 
    "ToolExecution", "ToolExecutionBrief", "AnalysisReport"
]
