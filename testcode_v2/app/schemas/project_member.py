from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel


class ProjectMemberBase(BaseModel):
    """项目成员基础Schema"""
    project_id: str
    user_id: int
    project_role_id: int


class ProjectMemberCreate(ProjectMemberBase):
    """创建项目成员Schema"""
    invited_by: Optional[int] = None


class ProjectMemberUpdate(BaseModel):
    """更新项目成员Schema"""
    project_role_id: Optional[int] = None
    status: Optional[str] = None


class ProjectMemberInDB(ProjectMemberBase):
    """数据库中的项目成员Schema"""
    id: int
    invited_by: Optional[int] = None
    joined_at: datetime
    status: str

    class Config:
        from_attributes = True


class ProjectMember(ProjectMemberInDB):
    """项目成员响应Schema"""
    pass


class ProjectMemberWithDetails(ProjectMember):
    """带详细信息的项目成员Schema"""
    username: Optional[str] = None
    email: Optional[str] = None
    role_name: Optional[str] = None
    invited_by_username: Optional[str] = None


class InviteMemberRequest(BaseModel):
    """邀请成员请求Schema"""
    user_id: int
    project_role_id: int


class ProjectMemberList(BaseModel):
    """项目成员列表Schema"""
    total: int
    members: List[ProjectMemberWithDetails] 