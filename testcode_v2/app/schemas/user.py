from datetime import datetime
from typing import Optional
from pydantic import BaseModel, EmailStr, Field

# 企业基础信息（用于用户schema中的嵌套）
class OrganizationInfo(BaseModel):
    """企业基础信息"""
    id: int
    name: str
    
    class Config:
        from_attributes = True

# 角色基础信息（用于用户schema中的嵌套）
class RoleInfo(BaseModel):
    """角色基础信息"""
    id: int
    name: str
    level: int
    
    class Config:
        from_attributes = True

class UserBase(BaseModel):
    """用户基础模型"""
    username: str
    email: Optional[EmailStr] = None
    is_active: bool = True
    pid: int = Field(default=0, description="父级ID")
    role_id: int = Field(default=3, description="系统角色ID")
    org_id: Optional[int] = Field(None, description="所属企业ID")
    can_create_project: bool = Field(default=True, description="是否可以创建项目")
    language_preference: str = Field(default='zh-CN', description="用户语言偏好")

class UserCreate(UserBase):
    """用户创建模型"""
    password: str
    org_id: Optional[int] = None

class UserUpdate(BaseModel):
    """用户更新模型"""
    username: Optional[str] = None
    email: Optional[EmailStr] = None
    password: Optional[str] = None
    is_active: Optional[bool] = None
    pid: Optional[int] = Field(None, description="父级ID")
    role_id: Optional[int] = Field(None, description="系统角色ID")
    org_id: Optional[int] = Field(None, description="所属企业ID")
    can_create_project: Optional[bool] = Field(None, description="是否可以创建项目")
    language_preference: Optional[str] = Field(None, description="用户语言偏好")

class UserInDB(UserBase):
    """数据库用户模型"""
    id: int
    is_superuser: bool = False
    role_id: Optional[int] = None
    org_id: Optional[int] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class User(UserInDB):
    """用户API响应模型"""
    pass

# 管理员用户详细信息模型（包含关联的企业和角色信息）
class AdminUserDetail(UserInDB):
    """管理员用户详细信息模型（包含企业和角色信息）"""
    organization: Optional[OrganizationInfo] = None
    role: Optional[RoleInfo] = None
    parent_username: Optional[str] = None  # 父级用户名
    children_count: int = 0  # 子账号数量
    
    class Config:
        from_attributes = True

class Token(BaseModel):
    """令牌模型"""
    access_token: str
    token_type: str = "bearer"
    
class TokenData(BaseModel):
    """令牌数据"""
    username: Optional[str] = None

class LoginRequest(BaseModel):
    """登录请求"""
    username: str
    password: str 