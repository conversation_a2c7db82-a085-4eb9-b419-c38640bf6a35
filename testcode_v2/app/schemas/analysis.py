from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List, ForwardRef
from datetime import datetime

# 创建Tool的前向引用
ToolRef = ForwardRef('Tool')

# 分析请求模型
class AnalysisRequest(BaseModel):
    query: str = Field(..., description="用户查询", example="哪些公司存在资金风险？")
    project_id: str = Field(..., description="项目ID")
    
    def __init__(self, **data):
        # 在初始化时打印所有传入的数据
        import logging
        logger = logging.getLogger("app.schemas.analysis")
        logger.info(f"AnalysisRequest接收到的原始数据: {data}")
        super().__init__(**data)

# 分析步骤模型
class AnalysisStep(BaseModel):
    step_id: str = Field(..., description="步骤ID", example="STEP1")
    tool_name: str = Field(..., description="工具名称", example="analyze_fund_transfers")
    purpose: str = Field(..., description="分析目的", example="分析所有公司的资金流转情况")
    parameters: Dict[str, Any] = Field(..., description="工具参数")

# 意图分析结果模型
class IntentAnalysis(BaseModel):
    problem_understanding: Dict[str, Any] = Field(..., description="问题理解")
    analysis_steps: List[AnalysisStep] = Field(..., description="分析步骤")

# 工具执行结果简略模型
class ToolExecutionBrief(BaseModel):
    id: str
    tool_id: str
    parameters: Dict[str, Any]
    execution_time: Optional[float] = None
    created_at: datetime
    step_id: Optional[str] = None  # 添加步骤ID字段

    class Config:
        from_attributes = True

# 工具执行结果详细模型
class ToolExecution(ToolExecutionBrief):
    result: Optional[Dict[str, Any]] = None
    tool: Optional[ToolRef] = None

# 分析记录简略模型
class AnalysisBrief(BaseModel):
    id: str
    query: str
    created_at: datetime

    class Config:
        from_attributes = True

# 分析记录详细模型
class Analysis(AnalysisBrief):
    intent_analysis: Optional[Dict[str, Any]] = None
    result: Optional[str] = None
    
    # 工具执行记录
    tool_executions: List[ToolExecutionBrief] = []

# 分析报告模型
class AnalysisReport(BaseModel):
    report: str = Field(..., description="分析报告")

# 解析前向引用
from app.schemas.tool import Tool
ToolExecution.update_forward_refs() 