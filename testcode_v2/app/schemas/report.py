"""
报告数据结构定义
用于标准化报告组件的数据格式
"""
from typing import List, Dict, Any, Optional, Union, Literal
from pydantic import BaseModel, Field
from datetime import datetime

# 基础报告组件类型
ReportComponentType = Literal[
    "header", 
    "kpi_grid", 
    "executive_summary", 
    "chart", 
    "insight_list", 
    "recommendation_list",
    "data_table",
    "text_section",
    "divider"
]

class MetricData(BaseModel):
    """单个KPI指标数据"""
    label: str = Field(..., description="指标名称")
    value: str = Field(..., description="指标值（格式化后的字符串）")
    change: Optional[str] = Field(None, description="变化幅度，如 '+15.2%'")
    change_type: Optional[Literal["increase", "decrease", "stable"]] = Field(None, description="变化类型")
    description: Optional[str] = Field(None, description="指标说明")

class HeaderData(BaseModel):
    """报告头部数据"""
    title: str = Field(..., description="报告标题")
    subtitle: Optional[str] = Field(None, description="副标题")
    generated_at: str = Field(..., description="生成时间")
    tags: List[str] = Field(default_factory=list, description="标签列表")
    summary_stats: Optional[Dict[str, Any]] = Field(None, description="概览统计")

class KpiGridData(BaseModel):
    """KPI网格数据"""
    title: str = Field(..., description="模块标题")
    metrics: List[MetricData] = Field(..., description="指标列表")
    layout: Optional[Dict[str, Any]] = Field(None, description="布局配置")

class ExecutiveSummaryData(BaseModel):
    """执行摘要数据"""
    title: str = Field(..., description="模块标题")
    content: str = Field(..., description="摘要内容")
    key_points: Optional[List[str]] = Field(None, description="关键要点")

class ChartData(BaseModel):
    """图表数据"""
    chart_id: str = Field(..., description="图表唯一标识")
    title: str = Field(..., description="图表标题")
    interpretation: str = Field(..., description="图表解读")
    chart_config: Dict[str, Any] = Field(..., description="ECharts配置")
    chart_type: Optional[str] = Field(None, description="图表类型")
    data_source: Optional[str] = Field(None, description="数据来源说明")

class InsightData(BaseModel):
    """单个洞察数据"""
    type: Literal["trend", "anomaly", "opportunity", "risk", "correlation"] = Field(..., description="洞察类型")
    text: str = Field(..., description="洞察内容")
    confidence: float = Field(..., ge=0, le=1, description="置信度")
    impact: Optional[Literal["high", "medium", "low"]] = Field(None, description="影响程度")
    category: Optional[str] = Field(None, description="业务分类")

class InsightListData(BaseModel):
    """洞察列表数据"""
    title: str = Field(..., description="模块标题")
    insights: List[InsightData] = Field(..., description="洞察列表")

class RecommendationData(BaseModel):
    """单个建议数据"""
    area: str = Field(..., description="业务领域")
    text: str = Field(..., description="建议内容")
    priority: Optional[Literal["high", "medium", "low"]] = Field(None, description="优先级")
    timeline: Optional[str] = Field(None, description="时间框架")

class RecommendationListData(BaseModel):
    """建议列表数据"""
    title: str = Field(..., description="模块标题")
    recommendations: List[RecommendationData] = Field(..., description="建议列表")

class DataTableData(BaseModel):
    """数据表格数据"""
    title: str = Field(..., description="表格标题")
    headers: List[str] = Field(..., description="表头")
    rows: List[List[str]] = Field(..., description="表格数据行")
    description: Optional[str] = Field(None, description="表格说明")

class TextSectionData(BaseModel):
    """文本段落数据"""
    title: Optional[str] = Field(None, description="段落标题")
    content: str = Field(..., description="段落内容")
    level: Optional[int] = Field(1, description="标题级别")

class DividerData(BaseModel):
    """分割线数据"""
    style: Optional[Literal["solid", "dashed", "dotted"]] = Field("solid", description="分割线样式")
    margin: Optional[str] = Field("24px", description="边距")

# 报告组件基类
class ReportComponent(BaseModel):
    """报告组件基类"""
    type: ReportComponentType = Field(..., description="组件类型")
    data: Union[
        HeaderData,
        KpiGridData, 
        ExecutiveSummaryData,
        ChartData,
        InsightListData,
        RecommendationListData,
        DataTableData,
        TextSectionData,
        DividerData
    ] = Field(..., description="组件数据")
    
    class Config:
        use_enum_values = True

# 完整报告数据结构
class StructuredReport(BaseModel):
    """结构化报告数据"""
    components: List[ReportComponent] = Field(..., description="报告组件列表")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="报告元数据")
    version: str = Field("1.0", description="数据结构版本")
    
    class Config:
        use_enum_values = True