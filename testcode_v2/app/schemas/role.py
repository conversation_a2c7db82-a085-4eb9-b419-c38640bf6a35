from datetime import datetime
from typing import Optional, List, Any
from pydantic import BaseModel, Field, validator


class RoleBase(BaseModel):
    """角色基础Schema"""
    name: str = Field(..., min_length=1, max_length=50, description="角色名称")
    description: Optional[str] = Field(None, description="角色描述")
    level: int = Field(0, ge=0, le=2, description="角色层级：0=系统级 1=企业级 2=用户级")
    permissions: Optional[List[str]] = Field(None, description="权限配置")
    is_active: bool = Field(True, description="是否启用")


class RoleCreate(RoleBase):
    """创建角色Schema"""
    
    @validator('permissions')
    def validate_permissions(cls, v):
        if v is not None and not isinstance(v, list):
            raise ValueError('权限配置必须是列表格式')
        return v


class RoleUpdate(BaseModel):
    """更新角色Schema"""
    name: Optional[str] = Field(None, min_length=1, max_length=50, description="角色名称")
    description: Optional[str] = Field(None, description="角色描述")
    level: Optional[int] = Field(None, ge=0, le=2, description="角色层级：0=系统级 1=企业级 2=用户级")
    permissions: Optional[List[str]] = Field(None, description="权限配置")
    is_active: Optional[bool] = Field(None, description="是否启用")
    
    @validator('permissions')
    def validate_permissions(cls, v):
        if v is not None and not isinstance(v, list):
            raise ValueError('权限配置必须是列表格式')
        return v


class Role(RoleBase):
    """角色Schema"""
    id: int
    is_system: bool
    created_at: datetime
    updated_at: datetime
    
    # 计算属性
    is_system_role: bool
    is_org_role: bool
    is_user_role: bool

    class Config:
        from_attributes = True


class RoleList(BaseModel):
    """角色列表Schema"""
    items: List[Role]
    total: int
    page: int
    size: int


class RolePermissionCheck(BaseModel):
    """角色权限检查Schema"""
    role_id: int
    permission: str
    has_permission: bool


 