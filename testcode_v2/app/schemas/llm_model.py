from typing import Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime


class LLMModelBase(BaseModel):
    """LLM模型基础Schema"""
    model_config = {"protected_namespaces": ()}

    name: str = Field(..., description="模型名称", example="GPT-3.5-Turbo")
    description: Optional[str] = Field(None, description="模型描述", example="快速响应的对话模型")
    model_name: str = Field(..., description="模型标识符", example="gpt-3.5-turbo")
    api_key: str = Field(..., description="API密钥")
    base_url: Optional[str] = Field("https://api.openai.com/v1", description="API基础URL")
    config: Optional[Dict[str, Any]] = Field(None, description="额外配置参数")
    is_active: bool = Field(True, description="是否启用")


class LLMModelCreate(LLMModelBase):
    """创建LLM模型Schema"""
    pass


class LLMModelUpdate(BaseModel):
    """更新LLM模型Schema"""
    model_config = {"protected_namespaces": ()}

    name: Optional[str] = Field(None, description="模型名称")
    description: Optional[str] = Field(None, description="模型描述")
    model_name: Optional[str] = Field(None, description="模型标识符")
    api_key: Optional[str] = Field(None, description="API密钥")
    base_url: Optional[str] = Field(None, description="API基础URL")
    config: Optional[Dict[str, Any]] = Field(None, description="额外配置参数")
    is_active: Optional[bool] = Field(None, description="是否启用")


class LLMModel(LLMModelBase):
    """LLM模型Schema（包含敏感信息，仅内部使用）"""
    id: str = Field(..., description="模型ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    model_config = {"from_attributes": True, "protected_namespaces": ()}


class LLMModelSafe(BaseModel):
    """LLM模型安全Schema（不包含API密钥等敏感信息）"""
    model_config = {"from_attributes": True, "protected_namespaces": ()}

    id: str = Field(..., description="模型ID")
    name: str = Field(..., description="模型名称", example="GPT-3.5-Turbo")
    description: Optional[str] = Field(None, description="模型描述", example="快速响应的对话模型")
    model_name: str = Field(..., description="模型标识符", example="gpt-3.5-turbo")
    base_url: Optional[str] = Field("https://api.openai.com/v1", description="API基础URL")
    config: Optional[Dict[str, Any]] = Field(None, description="额外配置参数")
    is_active: bool = Field(True, description="是否启用")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


class LLMModelTestConfig(BaseModel):
    """临时测试配置Schema"""
    model_config = {"protected_namespaces": ()}

    name: str = Field(..., description="模型名称")
    model_name: str = Field(..., description="模型标识符")
    api_key: str = Field(..., description="API密钥")
    base_url: Optional[str] = Field("https://api.openai.com/v1", description="API基础URL")

class LLMModelTestResult(BaseModel):
    """LLM模型测试结果Schema"""
    success: bool = Field(..., description="测试是否成功")
    response: Optional[str] = Field(None, description="模型响应")
    error: Optional[str] = Field(None, description="错误信息")
    latency: Optional[float] = Field(None, description="响应延迟(秒)")
