from typing import Any, Generic, Optional, TypeVar, List

from pydantic import BaseModel, Field

# 定义泛型类型变量
T = TypeVar("T")


class ResponseBase(BaseModel):
    """
    响应基类
    """
    code: int = Field(0, description="状态码，0表示成功，非0表示错误")
    message: str = Field("success", description="响应消息")
    request_id: Optional[str] = Field(None, description="请求ID")


class Response(BaseModel, Generic[T]):
    """
    标准响应模型
    """
    code: int = Field(0, description="状态码，0表示成功，非0表示错误")
    message: str = Field("success", description="响应消息")
    data: Optional[T] = Field(None, description="响应数据")
    request_id: Optional[str] = Field(None, description="请求ID")


class PageInfo(BaseModel):
    """
    分页信息
    """
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页条数")
    total: int = Field(..., description="总条数")
    total_page: int = Field(..., description="总页数")


class PageData(BaseModel, Generic[T]):
    """
    分页数据模型
    """
    items: List[T] = Field(..., description="数据列表")
    page_info: PageInfo = Field(..., description="分页信息")


class PageResponse(BaseModel, Generic[T]):
    """
    分页响应模型
    """
    code: int = Field(0, description="状态码，0表示成功，非0表示错误")
    message: str = Field("success", description="响应消息")
    data: Optional[PageData[T]] = Field(None, description="分页数据")
    request_id: Optional[str] = Field(None, description="请求ID")
