"""
报告生成模块
负责根据分析结果和项目自定义提示词生成专业的分析报告
"""

import json
import re
from typing import Dict, Any, List, Optional
from decimal import Decimal
import traceback
from datetime import datetime

from app.core.logger import log
from app.utils.json_utils import DecimalEncoder
from app.utils.time_utils import get_shanghai_time
from app.models.project import Project
from app.models.analysis import AnalysisStepDetail, ToolExecution
from app.db.session import SessionLocal
from app.schemas.report import (
    StructuredReport, ReportComponent, HeaderData, KpiGridData, 
    ExecutiveSummaryData, ChartData, InsightListData, RecommendationListData,
    DataTableData, TextSectionData, MetricData, InsightData, RecommendationData
)


class ReportGenerator:
    """报告生成器"""
    
    def __init__(self, llm_service):
        """
        初始化报告生成器
        
        Args:
            llm_service: LLM服务实例
        """
        self.llm_service = llm_service
        self.logger = log
    
    async def generate_report_with_context(self,
                                         context,  # ExecutionContext
                                         schema_info: str,
                                         reasoning: str = '',
                                         project_id: Optional[str] = None,
                                         db_session = None,
                                         user_language: str = 'zh-CN',
                                         analysis_insights: Optional[List[Any]] = None,
                                         lifecycle_logger = None) -> tuple[str, dict]:
        """
        使用增强方式生成报告，整合专业数据分析结果

        Args:
            context: 执行上下文 (ExecutionContext)
            schema_info: 数据库Schema信息
            reasoning: 推理过程
            project_id: 项目ID
            db_session: 数据库会话
            user_language: 用户语言偏好
            analysis_insights: 专业数据分析结果列表

        Returns:
            tuple[str, dict]: (生成的报告内容, token用量数据)
        """
        try:
            log.info("📊 开始生成结构化报告（新版本）")
            
            # 尝试生成结构化报告
            try:
                structured_report, usage = await self.generate_structured_report(
                    context=context,
                    schema_info=schema_info,
                    reasoning=reasoning,
                    project_id=project_id,
                    db_session=db_session,
                    user_language=user_language,
                    analysis_insights=analysis_insights,
                    lifecycle_logger=lifecycle_logger
                )
                
                # 将结构化报告转换为JSON字符串返回（保持接口兼容性）
                import json
                return json.dumps(structured_report.dict(), ensure_ascii=False, indent=2), usage
                
            except Exception as e:
                log.warning(f"结构化报告生成失败，回退到传统格式: {str(e)}")
                # 继续使用原有的报告生成逻辑作为备用
            
            log.info("🔥 回退到传统Markdown报告格式")
            
            # 🔥 获取项目自定义提示词
            project_custom_prompt = None
            if project_id and db_session:
                try:
                    project = db_session.query(Project).filter(Project.id == project_id).first()
                    if project and project.report_prompt:
                        project_custom_prompt = project.report_prompt
                        log.info(f"🔥 获取到项目自定义提示词，长度: {len(project_custom_prompt)}")
                    else:
                        log.info("🔥 项目未设置自定义提示词，使用默认提示词")
                except Exception as e:
                    log.warning(f"🔥 获取项目自定义提示词失败: {str(e)}")

            # 生成增强的提示词（包含分析洞察）
            result_prompt, result_system_prompt = self._generate_result_prompt(context, schema_info, reasoning, user_language, analysis_insights)

            # 🔥 如果有项目自定义提示词，将其拼接到system prompt中
            if project_custom_prompt and project_custom_prompt.strip():
                enhanced_system_prompt = f"""{result_system_prompt}

**项目特定要求：**
{project_custom_prompt.strip()}

**注意：** 请在遵循上述基础报告结构要求的同时，特别关注项目特定要求。如果项目特定要求与基础要求有冲突，请优先满足项目特定要求。
"""
                log.info("🔥 已将项目自定义提示词拼接到system prompt中")
            else:
                enhanced_system_prompt = result_system_prompt
                log.info("🔥 使用默认system prompt")
            print("生成分析报告用户提示词：",result_prompt)
            print("生成分析报告系统提示词：",enhanced_system_prompt)
            # 调用LLM生成最终答案（包含占位符）
            log.info("🔥 调用LLM生成报告")
            final_answer, usage_data = await self._call_llm_for_text_response(result_prompt, enhanced_system_prompt, user_language=user_language)
            log.info(f"🔥 LLM原始响应长度: {len(final_answer) if final_answer else 0}, 类型: {type(final_answer)}")

            # 🔥 记录报告生成的LLM调用到生命周期日志
            if lifecycle_logger:
                try:
                    lifecycle_logger.log_llm_call(
                        step_id="intelligent_report_generation",
                        call_type="intelligent_report",
                        user_prompt=result_prompt,
                        system_prompt=enhanced_system_prompt,
                        llm_response=final_answer,
                        usage_data=usage_data,
                        temperature=0.3,
                        reasoning=reasoning
                    )
                    log.info("✅ 智能报告生成LLM调用已记录到生命周期日志")
                except Exception as e:
                    log.error(f"❌ 记录智能报告生成LLM调用失败: {str(e)}")

            final_answer = self._strip_markdown_markers(final_answer)
            log.info(f"🔥 处理后响应长度: {len(final_answer) if final_answer else 0}, 类型: {type(final_answer)}")

            # Token用量记录
            if usage_data:
                log.debug(f"Token用量: {usage_data}")

            # 🔥 替换图表占位符为真实图表数据
            final_report_with_charts = self._replace_chart_placeholders(final_answer, context)

            log.info(f"🔥 报告生成成功，最终报告长度: {len(final_report_with_charts)}")
            return final_report_with_charts, usage_data
            
        except Exception as e:
            log.error(f"🔥 报告生成失败: {str(e)}")
            log.error(f"🔥 错误类型: {type(e).__name__}")
            raise e
    
    def _generate_result_prompt(self, context, schema_info: str, reasoning: str, user_language: str = 'zh-CN', analysis_insights: Optional[List[Any]] = None) -> tuple[str, str]:
        """生成最终结果提示词 - 所有内容都拼接在system prompt中

        Args:
            context: 执行上下文
            schema_info: 数据库Schema信息
            reasoning: 推理过程
            user_language: 用户语言偏好

        Returns:
            tuple[str, str]: (user_prompt, system_prompt)
        """
        # 获取当前时间
        current_time = get_shanghai_time()
        formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
        
        # 分析执行历史，判断问题复杂度
        execution_count = len(context.execution_history)
        
        # 获取已有上下文摘要（如果有的话）
        multi_round_context = ""
        if hasattr(context, 'multi_round_context') and context.multi_round_context:
            ctx_data = context.multi_round_context
            context_summary = ctx_data.get('context_summary', '')
            
            if context_summary:
                multi_round_context = f"""
**历史轮次分析摘要**
{context_summary}
"""
        # 如果存在上一步的推理，则需要将推理作为系统提示词的一部分
        reasoning_prompt = ""
        if reasoning:
            reasoning_prompt = f"""
**上一步的推理**
{reasoning}
"""

        # 提取图表信息
        chart_info = self._extract_chart_info_simple(context)
        print(f"🔍 DEBUG: chart_info 长度: {len(chart_info) if chart_info else 0}")
        if chart_info:
            print(f"🔍 DEBUG: chart_info 内容: {chart_info[:200]}...")
        else:
            print("🔍 DEBUG: chart_info 为空")
        chart_section = ""
        if chart_info:
            chart_section = f"""

**图表可视化信息：**
{chart_info}
"""

        # 🔥 新增：提取图表生成状态信息
        chart_generation_status_section = self._extract_chart_generation_status(context, user_language)
        if chart_generation_status_section:
            chart_section += chart_generation_status_section

        # 构建专业数据分析洞察部分
        analysis_insights_section = ""
        if analysis_insights:
            print(f"🔍 DEBUG: analysis_insights 数量: {len(analysis_insights)}")
            for i, insight in enumerate(analysis_insights):
                print(f"🔍 DEBUG: 洞察 {i+1}: {type(insight)} - {str(insight)[:100]}...")
            analysis_insights_section = self._build_analysis_insights_section(analysis_insights, user_language)
            print(f"🔍 DEBUG: analysis_insights_section 长度: {len(analysis_insights_section)}")
        else:
            print("🔍 DEBUG: analysis_insights 为空或None")

        # 根据用户语言偏好生成不同的角色描述
        if user_language == 'en-US':
            role_description = "You are an experienced and insightful **professional data analyst**. Your core responsibility is to deeply integrate, refine, and interpret all completed data analysis steps and tool outputs, ultimately writing a comprehensive **English** business analysis report."
            language_requirement = "English"
        else:
            role_description = "你是一个经验丰富、洞察敏锐的**专业数据分析师**。你的核心职责是将所有已完成的数据分析步骤和工具输出进行深度整合、提炼和解读，最终撰写一份**简体中文**的业务分析报告。"
            language_requirement = "简体中文"

        # 将所有内容拼接到system prompt中
        system_prompt = f"""{role_description}

**报告撰写核心指导{('（像人类分析师一样思考）' if user_language != 'en-US' else ' (Think like a human analyst)')}：**

1.  **用户导向与意图匹配：** 你的报告必须直接、清晰地回答用户的原始问题，并涵盖所有已推断出的多维度意图。
2.  **发现的提炼与解读：**
    *   **核心发现 (Findings)：** 这不是原始数据的简单罗列或 JSON 打印。你需要从所有工具的执行结果 (`context.execution_results`) 中**提取最关键的数据点、趋势、模式、关联性和任何值得注意的异常**。
    *   **智能图表插入：** 当描述数据趋势、模式或关键发现时，如果有相关的可视化图表可以支撑你的论述，请在适当的位置自然地插入图表占位符。**重要：必须严格使用提供的图表ID，格式为[CHART_PLACEHOLDER:图表ID]，不能使用自定义的描述性占位符。** 图表应该紧跟在相关数据描述之后，用"如下图所示"、"从图表中可以看出"等自然的表述方式引入。
    *   **业务解读：** 解释这些发现的**业务含义**，它们意味着什么，而不是仅仅陈述数字。
3.  **结论的逻辑推导：**
    *   **主要结论 (Conclusions)：** 结论必须是基于`核心发现`的**直接、逻辑推导**。它们是对核心发现的概括性总结，并直接回答用户的原始问题或其拆解的子问题。
    *   **明确引用：** 确保每个结论都能清晰地追溯到`核心发现`中的具体数据点、趋势或模式。
    *   **图表支撑：** 当结论需要数据证据支撑时，可以引用相关图表来增强说服力。
4.  **建议与决策的可操作性：**
    *   **建议与决策 (Recommendations)：** 建议必须是**具体、可操作的行动方案**，旨在解决`主要结论`中提出的问题、利用发现的机会，或优化现有业务流程。
    *   **业务影响：** 思考这些建议可能带来的业务影响或效益。
5.  **分析过程的总结：**
    *   **数据来源与步骤：** 这不是原始日志的简单复制。你需要将`context.execution_history`中的复杂执行过程，**概括性地总结**为用户可理解的、清晰的分析流程。突出关键的查询、探索和验证步骤，以及它们如何一步步引导至最终结论。避免过多的技术细节，除非其对理解报告至关重要。
5.  **不能伪造数据或者假设数据**

**报告输出形式决策与结构：**

在生成最终报告前，你必须**首先**严格判断用户查询的复杂度和分析深度，并**只能选择以下两种输出形式中的一种**：

1.  **简洁答案（适用于简单查询）：**
    *   **触发条件：**
        *   用户查询需求极其简单，且**核心发现**部分仅包含**一个单一的聚合数值**（例如：总数、平均值、百分比等），或者一个明确的**是/否判断**。
        *   **最关键的判断：** 该答案**无需任何进一步的趋势分析、多维度解读、复杂推理或业务建议。**
    *   **输出格式：** 直接以简洁的自然语言给出答案，无需任何Markdown分节标题

2.  **详细分析报告（适用于复杂查询）：**
    *   **触发条件：**
        *   用户查询需求涉及**多个维度分析**（例如：销量趋势、产品构成、地域分布等）。
        *   需要进行**趋势解读**、**复杂推理**、**比较分析**。
        *   需要提供**业务建议**或**决策思路**。
        *   **核心发现**部分包含多组数据点、或需要解释其业务含义、或需要识别模式和异常。
    *   **输出格式：** 严格遵循以下 Markdown 报告结构，并完整填充每一节内容。

# 详细分析报告格式如下：
## 1. 问题概述
- **用户查询**：<!-- 用户查询 -->

## 2. 核心发现
<!-- 在此部分，请提炼并总结所有工具调用结果中的关键数据点、趋势、模式和异常。
    用清晰的自然语言描述，并想象如何通过图表（例如折线图、柱状图）进行可视化呈现。
    避免直接粘贴原始JSON。 -->

## 3. 主要结论
<!-- 在此部分，基于"核心发现"进行逻辑推导，给出回答用户问题（及其子问题）的核心结论。
    确保结论能清晰追溯到"核心发现"中的具体数据或趋势。 -->

## 4. 建议与决策
<!-- 在此部分，根据"主要结论"提出具体、可操作的业务建议或决策思路。
    思考这些建议如何解决问题或利用机会。 -->

## 5. 数据来源与分析步骤回顾
<!-- 在此部分，概括性地总结整个分析过程，包括关键的查询、探索、验证步骤，以及它们如何逐步帮助你得出结论。
    避免过多技术细节，除非对理解分析流程至关重要。 -->

---

**当前分析任务信息：**

**当前时间**：{formatted_time}

**用户查询**：{context.user_query}

{multi_round_context}

{reasoning_prompt}

**本轮分析情况**：
# 执行步骤数：{execution_count}
# 工具执行结果
```json
{json.dumps(context.execution_history, ensure_ascii=False, indent=2, cls=DecimalEncoder)}
```

**数据库Schema信息：**
{schema_info}

{chart_section}

{analysis_insights_section}

请根据以上信息生成分析报告。
"""
        
        # 返回简单的用户提示词
        user_prompt = "请生成分析报告。"
        
        return user_prompt, system_prompt

    def _build_analysis_insights_section(self, analysis_insights: List[Any], user_language: str = 'zh-CN') -> str:
        """构建智能聚合的专业数据分析洞察部分

        Args:
            analysis_insights: 分析洞察结果列表
            user_language: 用户语言偏好

        Returns:
            str: 智能聚合后的格式化分析洞察部分
        """
        if not analysis_insights:
            return ""

        # 智能聚合分析洞察
        aggregated_insights = self._aggregate_analysis_insights(analysis_insights, user_language)

        # 构建结构化的洞察部分
        return self._build_structured_insights_presentation(aggregated_insights, user_language)

    def _aggregate_analysis_insights(self, analysis_insights_list: List[Any], user_language: str = 'zh-CN') -> dict:
        """智能聚合多轮分析洞察

        Args:
            analysis_insights_list: 多轮分析洞察结果列表
            user_language: 用户语言偏好

        Returns:
            dict: 聚合后的洞察数据
        """
        aggregated = {
            'analysis_overview': self._build_analysis_overview(analysis_insights_list),
            'key_insights': self._extract_and_rank_key_insights(analysis_insights_list),
            'progressive_findings': self._build_progressive_narrative(analysis_insights_list),
            'cross_correlations': self._identify_cross_correlations(analysis_insights_list),
            'overall_quality': self._calculate_overall_quality_assessment(analysis_insights_list),
            'critical_anomalies': self._aggregate_critical_anomalies(analysis_insights_list)
        }
        return aggregated

    def _build_analysis_overview(self, insights_list: List[Any]) -> dict:
        """构建分析概览"""
        total_rows = 0
        total_columns = set()
        analysis_rounds = len(insights_list)

        for insights in insights_list:
            if hasattr(insights, 'statistical_summary') and insights.statistical_summary:
                total_rows += insights.statistical_summary.total_rows
                total_columns.update(insights.statistical_summary.columns)

        return {
            'analysis_rounds': analysis_rounds,
            'total_data_points': total_rows,
            'unique_columns': len(total_columns),
            'analysis_scope': 'multi_dimensional' if analysis_rounds > 2 else 'focused'
        }

    def _extract_and_rank_key_insights(self, insights_list: List[Any]) -> List[dict]:
        """提取并排序关键洞察"""
        all_insights = []

        for round_idx, insights in enumerate(insights_list):
            if hasattr(insights, 'insights') and insights.insights:
                for insight in insights.insights:
                    insight_data = {
                        'round': round_idx + 1,
                        'type': insight.insight_type,
                        'title': insight.title,
                        'description': insight.description,
                        'confidence': insight.confidence,
                        'business_relevance': getattr(insight, 'business_relevance', ''),
                        'importance_score': self._calculate_insight_importance(insight)
                    }
                    all_insights.append(insight_data)

        # 去重相似洞察
        unique_insights = self._deduplicate_similar_insights(all_insights)

        # 按重要性排序
        ranked_insights = sorted(unique_insights, key=lambda x: x['importance_score'], reverse=True)

        return ranked_insights[:5]  # 返回最重要的5个洞察

    def _calculate_insight_importance(self, insight) -> float:
        """计算洞察重要性评分（优化版）"""
        base_score = insight.confidence
        title = insight.title.lower()
        description = insight.description.lower()

        # 根据洞察类型调整权重
        type_weights = {
            'anomaly': 1.3,     # 异常发现最重要
            'trend': 1.2,       # 趋势分析很重要
            'correlation': 1.1,  # 相关性分析重要
            'pattern': 1.0,     # 模式识别
            'summary': 0.7      # 总结性洞察权重降低
        }

        type_weight = type_weights.get(insight.insight_type, 1.0)

        # 业务价值评分
        business_value_score = self._calculate_business_value(title, description)

        # 数据支撑度评分
        data_support_score = self._calculate_data_support(description)

        # 可操作性评分
        actionability_score = self._calculate_actionability(title, description)

        # 避免低质量洞察的惩罚
        quality_penalty = self._calculate_quality_penalty(title, description)

        # 综合评分
        final_score = (
            base_score * type_weight * 0.4 +
            business_value_score * 0.3 +
            data_support_score * 0.2 +
            actionability_score * 0.1
        ) - quality_penalty

        return max(final_score, 0.1)  # 确保最低分不为负数

    def _calculate_business_value(self, title: str, description: str) -> float:
        """计算业务价值评分"""
        text = title + " " + description

        # 高价值关键词
        high_value_keywords = [
            '核心客户', '主要消费', '消费能力强', '高价值', '机会', '风险',
            '显著差异', '明显趋势', '关键发现', '重要洞察', '业务机会'
        ]

        # 中等价值关键词
        medium_value_keywords = [
            '年龄段', '品类偏好', '消费模式', '购买行为', '分布特征'
        ]

        # 低价值关键词（技术性描述）
        low_value_keywords = [
            '统计摘要', '数据质量', '异常值', '需要核实', '可能的'
        ]

        score = 0.5  # 基础分

        # 高价值关键词加分
        for keyword in high_value_keywords:
            if keyword in text:
                score += 0.2

        # 中等价值关键词加分
        for keyword in medium_value_keywords:
            if keyword in text:
                score += 0.1

        # 低价值关键词减分
        for keyword in low_value_keywords:
            if keyword in text:
                score -= 0.1

        return min(max(score, 0), 1.0)

    def _calculate_data_support(self, description: str) -> float:
        """计算数据支撑度评分"""
        import re

        # 检查是否包含具体数值
        numbers = re.findall(r'\d+(?:\.\d+)?[万亿%元件]?', description)

        # 检查是否包含比较性描述
        comparison_words = ['高于', '低于', '超过', '达到', '占比', '相比', '对比']
        has_comparison = any(word in description for word in comparison_words)

        # 检查是否包含具体范围或区间
        ranges = re.findall(r'\d+-\d+', description)

        score = 0.3  # 基础分

        if numbers:
            score += min(len(numbers) * 0.1, 0.4)  # 数值越多支撑度越高

        if has_comparison:
            score += 0.2

        if ranges:
            score += 0.1

        return min(score, 1.0)

    def _calculate_actionability(self, title: str, description: str) -> float:
        """计算可操作性评分"""
        text = title + " " + description

        # 可操作的关键词
        actionable_keywords = [
            '核心客户群', '目标群体', '重点关注', '优化方向', '改进建议',
            '营销策略', '产品定位', '客户细分'
        ]

        # 不可操作的关键词
        non_actionable_keywords = [
            '需要核实', '待查', '可能的', '统计摘要显示', '数据质量'
        ]

        score = 0.5  # 基础分

        for keyword in actionable_keywords:
            if keyword in text:
                score += 0.2

        for keyword in non_actionable_keywords:
            if keyword in text:
                score -= 0.3

        return min(max(score, 0), 1.0)

    def _calculate_quality_penalty(self, title: str, description: str) -> float:
        """计算质量惩罚分"""
        text = title + " " + description
        penalty = 0

        # 重复性惩罚
        if '总消费' in text and text.count('总消费') > 1:
            penalty += 0.2

        # 模糊性惩罚
        vague_words = ['可能', '或许', '大概', '似乎', '待查', '需要核实']
        for word in vague_words:
            if word in text:
                penalty += 0.1

        # 技术性过强惩罚
        technical_words = ['统计摘要', '异常值检测', '数据质量评分']
        for word in technical_words:
            if word in text:
                penalty += 0.15

        return min(penalty, 0.5)  # 最大惩罚0.5分

    def _deduplicate_similar_insights(self, insights_list: List[dict]) -> List[dict]:
        """智能去重相似洞察"""
        unique_insights = []
        seen_patterns = []  # 🔧 修复：使用列表而不是集合，因为字典不可哈希

        for insight in insights_list:
            # 创建洞察的语义指纹
            fingerprint = self._create_semantic_fingerprint(insight)

            # 检查是否与已有洞察重复
            is_duplicate = False
            for seen_fp in seen_patterns:
                if self._is_similar_insight(fingerprint, seen_fp):
                    is_duplicate = True
                    break

            if not is_duplicate:
                seen_patterns.append(fingerprint)  # 🔧 修复：使用append而不是add
                unique_insights.append(insight)

        return unique_insights

    def _create_semantic_fingerprint(self, insight: dict) -> dict:
        """创建洞察的语义指纹用于智能去重"""
        import re

        title = insight['title'].lower()
        description = insight['description'].lower()
        insight_type = insight['type']

        # 提取数值信息
        numbers = re.findall(r'\d+(?:\.\d+)?', description)

        # 提取关键实体（年龄段、品类等）
        age_groups = re.findall(r'\d+-\d+岁|\d+岁以上|青少年|中年|老年', title + description)
        categories = re.findall(r'手机|电脑|平板|智能穿戴|家电|服装|美妆', title + description)

        # 提取核心概念
        core_concepts = []
        concept_patterns = {
            '消费能力': ['消费', '支付', '购买力', '消费能力'],
            '年龄段': ['年龄', '岁', '群体'],
            '品类偏好': ['品类', '偏好', '喜好', '选择'],
            '异常值': ['异常', '偏低', '偏高', '显著', '突出'],
            '占比': ['占比', '比例', '百分比', '%']
        }

        for concept, keywords in concept_patterns.items():
            if any(keyword in title + description for keyword in keywords):
                core_concepts.append(concept)

        return {
            'type': insight_type,
            'numbers': sorted(numbers),
            'age_groups': sorted(set(age_groups)),
            'categories': sorted(set(categories)),
            'core_concepts': sorted(core_concepts),
            'title_words': sorted(set(title.split())),  # 🔧 修复：转换为排序列表
            'key_phrases': sorted(self._extract_key_phrases(title + description))  # 🔧 修复：转换为排序列表
        }

    def _extract_key_phrases(self, text: str) -> set:
        """提取关键短语"""
        key_phrases = set()

        # 常见的关键短语模式
        phrase_patterns = [
            r'(\d+岁[以上|及以上]*)',
            r'(总消费[金额]*)',
            r'(消费能力[最强|最弱|突出|偏低]*)',
            r'(品类[消费|偏好|分布]*)',
            r'(占比[最高|最低|次之]*)',
            r'(异常[值|高值|低值]*)'
        ]

        import re
        for pattern in phrase_patterns:
            matches = re.findall(pattern, text)
            key_phrases.update(matches)

        return key_phrases

    def _is_similar_insight(self, fp1: dict, fp2: dict) -> bool:
        """判断两个洞察是否相似"""
        # 类型必须相同
        if fp1['type'] != fp2['type']:
            return False

        # 检查核心概念重叠度
        concept_overlap = len(set(fp1['core_concepts']) & set(fp2['core_concepts']))
        concept_total = len(set(fp1['core_concepts']) | set(fp2['core_concepts']))
        concept_similarity = concept_overlap / concept_total if concept_total > 0 else 0

        # 检查年龄段重叠度
        age_overlap = len(set(fp1['age_groups']) & set(fp2['age_groups']))
        age_total = len(set(fp1['age_groups']) | set(fp2['age_groups']))
        age_similarity = age_overlap / age_total if age_total > 0 else 0

        # 检查品类重叠度
        category_overlap = len(set(fp1['categories']) & set(fp2['categories']))
        category_total = len(set(fp1['categories']) | set(fp2['categories']))
        category_similarity = category_overlap / category_total if category_total > 0 else 0

        # 检查关键短语重叠度（现在是列表，需要转换为集合进行操作）
        set1_phrases = set(fp1['key_phrases'])
        set2_phrases = set(fp2['key_phrases'])
        phrase_overlap = len(set1_phrases & set2_phrases)
        phrase_total = len(set1_phrases | set2_phrases)
        phrase_similarity = phrase_overlap / phrase_total if phrase_total > 0 else 0

        # 综合相似度评分
        similarity_score = (
            concept_similarity * 0.4 +
            age_similarity * 0.3 +
            category_similarity * 0.2 +
            phrase_similarity * 0.1
        )

        # 相似度阈值：超过70%认为是重复
        return similarity_score > 0.7

    def _build_progressive_narrative(self, insights_list: List[Any]) -> List[str]:
        """构建递进分析叙述（优化版）"""
        narrative = []
        analysis_chain = []

        for round_idx, insights in enumerate(insights_list):
            round_summary = self._summarize_analysis_round_enhanced(insights, round_idx + 1, analysis_chain)
            if round_summary:
                narrative.append(round_summary)
                # 记录分析链路，用于后续轮次的关联分析
                analysis_chain.append({
                    'round': round_idx + 1,
                    'key_findings': self._extract_round_key_findings(insights),
                    'data_scope': getattr(insights.statistical_summary, 'total_rows', 0) if hasattr(insights, 'statistical_summary') else 0
                })

        return narrative

    def _summarize_analysis_round_enhanced(self, insights, round_num: int, previous_rounds: List[dict]) -> str:
        """增强版单轮分析总结"""
        if not insights or not hasattr(insights, 'statistical_summary'):
            return ""

        stats = insights.statistical_summary

        # 获取本轮最重要的发现
        key_finding = self._get_round_key_finding(insights)

        # 分析与前轮的关联
        connection = self._analyze_round_connection(key_finding, previous_rounds, round_num)

        # 构建叙述
        base_summary = f"第{round_num}轮分析({stats.total_rows}行数据): {key_finding}"

        if connection:
            return f"{base_summary} → {connection}"
        else:
            return base_summary

    def _get_round_key_finding(self, insights) -> str:
        """获取本轮的关键发现"""
        if not hasattr(insights, 'insights') or not insights.insights:
            return "完成数据分析"

        # 按重要性排序洞察
        sorted_insights = sorted(insights.insights,
                               key=lambda x: self._calculate_insight_importance(x),
                               reverse=True)

        top_insight = sorted_insights[0]

        # 根据洞察类型生成更有意义的描述
        if top_insight.insight_type == 'anomaly':
            return f"识别关键异常({top_insight.title})"
        elif top_insight.insight_type == 'trend':
            return f"发现重要趋势({top_insight.title})"
        elif top_insight.insight_type == 'pattern':
            return f"识别核心模式({top_insight.title})"
        elif top_insight.insight_type == 'correlation':
            return f"发现关键关联({top_insight.title})"
        else:
            return f"发现{top_insight.title}"

    def _extract_round_key_findings(self, insights) -> List[str]:
        """提取本轮的关键发现"""
        if not hasattr(insights, 'insights') or not insights.insights:
            return []

        key_findings = []
        for insight in insights.insights[:3]:  # 取前3个重要洞察
            # 提取关键概念
            title = insight.title.lower()
            if '年龄段' in title or '岁' in title:
                key_findings.append('年龄段分析')
            if '消费' in title:
                key_findings.append('消费能力')
            if '品类' in title or '偏好' in title:
                key_findings.append('品类偏好')
            if '异常' in title:
                key_findings.append('异常检测')

        return list(set(key_findings))  # 去重

    def _analyze_round_connection(self, current_finding: str, previous_rounds: List[dict], round_num: int) -> str:
        """分析当前轮次与前轮的关联"""
        if not previous_rounds:
            return "建立分析基础"

        # 分析关联模式
        if round_num == 2:
            return "深化分析维度"
        elif round_num == 3:
            if any('消费能力' in prev['key_findings'] for prev in previous_rounds):
                if '品类' in current_finding:
                    return "验证消费-偏好关联"
                elif '异常' in current_finding:
                    return "识别消费异常"
            return "扩展分析范围"
        elif round_num == 4:
            if len(previous_rounds) >= 2:
                prev_findings = []
                for prev in previous_rounds:
                    prev_findings.extend(prev['key_findings'])

                if '消费能力' in prev_findings and '品类偏好' in prev_findings:
                    return "形成完整客户画像"
                elif '异常检测' in prev_findings:
                    return "完善异常分析"
            return "完善分析结论"
        else:
            return "持续深化分析"

    def _identify_cross_correlations(self, insights_list: List[Any]) -> List[str]:
        """识别跨维度关联（优化版）"""
        correlations = []

        # 收集所有洞察中的关键实体和关系
        entities = self._extract_business_entities(insights_list)

        # 分析业务层面的关联
        business_correlations = self._analyze_business_correlations(entities, insights_list)

        # 收集技术层面的相关性（过滤显而易见的关联）
        technical_correlations = self._collect_filtered_technical_correlations(insights_list)

        # 合并并排序
        all_correlations = business_correlations + technical_correlations

        # 按重要性排序
        sorted_correlations = sorted(all_correlations,
                                   key=lambda x: x.get('importance', 0),
                                   reverse=True)

        # 格式化输出
        for corr in sorted_correlations[:3]:
            correlations.append(corr['description'])

        return correlations

    def _extract_business_entities(self, insights_list: List[Any]) -> dict:
        """提取业务实体"""
        entities = {
            'age_groups': set(),
            'categories': set(),
            'consumption_patterns': set(),
            'anomalies': set()
        }

        for insights in insights_list:
            if hasattr(insights, 'insights') and insights.insights:
                for insight in insights.insights:
                    title = insight.title.lower()
                    description = insight.description.lower()
                    text = title + " " + description

                    # 提取年龄段
                    import re
                    age_groups = re.findall(r'\d+-\d+岁|\d+岁以上', text)
                    entities['age_groups'].update(age_groups)

                    # 提取品类
                    categories = re.findall(r'手机|电脑|平板|智能穿戴', text)
                    entities['categories'].update(categories)

                    # 提取消费模式
                    if '消费' in text:
                        if '高' in text or '强' in text:
                            entities['consumption_patterns'].add('高消费')
                        elif '低' in text or '弱' in text:
                            entities['consumption_patterns'].add('低消费')

                    # 提取异常
                    if insight.insight_type == 'anomaly':
                        entities['anomalies'].add(insight.title)

        return entities

    def _analyze_business_correlations(self, entities: dict, insights_list: List[Any]) -> List[dict]:
        """分析业务层面的关联"""
        correlations = []

        # 年龄段与消费能力的关联
        if entities['age_groups'] and entities['consumption_patterns']:
            high_consumption_ages = self._find_high_consumption_ages(insights_list)
            if high_consumption_ages:
                correlations.append({
                    'description': f"核心消费群体({', '.join(high_consumption_ages)})与高价值品类消费呈强关联",
                    'importance': 0.9
                })

        # 年龄段与品类偏好的关联
        age_category_patterns = self._find_age_category_patterns(insights_list)
        if age_category_patterns:
            correlations.append({
                'description': f"年龄段与品类偏好呈现明显分化：{age_category_patterns}",
                'importance': 0.8
            })

        return correlations

    def _find_high_consumption_ages(self, insights_list: List[Any]) -> List[str]:
        """找出高消费年龄段"""
        high_consumption_ages = []

        for insights in insights_list:
            if hasattr(insights, 'insights') and insights.insights:
                for insight in insights.insights:
                    text = insight.title + " " + insight.description
                    if '消费能力' in text and ('强' in text or '高' in text or '最' in text):
                        import re
                        ages = re.findall(r'\d+-\d+岁', text)
                        high_consumption_ages.extend(ages)

        return list(set(high_consumption_ages))

    def _find_age_category_patterns(self, insights_list: List[Any]) -> str:
        """找出年龄段与品类的关联模式"""
        patterns = []

        for insights in insights_list:
            if hasattr(insights, 'insights') and insights.insights:
                for insight in insights.insights:
                    text = insight.title + " " + insight.description
                    if '品类' in text or '偏好' in text:
                        import re
                        ages = re.findall(r'\d+-\d+岁', text)
                        categories = re.findall(r'手机|电脑|平板|智能穿戴', text)

                        if ages and categories:
                            patterns.append(f"{ages[0]}偏好{categories[0]}")

        return "、".join(patterns[:2]) if patterns else ""

    def _collect_filtered_technical_correlations(self, insights_list: List[Any]) -> List[dict]:
        """收集过滤后的技术相关性"""
        correlations = []

        for insights in insights_list:
            if hasattr(insights, 'correlations') and insights.correlations:
                for corr in insights.correlations:
                    correlation_value = abs(corr.get('correlation', 0))
                    if correlation_value > 0.7:
                        col1 = corr.get('column1', '')
                        col2 = corr.get('column2', '')

                        # 过滤掉显而易见的关联
                        if not self._is_obvious_correlation(col1, col2):
                            strength = corr.get('strength', 'strong')
                            correlation_pct = int(correlation_value * 100)
                            correlations.append({
                                'description': f"{col1} 与 {col2} 呈{strength}相关 ({correlation_pct}%)",
                                'importance': correlation_value * 0.6
                            })

        return correlations

    def _is_obvious_correlation(self, var1: str, var2: str) -> bool:
        """判断是否是显而易见的关联"""
        obvious_pairs = [
            ('total', 'percentage'),
            ('consumption', 'amount'),
            ('count', 'total'),
            ('sum', 'total')
        ]

        var1_lower = var1.lower()
        var2_lower = var2.lower()

        for pair in obvious_pairs:
            if (pair[0] in var1_lower and pair[1] in var2_lower) or \
               (pair[1] in var1_lower and pair[0] in var2_lower):
                return True

        return False

    def _calculate_overall_quality_assessment(self, insights_list: List[Any]) -> dict:
        """计算整体数据质量评估"""
        quality_scores = []
        completeness_scores = []
        consistency_scores = []

        for insights in insights_list:
            if hasattr(insights, 'data_quality_assessment') and insights.data_quality_assessment:
                quality = insights.data_quality_assessment
                quality_scores.append(quality.overall_score)
                completeness_scores.append(quality.completeness_score)
                consistency_scores.append(quality.consistency_score)

        if not quality_scores:
            return {'overall_score': 0.0, 'assessment': '无数据质量评估'}

        avg_quality = sum(quality_scores) / len(quality_scores)
        avg_completeness = sum(completeness_scores) / len(completeness_scores)
        avg_consistency = sum(consistency_scores) / len(consistency_scores)

        # 质量等级评估
        if avg_quality >= 0.9:
            assessment = '优秀'
        elif avg_quality >= 0.8:
            assessment = '良好'
        elif avg_quality >= 0.7:
            assessment = '一般'
        else:
            assessment = '需要改进'

        return {
            'overall_score': avg_quality,
            'completeness_score': avg_completeness,
            'consistency_score': avg_consistency,
            'assessment': assessment
        }

    def _aggregate_critical_anomalies(self, insights_list: List[Any]) -> List[str]:
        """聚合关键异常"""
        all_anomalies = []

        for insights in insights_list:
            if hasattr(insights, 'anomalies') and insights.anomalies:
                all_anomalies.extend(insights.anomalies)

        # 去重并按严重性排序
        unique_anomalies = list(set(all_anomalies))

        # 按关键词优先级排序
        priority_keywords = ['零', '异常值', '缺失', '错误', '不一致']

        def anomaly_priority(anomaly):
            for i, keyword in enumerate(priority_keywords):
                if keyword in anomaly:
                    return i
            return len(priority_keywords)

        sorted_anomalies = sorted(unique_anomalies, key=anomaly_priority)

        return sorted_anomalies[:3]  # 最多返回3个最关键的异常

    def _build_structured_insights_presentation(self, aggregated_insights: dict, user_language: str = 'zh-CN') -> str:
        """构建结构化的洞察呈现

        Args:
            aggregated_insights: 聚合后的洞察数据
            user_language: 用户语言偏好

        Returns:
            str: 结构化的洞察呈现文本
        """
        # 根据语言选择标题
        if user_language == 'en-US':
            section_title = "**Professional Data Analysis Insights:**"
            overview_title = "Analysis Overview"
            key_insights_title = "Key Findings"
            progressive_title = "Progressive Analysis"
            correlations_title = "Cross-dimensional Correlations"
            quality_title = "Overall Data Quality"
            anomalies_title = "Critical Anomalies"
        else:
            section_title = "**专业数据分析洞察：**"
            overview_title = "📊 分析概览"
            key_insights_title = "🔍 核心发现"
            progressive_title = "📈 递进分析"
            correlations_title = "🔗 跨维度关联"
            quality_title = "📋 整体数据质量"
            anomalies_title = "⚠️ 关键异常"

        sections = [section_title]

        # 1. 分析概览
        overview = aggregated_insights.get('analysis_overview', {})
        if overview:
            sections.append(f"\n**{overview_title}:**")
            sections.append(f"- 分析轮次: {overview.get('analysis_rounds', 0)}轮")
            sections.append(f"- 数据覆盖: {overview.get('total_data_points', 0)}个数据点")
            sections.append(f"- 涉及字段: {overview.get('unique_columns', 0)}个")
            sections.append(f"- 分析范围: {overview.get('analysis_scope', '未知')}")

        # 2. 核心发现（去重排序后的关键洞察）
        key_insights = aggregated_insights.get('key_insights', [])
        if key_insights:
            sections.append(f"\n**{key_insights_title}:**")
            for i, insight in enumerate(key_insights, 1):
                confidence_pct = int(insight['confidence'] * 100)
                sections.append(f"{i}. [{insight['type']}] {insight['title']}: {insight['description']} (置信度: {confidence_pct}%)")

        # 3. 递进分析发现
        progressive = aggregated_insights.get('progressive_findings', [])
        if progressive:
            sections.append(f"\n**{progressive_title}:**")
            for finding in progressive:
                sections.append(f"- {finding}")

        # 4. 跨维度关联
        correlations = aggregated_insights.get('cross_correlations', [])
        if correlations:
            sections.append(f"\n**{correlations_title}:**")
            for correlation in correlations:
                sections.append(f"- {correlation}")

        # 5. 整体数据质量
        quality = aggregated_insights.get('overall_quality', {})
        if quality and quality.get('overall_score', 0) > 0:
            sections.append(f"\n**{quality_title}:**")
            sections.append(f"- 综合评分: {quality.get('overall_score', 0):.2f} ({quality.get('assessment', '未知')})")
            sections.append(f"- 完整性: {quality.get('completeness_score', 0):.2f}")
            sections.append(f"- 一致性: {quality.get('consistency_score', 0):.2f}")

        # 6. 关键异常
        anomalies = aggregated_insights.get('critical_anomalies', [])
        if anomalies:
            sections.append(f"\n**{anomalies_title}:**")
            for anomaly in anomalies:
                sections.append(f"- ⚠️ {anomaly}")

        # 添加智能分析指导
        if len(sections) > 1:
            sections.append(f"\n**💡 智能分析指导：**")
            sections.append("以上洞察已经过智能聚合和去重处理，突出了最具业务价值的发现。请在报告中：")
            sections.append("1. 优先关注核心发现中的高置信度洞察")
            sections.append("2. 结合递进分析展示数据探索的逻辑脉络")
            sections.append("3. 利用跨维度关联发现业务机会或风险点")
            sections.append("4. 基于数据质量评估调整分析结论的可信度")

            return "\n".join(sections)

        return ""

    async def _call_llm_for_text_response(self, prompt: str, system_prompt: str = None, temperature: float = 0.3, user_language: str = 'zh-CN') -> tuple[str, dict]:
        """调用LLM生成文本响应"""
        try:
            # 使用传入的系统提示词，如果没有则使用默认值
            if system_prompt is None:
                system_prompt = "你是一个乐于助人的AI助手，负责生成用户友好的文本回复。"
            
            # 根据用户语言偏好添加语言指令
            language_instruction = ""
            if user_language == 'en-US':
                language_instruction = "\n\nIMPORTANT: Please respond in English."

            else:
                language_instruction = "\n\n重要：请使用中文回复。"
            
            system_prompt = system_prompt + language_instruction
            
            log.info("正在调用LLM生成文本响应...")
            
            # 记录完整的Prompt用于调试分析
            log.info("=" * 80)
            log.info("📝 ReportGenerator LLM调用 - 系统提示词:")
            log.info("=" * 80)
            log.info(system_prompt)
            log.info("=" * 80)
            log.info("📝 ReportGenerator LLM调用 - 用户提示词:")
            log.info("=" * 80)
            
            # 检查LLM服务是否有直接的文本生成方法
            if hasattr(self.llm_service, 'generate_text'):
                # 如果有generate_text方法，使用它
                response = await self.llm_service.generate_text(
                    prompt=prompt,
                    system_prompt=system_prompt,
                    temperature=temperature
                )
                return response, {}
            elif hasattr(self.llm_service, 'client'):
                # 如果有client属性，直接使用它 (OpenAIService使用client而不是llm_client)
                completion = await self.llm_service.client.chat.completions.create(
                    model=self.llm_service.model,
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": prompt}
                    ],
                    temperature=temperature
                )
                
                response_content = completion.choices[0].message.content
                usage_data = completion.usage.model_dump() if completion.usage else {}
                log.debug(f"LLM生成的文本响应: {response_content}")
                return response_content, usage_data
            else:
                # 回退方案：如果LLM服务没有合适的方法，抛出异常
                raise AttributeError("LLM服务既没有generate_text方法，也没有client属性，无法生成报告")
            
        except Exception as e:
            log.error(f"LLM调用失败: {str(e)}")
            error_msg = f"调用LLM (文本回复模式) 时出错: {str(e)}"
            log.error(error_msg)
            
            # 返回合理的默认响应，而不是抛出异常
            return "抱歉，在生成分析报告时遇到了技术问题。请尝试重新提问或简化您的查询。如果问题持续，请联系系统管理员。", {}

    def _strip_markdown_markers(self, text: str) -> str:
        """移除markdown标记"""
        if not text:
            return ""
        
        # 移除常见的markdown标记
        text = text.strip()
        
        # 移除markdown代码块标记
        if text.startswith('```') and text.endswith('```'):
            lines = text.split('\n')
            if len(lines) > 2:
                # 移除首尾的```行
                text = '\n'.join(lines[1:-1])
        
        return text

    def _extract_chart_info_simple(self, context) -> str:
        """简化的图表信息提取 - 直接从数据库查询，不需要复杂判断
        
        Args:
            context: 执行上下文
            
        Returns:
            str: 格式化的图表信息描述
        """
        if not hasattr(context, 'analysis_id') or not context.analysis_id:
            return ""
        
        try:
            # 直接查询数据库中的可视化信息（包括图表和表格）
            visualization_info_list = self._query_visualizations_simple(context.analysis_id)
            
            if not visualization_info_list:
                return ""
            
            # 分离图表和表格信息
            chart_descriptions = []
            table_descriptions = []
            
            for vis_meta in visualization_info_list:
                vis_id = vis_meta['vis_id']
                vis_description = vis_meta.get('vis_description', '')
                vis_type = vis_meta.get('vis_type', 'chart')
                
                if vis_type == 'table':
                    table_descriptions.append(f"**{vis_id}**: {vis_description}")
                else:
                    chart_descriptions.append(f"**{vis_id}**: {vis_description}")
            
            # 构建完整的可视化信息
            sections = []
            
            if chart_descriptions:
                sections.append(f"""**可用的数据图表：**

{chr(10).join([f"- {desc}" for desc in chart_descriptions])}

**📊 图表插入指南：**
- 格式：[CHART_PLACEHOLDER:图表ID]
- 例如：[CHART_PLACEHOLDER:chart_1]
- 请在描述相关数据时自然插入对应图表""")
            
            if table_descriptions:
                sections.append(f"""**可用的数据表格：**

{chr(10).join([f"- {desc}" for desc in table_descriptions])}

**📋 表格插入指南：**
- 格式：[TABLE_PLACEHOLDER:表格ID]
- 例如：[TABLE_PLACEHOLDER:table_1]
- 请在需要展示详细数据时插入对应表格""")
            
            return "\n\n".join(sections)
                
        except Exception as e:
            log.warning(f"提取图表信息失败: {str(e)}")
            return ""

    def _query_visualizations_simple(self, analysis_id: str) -> List[dict]:
        """简化的可视化查询 - 直接从数据库查询标准化的可视化数据（图表+表格）
        
        Args:
            analysis_id: 分析ID
            
        Returns:
            List[dict]: 可视化信息列表
        """
        db = None
        try:
            db = SessionLocal()
            
            # 查询有可视化数据的步骤 - 放宽查询条件
            vis_steps = db.query(AnalysisStepDetail).filter(
                AnalysisStepDetail.analysis_id == analysis_id,
                AnalysisStepDetail.has_chart_data == 1
                # 移除 interaction_type 限制，因为可能有不同的类型
            ).order_by(
                AnalysisStepDetail.created_at.asc(),
                AnalysisStepDetail.step_order.asc()
            ).all()

            log.info(f"🔍 查询到 {len(vis_steps)} 个有图表数据的步骤")
            
            vis_info_list = []
            chart_counter = 0
            table_counter = 0
            
            for step in vis_steps:
                # 从interaction_data中获取数据
                interaction_data = step.interaction_data or {}
                chart_data = interaction_data.get('chart_data')

                log.info(f"🔍 处理步骤 {step.step_id}: interaction_type={step.interaction_type}, has_chart_data={step.has_chart_data}")
                if chart_data:
                    log.info(f"🔍 图表数据格式: {chart_data.get('display_format')}")
                else:
                    log.warning(f"🔍 步骤 {step.step_id} 没有图表数据")
                
                if chart_data:
                    # 检查是否为多图表格式 - 统一数据结构访问路径
                    charts_list = None
                    if chart_data.get('display_format') == 'multi_chart':
                        # 尝试两种可能的数据结构路径
                        if chart_data.get('charts'):
                            charts_list = chart_data['charts']
                        elif chart_data.get('data', {}).get('charts'):
                            charts_list = chart_data['data']['charts']
                    
                    if charts_list:
                        # 多图表数据 - 解析每个子图表
                        log.info(f"🎨 检测到多图表数据，包含 {len(charts_list)} 个图表")
                        for chart in charts_list:
                            if chart.get('chart_type') == 'table':
                                # 表格
                                table_counter += 1
                                vis_id = f"table_{table_counter}"
                                vis_type = "table"
                                vis_description = f"基于{step.tool_name or '数据分析'}的数据表格"
                                if chart.get('title'):
                                    vis_description = f"基于{step.tool_name or '数据分析'}的{chart['title']}"
                            else:
                                # 图表
                                chart_counter += 1
                                vis_id = f"chart_{chart_counter}"
                                vis_type = "chart"
                                chart_title = chart.get('title', '图表')
                                vis_description = f"基于{step.tool_name or '数据分析'}的{chart_title}"

                            vis_info_list.append({
                                'vis_id': vis_id,
                                'vis_type': vis_type,
                                'vis_description': vis_description,
                                'step_id': step.step_id,
                                'created_at': step.created_at.isoformat() if step.created_at else None,
                                'chart_id': chart.get('chart_id'),  # 保存原始图表ID
                                'chart_title': chart.get('title')   # 保存图表标题
                            })
                    else:
                        # 单图表或表格数据
                        if self._is_table_data(chart_data):
                            # 表格数据
                            table_counter += 1
                            vis_id = f"table_{table_counter}"
                            vis_type = "table"
                            vis_description = f"基于{step.tool_name or '数据分析'}的数据表格"
                        else:
                            # 图表数据
                            chart_counter += 1
                            vis_id = f"chart_{chart_counter}"
                            vis_type = "chart"
                            chart_type = interaction_data.get('chart_type', '图表')
                            vis_description = f"基于{step.tool_name or '数据分析'}的{chart_type}"

                        vis_info_list.append({
                            'vis_id': vis_id,
                            'vis_type': vis_type,
                            'vis_description': vis_description,
                            'step_id': step.step_id,
                            'created_at': step.created_at.isoformat() if step.created_at else None
                        })
                    
            log.info(f"🎨 查询到 {len(vis_info_list)} 个可视化数据 (图表: {chart_counter}, 表格: {table_counter})")
            return vis_info_list
            
        except Exception as e:
            log.warning(f"查询可视化信息失败: {str(e)}")
            return []
        finally:
            if db:
                db.close()

    def _is_table_data(self, data: dict) -> bool:
        """检查数据是否是表格数据

        Args:
            data: 要检查的数据

        Returns:
            bool: 如果是表格数据返回True
        """
        if not isinstance(data, dict):
            return False

        # 辅助函数：检查单个数据对象是否为表格
        def _check_single_data(data_obj):
            if not isinstance(data_obj, dict):
                return False

            # 检查是否有render_type: "table"
            if data_obj.get('render_type') == 'table':
                return True
            # 检查是否有table_config字段
            if 'table_config' in data_obj:
                return True
            # 检查是否有display_format: "table"
            if data_obj.get('display_format') == 'table':
                return True
            # 检查传统表格结构 (columns + dataSource)
            if 'columns' in data_obj and 'dataSource' in data_obj:
                if isinstance(data_obj['columns'], list) and isinstance(data_obj['dataSource'], list):
                    return True
            # 检查新的MoE多图表格式中的表格
            if data_obj.get('total_charts') and data_obj.get('charts'):
                for chart in data_obj['charts']:
                    if chart.get('chart_type') == 'table':
                        return True

            return False

        # 1. 检查直接的表格结构
        if _check_single_data(data):
            log.debug(f"📊 检测到直接表格数据")
            return True

        # 2. 检查 data 字段内的表格结构
        if 'data' in data and isinstance(data['data'], dict):
            if _check_single_data(data['data']):
                log.debug(f"📊 检测到data内的表格数据")
                return True

        # 2.5. 检查 chart_data.data 结构（MoE格式）
        if 'chart_data' in data and isinstance(data['chart_data'], dict) and 'data' in data['chart_data']:
            if _check_single_data(data['chart_data']['data']):
                log.debug(f"📊 检测到chart_data.data内的表格数据")
                return True

        # 3. 检查嵌套结构 (类似图表的 result.result.result)
        # 这是为了处理新的数据结构变化
        nested_paths = [
            ['result', 'result', 'result'],
            ['result', 'result'],
            ['result']
        ]

        for path in nested_paths:
            current_data = data
            for key in path:
                if isinstance(current_data, dict) and key in current_data:
                    current_data = current_data[key]
                else:
                    current_data = None
                    break

            if current_data and _check_single_data(current_data):
                log.debug(f"📊 检测到嵌套表格数据 (路径: {'.'.join(path)})")
                return True

        # 4. 检查嵌套结构内的 data 字段
        for path in nested_paths:
            current_data = data
            for key in path:
                if isinstance(current_data, dict) and key in current_data:
                    current_data = current_data[key]
                else:
                    current_data = None
                    break

            if current_data and isinstance(current_data, dict) and 'data' in current_data:
                if _check_single_data(current_data['data']):
                    log.debug(f"📊 检测到嵌套data内的表格数据 (路径: {'.'.join(path)}.data)")
                    return True

        return False

    def _extract_table_data(self, data: dict) -> dict:
        """从复杂的数据结构中提取表格数据

        Args:
            data: 原始数据

        Returns:
            dict: 提取的表格数据
        """
        if not isinstance(data, dict):
            return data

        # 辅助函数：检查并提取单个数据对象的表格数据
        def _extract_single_table_data(data_obj):
            if not isinstance(data_obj, dict):
                return None

            # 如果有table_config，优先使用
            if 'table_config' in data_obj:
                return {
                    'data': {
                        'table_config': data_obj['table_config'],
                        'title': data_obj.get('title', '数据表格'),
                        'render_type': 'table',
                        'display_format': 'table'
                    }
                }

            # 如果是传统格式 (columns + dataSource)
            if 'columns' in data_obj and 'dataSource' in data_obj:
                return {
                    'data': {
                        'table_config': {
                            'columns': data_obj['columns'],
                            'dataSource': data_obj['dataSource']
                        },
                        'title': data_obj.get('title', '数据表格'),
                        'render_type': 'table',
                        'display_format': 'table'
                    }
                }

            # 如果是新的MoE多图表格式中的表格
            if data_obj.get('total_charts') and data_obj.get('charts'):
                for chart in data_obj['charts']:
                    if chart.get('chart_type') == 'table':
                        return {
                            'data': {
                                'table_config': chart.get('config', {}),
                                'title': chart.get('title', '数据表格'),
                                'render_type': 'table',
                                'display_format': 'table'
                            }
                        }

            # 如果已经是标准格式，直接返回
            if data_obj.get('render_type') == 'table' or data_obj.get('display_format') == 'table':
                return {'data': data_obj}

            return None

        # 1. 检查直接的表格结构
        result = _extract_single_table_data(data)
        if result:
            log.debug(f"📊 从直接结构提取表格数据")
            return result

        # 2. 检查 data 字段内的表格结构
        if 'data' in data and isinstance(data['data'], dict):
            result = _extract_single_table_data(data['data'])
            if result:
                log.debug(f"📊 从data字段提取表格数据")
                return result

        # 2.5. 检查 chart_data.data 结构（MoE格式）
        if 'chart_data' in data and isinstance(data['chart_data'], dict) and 'data' in data['chart_data']:
            result = _extract_single_table_data(data['chart_data']['data'])
            if result:
                log.debug(f"📊 从chart_data.data字段提取表格数据")
                return result

        # 3. 检查嵌套结构 (类似图表的 result.result.result)
        nested_paths = [
            ['result', 'result', 'result'],
            ['result', 'result'],
            ['result']
        ]

        for path in nested_paths:
            current_data = data
            for key in path:
                if isinstance(current_data, dict) and key in current_data:
                    current_data = current_data[key]
                else:
                    current_data = None
                    break

            if current_data:
                result = _extract_single_table_data(current_data)
                if result:
                    log.debug(f"📊 从嵌套结构提取表格数据 (路径: {'.'.join(path)})")
                    return result

        # 4. 检查嵌套结构内的 data 字段
        for path in nested_paths:
            current_data = data
            for key in path:
                if isinstance(current_data, dict) and key in current_data:
                    current_data = current_data[key]
                else:
                    current_data = None
                    break

            if current_data and isinstance(current_data, dict) and 'data' in current_data:
                result = _extract_single_table_data(current_data['data'])
                if result:
                    log.debug(f"📊 从嵌套data字段提取表格数据 (路径: {'.'.join(path)}.data)")
                    return result

        # 如果都没找到，返回原始数据
        log.debug(f"📊 未找到标准表格结构，返回原始数据")
        return data

    def _query_charts_simple(self, analysis_id: str) -> List[dict]:
        """简化的图表查询 - 直接从数据库查询标准化的图表数据
        
        Args:
            analysis_id: 分析ID
            
        Returns:
            List[dict]: 图表信息列表
        """
        db = None
        try:
            db = SessionLocal()
            
            # 查询有图表数据的步骤
            chart_steps = db.query(AnalysisStepDetail).filter(
                AnalysisStepDetail.analysis_id == analysis_id,
                AnalysisStepDetail.has_chart_data == 1,
                AnalysisStepDetail.interaction_type == 'chart_display'
            ).order_by(
                AnalysisStepDetail.created_at.asc(),
                AnalysisStepDetail.step_order.asc()
            ).all()
            
            chart_info_list = []
            chart_counter = 0  # 基于实际图表数量的计数器
            
            for step in chart_steps:
                # 从interaction_data中提取图表信息
                interaction_data = step.interaction_data or {}
                chart_data = interaction_data.get('chart_data', {})
                
                # BUG修复: 处理图表生成失败导致的chart_id不连续问题
                # 修复策略: 基于实际存在的图表数据分配连续的chart_id
                # 影响范围: app/services/report_generator.py:1573-1590
                # 修复日期: 2025-01-29
                
                if not chart_data:
                    log.warning(f"步骤 {step.step_id} 缺少图表数据，跳过")
                    continue
                
                # 检查是否为多图表格式
                if chart_data.get('display_format') == 'multi_chart' and chart_data.get('data', {}).get('charts'):
                    # 多图表数据 - 为每个子图表和表格分配连续的ID
                    charts = chart_data['data']['charts']
                    for chart in charts:
                        chart_counter += 1
                        
                        # BUG修复: 表格也要参与报告生成
                        # 修复策略: 为图表和表格统一分配chart_id，不再区分类型
                        # 影响范围: app/services/report_generator.py:1590-1622
                        # 修复日期: 2025-01-29
                        if chart.get('chart_type') == 'table':
                            chart_id = f"chart_{chart_counter}"
                            chart_description = f"基于{step.tool_name or '智能图表生成'}的数据表格"
                        else:
                            chart_id = f"chart_{chart_counter}"
                            chart_description = f"基于{step.tool_name or '智能图表生成'}的{chart.get('chart_type', 'echarts')}"
                        
                        chart_info_list.append({
                            'chart_id': chart_id,
                            'chart_description': chart_description,
                            'step_id': step.step_id,
                            'original_chart_id': chart.get('chart_id'),  # 保存原始chart_id用于数据查询
                            'chart_type': chart.get('chart_type', 'echarts'),  # 保存类型信息
                            'created_at': step.created_at.isoformat() if step.created_at else None
                        })
                        log.info(f"🎨 多图表中添加{chart.get('chart_type', '图表')}: {chart_id} (原始: {chart.get('chart_id')})")
                else:
                    # 单图表或表格数据
                    chart_counter += 1
                    chart_id = f"chart_{chart_counter}"
                    chart_type = interaction_data.get('chart_type', '图表')
                    
                    if chart_type == 'table':
                        chart_description = f"基于{step.tool_name or '数据分析'}的数据表格"
                    else:
                        chart_description = f"基于{step.tool_name or '数据分析'}的{chart_type}"
                    
                    chart_info_list.append({
                        'chart_id': chart_id,
                        'chart_description': chart_description,
                        'step_id': step.step_id,
                        'chart_type': chart_type,  # 保存类型信息
                        'created_at': step.created_at.isoformat() if step.created_at else None
                    })
                    log.info(f"🎨 单{chart_type}添加: {chart_id}")
                
            log.info(f"🎨 查询到 {len(chart_info_list)} 个图表")
            return chart_info_list
            
        except Exception as e:
            log.warning(f"查询图表信息失败: {str(e)}")
            return []
        finally:
            if db:
                db.close()

    def _replace_chart_placeholders(self, report_content: str, context) -> str:
        """简化的可视化占位符替换 - 直接查询数据库获取图表和表格数据

        Args:
            report_content: 包含占位符的报告内容
            context: 执行上下文

        Returns:
            str: 替换后的报告内容
        """
        if not report_content or not hasattr(context, 'analysis_id') or not context.analysis_id:
            return report_content or ""
        
        try:
            log.info(f"🔥 开始替换报告中的图表和表格占位符")
            log.info(f"   - 分析ID: {context.analysis_id}")
            log.info(f"   - 报告内容长度: {len(report_content)} 字符")

            # 查找图表和表格占位符
            chart_placeholder_pattern = r'\[CHART_PLACEHOLDER:(chart_\d+)\]'
            table_placeholder_pattern = r'\[TABLE_PLACEHOLDER:(table_\d+)\]'

            chart_placeholders = re.findall(chart_placeholder_pattern, report_content)
            table_placeholders = re.findall(table_placeholder_pattern, report_content)

            log.info(f"🔍 占位符检索结果:")
            log.info(f"   - 图表占位符: {len(chart_placeholders)} 个 {chart_placeholders}")
            log.info(f"   - 表格占位符: {len(table_placeholders)} 个 {table_placeholders}")

            if not chart_placeholders and not table_placeholders:
                log.info(f"⚠️ 报告中没有找到任何占位符，直接返回原始内容")
                return report_content
            
            # 直接从数据库查询可视化数据
            all_placeholders = chart_placeholders + table_placeholders
            visualization_data_map = self._query_visualization_data_simple(context.analysis_id, all_placeholders)
            
            # 替换占位符
            updated_content = report_content
            
            # 替换图表占位符
            log.info(f"🎨 开始替换图表占位符...")
            for chart_id in chart_placeholders:
                placeholder = f"[CHART_PLACEHOLDER:{chart_id}]"
                log.info(f"🎨 处理图表占位符: {placeholder}")

                if chart_id in visualization_data_map:
                    chart_data = visualization_data_map[chart_id]
                    log.info(f"   - ✅ 找到图表数据: {chart_id}")
                    log.info(f"   - 数据类型: {type(chart_data)}")

                    try:
                        chart_json = json.dumps(chart_data, ensure_ascii=False, indent=2, cls=DecimalEncoder)
                        log.info(f"   - JSON序列化成功，长度: {len(chart_json)} 字符")

                        chart_block = f"""
```chart
{chart_json}
```
"""
                        updated_content = updated_content.replace(placeholder, chart_block)
                        log.info(f"🎨 ✅ 成功替换图表占位符: {chart_id}")
                    except Exception as e:
                        log.error(f"   - ❌ JSON序列化失败: {str(e)}")
                        updated_content = updated_content.replace(placeholder, f"*[图表 {chart_id} 序列化失败: {str(e)}]*")
                else:
                    log.warning(f"🎨 ❌ 图表数据不存在: {chart_id}")
                    log.warning(f"   - 可用的数据: {list(visualization_data_map.keys())}")
                    updated_content = updated_content.replace(placeholder, f"*[图表 {chart_id} 数据不可用]*")
            
            # 替换表格占位符
            log.info(f"📊 开始替换表格占位符...")
            for table_id in table_placeholders:
                placeholder = f"[TABLE_PLACEHOLDER:{table_id}]"
                log.info(f"📊 处理表格占位符: {placeholder}")

                if table_id in visualization_data_map:
                    table_data = visualization_data_map[table_id]
                    log.info(f"   - ✅ 找到表格数据: {table_id}")
                    log.info(f"   - 数据类型: {type(table_data)}")

                    try:
                        table_json = json.dumps(table_data, ensure_ascii=False, indent=2, cls=DecimalEncoder)
                        log.info(f"   - JSON序列化成功，长度: {len(table_json)} 字符")

                        table_block = f"""
```table
{table_json}
```
"""
                        updated_content = updated_content.replace(placeholder, table_block)
                        log.info(f"📊 ✅ 成功替换表格占位符: {table_id}")
                    except Exception as e:
                        log.error(f"   - ❌ JSON序列化失败: {str(e)}")
                        updated_content = updated_content.replace(placeholder, f"*[表格 {table_id} 序列化失败: {str(e)}]*")
                else:
                    log.warning(f"📊 ❌ 表格数据不存在: {table_id}")
                    log.warning(f"   - 可用的数据: {list(visualization_data_map.keys())}")
                    updated_content = updated_content.replace(placeholder, f"*[表格 {table_id} 数据不可用]*")
            
            log.info(f"🔥 占位符替换完成!")
            log.info(f"   - 原始内容长度: {len(report_content)} 字符")
            log.info(f"   - 替换后长度: {len(updated_content)} 字符")
            log.info(f"   - 长度变化: {len(updated_content) - len(report_content):+d} 字符")

            return updated_content

        except Exception as e:
            log.error(f"🎨 替换占位符失败: {str(e)}")
            log.error(f"异常详情: {traceback.format_exc()}")
            return report_content

    def _query_visualization_data_simple(self, analysis_id: str, visualization_ids: List[str]) -> Dict[str, Any]:
        """简化的可视化数据查询 - 直接从数据库获取标准化的可视化数据（图表+表格）
        
        Args:
            analysis_id: 分析ID
            visualization_ids: 可视化ID列表（如：['chart_1', 'table_1']）
            
        Returns:
            Dict[str, Any]: 可视化数据映射
        """
        db = None
        try:
            db = SessionLocal()
            
            # BUG修复: 恢复interaction_type限制，确保与_query_charts_simple方法一致
            # 修复策略: 两个查询方法必须使用相同的条件，否则会导致图表信息和数据不匹配
            # 影响范围: app/services/report_generator.py:1728-1732
            # 修复日期: 2025-01-29
            vis_steps = db.query(AnalysisStepDetail).filter(
                AnalysisStepDetail.analysis_id == analysis_id,
                AnalysisStepDetail.has_chart_data == 1,
                AnalysisStepDetail.interaction_type == 'chart_display'
            ).order_by(
                AnalysisStepDetail.created_at.asc(),
                AnalysisStepDetail.step_order.asc()
            ).all()
            
            visualization_data_map = {}
            chart_counter = 0  # 统一计数器，不再区分图表和表格
            
            log.info(f"🔍 开始检索可视化数据，共找到 {len(vis_steps)} 个可视化步骤")

            for step in vis_steps:
                # 从interaction_data中获取数据
                interaction_data = step.interaction_data or {}
                chart_data = interaction_data.get('chart_data')

                log.info(f"🔍 检查步骤 {step.step_id} (step_order: {step.step_order})")
                log.info(f"   - interaction_type: {step.interaction_type}")
                log.info(f"   - has_chart_data: {step.has_chart_data}")
                log.info(f"   - chart_data存在: {chart_data is not None}")

                if chart_data:
                    log.info(f"   - chart_data类型: {type(chart_data)}")
                    log.info(f"   - chart_data键: {list(chart_data.keys()) if isinstance(chart_data, dict) else 'N/A'}")
                    
                    # 检查是否为多图表格式 - 统一数据结构访问路径
                    charts_list = None
                    if chart_data.get('display_format') == 'multi_chart':
                        log.info(f"🔍 检测到多图表格式，开始查找charts数据")
                        log.info(f"   - chart_data.get('charts'): {chart_data.get('charts') is not None}")
                        log.info(f"   - chart_data.get('data'): {chart_data.get('data') is not None}")
                        if chart_data.get('data'):
                            log.info(f"   - chart_data['data'].get('charts'): {chart_data['data'].get('charts') is not None}")
                        
                        # 尝试两种可能的数据结构路径
                        if chart_data.get('charts'):
                            charts_list = chart_data['charts']
                            log.info(f"✅ 使用直接路径找到charts数据: {len(charts_list)} 个")
                        elif chart_data.get('data', {}).get('charts'):
                            charts_list = chart_data['data']['charts']
                            log.info(f"✅ 使用嵌套路径找到charts数据: {len(charts_list)} 个")
                        else:
                            log.warning(f"❌ 未找到charts数据，多图表格式但无charts内容")
                    
                    if charts_list:
                        # 多图表数据 - 解析每个子图表
                        log.info(f"🎨 检测到多图表数据，包含 {len(charts_list)} 个图表")
                        
                        for chart in charts_list:
                            # BUG修复: 统一处理图表和表格，使用相同的计数器
                            # 修复策略: 表格和图表都使用chart_id，保持与信息查询的一致性
                            # 影响范围: app/services/report_generator.py:1810-1841
                            # 修复日期: 2025-01-29
                            chart_counter += 1
                            chart_id = f"chart_{chart_counter}"
                            log.info(f"   - 分配ID: {chart_id} (类型: {chart.get('chart_type', '图表')})")
                            log.info(f"   - 是否在请求列表中: {chart_id in visualization_ids}")
                            
                            if chart_id in visualization_ids:
                                if chart.get('chart_type') == 'table':
                                    # 构建表格数据结构
                                    chart_data_structure = {
                                        'data': {
                                            'table_config': chart.get('config', {}),
                                            'title': chart.get('title', '数据表格'),
                                            'render_type': 'table',
                                            'display_format': 'table'
                                        }
                                    }
                                    visualization_data_map[chart_id] = chart_data_structure
                                    log.info(f"📊 ✅ 成功获取多图表中的表格数据: {chart_id}")
                                else:
                                    # 构建图表数据结构
                                    chart_data_structure = {
                                        'data': {
                                            'chart_config': chart.get('config', {}),
                                            'title': chart.get('title', '图表'),
                                            'render_type': 'chart',
                                            'display_format': 'chart'
                                        }
                                    }
                                    visualization_data_map[chart_id] = chart_data_structure
                                    log.info(f"🎨 ✅ 成功获取多图表中的图表数据: {chart_id}")
                            else:
                                log.info(f"🎨 ⏭️ 跳过{chart.get('chart_type', '图表')}数据: {chart_id} (不在请求列表中)")
                    else:
                        # 单图表或表格数据 - 原有逻辑
                        is_table = self._is_table_data(chart_data)
                        log.info(f"   - 是否为表格数据: {is_table}")

                        # BUG修复: 统一处理单图表和表格，使用相同的计数器
                        # 修复策略: 表格和图表都使用chart_id，与多图表处理保持一致
                        # 影响范围: app/services/report_generator.py:1853-1880
                        # 修复日期: 2025-01-29
                        chart_counter += 1
                        chart_id = f"chart_{chart_counter}"
                        log.info(f"   - 分配ID: {chart_id} (类型: {'表格' if is_table else '图表'})")
                        log.info(f"   - 是否在请求列表中: {chart_id in visualization_ids}")

                        if chart_id in visualization_ids:
                            if is_table:
                                # 使用新的提取方法来处理嵌套数据结构
                                extracted_table_data = self._extract_table_data(chart_data)
                                visualization_data_map[chart_id] = extracted_table_data
                                log.info(f"📊 ✅ 成功获取表格数据: {chart_id}")
                                log.info(f"   - 提取后的数据结构: {list(extracted_table_data.keys()) if isinstance(extracted_table_data, dict) else 'N/A'}")
                            else:
                                # 图表数据
                                visualization_data_map[chart_id] = chart_data
                                log.info(f"🎨 ✅ 成功获取图表数据: {chart_id}")
                                log.info(f"   - 图表数据结构: {list(chart_data.keys()) if isinstance(chart_data, dict) else 'N/A'}")
                        else:
                            log.info(f"🎨 ⏭️ 跳过{'表格' if is_table else '图表'}数据: {chart_id} (不在请求列表中)")
                else:
                    log.info(f"   - ❌ 无chart_data数据")

            log.info(f"🔍 检索完成，最终结果:")
            log.info(f"   - 可视化计数器: {chart_counter}")
            log.info(f"   - 请求的可视化ID: {visualization_ids}")
            log.info(f"   - 实际获取到的数据: {list(visualization_data_map.keys())}")
            log.info(f"   - 获取成功的数量: {len(visualization_data_map)}")

            # 详细打印每个获取到的数据
            for vis_id, vis_data in visualization_data_map.items():
                log.info(f"📋 {vis_id} 详细信息:")
                if isinstance(vis_data, dict):
                    if 'data' in vis_data and isinstance(vis_data['data'], dict):
                        data_info = vis_data['data']
                        log.info(f"   - 类型: {data_info.get('render_type', 'unknown')}")
                        log.info(f"   - 显示格式: {data_info.get('display_format', 'unknown')}")
                        log.info(f"   - 标题: {data_info.get('title', 'N/A')}")
                        if 'table_config' in data_info:
                            table_config = data_info['table_config']
                            log.info(f"   - 表格列数: {len(table_config.get('columns', []))}")
                            log.info(f"   - 表格行数: {len(table_config.get('dataSource', []))}")
                        elif 'chart_config' in data_info:
                            log.info(f"   - 图表配置存在: True")
                    else:
                        log.info(f"   - 原始数据键: {list(vis_data.keys())}")
                else:
                    log.info(f"   - 数据类型: {type(vis_data)}")
            
            return visualization_data_map
            
        except Exception as e:
            log.warning(f"查询可视化数据失败: {str(e)}")
            return {}
        finally:
            if db:
                db.close()

    def _query_chart_data_simple(self, analysis_id: str, chart_ids: List[str]) -> Dict[str, Any]:
        """简化的图表数据查询 - 直接从数据库获取标准化的图表数据
        
        Args:
            analysis_id: 分析ID
            chart_ids: 图表ID列表
            
        Returns:
            Dict[str, Any]: 图表数据映射
        """
        db = None
        try:
            db = SessionLocal()
            
            # 查询图表步骤
            chart_steps = db.query(AnalysisStepDetail).filter(
                AnalysisStepDetail.analysis_id == analysis_id,
                AnalysisStepDetail.has_chart_data == 1,
                AnalysisStepDetail.interaction_type == 'chart_display'
            ).order_by(
                AnalysisStepDetail.created_at.asc(),
                AnalysisStepDetail.step_order.asc()
            ).all()
            
            chart_data_map = {}
            chart_counter = 0
            
            for step in chart_steps:
                # 从interaction_data中获取数据
                interaction_data = step.interaction_data or {}
                chart_data = interaction_data.get('chart_data')
                
                if chart_data and not self._is_table_data(chart_data):
                    # 只处理图表数据，跳过表格数据
                    chart_counter += 1
                    chart_id = f"chart_{chart_counter}"
                    
                    if chart_id in chart_ids:
                        chart_data_map[chart_id] = chart_data
                        log.info(f"🎨 获取图表数据: {chart_id}")
                    else:
                        log.debug(f"🎨 跳过图表数据: {chart_id} (不在请求列表中)")
            
            return chart_data_map
            
        except Exception as e:
            log.warning(f"查询图表数据失败: {str(e)}")
            return {}
        finally:
            if db:
                db.close()

    def _extract_chart_generation_status(self, context, user_language: str = 'zh-CN') -> str:
        """
        提取图表生成状态信息，让主Agent感知图表生成情况

        Args:
            context: 执行上下文
            user_language: 用户语言偏好

        Returns:
            str: 图表生成状态信息的文本描述
        """
        try:
            chart_status_info = []
            total_charts_generated = 0
            total_charts_failed = 0
            total_charts_skipped = 0

            # 遍历执行历史，查找图表生成状态
            for record in context.execution_history:
                result = record.get('result', {})
                chart_status = result.get('_chart_generation_status')

                if chart_status:
                    status = chart_status.get('status')
                    chart_count = chart_status.get('chart_count', 0)
                    method = chart_status.get('generation_method')
                    message = chart_status.get('message', '')
                    tool_name = record.get('tool_name', '未知工具')

                    # 统计图表生成情况
                    if status == 'success':
                        total_charts_generated += chart_count
                        if user_language == 'en-US':
                            chart_status_info.append(f"✅ **{tool_name}**: Successfully generated {chart_count} chart(s) using {method}")
                        else:
                            chart_status_info.append(f"✅ **{tool_name}**: 成功生成 {chart_count} 个图表（使用{method}）")
                    elif status == 'failed':
                        total_charts_failed += 1
                        if user_language == 'en-US':
                            chart_status_info.append(f"❌ **{tool_name}**: Chart generation failed - {message}")
                        else:
                            chart_status_info.append(f"❌ **{tool_name}**: 图表生成失败 - {message}")
                    elif status == 'skipped':
                        total_charts_skipped += 1
                        if user_language == 'en-US':
                            chart_status_info.append(f"⏭️ **{tool_name}**: Chart generation skipped - {message}")
                        else:
                            chart_status_info.append(f"⏭️ **{tool_name}**: 跳过图表生成 - {message}")

            # 如果没有图表生成状态信息，返回空字符串
            if not chart_status_info:
                return ""

            # 构建状态摘要
            if user_language == 'en-US':
                status_summary = f"""

**Chart Generation Status Summary:**
- Total charts generated: {total_charts_generated}
- Failed attempts: {total_charts_failed}
- Skipped (data not suitable): {total_charts_skipped}

**Detailed Status:**
{chr(10).join(chart_status_info)}

**Important for Report Writing:**
- When describing data trends and findings, you can reference the generated charts to enhance your analysis
- If chart generation failed or was skipped, focus on textual analysis and explain why visualization wasn't possible
- Use chart placeholders appropriately when charts are available to support your narrative
"""
            else:
                status_summary = f"""

**图表生成状态摘要：**
- 成功生成图表数量: {total_charts_generated}
- 生成失败次数: {total_charts_failed}
- 跳过次数（数据不适合可视化）: {total_charts_skipped}

**详细状态：**
{chr(10).join(chart_status_info)}

**报告撰写重要提示：**
- 在描述数据趋势和发现时，可以引用生成的图表来增强分析
- 如果图表生成失败或被跳过，请专注于文本分析并解释为什么无法可视化
- 当有图表可用时，适当使用图表占位符来支撑你的叙述
"""

            return status_summary

        except Exception as e:
            log.error(f"提取图表生成状态时出错: {str(e)}")
            return ""

    # ==================== 结构化报告生成方法 ====================

    async def generate_structured_report(self,
                                       context,  # ExecutionContext
                                       schema_info: str,
                                       reasoning: str = '',
                                       project_id: Optional[str] = None,
                                       db_session = None,
                                       user_language: str = 'zh-CN',
                                       analysis_insights: Optional[List[Any]] = None,
                                       lifecycle_logger = None) -> tuple[StructuredReport, dict]:
        """
        生成结构化报告（JSON格式）
        
        Args:
            context: 执行上下文
            schema_info: 数据库Schema信息
            reasoning: 推理过程
            project_id: 项目ID
            db_session: 数据库会话
            user_language: 用户语言
            analysis_insights: 分析洞察
            lifecycle_logger: 生命周期日志器
            
        Returns:
            tuple[StructuredReport, dict]: 结构化报告和使用统计
        """
        try:
            log.info("🏗️ 开始生成结构化报告")
            
            components = []
            total_usage = {"total_tokens": 0, "prompt_tokens": 0, "completion_tokens": 0}
            
            # 1. 生成报告头部
            header_component = await self._generate_header_component(
                context, project_id, db_session, user_language
            )
            if header_component:
                components.append(header_component)
            
            # 2. 生成KPI网格
            kpi_component, kpi_usage = await self._generate_kpi_component(
                context, analysis_insights, user_language
            )
            if kpi_component:
                components.append(kpi_component)
                self._merge_usage(total_usage, kpi_usage)
            
            # 3. 生成执行摘要
            summary_component, summary_usage = await self._generate_summary_component(
                context, analysis_insights, user_language
            )
            if summary_component:
                components.append(summary_component)
                self._merge_usage(total_usage, summary_usage)
            
            # 4. 生成图表组件
            chart_components = await self._generate_chart_components(context, user_language)
            components.extend(chart_components)
            
            # 5. 生成洞察列表
            insight_component, insight_usage = await self._generate_insight_component(
                analysis_insights, user_language
            )
            if insight_component:
                components.append(insight_component)
                self._merge_usage(total_usage, insight_usage)
            
            # 6. 生成建议列表
            recommendation_component, rec_usage = await self._generate_recommendation_component(
                context, analysis_insights, user_language
            )
            if recommendation_component:
                components.append(recommendation_component)
                self._merge_usage(total_usage, rec_usage)
            
            # 构建结构化报告
            structured_report = StructuredReport(
                components=components,
                metadata={
                    "generated_at": get_shanghai_time().isoformat(),
                    "project_id": project_id,
                    "language": user_language,
                    "total_components": len(components)
                }
            )
            
            log.info(f"✅ 结构化报告生成完成，包含 {len(components)} 个组件")
            return structured_report, total_usage

        except Exception as e:
            log.error(f"❌ 结构化报告生成失败: {str(e)}")
            log.error(traceback.format_exc())
            raise

    async def _generate_header_component(self, context, project_id: Optional[str], 
                                       db_session, user_language: str) -> Optional[ReportComponent]:
        """生成报告头部组件"""
        try:
            # 获取项目信息
            project_name = "数据分析项目"
            if project_id and db_session:
                project = db_session.query(Project).filter(Project.id == project_id).first()
                if project:
                    project_name = project.name
            
            # 生成标题和标签
            title = f"{project_name} - 智能分析报告" if user_language == 'zh-CN' else f"{project_name} - Intelligence Analysis Report"
            subtitle = "基于多轮对话的深度数据洞察" if user_language == 'zh-CN' else "Deep Data Insights Based on Multi-round Conversations"
            
            # 提取分析标签
            tags = []
            if hasattr(context, 'steps') and context.steps:
                # 从步骤中提取工具类型作为标签
                tool_types = set()
                for step in context.steps:
                    if hasattr(step, 'tool_name') and step.tool_name:
                        tool_types.add(step.tool_name)
                tags = list(tool_types)[:5]  # 最多5个标签
            
            header_data = HeaderData(
                title=title,
                subtitle=subtitle,
                generated_at=get_shanghai_time().strftime("%Y-%m-%d %H:%M:%S"),
                tags=tags
            )
            
            return ReportComponent(type="header", data=header_data)
            
        except Exception as e:
            log.error(f"生成头部组件失败: {str(e)}")
            return None

    async def _generate_kpi_component(self, context, analysis_insights: Optional[List[Any]], 
                                    user_language: str) -> tuple[Optional[ReportComponent], dict]:
        """生成KPI网格组件"""
        try:
            # 构建KPI提取提示词
            prompt = self._build_kpi_extraction_prompt(context, analysis_insights, user_language)
            
            # 调用LLM生成KPI数据
            response, usage = await self._call_llm_for_json_response(
                prompt, 
                system_prompt="你是一个专业的数据分析师，擅长提取关键业务指标。",
                user_language=user_language
            )
            
            if response and isinstance(response, dict) and 'metrics' in response:
                metrics = []
                for metric_data in response['metrics']:
                    metric = MetricData(
                        label=metric_data.get('label', ''),
                        value=metric_data.get('value', ''),
                        change=metric_data.get('change'),
                        change_type=metric_data.get('change_type'),
                        description=metric_data.get('description')
                    )
                    metrics.append(metric)
                
                kpi_data = KpiGridData(
                    title=response.get('title', '核心业绩指标' if user_language == 'zh-CN' else 'Key Performance Indicators'),
                    metrics=metrics
                )
                
                return ReportComponent(type="kpi_grid", data=kpi_data), usage
            
            return None, usage
            
        except Exception as e:
            log.error(f"生成KPI组件失败: {str(e)}")
            return None, {"total_tokens": 0, "prompt_tokens": 0, "completion_tokens": 0}

    async def _generate_summary_component(self, context, analysis_insights: Optional[List[Any]], 
                                        user_language: str) -> tuple[Optional[ReportComponent], dict]:
        """生成执行摘要组件"""
        try:
            # 构建摘要生成提示词
            prompt = self._build_summary_prompt(context, analysis_insights, user_language)
            
            # 调用LLM生成摘要
            response, usage = await self._call_llm_for_json_response(
                prompt,
                system_prompt="你是一个专业的商业分析师，擅长撰写简洁有力的执行摘要。",
                user_language=user_language
            )
            
            if response and isinstance(response, dict) and 'content' in response:
                summary_data = ExecutiveSummaryData(
                    title=response.get('title', '执行摘要' if user_language == 'zh-CN' else 'Executive Summary'),
                    content=response['content'],
                    key_points=response.get('key_points', [])
                )
                
                return ReportComponent(type="executive_summary", data=summary_data), usage
            
            return None, usage
            
        except Exception as e:
            log.error(f"生成摘要组件失败: {str(e)}")
            return None, {"total_tokens": 0, "prompt_tokens": 0, "completion_tokens": 0}

    async def _generate_chart_components(self, context, user_language: str) -> List[ReportComponent]:
        """生成图表组件列表"""
        chart_components = []
        
        try:
            # 获取图表信息
            vis_info_list = self._extract_chart_info_simple(context)
            
            for vis_info in vis_info_list:
                if vis_info.get('type') == 'chart':
                    # 获取图表配置
                    chart_config = vis_info.get('chart_config', {})
                    if chart_config:
                        # 生成图表解读
                        interpretation = await self._generate_chart_interpretation(
                            vis_info, chart_config, user_language
                        )
                        
                        chart_data = ChartData(
                            chart_id=vis_info.get('id', f"chart_{len(chart_components) + 1}"),
                            title=vis_info.get('description', '数据图表'),
                            interpretation=interpretation,
                            chart_config=chart_config,
                            chart_type=chart_config.get('series', [{}])[0].get('type', 'line')
                        )
                        
                        chart_components.append(ReportComponent(type="chart", data=chart_data))
                        
        except Exception as e:
            log.error(f"生成图表组件失败: {str(e)}")
        
        return chart_components

    async def _generate_insight_component(self, analysis_insights: Optional[List[Any]], 
                                        user_language: str) -> tuple[Optional[ReportComponent], dict]:
        """生成洞察列表组件"""
        try:
            if not analysis_insights:
                return None, {"total_tokens": 0, "prompt_tokens": 0, "completion_tokens": 0}
            
            # 构建洞察提取提示词
            prompt = self._build_insight_extraction_prompt(analysis_insights, user_language)
            
            # 调用LLM生成洞察数据
            response, usage = await self._call_llm_for_json_response(
                prompt,
                system_prompt="你是一个专业的数据洞察分析师，擅长发现数据中的关键模式和趋势。",
                user_language=user_language
            )
            
            if response and isinstance(response, dict) and 'insights' in response:
                insights = []
                for insight_data in response['insights']:
                    insight = InsightData(
                        type=insight_data.get('type', 'trend'),
                        text=insight_data.get('text', ''),
                        confidence=float(insight_data.get('confidence', 0.8)),
                        impact=insight_data.get('impact'),
                        category=insight_data.get('category')
                    )
                    insights.append(insight)
                
                insight_list_data = InsightListData(
                    title=response.get('title', '关键业务洞察' if user_language == 'zh-CN' else 'Key Business Insights'),
                    insights=insights
                )
                
                return ReportComponent(type="insight_list", data=insight_list_data), usage
            
            return None, usage
            
        except Exception as e:
            log.error(f"生成洞察组件失败: {str(e)}")
            return None, {"total_tokens": 0, "prompt_tokens": 0, "completion_tokens": 0}

    async def _generate_recommendation_component(self, context, analysis_insights: Optional[List[Any]], 
                                               user_language: str) -> tuple[Optional[ReportComponent], dict]:
        """生成建议列表组件"""
        try:
            # 构建建议生成提示词
            prompt = self._build_recommendation_prompt(context, analysis_insights, user_language)
            
            # 调用LLM生成建议数据
            response, usage = await self._call_llm_for_json_response(
                prompt,
                system_prompt="你是一个专业的商业顾问，擅长基于数据分析结果提出可行的行动建议。",
                user_language=user_language
            )
            
            if response and isinstance(response, dict) and 'recommendations' in response:
                recommendations = []
                for rec_data in response['recommendations']:
                    recommendation = RecommendationData(
                        area=rec_data.get('area', ''),
                        text=rec_data.get('text', ''),
                        priority=rec_data.get('priority'),
                        timeline=rec_data.get('timeline')
                    )
                    recommendations.append(recommendation)
                
                rec_list_data = RecommendationListData(
                    title=response.get('title', '行动建议' if user_language == 'zh-CN' else 'Action Recommendations'),
                    recommendations=recommendations
                )
                
                return ReportComponent(type="recommendation_list", data=rec_list_data), usage
            
            return None, usage
            
        except Exception as e:
            log.error(f"生成建议组件失败: {str(e)}")
            return None, {"total_tokens": 0, "prompt_tokens": 0, "completion_tokens": 0}

    def _build_kpi_extraction_prompt(self, context, analysis_insights: Optional[List[Any]], 
                                   user_language: str) -> str:
        """构建KPI提取提示词"""
        lang_config = {
            'zh-CN': {
                'instruction': '请从以下分析结果中提取3-5个最核心的业务KPI指标',
                'format_desc': '返回JSON格式，包含title和metrics数组',
                'example': '''
                {
                    "title": "核心业绩指标",
                    "metrics": [
                        {
                            "label": "总销售额",
                            "value": "¥ 1,234.5 M",
                            "change": "+15.2%",
                            "change_type": "increase",
                            "description": "相比上期增长显著"
                        }
                    ]
                }
                '''
            },
            'en-US': {
                'instruction': 'Please extract 3-5 core business KPI indicators from the following analysis results',
                'format_desc': 'Return JSON format with title and metrics array',
                'example': '''
                {
                    "title": "Key Performance Indicators",
                    "metrics": [
                        {
                            "label": "Total Sales",
                            "value": "$ 1,234.5 M",
                            "change": "+15.2%", 
                            "change_type": "increase",
                            "description": "Significant growth compared to previous period"
                        }
                    ]
                }
                '''
            }
        }
        
        config = lang_config.get(user_language, lang_config['zh-CN'])
        
        # 构建分析结果摘要
        analysis_summary = self._build_analysis_summary_for_prompt(context, analysis_insights)
        
        return f"""
{config['instruction']}：

{analysis_summary}

{config['format_desc']}：
{config['example']}

请确保：
1. 指标值要具体且格式化良好
2. 变化幅度要准确计算
3. change_type只能是: increase, decrease, stable
4. 描述要简洁有力
"""

    def _build_summary_prompt(self, context, analysis_insights: Optional[List[Any]], 
                            user_language: str) -> str:
        """构建摘要生成提示词"""
        lang_config = {
            'zh-CN': {
                'instruction': '请基于以下分析结果撰写一段200字以内的执行摘要',
                'requirements': [
                    '突出最重要的发现和结论',
                    '使用商业化语言，面向决策者',
                    '结构清晰，逻辑连贯',
                    '包含可量化的关键数据'
                ]
            },
            'en-US': {
                'instruction': 'Please write an executive summary within 200 words based on the following analysis results',
                'requirements': [
                    'Highlight the most important findings and conclusions',
                    'Use business language for decision makers',
                    'Clear structure and logical coherence',
                    'Include quantifiable key data'
                ]
            }
        }
        
        config = lang_config.get(user_language, lang_config['zh-CN'])
        analysis_summary = self._build_analysis_summary_for_prompt(context, analysis_insights)
        
        return f"""
{config['instruction']}：

{analysis_summary}

要求：
{chr(10).join([f"- {req}" for req in config['requirements']])}

返回JSON格式：
{{
    "title": "执行摘要",
    "content": "摘要内容...",
    "key_points": ["要点1", "要点2", "要点3"]
}}
"""

    def _build_insight_extraction_prompt(self, analysis_insights: List[Any], user_language: str) -> str:
        """构建洞察提取提示词"""
        insights_text = self._build_analysis_insights_section(analysis_insights, user_language)
        
        lang_config = {
            'zh-CN': {
                'instruction': '请从以下分析洞察中提取5-8个最有价值的业务洞察',
                'types': 'trend(趋势), anomaly(异常), opportunity(机会), risk(风险), correlation(关联)'
            },
            'en-US': {
                'instruction': 'Please extract 5-8 most valuable business insights from the following analysis',
                'types': 'trend, anomaly, opportunity, risk, correlation'
            }
        }
        
        config = lang_config.get(user_language, lang_config['zh-CN'])
        
        return f"""
{config['instruction']}：

{insights_text}

返回JSON格式：
{{
    "title": "关键业务洞察",
    "insights": [
        {{
            "type": "trend",
            "text": "洞察内容...",
            "confidence": 0.95,
            "impact": "high",
            "category": "销售"
        }}
    ]
}}

洞察类型包括：{config['types']}
置信度范围：0.0-1.0
影响程度：high, medium, low
"""

    def _build_recommendation_prompt(self, context, analysis_insights: Optional[List[Any]], 
                                   user_language: str) -> str:
        """构建建议生成提示词"""
        analysis_summary = self._build_analysis_summary_for_prompt(context, analysis_insights)
        
        lang_config = {
            'zh-CN': {
                'instruction': '请基于分析结果提出3-6个具体的行动建议',
                'areas': '市场营销, 产品优化, 运营管理, 客户服务, 技术改进'
            },
            'en-US': {
                'instruction': 'Please provide 3-6 specific action recommendations based on analysis results',
                'areas': 'Marketing, Product Optimization, Operations, Customer Service, Technical Improvement'
            }
        }
        
        config = lang_config.get(user_language, lang_config['zh-CN'])
        
        return f"""
{config['instruction']}：

{analysis_summary}

返回JSON格式：
{{
    "title": "行动建议",
    "recommendations": [
        {{
            "area": "市场营销",
            "text": "建议内容...",
            "priority": "high",
            "timeline": "1-3个月"
        }}
    ]
}}

业务领域包括：{config['areas']}
优先级：high, medium, low
时间框架：如"1-3个月", "短期", "长期"等
"""

    def _build_analysis_summary_for_prompt(self, context, analysis_insights: Optional[List[Any]]) -> str:
        """为提示词构建分析结果摘要"""
        summary_parts = []
        
        # 添加步骤摘要
        if hasattr(context, 'steps') and context.steps:
            summary_parts.append("**执行步骤：**")
            for i, step in enumerate(context.steps[:5], 1):  # 最多5个步骤
                step_name = getattr(step, 'step_name', f'步骤{i}')
                tool_name = getattr(step, 'tool_name', '')
                summary_parts.append(f"{i}. {step_name} ({tool_name})")
        
        # 添加洞察摘要
        if analysis_insights:
            insights_summary = self._build_analysis_insights_section(analysis_insights, 'zh-CN')
            if insights_summary:
                summary_parts.append("**分析洞察：**")
                summary_parts.append(insights_summary[:1000])  # 限制长度
        
        return "\n".join(summary_parts)

    async def _generate_chart_interpretation(self, vis_info: dict, chart_config: dict, 
                                           user_language: str) -> str:
        """生成图表解读"""
        try:
            chart_type = chart_config.get('series', [{}])[0].get('type', 'line')
            chart_title = vis_info.get('description', '数据图表')
            
            prompt = f"""
请为以下图表生成简洁的解读文本（100字以内）：

图表标题：{chart_title}
图表类型：{chart_type}
图表配置：{json.dumps(chart_config, ensure_ascii=False)[:500]}

要求：
1. 突出关键趋势和模式
2. 使用商业化语言
3. 避免技术术语
4. 专注于业务含义
"""
            
            response, _ = await self._call_llm_for_text_response(
                prompt, 
                system_prompt="你是一个专业的数据可视化分析师。",
                user_language=user_language
            )
            
            return response.strip() if response else f"基于{chart_title}的数据可视化分析"
            
        except Exception as e:
            log.error(f"生成图表解读失败: {str(e)}")
            return f"基于{vis_info.get('description', '数据')}的可视化分析"

    async def _call_llm_for_json_response(self, prompt: str, system_prompt: str = None, 
                                        temperature: float = 0.3, user_language: str = 'zh-CN') -> tuple[dict, dict]:
        """调用LLM获取JSON响应"""
        try:
            # 确保提示词要求JSON格式
            json_prompt = f"{prompt}\n\n请确保返回有效的JSON格式，不要包含任何其他文本。"
            
            response, usage = await self._call_llm_for_text_response(
                json_prompt, system_prompt, temperature, user_language
            )
            
            if response:
                # 尝试解析JSON
                try:
                    # 清理响应文本
                    clean_response = response.strip()
                    if clean_response.startswith('```json'):
                        clean_response = clean_response[7:]
                    if clean_response.endswith('```'):
                        clean_response = clean_response[:-3]
                    
                    json_data = json.loads(clean_response.strip())
                    return json_data, usage
                except json.JSONDecodeError as e:
                    log.error(f"JSON解析失败: {str(e)}, 原始响应: {response[:200]}")
                    return {}, usage
            
            return {}, usage
            
        except Exception as e:
            log.error(f"LLM JSON调用失败: {str(e)}")
            return {}, {"total_tokens": 0, "prompt_tokens": 0, "completion_tokens": 0}

    def _merge_usage(self, total_usage: dict, new_usage: dict):
        """合并使用统计"""
        for key in ['total_tokens', 'prompt_tokens', 'completion_tokens']:
            total_usage[key] = total_usage.get(key, 0) + new_usage.get(key, 0)
