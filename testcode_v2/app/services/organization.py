from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import func, and_

from app.models.organization import Organization
from app.models.user import User
from app.models.project import Project
from app.schemas.organization import OrganizationCreate, OrganizationUpdate, OrganizationStats


class OrganizationService:
    """企业服务类"""

    @staticmethod
    def get_organization(db: Session, org_id: int) -> Optional[Organization]:
        """获取企业信息"""
        return db.query(Organization).filter(Organization.id == org_id).first()



    @staticmethod
    def get_organizations(
        db: Session, 
        skip: int = 0, 
        limit: int = 100,
        status: Optional[int] = None,
        search: Optional[str] = None
    ) -> tuple[List[Organization], int]:
        """获取企业列表"""
        query = db.query(Organization)
        
        # 状态筛选
        if status is not None:
            query = query.filter(Organization.status == status)
        
        # 搜索筛选
        if search:
            query = query.filter(Organization.name.contains(search))
        
        total = query.count()
        organizations = query.offset(skip).limit(limit).all()
        
        return organizations, total

    @staticmethod
    def create_organization(db: Session, org_data: OrganizationCreate) -> Organization:
        """创建企业"""
        # 检查企业名称是否已存在
        existing_name = db.query(Organization).filter(Organization.name == org_data.name).first()
        if existing_name:
            raise ValueError(f"企业名称 '{org_data.name}' 已存在")
        
        # 创建企业
        db_org = Organization(**org_data.dict())
        db.add(db_org)
        db.commit()
        db.refresh(db_org)
        return db_org

    @staticmethod
    def update_organization(
        db: Session, 
        org_id: int, 
        org_data: OrganizationUpdate
    ) -> Optional[Organization]:
        """更新企业信息"""
        db_org = db.query(Organization).filter(Organization.id == org_id).first()
        if not db_org:
            return None
        
        # 检查名称唯一性（排除自己）
        update_dict = org_data.dict(exclude_unset=True)
        
        if "name" in update_dict:
            existing_name = db.query(Organization).filter(
                and_(Organization.name == update_dict["name"], Organization.id != org_id)
            ).first()
            if existing_name:
                raise ValueError(f"企业名称 '{update_dict['name']}' 已存在")
        
        # 更新字段
        for field, value in update_dict.items():
            setattr(db_org, field, value)
        
        db.commit()
        db.refresh(db_org)
        return db_org

    @staticmethod
    def delete_organization(db: Session, org_id: int) -> bool:
        """删除企业"""
        db_org = db.query(Organization).filter(Organization.id == org_id).first()
        if not db_org:
            return False
        
        # 检查是否有关联的用户或项目
        user_count = db.query(User).filter(User.org_id == org_id).count()
        project_count = db.query(Project).filter(Project.org_id == org_id).count()
        
        if user_count > 0 or project_count > 0:
            raise ValueError(f"无法删除企业，还有 {user_count} 个用户和 {project_count} 个项目关联")
        
        db.delete(db_org)
        db.commit()
        return True

    @staticmethod
    def get_organization_stats(db: Session, org_id: int) -> Optional[OrganizationStats]:
        """获取企业统计信息"""
        org = db.query(Organization).filter(Organization.id == org_id).first()
        if not org:
            return None
        
        # 统计用户数量
        user_count = db.query(User).filter(
            and_(User.org_id == org_id, User.is_active == True)
        ).count()
        
        # 统计项目数量 - 直接通过org_id查询
        project_count = db.query(Project).filter(Project.org_id == org_id).count()
        
        # Token使用量（从组织表中获取实际统计）
        token_used = org.total_token_usage
        
        # 计算使用率
        user_usage_rate = user_count / org.user_limit if org.user_limit > 0 else 0
        project_usage_rate = project_count / org.project_limit if org.project_limit > 0 else 0
        token_usage_rate = token_used / org.token_limit if org.token_limit > 0 else 0
        
        return OrganizationStats(
            id=org.id,
            name=org.name,
            status=org.status,
            user_count=user_count,
            project_count=project_count,
            token_used=token_used,
            user_limit=org.user_limit,
            project_limit=org.project_limit,
            token_limit=org.token_limit,
            user_usage_rate=min(user_usage_rate, 1.0),
            project_usage_rate=min(project_usage_rate, 1.0),
            token_usage_rate=min(token_usage_rate, 1.0)
        )

    @staticmethod
    def get_all_organization_stats(db: Session) -> List[OrganizationStats]:
        """获取所有企业的统计信息"""
        organizations = db.query(Organization).all()
        stats_list = []
        
        for org in organizations:
            stats = OrganizationService.get_organization_stats(db, org.id)
            if stats:
                stats_list.append(stats)
        
        return stats_list

    @staticmethod
    def check_user_limit(db: Session, org_id: int) -> bool:
        """检查企业用户数是否超限"""
        org = db.query(Organization).filter(Organization.id == org_id).first()
        if not org:
            return False
        
        user_count = db.query(User).filter(
            and_(User.org_id == org_id, User.is_active == True)
        ).count()
        
        return user_count < org.user_limit

    @staticmethod
    def check_project_limit(db: Session, org_id: int) -> bool:
        """检查企业项目数是否超限"""
        org = db.query(Organization).filter(Organization.id == org_id).first()
        if not org:
            return False
        
        # 直接通过org_id查询项目数量
        project_count = db.query(Project).filter(Project.org_id == org_id).count()
        
        return project_count < org.project_limit

    @staticmethod
    def check_token_limit(db: Session, org_id: int) -> bool:
        """检查企业Token用量是否超限"""
        org = db.query(Organization).filter(Organization.id == org_id).first()
        if not org:
            return False
        
        return org.total_token_usage < org.token_limit

    @staticmethod
    def get_token_usage_info(db: Session, org_id: int) -> dict:
        """获取企业Token用量详细信息"""
        org = db.query(Organization).filter(Organization.id == org_id).first()
        if not org:
            return {
                "total_usage": 0,
                "token_limit": 0,
                "remaining": 0,
                "usage_percentage": 0.0,
                "is_exceeded": True
            }
        
        remaining = max(0, org.token_limit - org.total_token_usage)
        usage_percentage = (org.total_token_usage / org.token_limit * 100) if org.token_limit > 0 else 0.0
        is_exceeded = org.total_token_usage >= org.token_limit
        
        return {
            "total_usage": org.total_token_usage,
            "token_limit": org.token_limit,
            "remaining": remaining,
            "usage_percentage": round(usage_percentage, 2),
            "is_exceeded": is_exceeded
        } 