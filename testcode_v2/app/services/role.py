from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import or_, and_

from app.models.role import Role
from app.schemas.role import RoleCreate, RoleUpdate, RoleList
from app.core.exceptions import BadRequestException, NotFoundException


class RoleService:
    """角色服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_roles(
        self,
        skip: int = 0,
        limit: int = 10,
        search: Optional[str] = None,
        level: Optional[int] = None
    ) -> RoleList:
        """获取角色列表"""
        query = self.db.query(Role)
        
        # 搜索条件
        if search:
            query = query.filter(
                or_(
                    Role.name.contains(search),
                    Role.description.contains(search)
                )
            )
        
        # 角色层级过滤
        if level is not None:
            query = query.filter(Role.level == level)
        
        # 获取总数
        total = query.count()
        
        # 分页查询
        roles = query.offset(skip).limit(limit).all()
        
        return RoleList(
            items=roles,
            total=total,
            page=(skip // limit) + 1,
            size=limit
        )
    
    def create_role(self, role_data: RoleCreate) -> Role:
        """创建角色"""
        # 检查角色名称是否已存在
        existing_role = self.db.query(Role).filter(Role.name == role_data.name).first()
        if existing_role:
            raise BadRequestException(f"角色名称 {role_data.name} 已存在")
        
        # 创建角色
        role = Role(
            name=role_data.name,
            description=role_data.description,
            level=role_data.level,
            permissions=role_data.permissions or [],
            is_active=role_data.is_active,
            is_system=False  # 手动创建的角色默认不是系统角色
        )
        
        self.db.add(role)
        self.db.commit()
        self.db.refresh(role)
        
        return role
    
    def get_role_by_id(self, role_id: int) -> Optional[Role]:
        """根据ID获取角色"""
        return self.db.query(Role).filter(Role.id == role_id).first()
    
    def update_role(self, role_id: int, role_data: RoleUpdate) -> Role:
        """更新角色"""
        role = self.get_role_by_id(role_id)
        if not role:
            raise NotFoundException("角色不存在")
        
        # 检查角色名称是否与其他角色冲突
        if role_data.name and role_data.name != role.name:
            existing_role = self.db.query(Role).filter(
                and_(Role.name == role_data.name, Role.id != role_id)
            ).first()
            if existing_role:
                raise BadRequestException(f"角色名称 {role_data.name} 已存在")
        
        # 更新字段
        update_data = role_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(role, field, value)
        
        self.db.commit()
        self.db.refresh(role)
        
        return role
    
    def delete_role(self, role_id: int) -> None:
        """删除角色"""
        role = self.get_role_by_id(role_id)
        if not role:
            raise NotFoundException("角色不存在")
        
        # 检查是否为系统角色
        if role.is_system:
            raise BadRequestException("系统角色不能删除")
        
        # 检查是否有用户使用该角色
        from app.models.user import User
        user_count = self.db.query(User).filter(User.role_id == role_id).count()
        if user_count > 0:
            raise BadRequestException(f"该角色被 {user_count} 个用户使用，无法删除")
        
        self.db.delete(role)
        self.db.commit()
    

    
    def get_role_by_name(self, name: str) -> Optional[Role]:
        """根据名称获取角色"""
        return self.db.query(Role).filter(Role.name == name).first()
    
    def check_permission(self, role_id: int, permission: str) -> bool:
        """检查角色是否有指定权限"""
        role = self.get_role_by_id(role_id)
        if not role or not role.is_active:
            return False
        
        permissions = role.permissions or []
        
        # 检查通配符权限
        for perm in permissions:
            if perm.endswith('*'):
                prefix = perm[:-1]
                if permission.startswith(prefix):
                    return True
            elif perm == permission:
                return True
        
        return False 