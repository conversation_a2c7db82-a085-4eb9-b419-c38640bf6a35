"""
SQL生成器实现模块

本模块提供了一个SQL生成器的实现，可以根据用户意图生成SQL查询。
主要功能：
1. 根据用户问题和数据库结构生成SQL查询
2. 使用LLM技术生成高质量的SQL
3. 提供多种备选和回退策略
4. 支持参数化查询

作者: AI数据分析团队
日期: 2023-07-01
"""

import json
import logging
import re
from typing import Dict, Any, Optional, List, Tuple, Union
import os

# 获取logger
logger = logging.getLogger("app.services.sql_generator_impl")

class SQLGenerator:
    """SQL生成器"""
    
    def __init__(self, schema_file: str = "schema_description.md"):
        """初始化SQL生成器
        
        Args:
            schema_file: schema描述文件路径，默认为schema_description.md
        """
        self.schema_file = schema_file
        self.logger = logging.getLogger('app.services.sql_generator_impl')
        self.logger.info("SQLGenerator初始化完成，使用schema文件: %s", schema_file)
    
    def _load_schema(self) -> str:
        """加载schema描述
        
        Returns:
            str: schema描述文本
        """
        try:
            # 尝试相对于当前目录加载
            if os.path.exists(self.schema_file):
                with open(self.schema_file, 'r', encoding='utf-8') as f:
                    return f.read()
            
            # 尝试相对于应用根目录加载
            app_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            full_path = os.path.join(app_root, self.schema_file)
            if os.path.exists(full_path):
                with open(full_path, 'r', encoding='utf-8') as f:
                    return f.read()
            
            # 尝试从预定义的路径加载
            schema_path = os.path.join(app_root, "schema_description.md")
            if os.path.exists(schema_path):
                self.logger.info(f"从预定义路径加载schema: {schema_path}")
                with open(schema_path, 'r', encoding='utf-8') as f:
                    return f.read()
            
            # 使用内置的schema
            self.logger.warning(f"无法加载schema文件，使用内置的简化schema")
            return self._get_builtin_schema()
        except Exception as e:
            error_msg = f"加载Schema描述文件失败: {str(e)}"
            self.logger.error(error_msg)
            # 使用内置的schema
            return self._get_builtin_schema()
    
    def _get_builtin_schema(self) -> str:
        """获取内置的schema描述
        
        Returns:
            str: 内置的schema描述
        """
        return """# 数据库表结构说明

## 表: FINRISK_BASE_INFO
### 列信息:
- ID (NUMBER(22), 非空)
- COMPANY_NAME (VARCHAR2(400), 可空) - 公司名称
- COMPANY_TYPE (VARCHAR2(100), 可空) - 公司类型
- REG_DATE (DATE, 可空) - 注册日期
- REG_CAPITAL (NUMBER(15,2), 可空) - 注册资本
- REG_ADDR (VARCHAR2(400), 可空) - 注册地址
- LEGAL_PERSON (VARCHAR2(100), 可空) - 法人代表
- BIZ_SCOPE (VARCHAR2(4000), 可空) - 经营范围
- BIZ_STATUS (VARCHAR2(100), 可空) - 经营状态

## 表: FINRISK_BANK_ACCOUNTS
### 列信息:
- ID (NUMBER(22), 可空) - 账户ID
- NAME (VARCHAR2(400), 可空) - 户名
- ACCOUNT (VARCHAR2(40), 可空) - 账号
- BALANCE (NUMBER(20,2), 可空) - 余额

## 表: FINRISK_BANK_TRANSFERS
### 列信息:
- TXN_ID (NUMBER(22), 可空) - 交易ID
- SRC_ACCT_ID (NUMBER(22), 可空) - 源账户ID
- DST_ACCT_ID (NUMBER(22), 可空) - 目标账户ID
- DESCRIPTION (VARCHAR2(400), 可空) - 交易描述
- AMOUNT (NUMBER(22), 可空) - 交易金额

## 表: FINRISK_NEWS
### 列信息:
- ID (NUMBER(22), 可空) - 新闻ID
- TITLE (VARCHAR2(400), 可空) - 新闻标题
- CONTENT (CLOB, 可空) - 新闻内容
- PUBLISH_TIME (DATE, 可空) - 发布时间
- SOURCE (VARCHAR2(100), 可空) - 新闻来源
- TAG (VARCHAR2(400), 可空) - 新闻标签
"""
    
    async def generate_sql(self, intent: Dict[str, Any] = None, question: str = None, 
                         tables: Dict[str, Any] = None, db_type: str = None,
                         database_schema: Dict[str, Any] = None,
                         ds_type: str = None, additional_instructions: str = None) -> Union[str, Tuple[str, Dict[str, Any]]]:
        """根据意图生成SQL
        
        Args:
            intent: 查询意图，优先使用
            question: 用户问题（如果未提供intent）
            tables: 表结构字典（如果未提供intent）
            db_type: 数据库类型
            database_schema: 数据库Schema（兼容sql_generator.py）
            ds_type: 数据源类型（兼容sql_generator.py）
            additional_instructions: 额外指令（兼容sql_generator.py）
            
        Returns:
            Union[str, Tuple[str, Dict[str, Any]]]: 生成的SQL和参数，或仅SQL
        """
        try:
            # 处理新的参数格式
            if intent is None and question is not None:
                intent = {
                    "query": question,
                    "tables": tables or {},
                    "db_type": db_type or ds_type,
                    "additional_instructions": additional_instructions
                }
                self.logger.info(f"使用问题作为查询: {question}")
                
            # 处理database_schema参数
            if database_schema is not None and "tables" in database_schema:
                if intent is None:
                    intent = {}
                if "tables" not in intent or not intent["tables"]:
                    intent["tables"] = database_schema["tables"]
                    
            # 加载schema
            schema = self._load_schema()
            self.logger.info(f"开始生成SQL，意图: {json.dumps(intent, ensure_ascii=False)}")
            
            # 构建提示
            prompt = self._build_prompt(intent, schema)
            self.logger.info(f"生成的提示: {prompt}")
            
            # 最多尝试3次生成SQL
            for attempt in range(3):
                self.logger.info(f"第{attempt + 1}次尝试生成SQL")
                try:
                    sql, params = await self._generate_sql_with_llm(prompt)
                    self._validate_sql(sql, params)
                    
                    # 根据调用方式返回不同格式
                    if question is not None and intent.get("query") == question:
                        # 如果是通过question参数调用，则只返回SQL字符串
                        return sql
                    else:
                        # 原有的intent方式调用，返回SQL和参数元组
                        return sql, params
                        
                except Exception as e:
                    self.logger.error(f"第{attempt + 1}次生成SQL失败: {str(e)}")
                    if attempt < 2:  # 如果不是最后一次尝试
                        prompt = self._update_prompt_with_error(prompt, str(e))
                    else:  # 最后一次尝试失败
                        self.logger.info("使用备选SQL生成方案")
                        sql, params = self._generate_fallback_sql(intent)
                        
                        # 根据调用方式返回不同格式
                        if question is not None and intent.get("query") == question:
                            return sql
                        else:
                            return sql, params
            
        except Exception as e:
            self.logger.error(f"SQL生成失败: {str(e)}")
            # 返回一个简单的SQL和空参数
            if question is not None:
                return "SELECT 1 FROM DUAL"
            else:
                return "SELECT 1 FROM DUAL", {}

    def _validate_sql(self, sql: str, params: Dict[str, Any]) -> None:
        """验证SQL的正确性
        
        Args:
            sql: SQL语句
            params: SQL参数
        """
        self.logger.info("开始验证SQL")
        self.logger.info(f"验证SQL: {sql}")
        self.logger.info(f"验证参数: {json.dumps(params, ensure_ascii=False)}")
        
        # 把SQL语句统一为大写，去掉多余空格，方便匹配
        sql_upper = re.sub(r'\s+', ' ', sql.upper())
        
        # 提取SQL中使用的表名
        table_pattern = r'\bFROM\s+([A-Za-z_][A-Za-z0-9_$#]*)|JOIN\s+([A-Za-z_][A-Za-z0-9_$#]*)'
        tables = []
        for match in re.finditer(table_pattern, sql_upper, re.IGNORECASE):
            table_name = match.group(1) or match.group(2)
            if table_name and table_name not in ['DUAL', 'SYSDATE']:
                tables.append(table_name)
        
        self.logger.info(f"提取的表名: {tables}")
        
        # 获取所有临时表名，包括WITH子句和子查询中定义的表
        cte_tables = set()
        
        # 1. 识别WITH子句中定义的临时表（单个或多个）
        # 先识别整个WITH块
        if 'WITH ' in sql_upper:
            # 尝试提取整个WITH块中的所有临时表
            with_block_pattern = r'WITH\s+(.*?)(?:SELECT|INSERT|UPDATE|DELETE|MERGE)'
            with_blocks = re.findall(with_block_pattern, sql_upper, re.DOTALL | re.IGNORECASE)
            
            for with_block in with_blocks:
                # 在WITH块中查找所有临时表定义
                table_defs = re.findall(r'([A-Za-z_][A-Za-z0-9_$#]*)\s+(?:AS|AS\s*\(\s*SELECT|\()', with_block)
                for table in table_defs:
                    cte_tables.add(table)
        
        # 2. 识别子查询中定义的临时表
        subquery_pattern = r'\)\s+([A-Za-z_][A-Za-z0-9_$#]*)\s+'
        for match in re.finditer(subquery_pattern, sql_upper):
            subquery_alias = match.group(1)
            if subquery_alias not in ['WHERE', 'ORDER', 'GROUP', 'HAVING', 'FROM', 'JOIN', 'LEFT', 'RIGHT', 'INNER', 'OUTER', 'FULL', 'ON', 'AND', 'OR', 'IN']:
                cte_tables.add(subquery_alias)
        
        # 3. 识别IN子句中的临时结果集
        in_pattern = r'IN\s*\(\s*SELECT.*?\)\s*'
        if re.search(in_pattern, sql_upper, re.DOTALL):
            # 这些是隐式临时表，不需要具体名字，但需要标记存在
            self.logger.info("发现IN子句中的子查询，将被视为临时表")
        
        # 移除重复项并转为列表
        cte_tables = list(cte_tables)
        self.logger.info(f"识别的临时表: {cte_tables}")
        
        # 验证表是否存在于schema中（排除临时表）
        schema_content = self._load_schema().upper()
        for table in tables:
            # 跳过临时表、子查询表和系统表的验证
            if table in cte_tables or table.startswith('SYS_') or table.startswith('V$'):
                continue
                
            if table not in schema_content:
                # 再做一次检查，看是否是子查询的别名
                if re.search(r'\)\s+' + re.escape(table) + r'\b', sql_upper):
                    self.logger.info(f"表名 {table} 被识别为子查询别名，跳过验证")
                    continue
                    
                error_msg = f"未知的表名: {table}"
                self.logger.error(error_msg)
                raise ValueError(error_msg)

    def _build_prompt(self, intent: Dict[str, Any], schema: str) -> str:
        """构建提示
        
        Args:
            intent: 查询意图
            schema: schema描述
            
        Returns:
            str: 生成的提示
        """
        query = intent.get("query", "")
        intent_analysis = intent.get("intent_analysis", {})
        
        # 提取问题理解
        problem_understanding = ""
        if intent_analysis and "problem_understanding" in intent_analysis:
            problem_understanding = intent_analysis["problem_understanding"]
        
        return f"""请根据以下信息生成Oracle SQL查询：

1. 查询意图：
原始问题: {query}
问题理解: {problem_understanding}

2. 可用的表结构：
{schema}

3. 特殊说明：
- 使用 graph_table 函数查询图数据
- 使用 vector_distance 函数进行向量相似度计算
- 使用 contains 函数进行全文检索

请生成标准的Oracle SQL，确保：
1. 所有表名和字段名都存在且拼写正确
2. 查询逻辑符合意图要求
3. 返回格式符合预期
4. 使用参数化查询（使用:param_name）"""
    
    def _generate_fallback_sql(self, intent: Dict[str, Any]) -> Tuple[str, Dict[str, Any]]:
        """生成备选SQL"""
        query = intent.get("query", "")
        intent_text = json.dumps(intent, ensure_ascii=False).lower()
        
        # 根据意图类型生成基础查询
        if "企业基本信息" in intent_text or "公司基本信息" in intent_text:
            return """
                SELECT *
                FROM FINRISK_BASE_INFO
                WHERE COMPANY_NAME LIKE :company_name
            """, {"company_name": "%"}
        
        elif "资金往来" in intent_text or "交易" in intent_text or "转账" in intent_text:
            return """
                SELECT 
                    a1.NAME AS 源公司名称,
                    a2.NAME AS 目标公司名称,
                    t.AMOUNT AS 交易金额,
                    t.DESCRIPTION AS 交易描述
                FROM FINRISK_BANK_TRANSFERS t
                JOIN FINRISK_BANK_ACCOUNTS a1 ON t.SRC_ACCT_ID = a1.ID
                JOIN FINRISK_BANK_ACCOUNTS a2 ON t.DST_ACCT_ID = a2.ID
                WHERE a1.NAME LIKE :company_name OR a2.NAME LIKE :company_name
            """, {"company_name": "%"}
        
        elif "新闻" in intent_text or "舆情" in intent_text:
            return """
                SELECT *
                FROM FINRISK_NEWS
                WHERE TITLE LIKE :keyword OR CONTENT LIKE :keyword
                ORDER BY PUBLISH_TIME DESC
            """, {"keyword": "%"}
        
        else:
            # 最基础的查询
            return """
                SELECT *
                FROM FINRISK_BASE_INFO
                WHERE 1=1
            """, {}
    
    async def _generate_sql_with_llm(self, prompt: str) -> Tuple[str, Dict[str, Any]]:
        """使用LLM生成SQL
        
        Args:
            prompt: 提示信息
            
        Returns:
            tuple: (SQL语句, 参数字典)
        """
        try:
            # 导入LLM服务，正确导入路径
            from app.services.llm.openai_service import OpenAIService
            
            self.logger.info("开始调用LLM生成SQL")
            llm_service = OpenAIService()
            
            # 构建系统提示
            system_prompt = """你是一个Oracle SQL专家。
            你的任务是根据提供的schema信息和查询意图生成SQL。
            
            要求：
            1. 只返回JSON格式的响应
            2. JSON必须包含两个字段：sql和parameters
            3. sql字段是字符串，包含生成的SQL语句
            4. parameters字段是对象，包含SQL中使用的参数
            5. 确保SQL语法正确，字段名匹配schema
            6. 如果有错误信息，分析错误原因并修正
            7. 不要返回任何其他内容
            
            示例输出：
            {
                "sql": "SELECT * FROM FINRISK_BASE_INFO WHERE REG_CAPITAL > :min_capital",
                "parameters": {
                    "min_capital": 10000000
                }
            }
            """
            
            # 尝试调用OpenAI服务
            try:
                # 构建完整的消息
                messages = [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": prompt}
                ]
                
                # 直接调用OpenAI客户端
                completion = await llm_service.client.chat.completions.create(
                    model=llm_service.model,
                    messages=messages,
                    temperature=0.1,
                    response_format={"type": "json_object"}
                )
                
                # 解析响应
                if completion and completion.choices and len(completion.choices) > 0:
                    content = completion.choices[0].message.content
                    self.logger.info(f"LLM原始响应: {content}")
                    
                    # 解析JSON
                    result = json.loads(content)
                    if not isinstance(result, dict) or "sql" not in result or "parameters" not in result:
                        raise ValueError("LLM响应格式不正确，缺少sql或parameters字段")
                    
                    sql = result["sql"]
                    parameters = result["parameters"]
                    
                    self.logger.info(f"解析后的SQL: {sql}")
                    self.logger.info(f"解析后的参数: {json.dumps(parameters, ensure_ascii=False)}")
                    
                    return sql, parameters
                else:
                    raise ValueError("LLM返回的响应为空")
            
            except Exception as llm_error:
                self.logger.error(f"LLM调用失败: {str(llm_error)}")
                self.logger.info("回退到模拟SQL生成")
                
                # 回退到模拟的SQL生成方式
                return self._generate_simulated_sql(prompt)
            
        except Exception as e:
            self.logger.error(f"SQL生成失败: {str(e)}")
            raise ValueError(f"无法生成SQL: {str(e)}")
    
    def _generate_simulated_sql(self, prompt: str) -> Tuple[str, Dict[str, Any]]:
        """模拟生成SQL（当LLM调用失败时使用）
        
        Args:
            prompt: 提示信息
            
        Returns:
            tuple: (SQL语句, 参数字典)
        """
        self.logger.info("使用模拟SQL生成")
        
        # 基于提示内容生成不同的SQL
        if "企业基本信息" in prompt or "公司信息" in prompt:
            sql = "SELECT * FROM FINRISK_BASE_INFO WHERE COMPANY_NAME LIKE :company_name"
            parameters = {"company_name": "%公司%"}
        elif "资金流转" in prompt or "资金往来" in prompt:
            sql = """
            SELECT 
                a1.NAME AS 源账户名称,
                a2.NAME AS 目标账户名称,
                t.AMOUNT AS 交易金额,
                t.DESCRIPTION AS 交易描述
            FROM FINRISK_BANK_TRANSFERS t
            JOIN FINRISK_BANK_ACCOUNTS a1 ON t.SRC_ACCT_ID = a1.ID
            JOIN FINRISK_BANK_ACCOUNTS a2 ON t.DST_ACCT_ID = a2.ID
            WHERE a1.NAME LIKE :company_name OR a2.NAME LIKE :company_name
            """
            parameters = {"company_name": "%"}
        elif "新闻" in prompt or "舆情" in prompt:
            sql = """
            SELECT 
                ID, TITLE, SUBSTR(CONTENT, 1, 100) AS 内容摘要, 
                PUBLISH_TIME, SOURCE, TAG
            FROM FINRISK_NEWS
            WHERE TITLE LIKE :keyword OR CONTENT LIKE :keyword
            ORDER BY PUBLISH_TIME DESC
            """
            parameters = {"keyword": "%风险%"}
        else:
            sql = "SELECT * FROM FINRISK_BASE_INFO WHERE ROWNUM <= 10"
            parameters = {}
        
        self.logger.info(f"模拟生成的SQL: {sql}")
        self.logger.info(f"模拟生成的参数: {json.dumps(parameters, ensure_ascii=False)}")
        
        return sql, parameters
    
    def _update_prompt_with_error(self, prompt: str, error: str) -> str:
        """使用错误信息更新提示"""
        return prompt + f"\n\n上次生成失败，错误信息：{error}\n请修正这个问题并重新生成。" 