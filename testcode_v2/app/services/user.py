from typing import Optional, List
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from app.core.security import pwd_context
from app.models.user import User
from app.models.organization import Organization
from app.models.role import Role
from app.schemas.user import UserCreate, UserUpdate


def get_user_by_username(db: Session, username: str) -> Optional[User]:
    """
    通过用户名获取用户
    """
    return db.query(User).filter(User.username == username).first()


def get_user_by_id(db: Session, user_id: int) -> Optional[User]:
    """
    通过用户ID获取用户
    """
    return db.query(User).filter(User.id == user_id).first()


def get_user_by_email(db: Session, email: str) -> Optional[User]:
    """
    通过电子邮件获取用户
    """
    return db.query(User).filter(User.email == email).first()


def create_user(
    db: Session,
    username: str,
    password: str,
    email: Optional[str] = None,
    is_superuser: bool = False,
    role_id: Optional[int] = None,
    org_id: Optional[int] = None,
    pid: int = 0
) -> User:
    """
    创建新用户（支持RBAC）
    """
    # 检查用户名是否已存在
    existing_user = get_user_by_username(db, username)
    if existing_user:
        raise ValueError(f"用户名 '{username}' 已存在")
    
    # 检查邮箱是否已存在
    if email:
        existing_email = get_user_by_email(db, email)
        if existing_email:
            raise ValueError(f"邮箱 '{email}' 已存在")
    
    # 检查企业限额
    if org_id:
        from app.services.organization import OrganizationService
        if not OrganizationService.check_user_limit(db, org_id):
            raise ValueError("企业用户数量已达上限")
    
    hashed_password = pwd_context.hash(password)
    db_user = User(
        username=username,
        email=email,
        hashed_password=hashed_password,
        is_superuser=is_superuser,
        role_id=role_id,
        org_id=org_id,
        pid=pid
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user


def authenticate_user(db: Session, username: str, password: str) -> Optional[User]:
    """
    认证用户
    """
    user = get_user_by_username(db, username)
    if not user:
        return None
    if not pwd_context.verify(password, user.hashed_password):
        return None
    return user


def get_all_users(db: Session, skip: int = 0, limit: int = 100):
    """
    获取所有用户
    """
    return db.query(User).offset(skip).limit(limit).all() 


def get_users_by_parent_id(db: Session, pid: int, skip: int = 0, limit: int = 100):
    """
    根据父级ID获取子用户列表
    """
    return db.query(User).filter(User.pid == pid).offset(skip).limit(limit).all()


def get_users_by_org(
    db: Session, 
    org_id: int, 
    skip: int = 0, 
    limit: int = 100,
    role_id: Optional[int] = None,
    is_active: Optional[bool] = None,
    search: Optional[str] = None
) -> tuple[List[User], int]:
    """
    获取企业用户列表
    """
    query = db.query(User).filter(User.org_id == org_id)
    
    # 角色筛选
    if role_id is not None:
        query = query.filter(User.role_id == role_id)
    
    # 状态筛选
    if is_active is not None:
        query = query.filter(User.is_active == is_active)
    
    # 搜索筛选
    if search:
        query = query.filter(
            or_(
                User.username.contains(search),
                User.email.contains(search)
            )
        )
    
    total = query.count()
    users = query.offset(skip).limit(limit).all()
    
    return users, total


def update_user(
    db: Session, 
    user_id: int, 
    user_data: UserUpdate
) -> Optional[User]:
    """
    更新用户信息
    """
    db_user = db.query(User).filter(User.id == user_id).first()
    if not db_user:
        return None
    
    update_dict = user_data.dict(exclude_unset=True)
    
    # 检查用户名唯一性（排除自己）
    if "username" in update_dict:
        existing_user = db.query(User).filter(
            and_(User.username == update_dict["username"], User.id != user_id)
        ).first()
        if existing_user:
            raise ValueError(f"用户名 '{update_dict['username']}' 已存在")
    
    # 检查邮箱唯一性（排除自己）
    if "email" in update_dict and update_dict["email"]:
        existing_email = db.query(User).filter(
            and_(User.email == update_dict["email"], User.id != user_id)
        ).first()
        if existing_email:
            raise ValueError(f"邮箱 '{update_dict['email']}' 已存在")
    
    # 处理密码更新
    if "password" in update_dict:
        update_dict["hashed_password"] = pwd_context.hash(update_dict.pop("password"))
    
    # 更新字段
    for field, value in update_dict.items():
        setattr(db_user, field, value)
    
    db.commit()
    db.refresh(db_user)
    return db_user


def delete_user(db: Session, user_id: int) -> bool:
    """
    删除用户
    """
    db_user = db.query(User).filter(User.id == user_id).first()
    if not db_user:
        return False
    
    # 检查是否有子用户
    child_count = db.query(User).filter(User.pid == user_id).count()
    if child_count > 0:
        raise ValueError(f"无法删除用户，还有 {child_count} 个子用户")
    
    # 检查是否有关联的项目
    from app.models.project import Project
    project_count = db.query(Project).filter(Project.user_id == user_id).count()
    if project_count > 0:
        raise ValueError(f"无法删除用户，还有 {project_count} 个项目")
    
    db.delete(db_user)
    db.commit()
    return True


def load_user_relations(db: Session, user: User):
    """
    手动加载用户的关联数据（组织和角色）
    """
    # 加载组织信息
    if user.org_id:
        organization = db.query(Organization).filter(Organization.id == user.org_id).first()
        # 动态添加属性
        user.organization = organization
    else:
        user.organization = None
    
    # 加载角色信息
    if user.role_id:
        role = db.query(Role).filter(Role.id == user.role_id).first()
        user.role = role
    else:
        user.role = None
    
    return user


def load_users_relations(db: Session, users: List[User]) -> List[User]:
    """
    批量加载用户的关联数据
    """
    if not users:
        return users
    
    # 提取所有需要查询的ID
    org_ids = list(set([user.org_id for user in users if user.org_id]))
    role_ids = list(set([user.role_id for user in users if user.role_id]))
    
    # 批量查询组织和角色
    organizations = {}
    if org_ids:
        orgs = db.query(Organization).filter(Organization.id.in_(org_ids)).all()
        organizations = {org.id: org for org in orgs}
    
    roles = {}
    if role_ids:
        roles_list = db.query(Role).filter(Role.id.in_(role_ids)).all()
        roles = {role.id: role for role in roles_list}
    
    # 为每个用户设置关联数据
    for user in users:
        user.organization = organizations.get(user.org_id) if user.org_id else None
        user.role = roles.get(user.role_id) if user.role_id else None
    
    return users


def get_user_permissions(db: Session, user_id: int) -> List[str]:
    """
    获取用户的所有权限
    """
    from app.models.role import Role
    
    user = get_user_by_id(db, user_id)
    if not user or not user.role_id:
        return []
    
    # 手动查询角色信息
    role = db.query(Role).filter(Role.id == user.role_id).first()
    if not role:
        return []
    
    return role.permissions or []


def check_user_permission(db: Session, user_id: int, permission: str) -> bool:
    """
    检查用户是否有指定权限
    """
    user = get_user_by_id(db, user_id)
    if not user:
        return False
    
    return user.has_permission(permission, db)


def get_accessible_user_ids(db: Session, user_id: int):
    """
    获取用户可访问的用户ID列表（包括自己和所有子用户）
    """
    user_ids = [user_id]
    
    def get_child_users(pid):
        children = db.query(User).filter(User.pid == pid).all()
        for child in children:
            user_ids.append(child.id)
            get_child_users(child.id)  # 递归获取子用户的子用户
    
    get_child_users(user_id)
    return user_ids


def check_project_access_permission(db: Session, user_id: int, project_id: str):
    """
    检查用户是否有访问某个项目的权限
    """
    from app.models.project import Project
    
    # 获取项目信息
    project = db.query(Project).filter(Project.id == project_id).first()
    if not project:
        return False
    
    # 检查是否是项目创建者或有权限访问的用户
    accessible_user_ids = get_accessible_user_ids(db, user_id)
    return project.user_id in accessible_user_ids 