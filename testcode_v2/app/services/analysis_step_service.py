"""
分析步骤管理服务
================
用于保存和还原完整的分析时间轴，确保历史会话能够完整展示分析过程
"""

from typing import List, Dict, Any, Optional
from datetime import datetime, timezone, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import desc

from app.models.analysis import Analysis, AnalysisStepDetail
from app.core.logger import log


def get_shanghai_time():
    """获取上海时区的当前时间"""
    shanghai_tz = timezone(timedelta(hours=8))
    return datetime.now(shanghai_tz).replace(tzinfo=None)


class AnalysisStepService:
    """分析步骤管理服务"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def save_step(
        self,
        analysis_id: str,
        step_id: str,
        step_name: str,
        step_type: str,
        status: str = 'process',
        step_order: int = None,
        description: str = None,
        tool_name: str = None,
        parameters: Dict[str, Any] = None,
        has_result: bool = False,
        result_key: str = None,
        has_intent_data: bool = False,
        has_report: bool = False,
        has_chart_data: bool = False,
        plan_data: Dict[str, Any] = None,
        reasoning: str = None,
        execution_time: float = None,
        error_message: str = None,
        interaction_type: str = None,
        interaction_data: Dict[str, Any] = None
    ) -> bool:
        """保存分析步骤"""
        try:
            # 处理数据冗余：如果 step_name 和 reasoning 完全一样，则清空 reasoning
            if reasoning and step_name and reasoning.strip() == step_name.strip():
                reasoning = None
            # 如果没有指定步骤顺序，自动计算
            if step_order is None:
                max_order_result = self.db.query(AnalysisStepDetail.step_order).filter(
                    AnalysisStepDetail.analysis_id == analysis_id
                ).order_by(AnalysisStepDetail.step_order.desc()).first()
                step_order = (max_order_result[0] + 1) if max_order_result else 1
            
            # 检查是否已存在相同步骤
            existing_step = self.db.query(AnalysisStepDetail).filter(
                AnalysisStepDetail.analysis_id == analysis_id,
                AnalysisStepDetail.step_id == step_id
            ).first()
            
            if existing_step:
                # 更新现有步骤
                existing_step.step_name = step_name
                existing_step.status = status
                existing_step.description = description
                existing_step.tool_name = tool_name
                existing_step.parameters = parameters
                existing_step.has_result = 1 if has_result else 0
                existing_step.result_key = result_key
                existing_step.has_intent_data = 1 if has_intent_data else 0
                existing_step.has_report = 1 if has_report else 0
                existing_step.has_chart_data = 1 if has_chart_data else 0
                existing_step.plan_data = plan_data
                existing_step.reasoning = reasoning
                existing_step.execution_time = execution_time
                existing_step.error_message = error_message
                existing_step.interaction_type = interaction_type
                existing_step.interaction_data = interaction_data
                # updated_at 由数据库自动维护，不需要手动设置
                
                if status == 'finish' and existing_step.end_time is None:
                    existing_step.end_time = get_shanghai_time()
                
                log.info(f"更新分析步骤: {step_id} -> {step_name}")
            else:
                # 创建新步骤
                current_time = get_shanghai_time()
                step_detail = AnalysisStepDetail(
                    analysis_id=analysis_id,
                    step_id=step_id,
                    step_name=step_name,
                    step_type=step_type,
                    status=status,
                    step_order=step_order,
                    start_time=current_time,
                    end_time=current_time if status == 'finish' else None,
                    execution_time=execution_time,
                    description=description,
                    tool_name=tool_name,
                    parameters=parameters,
                    has_result=1 if has_result else 0,
                    result_key=result_key,
                    has_intent_data=1 if has_intent_data else 0,
                    has_report=1 if has_report else 0,
                    has_chart_data=1 if has_chart_data else 0,
                    plan_data=plan_data,
                    reasoning=reasoning,
                    error_message=error_message,
                    interaction_type=interaction_type,
                    interaction_data=interaction_data
                )
                
                self.db.add(step_detail)
                log.info(f"保存新分析步骤: {step_id} -> {step_name}")
            
            self.db.commit()
            return True
            
        except Exception as e:
            log.error(f"保存分析步骤失败: {str(e)}")
            self.db.rollback()
            return False
    
    def update_step_status(
        self,
        analysis_id: str,
        step_id: str,
        status: str,
        execution_time: float = None,
        reasoning: str = None,
        error_message: str = None,
        step_name: str = None,
        description: str = None
    ) -> bool:
        """更新步骤状态"""
        try:
            step = self.db.query(AnalysisStepDetail).filter(
                AnalysisStepDetail.analysis_id == analysis_id,
                AnalysisStepDetail.step_id == step_id
            ).first()
            
            if step:
                step.status = status
                # updated_at 由数据库自动维护，不需要手动设置
                
                if status == 'finish' and step.end_time is None:
                    step.end_time = get_shanghai_time()
                
                if execution_time is not None:
                    step.execution_time = execution_time
                
                if reasoning is not None:
                    step.reasoning = reasoning
                
                if error_message is not None:
                    step.error_message = error_message
                
                if step_name is not None:
                    step.step_name = step_name
                
                if description is not None:
                    step.description = description
                
                self.db.commit()
                log.info(f"更新步骤状态: {step_id} -> {status}")
                return True
            else:
                log.warning(f"未找到步骤: {step_id}")
                return False
                
        except Exception as e:
            log.error(f"更新步骤状态失败: {str(e)}")
            self.db.rollback()
            return False
    
    def update_step_with_result(
        self,
        analysis_id: str,
        step_id: str,
        status: str,
        step_name: str = None,
        description: str = None,
        tool_name: str = None,
        parameters: Dict[str, Any] = None,
        interaction_data: Dict[str, Any] = None,
        execution_time: float = None
    ) -> bool:
        """更新步骤并添加结果数据"""
        try:
            step = self.db.query(AnalysisStepDetail).filter(
                AnalysisStepDetail.analysis_id == analysis_id,
                AnalysisStepDetail.step_id == step_id
            ).first()
            
            if step:
                step.status = status
                # updated_at 由数据库自动维护，不需要手动设置
                
                if status == 'finish' and step.end_time is None:
                    step.end_time = get_shanghai_time()
                
                if step_name is not None:
                    step.step_name = step_name
                
                if description is not None:
                    step.description = description
                
                if tool_name is not None:
                    step.tool_name = tool_name
                    step.step_type = 'tool'  # 更新为工具类型
                
                if parameters is not None:
                    step.parameters = parameters
                
                if interaction_data is not None:
                    step.interaction_data = interaction_data
                
                if execution_time is not None:
                    step.execution_time = execution_time
                
                self.db.commit()
                log.info(f"更新步骤结果: {step_id}")
                return True
            else:
                log.warning(f"未找到步骤: {step_id}")
                return False
                
        except Exception as e:
            log.error(f"更新步骤结果失败: {str(e)}")
            self.db.rollback()
            return False

    def update_step_with_user_response(
        self,
        analysis_id: str,
        step_id: str,
        status: str,
        description: str,
        user_answers: Dict[str, Any],
        user_questions: List[Dict[str, Any]]
    ) -> bool:
        """更新智能交互步骤，添加用户回复数据"""
        try:
            step = self.db.query(AnalysisStepDetail).filter(
                AnalysisStepDetail.analysis_id == analysis_id,
                AnalysisStepDetail.step_id == step_id
            ).first()
            
            if step:
                step.status = status
                step.description = description
                # updated_at 由数据库自动维护，不需要手动设置
                
                if status == 'finish' and step.end_time is None:
                    step.end_time = get_shanghai_time()
                
                # 更新parameters，添加用户回复数据
                if step.parameters is None:
                    step.parameters = {}
                
                step.parameters.update({
                    'userClarificationAnswers': user_answers,
                    'userClarificationQuestions': user_questions,
                    'hasUserResponse': True,
                    'completed': True
                })
                
                # 更新interaction_data
                if step.interaction_data is None:
                    step.interaction_data = {}
                
                step.interaction_data.update({
                    'user_answers': user_answers,
                    'user_questions': user_questions,
                    'completed': True,
                    'response_type': 'user_clarification'
                })
                
                self.db.commit()
                log.info(f"更新智能交互步骤用户回复: {step_id}")
                return True
            else:
                log.warning(f"未找到智能交互步骤: {step_id}")
                return False
                
        except Exception as e:
            log.error(f"更新智能交互步骤用户回复失败: {str(e)}")
            self.db.rollback()
            return False
    
    def get_analysis_steps(self, analysis_id: str) -> List[Dict[str, Any]]:
        """获取分析的所有步骤"""
        try:
            steps = self.db.query(AnalysisStepDetail).filter(
                AnalysisStepDetail.analysis_id == analysis_id
            ).order_by(AnalysisStepDetail.id.asc()).all()
            
            return [step.to_dict() for step in steps]
            
        except Exception as e:
            log.error(f"获取分析步骤失败: {str(e)}")
            return []
    
    def save_steps_batch(
        self,
        analysis_id: str,
        steps: List[Dict[str, Any]]
    ) -> bool:
        """批量保存分析步骤"""
        try:
            # 先删除现有步骤
            self.db.query(AnalysisStepDetail).filter(
                AnalysisStepDetail.analysis_id == analysis_id
            ).delete()
            
            # 批量插入新步骤
            for i, step in enumerate(steps):
                step_detail = AnalysisStepDetail(
                    analysis_id=analysis_id,
                    step_id=step.get('id', f'step_{i}'),
                    step_name=step.get('name', '未知步骤'),
                    step_type=self._determine_step_type(step),
                    status=step.get('status', 'finish'),
                    step_order=i + 1,
                    start_time=datetime.fromisoformat(step['time'].replace('Z', '+00:00')) if step.get('time') else get_shanghai_time(),
                    end_time=datetime.fromisoformat(step['time'].replace('Z', '+00:00')) if step.get('time') and step.get('status') == 'finish' else None,
                    execution_time=step.get('executionTime'),
                    description=step.get('description'),
                    tool_name=step.get('tool_name'),
                    parameters=step.get('parameters'),
                    has_result=1 if step.get('hasResult', False) else 0,
                    result_key=step.get('resultKey'),
                    has_intent_data=1 if step.get('hasIntentData', False) else 0,
                    has_report=1 if step.get('hasReport', False) else 0,
                    plan_data=step.get('planData'),
                    error_message=step.get('errorMessage')
                )
                
                self.db.add(step_detail)
            
            self.db.commit()
            log.info(f"批量保存分析步骤成功: {analysis_id}, 共{len(steps)}个步骤")
            return True
            
        except Exception as e:
            log.error(f"批量保存分析步骤失败: {str(e)}")
            self.db.rollback()
            return False
    
    def _determine_step_type(self, step: Dict[str, Any]) -> str:
        """根据步骤信息判断步骤类型"""
        step_name = step.get('name', '').lower()
        step_id = step.get('id', '').lower()
        
        if 'evaluation' in step_id or '智能评估' in step.get('name', '') or 'evaluation' in step_name:
            return 'evaluation'
        elif step.get('tool_name'):
            return 'tool'
        elif 'report' in step_name or 'report' in step_id or step.get('hasReport'):
            return 'report'
        elif 'plan' in step_name or 'plan' in step_id or step.get('planData'):
            return 'planning'
        elif 'error' in step_name or 'error' in step_id or step.get('errorMessage'):
            return 'error'
        else:
            return 'system'
    
    def cleanup_old_steps(self, analysis_id: str, keep_count: int = 100) -> bool:
        """清理旧的步骤记录，保留最新的指定数量"""
        try:
            # 获取需要删除的步骤
            steps_to_delete = self.db.query(AnalysisStepDetail).filter(
                AnalysisStepDetail.analysis_id == analysis_id
            ).order_by(desc(AnalysisStepDetail.step_order)).offset(keep_count).all()
            
            if steps_to_delete:
                for step in steps_to_delete:
                    self.db.delete(step)
                
                self.db.commit()
                log.info(f"清理旧步骤记录: {analysis_id}, 删除{len(steps_to_delete)}个步骤")
            
            return True
            
        except Exception as e:
            log.error(f"清理旧步骤记录失败: {str(e)}")
            self.db.rollback()
            return False 