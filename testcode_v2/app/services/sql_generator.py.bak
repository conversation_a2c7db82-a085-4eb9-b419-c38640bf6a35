import os
import re
import json
import logging
from typing import Dict, List, Any, Optional, Tuple, Union
from openai import OpenAI
from app.core.config import settings
from app.services.llm.base import BaseLLM
from tenacity import retry, stop_after_attempt, wait_random_exponential

# 配置日志
logger = logging.getLogger(__name__)

class SQLGenerator:
    """SQL生成器服务，使用LLM将自然语言问题转换为SQL查询"""
    
    def __init__(self, llm: BaseLLM = None):
        """
        初始化SQL生成器
        
        Args:
            llm: 大模型接口
        """
        self.client = OpenAI(
            api_key=settings.OPENAI_API_KEY,
            base_url=settings.OPENAI_BASE_URL
        )
        self.model = settings.OPENAI_MODEL
        self.llm = llm
    
    def clean_sql(self, sql_text: str) -> str:
        """
        清理SQL文本，移除Markdown格式和其他不必要的标记
        
        参数:
            sql_text: 原始SQL文本
            
        返回:
            清理后的SQL文本
        """
        # 移除```sql 和 ``` 标记
        sql_clean = re.sub(r'```sql\s*', '', sql_text)
        sql_clean = re.sub(r'```', '', sql_clean)
        
        # 移除SQL--注释
        sql_clean = re.sub(r'--.*$', '', sql_clean, flags=re.MULTILINE)
        
        # 移除可能的多余空行
        sql_clean = re.sub(r'\n\s*\n', '\n', sql_clean)
        
        # 移除开头和结尾的空白字符
        sql_clean = sql_clean.strip()
        
        return sql_clean

    async def generate_sql(self, user_question: str, database_schema: Dict[str, Any], 
                           ds_type: str = None, db_type: str = None, 
                           additional_instructions: str = None) -> str:
        """
        根据用户问题和数据库模式生成SQL语句
        
        Args:
            user_question: 用户问题
            database_schema: 数据库模式，包含表名和列名
            ds_type: 数据源类型
            db_type: 数据库类型，如'oracle', 'mysql'等
            additional_instructions: 额外的指示
        
        Returns:
            生成的SQL语句
        """
        logger.info(f"生成SQL查询：问题='{user_question}'，数据库类型='{db_type}'")
        
        # 将数据库模式格式化为文本
        schema_text = self._format_schema(database_schema)
        
        # 获取数据库特定的提示
        db_hints = self._get_database_specific_hints(db_type)
        
        # 构建提示，采用更结构化的prompt格式
        prompt = self._build_structured_sql_prompt(user_question, schema_text, db_type, db_hints, additional_instructions)
        
        logger.debug(f"SQL生成提示：\n{prompt}")
        
        try:
            # 调用大模型生成SQL
            response = await self._call_llm(prompt)
            
            # 提取SQL语句
            sql = self._extract_sql(response)
            
            # 后处理SQL（如处理Oracle的LIMIT）
            sql = self._post_process_sql(sql, db_type)
            
            logger.info(f"SQL生成成功：{sql}")
            return sql
            
        except Exception as e:
            logger.error(f"SQL生成失败: {str(e)}", exc_info=True)
            raise
    
    def _build_structured_sql_prompt(self, user_question: str, schema_text: str, 
                                   db_type: str, db_hints: str, 
                                   additional_instructions: Optional[str]) -> str:
        """构建结构化的SQL生成提示"""
        # 基于固定模板构建更结构化的prompt
        prompt = f"""你是一个专业的SQL生成助手。我将提供数据库表结构和一个用户问题，请你生成一个能够回答该问题的SQL查询语句。

## 数据库类型
{db_type or '未知'}

## 表结构
{schema_text}

## 用户问题
{user_question}

## 特定要求
{db_hints}

{additional_instructions or ''}

请注意：
1. 只返回SQL语句，不需要任何解释或其他内容
2. 确保使用正确的表名和列名，严格按照提供的数据库表结构
3. 根据问题选择合适的表和列进行查询
4. 如果需要连接多个表，请使用适当的JOIN条件
5. 如果数据库是Oracle，对于VARCHAR2类型字段与数字进行比较时，需要使用TO_NUMBER()函数
6. 对于字符串类型和数字类型的比较，使用恰当的类型转换函数

请生成SQL语句：
"""
        
        return prompt
    
    def _format_schema(self, database_schema: Dict[str, Any]) -> str:
        """
        将数据库模式格式化为文本
        
        Args:
            database_schema: 数据库模式，包含表名和列名及其他信息
        
        Returns:
            格式化后的文本
        """
        if not database_schema or "tables" not in database_schema:
            return "数据库模式为空"
        
        schema_text = []
        
        # 遍历所有表
        for table_name, columns in database_schema["tables"].items():
            # 获取表注释（如果有）
            table_comment = ""
            if "table_comments" in database_schema and table_name in database_schema["table_comments"]:
                table_comment = f" - {database_schema['table_comments'][table_name]}"
            
            schema_text.append(f"表: {table_name}{table_comment}")
            
            # 遍历表的所有列
            for col_name, col_info in columns.items():
                col_type = col_info.get("type", "未知类型")
                is_nullable = "可为空" if col_info.get("nullable", True) else "非空"
                
                # 获取列注释（如果有）
                col_comment = ""
                if "comment" in col_info and col_info["comment"]:
                    col_comment = f" - {col_info['comment']}"
                
                schema_text.append(f"  - {col_name} ({col_type}, {is_nullable}){col_comment}")
            
            # 添加主键信息（如果有）
            if "primary_keys" in database_schema and table_name in database_schema["primary_keys"]:
                primary_keys = database_schema["primary_keys"][table_name]
                if primary_keys:
                    schema_text.append(f"  主键: {', '.join(primary_keys)}")
            
            # 添加外键信息（如果有）
            if "foreign_keys" in database_schema and table_name in database_schema["foreign_keys"]:
                for fk_col, fk_info in database_schema["foreign_keys"][table_name].items():
                    ref_table = fk_info.get("table", "")
                    ref_col = fk_info.get("column", "")
                    if ref_table and ref_col:
                        schema_text.append(f"  外键: {fk_col} -> {ref_table}.{ref_col}")
            
            schema_text.append("")  # 添加空行分隔不同表
        
        return "\n".join(schema_text)
    
    def _get_database_specific_hints(self, db_type: Optional[str]) -> str:
        """
        获取数据库特定的提示
        
        Args:
            db_type: 数据库类型
        
        Returns:
            数据库特定的提示
        """
        if not db_type:
            return ""
        
        db_type = db_type.lower()
        
        if db_type == "oracle":
            return """
Oracle SQL 语法特点：
1. 使用ROWNUM进行分页，而不是LIMIT。例如：SELECT * FROM (SELECT t.*, ROWNUM AS rn FROM table t WHERE ...) WHERE rn BETWEEN 1 AND 10
2. Oracle不支持LIMIT语法，要使用ROWNUM实现分页查询
3. 字符串使用单引号，如'value'
4. 表别名不需要AS关键字
5. 日期格式使用TO_DATE('2023-01-01', 'YYYY-MM-DD')
6. 字符串连接使用||而非+
7. 对于VARCHAR2类型字段与数字进行比较时，**必须**使用TO_NUMBER()函数
8. 下面这些字段被识别为存储了数字的VARCHAR2类型，进行数值比较时必须使用TO_NUMBER()函数:
   - BGSL（被告数量）
   - FMYQSL（负面舆情数量）
   - IS_SXQY（是否失信企业，1表示是，0表示否）
   - IS_SXBZXR（是否是失信被执行人，1表示是，0表示否）
   - IS_WDQKTGG（是否有未到期开庭公告，1表示是，0表示否）
9. 子查询必须有别名
10. 聚合函数：AVG, COUNT, MAX, MIN, SUM等
11. TOP N查询使用ROW_NUMBER() OVER (ORDER BY ...)
12. NULL值比较使用IS NULL或IS NOT NULL，不能用=NULL
13. 字符串模糊匹配：LIKE '%关键词%'，大小写敏感，使用UPPER或LOWER函数实现不区分大小写的查询
"""
        elif db_type == "mysql":
            return """
MySQL SQL 语法特点：
1. 分页使用LIMIT语法，例如：LIMIT 10 或 LIMIT 10, 20（跳过10行，返回20行）
2. 表别名可以使用AS关键字，也可以省略
3. 日期格式使用STR_TO_DATE('2023-01-01', '%Y-%m-%d')
4. 字符串连接使用CONCAT()函数
5. NULL值比较使用IS NULL或IS NOT NULL
6. 字符串模糊匹配使用LIKE '%关键词%'
"""
        else:
            return ""
    
    def _extract_sql(self, response: str) -> str:
        """
        从大模型响应中提取SQL语句
        
        Args:
            response: 大模型响应
        
        Returns:
            提取的SQL语句
        """
        # 如果响应包含三个反引号（Markdown代码块格式），提取其中的内容
        code_block_match = re.search(r"```(?:sql)?(.*?)```", response, re.DOTALL)
        if code_block_match:
            return code_block_match.group(1).strip()
        
        # 否则返回整个响应，去掉前后的空白字符
        return response.strip()
    
    def _post_process_sql(self, sql: str, db_type: Optional[str]) -> str:
        """
        对生成的SQL进行后处理，处理特定数据库的语法
        
        Args:
            sql: 原始SQL语句
            db_type: 数据库类型
        
        Returns:
            处理后的SQL语句
        """
        if not db_type:
            return sql
        
        db_type = db_type.lower()
        
        if db_type == "oracle":
            # 处理LIMIT子句，将其转换为Oracle风格的分页
            limit_match = re.search(r"\bLIMIT\s+(\d+)(?:\s*,\s*(\d+))?", sql, re.IGNORECASE)
            if limit_match:
                # 提取LIMIT参数
                offset = 0
                limit = int(limit_match.group(1))
                
                if limit_match.group(2):  # 如果有两个参数(LIMIT offset, limit)
                    offset = limit
                    limit = int(limit_match.group(2))
                
                # 从SQL中移除LIMIT子句
                sql = re.sub(r"\bLIMIT\s+\d+(?:\s*,\s*\d+)?", "", sql, flags=re.IGNORECASE)
                
                # 添加Oracle风格的分页
                if offset == 0:
                    # 简单情况：LIMIT n => WHERE ROWNUM <= n
                    # 注意：这种替换只适用于没有ORDER BY的简单查询
                    if " ORDER BY " in sql.upper():
                        # 对于包含ORDER BY的查询，需要使用子查询
                        sql = f"SELECT * FROM ({sql}) WHERE ROWNUM <= {limit}"
                    else:
                        # 简单情况可以直接添加ROWNUM条件
                        # 检查是否已有WHERE子句
                        if " WHERE " in sql.upper():
                            sql = sql.replace(" WHERE ", f" WHERE ROWNUM <= {limit} AND ", 1)
                        else:
                            # 如果没有WHERE子句，添加一个
                            sql = f"{sql} WHERE ROWNUM <= {limit}"
                else:
                    # 复杂情况：LIMIT offset, limit => 使用ROW_NUMBER()或嵌套查询
                    # 这里使用嵌套查询，可能更适合Oracle 11g及以下版本
                    sql = f"""
SELECT *
FROM (
    SELECT a.*, ROWNUM rnum
    FROM ({sql}) a
    WHERE ROWNUM <= {offset + limit}
)
WHERE rnum > {offset}
"""
            
            # 处理VARCHAR2类型字段与数字比较的情况
            # 使用TO_NUMBER函数包装这些VARCHAR2字段
            varchar2_numeric_fields = ['BGSL', 'FMYQSL', 'IS_SXQY', 'IS_SXBZXR', 'IS_WDQKTGG']
            
            for field in varchar2_numeric_fields:
                # 匹配整个列名（避免部分匹配），后面跟着比较运算符
                # 正则表达式中的\b表示单词边界，确保匹配整个列名
                for operator in ['=', '>', '<', '>=', '<=', '<>', '!=']:
                    # 处理以下模式：
                    # 1. FIELD = 数字
                    pattern = rf'(\b{field}\b\s*{re.escape(operator)}\s*\d+)'
                    sql = re.sub(pattern, lambda m: f'TO_NUMBER({field}) {operator} ' + m.group(0).split(operator)[1].strip(), sql, flags=re.IGNORECASE)
                    
                    # 2. 数字 = FIELD
                    pattern = rf'(\d+\s*{re.escape(operator)}\s*\b{field}\b)'
                    sql = re.sub(pattern, lambda m: m.group(0).split(operator)[0].strip() + f' {operator} TO_NUMBER({field})', sql, flags=re.IGNORECASE)
                    
                    # 3. FIELD 在IN列表中
                    in_pattern = rf'(\b{field}\b\s+IN\s*\([\d\s,]+\))'
                    sql = re.sub(in_pattern, lambda m: f'TO_NUMBER({field}) IN' + m.group(0).split('IN')[1], sql, flags=re.IGNORECASE)
        
        return sql
    
    async def _call_llm(self, prompt: str) -> str:
        """
        调用大模型生成文本
        
        Args:
            prompt: 提示文本
        
        Returns:
            生成的文本
        """
        if not self.llm:
            raise ValueError("LLM未初始化")
        
        try:
            response = await self.llm.generate(prompt)
            return response
        except Exception as e:
            logger.error(f"调用LLM失败: {str(e)}", exc_info=True)
            raise

    def analyze_query_complexity(self, question: str, tables: Dict[str, List[str]], db_type: str = "mysql") -> Dict[str, Any]:
        """
        分析查询复杂度，判断是否需要多步查询
        
        参数:
            question: 用户的自然语言问题
            tables: 数据库表结构信息
            db_type: 数据库类型
        
        返回:
            复杂度评估和分解建议
        """
        table_info = ""
        for table_name, columns in tables.items():
            table_info += f"表名: {table_name}\n列: {', '.join(columns)}\n\n"
        
        # 获取数据库特定提示
        db_specific_hints = self._get_database_specific_hints(db_type)
        
        prompt = f"""
        作为一个{db_type.upper()}数据库专家，请评估以下用户问题的复杂性，并判断是否需要多步查询来解决。
        
        数据库类型: {db_type.upper()}
        
        数据库表结构:
        {table_info}
        
        {db_specific_hints}
        
        用户问题: {question}
        
        请分析:
        1. 这个问题的复杂度（简单/中等/复杂）
        2. 是否需要多表连接
        3. 是否需要聚合计算
        4. 是否需要子查询
        5. 是否需要多步处理
        
        以JSON格式返回你的分析结果，格式如下:
        {{"complexity": "简单/中等/复杂", "needs_joins": true/false, "needs_aggregation": true/false, "needs_subqueries": true/false, "needs_multi_steps": true/false}}
        """
        
        logger.debug(f"复杂度分析提示: {prompt}")
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": f"你是一个{db_type.upper()}数据库专家，擅长评估查询复杂度和SQL优化。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1
            )
            
            # 提取结果
            result = response.choices[0].message.content.strip()
            try:
                # 尝试提取JSON部分
                json_match = re.search(r'({.*})', result, re.DOTALL)
                if json_match:
                    json_str = json_match.group(1)
                    return json.loads(json_str)
                
                # 如果没有匹配到JSON，尝试直接解析
                return json.loads(result)
            except Exception as e:
                # 如果解析失败，返回默认值
                logger.warning(f"复杂度分析结果解析失败: {result}")
                return {"complexity": "中等", "needs_joins": True, "needs_aggregation": True, "needs_subqueries": False, "needs_multi_steps": False}
                
        except Exception as e:
            logger.error(f"复杂度分析失败: {str(e)}")
            # 返回默认值
            return {"complexity": "中等", "needs_joins": True, "needs_aggregation": True, "needs_subqueries": False, "needs_multi_steps": False} 