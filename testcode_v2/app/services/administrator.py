from typing import Optional, List
from datetime import datetime
from sqlalchemy.orm import Session, aliased
from sqlalchemy import func, case

from app.core.security import pwd_context
from app.models.administrator import Administrator
from app.models.user import User
from app.utils.time_utils import get_shanghai_time


def get_admin_by_username(db: Session, username: str) -> Optional[Administrator]:
    """
    通过用户名获取管理员
    """
    return db.query(Administrator).filter(Administrator.username == username).first()


def get_admin_by_id(db: Session, admin_id: int) -> Optional[Administrator]:
    """
    通过ID获取管理员
    """
    return db.query(Administrator).filter(Administrator.id == admin_id).first()


def get_admin_by_email(db: Session, email: str) -> Optional[Administrator]:
    """
    通过邮箱获取管理员
    """
    return db.query(Administrator).filter(Administrator.email == email).first()


def create_admin(
    db: Session,
    username: str,
    password: str,
    email: Optional[str] = None
) -> Administrator:
    """
    创建新管理员
    """
    hashed_password = pwd_context.hash(password)
    db_admin = Administrator(
        username=username,
        email=email,
        hashed_password=hashed_password,
        is_active=True
    )
    db.add(db_admin)
    db.commit()
    db.refresh(db_admin)
    return db_admin


def authenticate_admin(db: Session, username: str, password: str) -> Optional[Administrator]:
    """
    认证管理员
    """
    admin = get_admin_by_username(db, username)
    if not admin:
        return None
    if not admin.is_active:
        return None
    if not pwd_context.verify(password, admin.hashed_password):
        return None
    
    # 更新最后登录时间
    admin.last_login_at = get_shanghai_time()
    db.commit()
    return admin


def get_all_admins(db: Session, skip: int = 0, limit: int = 100) -> List[Administrator]:
    """
    获取所有管理员
    """
    return db.query(Administrator).offset(skip).limit(limit).all()


def update_admin(
    db: Session,
    admin_id: int,
    **update_data
) -> Optional[Administrator]:
    """
    更新管理员信息
    """
    admin = get_admin_by_id(db, admin_id)
    if not admin:
        return None
    
    for field, value in update_data.items():
        if hasattr(admin, field) and value is not None:
            if field == "password":
                # 密码需要哈希处理
                setattr(admin, "hashed_password", pwd_context.hash(value))
            else:
                setattr(admin, field, value)
    
    db.commit()
    db.refresh(admin)
    return admin


def delete_admin(db: Session, admin_id: int) -> bool:
    """
    删除管理员
    """
    admin = get_admin_by_id(db, admin_id)
    if not admin:
        return False
    
    db.delete(admin)
    db.commit()
    return True


# 用户管理相关服务 - 管理员可以操作用户
def get_all_users_for_admin(db: Session, skip: int = 0, limit: int = 100) -> List[User]:
    """
    管理员获取所有用户列表（包含企业、角色、父级用户名和子账号数量信息）
    """
    # 创建父用户别名
    parent_user = aliased(User)
    
    # 子查询：计算每个用户的子账号数量
    children_count_subquery = (
        db.query(User.pid, func.count(User.id).label('children_count'))
        .group_by(User.pid)
        .subquery()
    )
    
    # 主查询：获取用户列表，包含父级用户名和子账号数量
    query = (
        db.query(User)
        .outerjoin(parent_user, User.pid == parent_user.id)
        .outerjoin(children_count_subquery, User.id == children_count_subquery.c.pid)
        .add_columns(
            parent_user.username.label('parent_username'),
            func.coalesce(children_count_subquery.c.children_count, 0).label('children_count')
        )
        .offset(skip)
        .limit(limit)
    )
    
    results = query.all()
    
    # 处理结果，为User对象添加额外字段
    users = []
    for result in results:
        user = result[0]  # User对象
        parent_username = result[1]  # 父级用户名
        children_count = result[2]  # 子账号数量
        
        # 动态添加属性
        user.parent_username = parent_username
        user.children_count = children_count
        users.append(user)
    
    # 手动加载关联数据
    from app.services.user import load_users_relations
    users = load_users_relations(db, users)
    
    return users


def get_user_by_id_for_admin(db: Session, user_id: int) -> Optional[User]:
    """
    管理员通过ID获取用户（包含企业、角色、父级用户名和子账号数量信息）
    """
    # 创建父用户别名
    parent_user = aliased(User)
    
    # 子查询：计算该用户的子账号数量
    children_count = (
        db.query(func.count(User.id))
        .filter(User.pid == user_id)
        .scalar() or 0
    )
    
    # 主查询：获取用户信息，包含父级用户名
    result = (
        db.query(User)
        .outerjoin(parent_user, User.pid == parent_user.id)
        .add_columns(parent_user.username.label('parent_username'))
        .filter(User.id == user_id)
        .first()
    )
    
    if result:
        user = result[0]  # User对象
        parent_username = result[1]  # 父级用户名
        
        # 动态添加属性
        user.parent_username = parent_username
        user.children_count = children_count
        
        # 手动加载关联数据
        from app.services.user import load_user_relations
        user = load_user_relations(db, user)
        
        return user
    
    return None


def create_user_by_admin(
    db: Session,
    username: str,
    password: str,
    email: Optional[str] = None,
    pid: int = 0,
    is_superuser: bool = False,
    org_id: Optional[int] = None,
    role_id: Optional[int] = None,
    is_active: bool = True
) -> User:
    """
    管理员创建用户（限制只能创建父级用户，pid固定为0）
    """
    hashed_password = pwd_context.hash(password)
    db_user = User(
        username=username,
        email=email,
        hashed_password=hashed_password,
        pid=0,  # 强制设置为0，后台管理只能创建父级用户
        is_superuser=is_superuser,
        is_active=is_active,
        org_id=org_id,
        role_id=role_id
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user


def update_user_by_admin(
    db: Session,
    user_id: int,
    **update_data
) -> Optional[User]:
    """
    管理员更新用户信息
    """
    user = get_user_by_id_for_admin(db, user_id)
    if not user:
        return None
    
    for field, value in update_data.items():
        if hasattr(user, field) and value is not None:
            if field == "password":
                # 密码需要哈希处理
                setattr(user, "hashed_password", pwd_context.hash(value))
            else:
                setattr(user, field, value)
    
    db.commit()
    db.refresh(user)
    return user


def delete_user_by_admin(db: Session, user_id: int) -> bool:
    """
    管理员删除用户
    """
    user = get_user_by_id_for_admin(db, user_id)
    if not user:
        return False
    
    db.delete(user)
    db.commit()
    return True


def get_users_count(db: Session) -> int:
    """
    获取用户总数
    """
    return db.query(User).count()


def get_children_count_by_user_id(db: Session, user_id: int) -> int:
    """
    获取指定用户的子账号数量
    """
    return db.query(User).filter(User.pid == user_id).count()


def get_users_by_parent_id(db: Session, pid: int, skip: int = 0, limit: int = 100) -> List[User]:
    """
    根据父级ID获取子用户列表（包含企业和角色信息）
    """
    users = (db.query(User)
            .filter(User.pid == pid)
            .offset(skip)
            .limit(limit)
            .all())
    
    # 手动加载关联数据
    from app.services.user import load_users_relations
    users = load_users_relations(db, users)
    
    return users 