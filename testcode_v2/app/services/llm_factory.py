import logging
from typing import Optional
from sqlalchemy.orm import Session

from app.services.llm import LLMService
from app.services.llm.openai_service import OpenAIService
from app.services.llm_model_service import LLMModelService
from app.models.llm_model import LLMModel
from app.core.config import settings


class LLMFactory:
    """LLM服务工厂类，用于根据配置动态创建LLM服务实例"""
    
    def __init__(self, db: Session = None):
        self.db = db
        self.logger = logging.getLogger("app.services.llm_factory")
        self._model_service = LLMModelService(db) if db else None
    
    def create_service(self, model_id: Optional[str] = None) -> LLMService:
        """
        创建LLM服务实例

        Args:
            model_id: 指定的模型ID，如果为None则使用第一个可用模型

        Returns:
            LLM服务实例
        """
        # 如果没有数据库连接，使用配置文件中的默认设置
        if not self.db or not self._model_service:
            return self._create_default_service()

        # 获取指定模型或第一个可用模型
        if model_id:
            model = self._model_service.get_model(model_id)
        else:
            model = self._model_service.get_active_model_for_service()

        if not model:
            self.logger.warning("未找到可用的LLM模型配置，使用默认配置")
            return self._create_default_service()

        return self._create_service_from_model(model)
    
    def _create_default_service(self) -> LLMService:
        """使用配置文件中的默认设置创建服务"""
        self.logger.info("使用默认配置创建OpenAI服务")
        return OpenAIService(
            api_key=settings.OPENAI_API_KEY,
            base_url=settings.OPENAI_BASE_URL,
            model=settings.OPENAI_MODEL
        )
    
    def _create_service_from_model(self, model: LLMModel) -> LLMService:
        """根据模型配置创建服务"""
        self.logger.info(f"使用模型配置创建服务: {model.name} ({model.model_name})")

        # 简化后只支持OpenAI协议
        return OpenAIService(
            api_key=model.api_key,
            base_url=model.base_url,
            model=model.model_name,
            model_id=model.id  # 传递模型ID用于Token统计
        )
    
    def get_available_models(self) -> list:
        """获取可用的模型列表"""
        if not self._model_service:
            return []
        
        return self._model_service.get_models(active_only=True)
    
    def get_first_active_model(self) -> Optional[LLMModel]:
        """获取第一个可用模型"""
        if not self._model_service:
            return None

        return self._model_service.get_active_model_for_service()


# 全局LLM工厂实例（无数据库连接）
_global_factory = LLMFactory()


def get_llm_service(db: Session = None, model_id: Optional[str] = None) -> LLMService:
    """
    获取LLM服务实例的便捷函数
    
    Args:
        db: 数据库会话
        model_id: 指定的模型ID
        
    Returns:
        LLM服务实例
    """
    if db:
        factory = LLMFactory(db)
        return factory.create_service(model_id)
    else:
        return _global_factory.create_service(model_id)
