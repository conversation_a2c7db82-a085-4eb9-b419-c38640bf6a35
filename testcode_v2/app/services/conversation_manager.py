"""
对话上下文管理器
================
管理LLM分析对话的上下文状态，支持澄清后继续分析
"""

import uuid
import time
from typing import Dict, Any, Optional
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from app.utils.time_utils import get_shanghai_time
from threading import Lock
import json
import logging

log = logging.getLogger(__name__)


@dataclass
class ConversationContext:
    """对话上下文"""
    conversation_id: str
    project_id: str
    original_query: str
    created_at: datetime
    last_activity: datetime
    
    # 分析状态
    analysis_id: Optional[str] = None
    is_waiting_clarification: bool = False
    clarification_request: Optional[Dict[str, Any]] = None
    
    # 执行历史
    execution_history: list = field(default_factory=list)
    execution_results: Dict[str, Any] = field(default_factory=dict)
    
    # 规划状态
    planning_rounds: int = 0
    max_planning_rounds: int = 25
    current_plan: Optional[Dict] = None
    task_state: str = 'planning'  # 'planning', 'executing', 'completed', 'failed'
    
    def is_expired(self, timeout_hours: int = 24) -> bool:
        """检查对话是否过期"""
        return get_shanghai_time() - self.last_activity > timedelta(hours=timeout_hours)
    
    def update_activity(self):
        """更新最后活动时间"""
        self.last_activity = get_shanghai_time()
    
    def add_execution_record(self, tool_name: str, parameters: Dict, result: Any = None, reasoning: str = ''):
        """添加执行记录"""
        record = {
            'timestamp': get_shanghai_time().isoformat(),
            'reasoning': reasoning,
            'tool_name': tool_name,
            'parameters': parameters,
            'result': result
        }
        self.execution_history.append(record)
        
        # 存储结果，使用工具名作为键
        if result is not None:
            result_key = f"{tool_name}_{len(self.execution_history)}"
            self.execution_results[result_key] = result
    
    def set_clarification_request(self, clarification_data: Dict[str, Any]):
        """设置澄清请求"""
        self.is_waiting_clarification = True
        self.clarification_request = clarification_data
        self.update_activity()
    
    def clear_clarification_request(self):
        """清除澄清请求"""
        self.is_waiting_clarification = False
        self.clarification_request = None
        self.update_activity()
    
    def is_completed(self) -> bool:
        """检查任务是否完成"""
        return self.task_state == 'completed' or self.task_state == 'failed'
    
    def is_max_rounds_reached(self) -> bool:
        """检查是否达到最大规划轮数"""
        return self.planning_rounds >= self.max_planning_rounds


class ConversationManager:
    """对话管理器，使用内存存储对话上下文"""
    
    def __init__(self):
        self._conversations: Dict[str, ConversationContext] = {}
        self._lock = Lock()
        self._cleanup_interval = 3600  # 1小时清理一次过期对话
        self._last_cleanup = time.time()
    
    def create_conversation(self, project_id: str, query: str) -> str:
        """创建新对话，返回对话ID"""
        conversation_id = str(uuid.uuid4())
        
        with self._lock:
            context = ConversationContext(
                conversation_id=conversation_id,
                project_id=project_id,
                original_query=query,
                created_at=get_shanghai_time(),
                last_activity=get_shanghai_time()
            )
            self._conversations[conversation_id] = context
            
        log.info(f"创建新对话: {conversation_id}, 项目: {project_id}")
        self._cleanup_expired_conversations()
        return conversation_id
    
    def get_conversation(self, conversation_id: str) -> Optional[ConversationContext]:
        """获取对话上下文"""
        with self._lock:
            context = self._conversations.get(conversation_id)
            if context:
                context.update_activity()
                return context
            return None
    
    def update_conversation(self, conversation_id: str, **kwargs) -> bool:
        """更新对话属性"""
        with self._lock:
            context = self._conversations.get(conversation_id)
            if context:
                for key, value in kwargs.items():
                    if hasattr(context, key):
                        setattr(context, key, value)
                context.update_activity()
                return True
            return False
    
    def set_analysis_id(self, conversation_id: str, analysis_id: str) -> bool:
        """设置分析ID"""
        return self.update_conversation(conversation_id, analysis_id=analysis_id)
    
    def set_clarification_request(self, conversation_id: str, clarification_data: Dict[str, Any]) -> bool:
        """设置澄清请求"""
        with self._lock:
            context = self._conversations.get(conversation_id)
            if context:
                context.set_clarification_request(clarification_data)
                return True
            return False
    
    def clear_clarification_request(self, conversation_id: str) -> bool:
        """清除澄清请求"""
        with self._lock:
            context = self._conversations.get(conversation_id)
            if context:
                context.clear_clarification_request()
                return True
            return False
    
    def add_execution_record(self, conversation_id: str, tool_name: str, 
                           parameters: Dict, result: Any = None, reasoning: str = '') -> bool:
        """添加执行记录"""
        with self._lock:
            context = self._conversations.get(conversation_id)
            if context:
                context.add_execution_record(tool_name, parameters, result, reasoning)
                return True
            return False
    
    def delete_conversation(self, conversation_id: str) -> bool:
        """删除对话"""
        with self._lock:
            if conversation_id in self._conversations:
                del self._conversations[conversation_id]
                log.info(f"删除对话: {conversation_id}")
                return True
            return False
    
    def get_active_conversations(self, project_id: str = None) -> Dict[str, ConversationContext]:
        """获取活跃对话列表"""
        with self._lock:
            conversations = {}
            for conv_id, context in self._conversations.items():
                if not context.is_expired():
                    if project_id is None or context.project_id == project_id:
                        conversations[conv_id] = context
            return conversations
    
    def _cleanup_expired_conversations(self):
        """清理过期对话"""
        current_time = time.time()
        if current_time - self._last_cleanup < self._cleanup_interval:
            return
        
        with self._lock:
            expired_conversations = []
            for conv_id, context in self._conversations.items():
                if context.is_expired():
                    expired_conversations.append(conv_id)
            
            for conv_id in expired_conversations:
                del self._conversations[conv_id]
                log.info(f"清理过期对话: {conv_id}")
            
            if expired_conversations:
                log.info(f"清理了 {len(expired_conversations)} 个过期对话")
            
            self._last_cleanup = current_time
    
    def get_conversation_stats(self) -> Dict[str, Any]:
        """获取对话统计信息"""
        with self._lock:
            total_conversations = len(self._conversations)
            active_conversations = sum(1 for ctx in self._conversations.values() if not ctx.is_expired())
            waiting_clarification = sum(1 for ctx in self._conversations.values() 
                                      if ctx.is_waiting_clarification and not ctx.is_expired())
            
            return {
                "total_conversations": total_conversations,
                "active_conversations": active_conversations,
                "waiting_clarification": waiting_clarification,
                "last_cleanup": datetime.fromtimestamp(self._last_cleanup).isoformat()
            }


# 全局对话管理器实例
conversation_manager = ConversationManager() 