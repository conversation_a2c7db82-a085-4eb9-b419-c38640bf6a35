"""
图表生成模块
===========

基于专家混合架构的智能图表生成系统，支持多种可视化形态。

主要组件:
- agent: 图表生成Agent
- data_structures: 数据结构定义
- experts: 专家系统（ECharts专家、表格专家）
- utils: 工具模块（数据分析器等）
- prompts: 图表生成相关提示词
"""

from .agent import ChartGenerationAgent
from .data_structures import (
    ExpertInput,
    create_expert_input,
    VisualizationPlanningDocument,
    validate_planning_document,
    MultiChartResult,
    MultiChartExecutionResult,
    create_multi_chart_result
)
from .experts import EChartsExpert, TableExpert
from .utils import DataProfiler, DataSummaryGenerator

# 主要导出
__all__ = [
    'ChartGenerationAgent',
    'ExpertInput',
    'create_expert_input',
    'VisualizationPlanningDocument',
    'validate_planning_document',
    'MultiChartResult',
    'MultiChartExecutionResult',
    'create_multi_chart_result',
    'EChartsExpert',
    'TableExpert',
    'DataProfiler',
    'DataSummaryGenerator'
]
