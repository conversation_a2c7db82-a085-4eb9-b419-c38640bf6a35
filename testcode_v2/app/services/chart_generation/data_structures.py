"""
MoE图表生成Agent数据结构定义
============================
定义可视化规划书、专家接口等核心数据结构
"""

from typing import Dict, Any, List, Optional, Union
from pydantic import BaseModel, Field
from enum import Enum


class DisplayType(str, Enum):
    """可视化显示类型枚举"""
    ECHARTS = "echarts"
    TABLE = "table"
    # 未来可扩展：CARD = "card", SUMMARY = "summary", etc.


class ConditionalFormattingRule(BaseModel):
    """条件格式化规则"""
    column: str = Field(..., description="应用格式化的列名")
    condition: str = Field(..., description="条件表达式，如 'value > 100' 或 'value < 0'")
    style: Dict[str, str] = Field(..., description="CSS样式，如 {'color': '#e53e3e', 'font-weight': 'bold'}")


class EChartsDisplayInstructions(BaseModel):
    """ECharts显示指令"""
    title: str = Field(..., description="图表标题")
    chart_type: Optional[str] = Field(None, description="图表类型，如 'line', 'bar', 'pie'等")
    data_mapping: Optional[Dict[str, str]] = Field(None, description="数据字段映射")
    custom_options: Optional[Dict[str, Any]] = Field(None, description="自定义ECharts选项")


class TableDisplayInstructions(BaseModel):
    """HTML表格显示指令"""
    title: str = Field(..., description="表格标题")
    column_rename: Optional[Dict[str, str]] = Field(None, description="列名重命名映射")
    conditional_formatting: Optional[List[ConditionalFormattingRule]] = Field(None, description="条件格式化规则")
    table_style: Optional[Dict[str, str]] = Field(None, description="表格整体样式")
    max_rows: Optional[int] = Field(None, description="最大显示行数")


class VisualizationPlan(BaseModel):
    """单个可视化规划"""
    plan_id: str = Field(..., description="规划ID")
    description: str = Field(..., description="规划描述")
    display_type: DisplayType = Field(..., description="显示类型")
    display_instructions: Union[EChartsDisplayInstructions, TableDisplayInstructions] = Field(
        ..., description="显示指令"
    )
    priority: Optional[int] = Field(default=1, description="优先级，数字越小优先级越高")
    python_code_transformer: Optional[str] = Field(default=None, description="数据转换代码")


class VisualizationPlanningDocument(BaseModel):
    """可视化规划书"""
    should_generate: bool = Field(..., description="是否应该生成可视化")
    reason: str = Field(..., description="决策原因")
    visualization_plans: List[VisualizationPlan] = Field(default_factory=list, description="可视化规划列表")


class ExpertInput(BaseModel):
    """专家输入数据结构"""
    data_summary: Dict[str, Any] = Field(..., description="数据摘要（替代完整数据）")
    display_instructions: Dict[str, Any] = Field(..., description="显示指令")
    user_language: str = Field(default='zh-CN', description="用户语言偏好")


class EChartsExpertOutput(BaseModel):
    """ECharts专家输出"""
    success: bool = Field(..., description="是否成功")
    chart_config: Optional[Dict[str, Any]] = Field(None, description="ECharts配置")
    python_code_transformer: Optional[str] = Field(None, description="数据转换代码")
    original_prompts: Optional[Dict[str, str]] = Field(None, description="原始提示词上下文")
    error: Optional[str] = Field(None, description="错误信息")


class TableExpertOutput(BaseModel):
    """HTML表格专家输出"""
    success: bool = Field(..., description="是否成功")
    html_content: Optional[str] = Field(None, description="HTML表格内容")
    error: Optional[str] = Field(None, description="错误信息")


class MoEExecutionResult(BaseModel):
    """MoE执行结果"""
    success: bool = Field(..., description="是否成功")
    render_type: Optional[str] = Field(None, description="渲染类型：'echarts' 或 'html'")
    title: Optional[str] = Field(None, description="标题")
    content: Optional[Union[Dict[str, Any], str]] = Field(None, description="内容（ECharts配置或HTML字符串）")
    description: Optional[str] = Field(None, description="描述")
    error: Optional[str] = Field(None, description="错误信息")
    debug_info: Optional[Dict[str, Any]] = Field(None, description="调试信息")


class MultiChartResult(BaseModel):
    """多图表执行结果"""
    chart_id: str = Field(..., description="图表ID")
    plan_id: str = Field(..., description="规划ID")
    title: str = Field(..., description="图表标题")
    chart_type: str = Field(..., description="图表类型：'echarts' 或 'table'")
    config: Union[Dict[str, Any], str] = Field(..., description="图表配置或HTML内容")
    description: Optional[str] = Field(None, description="图表描述")
    priority: int = Field(default=1, description="优先级")


class MultiChartExecutionResult(BaseModel):
    """多图表执行结果"""
    success: bool = Field(..., description="是否成功")
    total_charts: int = Field(..., description="总图表数量")
    successful_charts: int = Field(..., description="成功生成的图表数量")
    charts: List[MultiChartResult] = Field(default_factory=list, description="图表列表")
    layout: str = Field(default="grid", description="布局方式：'grid', 'tabs', 'carousel'")
    error: Optional[str] = Field(None, description="错误信息")


# 示例数据结构
EXAMPLE_VISUALIZATION_PLANNING_DOCUMENT = {
    "should_generate": True,
    "reason": "数据包含多个数值字段，适合生成图表进行对比分析",
    "visualization_plans": [
        {
            "plan_id": "plan-001-echarts",
            "description": "生成柱状图展示各部门销售额对比",
            "display_type": "echarts",
            "display_instructions": {
                "title": "各部门销售额对比",
                "chart_type": "bar",
                "data_mapping": {
                    "x_axis": "department",
                    "y_axis": "sales_amount"
                }
            }
        }
    ]
}

EXAMPLE_TABLE_PLANNING_DOCUMENT = {
    "should_generate": True,
    "reason": "用户需要查看详细的数值信息，表格形式更适合",
    "visualization_plans": [
        {
            "plan_id": "plan-002-table",
            "description": "以表格形式展示销售详情，突出显示异常数据",
            "display_type": "table",
            "display_instructions": {
                "title": "销售详情表",
                "column_rename": {
                    "department": "部门",
                    "sales": "销售额（万元）",
                    "profit": "利润（万元）",
                    "profit_margin": "利润率（%）"
                },
                "conditional_formatting": [
                    {
                        "column": "profit",
                        "condition": "value < 0",
                        "style": {"color": "#e53e3e", "font-weight": "bold"}
                    },
                    {
                        "column": "profit_margin",
                        "condition": "value > 20",
                        "style": {"background-color": "#c6f6d5"}
                    }
                ]
            }
        }
    ]
}


def validate_planning_document(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    验证可视化规划书的有效性
    
    Args:
        data: 规划书数据
        
    Returns:
        Dict[str, Any]: 验证结果
    """
    try:
        # 使用Pydantic验证
        planning_doc = VisualizationPlanningDocument(**data)
        return {
            "valid": True,
            "planning_document": planning_doc,
            "error": None
        }
    except Exception as e:
        return {
            "valid": False,
            "planning_document": None,
            "error": str(e)
        }


def create_expert_input(data_summary: Dict[str, Any], display_instructions: Dict[str, Any], user_language: str = 'zh-CN') -> ExpertInput:
    """
    创建专家输入数据结构

    Args:
        data_summary: 数据摘要
        display_instructions: 显示指令
        user_language: 用户语言偏好

    Returns:
        ExpertInput: 专家输入数据结构
    """
    return ExpertInput(
        data_summary=data_summary,
        display_instructions=display_instructions,
        user_language=user_language
    )


def create_moe_result(
    success: bool,
    render_type: Optional[str] = None,
    title: Optional[str] = None,
    content: Optional[Union[Dict[str, Any], str]] = None,
    description: Optional[str] = None,
    error: Optional[str] = None,
    debug_info: Optional[Dict[str, Any]] = None
) -> MoEExecutionResult:
    """
    创建MoE执行结果

    Args:
        success: 是否成功
        render_type: 渲染类型
        title: 标题
        content: 内容
        description: 描述
        error: 错误信息
        debug_info: 调试信息

    Returns:
        MoEExecutionResult: MoE执行结果
    """
    return MoEExecutionResult(
        success=success,
        render_type=render_type,
        title=title,
        content=content,
        description=description,
        error=error,
        debug_info=debug_info
    )


def create_multi_chart_result(
    charts: List[MultiChartResult],
    layout: str = "grid",
    error: Optional[str] = None
) -> MultiChartExecutionResult:
    """
    创建多图表执行结果

    Args:
        charts: 图表列表
        layout: 布局方式
        error: 错误信息

    Returns:
        MultiChartExecutionResult: 多图表执行结果
    """
    successful_charts = len([c for c in charts if c.config is not None])

    return MultiChartExecutionResult(
        success=successful_charts > 0,
        total_charts=len(charts),
        successful_charts=successful_charts,
        charts=charts,
        layout=layout,
        error=error
    )
