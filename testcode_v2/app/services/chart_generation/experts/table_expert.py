"""
Ant Design表格专家
================
专门负责生成Ant Design表格配置
支持列配置、数据源填充、样式定制等功能
"""

import json
import logging
import time
import traceback
from typing import Dict, Any, Optional, List
from sqlalchemy.orm import Session
import pandas as pd

from app.services.llm_factory import get_llm_service
from ..prompts import get_table_expert_prompt
from ..data_structures import ExpertInput, TableExpertOutput

# 配置日志
logger = logging.getLogger(__name__)


class TableExpert:
    """
    升级版Ant Design表格配置专家 - 从"排版工"到"数据科学家"

    专门负责：
    1. 深度分析"多维数据写真"，自主发现数据中的故事和异常
    2. 设计动态的、可执行的规则来讲述数据故事
    3. 生成智能的、适应性强的表格渲染配置
    4. 实现"规则与执行分离"的优雅架构
    5. 基于统计洞察进行智能异常检测和可视化标注
    """
    
    def __init__(self, db: Session):
        """
        初始化Ant Design表格专家
        
        Args:
            db: 数据库会话
        """
        self.db = db
        
        # 获取LLM服务
        self.llm_service = get_llm_service(db)
        
        # 获取LLM客户端
        if hasattr(self.llm_service, 'client'):
            self.llm_client = self.llm_service.client
        else:
            from openai import AsyncOpenAI
            from app.core.config import settings
            self.llm_client = AsyncOpenAI(
                api_key=settings.OPENAI_API_KEY,
                base_url=settings.OPENAI_BASE_URL
            )
        
        # 获取模型名称
        if hasattr(self.llm_service, 'model'):
            self.model = self.llm_service.model
        else:
            from app.core.config import settings
            self.model = settings.OPENAI_MODEL

        # 获取模型ID（用于Token统计）
        if hasattr(self.llm_service, 'model_id'):
            self.model_id = self.llm_service.model_id
        else:
            self.model_id = None
            logger.warning("LLM服务未提供model_id，Token统计可能无法正常工作")
    
    async def generate_table_html(
        self,
        expert_input: ExpertInput,
        analysis_id: Optional[str] = None,
        user_id: Optional[str] = None,
        project_id: Optional[str] = None,
        organization_id: Optional[int] = None,
        background_tasks = None
    ) -> TableExpertOutput:
        """
        生成Ant Design表格配置
        
        Args:
            expert_input: 专家输入数据
            analysis_id: 分析ID（用于Token统计）
            user_id: 用户ID（用于Token统计）
            project_id: 项目ID（用于Token统计）
            organization_id: 组织ID（用于Token统计）
            background_tasks: FastAPI后台任务对象（用于异步Token记录）
            
        Returns:
            TableExpertOutput: Ant Design表格专家输出
        """
        try:
            start_time = time.time()
            logger.info("=" * 80)
            logger.info("📋 Ant Design表格专家开始生成表格配置")
            logger.info("=" * 80)

            # 详细日志：专家输入
            logger.info("📋 Ant Design表格专家输入:")
            logger.info(f"   用户语言: {expert_input.user_language}")
            logger.info(f"   显示指令: {json.dumps(expert_input.display_instructions, ensure_ascii=False, indent=2)}")

            data_profile_str = json.dumps(expert_input.data_summary, ensure_ascii=False, indent=2)
            logger.info(f"   数据写真: {data_profile_str[:500]}..." if len(data_profile_str) > 500 else f"   数据写真: {data_profile_str}")

            # 获取提示词
            prompts = get_table_expert_prompt(
                data_profile=expert_input.data_summary,  # 传递数据写真作为data_profile参数
                display_instructions=expert_input.display_instructions,
                user_language=expert_input.user_language
            )

            # 详细日志：系统提示词
            logger.info("🤖 Ant Design表格专家系统提示词:")
            logger.info("-" * 60)
            logger.info(prompts["system"])
            logger.info("-" * 60)

            # 详细日志：用户提示词
            logger.info("👤 Ant Design表格专家用户提示词:")
            logger.info("-" * 60)
            logger.info(prompts["user"])
            logger.info("-" * 60)

            # 调用LLM
            llm_start_time = time.time()
            logger.info("🚀 Ant Design表格专家开始调用LLM模型...")
            logger.info(f"   模型: {self.model}")
            logger.info(f"   温度: 0.1")

            completion = await self.llm_client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": prompts["system"]},
                    {"role": "user", "content": prompts["user"]}
                ],
                temperature=0.1
            )

            llm_end_time = time.time()
            llm_duration = llm_end_time - llm_start_time
            logger.info(f"✅ Ant Design表格专家LLM调用完成，耗时: {llm_duration:.2f}秒")

            response_content = completion.choices[0].message.content

            # 详细日志：LLM响应
            logger.info("📝 Ant Design表格专家LLM原始响应:")
            logger.info("-" * 60)
            logger.info(response_content)
            logger.info("-" * 60)

            # 详细日志：Token使用情况
            if completion.usage:
                usage = completion.usage
                logger.info("💰 Ant Design表格专家Token使用情况:")
                logger.info(f"   输入Token: {usage.prompt_tokens}")
                logger.info(f"   输出Token: {usage.completion_tokens}")
                logger.info(f"   总Token: {usage.total_tokens}")
            
            # 记录Token用量
            await self._record_token_usage(
                completion, analysis_id, user_id, project_id, 
                organization_id, background_tasks
            )
            
            # 提取JSON配置内容
            table_config = self._extract_json_content(response_content)
            
            if not table_config:
                return TableExpertOutput(
                    success=False,
                    html_content=None,
                    error="无法从LLM响应中提取有效的表格配置JSON"
                )
            
            # 验证表格配置
            validation_result = self._validate_table_config(table_config)
            if not validation_result['valid']:
                return TableExpertOutput(
                    success=False,
                    html_content=None,
                    error=f"表格配置验证失败: {validation_result['reason']}"
                )

            # 🔄 数据注入：将完整数据注入到表格配置中
            final_config = self._inject_full_data_to_table_config(
                table_config, expert_input.processed_df
            )

            # 将表格配置转换为JSON字符串（保持与现有接口的兼容性）
            table_config_json = json.dumps(final_config, ensure_ascii=False, indent=2)
            
            end_time = time.time()
            total_duration = end_time - start_time
            logger.info(f"Ant Design表格专家配置生成完成，总耗时: {total_duration:.2f}秒")
            
            return TableExpertOutput(
                success=True,
                html_content=table_config_json,  # 复用现有字段，但内容是JSON
                error=None
            )
            
        except Exception as e:
            logger.error(f"Ant Design表格专家生成配置失败: {str(e)}")
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            
            return TableExpertOutput(
                success=False,
                html_content=None,
                error=f"Ant Design表格专家异常: {str(e)}"
            )
    
    def _extract_json_content(self, response_content: str) -> Optional[Dict[str, Any]]:
        """
        从LLM响应中提取JSON配置内容
        
        Args:
            response_content: LLM响应内容
            
        Returns:
            Optional[Dict[str, Any]]: 提取的表格配置JSON
        """
        try:
            content = response_content.strip()
            
            # 首先尝试直接解析整个响应为JSON
            try:
                result = json.loads(content)
                return result
            except json.JSONDecodeError:
                logger.info("直接JSON解析失败，尝试提取JSON代码块")

            # 查找JSON代码块
            if "```json" in content:
                json_start = content.find("```json") + 7
                json_end = content.find("```", json_start)
                if json_end > json_start:
                    json_str = content[json_start:json_end].strip()
                    try:
                        result = json.loads(json_str)
                        return result
                    except json.JSONDecodeError as e:
                        logger.warning(f"JSON代码块解析失败: {str(e)}")

            # 查找普通代码块中的JSON
            if "```" in content:
                first_triple = content.find("```")
                content_after_first = content[first_triple + 3:]
                newline_pos = content_after_first.find('\n')
                if newline_pos != -1:
                    json_start = first_triple + 3 + newline_pos + 1
                else:
                    json_start = first_triple + 3
                json_end = content.find("```", json_start)
                if json_end > json_start:
                    json_str = content[json_start:json_end].strip()
                    try:
                        result = json.loads(json_str)
                        return result
                    except json.JSONDecodeError as e:
                        logger.warning(f"普通代码块JSON解析失败: {str(e)}")

            # 查找第一个 { 到最后一个 } 的内容
            json_start = content.find("{")
            json_end = content.rfind("}") + 1
            if json_start >= 0 and json_end > json_start:
                json_str = content[json_start:json_end]
                try:
                    result = json.loads(json_str)
                    return result
                except json.JSONDecodeError as e:
                    logger.warning(f"大括号范围JSON解析失败: {str(e)}")
            
            logger.error("无法从响应中提取有效的JSON配置")
            logger.error(f"响应内容: {content[:500]}...")
            return None
            
        except Exception as e:
            logger.error(f"JSON提取过程中发生异常: {str(e)}")
            return None
    
    def _validate_table_config(self, table_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证表格配置的有效性
        
        Args:
            table_config: 表格配置字典
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        try:
            # 基本格式检查
            if not table_config or not isinstance(table_config, dict):
                return {
                    'valid': False,
                    'reason': "表格配置为空或不是字典格式"
                }
            
            # 检查必需字段
            required_fields = ['columns', 'dataSource']
            missing_fields = []
            for field in required_fields:
                if field not in table_config:
                    missing_fields.append(field)
            
            if missing_fields:
                return {
                    'valid': False,
                    'reason': f"缺少必需字段: {', '.join(missing_fields)}"
                }
            
            # 验证columns格式
            columns = table_config['columns']
            if not isinstance(columns, list) or len(columns) == 0:
                return {
                    'valid': False,
                    'reason': "columns必须是非空数组"
                }
            
            # 验证dataSource格式
            dataSource = table_config['dataSource']
            if not isinstance(dataSource, list):
                return {
                    'valid': False,
                    'reason': "dataSource必须是数组"
                }
            
            # 验证每个列配置
            for i, col in enumerate(columns):
                if isinstance(col, str):
                    continue  # 字符串格式的列是允许的
                elif isinstance(col, dict):
                    if 'dataIndex' not in col and 'key' not in col:
                        return {
                            'valid': False,
                            'reason': f"第{i+1}列缺少dataIndex或key字段"
                        }
                else:
                    return {
                        'valid': False,
                        'reason': f"第{i+1}列配置格式错误，必须是字符串或对象"
                    }
            
            return {'valid': True, 'reason': None}

        except Exception as e:
            return {
                'valid': False,
                'reason': f"验证过程异常: {str(e)}"
            }

    def _inject_full_data_to_table_config(
        self,
        table_config: Dict[str, Any],
        df: pd.DataFrame
    ) -> Dict[str, Any]:
        """
        将完整数据注入到表格配置中

        Args:
            table_config: AI生成的表格配置（包含样本数据）
            df: 完整的DataFrame

        Returns:
            Dict[str, Any]: 注入完整数据后的表格配置
        """
        try:
            logger.info("🔄 开始将完整数据注入到表格配置中")

            # 复制配置以避免修改原始配置
            final_config = table_config.copy()

            # 转换DataFrame为表格数据格式
            full_data = self._convert_dataframe_to_table_data(df)

            # 智能决策：是否使用全量数据
            should_use_full_data = self._should_use_full_data(df, table_config)

            if should_use_full_data:
                logger.info(f"✅ 使用全量数据，共 {len(full_data)} 条记录")
                final_config['dataSource'] = full_data

                # 调整分页设置
                if 'pagination' not in final_config:
                    final_config['pagination'] = {}

                # 根据数据量调整分页大小
                if len(full_data) > 50:
                    final_config['pagination']['pageSize'] = 20
                elif len(full_data) > 20:
                    final_config['pagination']['pageSize'] = 15
                else:
                    final_config['pagination']['pageSize'] = 10

                final_config['pagination']['showSizeChanger'] = True
                final_config['pagination']['showQuickJumper'] = True
                final_config['pagination']['showTotal'] = True

            else:
                logger.info(f"⚠️ 保持样本数据，共 {len(table_config.get('dataSource', []))} 条记录")
                # 保持原有的样本数据

            logger.info(f"✅ 表格数据注入完成")
            return final_config

        except Exception as e:
            logger.error(f"❌ 表格数据注入失败: {str(e)}")
            logger.error(f"异常详情: {traceback.format_exc()}")
            # 返回原始配置
            return table_config

    def _convert_dataframe_to_table_data(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        将DataFrame转换为表格数据格式

        Args:
            df: 原始DataFrame

        Returns:
            List[Dict[str, Any]]: 表格数据列表
        """
        try:
            # 转换为records格式
            records = df.to_dict('records')

            # 添加key字段并处理数据类型
            table_data = []
            for i, record in enumerate(records):
                table_row = {"key": f"row_{i}"}

                for key, value in record.items():
                    # 处理不同数据类型
                    if pd.isna(value):
                        table_row[key] = None
                    elif isinstance(value, (int, float)):
                        # 保持数值类型，但转换为Python原生类型
                        if pd.isna(value):
                            table_row[key] = None
                        else:
                            table_row[key] = float(value) if isinstance(value, float) else int(value)
                    elif hasattr(value, 'isoformat'):  # datetime对象
                        table_row[key] = value.isoformat()
                    else:
                        table_row[key] = str(value)

                table_data.append(table_row)

            return table_data

        except Exception as e:
            logger.error(f"DataFrame转换失败: {str(e)}")
            return []

    def _should_use_full_data(self, df: pd.DataFrame, table_config: Dict[str, Any]) -> bool:
        """
        智能决策是否使用全量数据

        Args:
            df: 完整DataFrame
            table_config: AI生成的表格配置

        Returns:
            bool: 是否使用全量数据
        """
        try:
            total_rows = len(df)
            sample_rows = len(table_config.get('dataSource', []))

            logger.info(f"📊 数据量分析: 总数据 {total_rows} 行，样本数据 {sample_rows} 行")

            # 决策规则
            if total_rows <= 5:
                # 数据很少，直接使用全量
                logger.info("✅ 数据量很少，使用全量数据")
                return True
            elif total_rows <= 100:
                # 中等数据量，使用全量但加分页
                logger.info("✅ 中等数据量，使用全量数据并启用分页")
                return True
            elif total_rows <= 1000:
                # 较大数据量，使用全量但优化分页
                logger.info("✅ 较大数据量，使用全量数据并优化分页")
                return True
            else:
                # 超大数据量，考虑性能，可能只使用样本
                logger.info("⚠️ 超大数据量，考虑性能影响")
                # 但仍然倾向于使用全量，让前端处理性能优化
                return True

        except Exception as e:
            logger.error(f"数据量决策失败: {str(e)}")
            # 默认使用全量数据
            return True
    

    
    async def _record_token_usage(
        self,
        completion,
        analysis_id: Optional[str],
        user_id: Optional[str],
        project_id: Optional[str],
        organization_id: Optional[int],
        background_tasks
    ):
        """记录Token使用量"""
        try:
            # 获取并记录Token用量
            usage_data = completion.usage.model_dump() if completion.usage else None
            if usage_data and self.model_id and organization_id and background_tasks:
                prompt_tokens = usage_data.get('prompt_tokens', 0)
                completion_tokens = usage_data.get('completion_tokens', 0)
                total_tokens = usage_data.get('total_tokens', 0)

                # 异步记录Token用量
                from app.services.token_usage_service import record_usage_task
                background_tasks.add_task(
                    record_usage_task,
                    user_id=user_id,
                    org_id=organization_id,
                    project_id=project_id,
                    analysis_id=analysis_id,
                    model_id=self.model_id,
                    model_name=self.model,
                    prompt_tokens=prompt_tokens,
                    completion_tokens=completion_tokens,
                    total_tokens=total_tokens
                )
        except Exception as e:
            logger.warning(f"记录Token使用量失败: {str(e)}")


# 便捷函数
async def generate_table_html(
    db: Session,
    expert_input: ExpertInput,
    analysis_id: Optional[str] = None,
    user_id: Optional[str] = None,
    project_id: Optional[str] = None,
    organization_id: Optional[int] = None,
    background_tasks = None
) -> TableExpertOutput:
    """
    便捷函数：生成HTML表格
    
    Args:
        db: 数据库会话
        expert_input: 专家输入数据
        analysis_id: 分析ID
        user_id: 用户ID
        project_id: 项目ID
        organization_id: 组织ID
        background_tasks: 后台任务
        
    Returns:
        TableExpertOutput: HTML表格专家输出
    """
    expert = TableExpert(db)
    return await expert.generate_table_html(
        expert_input=expert_input,
        analysis_id=analysis_id,
        user_id=user_id,
        project_id=project_id,
        organization_id=organization_id,
        background_tasks=background_tasks
    )
