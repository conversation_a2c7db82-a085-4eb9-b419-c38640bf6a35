"""
ECharts专家
==========
专门负责生成高质量的ECharts图表配置
基于现有的图表生成逻辑，专注于图表配置优化
"""

import json
import logging
import time
import traceback
from typing import Dict, Any, Optional
from sqlalchemy.orm import Session

from app.services.llm_factory import get_llm_service
from ..prompts import get_echarts_expert_prompt
from ..data_structures import ExpertInput, EChartsExpertOutput

# 配置日志
logger = logging.getLogger(__name__)


class EChartsExpert:
    """
    升级版ECharts配置生成专家 - 从"配置翻译器"到"智能设计师"

    专门负责：
    1. 深度解读"多维数据写真"，理解数据的内在特征和分布
    2. 基于数据洞察进行有根据的、智能的图表设计决策
    3. 生成具有洞察力、上下文丰富的ECharts配置
    4. 利用统计信息优化图表的可读性和表达力
    5. 实现洞察驱动的可视化设计
    """
    
    def __init__(self, db: Session):
        """
        初始化ECharts专家
        
        Args:
            db: 数据库会话
        """
        self.db = db
        
        # 获取LLM服务
        self.llm_service = get_llm_service(db)
        
        # 获取LLM客户端
        if hasattr(self.llm_service, 'client'):
            self.llm_client = self.llm_service.client
        else:
            from openai import AsyncOpenAI
            from app.core.config import settings
            self.llm_client = AsyncOpenAI(
                api_key=settings.OPENAI_API_KEY,
                base_url=settings.OPENAI_BASE_URL
            )
        
        # 获取模型名称
        if hasattr(self.llm_service, 'model'):
            self.model = self.llm_service.model
        else:
            from app.core.config import settings
            self.model = settings.OPENAI_MODEL

        # 获取模型ID（用于Token统计）
        if hasattr(self.llm_service, 'model_id'):
            self.model_id = self.llm_service.model_id
        else:
            self.model_id = None
            logger.warning("LLM服务未提供model_id，Token统计可能无法正常工作")
    
    async def generate_chart_config(
        self,
        expert_input: ExpertInput,
        analysis_id: Optional[str] = None,
        user_id: Optional[str] = None,
        project_id: Optional[str] = None,
        organization_id: Optional[int] = None,
        background_tasks = None
    ) -> EChartsExpertOutput:
        """
        生成ECharts配置
        
        Args:
            expert_input: 专家输入数据
            analysis_id: 分析ID（用于Token统计）
            user_id: 用户ID（用于Token统计）
            project_id: 项目ID（用于Token统计）
            organization_id: 组织ID（用于Token统计）
            background_tasks: FastAPI后台任务对象（用于异步Token记录）
            
        Returns:
            EChartsExpertOutput: ECharts专家输出
        """
        try:
            start_time = time.time()
            logger.info("=" * 80)
            logger.info("📊 ECharts专家开始生成图表配置")
            logger.info("=" * 80)

            # 详细日志：专家输入
            logger.info("📋 ECharts专家输入:")
            logger.info(f"   用户语言: {expert_input.user_language}")
            logger.info(f"   显示指令: {json.dumps(expert_input.display_instructions, ensure_ascii=False, indent=2)}")

            data_profile_str = json.dumps(expert_input.data_summary, ensure_ascii=False, indent=2)
            logger.info(f"   数据写真: {data_profile_str[:500]}..." if len(data_profile_str) > 500 else f"   数据写真: {data_profile_str}")

            # 获取提示词
            prompts = get_echarts_expert_prompt(
                data_profile=expert_input.data_summary,  # 传递数据写真作为data_profile参数
                display_instructions=expert_input.display_instructions,
                user_language=expert_input.user_language
            )

            # 详细日志：系统提示词
            logger.info("🤖 ECharts专家系统提示词:")
            logger.info("-" * 60)
            logger.info(prompts["system"])
            logger.info("-" * 60)

            # 详细日志：用户提示词
            logger.info("👤 ECharts专家用户提示词:")
            logger.info("-" * 60)
            logger.info(prompts["user"])
            logger.info("-" * 60)

            # 调用LLM
            messages = [
                {"role": "system", "content": prompts["system"]},
                {"role": "user", "content": prompts["user"]}
            ]
            response = await self.llm_service.call_llm(messages)

            if not response:
                logger.error("❌ LLM响应为空")
                return EChartsExpertOutput(
                    success=False,
                    original_prompts=prompts,
                    error="LLM响应为空"
                )

            # 解析响应
            response_content = response.strip()
            logger.info("🔍 ECharts专家LLM响应:")
            logger.info(response_content)
            logger.info("------------------------------------------------------------")

            # 提取图表配置
            response_data = self._extract_chart_config(response_content)

            if not response_data:
                logger.error("❌ 无法从LLM响应中提取有效的响应数据")
                return EChartsExpertOutput(
                    success=False,
                    chart_config=None,
                    python_code_transformer=None,
                    original_prompts=prompts,
                    error="无法从LLM响应中提取有效的响应数据"
                )

            # 处理统一的响应格式
            python_code_transformer = None
            chart_config = None
            
            if isinstance(response_data, dict):
                if 'chart_config' in response_data:
                    # 标准格式：包含python_code_transformer和chart_config
                    chart_config = response_data.get('chart_config')
                    python_code_transformer = response_data.get('python_code_transformer')
                    logger.info("✅ 提取chart_config和python_code_transformer")
                    if python_code_transformer:
                        logger.info(f"📝 提取到python_code_transformer，长度: {len(python_code_transformer)} 字符")
                else:
                    # 直接是chart_config（无python_code_transformer）
                    chart_config = response_data
                    logger.info("✅ 直接使用响应作为chart_config")
            else:
                logger.error("❌ 响应数据格式错误，不是字典类型")
                return EChartsExpertOutput(
                    success=False,
                    chart_config=None,
                    python_code_transformer=None,
                    original_prompts=prompts,
                    error="响应数据格式错误"
                )

            if not chart_config:
                logger.error("❌ 无法从响应中提取有效的ECharts配置")
                return EChartsExpertOutput(
                    success=False,
                    chart_config=None,
                    python_code_transformer=python_code_transformer,
                    original_prompts=prompts,
                    error="无法从响应中提取有效的ECharts配置"
                )

            logger.info("✅ ECharts配置解析成功:")
            try:
                config_str = json.dumps(chart_config, ensure_ascii=False, indent=2)
                logger.info("-" * 60)
                logger.info(config_str)
                logger.info("-" * 60)
            except Exception as e:
                logger.info(f"   配置序列化失败: {str(e)}")

            # 验证图表配置
            if isinstance(chart_config, list):
                # 处理数组格式的配置（多个图表）
                if not chart_config:
                    logger.error("❌ 图表配置数组为空")
                    return EChartsExpertOutput(
                        success=False,
                        original_prompts=prompts,
                        error="图表配置数组为空"
                    )
                
                # 验证数组中的每个图表配置
                for i, config in enumerate(chart_config):
                    validation_result = self._validate_chart_config(config)
                    if not validation_result.get('valid', False):
                        error_msg = f"第{i+1}个图表配置验证失败: {validation_result.get('reason', '未知错误')}"
                        logger.error(f"❌ {error_msg}")
                        return EChartsExpertOutput(
                            success=False,
                            original_prompts=prompts,
                            error=error_msg
                        )
                
                # 处理数组中每个图表配置的函数
                processed_config = [self._process_chart_functions(config) for config in chart_config]
            else:
                # 处理单个图表配置
                validation_result = self._validate_chart_config(chart_config)
                if not validation_result.get('valid', False):
                    error_msg = validation_result.get('reason', '图表配置验证失败')
                    logger.error(f"❌ 图表配置验证失败: {error_msg}")
                    return EChartsExpertOutput(
                        success=False,
                        original_prompts=prompts,
                        error=error_msg
                    )

                # 处理图表配置中的函数
                processed_config = self._process_chart_functions(chart_config)

            end_time = time.time()
            total_duration = end_time - start_time
            logger.info("=" * 80)
            logger.info(f"📊 ECharts专家配置生成完成，总耗时: {total_duration:.2f}秒")
            logger.info("=" * 80)

            return EChartsExpertOutput(
                success=True,
                chart_config=processed_config,
                python_code_transformer=python_code_transformer,
                original_prompts=prompts,
                error=None
            )
            
        except Exception as e:
            logger.error(f"ECharts专家生成配置失败: {str(e)}")
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            
            return EChartsExpertOutput(
                success=False,
                chart_config=None,
                original_prompts=None,
                error=f"ECharts专家异常: {str(e)}"
            )
    
    async def generate_chart_config_with_guidance(
        self,
        expert_input: ExpertInput,
        expert_guidance: Optional[Dict[str, Any]] = None,
        expert_context: Optional[Dict[str, Any]] = None,
        analysis_id: Optional[str] = None,
        user_id: Optional[str] = None,
        project_id: Optional[str] = None,
        organization_id: Optional[int] = None,
        background_tasks = None
    ) -> EChartsExpertOutput:
        """
        生成ECharts配置 - 带专家指导版本
        
        Args:
            expert_input: 专家输入数据
            expert_guidance: 主规划师的专家指导
            expert_context: 针对此图表的专家上下文
            analysis_id: 分析ID（用于Token统计）
            user_id: 用户ID（用于Token统计）
            project_id: 项目ID（用于Token统计）
            organization_id: 组织ID（用于Token统计）
            background_tasks: FastAPI后台任务对象（用于异步Token记录）
            
        Returns:
            EChartsExpertOutput: 包含图表配置的输出结果
        """
        try:
            logger.info("🎯 ECharts专家开始生成配置（带指导）")
            
            if expert_guidance:
                logger.info(f"📋 收到专家指导: {expert_guidance.get('data_insights', 'N/A')[:100]}...")
            
            if expert_context:
                logger.info(f"🎯 收到专家上下文: {expert_context.get('focus_areas', 'N/A')[:100]}...")

            # 获取增强的提示词（包含指导信息）
            prompts = get_echarts_expert_prompt(
                data_profile=expert_input.data_summary,
                display_instructions=expert_input.display_instructions,
                expert_guidance=expert_guidance,
                expert_context=expert_context,
                user_language=expert_input.user_language
            )

            # 调用LLM
            messages = [
                {"role": "system", "content": prompts["system"]},
                {"role": "user", "content": prompts["user"]}
            ]
            response = await self.llm_service.call_llm(messages)

            if not response:
                logger.error("❌ LLM响应为空")
                return EChartsExpertOutput(
                    success=False,
                    original_prompts=prompts,
                    error="LLM响应为空"
                )

            # 解析响应
            response_content = response.strip()
            logger.info("🔍 ECharts专家LLM响应:")
            logger.info(response_content)
            logger.info("------------------------------------------------------------")

            # 提取图表配置
            response_data = self._extract_chart_config(response_content)
            if not response_data:
                logger.error("❌ 无法提取有效的图表配置")
                return EChartsExpertOutput(
                    success=False,
                    original_prompts=prompts,
                    error="无法提取有效的图表配置"
                )

            # 解析响应数据
            python_code_transformer = None
            chart_config = None
            
            if isinstance(response_data, dict):
                if 'chart_config' in response_data:
                    # 标准格式：包含python_code_transformer和chart_config
                    chart_config = response_data.get('chart_config')
                    python_code_transformer = response_data.get('python_code_transformer')
                    logger.info("✅ 提取chart_config和python_code_transformer")
                    if python_code_transformer:
                        logger.info(f"📝 提取到python_code_transformer，长度: {len(python_code_transformer)} 字符")
                else:
                    # 直接是chart_config（无python_code_transformer）
                    chart_config = response_data
                    logger.info("✅ 直接使用响应作为chart_config")
            else:
                logger.error("❌ 响应数据格式错误")
                return EChartsExpertOutput(
                    success=False,
                    original_prompts=prompts,
                    error="响应数据格式错误"
                )

            if not chart_config:
                logger.error("❌ chart_config为空")
                return EChartsExpertOutput(
                    success=False,
                    original_prompts=prompts,
                    error="chart_config为空"
                )

            # 验证图表配置
            validation_result = self._validate_chart_config(chart_config)
            if not validation_result.get('valid', False):
                error_msg = validation_result.get('reason', '图表配置验证失败')
                logger.error(f"❌ 图表配置验证失败: {error_msg}")
                return EChartsExpertOutput(
                    success=False,
                    original_prompts=prompts,
                    error=error_msg
                )

            # 处理图表配置中的函数
            processed_config = self._process_chart_functions(chart_config)

            logger.info("✅ ECharts专家配置生成成功（带指导）")
            return EChartsExpertOutput(
                success=True,
                chart_config=processed_config,
                python_code_transformer=python_code_transformer,
                original_prompts=prompts,
                error=None
            )

        except Exception as e:
            logger.error(f"❌ ECharts专家生成失败（带指导）: {str(e)}")
            import traceback
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            return EChartsExpertOutput(
                success=False,
                original_prompts=None,  # 异常情况下可能prompts未初始化
                error=f"ECharts专家生成异常: {str(e)}"
            )
    
    def _extract_chart_config(self, response_content: str) -> Optional[Dict[str, Any]]:
        """
        从LLM响应中提取ECharts配置和python_code_transformer
        
        Args:
            response_content: LLM响应内容
            
        Returns:
            Optional[Dict[str, Any]]: 提取的完整响应（包含chart_config和可选的python_code_transformer）
        """
        try:
            # 首先尝试直接解析整个响应为JSON
            try:
                result = json.loads(response_content)
                logger.info("直接JSON解析成功")
                
                # BUG修复: 处理包含python_code_transformer的响应格式
                # 修复策略: 返回完整响应，让上层处理chart_config和python_code_transformer
                # 影响范围: app/services/chart_generation/experts/echarts_expert.py:242-247
                # 修复日期: 2025-01-12
                
                # 直接返回完整响应
                return result
            except json.JSONDecodeError:
                logger.info("直接JSON解析失败，尝试提取JSON代码块")

            # 查找并提取JSON代码块
            json_start = -1
            json_end = -1

            # 查找 ```json 代码块
            if "```json" in response_content:
                json_start = response_content.find("```json") + 7
                json_end = response_content.find("```", json_start)
            # 查找 ``` 代码块（可能没有指定语言）
            elif "```" in response_content:
                first_triple = response_content.find("```")
                # 跳过第一个```和可能的语言标识符
                content_after_first = response_content[first_triple + 3:]
                newline_pos = content_after_first.find('\n')
                if newline_pos != -1:
                    json_start = first_triple + 3 + newline_pos + 1
                else:
                    json_start = first_triple + 3
                json_end = response_content.find("```", json_start)
            # 查找第一个 { 到最后一个 }
            else:
                json_start = response_content.find("{")
                json_end = response_content.rfind("}") + 1

            if json_start >= 0 and json_end > json_start:
                json_str = response_content[json_start:json_end].strip()

                try:
                    result = json.loads(json_str)
                    # 直接返回完整响应
                    return result
                except json.JSONDecodeError as e:
                    logger.warning(f"提取的JSON解析失败: {str(e)}")
                    logger.warning(f"提取的JSON内容: {json_str[:500]}...")

            # 如果所有方法都失败，返回None
            logger.error("无法从响应中提取有效的JSON")
            logger.error(f"响应内容: {response_content[:1000]}...")
            return None

        except Exception as e:
            logger.error(f"JSON提取过程中发生异常: {str(e)}")
            return None
    
    def _validate_chart_config(self, chart_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证图表配置的完整性
        
        Args:
            chart_config: ECharts配置
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        try:
            # 检查基本必要字段
            basic_required_fields = ["title", "series"]
            for field in basic_required_fields:
                if field not in chart_config:
                    return {
                        'valid': False,
                        'reason': f"缺少必要字段: {field}"
                    }

            # 检查series是否有数据
            series = chart_config.get("series", [])
            if not series or not isinstance(series, list) or len(series) == 0:
                return {
                    'valid': False,
                    'reason': "series字段为空或格式错误"
                }

            # 检查第一个series是否有数据或使用dataset+encode模式
            first_series = series[0]
            if not isinstance(first_series, dict):
                return {
                    'valid': False,
                    'reason': "第一个series格式错误"
                }

            # 检查是否使用dataset+encode模式或传统data模式
            has_data = first_series.get("data") is not None
            has_encode = first_series.get("encode") is not None
            has_dataset = chart_config.get("dataset") is not None

            if not has_data and not (has_encode and has_dataset):
                return {
                    'valid': False,
                    'reason': "series缺少数据：需要data字段或dataset+encode模式"
                }

            # 根据图表类型检查特定字段
            chart_type = first_series.get("type", "")

            # 饼图不需要xAxis和yAxis
            if chart_type in ["pie", "doughnut"]:
                return {'valid': True, 'reason': None}

            # 其他图表类型需要xAxis和yAxis
            axis_required_fields = ["xAxis", "yAxis"]
            for field in axis_required_fields:
                if field not in chart_config:
                    return {
                        'valid': False,
                        'reason': f"缺少必要字段: {field}"
                    }

            return {'valid': True, 'reason': None}

        except Exception as e:
            logger.error(f"验证图表配置时出错: {str(e)}")
            return {
                'valid': False,
                'reason': f"验证过程异常: {str(e)}"
            }
    
    def _process_chart_functions(self, chart_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理图表配置中的JavaScript函数

        新策略：保留和处理formatter函数，与Simple Agent保持一致
        支持智能格式化，提升用户体验

        Args:
            chart_config: 原始图表配置

        Returns:
            Dict[str, Any]: 处理后的图表配置（保留formatter函数）
        """
        import copy

        def process_value(value):
            """递归处理配置值"""
            if isinstance(value, dict):
                if "__function__" in value:
                    # 保留__function__格式，与Simple Agent一致
                    return {"__function__": value["__function__"]}
                else:
                    return {k: process_value(v) for k, v in value.items()}
            elif isinstance(value, list):
                return [process_value(item) for item in value]
            else:
                return value

        try:
            processed_config = process_value(copy.deepcopy(chart_config))

            # 统计处理结果
            original_str = json.dumps(chart_config, ensure_ascii=False)
            processed_str = json.dumps(processed_config, ensure_ascii=False)

            if original_str != processed_str:
                logger.info("✅ 成功处理了JavaScript函数字段，保持formatter功能")
            else:
                logger.debug("✅ 配置中未发现需要处理的JavaScript函数字段")

            return processed_config
        except Exception as e:
            logger.warning(f"处理JavaScript函数时出错: {str(e)}，返回原始配置")
            return chart_config
    
    async def _record_token_usage(
        self,
        response,
        analysis_id: Optional[str],
        user_id: Optional[str],
        project_id: Optional[str],
        organization_id: Optional[int],
        background_tasks
    ):
        """记录Token使用量"""
        try:
            # 获取并记录Token用量
            usage_data = response.usage.model_dump() if response.usage else None
            if usage_data and self.model_id and organization_id and background_tasks:
                prompt_tokens = usage_data.get('prompt_tokens', 0)
                completion_tokens = usage_data.get('completion_tokens', 0)
                total_tokens = usage_data.get('total_tokens', 0)

                # 异步记录Token用量
                from app.services.token_usage_service import record_usage_task
                background_tasks.add_task(
                    record_usage_task,
                    user_id=user_id,
                    org_id=organization_id,
                    project_id=project_id,
                    analysis_id=analysis_id,
                    model_id=self.model_id,
                    model_name=self.model,
                    prompt_tokens=prompt_tokens,
                    completion_tokens=completion_tokens,
                    total_tokens=total_tokens
                )
        except Exception as e:
            logger.warning(f"记录Token使用量失败: {str(e)}")


# 便捷函数
async def generate_echarts_config(
    db: Session,
    expert_input: ExpertInput,
    analysis_id: Optional[str] = None,
    user_id: Optional[str] = None,
    project_id: Optional[str] = None,
    organization_id: Optional[int] = None,
    background_tasks = None
) -> EChartsExpertOutput:
    """
    便捷函数：生成ECharts配置
    
    Args:
        db: 数据库会话
        expert_input: 专家输入数据
        analysis_id: 分析ID
        user_id: 用户ID
        project_id: 项目ID
        organization_id: 组织ID
        background_tasks: 后台任务
        
    Returns:
        EChartsExpertOutput: ECharts专家输出
    """
    expert = EChartsExpert(db)
    return await expert.generate_chart_config(
        expert_input=expert_input,
        analysis_id=analysis_id,
        user_id=user_id,
        project_id=project_id,
        organization_id=organization_id,
        background_tasks=background_tasks
    )
