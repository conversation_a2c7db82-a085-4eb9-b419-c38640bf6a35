"""
图表生成Agent
=============
基于专家混合架构的智能图表生成Agent
支持多种可视化形态（图表/表格），提升可扩展性和输出质量
"""

import json
import time
import traceback
import pandas as pd
import numpy as np
import datetime
import math
import re
from typing import Dict, Any, Optional, Callable, List
from sqlalchemy.orm import Session

from app.core.config import settings
from app.services.llm_factory import get_llm_service
from app.core.logger import log
from .prompts import get_master_planning_prompt

from .experts.echarts_expert import EChartsExpert
from .experts.table_expert import TableExpert
from .experts.super_script_expert import SuperScriptExpert
from .simple_agent import SimpleChartAgent
from .data_structures import (
    validate_planning_document,
    create_expert_input,
    create_moe_result,
    create_multi_chart_result,
    DisplayType,
    MultiChartResult,
    MultiChartExecutionResult
)

from .utils.data_profiler import DataProfiler
from app.services.chart_cache_service import chart_cache_service

class ChartGenerationAgent:
    """
    基于MoE架构的图表生成Agent
    
    工作流程：
    1. 主规划师：分析数据，决策可视化形态，生成处理代码
    2. 沙箱执行器：安全执行数据处理代码
    3. 专家生成器：根据类型调用相应专家生成最终输出
    """
    
    def __init__(self, db: Session):
        """
        初始化MoE图表生成Agent
        
        Args:
            db: 数据库会话
        """
        self.db = db
        
        # 获取LLM服务
        self.llm_service = get_llm_service(db)
        
        # 获取LLM客户端
        if hasattr(self.llm_service, 'client'):
            self.llm_client = self.llm_service.client
        else:
            from openai import AsyncOpenAI
            self.llm_client = AsyncOpenAI(
                api_key=settings.OPENAI_API_KEY,
                base_url=settings.OPENAI_BASE_URL
            )
        
        # 获取模型名称
        if hasattr(self.llm_service, 'model'):
            self.model = self.llm_service.model
        else:
            self.model = settings.OPENAI_MODEL

        # 获取模型ID（用于Token统计）
        if hasattr(self.llm_service, 'model_id'):
            self.model_id = self.llm_service.model_id
        else:
            self.model_id = None
            log.warning("LLM服务未提供model_id，Token统计可能无法正常工作")
        
        # 初始化组件
        self.echarts_expert = EChartsExpert(db)
        self.table_expert = TableExpert(db)
        self.super_script_expert = SuperScriptExpert(db)
        self.data_profiler = DataProfiler()
    
    async def process_tool_result(
        self,
        analysis_id: str,
        step_id: str,
        tool_name: str,
        tool_result: Dict[str, Any],
        user_query: str,
        schema_info: Optional[str] = None,
        reasoning: str = "",
        sql_statement: Optional[str] = None,
        conversation_context: Optional[Dict[str, Any]] = None,
        event_callback: Optional[Callable[[str, Dict[str, Any]], None]] = None,
        user_id: Optional[str] = None,
        project_id: Optional[str] = None,
        background_tasks = None,
        user_language: str = 'zh-CN',
        analysis_insights = None,
        enhance_tool_result: bool = True
    ) -> Optional[Dict[str, Any]]:
        """
        处理工具执行结果，使用MoE架构生成可视化
        
        Args:
            analysis_id: 分析ID
            step_id: 步骤ID
            tool_name: 工具名称
            tool_result: 工具执行结果
            user_query: 用户查询
            schema_info: 数据库Schema信息
            reasoning: 推理过程
            sql_statement: 执行的SQL语句
            conversation_context: 对话上下文，包含历史图表等信息
                格式: {
                    "previous_charts": [
                        {
                            "step_id": "step_001",
                            "chart_type": "line", 
                            "title": "用户增长趋势",
                            "data_mapping": {"x_axis": "date", "y_axis": "user_count"},
                            "description": "展示最近6个月的用户增长趋势"
                        }
                    ]
                }
            event_callback: 事件回调函数
            user_id: 用户ID
            project_id: 项目ID
            background_tasks: FastAPI后台任务对象
            user_language: 用户语言偏好
            
        Returns:
            Optional[Dict[str, Any]]: 如果生成了可视化，返回结果事件数据；否则返回None
        """
        try:
            total_start_time = time.time()
            log.info(f"MoE图表生成Agent开始处理工具结果: {tool_name}")
            
            # 获取组织ID用于Token统计
            organization_id = await self._get_organization_id(user_id, project_id)
            
            # 从对话上下文中提取历史图表信息
            previous_charts = None
            if conversation_context and 'previous_charts' in conversation_context:
                previous_charts = conversation_context['previous_charts']
                log.info(f"📊 检测到历史图表记录: {len(previous_charts)} 个")

                # 🔥 详细打印历史图表信息
                for i, chart in enumerate(previous_charts, 1):
                    log.info(f"   历史图表{i}: {chart.get('title', '未命名')} "
                            f"(类型: {chart.get('chart_type', '未知')}, "
                            f"步骤: {chart.get('step_id', '未知')}, "
                            f"轮次: {chart.get('round_number', '未知')})")
            else:
                log.info("📊 未检测到历史图表记录")

            # 🔥 智能路由：根据数据量选择生成策略
            data_rows = self._get_data_row_count(tool_result)
            log.info(f"📊 检测到数据行数: {data_rows}")

            if data_rows <  100:
                log.info("🚀 数据量较小，使用简单Agent直接生成")
                return await self._use_simple_agent(
                    tool_result=tool_result,
                    user_query=user_query,
                    tool_name=tool_name,
                    reasoning=reasoning,
                    schema_info=schema_info,
                    sql_statement=sql_statement,
                    step_id=step_id,
                    analysis_id=analysis_id,
                    user_id=user_id,
                    project_id=project_id,
                    organization_id=organization_id,
                    background_tasks=background_tasks,
                    event_callback=event_callback,
                    user_language=user_language,
                    analysis_insights=analysis_insights,
                    conversation_context=conversation_context,
                    enhance_tool_result=enhance_tool_result
                )
            else:
                log.info("🏗️ 数据量较大，使用MoE架构生成多图表")

            # 阶段1：主规划师分析（MoE架构）
            planning_result = await self._master_planner_analysis(
                tool_result=tool_result,
                user_query=user_query,
                tool_name=tool_name,
                reasoning=reasoning,
                schema_info=schema_info,
                sql_statement=sql_statement,
                previous_charts=previous_charts,
                analysis_id=analysis_id,
                user_id=user_id,
                project_id=project_id,
                organization_id=organization_id,
                background_tasks=background_tasks,
                user_language=user_language,
                analysis_insights=analysis_insights
            )
            
            if not planning_result:
                log.error("❌ 主规划师分析失败，返回None")
                return None

            should_generate = planning_result.get('should_generate', False)
            reason = planning_result.get('reason', '未提供原因')

            log.info(f"🎯 主规划师决策结果:")
            log.info(f"   是否生成可视化: {should_generate}")
            log.info(f"   决策原因: {reason}")

            if not should_generate:
                log.info("⏭️ 主规划师判断不适合生成可视化，跳过后续流程")
                return None
            
            # 验证规划文档
            validation_result = validate_planning_document(planning_result)
            if not validation_result['valid']:
                log.error(f"规划文档验证失败: {validation_result['error']}")
                return None
            
            planning_doc = validation_result['planning_document']

            # 检查是否有可视化规划
            if not planning_doc.visualization_plans:
                log.error("规划文档中没有可视化规划")
                return None

            # 🔥 核心改动：支持多个规划
            total_plans = len(planning_doc.visualization_plans)
            log.info(f"📊 检测到 {total_plans} 个可视化规划，开始批量处理")
            
            # 阶段2：数据准备
            # 直接将工具结果转换为DataFrame，不执行任何代码转换
            processed_df = self._convert_tool_result_to_dataframe(tool_result)
            if processed_df is None:
                log.error("数据转换失败")
                return None
            
            # 阶段3：生成多维数据写真
            log.info("📊 开始生成多维数据写真...")
            data_profile = self.data_profiler.generate_profile(
                df=processed_df,
                context={
                    "user_query": user_query,
                    "tool_name": tool_name,
                    "reasoning": reasoning,
                    "total_plans": total_plans
                }
            )
            log.info(f"✅ 数据写真生成完成，包含 {len(data_profile.get('column_profiles', {}))} 列的详细画像")

            # 阶段4：并行专家生成 + 进度反馈
            import asyncio
            
            # BUG修复: 删除所有进度事件发送，简化为后端并行执行
            # 修复策略: 参考Simple Agent，只在最后统一发送step_started, step_completed, chart_generated事件
            # 影响范围: app/services/chart_generation/agent.py:262-292
            # 修复日期: 2025-01-29

            log.info(f"🚀 开始并行处理 {total_plans} 个图表规划")
            
            # BUG修复: 添加调试日志查看planning_doc.visualization_plans是否包含重复计划
            log.info(f"🔍 检查规划文档中的图表计划:")
            plan_ids = [plan.plan_id for plan in planning_doc.visualization_plans]
            duplicate_plan_ids = [plan_id for plan_id in plan_ids if plan_ids.count(plan_id) > 1]
            if duplicate_plan_ids:
                log.error(f"🚨 检测到重复的plan_id: {duplicate_plan_ids}")
            
            for i, plan in enumerate(planning_doc.visualization_plans):
                log.info(f"   plan[{i}]: id={plan.plan_id}, type={plan.display_type}, priority={plan.priority}")
            
            # 创建并行任务
            tasks = []
            for i, plan in enumerate(planning_doc.visualization_plans):
                log.info(f"📋 创建任务 {i+1}/{total_plans}: {plan.plan_id}")
                
                task = self._process_single_plan_with_progress(
                    plan=plan,
                    plan_index=i,
                    total_plans=total_plans,
                    processed_df=processed_df,
                    data_profile=data_profile,
                    step_id=step_id,
                    analysis_id=analysis_id,
                    user_id=user_id,
                    project_id=project_id,
                    organization_id=organization_id,
                    background_tasks=background_tasks,
                    user_language=user_language,
                    event_callback=event_callback,
                    expert_guidance=planning_result.get('expert_guidance', {})
                )
                tasks.append(task)

            # 并行执行所有任务
            log.info("⚡ 开始并行执行图表生成任务")
            chart_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # BUG修复: 添加调试日志查看并行执行结果
            log.info(f"🔍 并行执行完成，原始结果数量: {len(chart_results)}")
            
            # 过滤成功的结果
            successful_results = []
            failed_count = 0
            
            for i, result in enumerate(chart_results):
                if isinstance(result, Exception):
                    log.error(f"❌ 第 {i+1} 个图表生成异常: {str(result)}")
                    failed_count += 1
                elif result is None:
                    log.warning(f"⚠️ 第 {i+1} 个图表生成返回None")
                    failed_count += 1
                else:
                    successful_results.append(result)
                    log.info(f"✅ 第 {i+1} 个图表生成成功: {result.title} (ID: {result.chart_id})")

            chart_results = successful_results
            log.info(f"🔍 过滤后成功结果数量: {len(chart_results)}")
            
            # 检查成功结果中是否有重复
            success_chart_ids = [r.chart_id for r in chart_results]
            success_duplicate_ids = [chart_id for chart_id in success_chart_ids if success_chart_ids.count(chart_id) > 1]
            if success_duplicate_ids:
                log.error(f"🚨 检测到成功结果中重复的chart_id: {success_duplicate_ids}")
            
            if not chart_results:
                log.error("❌ 所有图表生成都失败了")
                if event_callback and callable(event_callback):
                    try:
                        event_callback("chart_generation_failed", {
                            "message": "所有图表生成都失败了",
                            "step_id": step_id,
                            "analysis_id": analysis_id,
                            "generation_method": "moe_agent"
                        })
                    except Exception as e:
                        log.warning(f"发送进度事件失败: {str(e)}")
                return None

            log.info(f"✅ 并行生成完成: 成功 {len(chart_results)} 个，失败 {failed_count} 个")
            # BUG修复: 删除chart_generation_completed事件发送，统一在最后处理
            # 修复策略: 直接生成多图表事件数据，不发送中间事件
            # 影响范围: app/services/chart_generation/agent.py:356-379
            # 修复日期: 2025-01-29

            # 生成多图表事件数据
            if chart_results:
                chart_event_data = await self._generate_multi_chart_event_data(
                    chart_results=chart_results,
                    step_id=step_id,
                    analysis_id=analysis_id,
                    event_callback=None,  # 不在这里发送事件
                    user_language=user_language
                )

                # 🔥 新增：存储图表到Redis缓存
                await self._cache_charts_to_redis(
                    chart_results=chart_results,
                    analysis_id=analysis_id,
                    step_id=step_id,
                    conversation_context=conversation_context
                )

                # 🔥 新增：增强工具结果，让主Agent感知图表生成状态
                if enhance_tool_result:
                    self._enhance_tool_result_with_chart_status(
                        tool_result=tool_result,
                        chart_status="success",
                        chart_count=len(chart_results),
                        generation_method="moe_agent",
                        message=f"智能多图表生成完成，共生成 {len(chart_results)} 个图表"
                    )
            else:
                log.error("❌ 所有图表生成都失败了")

                # 🔥 新增：增强工具结果，记录图表生成失败状态
                if enhance_tool_result:
                    self._enhance_tool_result_with_chart_status(
                        tool_result=tool_result,
                        chart_status="failed",
                        chart_count=0,
                        generation_method="moe_agent",
                        message="所有图表生成都失败了"
                    )
                return None

            total_end_time = time.time()
            total_duration = total_end_time - total_start_time
            log.info(f"MoE图表生成完成，总耗时: {total_duration:.2f}秒")

            # BUG修复: 参考Simple Agent，统一在最后发送所有事件
            # 修复策略: 按照Simple Agent的模式发送step_started, step_completed, chart_generated事件
            # 影响范围: app/services/chart_generation/agent.py:376-378
            # 修复日期: 2025-01-29
            if chart_event_data and event_callback and callable(event_callback):
                try:
                    # 提取charts_data用于事件发送
                    charts_data = chart_event_data.get("chart_data", {}).get("data", {}).get("charts", [])
                    
                    # 1. 推送图表步骤开始事件
                    from app.services.system_tools import SystemTools
                    tool_display_name = SystemTools.get_tool_name('chart_generation_tool', user_language or 'zh-CN')

                    event_callback("step_started", {
                        "step_id": chart_event_data["step_id"],
                        "tool_name": tool_display_name,
                        "parameters": {
                            "chart_count": len(charts_data),
                            "chart_types": [chart.get('chart_type', 'echarts') for chart in charts_data],
                            "auto_generated": True
                        },
                        "message": "开始智能图表生成" if user_language == 'zh-CN' else "Starting intelligent chart generation"
                    })

                    # 2. 推送图表步骤完成事件
                    step_result = {
                        "message": chart_event_data["message"],
                        "charts": charts_data,
                        "data": chart_event_data["chart_data"]["data"],
                        "display_format": chart_event_data["chart_data"]["display_format"],
                        "auto_generated": True,
                        "generated_by": "moe_agent"
                    }

                    event_callback("step_completed", {
                        "step_id": chart_event_data["step_id"],
                        "tool_name": "智能图表生成",
                        "result": step_result,
                        "reasoning": f"基于MoE Agent自动生成{len(charts_data)}个可视化",
                        "message": chart_event_data["message"]
                    })

                    # 3. 推送图表生成事件（用于图表弹框显示）
                    event_callback("chart_generated", {
                        "message": chart_event_data["message"],
                        "step_id": chart_event_data["step_id"],
                        "analysis_id": analysis_id,
                        "chart_data": chart_event_data["chart_data"],
                        "tool_name": "智能图表生成",
                        "generation_method": "moe_agent",
                        "chart_count": len(charts_data),
                        "user_language": user_language
                    })
                    
                    log.info(f"✅ MoE Agent统一事件发送完成，共生成 {len(charts_data)} 个可视化")
                    
                except Exception as e:
                    log.error(f"❌ MoE Agent事件发送失败: {str(e)}")

            return chart_event_data
            
        except Exception as e:
            total_end_time = time.time()
            total_duration = total_end_time - total_start_time
            
            log.error("=" * 80)
            log.error(f"❌ MoE图表生成Agent处理失败")
            log.error(f"📍 步骤ID: {step_id}")
            log.error(f"🔧 工具名称: {tool_name}")
            log.error(f"⚠️ 错误类型: {type(e).__name__}")
            log.error(f"💬 错误信息: {str(e)}")
            log.error(f"⏱️ 失败总耗时: {total_duration:.2f}秒")
            log.error(f"📋 异常堆栈: {traceback.format_exc()}")
            log.error("=" * 80)
            
            return None

    def _sample_data_for_master_planner(self, df: pd.DataFrame, sample_size: int = 10) -> Optional[List[Dict[str, Any]]]:
        """
        为主规划师随机采样数据
        
        Args:
            df: 数据DataFrame
            sample_size: 采样数量，默认10行
            
        Returns:
            Optional[List[Dict[str, Any]]]: 采样的数据列表
        """
        try:
            if df is None or df.empty:
                return None
            
            # 获取实际的采样数量（不超过数据总行数）
            actual_sample_size = min(sample_size, len(df))
            
            # 随机采样
            sampled_df = df.sample(n=actual_sample_size, random_state=42)  # 使用固定随机种子确保可重现
            
            # 转换为字典列表，确保JSON可序列化
            samples = []
            for _, row in sampled_df.iterrows():
                sample_row = {}
                for col, value in row.items():
                    # 处理各种数据类型，确保JSON可序列化
                    if pd.isna(value):
                        sample_row[col] = None
                    elif isinstance(value, (pd.Timestamp, pd.DatetimeTZDtype)):
                        sample_row[col] = str(value)
                    elif isinstance(value, (int, float, str, bool)):
                        sample_row[col] = value
                    else:
                        sample_row[col] = str(value)
                samples.append(sample_row)
            
            log.info(f"📊 成功采样 {len(samples)} 行数据用于主规划师分析")
            return samples
            
        except Exception as e:
            log.error(f"❌ 数据采样失败: {str(e)}")
            return None

    def _get_data_row_count(self, tool_result: Dict[str, Any]) -> int:
        """获取数据行数 - 与_convert_tool_result_to_dataframe保持一致的数据提取逻辑"""
        try:
            log.info("🔍 开始检测数据行数")
            log.info(f"📊 工具结果类型: {type(tool_result)}")
            log.info(f"📊 工具结果键: {list(tool_result.keys()) if isinstance(tool_result, dict) else 'N/A'}")

            # 尝试从不同的字段中提取数据 - 与_convert_tool_result_to_dataframe保持一致
            data = None
            extraction_method = None

            if 'data' in tool_result and isinstance(tool_result['data'], list):
                data = tool_result['data']
                extraction_method = "tool_result['data']"
            elif 'results' in tool_result and isinstance(tool_result['results'], list):
                data = tool_result['results']
                extraction_method = "tool_result['results']"
            elif 'result' in tool_result and isinstance(tool_result['result'], list):
                data = tool_result['result']
                extraction_method = "tool_result['result']"
            elif 'rows' in tool_result and isinstance(tool_result['rows'], list):
                data = tool_result['rows']
                extraction_method = "tool_result['rows']"
            elif isinstance(tool_result, list):
                data = tool_result
                extraction_method = "tool_result (直接列表)"
            # 检查嵌套的data字段
            elif isinstance(tool_result, dict) and 'data' in tool_result and isinstance(tool_result['data'], dict) and 'rows' in tool_result['data']:
                data = tool_result['data']['rows']
                extraction_method = "tool_result['data']['rows']"

            log.info(f"🔍 数据提取方法: {extraction_method}")

            if data is None:
                log.warning("⚠️ 无法从工具结果中提取数据列表")
                log.warning("🔍 支持的数据字段: 'data', 'results', 'result', 'rows' 或直接列表")
                return 0

            row_count = len(data)
            log.info(f"✅ 成功检测数据行数: {row_count}")
            return row_count

        except Exception as e:
            log.error(f"❌ 检测数据行数失败: {str(e)}")
            return 0

    async def _use_simple_agent(
        self,
        tool_result: Dict[str, Any],
        user_query: str,
        tool_name: str,
        reasoning: str,
        schema_info: Optional[str],
        sql_statement: Optional[str],
        step_id: str,
        analysis_id: str,
        user_id: Optional[str],
        project_id: Optional[str],
        organization_id: Optional[int],
        background_tasks,
        event_callback: Optional[Callable],
        user_language: str,
        analysis_insights = None,
        conversation_context: Optional[Dict[str, Any]] = None,
        enhance_tool_result: bool = True
    ) -> Optional[Dict[str, Any]]:
        """使用简单Agent生成单图表"""
        try:
            # 提取历史图表信息
            # 从对话上下文中提取历史图表信息
            previous_charts = None
            if conversation_context and 'previous_charts' in conversation_context:
                previous_charts = conversation_context['previous_charts']
            if previous_charts:
                log.info(f"📊 检测到 {len(previous_charts)} 个历史图表，将传递给Simple Agent进行重复检查")

            # 创建简单Agent实例
            simple_agent = SimpleChartAgent(self.db)

            # 调用简单Agent
            result = await simple_agent.process_tool_result(
                analysis_id=analysis_id,
                step_id=step_id,
                tool_name=tool_name,
                tool_result=tool_result,
                user_query=user_query,
                schema_info=schema_info,
                reasoning=reasoning,
                sql_statement=sql_statement,
                event_callback=event_callback,
                user_id=user_id,
                project_id=project_id,
                organization_id=organization_id,
                background_tasks=background_tasks,
                user_language=user_language,
                analysis_insights=analysis_insights,
                enhance_tool_result=enhance_tool_result,  # 🔥 传递增强工具结果参数
                previous_charts=previous_charts
            )

            if result:
                log.info("✅ 简单Agent生成成功")
                return result
            else:
                log.info("ℹ️ 简单Agent判断不适合生成图表")
                return None

        except Exception as e:
            log.error(f"❌ 简单Agent调用失败: {str(e)}")
            return None

    async def _process_single_plan_with_progress(
        self,
        plan,
        plan_index: int,
        total_plans: int,
        processed_df: pd.DataFrame,
        data_profile: Dict[str, Any],
        step_id: str,
        analysis_id: str,
        user_id: Optional[str],
        project_id: Optional[str],
        organization_id: Optional[int],
        background_tasks,
        user_language: str,
        event_callback: Optional[Callable[[str, Dict[str, Any]], None]] = None,
        expert_guidance: Optional[Dict[str, Any]] = None
    ) -> Optional[MultiChartResult]:
        """
        处理单个图表规划 - 带进度反馈版本
        
        Args:
            plan: 可视化规划
            plan_index: 规划索引
            total_plans: 总规划数
            processed_df: 处理后的数据
            data_profile: 数据写真
            step_id: 步骤ID
            analysis_id: 分析ID
            user_id: 用户ID
            project_id: 项目ID
            organization_id: 组织ID
            background_tasks: 后台任务
            user_language: 用户语言
            event_callback: 事件回调
            expert_guidance: 专家指导信息

        Returns:
            Optional[MultiChartResult]: 图表结果
        """
        try:
            # BUG修复: 删除单个图表进度事件发送
            # 修复策略: 简化为静默执行，只在最后统一发送事件
            # 影响范围: app/services/chart_generation/agent.py:548-560
            # 修复日期: 2025-01-29

            log.info(f"🎨 开始专家生成，类型: {plan.display_type}, 优先级: {plan.priority}")

            # 专家生成 - 传递专家指导
            expert_result = await self._expert_generation_with_guidance(
                plan=plan,
                processed_df=processed_df,
                data_profile=data_profile,
                analysis_id=analysis_id,
                user_id=user_id,
                project_id=project_id,
                organization_id=organization_id,
                background_tasks=background_tasks,
                user_language=user_language,
                expert_guidance=expert_guidance
            )

            if not expert_result or not expert_result.success:
                error_msg = expert_result.error if expert_result else "专家生成失败"
                log.error(f"专家生成失败: {error_msg}")
                
                # BUG修复: 删除失败事件发送
                # 修复策略: 静默处理错误，只在最后统一发送事件
                # 影响范围: app/services/chart_generation/agent.py:572-585
                # 修复日期: 2025-01-29
                
                return None

            # 🔥 关键修复：从expert_result中提取处理后的DataFrame
            if expert_result.debug_info and "processed_df" in expert_result.debug_info:
                processed_df = expert_result.debug_info["processed_df"]
                log.info(f"✅ 从专家结果中提取处理后的DataFrame: {processed_df.shape}")
                log.info(f"✅ 处理后的列名: {list(processed_df.columns)}")
            else:
                log.warning("⚠️ 专家结果中未找到处理后的DataFrame，使用原始数据")

            # 创建图表结果
            chart_result = MultiChartResult(
                chart_id=f"chart_{plan.plan_id}_{plan_index}",
                plan_id=plan.plan_id,
                title=expert_result.title or f"图表 {plan_index + 1}",
                chart_type=plan.display_type,
                config=expert_result.content,  # 修复：使用content字段（MoEExecutionResult）
                description=expert_result.description or plan.description,
                priority=plan.priority
            )

            # BUG修复: 删除单个图表完成事件发送
            # 修复策略: 静默执行，只在最后统一发送事件
            # 影响范围: app/services/chart_generation/agent.py:611-624
            # 修复日期: 2025-01-29

            log.info(f"✅ 图表生成成功: {chart_result.title}")
            return chart_result

        except Exception as e:
            log.error(f"处理图表规划失败: {str(e)}")
            
            # BUG修复: 删除异常事件发送
            # 修复策略: 静默处理异常，只在最后统一发送事件
            # 影响范围: app/services/chart_generation/agent.py:611-624
            # 修复日期: 2025-01-29
            
            return None

    async def _master_planner_analysis(
        self,
        tool_result: Dict[str, Any],
        user_query: str,
        tool_name: str,
        reasoning: str,
        schema_info: Optional[str],
        sql_statement: Optional[str],
        previous_charts: Optional[List[Dict[str, Any]]],
        analysis_id: str,
        user_id: Optional[str],
        project_id: Optional[str],
        organization_id: Optional[int],
        background_tasks,
        user_language: str,
        analysis_insights = None
    ) -> Optional[Dict[str, Any]]:
        """
        主规划师分析阶段

        Returns:
            Optional[Dict[str, Any]]: 规划结果
        """
        try:
            log.info("=" * 80)
            log.info("🎯 主规划师开始分析")
            log.info("=" * 80)
            start_time = time.time()

            # 功能实现: 在主规划师阶段使用DataProfiler生成数据概要
            # 实现方案: 先将tool_result转换为DataFrame，然后生成数据概要传递给LLM
            # 影响范围: app/services/chart_generation/agent.py _master_planner_analysis方法
            # 实现日期: 2025-01-31
            
            # 生成数据概要和随机样本用于主规划师决策
            log.info("📊 开始为主规划师生成数据概要和样本...")
            data_profile = None
            data_samples = None
            processed_df = None
            
            try:
                # 转换工具结果为DataFrame
                processed_df = self._convert_tool_result_to_dataframe(tool_result)
                
                if processed_df is not None and not processed_df.empty:
                    # 生成多维数据写真
                    data_profile = self.data_profiler.generate_profile(
                        df=processed_df,
                        context={
                            "user_query": user_query,
                            "tool_name": tool_name,
                            "reasoning": reasoning,
                            "stage": "master_planner"
                        }
                    )
                    log.info(f"✅ 主规划师数据概要生成完成，大小: {len(str(data_profile))} 字符")
                    
                    # 随机采样数据样本
                    data_samples = self._sample_data_for_master_planner(processed_df, sample_size=10)
                    log.info(f"✅ 主规划师数据采样完成，采样数量: {len(data_samples) if data_samples else 0} 行")
                else:
                    log.warning("⚠️ 无法转换工具结果为DataFrame，主规划师将使用原始数据")
                    
            except Exception as e:
                log.warning(f"⚠️ 主规划师数据概要生成失败: {str(e)}，将使用原始数据")

            # 获取主规划师提示词 - 传递数据概要和样本
            prompts = get_master_planning_prompt(
                user_query=user_query,
                tool_result=tool_result,
                tool_name=tool_name,
                reasoning=reasoning,
                schema_info=schema_info,
                sql_statement=sql_statement,
                previous_charts=previous_charts,
                user_language=user_language,
                analysis_insights=analysis_insights,
                data_profile=data_profile,  # 传递数据概要
                data_samples=data_samples   # 新增：传递数据样本
            )

            # 详细日志：输入参数
            log.info("📋 主规划师输入参数:")
            log.info(f"   用户查询: {user_query}")
            log.info(f"   工具名称: {tool_name}")
            log.info(f"   推理过程: {reasoning[:200]}..." if len(reasoning) > 200 else f"   推理过程: {reasoning}")
            log.info(f"   用户语言: {user_language}")
            log.info(f"   Schema信息: {'有' if schema_info else '无'}")
            log.info(f"   SQL语句: {'有' if sql_statement else '无'}")
            log.info(f"   历史图表: {len(previous_charts) if previous_charts else 0} 个")

            # 详细日志：工具结果
            log.info("📊 工具执行结果:")
            try:
                tool_result_str = json.dumps(tool_result, ensure_ascii=False, indent=2)
                if len(tool_result_str) > 1000:
                    log.info(f"   结果长度: {len(tool_result_str)} 字符")
                    log.info(f"   结果预览: {tool_result_str[:500]}...")
                else:
                    log.info(f"   完整结果: {tool_result_str}")
            except Exception as e:
                log.info(f"   结果序列化失败: {str(e)}")
                log.info(f"   结果类型: {type(tool_result)}")
                log.info(f"   结果键: {list(tool_result.keys()) if isinstance(tool_result, dict) else 'N/A'}")

            # 详细日志：系统提示词
            log.info("🤖 主规划师系统提示词:")
            log.info("-" * 60)
            log.info(prompts["system"])
            log.info("-" * 60)

            # 详细日志：用户提示词
            log.info("👤 主规划师用户提示词:")
            log.info("-" * 60)
            user_prompt = prompts["user"]
            if len(user_prompt) > 2000:
                log.info(f"用户提示词长度: {len(user_prompt)} 字符")
                log.info("用户提示词（前1000字符）:")
                log.info(user_prompt[:1000])
                log.info("...")
                log.info("用户提示词（后1000字符）:")
                log.info(user_prompt[-1000:])
            else:
                log.info(user_prompt)
            log.info("-" * 60)

            # 调用LLM
            log.info("🚀 开始调用LLM模型...")
            log.info(f"   模型: {self.model}")
            log.info(f"   温度: 0.1")

            completion = await self.llm_client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": prompts["system"]},
                    {"role": "user", "content": prompts["user"]}
                ],
                temperature=0.1
            )

            response_content = completion.choices[0].message.content

            # 详细日志：LLM响应
            log.info("📝 主规划师LLM原始响应:")
            log.info("-" * 60)
            if len(response_content) > 3000:
                log.info(f"LLM响应长度: {len(response_content)} 字符")
                log.info("LLM响应（完整内容）:")
                log.info(response_content)
            else:
                log.info(response_content)
            log.info("-" * 60)
            
            # 详细日志：Token使用情况
            if completion.usage:
                usage = completion.usage
                log.info("💰 Token使用情况:")
                log.info(f"   输入Token: {usage.prompt_tokens}")
                log.info(f"   输出Token: {usage.completion_tokens}")
                log.info(f"   总Token: {usage.total_tokens}")

            # 记录Token用量
            await self._record_token_usage(
                completion, analysis_id, user_id, project_id,
                organization_id, background_tasks
            )

            # 解析响应
            log.info("🔍 开始解析LLM响应...")
            result = self._extract_json_from_response(response_content)

            if result:
                log.info("✅ JSON解析成功:")
                try:
                    result_str = json.dumps(result, ensure_ascii=False, indent=2)
                    log.info("-" * 60)
                    log.info(result_str)
                    log.info("-" * 60)
                except Exception as e:
                    log.info(f"   解析结果序列化失败: {str(e)}")
                    log.info(f"   解析结果类型: {type(result)}")
            else:
                log.error("❌ JSON解析失败，返回None")

            end_time = time.time()
            duration = end_time - start_time
            log.info("=" * 80)
            log.info(f"🎯 主规划师分析完成，总耗时: {duration:.2f}秒")
            log.info("=" * 80)

            return result
            
        except Exception as e:
            log.error(f"主规划师分析失败: {str(e)}")
            log.error(f"异常堆栈: {traceback.format_exc()}")
            return None
    

    
    def _convert_tool_result_to_dataframe(self, tool_result: Dict[str, Any]) -> Optional[pd.DataFrame]:
        """
        将工具结果转换为DataFrame

        Args:
            tool_result: 工具执行结果

        Returns:
            Optional[pd.DataFrame]: 转换后的DataFrame
        """
        try:
            log.info("🔄 开始转换工具结果为DataFrame")

            # 详细日志：工具结果结构分析
            log.info(f"📊 工具结果类型: {type(tool_result)}")
            log.info(f"📊 工具结果键: {list(tool_result.keys()) if isinstance(tool_result, dict) else 'N/A'}")

            # 尝试序列化工具结果以便调试
            try:
                tool_result_str = json.dumps(tool_result, ensure_ascii=False, indent=2)
                if len(tool_result_str) > 1000:
                    log.info(f"📊 工具结果长度: {len(tool_result_str)} 字符")
                    log.info(f"📊 工具结果预览: {tool_result_str[:500]}...")
                else:
                    log.info(f"📊 完整工具结果: {tool_result_str}")
            except Exception as e:
                log.info(f"📊 工具结果序列化失败: {str(e)}")

            # 尝试从不同的字段中提取数据
            data = None
            extraction_method = None

            if 'data' in tool_result and isinstance(tool_result['data'], list):
                data = tool_result['data']
                extraction_method = "tool_result['data']"
            elif 'results' in tool_result and isinstance(tool_result['results'], list):
                data = tool_result['results']
                extraction_method = "tool_result['results']"
            elif 'result' in tool_result and isinstance(tool_result['result'], list):
                data = tool_result['result']
                extraction_method = "tool_result['result']"
            elif 'rows' in tool_result and isinstance(tool_result['rows'], list):
                data = tool_result['rows']
                extraction_method = "tool_result['rows']"
            elif isinstance(tool_result, list):
                data = tool_result
                extraction_method = "tool_result (直接列表)"

            log.info(f"🔍 数据提取方法: {extraction_method}")

            if data is None:
                log.error("❌ 无法从工具结果中提取数据列表")
                log.error("🔍 支持的数据字段: 'data', 'results', 'result', 'rows' 或直接列表")
                return None

            log.info(f"✅ 成功提取数据，数据类型: {type(data)}, 数据长度: {len(data)}")

            if not data:
                log.error("❌ 工具结果数据为空")
                return None

            # 显示数据样本
            if len(data) > 0:
                log.info(f"📋 数据样本: {json.dumps(data[0], ensure_ascii=False) if isinstance(data[0], dict) else data[0]}")

            # 转换为DataFrame
            df = pd.DataFrame(data)
            log.info(f"✅ 成功转换为DataFrame，形状: {df.shape}")
            log.info(f"📋 DataFrame列: {list(df.columns)}")

            return df

        except Exception as e:
            log.error(f"❌ 转换工具结果为DataFrame失败: {str(e)}")
            import traceback
            log.error(f"❌ 异常详情: {traceback.format_exc()}")
            return None



    async def _expert_generation_with_guidance(
        self,
        plan,
        processed_df: pd.DataFrame,
        data_profile: Dict[str, Any],
        analysis_id: str,
        user_id: Optional[str],
        project_id: Optional[str],
        organization_id: Optional[int],
        background_tasks,
        user_language: str,
        expert_guidance: Optional[Dict[str, Any]] = None
    ):
        """
        专家生成阶段 - 带专家指导版本
        
        Args:
            plan: 可视化规划
            processed_df: 处理后的数据
            data_profile: 多维数据写真（来自DataProfiler）
            analysis_id: 分析ID
            user_id: 用户ID
            project_id: 项目ID
            organization_id: 组织ID
            background_tasks: 后台任务
            user_language: 用户语言
            expert_guidance: 主规划师的专家指导
            
        Returns:
            专家生成结果
        """
        try:
            log.info("=" * 60)
            log.info(f"🎨 开始专家生成（带指导），类型: {plan.display_type}")
            if expert_guidance:
                log.info(f"📋 收到专家指导: {len(json.dumps(expert_guidance, ensure_ascii=False))} 字符")
            log.info("=" * 60)

            # 🎯 战略指导模式：主规划师不执行数据处理，只提供指导
            # ECharts专家将根据指导进行实际的数据转换和实现
            log.info("🎯 主规划师提供战略指导，数据处理由ECharts专家负责")
            
            # 使用原始数据，等待ECharts专家进行最终的数据转换
            plan_specific_df = processed_df

            # 使用多维数据写真创建专家输入
            log.info("🔄 使用多维数据写真创建专家输入...")
            log.info(f"📊 数据写真大小: {len(json.dumps(data_profile, ensure_ascii=False))} 字符")

            # 创建专家输入
            expert_input = create_expert_input(
                data_summary=data_profile,
                display_instructions=plan.display_instructions.dict() if hasattr(plan.display_instructions, 'dict') else plan.display_instructions,
                user_language=user_language
            )

            # 根据显示类型调用相应专家（带指导）
            if plan.display_type == "echarts":
                # 🚀 新增：检查是否启用超级脚本模式
                use_super_script = getattr(settings, 'USE_SUPER_SCRIPT_MODE', False) or \
                                 expert_guidance and expert_guidance.get('use_super_script', False)
                
                if use_super_script:
                    log.info("🚀 调用超级脚本专家（一体化模式）")
                    
                    # 获取专家上下文
                    expert_context = getattr(plan, 'expert_context', {})
                    
                    result = await self.super_script_expert.generate_complete_script(
                        data_profile=data_profile,
                        display_instructions=plan.display_instructions.dict() if hasattr(plan.display_instructions, 'dict') else plan.display_instructions,
                        expert_guidance=expert_guidance,
                        expert_context=expert_context,
                        user_language=user_language,
                        analysis_id=analysis_id,
                        user_id=user_id,
                        project_id=project_id,
                        organization_id=organization_id,
                        background_tasks=background_tasks
                    )
                else:
                    log.info("🎯 调用ECharts专家（传统模式）")
                    
                    # 获取专家上下文
                    expert_context = getattr(plan, 'expert_context', {})
                    
                    result = await self.echarts_expert.generate_chart_config_with_guidance(
                        expert_input=expert_input,
                        expert_guidance=expert_guidance,
                        expert_context=expert_context,
                        analysis_id=analysis_id,
                        user_id=user_id,
                        project_id=project_id,
                        organization_id=organization_id,
                        background_tasks=background_tasks
                    )
                
                # 🎯 数据处理执行点：根据模式执行不同的处理逻辑
                if result.success and result.python_code_transformer:
                    if use_super_script:
                        log.info("🚀 超级脚本模式：执行一体化脚本...")
                        try:
                            # 执行超级脚本，直接获取ECharts JSON配置
                            chart_config = await self._execute_super_script(
                                processed_df, result.python_code_transformer, analysis_id,
                                original_prompts=result.original_prompts,
                                user_id=user_id,
                                project_id=project_id,
                                organization_id=organization_id,
                                background_tasks=background_tasks
                            )
                            log.info("✅ 超级脚本执行完成，直接获得最终图表配置")
                            
                            # 超级脚本模式直接返回结果，无需数据注入
                            return create_moe_result(
                                success=True,
                                render_type='echarts',
                                title=expert_input.display_instructions.get('title', '数据图表'),
                                content=chart_config,  # 直接使用脚本生成的配置
                                description=plan.description,
                                debug_info={
                                    "mode": "super_script",
                                    "original_df_shape": processed_df.shape,
                                    "original_df_columns": list(processed_df.columns)
                                }
                            )
                        except Exception as e:
                            log.error(f"🚫 超级脚本执行失败: {str(e)}")
                            return create_moe_result(
                                success=False,
                                error=f"超级脚本执行失败: {str(e)}"
                            )
                    else:
                        log.info("🐍 传统模式：执行数据转换脚本...")
                        try:
                            # 执行脚本，得到最终的DataFrame，传递原始提示词上下文
                            processed_df = await self._execute_python_code_transformer(
                                processed_df, result.python_code_transformer, analysis_id,
                                original_prompts=result.original_prompts,
                                user_id=user_id,
                                project_id=project_id,
                                organization_id=organization_id,
                                background_tasks=background_tasks
                            )
                            log.info(f"✅ 数据转换完成，最终数据形状: {processed_df.shape}")
                            log.info(f"✅ 最终数据列名: {list(processed_df.columns)}")
                            
                            # 重新生成数据写真（因为数据已经被转换）
                            log.info("🔄 重新生成数据写真...")
                            data_profile = self.data_profiler.generate_profile(
                                df=processed_df,
                                context={
                                    "regenerated_after_transformation": True,
                                    "original_shape": f"{processed_df.shape}",
                                    "transformation_applied": "echarts_expert_final_implementation"
                                }
                            )
                            log.info(f"✅ 重新生成数据写真完成，列数: {len(data_profile.get('column_profiles', {}))}")
                        except Exception as e:
                            log.error(f"🚫 数据转换失败: {str(e)}")
                            # 不再降级，直接返回失败结果
                            return create_moe_result(
                                success=False,
                                error=f"数据转换失败: {str(e)}"
                            )
                else:
                    # 如果专家没生成脚本，则使用原始数据
                    if use_super_script:
                        log.info("ℹ️ 超级脚本专家未生成脚本，返回失败")
                        return create_moe_result(
                            success=False,
                            error="超级脚本专家未生成脚本"
                        )
                    else:
                        log.info("ℹ️ ECharts专家未生成数据转换脚本，使用原始数据")
                
                if result.success:
                    # BUG修复: 添加数据注入步骤，将处理后的数据填充到图表配置中
                    # 修复策略: 在返回结果前调用数据注入方法
                    # 影响范围: app/services/chart_generation/agent.py:1057-1063
                    # 修复日期: 2025-01-29
                    
                    # 🔍 DEBUG: 确认使用的数据是否正确
                    log.info(f"🔍 【数据注入前检查】processed_df形状: {processed_df.shape}")
                    log.info(f"🔍 【数据注入前检查】processed_df列名: {list(processed_df.columns)}")
                    if not processed_df.empty:
                        log.info(f"🔍 【数据注入前检查】前2行数据样本:")
                        sample = processed_df.head(2).to_dict('records')
                        for i, record in enumerate(sample):
                            log.info(f"    样本{i+1}: {record}")
                    
                    # 注入处理后的数据到图表配置
                    log.info("🔄 开始将处理后的数据注入到图表配置中...")
                    final_chart_config = self._inject_data_to_echarts_config(
                        config_template=result.chart_config,
                        df=processed_df,
                        chart_type=getattr(plan.display_instructions, 'chart_type', 'line')
                    )
                    log.info("✅ 数据注入完成")
                    
                    return create_moe_result(
                        success=True,
                        render_type='echarts',
                        title=expert_input.display_instructions.get('title', '数据图表'),
                        content=final_chart_config,  # 使用注入数据后的配置
                        description=plan.description,
                        debug_info={
                            "processed_df": processed_df,  # 🔥 关键修复：返回处理后的DataFrame
                            "df_shape": processed_df.shape,
                            "df_columns": list(processed_df.columns)
                        }
                    )
                else:
                    return create_moe_result(
                        success=False,
                        error=result.error
                    )
                    
            elif plan.display_type == "table":
                log.info("📋 调用表格专家（暂时使用原方法）")
                
                # 表格专家暂时使用原方法，后续可以扩展
                result = await self.table_expert.generate_table_html(
                    expert_input=expert_input,
                    analysis_id=analysis_id,
                    user_id=user_id,
                    project_id=project_id,
                    organization_id=organization_id,
                    background_tasks=background_tasks
                )

                if result.success:
                    return create_moe_result(
                        success=True,
                        render_type='table',
                        title=expert_input.display_instructions.get('title', '数据表格'),
                        content=result.html_content,
                        description=plan.description,
                        debug_info={
                            "processed_df": processed_df,  # 🔥 关键修复：返回处理后的DataFrame
                            "df_shape": processed_df.shape,
                            "df_columns": list(processed_df.columns)
                        }
                    )
                else:
                    return create_moe_result(
                        success=False,
                        error=result.error
                    )
            else:
                log.error(f"❌ 不支持的显示类型: {plan.display_type}")
                return create_moe_result(
                    success=False,
                    error=f"不支持的显示类型: {plan.display_type}"
                )

        except Exception as e:
            log.error(f"❌ 专家生成（带指导）失败: {str(e)}")
            import traceback
            log.error(f"异常堆栈: {traceback.format_exc()}")
            return create_moe_result(
                success=False,
                error=f"专家生成异常: {str(e)}"
            )

    async def _generate_multi_chart_event_data(
        self,
        chart_results: List[MultiChartResult],
        step_id: str,
        analysis_id: str,
        event_callback: Optional[Callable],
        user_language: str
    ) -> Dict[str, Any]:
        """
        生成多图表事件数据并推送事件 - 统一格式

        Args:
            chart_results: 图表结果列表
            step_id: 步骤ID
            analysis_id: 分析ID
            event_callback: 事件回调
            user_language: 用户语言

        Returns:
            Dict[str, Any]: 多图表事件数据
        """
        try:
            # 生成专用的多图表步骤ID
            multi_chart_step_id = f"moe_chart_step_{step_id}"

            # BUG修复: 添加调试日志查看chart_results是否包含重复数据 
            log.info(f"🔍 开始构建图表数据，输入chart_results数量: {len(chart_results)}")
            for i, chart in enumerate(chart_results):
                log.info(f"   chart_results[{i}]: id={chart.chart_id}, plan_id={chart.plan_id}, title={chart.title}")
            
            # 检查是否有重复的chart_id
            chart_ids = [chart.chart_id for chart in chart_results]
            duplicate_ids = [chart_id for chart_id in chart_ids if chart_ids.count(chart_id) > 1]
            if duplicate_ids:
                log.error(f"🚨 检测到输入中重复的chart_id: {duplicate_ids}")

            # 🔥 构建统一的图表数据列表
            charts_data = []
            for chart in chart_results:
                if chart.chart_type == "echarts":
                    # BUG修复: 简化ECharts处理逻辑，避免重复
                    # 不管config是什么格式，都统一按单个图表处理
                    chart_data = {
                        "chart_id": chart.chart_id,
                        "plan_id": chart.plan_id,
                        "title": chart.title,
                        "chart_type": "echarts",
                        "config": chart.config,  # 保持原始config格式
                        "description": chart.description,
                        "priority": chart.priority
                    }
                    charts_data.append(chart_data)
                    log.info(f"✅ 添加ECharts图表: {chart.chart_id} - {chart.title}")
                    
                else:  # table
                    # 🔥 处理表格配置，统一使用 config 字段
                    try:
                        table_config = json.loads(chart.config) if isinstance(chart.config, str) else chart.config
                    except json.JSONDecodeError:
                        log.error(f"表格配置JSON解析失败: {chart.chart_id}")
                        table_config = {"error": "配置解析失败"}

                    # 🔥 表格配置验证：确保包含必要的columns字段
                    if not isinstance(table_config, dict) or not table_config.get('columns'):
                        log.error(f"MoE表格配置缺少columns字段: {chart.title}")
                        # 跳过无效的表格配置
                        continue

                    chart_data = {
                        "chart_id": chart.chart_id,
                        "plan_id": chart.plan_id,
                        "title": chart.title,
                        "chart_type": "table",
                        "config": table_config,  # 🔥 统一使用 config 字段
                        "description": chart.description,
                        "priority": chart.priority
                    }
                    
                    # 🔥 调试：打印MoE表格配置结构
                    log.info(f"🔍 MoE表格配置验证: {chart_data['title']}")
                    log.info(f"   - 包含columns: {'columns' in table_config}")
                    log.info(f"   - 包含dataSource: {'dataSource' in table_config}")
                    log.info(f"   - columns数量: {len(table_config.get('columns', []))}")
                    log.info(f"   - dataSource数量: {len(table_config.get('dataSource', []))}")

                    charts_data.append(chart_data)

            # 按优先级排序
            charts_data.sort(key=lambda x: x.get('priority', 999))
            
            # BUG修复: 添加调试日志查看最终生成的图表数据
            log.info(f"🔍 图表数据构建完成，charts_data数量: {len(charts_data)}")
            for i, chart_data in enumerate(charts_data):
                log.info(f"   charts_data[{i}]: id={chart_data['chart_id']}, plan_id={chart_data['plan_id']}, title={chart_data['title']}")
            
            # 检查最终结果中是否有重复的chart_id
            final_chart_ids = [chart_data['chart_id'] for chart_data in charts_data]
            final_duplicate_ids = [chart_id for chart_id in final_chart_ids if final_chart_ids.count(chart_id) > 1]
            if final_duplicate_ids:
                log.error(f"🚨 检测到最终结果中重复的chart_id: {final_duplicate_ids}")
                # 去重处理：保留第一个，移除后续重复项
                seen_ids = set()
                unique_charts_data = []
                for chart_data in charts_data:
                    if chart_data['chart_id'] not in seen_ids:
                        unique_charts_data.append(chart_data)
                        seen_ids.add(chart_data['chart_id'])
                    else:
                        log.warning(f"🔄 移除重复的图表: {chart_data['chart_id']}")
                
                charts_data = unique_charts_data
                log.info(f"✅ 去重后图表数量: {len(charts_data)}")

            # 🔥 统一的多图表格式输出（与Simple Agent完全一致）
            multi_chart_event_data = {
                "message": f"智能图表生成完成，共生成 {len(charts_data)} 个可视化",
                "step_id": multi_chart_step_id,
                "analysis_id": analysis_id,
                "chart_data": {
                    "display_format": "multi_chart",  # 🔥 统一使用多图表格式
                    "data": {
                        "total_charts": len(charts_data),
                        "layout": "grid",
                        "charts": charts_data,
                        "auto_generated": True,
                        "generated_by": "moe_agent"  # 🔥 统一的生成标识字段
                    }
                }
            }

            # 🔥 不在这里发送事件，统一在process_tool_result的最后处理

            # 🔥 返回前进行结构验证
            validation_result = self._validate_multi_chart_event_data(multi_chart_event_data)
            if not validation_result['valid']:
                log.error(f"❌ MoE Agent返回数据验证失败: {validation_result['error']}")
                return None
                
            return multi_chart_event_data

        except Exception as e:
            log.error(f"生成多图表事件数据失败: {str(e)}")
            return {
                "message": "多图表生成失败",
                "step_id": f"moe_chart_step_{step_id}",
                "analysis_id": analysis_id,
                "error": str(e)
            }

    # 🔥 已废弃：统一使用多图表格式，不再需要单图表处理
    # async def _generate_event_data(...) 方法已被 _generate_multi_chart_event_data 替代
    # 所有输出现在都统一使用 multi_chart 格式，简化前端处理逻辑

    async def _get_organization_id(self, user_id: Optional[str], project_id: Optional[str]) -> Optional[int]:
        """获取组织ID用于Token统计"""
        try:
            if user_id and project_id:
                from app.models.project import Project
                project = self.db.query(Project).filter(Project.id == project_id).first()
                if project:
                    return project.org_id
        except Exception as e:
            log.warning(f"获取组织ID失败，将跳过Token统计: {str(e)}")
        return None

    async def _record_token_usage(
        self,
        completion,
        analysis_id: Optional[str],
        user_id: Optional[str],
        project_id: Optional[str],
        organization_id: Optional[int],
        background_tasks
    ):
        """记录Token使用量"""
        try:
            usage_data = completion.usage.model_dump() if completion.usage else None
            if usage_data and self.model_id and organization_id and background_tasks:
                prompt_tokens = usage_data.get('prompt_tokens', 0)
                completion_tokens = usage_data.get('completion_tokens', 0)
                total_tokens = usage_data.get('total_tokens', 0)

                from app.services.token_usage_service import record_usage_task
                background_tasks.add_task(
                    record_usage_task,
                    user_id=user_id,
                    org_id=organization_id,
                    project_id=project_id,
                    analysis_id=analysis_id,
                    model_id=self.model_id,
                    model_name=self.model,
                    prompt_tokens=prompt_tokens,
                    completion_tokens=completion_tokens,
                    total_tokens=total_tokens
                )
        except Exception as e:
            log.warning(f"记录Token使用量失败: {str(e)}")

    def _extract_json_from_response(self, response_content: str) -> Optional[Dict[str, Any]]:
        """从LLM文本响应中提取JSON内容"""
        try:
            # 首先尝试直接解析整个响应为JSON
            try:
                result = json.loads(response_content)
                log.info("直接JSON解析成功")
                return result
            except json.JSONDecodeError:
                log.info("直接JSON解析失败，尝试提取JSON代码块")

            # 查找并提取JSON代码块
            json_start = -1
            json_end = -1

            # 查找 ```json 代码块
            if "```json" in response_content:
                json_start = response_content.find("```json") + 7
                json_end = response_content.find("```", json_start)
            # 查找 ``` 代码块（可能没有指定语言）
            elif "```" in response_content:
                first_triple = response_content.find("```")
                content_after_first = response_content[first_triple + 3:]
                newline_pos = content_after_first.find('\n')
                if newline_pos != -1:
                    json_start = first_triple + 3 + newline_pos + 1
                else:
                    json_start = first_triple + 3
                json_end = response_content.find("```", json_start)
            # 查找第一个 { 到最后一个 }
            else:
                json_start = response_content.find("{")
                json_end = response_content.rfind("}") + 1

            if json_start >= 0 and json_end > json_start:
                json_str = response_content[json_start:json_end].strip()

                try:
                    result = json.loads(json_str)
                    return result
                except json.JSONDecodeError as e:
                    log.warning(f"提取的JSON解析失败: {str(e)}")
                    log.warning(f"提取的JSON内容: {json_str[:500]}...")

            # 如果所有方法都失败，返回None
            log.error("无法从响应中提取有效的JSON")
            log.error(f"响应内容: {response_content[:1000]}...")
            return None

        except Exception as e:
            log.error(f"JSON提取过程中发生异常: {str(e)}")
            return None

    async def _inject_data_with_ai_assistance(
        self,
        config_template: Dict[str, Any],
        df: pd.DataFrame,
        plan,
        data_profile: Dict[str, Any],
        analysis_id: Optional[str],
        user_id: Optional[str],
        project_id: Optional[str],
        organization_id: Optional[int],
        background_tasks,
        user_language: str
    ) -> Dict[str, Any]:
        """
        AI驱动的智能数据注入：让模型分析数据并进行最佳转换

        Args:
            config_template: ECharts配置模板
            df: 原始DataFrame
            plan: 可视化规划
            data_profile: 数据写真
            其他参数: LLM调用所需参数

        Returns:
            Dict[str, Any]: 注入优化数据后的配置
        """
        try:
            # 获取图表类型
            chart_type = getattr(plan.display_instructions, 'chart_type', 'unknown')
            log.info(f"🤖 开始AI驱动的数据注入，图表类型: {chart_type}")

            # 构建数据转换提示词
            transformation_prompt = self._build_data_transformation_prompt(
                config_template, df, plan, data_profile, user_language
            )

            # 调用LLM进行数据转换分析
            llm_service = get_llm_service(self.db)
            messages = [
                {"role": "system", "content": transformation_prompt["system"]},
                {"role": "user", "content": transformation_prompt["user"]}
            ]
            response = await llm_service.call_llm(messages)

            if not response:
                log.error("❌ LLM数据转换分析失败")
                return self._inject_data_to_echarts_config(config_template, df, chart_type)

            # 解析LLM返回的转换指令
            transformation_result = self._parse_transformation_response(response)

            if not transformation_result:
                log.error("❌ 转换指令解析失败")
                return self._inject_data_to_echarts_config(config_template, df, chart_type)

            # 执行数据转换
            transformed_data = self._execute_data_transformation(df, transformation_result, chart_type)

            # 注入转换后的数据
            final_config = self._inject_transformed_data(config_template, transformed_data)

            log.info(f"✅ AI驱动数据注入完成，转换了 {len(transformed_data)} 条记录")
            return final_config

        except Exception as e:
            log.error(f"❌ AI驱动数据注入失败: {str(e)}")
            import traceback
            log.error(f"❌ 异常详情: {traceback.format_exc()}")
            # 降级到原始方法
            return self._inject_data_to_echarts_config(config_template, df, chart_type)

    def _build_data_transformation_prompt(
        self,
        config_template: Dict[str, Any],
        df: pd.DataFrame,
        plan,
        data_profile: Dict[str, Any],
        user_language: str
    ) -> Dict[str, str]:
        """构建数据转换分析提示词"""

        # 获取数据样本
        data_sample = df.head(10).to_dict('records')
        data_columns = df.columns.tolist()
        data_shape = df.shape

        # 获取图表配置信息
        chart_type = getattr(plan.display_instructions, 'chart_type', 'unknown')
        data_mapping = getattr(plan.display_instructions, 'data_mapping', {})

        if user_language == 'en-US':
            system_prompt = f"""You are a data transformation expert specializing in preparing data for ECharts visualizations.

Your task is to analyze the provided data and chart configuration, then determine the optimal data transformation strategy.

## Chart Information:
- Chart Type: {chart_type}
- Plan ID: {plan.plan_id}
- Description: {plan.description}
- Data Mapping: {data_mapping}

## Data Information:
- Shape: {data_shape[0]} rows, {data_shape[1]} columns
- Columns: {data_columns}
- Data Profile: {data_profile.get('summary', 'Not available')}

## Your Analysis Should Include:
1. **Data Quality Issues**: Identify problematic values (like "0E-10", nulls, duplicates)
2. **Column Mapping**: Determine which columns should be used for x-axis, y-axis, series grouping
3. **Data Aggregation**: Decide if data needs grouping, summing, averaging
4. **Data Filtering**: Identify rows that should be excluded
5. **Data Transformation**: Specify any data type conversions or calculations needed

## Output Format:
Return a JSON object with transformation instructions:
```json
{{
  "data_quality_issues": ["issue1", "issue2"],
  "column_mapping": {{
    "x_axis": "column_name",
    "y_axis": "column_name",
    "series_group": "column_name_or_null"
  }},
  "filters": [
    {{"column": "column_name", "condition": "!=", "value": "0E-10"}},
    {{"column": "column_name", "condition": "notnull"}}
  ],
  "aggregation": {{
    "group_by": ["column1", "column2"],
    "agg_functions": {{"column3": "sum", "column4": "mean"}}
  }},
  "transformations": [
    {{"column": "column_name", "operation": "to_numeric"}},
    {{"column": "date_column", "operation": "to_datetime"}}
  ],
  "final_columns": ["col1", "col2", "col3"],
  "reasoning": "Explanation of transformation decisions"
}}
```"""
        else:
            system_prompt = f"""你是数据转换专家，专门为ECharts可视化准备数据。

你的任务是分析提供的数据和图表配置，然后确定最佳的数据转换策略。

## 图表信息:
- 图表类型: {chart_type}
- 规划ID: {plan.plan_id}
- 描述: {plan.description}
- 数据映射: {data_mapping}

## 数据信息:
- 形状: {data_shape[0]} 行, {data_shape[1]} 列
- 列名: {data_columns}
- 数据概况: {data_profile.get('summary', '不可用')}

## 你的分析应包括:
1. **数据质量问题**: 识别问题值（如"0E-10"、空值、重复值）
2. **列映射**: 确定哪些列应用于x轴、y轴、系列分组
3. **数据聚合**: 决定是否需要分组、求和、平均
4. **数据过滤**: 识别应排除的行
5. **数据转换**: 指定任何数据类型转换或计算

## 输出格式:
返回包含转换指令的JSON对象:
```json
{{
  "data_quality_issues": ["问题1", "问题2"],
  "column_mapping": {{
    "x_axis": "列名",
    "y_axis": "列名",
    "series_group": "列名或null"
  }},
  "filters": [
    {{"column": "列名", "condition": "!=", "value": "0E-10"}},
    {{"column": "列名", "condition": "notnull"}}
  ],
  "aggregation": {{
    "group_by": ["列1", "列2"],
    "agg_functions": {{"列3": "sum", "列4": "mean"}}
  }},
  "transformations": [
    {{"column": "列名", "operation": "to_numeric"}},
    {{"column": "日期列", "operation": "to_datetime"}}
  ],
  "final_columns": ["列1", "列2", "列3"],
  "reasoning": "转换决策的解释"
}}
```"""

        user_prompt = f"""请分析以下数据并提供转换指令:

## 数据样本 (前10行):
```json
{json.dumps(data_sample, ensure_ascii=False, indent=2)}
```

## ECharts配置模板:
```json
{json.dumps(config_template, ensure_ascii=False, indent=2)}
```

请基于图表类型和数据特征，提供最佳的数据转换策略。特别注意处理异常值和确保数据格式符合ECharts的要求。"""

        return {
            "system": system_prompt,
            "user": user_prompt
        }

    def _parse_transformation_response(self, response_content: str) -> Optional[Dict[str, Any]]:
        """解析LLM返回的转换指令"""
        try:
            # 尝试提取JSON
            import re
            json_match = re.search(r'```json\s*(.*?)\s*```', response_content, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                # 如果没有代码块，尝试直接解析
                json_str = response_content.strip()

            transformation_result = json.loads(json_str)
            log.info(f"✅ 转换指令解析成功: {transformation_result.get('reasoning', 'No reasoning provided')}")
            return transformation_result

        except Exception as e:
            log.error(f"❌ 转换指令解析失败: {str(e)}")
            log.error(f"原始响应: {response_content[:500]}...")
            return None

    def _execute_data_transformation(self, df: pd.DataFrame, transformation_result: Dict[str, Any], chart_type: str = None) -> List[Dict]:
        """执行数据转换"""
        try:
            log.info(f"🔄 开始执行数据转换，图表类型: {chart_type}")
            working_df = df.copy()

            # 1. 数据类型转换
            transformations = transformation_result.get('transformations', [])
            for transform in transformations:
                column = transform.get('column')
                operation = transform.get('operation')

                if column in working_df.columns:
                    if operation == 'to_numeric':
                        # 先尝试不强制转换，记录转换失败的情况
                        original_count = len(working_df)
                        working_df[column] = pd.to_numeric(working_df[column], errors='coerce')
                        nan_count = working_df[column].isna().sum()
                        if nan_count > 0:
                            log.warning(f"⚠️ 列 {column} 转换为数值时产生了 {nan_count}/{original_count} 个NaN值")
                    elif operation == 'to_datetime':
                        working_df[column] = pd.to_datetime(working_df[column], errors='coerce')
                    log.info(f"✅ 转换列 {column} 为 {operation}")

            # 2. 数据过滤
            filters = transformation_result.get('filters', [])
            for filter_rule in filters:
                column = filter_rule.get('column')
                condition = filter_rule.get('condition')
                value = filter_rule.get('value')

                if column in working_df.columns:
                    if condition == '!=':
                        working_df = working_df[working_df[column] != value]
                    elif condition == 'notnull':
                        working_df = working_df[working_df[column].notnull()]
                    elif condition == '>':
                        working_df = working_df[working_df[column] > value]
                    elif condition == '<':
                        working_df = working_df[working_df[column] < value]
                    log.info(f"✅ 过滤条件: {column} {condition} {value}")

            # 3. 数据聚合
            aggregation = transformation_result.get('aggregation')
            if aggregation and aggregation.get('group_by'):
                group_by = aggregation['group_by']
                agg_functions = aggregation.get('agg_functions', {})

                # 确保分组列存在
                valid_group_by = [col for col in group_by if col in working_df.columns]
                if valid_group_by and agg_functions:
                    working_df = working_df.groupby(valid_group_by).agg(agg_functions).reset_index()
                    log.info(f"✅ 数据聚合: 按 {valid_group_by} 分组")

            # 4. 选择最终列
            final_columns = transformation_result.get('final_columns', [])
            if final_columns:
                valid_columns = [col for col in final_columns if col in working_df.columns]
                if valid_columns:
                    working_df = working_df[valid_columns]
                    log.info(f"✅ 选择列: {valid_columns}")

            # 5. 转换为records格式，根据图表类型选择合适的数据格式
            result_data = self._safe_dataframe_to_records(working_df, chart_type)
            log.info(f"✅ 数据转换完成，从 {len(df)} 行转换为 {len(result_data)} 行")

            return result_data

        except Exception as e:
            log.error(f"❌ 数据转换执行失败: {str(e)}")
            import traceback
            log.error(f"❌ 异常详情: {traceback.format_exc()}")
            # 降级到原始数据
            return self._safe_dataframe_to_records(df, chart_type)

    def _inject_transformed_data(self, config_template: Dict[str, Any], transformed_data: List[Dict]) -> Dict[str, Any]:
        """将转换后的数据注入到配置中"""
        try:
            import copy
            final_config = copy.deepcopy(config_template)

            # 验证并注入数据
            if "dataset" in final_config:
                final_config["dataset"]["source"] = transformed_data
                log.info("✅ 使用dataset模式注入转换后的数据")
            else:
                # 兼容旧格式
                log.warning("⚠️ 配置未使用dataset模式，尝试兼容处理")
                final_config = self._inject_data_legacy_mode(final_config, transformed_data, "unknown")

            return final_config

        except Exception as e:
            log.error(f"❌ 数据注入失败: {str(e)}")
            return config_template

    async def _execute_python_code_transformer(
        self, 
        df: pd.DataFrame, 
        python_code: str, 
        analysis_id: str,
        original_prompts: Optional[Dict[str, str]] = None,
        user_id: Optional[str] = None,
        project_id: Optional[str] = None,
        organization_id: Optional[int] = None,
        background_tasks = None
    ) -> pd.DataFrame:
        """
        执行python_code_transformer进行数据预处理
        
        Args:
            df: 原始数据DataFrame
            python_code: 要执行的Python代码
            analysis_id: 分析ID（用于日志）
            
        Returns:
            处理后的DataFrame
        """
        try:
            log.info(f"🐍 开始执行python_code_transformer，原始数据形状: {df.shape}")
            log.info(f"📝 执行代码长度: {len(python_code)} 字符")
            

            
            # 准备执行环境
            import copy
            working_df = df.copy()
            
            # 创建安全的执行环境
            safe_globals = {
                '__builtins__': {
                    'len': len,
                    'str': str,
                    'int': int,
                    'float': float,
                    'bool': bool,
                    'list': list,
                    'dict': dict,
                    'tuple': tuple,
                    'set': set,
                    'range': range,
                    'enumerate': enumerate,
                    'zip': zip,
                    'map': map,
                    'filter': filter,
                    'sorted': sorted,
                    'min': min,
                    'max': max,
                    'sum': sum,
                    'abs': abs,
                    'round': round,
                    'print': print,
                    'isinstance': isinstance,  # 添加isinstance函数
                    'ValueError': ValueError,
                    'TypeError': TypeError,
                    'KeyError': KeyError,
                    'IndexError': IndexError,
                    '__import__': __import__  # 允许导入模块
                },
                'pd': pd,
                'pandas': pd,
                'np': np,
                'numpy': np,
                'json': json,
                'datetime': datetime,
                'math': math,
                're': re,
                # BUG修复: 添加Python基本布尔值和None，防止name 'True'/'False'/'None' is not defined错误
                'True': True,
                'False': False,
                'None': None,
                # BUG修复: 添加JavaScript风格布尔值映射，防止name 'true'/'false'/'null' is not defined错误  
                'true': True,
                'false': False,
                'null': None
            }
            
            # 创建局部变量环境
            local_vars = {
                'df': working_df,
                'transformed_df': None
            }
            
            # 执行代码
            log.info("⚡ 执行数据转换代码...")
            exec(python_code, safe_globals, local_vars)
            
            # 获取转换结果
            result_df = local_vars.get('transformed_df')
            
            if result_df is None:
                log.warning("⚠️ python_code_transformer未设置transformed_df，使用原始数据")
                return working_df
            
            if not isinstance(result_df, pd.DataFrame):
                log.warning(f"⚠️ transformed_df不是DataFrame类型: {type(result_df)}，尝试转换")
                # BUG修复: 当返回值是字典时，尝试提取第一个DataFrame值
                # 修复策略: 检查是否为包含DataFrame的字典，如果是则提取第一个DataFrame
                # 影响范围: app/services/chart_generation/agent.py _execute_python_code_transformer方法
                # 修复日期: 2025-01-31
                if isinstance(result_df, dict):
                    # 尝试从字典中提取DataFrame
                    df_values = [v for v in result_df.values() if isinstance(v, pd.DataFrame)]
                    if df_values:
                        result_df = df_values[0]  # 使用第一个DataFrame
                        log.info(f"📊 从字典中提取DataFrame: {result_df.shape}")
                    else:
                        log.error("❌ 字典中没有找到DataFrame，无法继续")
                        raise ValueError("transformed_df返回的字典中不包含DataFrame")
                else:
                    result_df = pd.DataFrame(result_df)
            
            log.info(f"✅ 数据转换成功，从 {df.shape} 转换为 {result_df.shape}")
            log.info(f"📋 转换后列名: {list(result_df.columns)}")
            
            # 验证结果数据
            if result_df.empty:
                log.warning("⚠️ 转换后数据为空，使用原始数据")
                return working_df
            
            return result_df
            
        except Exception as e:
            error_msg = str(e)
            import traceback
            error_traceback = traceback.format_exc()
            log.error(f"❌ python_code_transformer执行失败: {error_msg}")
            log.error(f"❌ 异常详情: {error_traceback}")
            
            # BUG修复: 实现Python代码重试机制
            # 修复策略: 当代码执行失败时，将错误信息发送给LLM重新生成代码
            # 影响范围: app/services/chart_generation/agent.py _execute_python_code_transformer方法
            # 修复日期: 2025-01-31
            
            # 检查是否可以重试（避免无限重试）
            retry_count = getattr(self, '_current_retry_count', 0)
            max_retries = 2  # 最多重试2次
            
            if retry_count < max_retries:
                log.info(f"🔄 尝试重新生成Python代码 (第{retry_count + 1}次重试)")
                self._current_retry_count = retry_count + 1
                
                try:
                    # 调用LLM重新生成代码，使用原始提示词上下文
                    fixed_code = await self._retry_python_code_generation(
                        original_code=python_code,
                        error_message=error_msg,
                        error_traceback=error_traceback,
                        df_info={
                            'shape': df.shape,
                            'columns': list(df.columns),
                            'dtypes': df.dtypes.to_dict(),
                            'sample_data': df.head(3).to_dict('records') if len(df) > 0 else []
                        },
                        original_prompts=original_prompts,
                        analysis_id=analysis_id,
                        user_id=user_id,
                        project_id=project_id,
                        organization_id=organization_id,
                        background_tasks=background_tasks
                    )
                    
                    if fixed_code and fixed_code != python_code:
                        log.info("🔧 获得修复后的代码，开始重新执行...")
                        log.info(f"📝 修复后代码预览: {fixed_code[:200]}{'...' if len(fixed_code) > 200 else ''}")
                        log.info("=" * 50 + " 重试执行开始 " + "=" * 50)
                        
                        # 递归调用，使用修复后的代码，传递原始提示词
                        try:
                            result = await self._execute_python_code_transformer(
                                df, fixed_code, analysis_id, original_prompts,
                                user_id, project_id, organization_id, background_tasks
                            )
                            log.info("=" * 50 + " 重试执行成功 " + "=" * 50)
                            log.info(f"✅ 重试成功！最终数据形状: {result.shape}")
                            return result
                        except Exception as retry_exec_error:
                            log.error("=" * 50 + " 重试执行失败 " + "=" * 50)
                            log.error(f"❌ 重试执行仍然失败: {str(retry_exec_error)}")
                            # 继续抛出异常，让外层重试逻辑处理
                            raise retry_exec_error
                    else:
                        log.warning("⚠️ LLM未能生成有效的修复代码")
                        if not fixed_code:
                            log.warning("   - 修复代码为空")
                        elif fixed_code == python_code:
                            log.warning("   - 修复代码与原代码相同，没有修复")
                        
                except Exception as retry_error:
                    log.error(f"❌ 重试生成代码失败: {str(retry_error)}")
                finally:
                    # 重置重试计数器
                    if hasattr(self, '_current_retry_count'):
                        delattr(self, '_current_retry_count')
            else:
                log.error(f"🚫 已达到最大重试次数({max_retries})，停止重试")
            
            log.error("🚫 Python代码执行失败，拒绝生成图表以确保质量")
            raise RuntimeError(f"数据预处理失败: {error_msg}")

    async def _retry_python_code_generation(
        self,
        original_code: str,
        error_message: str,
        error_traceback: str,
        df_info: Dict[str, Any],
        original_prompts: Optional[Dict[str, str]] = None,
        analysis_id: Optional[str] = None,
        user_id: Optional[str] = None,
        project_id: Optional[str] = None,
        organization_id: Optional[int] = None,
        background_tasks = None
    ) -> Optional[str]:
        """
        当Python代码执行失败时，使用LLM重新生成修复后的代码
        保持原始提示词上下文，只追加错误信息
        
        Args:
            original_code: 原始失败的代码
            error_message: 错误消息
            error_traceback: 错误堆栈
            df_info: DataFrame信息
            original_prompts: 原始提示词上下文
            
        Returns:
            Optional[str]: 修复后的代码，如果修复失败返回None
        """
        try:
            from app.services.llm.openai_service import OpenAIService
            
            # 如果有原始提示词，使用原始上下文进行重试
            if original_prompts and original_prompts.get('system') and original_prompts.get('user'):
                log.info("🔄 使用原始提示词上下文进行重试")
                
                # 在原始用户提示词后追加错误信息
                error_context = f"""

🚨 **代码执行错误，需要修复**:

**执行失败的代码:**
```python
{original_code}
```

**错误信息:**
```
{error_message}
```

**错误堆栈:**
```
{error_traceback}
```

**数据信息:**
- 数据形状: {df_info['shape']}
- 列名: {df_info['columns']}
- 数据类型: {df_info['dtypes']}
- 样本数据: {df_info['sample_data']}

**修复要求:**
请基于上述错误信息，修复Python代码中的问题。重点关注：
1. **变量作用域**: 避免在lambda函数中引用外部变量，所有变量必须在同一作用域内定义
2. **布尔值语法**: 使用Python的True/False，不要使用JavaScript的true/false
3. **变量定义**: 确保所有使用的变量都已正确定义，避免NameError
4. **语法正确**: 确保Python语法完全正确
5. **数据框操作**: 使用正确的pandas操作
6. **输出要求**: 最终必须设置 `transformed_df = 最终结果DataFrame`

**常见错误修复指南:**
- ❌ `name 'true' is not defined` → ✅ 使用 `True`
- ❌ `name 'df_filtered' is not defined` → ✅ 在使用前定义: `df_filtered = df[df['col'] > 0]`
- ❌ lambda函数引用外部变量 → ✅ 使用函数参数或group内部操作

请重新生成完整的修复后的Python代码。"""

                # 使用原始提示词 + 错误上下文
                messages = [
                    {"role": "system", "content": original_prompts['system']},
                    {"role": "user", "content": original_prompts['user'] + error_context}
                ]
                
            else:
                # 如果没有原始提示词，使用简化的重试提示词
                log.warning("⚠️ 没有原始提示词上下文，使用简化重试模式")
                
                retry_prompt = f"""
你是一个Python数据处理专家。以下代码执行时出现了错误，请分析错误原因并提供修复后的代码。

**原始代码:**
```python
{original_code}
```

**错误信息:**
```
{error_message}
```

**错误堆栈:**
```
{error_traceback}
```

**数据信息:**
- 数据形状: {df_info['shape']}
- 列名: {df_info['columns']}
- 数据类型: {df_info['dtypes']}
- 样本数据: {df_info['sample_data']}

**修复要求:**
1. **变量作用域**: 确保所有变量都在正确的作用域内，避免在lambda函数中引用外部变量
2. **布尔值语法**: 使用Python的True/False/None，不要使用JavaScript的true/false/null
3. **变量定义**: 确保所有使用的变量都已正确定义，避免NameError
4. **语法正确**: 确保Python语法完全正确，避免使用无效的语句分隔符
5. **数据框操作**: 使用正确的pandas操作，确保所有列名和方法调用都是有效的
6. **异常处理**: 添加必要的异常处理，确保代码的鲁棒性
7. **输出要求**: 最终必须设置 `transformed_df = 最终结果DataFrame`

**常见错误修复指南:**
- ❌ `name 'true' is not defined` → ✅ 使用 `True`
- ❌ `name 'false' is not defined` → ✅ 使用 `False`
- ❌ `name 'null' is not defined` → ✅ 使用 `None`
- ❌ `name 'df_filtered' is not defined` → ✅ 在使用前定义: `df_filtered = df[df['col'] > 0]`
- ❌ lambda函数引用外部变量 → ✅ 使用函数参数或group内部操作

请只返回修复后的Python代码，不要包含任何解释文字。代码应该能够直接执行。
"""
                
                messages = [{"role": "user", "content": retry_prompt}]

            # 调用LLM生成修复代码，使用与report_generator相同的方式支持token计量
            log.info("🤖 正在调用LLM修复Python代码...")
            
            if hasattr(self.llm_service, 'client'):
                # 使用client方式调用，支持token计量
                completion = await self.llm_service.client.chat.completions.create(
                    model=self.llm_service.model,
                    messages=messages,
                    temperature=0.1  # 低温度确保代码准确性
                )
                
                fixed_code = completion.choices[0].message.content
                usage_data = completion.usage.model_dump() if completion.usage else {}
                
                # 记录token使用量
                if usage_data and self.model_id and organization_id and background_tasks:
                    from app.services.token_usage_service import record_usage_task
                    background_tasks.add_task(
                        record_usage_task,
                        user_id=user_id,
                        org_id=organization_id,
                        project_id=project_id,
                        analysis_id=analysis_id,
                        model_id=self.model_id,
                        model_name=self.model,
                        prompt_tokens=usage_data.get('prompt_tokens', 0),
                        completion_tokens=usage_data.get('completion_tokens', 0),
                        total_tokens=usage_data.get('total_tokens', 0)
                    )
                    log.info(f"📊 重试LLM Token使用: {usage_data.get('total_tokens', 0)} tokens")
                
            else:
                # 回退到call_llm方法
                fixed_code = await self.llm_service.call_llm(messages)
            
            if not fixed_code:
                log.error("❌ LLM修复代码响应为空")
                return None
                
            fixed_code = fixed_code.strip()
            
            # 清理代码（移除可能的markdown标记）
            if fixed_code.startswith('```python'):
                fixed_code = fixed_code[9:]
            if fixed_code.startswith('```'):
                fixed_code = fixed_code[3:]
            if fixed_code.endswith('```'):
                fixed_code = fixed_code[:-3]
            
            fixed_code = fixed_code.strip()
            
            if not fixed_code:
                log.error("❌ LLM返回的修复代码为空")
                return None
                
            log.info(f"🔧 LLM生成的修复代码长度: {len(fixed_code)} 字符")
            log.info(f"🔧 修复代码预览: {fixed_code[:200]}{'...' if len(fixed_code) > 200 else ''}")
            
            return fixed_code
            
        except Exception as e:
            log.error(f"❌ 调用LLM修复代码失败: {str(e)}")
            return None

    def _inject_data_to_echarts_config(
        self,
        config_template: Dict[str, Any],
        df: pd.DataFrame,
        chart_type: str = None
    ) -> Dict[str, Any]:
        """
        将DataFrame数据注入到ECharts配置模板中
        优化数据注入：直接填充dataset.source，不使用复杂的智能注入逗表类型，用于特殊数据格式处理

        Returns:
            Dict[str, Any]: 注入数据后的完整配置
        """
        try:
            # 🔍 DEBUG: 详细检查DataFrame和配置匹配性
            log.info("🔍 【数据注入诊断】开始详细检查...")
            log.info(f"📋 DataFrame形状: {df.shape}")
            log.info(f"📋 DataFrame列名: {list(df.columns)}")
            log.info(f"📋 DataFrame数据类型: {dict(df.dtypes)}")
            
            # 检查配置中期望的字段
            if "dataset" in config_template and "dimensions" in config_template["dataset"]:
                expected_fields = config_template["dataset"]["dimensions"]
                log.info(f"📋 配置期望字段: {expected_fields}")
                
                # 检查字段匹配性
                missing_fields = [field for field in expected_fields if field not in df.columns]
                extra_fields = [field for field in df.columns if field not in expected_fields]
                
                if missing_fields:
                    log.error(f"❌ 配置期望但数据中缺失的字段: {missing_fields}")
                if extra_fields:
                    log.warning(f"⚠️ 数据中存在但配置未使用的字段: {extra_fields}")
                    
                # 打印数据样本用于分析
                if not df.empty:
                    log.info("📋 DataFrame前3行数据样本:")
                    sample_data = df.head(3).to_dict('records')
                    for i, record in enumerate(sample_data):
                        log.info(f"  行{i+1}: {record}")
            
            import copy
            final_config = copy.deepcopy(config_template)

            # 🔥 从配置中检测图表类型（如果未提供）
            if not chart_type:
                chart_type = self._detect_chart_type_from_config(final_config)
                log.info(f"🔍 从配置中检测到图表类型: {chart_type}")

            log.info(f"🎯 开始简单数据注入，图表类型: {chart_type}")

            # 转换DataFrame为records格式，根据图表类型选择合适的数据格式
            data_records = self._safe_dataframe_to_records(df, chart_type)

            # 验证模板是否使用dataset模式
            if "dataset" not in final_config:
                log.error("❌ 配置模板未使用dataset模式，这与重构后的架构不兼容")
                log.error("🔍 ECharts专家应该生成包含dataset字段的配置")
                return config_template
            
            # 简单的数据注入：直接填充dataset.source
            log.info(f"📊 使用dataset模式注入数据，图表类型: {chart_type}")
            
            # 🔥 检查dataset是否为列表（多子图情况）还是字典（单图情况）
            if isinstance(final_config["dataset"], list):
                # 多子图情况：dataset是列表，为每个dataset注入数据
                log.info("🔄 检测到多子图配置，dataset为列表格式")
                for i, dataset in enumerate(final_config["dataset"]):
                    if isinstance(dataset, dict):
                        dataset["source"] = data_records
                        log.info(f"✅ 为dataset[{i}]注入了 {len(data_records)} 条记录")
                    else:
                        log.warning(f"⚠️ dataset[{i}]不是字典格式，跳过数据注入")
            else:
                # 单图情况：dataset是字典，直接注入数据
                log.info("🔄 检测到单图配置，dataset为字典格式")
                final_config["dataset"]["source"] = data_records
                log.info(f"✅ 数据注入完成，注入了 {len(data_records)} 条记录")

            return final_config

        except Exception as e:
            log.error(f"❌ 数据注入失败: {str(e)}")
            import traceback
            log.error(f"❌ 异常详情: {traceback.format_exc()}")
            # 返回原始配置模板
            return config_template

    def _transform_data_for_chart(
        self,
        df: pd.DataFrame,
        chart_type: str,
        data_mapping: Dict[str, Any],
        plan: Any
    ) -> List[Dict]:
        """
        根据图表类型和数据映射转换数据

        Args:
            df: 原始DataFrame
            chart_type: 图表类型
            data_mapping: 数据映射配置
            plan: 可视化规划

        Returns:
            List[Dict]: 转换后的数据
        """
        try:
            log.info(f"🔄 开始数据转换，图表类型: {chart_type}")

            # 根据图表类型选择转换策略
            if chart_type in ['pie', 'doughnut']:
                return self._transform_data_for_pie_chart(df, data_mapping)
            elif chart_type in ['line', 'bar', 'scatter']:
                return self._transform_data_for_xy_chart(df, data_mapping)
            elif chart_type == 'heatmap':
                return self._transform_data_for_heatmap(df, data_mapping)
            else:
                # 默认转换：使用所有数据
                log.info(f"📊 使用默认转换策略，图表类型: {chart_type}")
                return self._safe_dataframe_to_records(df, chart_type)

        except Exception as e:
            log.error(f"❌ 数据转换失败: {str(e)}")
            # 降级到全量数据
            return self._safe_dataframe_to_records(df, chart_type)

    def _transform_data_for_pie_chart(self, df: pd.DataFrame, data_mapping: Dict[str, Any]) -> List[Dict]:
        """饼图数据转换：聚合数据"""
        try:
            category_col = data_mapping.get('category') or data_mapping.get('name')
            value_col = data_mapping.get('value') or data_mapping.get('y_axis')

            if not category_col or not value_col:
                log.warning("⚠️ 饼图缺少必要的数据映射，使用前两列")
                cols = df.columns.tolist()
                category_col = cols[0] if len(cols) > 0 else None
                value_col = cols[1] if len(cols) > 1 else cols[0]

            if category_col not in df.columns or value_col not in df.columns:
                log.error(f"❌ 指定的列不存在: {category_col}, {value_col}")
                return self._safe_dataframe_to_records(df, 'pie')

            # 按分类聚合数据
            aggregated = df.groupby(category_col)[value_col].sum().reset_index()

            # 转换为饼图需要的格式
            pie_data = []
            for _, row in aggregated.iterrows():
                pie_data.append({
                    'name': str(row[category_col]),
                    'value': float(row[value_col])
                })

            log.info(f"🥧 饼图数据转换完成，{len(pie_data)} 个分类")
            return pie_data

        except Exception as e:
            log.error(f"❌ 饼图数据转换失败: {str(e)}")
            return self._safe_dataframe_to_records(df, 'pie')

    def _transform_data_for_xy_chart(self, df: pd.DataFrame, data_mapping: Dict[str, Any]) -> List[Dict]:
        """XY轴图表数据转换：保持时间序列或分类顺序"""
        try:
            x_col = data_mapping.get('x_axis') or data_mapping.get('x')
            y_col = data_mapping.get('y_axis') or data_mapping.get('y')

            if not x_col or not y_col:
                log.warning("⚠️ XY图表缺少必要的数据映射，使用前两列")
                cols = df.columns.tolist()
                x_col = cols[0] if len(cols) > 0 else None
                y_col = cols[1] if len(cols) > 1 else cols[0]

            if x_col not in df.columns or y_col not in df.columns:
                log.error(f"❌ 指定的列不存在: {x_col}, {y_col}")
                return self._safe_dataframe_to_records(df)

            # 选择相关列并排序
            chart_df = df[[x_col, y_col]].copy()

            # 尝试按X轴排序（如果是时间或数值）
            try:
                chart_df = chart_df.sort_values(x_col)
            except:
                log.info("📊 X轴无法排序，保持原始顺序")

            # 转换为records格式
            xy_data = []
            for _, row in chart_df.iterrows():
                xy_data.append({
                    x_col: row[x_col],
                    y_col: row[y_col]
                })

            log.info(f"📈 XY图表数据转换完成，{len(xy_data)} 个数据点")
            return xy_data

        except Exception as e:
            log.error(f"❌ XY图表数据转换失败: {str(e)}")
            return self._safe_dataframe_to_records(df, 'line')

    def _transform_data_for_heatmap(self, df: pd.DataFrame, data_mapping: Dict[str, Any]) -> List[Dict]:
        """热力图数据转换"""
        try:
            # 热力图通常需要三个维度：x, y, value
            x_col = data_mapping.get('x_axis') or data_mapping.get('x')
            y_col = data_mapping.get('y_axis') or data_mapping.get('y')
            value_col = data_mapping.get('value') or data_mapping.get('z')

            if not all([x_col, y_col, value_col]):
                log.warning("⚠️ 热力图缺少必要的数据映射，使用前三列")
                cols = df.columns.tolist()
                x_col = cols[0] if len(cols) > 0 else None
                y_col = cols[1] if len(cols) > 1 else None
                value_col = cols[2] if len(cols) > 2 else cols[0]

            # 验证列存在
            required_cols = [col for col in [x_col, y_col, value_col] if col and col in df.columns]
            if len(required_cols) < 2:
                log.error("❌ 热力图数据列不足")
                return self._safe_dataframe_to_records(df, 'heatmap')

            # 选择相关列
            heatmap_df = df[required_cols].copy()

            log.info(f"🔥 热力图数据转换完成，{len(heatmap_df)} 个数据点")
            return self._safe_dataframe_to_records(heatmap_df, 'heatmap')

        except Exception as e:
            log.error(f"❌ 热力图数据转换失败: {str(e)}")
            return self._safe_dataframe_to_records(df, 'heatmap')

    def _inject_data_legacy_mode(
        self,
        config: Dict[str, Any],
        data: List[Dict],
        chart_type: str
    ) -> Dict[str, Any]:
        """
        兼容模式：直接注入数据到series数组中
        适用于不支持dataset模式的配置
        """
        try:
            import copy
            final_config = copy.deepcopy(config)

            # 检查是否有series配置
            if "series" not in final_config:
                log.error("❌ 配置中未找到series字段")
                return config
                
            series = final_config["series"]
            if isinstance(series, list) and len(series) > 0:
                # 注入到第一个series的data字段
                series[0]["data"] = data
                log.info(f"✅ 兼容模式数据注入完成，注入了 {len(data)} 条记录")
            else:
                log.error("❌ series配置格式不正确")
                return config
                
            return final_config
            
        except Exception as e:
            log.error(f"❌ 兼容模式注入失败: {str(e)}")
            return config



    def _detect_chart_type_from_config(self, config: Dict[str, Any]) -> str:
        """
        从ECharts配置中检测图表类型

        Args:
            config: ECharts配置

        Returns:
            str: 检测到的图表类型
        """
        try:
            # 检查series配置
            series = config.get('series', [])
            if not series:
                return 'unknown'

            # 获取第一个系列的类型
            first_series = series[0] if isinstance(series, list) else series
            if isinstance(first_series, dict):
                chart_type = first_series.get('type', 'unknown')
                log.info(f"🔍 从series.type检测到图表类型: {chart_type}")
                return chart_type

            return 'unknown'

        except Exception as e:
            log.warning(f"⚠️ 图表类型检测失败: {str(e)}")
            return 'unknown'



    def _safe_dataframe_to_records(self, df: pd.DataFrame, chart_type: str = None) -> List[Dict]:
        """
        安全地将DataFrame转换为records，确保数值正确处理
        特别处理NaN值，避免在图表中显示NaN
        🔥 新增：支持特殊图表类型的数据格式处理

        Args:
            df: DataFrame
            chart_type: 图表类型，用于特殊数据格式处理

        Returns:
            List[Dict] | List[List]: 安全的records列表或数组列表（针对特殊图表类型）
        """
        try:
            # 先处理DataFrame中的NaN值
            import numpy as np
            cleaned_df = df.copy()

            # 将NaN值替换为None或合适的默认值
            for col in cleaned_df.columns:
                if cleaned_df[col].dtype in ['float64', 'float32', 'int64', 'int32']:
                    # 数值列：将NaN替换为None，这样在JSON中会变成null
                    cleaned_df[col] = cleaned_df[col].where(pd.notnull(cleaned_df[col]), None)
                elif cleaned_df[col].dtype == 'object':
                    # 对象列：将NaN替换为None
                    cleaned_df[col] = cleaned_df[col].where(pd.notnull(cleaned_df[col]), None)

            # 🔥 特殊图表类型处理
            if chart_type == 'boxplot':
                return self._handle_boxplot_data(cleaned_df)
            elif chart_type in ['candlestick', 'k']:
                return self._handle_candlestick_data(cleaned_df)
            elif chart_type == 'graph':
                return self._handle_graph_data(cleaned_df)
            
            # 默认处理：转换为records
            records = cleaned_df.to_dict('records')

            # 进一步处理可能的序列化问题
            safe_records = []
            for record in records:
                safe_record = {}
                for key, value in record.items():
                    # 检查是否为NaN（额外保险）
                    if pd.isna(value):
                        safe_record[key] = None
                        continue

                    try:
                        # 测试是否可以JSON序列化
                        import json
                        json.dumps(value)
                        safe_record[key] = value
                    except (TypeError, ValueError):
                        # 处理特殊类型
                        if hasattr(value, 'isoformat'):  # datetime对象
                            safe_record[key] = value.isoformat()
                        elif hasattr(value, 'item') and hasattr(value, 'dtype'):  # numpy标量
                            try:
                                converted_value = value.item()
                                # 再次检查是否为NaN
                                if pd.isna(converted_value):
                                    safe_record[key] = None
                                else:
                                    safe_record[key] = converted_value
                            except:
                                safe_record[key] = None
                        elif isinstance(value, (np.integer, np.floating)):
                            # 处理numpy数值类型
                            if np.isnan(value) if isinstance(value, np.floating) else False:
                                safe_record[key] = None
                            else:
                                safe_record[key] = value.item()
                        else:
                            # 其他情况转为字符串，但避免"nan"字符串
                            str_value = str(value)
                            if str_value.lower() in ['nan', 'none', 'null']:
                                safe_record[key] = None
                            else:
                                safe_record[key] = str_value

                safe_records.append(safe_record)

            log.info(f"📊 安全转换完成，处理了 {len(safe_records)} 条记录")
            return safe_records

        except Exception as e:
            log.error(f"DataFrame转换失败: {str(e)}")
            # 返回基本信息，让模型知道有问题
            return [{"error": f"数据转换失败: {str(e)}", "row_count": len(df)}]

    def _handle_boxplot_data(self, df: pd.DataFrame) -> List[List]:
        """
        处理箱线图数据格式
        箱线图需要 [min, Q1, median, Q3, max] 格式的数组数据

        Args:
            df: DataFrame

        Returns:
            List[List]: 箱线图数据数组
        """
        try:
            import numpy as np
            boxplot_data = []
            
            # 检查是否已经是正确的箱线图格式
            if len(df.columns) == 5:
                # 假设列的顺序是 [min, Q1, median, Q3, max]
                for _, row in df.iterrows():
                    # 转换为数值并处理NaN
                    box_values = []
                    for value in row.values:
                        if pd.isna(value):
                            box_values.append(None)
                        else:
                            try:
                                box_values.append(float(value))
                            except (ValueError, TypeError):
                                box_values.append(None)
                    boxplot_data.append(box_values)
            else:
                # 尝试从数值列计算箱线图统计数据
                numeric_cols = df.select_dtypes(include=[np.number]).columns
                
                if len(numeric_cols) > 0:
                    for col in numeric_cols:
                        data = df[col].dropna()
                        if len(data) > 0:
                            q1 = data.quantile(0.25)
                            median = data.quantile(0.5)
                            q3 = data.quantile(0.75)
                            min_val = data.min()
                            max_val = data.max()
                            
                            boxplot_data.append([
                                float(min_val) if not pd.isna(min_val) else None,
                                float(q1) if not pd.isna(q1) else None,
                                float(median) if not pd.isna(median) else None,
                                float(q3) if not pd.isna(q3) else None,
                                float(max_val) if not pd.isna(max_val) else None
                            ])
                else:
                    log.warning("⚠️ 箱线图数据中没有找到数值列")
                    return []

            log.info(f"📦 箱线图数据转换完成，生成了 {len(boxplot_data)} 个箱线图")
            return boxplot_data

        except Exception as e:
            log.error(f"❌ 箱线图数据处理失败: {str(e)}")
            return []

    def _handle_candlestick_data(self, df: pd.DataFrame) -> List[List]:
        """
        处理K线图数据格式
        K线图需要 [open, close, lowest, highest] 格式的数组数据

        Args:
            df: DataFrame

        Returns:
            List[List]: K线图数据数组
        """
        try:
            candlestick_data = []
            
            # 检查是否包含OHLC列
            required_cols = ['open', 'high', 'low', 'close']
            available_cols = [col for col in required_cols if col in df.columns]
            
            if len(available_cols) == 4:
                for _, row in df.iterrows():
                    candlestick_data.append([
                        float(row['open']) if not pd.isna(row['open']) else None,
                        float(row['close']) if not pd.isna(row['close']) else None,
                        float(row['low']) if not pd.isna(row['low']) else None,
                        float(row['high']) if not pd.isna(row['high']) else None
                    ])
            else:
                log.warning(f"⚠️ K线图数据缺少必要列，需要: {required_cols}, 找到: {available_cols}")
                return []

            log.info(f"📈 K线图数据转换完成，生成了 {len(candlestick_data)} 个K线")
            return candlestick_data

        except Exception as e:
            log.error(f"❌ K线图数据处理失败: {str(e)}")
            return []

    def _handle_graph_data(self, df: pd.DataFrame) -> Dict:
        """
        处理关系图数据格式
        关系图需要nodes和links的特殊格式

        Args:
            df: DataFrame

        Returns:
            Dict: 关系图数据结构
        """
        try:
            # 这里需要根据具体的数据结构来处理
            # 暂时返回空结构，避免错误
            log.warning("⚠️ 关系图数据处理暂未实现")
            return {"nodes": [], "links": []}

        except Exception as e:
            log.error(f"❌ 关系图数据处理失败: {str(e)}")
            return {"nodes": [], "links": []}

    async def _cache_charts_to_redis(
        self,
        chart_results: List,
        analysis_id: str,
        step_id: str,
        conversation_context: Optional[Dict[str, Any]]
    ) -> None:
        """
        将生成的图表信息缓存到Redis
        
        功能实现: 提取图表信息并存储到Redis缓存
        实现方案: 使用ChartCacheService批量存储图表
        影响范围: app/services/chart_generation/agent.py _cache_charts_to_redis方法
        实现日期: 2025-01-12

        Args:
            chart_results: 图表结果列表
            analysis_id: 分析ID
            step_id: 步骤ID
            conversation_context: 对话上下文（包含conversation_id等信息）
        """
        try:
            # 从对话上下文中提取conversation_id
            conversation_id = None
            round_number = 1
            
            if conversation_context:
                # 尝试多种方式获取conversation_id
                conversation_id = (
                    conversation_context.get('conversation_id') or
                    conversation_context.get('analysis', {}).get('conversation_id')
                )
                round_number = (
                    conversation_context.get('round_number') or
                    conversation_context.get('analysis', {}).get('round_number') or
                    1
                )
            
            # 如果没有conversation_id，尝试从数据库查询
            if not conversation_id:
                try:
                    from app.models.analysis import Analysis
                    analysis = self.db.query(Analysis).filter(Analysis.id == analysis_id).first()
                    if analysis:
                        conversation_id = analysis.conversation_id
                        round_number = analysis.round_number or 1
                except Exception as e:
                    log.warning(f"从数据库查询conversation_id失败: {str(e)}")
            
            if not conversation_id:
                log.warning("无法获取conversation_id，跳过图表缓存")
                return
            
            # 准备缓存的图表信息列表
            charts_to_cache = []
            
            for chart_result in chart_results:
                try:
                    # 提取图表基本信息
                    chart_info = {
                        "chart_id": getattr(chart_result, 'chart_id', None),
                        "plan_id": getattr(chart_result, 'plan_id', None),
                        "title": getattr(chart_result, 'title', '未命名图表'),
                        "chart_type": getattr(chart_result, 'chart_type', 'echarts'),
                        "description": getattr(chart_result, 'description', ''),
                        "priority": getattr(chart_result, 'priority', 1),
                        "config_summary": self._extract_config_summary(chart_result)
                    }
                    
                    charts_to_cache.append(chart_info)
                    
                except Exception as e:
                    log.warning(f"提取图表信息失败: {str(e)}")
                    continue
            
            if charts_to_cache:
                # 批量存储到Redis缓存
                success_count = chart_cache_service.store_charts_batch(
                    conversation_id=conversation_id,
                    analysis_id=analysis_id,
                    step_id=step_id,
                    round_number=round_number,
                    charts_list=charts_to_cache
                )
                
                log.info(f"🔥 图表缓存完成: {success_count}/{len(charts_to_cache)} 个图表已存储到Redis")
            else:
                log.warning("没有有效的图表信息可缓存")
                
        except Exception as e:
            log.error(f"❌ 缓存图表到Redis失败: {str(e)}")
            # 不抛出异常，避免影响主流程
    
    def _extract_config_summary(self, chart_result) -> Dict[str, Any]:
        """
        提取图表配置的摘要信息（避免存储完整配置）
        
        Args:
            chart_result: 图表结果对象
            
        Returns:
            Dict[str, Any]: 配置摘要
        """
        try:
            config = getattr(chart_result, 'config', {})
            if not isinstance(config, dict):
                return {}
            
            # 只提取关键的配置信息
            summary = {}
            
            # 提取标题信息
            if 'title' in config:
                title_config = config['title']
                if isinstance(title_config, dict):
                    summary['title'] = title_config.get('text')
                elif isinstance(title_config, str):
                    summary['title'] = title_config
            
            # 提取系列类型
            if 'series' in config:
                series = config['series']
                if isinstance(series, list) and len(series) > 0:
                    summary['series_types'] = [s.get('type') for s in series if isinstance(s, dict)]
                elif isinstance(series, dict):
                    summary['series_types'] = [series.get('type')]
            
            # 提取数据维度信息
            if 'dataset' in config:
                dataset = config['dataset']
                if isinstance(dataset, dict) and 'dimensions' in dataset:
                    summary['dimensions'] = dataset['dimensions']
                elif isinstance(dataset, list) and len(dataset) > 0:
                    # 多dataset情况
                    summary['dimensions'] = dataset[0].get('dimensions', []) if isinstance(dataset[0], dict) else []
            
            return summary
            
        except Exception as e:
            log.warning(f"提取配置摘要失败: {str(e)}")
            return {}

    def _validate_multi_chart_event_data(self, multi_chart_event_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证多图表事件数据结构，确保与前端要求匹配
        
        Args:
            multi_chart_event_data: 多图表事件数据
            
        Returns:
            Dict[str, Any]: 验证结果 {'valid': bool, 'error': str}
        """
        try:
            # 检查顶层结构
            required_top_fields = ['message', 'step_id', 'analysis_id', 'chart_data']
            for field in required_top_fields:
                if field not in multi_chart_event_data:
                    return {'valid': False, 'error': f'缺少顶层字段: {field}'}
            
            # 检查chart_data结构
            chart_data = multi_chart_event_data.get('chart_data', {})
            if not isinstance(chart_data, dict):
                return {'valid': False, 'error': 'chart_data必须是字典类型'}
            
            # 检查display_format
            if chart_data.get('display_format') != 'multi_chart':
                return {'valid': False, 'error': 'display_format必须是multi_chart'}
            
            # 检查data结构
            data = chart_data.get('data', {})
            if not isinstance(data, dict):
                return {'valid': False, 'error': 'chart_data.data必须是字典类型'}
            
            # 检查charts数组
            charts = data.get('charts', [])
            if not isinstance(charts, list) or len(charts) == 0:
                return {'valid': False, 'error': 'charts必须是非空数组'}
            
            # 检查每个chart的结构
            for i, chart in enumerate(charts):
                if not isinstance(chart, dict):
                    return {'valid': False, 'error': f'charts[{i}]必须是字典类型'}
                
                # 检查必要字段
                required_chart_fields = ['chart_id', 'title', 'chart_type', 'config']
                for field in required_chart_fields:
                    if field not in chart:
                        return {'valid': False, 'error': f'charts[{i}]缺少字段: {field}'}
                
                # 特别验证表格类型
                if chart.get('chart_type') == 'table':
                    config = chart.get('config', {})
                    if not isinstance(config, dict):
                        return {'valid': False, 'error': f'charts[{i}]的config必须是字典类型'}
                    
                    if 'columns' not in config:
                        return {'valid': False, 'error': f'表格charts[{i}]的config缺少columns字段'}
                    
                    if not isinstance(config['columns'], list):
                        return {'valid': False, 'error': f'表格charts[{i}]的columns必须是数组类型'}
            
            log.info(f"✅ MoE Agent返回数据结构验证通过: {len(charts)}个图表")
            return {'valid': True, 'error': None}
            
        except Exception as e:
            return {'valid': False, 'error': f'验证过程异常: {str(e)}'}

    def _enhance_tool_result_with_chart_status(
        self,
        tool_result: Dict[str, Any],
        chart_status: str,
        chart_count: int,
        generation_method: str,
        message: str
    ) -> None:
        """
        增强工具结果，添加图表生成状态信息，让主Agent感知

        Args:
            tool_result: 原始工具结果（会被直接修改）
            chart_status: 图表生成状态 (success/failed/skipped)
            chart_count: 生成的图表数量
            generation_method: 生成方法 (moe_agent/simple_agent)
            message: 状态消息
        """
        try:
            # 在工具结果中添加图表生成状态信息
            if not isinstance(tool_result, dict):
                log.warning("工具结果不是字典类型，无法增强")
                return

            # 添加图表生成状态信息
            chart_generation_status = {
                "status": chart_status,
                "chart_count": chart_count,
                "generation_method": generation_method,
                "message": message,
                "timestamp": time.time(),
                "agent_type": "chart_generation_agent"
            }

            # 将状态信息添加到工具结果中
            tool_result["_chart_generation_status"] = chart_generation_status

            log.info(f"✅ 工具结果已增强图表生成状态: {chart_status}, 图表数量: {chart_count}")

        except Exception as e:
            log.warning(f"增强工具结果失败，但不影响主流程: {str(e)}")
    
    async def _execute_super_script(
        self, 
        df: pd.DataFrame, 
        python_code: str, 
        analysis_id: str,
        original_prompts: Optional[Dict[str, str]] = None,
        user_id: Optional[str] = None,
        project_id: Optional[str] = None,
        organization_id: Optional[int] = None,
        background_tasks = None
    ) -> Dict[str, Any]:
        """
        执行超级脚本，直接获取ECharts JSON配置
        
        Args:
            df: 原始数据DataFrame
            python_code: 超级脚本代码
            analysis_id: 分析ID（用于日志）
            
        Returns:
            ECharts配置字典
        """
        try:
            log.info(f"🚀 开始执行超级脚本，原始数据形状: {df.shape}")
            log.info(f"📝 脚本长度: {len(python_code)} 字符")
            
            # 准备执行环境
            import copy
            import io
            import sys
            from contextlib import redirect_stdout
            
            working_df = df.copy()
            
            # 创建安全的执行环境
            safe_globals = {
                '__builtins__': {
                    'len': len,
                    'str': str,
                    'int': int,
                    'float': float,
                    'bool': bool,
                    'list': list,
                    'dict': dict,
                    'tuple': tuple,
                    'set': set,
                    'range': range,
                    'enumerate': enumerate,
                    'zip': zip,
                    'map': map,
                    'filter': filter,
                    'sorted': sorted,
                    'min': min,
                    'max': max,
                    'sum': sum,
                    'abs': abs,
                    'round': round,
                    'print': print,
                    'isinstance': isinstance,
                    'ValueError': ValueError,
                    'TypeError': TypeError,
                    'KeyError': KeyError,
                    'IndexError': IndexError,
                    '__import__': __import__
                },
                'pd': pd,
                'pandas': pd,
                'np': np,
                'numpy': np,
                'json': json,
                'datetime': datetime,
                'math': math,
                're': re,
                'True': True,
                'False': False,
                'None': None,
                'true': True,
                'false': False,
                'null': None
            }
            
            # 创建局部变量环境
            local_vars = {
                'df': working_df
            }
            
            # 捕获标准输出
            output_buffer = io.StringIO()
            
            # 执行脚本并捕获输出
            log.info("⚡ 执行超级脚本...")
            with redirect_stdout(output_buffer):
                exec(python_code, safe_globals, local_vars)
            
            # 获取输出的JSON字符串
            json_output = output_buffer.getvalue().strip()
            
            if not json_output:
                raise ValueError("脚本没有输出任何内容")
            
            # 解析JSON配置
            try:
                chart_config = json.loads(json_output)
                log.info("✅ 成功解析脚本输出的JSON配置")
                log.info(f"📊 配置包含的主要字段: {list(chart_config.keys())}")
                
                return chart_config
                
            except json.JSONDecodeError as e:
                log.error(f"❌ JSON解析失败: {str(e)}")
                log.error(f"❌ 脚本输出内容: {json_output[:500]}...")
                raise ValueError(f"脚本输出不是有效的JSON: {str(e)}")
            
        except Exception as e:
            log.error(f"❌ 超级脚本执行失败: {str(e)}")
            import traceback
            log.error(f"异常堆栈: {traceback.format_exc()}")
            raise
