"""
简单图表生成Agent
================
处理小数据量（≤100行）的图表和表格生成
通过LLM规划师决策，支持图表、表格、混合输出
"""

import json
import logging
import time
from typing import Dict, Any, Optional, Callable, List
from datetime import datetime
from sqlalchemy.orm import Session

from app.core.config import settings
from app.services.llm_factory import get_llm_service
from app.core.logger import log
from app.services.chart_cache_service import chart_cache_service

class SimpleChartAgent:
    """简单图表生成Agent - 使用LLM进行数据分析和图表/表格生成"""
    
    def __init__(self, db: Session):
        """初始化简单图表生成Agent
        
        Args:
            db: 数据库会话
        """
        self.db = db
        
        # 获取LLM服务
        self.llm_service = get_llm_service(db)
        
        # 获取LLM客户端
        if hasattr(self.llm_service, 'client'):
            self.llm_client = self.llm_service.client
        else:
            from openai import AsyncOpenAI
            self.llm_client = AsyncOpenAI(
                api_key=settings.OPENAI_API_KEY,
                base_url=settings.OPENAI_BASE_URL
            )
        
        # 获取模型名称
        if hasattr(self.llm_service, 'model'):
            self.model = self.llm_service.model
        else:
            self.model = settings.OPENAI_MODEL

        # 获取模型ID（用于Token统计）
        if hasattr(self.llm_service, 'model_id'):
            self.model_id = self.llm_service.model_id
        else:
            self.model_id = None
            log.warning("LLM服务未提供model_id，Token统计可能无法正常工作")

    async def process_tool_result(
        self,
        analysis_id: str,
        step_id: str,
        tool_name: str,
        tool_result: Dict[str, Any],
        user_query: str,
        schema_info: Optional[str] = None,
        reasoning: str = "",
        sql_statement: Optional[str] = None,
        event_callback: Optional[Callable[[str, Dict[str, Any]], None]] = None,
        user_id: Optional[str] = None,
        project_id: Optional[str] = None,
        organization_id: Optional[int] = None,
        background_tasks = None,
        user_language: str = 'zh-CN',
        analysis_insights = None,
        enhance_tool_result: bool = True,
        previous_charts: Optional[List[Dict[str, Any]]] = None
    ) -> Optional[Dict[str, Any]]:
        """处理工具执行结果，判断是否需要生成图表或表格
        
        Args:
            analysis_id: 分析ID
            step_id: 步骤ID
            tool_name: 工具名称
            tool_result: 工具执行结果
            user_query: 用户查询
            schema_info: 数据库Schema信息
            reasoning: 推理过程
            sql_statement: 执行的SQL语句（可选）
            event_callback: 事件回调函数
            user_id: 用户ID（用于Token统计）
            project_id: 项目ID（用于Token统计）
            organization_id: 组织ID
            background_tasks: FastAPI后台任务对象（用于异步Token记录）
            user_language: 用户语言，用于LLM提示词
            analysis_insights: 分析洞察
            
        Returns:
            Optional[Dict[str, Any]]: 图表生成结果
        """
        try:
            total_start_time = time.time()
            log.info(f"🎨 简单图表生成Agent开始处理工具结果: {tool_name}")
            
            # LLM分析
            llm_error_details = {}
            try:
                chart_result = await self._analyze_with_llm(
                    tool_result=tool_result,
                    user_query=user_query,
                    tool_name=tool_name,
                    reasoning=reasoning,
                    schema_info=schema_info,
                    sql_statement=sql_statement,
                    analysis_insights=analysis_insights,
                    previous_charts=previous_charts,
                    user_language=user_language
                )
            except Exception as e:
                # 捕获LLM调用的详细异常信息
                llm_error_details = {
                    "error_type": type(e).__name__,
                    "error_message": str(e)
                }
                
                # 获取详细的异常堆栈
                import traceback
                llm_error_details["error_traceback"] = traceback.format_exc()

                log.error(f"❌ LLM图表分析调用异常: {llm_error_details['error_type']}: {llm_error_details['error_message']}")
                chart_result = None
            
            # LLM规划师已经决定了是否生成以及生成什么类型（图表或表格）
            if chart_result and chart_result.get("should_generate", False):
                # 统一处理为多图表格式（与MoE Agent保持一致）
                return await self._generate_unified_chart_result(
                    chart_result, step_id, analysis_id, tool_name, event_callback, user_language,
                    tool_result, enhance_tool_result
                )
            else:
                # LLM规划师判断数据不适合可视化，不推送任何事件
                total_end_time = time.time()
                total_duration = total_end_time - total_start_time

                if chart_result is None:
                    # LLM分析完全失败，显示真实的错误信息
                    log.error("=" * 80)
                    log.error(f"❌ 简单图表生成Agent - LLM分析完全失败")
                    log.error(f"📍 步骤ID: {step_id}")
                    log.error(f"🔧 工具名称: {tool_name}")
                    log.error(f"⏱️ 分析总耗时: {total_duration:.2f}秒")

                    if llm_error_details:
                        # 显示真实的错误信息
                        log.error(f"🚨 真实错误信息:")
                        log.error(f"   - 错误类型: {llm_error_details['error_type']}")
                        log.error(f"   - 错误消息: {llm_error_details['error_message']}")
                        
                        # 如果需要，也可以记录完整的堆栈跟踪
                        if log.level <= logging.DEBUG:
                            log.debug(f"   - 完整堆栈: {llm_error_details['error_traceback']}")
                    
                    log.error("=" * 80)
                else:
                    # LLM分析成功但判断不适合生成图表
                    log.info("=" * 80)
                    log.info(f"📊 简单图表生成Agent - 数据不适合可视化")
                    log.info(f"📍 步骤ID: {step_id}")
                    log.info(f"🔧 工具名称: {tool_name}")
                    log.info(f"💡 LLM判断原因: {chart_result.get('reason', '未提供原因')}")
                    log.info(f"⏱️ 分析总耗时: {total_duration:.2f}秒")
                    log.info("=" * 80)

                return None

        except Exception as e:
            log.error(f"❌ 简单图表生成Agent处理失败: {str(e)}")
            import traceback
            log.error(f"❌ 异常详情: {traceback.format_exc()}")
            return None

    async def _analyze_with_llm(
        self,
        tool_result: Dict[str, Any],
        user_query: str,
        tool_name: str,
        reasoning: str,
        schema_info: Optional[str] = None,
        sql_statement: Optional[str] = None,
        analysis_insights = None,
        previous_charts: Optional[List[Dict[str, Any]]] = None,
        user_language: str = 'zh-CN'
    ) -> Optional[Dict[str, Any]]:
        """使用LLM分析工具结果并生成图表/表格配置
        
        Args:
            tool_result: 工具执行结果
            user_query: 用户查询
            tool_name: 工具名称
            reasoning: 推理过程
            schema_info: 数据库Schema信息
            sql_statement: 执行的SQL语句
            
        Returns:
            Optional[Dict[str, Any]]: LLM分析结果
        """
        try:
            log.info("🤖 开始LLM规划师分析...")
            
            # 🔥 详细日志：分析传入的参数
            log.info(f"🔍 Simple Agent 参数检查:")
            log.info(f"   用户查询: {user_query[:100]}...")
            log.info(f"   工具名称: {tool_name}")
            log.info(f"   previous_charts 参数: {type(previous_charts)} (长度: {len(previous_charts) if previous_charts else 0})")
            
            # 构建历史图表部分
            previous_charts_section = ""
            if previous_charts and len(previous_charts) > 0:
                log.info(f"🔍 准备传递给LLM的历史图表信息: {len(previous_charts)} 个")
                for i, chart in enumerate(previous_charts, 1):
                    log.info(f"   历史图表{i}: {chart.get('title', '未命名')} "
                            f"(类型: {chart.get('chart_type', '未知')}, "
                            f"步骤: {chart.get('step_id', '未知')}, "
                            f"轮次: {chart.get('round_number', '未知')}, "
                            f"描述: {chart.get('description', '无描述')[:50]}...)")

                # 打印完整的历史图表JSON（用于调试）
                log.info(f"🔍 完整历史图表JSON: {json.dumps(previous_charts, ensure_ascii=False, indent=2)}")
            else:
                log.info("🔍 没有历史图表信息传递给LLM - 这可能是首次生成或缓存获取失败")

            # 🔥 确保重复检查指令始终存在（无论是否有历史图表）
            if previous_charts and len(previous_charts) > 0:
                # 🔥 增强日志：详细分析历史图表内容
                log.info(f"🔍 Simple Agent分析历史图表内容:")
                for i, chart in enumerate(previous_charts, 1):
                    chart_title = chart.get('title', '未命名')
                    chart_type = chart.get('chart_type', '未知')
                    analysis_id = chart.get('analysis_id', '未知')
                    step_id = chart.get('step_id', '未知')
                    description = chart.get('description', '无描述')
                    log.info(f"   历史图表{i}: {chart_title}")
                    log.info(f"     - 类型: {chart_type}")
                    log.info(f"     - 分析ID: {analysis_id}")
                    log.info(f"     - 步骤ID: {step_id}")
                    log.info(f"     - 描述: {description[:100]}...")

                if user_language == 'en-US':
                    previous_charts_section = """
## Previous Charts in This Conversation

The following charts have already been generated in this conversation:
```json
""" + json.dumps(previous_charts, ensure_ascii=False, indent=2) + """
```

**🚨 CRITICAL: Avoid generating duplicate or similar charts!**
- **MANDATORY DUPLICATE CHECK**: Before deciding to generate any chart, carefully compare the current request with existing charts above
- **STRICT SIMILARITY RULES**: 
  * If a chart with the same analysis goal already exists → choose `should_generate: false`
  * If a chart covers the same data dimensions/time period → choose `should_generate: false`
  * If a chart shows the same metrics or KPIs → choose `should_generate: false`
  * If a chart has the same or very similar title → choose `should_generate: false`
- **ONLY generate new charts when they provide completely NEW insights or significantly different analytical perspectives**
- **When in doubt, choose NOT to generate** - it's better to avoid duplication than to create redundant visualizations
- **Special attention**: Do not create charts with nearly identical titles (e.g., "High-Spend Zero-Purchase Ad Series: Total Spend vs Average CPC Distribution")
- **ZERO TOLERANCE for duplicates**: Even with minor differences, if core analysis content is the same, reject generation
"""
                else:
                    previous_charts_section = """
## 对话历史中的图表记录

本次对话中已生成过以下图表：
```json
""" + json.dumps(previous_charts, ensure_ascii=False, indent=2) + """
```

**🚨 关键：严格避免重复生成相似的图表！**
- **强制重复检查**: 在决定生成任何图表之前，必须仔细对比当前请求与上述已有图表
- **严格相似性规则**:
  * 如果已存在相同分析目标的图表 → 选择 `should_generate: false`
  * 如果已存在覆盖相同数据维度/时间段的图表 → 选择 `should_generate: false`
  * 如果已存在展示相同指标或KPI的图表 → 选择 `should_generate: false`
  * 如果已存在相同标题或极相似描述的图表 → 选择 `should_generate: false`
- **只有在提供完全新的洞察或显著不同的分析角度时才生成新图表**
- **当有疑问时，选择不生成** - 避免重复比创建冗余可视化更好
- **特别注意**: 不要生成标题几乎相同的图表（如"高花费零购买广告系列：总花费与平均CPC分布"）
- **零容忍重复**: 即使是细微的差别，如果核心分析内容相同，也应拒绝生成
"""
            else:
                # 🔥 即使没有历史图表，也要添加基本的重复检查和质量控制指令
                if user_language == 'en-US':
                    previous_charts_section = """
## Chart Generation Guidelines

**🚨 IMPORTANT: Maintain high quality standards!**
- **QUALITY FIRST**: Only generate charts that provide clear, valuable insights
- **AVOID LOW-VALUE VISUALIZATIONS**: Do not create charts just for the sake of having visuals
- **STRICT DATA REQUIREMENTS**: Ensure data is suitable for meaningful visualization
- **STRICT DATA VALIDATION**: Reject generation when data rows<3, std dev=0, unique values=1
- **When in doubt, choose NOT to generate** - it's better to have no chart than a poor quality one
- **DATA QUALITY GATEKEEPER**: As chart generation agent, prioritize visualization value over quantity
- **AGGRESSIVE QUALITY FILTERING**: Apply stringent standards to prevent low-value visualizations
"""
                else:
                    previous_charts_section = """
## 图表生成指导原则

**🚨 重要：始终保持高质量标准！**
- **质量优先**: 只生成能提供清晰、有价值洞察的图表
- **避免低价值可视化**: 不要为了有图表而生成图表
- **严格数据要求**: 确保数据适合进行有意义的可视化
- **严格数据验证**: 数据行数<3、标准差=0、唯一值=1时应拒绝生成
- **当有疑问时，选择不生成** - 宁缺毋滥，没有图表比低质量图表更好
- **数据质量把关**: 作为图表生成代理，优先考虑可视化价值而非数量
- **激进质量过滤**: 应用严格标准防止低价值可视化
"""

            # BUG修复: f-string格式化错误导致Simple Agent失败
            # 修复策略: 将f-string改为字符串连接，避免JSON模板中的大括号冲突
            # 影响范围: app/services/chart_generation/simple_agent.py:255-336
            # 修复日期: 2025-01-12
            
            # 构建系统提示词 - 支持多图表生成
            system_prompt = """你是一个数据可视化专家，专注于生成ECharts图表配置。

🚫 **CRITICAL: 严禁使用Formatter函数** 🚫
- **绝对禁止**: `axisLabel.formatter`、`tooltip.formatter`、`label.formatter` 等所有formatter
- **原因**: formatter会导致鼠标悬浮无法显示数据，造成用户体验问题
- **替代方案**: 使用轴标题添加单位，如 `"yAxis": {"name": "销售额 (万元)"}`
- **保持简洁**: 依赖ECharts默认格式化，避免JavaScript函数错误

## 核心任务：
1. 分析提供的完整数据
2. **专注于生成ECharts图表**，绝不生成表格
3. 直接生成完整的ECharts图表配置
4. **避免重复生成相似的图表**
5. **🔥 严格的数据质量检查 - 拒绝生成无意义的图表**
6. **🚫 严禁使用formatter函数** - 保持配置简洁，避免渲染错误

## 图表类型选择（仅限ECharts）：
- **柱状图**: 分类比较、排名
- **折线图**: 时间趋势、连续变化
- **饼图**: 组成分析（≤7个分类）
- **散点图**: 相关性、关系
- **面积图**: 累积值、堆叠趋势
- **如果数据不适合以上任何图表类型，则不应生成任何内容**

## 多图表策略（直观性优先）：
- **🚨 核心原则：直观性胜过一切** - 宁可生成多个清晰的图表，也不要生成一个混乱的图表
- **取消数量限制**: 根据数据复杂度生成任意数量的图表以确保清晰度
- **数据分组优先**: 如果数据包含多个分组（如不同广告活动），优先生成对比图表（柱状图）
- **优先级排序**: 为对比类图表分配最高优先级（`priority: 1`），为趋势类图表分配次级优先级（`priority: 2+`）
- **避免混合指标**: 不要将数量级差异巨大的指标混在一个图表中
- 每个图表应回答不同的问题

## 🚨 数据质量检查 - 必须严格执行：

**拒绝生成图表的情况**：
1. **数据量不足**：
   - 有效数据行数 < 3 行
   - 大部分字段值为空/None/null

2. **数据无分析价值**：
   - 所有数值字段都为 0 或 None
   - 分类字段全部为 "Unknown"、"未知"、"N/A" 等无效值
   - 所有记录的关键字段值完全相同，无变化趋势

3. **数据质量过低**：
   - 超过 70% 的关键字段为空值
   - 数据明显异常或不合理

4. **🔥 重复分析检测**：
   - 当前数据和分析需求与历史图表高度重叠
   - 已存在几乎相同的可视化结果
   - 无法提供新的洞察或价值

**如果数据不符合生成条件，必须返回**：
```json
{
  "should_generate": false,
  "reason": "数据质量不足：[具体原因]" 或 "检测到重复分析：[重复图表说明]",
  "suggestion": "建议检查数据源或调整查询条件" 或 "建议查看已有图表或换个分析角度"
}
```

## 🚫 禁用Formatter函数（重要）：
- **严禁使用formatter函数**: 禁止使用 `axisLabel.formatter`、`tooltip.formatter`、`label.formatter` 等
- **原因**: formatter容易出错，特别是在dataset模式下会导致tooltip显示异常
- **替代方案**: 使用ECharts默认显示，通过轴标题(name)添加单位说明
- **简洁优先**: 保持配置简洁，依赖ECharts自动格式化

## 关键优势：
- 数据量小，可以直接使用所有数据
- 无需复杂的数据处理
- 直接基于实际数据生成ECharts配置
- 支持多角度分析

""" + previous_charts_section + """

**分析策略指导：**
- 优先考虑最能清晰表达数据特征的图表类型
- 考虑不同的视角：概览vs详情、趋势vs分布、总体vs分类等

**🚨 重要提醒 - 强制执行重复检测：**
- **第一优先级**：如果历史图表中已有相似的可视化，**必须**选择 `should_generate: false`
- **严格标准**：只有在能提供**完全新的洞察**或**显著不同的分析角度**时，才生成新图表
- **宁缺毋滥**：当有任何疑问时，选择不生成，避免重复或冗余的可视化
- **多图表限制**：如果决定生成多图表，确保每个图表都有独特的分析价值，且与历史图表无重叠

**单图表输出格式（必须是ECharts图表）：**
```json
{{
  "should_generate": true,
  "reason": "数据适合生成图表",
  "chart_type": "single",
  "chart_config": {{
    "title": {{"text": "基于实际数据的标题", "left": "center"}},
    "dataset": {{
      "dimensions": ["date_start", "avg_cpc"],
      "source": [
        {{"date_start": "2025-01-01", "avg_cpc": 0.58}},
        {{"date_start": "2025-01-02", "avg_cpc": 0.62}}
      ]
    }},
    "xAxis": {{"type": "category"}},
    "yAxis": {{"type": "value"}},
    "series": [{{"type": "line", "encode": {{"x": "date_start", "y": "avg_cpc"}}}}],
    "tooltip": {{"trigger": "axis"}}
  }}
}}
```

**多图表输出格式（仅限ECharts图表）：**
```json
{{
  "should_generate": true,
  "reason": "数据丰富，适合多角度分析",
  "chart_type": "multi",
  "charts": [
    {{
      "chart_id": "chart_001",
      "title": "总体趋势分析",
      "description": "展示整体数据趋势",
      "priority": 1,
      "chart_type": "echarts",
      "chart_config": {{
        "title": {{"text": "总体趋势", "left": "center"}},
        "dataset": {{"dimensions": ["date", "value"], "source": [...]}},
        "series": [{{"type": "line", "encode": {{"x": "date", "y": "value"}}}}]
      }}
    }},
    {{
      "chart_id": "chart_002",
      "title": "分类对比分析",
      "description": "不同类别的对比",
      "priority": 2,
      "chart_type": "echarts",
      "chart_config": {{
        "title": {{"text": "分类对比", "left": "center"}},
        "dataset": {{"dimensions": ["category", "count"], "source": [...]}},
        "series": [{{"type": "bar", "encode": {{"x": "category", "y": "count"}}}}]
      }}
    }}
  ]
}}
```

**重要要求**：
- **绝不输出任何包含 `chart_type: "table"` 或 `table_config` 的内容**
- **🚫 严禁使用formatter函数**: 禁止使用 `axisLabel.formatter`、`tooltip.formatter`、`label.formatter` 等
- **使用轴标题代替formatter**: 通过 `yAxis.name` 或 `xAxis.name` 添加单位说明，如 `"name": "销售额 (万元)"`
- dataset.source 必须包含完整的实际数据，不能为空数组
- 数值字段必须是数字类型，不能是字符串（如 1.65 而不是 "1.65"）
- 日期字段保持字符串格式
- 直接使用提供的数据，不要留空

**数据类型要求**：
```json
// 正确的数据类型
{{"date_start": "2025-01-01", "avg_cpc": 0.58, "clicks": 1420}}

// 错误的数据类型
{{"date_start": "2025-01-01", "avg_cpc": "0.58", "clicks": "1420"}}
```

**不适合可视化的输出格式：**
```json
{{
  "should_generate": false,
  "reason": "具体原因说明"
}}
```

请严格按照以上格式返回JSON结果。
"""

            # 提取实际数据
            actual_data = self._extract_data_from_tool_result(tool_result)

            # 转换数据类型（确保数值字段是数字类型）
            processed_data = self._convert_data_types(actual_data) if actual_data else []

            # 构建分析洞察信息
            insights_section = ""
            if analysis_insights and (analysis_insights.insights or analysis_insights.key_findings):
                insights_info = []

                # 添加关键洞察
                if analysis_insights.insights:
                    insights_info.append("**关键洞察：**")
                    for insight in analysis_insights.insights[:3]:  # 最多显示3个洞察
                        insights_info.append(f"- {insight.title}: {insight.description}")

                # 添加关键发现
                if analysis_insights.key_findings:
                    insights_info.append("**关键发现：**")
                    for finding in analysis_insights.key_findings[:3]:  # 最多显示3个发现
                        insights_info.append(f"- {finding}")

                # 添加推荐的图表类型（如果有）
                if hasattr(analysis_insights, 'recommended_charts') and analysis_insights.recommended_charts:
                    insights_info.append("**推荐图表类型：**")
                    for chart_type in analysis_insights.recommended_charts:
                        insights_info.append(f"- {chart_type}")

                if insights_info:
                    insights_section = f"""

**分析洞察参考：**
{chr(10).join(insights_info)}

请结合这些洞察来选择最合适的图表类型和配置，突出显示关键发现。
"""

            # 构建用户提示词 - 直接包含所有数据
            user_prompt = f"""
## 用户查询
{user_query}{insights_section}

## 完整数据（共{len(processed_data)}行）
```json
{json.dumps(processed_data, ensure_ascii=False, indent=2)}
```

## 数据库Schema信息
{schema_info if schema_info else "未提供Schema信息"}

## 执行的SQL语句
{sql_statement if sql_statement else "未提供SQL语句"}

请基于以上完整数据生成最佳的可视化配置。数据量较小，可以直接使用所有数据。

**多图表生成指导（直观性优先）：**
- **🚨 核心原则：直观性胜过一切** - 宁可生成多个清晰的图表，也不要生成一个混乱的图表
- **取消数量限制**: 如果数据包含多个分组或指标，生成任意数量的图表以确保清晰度
- **数据分组优先**: 如果数据包含多个分组（如不同广告活动），首先生成对比图表（柱状图）展示总计对比
- **指标分离**: 将数量级差异巨大的指标分离到不同图表中（如花费vs点击vs展示分开显示）
- **优先级排序**: 对比类图表设为priority: 1，趋势类图表设为priority: 2+
- 每个图表应回答不同的问题，避免重复信息
- **清晰度验证**: 确保用户能在几秒钟内理解每个图表的核心信息

**🚨 重要提醒 - 强制执行重复检测：**
- **第一优先级**：如果历史图表中已有相似的可视化，**必须**选择 `should_generate: false`
- **严格标准**：只有在能提供**完全新的洞察**或**显著不同的分析角度**时，才生成新图表
- **宁缺毋滥**：当有任何疑问时，选择不生成，避免重复或冗余的可视化
- **多图表限制**：如果决定生成多图表，确保每个图表都有独特的分析价值，且与历史图表无重叠

**单图表输出格式（必须包含实际数据）：**
```json
{{
  "should_generate": true,
  "reason": "数据适合生成图表",
  "chart_type": "single",
  "chart_config": {{
    "title": {{"text": "基于实际数据的标题", "left": "center"}},
    "dataset": {{
      "dimensions": ["date_start", "avg_cpc"],
      "source": [
        {{"date_start": "2025-01-01", "avg_cpc": 0.58}},
        {{"date_start": "2025-01-02", "avg_cpc": 0.62}}
      ]
    }},
    "xAxis": {{"type": "category"}},
    "yAxis": {{"type": "value"}},
    "series": [{{"type": "line", "encode": {{"x": "date_start", "y": "avg_cpc"}}}}],
    "tooltip": {{"trigger": "axis"}}
  }}
}}
```

**多图表输出格式（推荐用于复杂数据分析）：**
```json
{{
  "should_generate": true,
  "reason": "数据丰富，适合多角度分析",
  "chart_type": "multi",
  "charts": [
    {{
      "chart_id": "chart_001",
      "title": "总体趋势分析",
      "description": "展示整体数据趋势",
      "priority": 1,
      "chart_type": "echarts",
      "chart_config": {{
        "title": {{"text": "总体趋势", "left": "center"}},
        "dataset": {{"dimensions": ["date", "value"], "source": [...]}},
        "series": [{{"type": "line", "encode": {{"x": "date", "y": "value"}}}}]
      }}
    }},
    {{
      "chart_id": "chart_002",
      "title": "分类对比分析",
      "description": "不同类别的对比",
      "priority": 2,
      "chart_type": "echarts",
      "chart_config": {{
        "title": {{"text": "分类对比", "left": "center"}},
        "dataset": {{"dimensions": ["category", "count"], "source": [...]}},
        "series": [{{"type": "bar", "encode": {{"x": "category", "y": "count"}}}}]
      }}
    }},
    {{
      "chart_id": "table_003",
      "title": "详细数据表格",
      "description": "提供完整的数据明细",
      "priority": 3,
      "chart_type": "table",
      "chart_config": {{
        "title": "数据明细表",
        "columns": [
          {{"title": "日期", "dataIndex": "date_start", "key": "date_start"}},
          {{"title": "数值", "dataIndex": "value", "key": "value"}}
        ],
        "dataSource": [
          {{"key": "row_1", "date_start": "2025-01-01", "value": 100}},
          {{"key": "row_2", "date_start": "2025-01-02", "value": 120}}
        ]
      }}
    }}
  ]
}}
```

**表格输出格式（必须包含实际数据）：**
```json
{{
  "should_generate": true,
  "reason": "数据适合表格展示",
  "chart_type": "table",
  "table_config": {{
    "title": "数据表格",
    "columns": [
      {{"title": "日期", "dataIndex": "date_start", "key": "date_start"}},
      {{"title": "平均CPC", "dataIndex": "avg_cpc", "key": "avg_cpc"}}
    ],
    "dataSource": [
      {{"key": "row_1", "date_start": "2025-01-01", "avg_cpc": 0.58}},
      {{"key": "row_2", "date_start": "2025-01-02", "avg_cpc": 0.62}}
    ]
  }}
}}
```

**重要要求**：
- **🚫 严禁使用formatter函数**: 禁止使用 `axisLabel.formatter`、`tooltip.formatter`、`label.formatter` 等
- **使用轴标题代替formatter**: 通过 `yAxis.name` 或 `xAxis.name` 添加单位说明，如 `"name": "销售额 (万元)"`
- dataset.source 必须包含完整的实际数据，不能为空数组
- table_config.dataSource 必须包含完整的实际数据
- 数值字段必须是数字类型，不能是字符串（如 1.65 而不是 "1.65"）
- 日期字段保持字符串格式
- 直接使用提供的数据，不要留空

**数据类型要求**：
```json
// 正确的数据类型
{{"date_start": "2025-01-01", "avg_cpc": 0.58, "clicks": 1420}}

// 错误的数据类型
{{"date_start": "2025-01-01", "avg_cpc": "0.58", "clicks": "1420"}}
```

**不适合可视化的输出格式：**
```json
{{
  "should_generate": false,
  "reason": "具体原因说明"
}}
```

请严格按照以上格式返回JSON结果。
"""

            # 调用LLM
            response = await self.llm_client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.1
            )

            # 解析响应 - 使用专家的提取方法
            response_content = response.choices[0].message.content.strip()
            log.info(f"🔍 LLM原始响应长度: {len(response_content)} 字符")

            # 检查响应是否完整（以}或```结尾）
            is_complete = response_content.endswith('}') or response_content.endswith('```')
            log.info(f"🔍 响应完整性检查: {'✅ 完整' if is_complete else '❌ 可能被截断'}")
            log.info(f"🔍 响应结尾: ...{response_content[-100:]}")

            # 如果需要调试，可以打印完整响应（但默认不打印避免日志过长）
            if not is_complete:
                log.warning("⚠️ 检测到响应可能被截断，记录完整内容用于调试")
                log.warning("🔍 LLM完整响应内容:")
                log.warning("=" * 80)
                log.warning(response_content)
                log.warning("=" * 80)

            # 使用专家的JSON提取方法
            result = self._extract_json_from_response(response_content)

            if result:
                log.info("✅ LLM规划师分析完成")

                # 🔥 详细分析LLM的决策
                should_generate = result.get('should_generate', False)
                reason = result.get('reason', '未提供原因')
                chart_type = result.get('chart_type', '未知')



                log.info(f"🤖 LLM决策结果:")
                log.info(f"   是否生成图表: {'✅ 是' if should_generate else '❌ 否'}")
                log.info(f"   决策原因: {reason}")
                log.info(f"   图表类型: {chart_type}")

                if should_generate:
                    if chart_type == 'multi':
                        charts = result.get('charts', [])
                        log.info(f"   生成图表数量: {len(charts)}")
                        for i, chart in enumerate(charts, 1):
                            log.info(f"     图表{i}: {chart.get('title', '未命名')} (类型: {chart.get('chart_type', '未知')})")
                    else:
                        log.info(f"   单图表标题: {result.get('title', '未命名')}")
                    
                    # 🔥 如果决定生成，记录历史图表情况供参考
                    if previous_charts and len(previous_charts) > 0:
                        log.info(f"📝 LLM决定生成图表，当前存在 {len(previous_charts)} 个历史图表")
                        log.info(f"   LLM应该已经考虑了重复性问题")
                else:
                    log.info(f"🚫 LLM决定不生成图表，原因: {reason}")
                    if previous_charts and len(previous_charts) > 0:
                        log.info(f"   ✅ 正确行为：检测到了 {len(previous_charts)} 个历史图表，避免重复生成")
                    else:
                        log.info(f"   原因分析：数据质量或适用性问题")

                return result
            else:
                log.error("❌ 无法从LLM响应中提取有效的JSON配置")
                return None

        except Exception as e:
            log.error(f"❌ LLM分析失败: {str(e)}")
            return None

    def _extract_data_from_tool_result(self, tool_result: Dict[str, Any]) -> Optional[list]:
        """
        从工具结果中提取实际数据

        Args:
            tool_result: 工具执行结果

        Returns:
            Optional[list]: 提取的数据列表
        """
        try:
            # 尝试从不同的字段中提取数据
            if 'data' in tool_result and isinstance(tool_result['data'], list):
                return tool_result['data']
            elif 'results' in tool_result and isinstance(tool_result['results'], list):
                return tool_result['results']
            elif 'result' in tool_result and isinstance(tool_result['result'], list):
                return tool_result['result']
            elif 'rows' in tool_result and isinstance(tool_result['rows'], list):
                return tool_result['rows']
            elif isinstance(tool_result, list):
                return tool_result
            else:
                log.warning("❌ 无法从工具结果中提取数据列表")
                return None

        except Exception as e:
            log.error(f"❌ 提取数据失败: {str(e)}")
            return None

    def _convert_data_types(self, data: list) -> list:
        """
        转换数据类型，确保数值字段是数字类型

        Args:
            data: 原始数据列表

        Returns:
            list: 类型转换后的数据列表
        """
        try:
            if not data or not isinstance(data, list):
                return data

            converted_data = []

            for row in data:
                if not isinstance(row, dict):
                    converted_data.append(row)
                    continue

                converted_row = {}
                for key, value in row.items():
                    # 尝试转换数值类型
                    if isinstance(value, str):
                        # 尝试转换为数字
                        try:
                            # 先尝试整数
                            if '.' not in value and value.replace('-', '').replace('+', '').isdigit():
                                converted_row[key] = int(value)
                            # 再尝试浮点数
                            elif value.replace('.', '').replace('-', '').replace('+', '').replace('e', '').replace('E', '').isdigit():
                                converted_row[key] = float(value)
                            else:
                                # 保持字符串
                                converted_row[key] = value
                        except (ValueError, TypeError):
                            # 转换失败，保持原值
                            converted_row[key] = value
                    else:
                        # 非字符串类型，保持原值
                        converted_row[key] = value

                converted_data.append(converted_row)

            log.info(f"✅ 数据类型转换完成，处理了 {len(converted_data)} 行数据")
            return converted_data

        except Exception as e:
            log.error(f"❌ 数据类型转换失败: {str(e)}")
            return data  # 转换失败时返回原数据

    def _extract_json_from_response(self, response_content: str) -> Optional[Dict[str, Any]]:
        """
        从LLM响应中提取JSON配置（参考专家的提取方法）

        Args:
            response_content: LLM响应内容

        Returns:
            Optional[Dict[str, Any]]: 提取的JSON配置
        """
        try:
            # 首先尝试直接解析整个响应为JSON
            try:
                result = json.loads(response_content)
                log.info("✅ 直接JSON解析成功")
                return result
            except json.JSONDecodeError:
                log.info("🔍 直接JSON解析失败，尝试提取JSON代码块")

            # 查找并提取JSON代码块
            json_start = -1
            json_end = -1

            # 查找 ```json 代码块
            if "```json" in response_content:
                json_start = response_content.find("```json") + 7
                json_end = response_content.find("```", json_start)
                if json_end == -1:
                    log.error("🔍 找到```json代码块但没有结束标记")
                    log.error(f"🔍 ```json开始位置: {json_start - 7}")
                    log.error(f"🔍 响应结尾内容: ...{response_content[-200:]}")
                    log.error(f"🔍 从```json开始的内容: {response_content[json_start-7:json_start+100]}...")

                    # 尝试应急修复：使用从```json开始到响应结尾的内容
                    log.warning("🔧 尝试应急修复：使用截断的JSON内容")
                    json_str = response_content[json_start:].strip()

                    # 尝试智能补全JSON
                    fixed_json = self._try_fix_incomplete_json(json_str)
                    if fixed_json:
                        try:
                            result = json.loads(fixed_json)
                            log.info("✅ 应急修复成功")
                            return result
                        except json.JSONDecodeError as e:
                            log.error(f"❌ 应急修复失败: {str(e)}")

                    return None
                log.info("🔍 找到```json代码块")
            # 查找 ``` 代码块（可能没有指定语言）
            elif "```" in response_content:
                first_triple = response_content.find("```")
                # 跳过第一个```和可能的语言标识符
                content_after_first = response_content[first_triple + 3:]
                newline_pos = content_after_first.find('\n')
                if newline_pos != -1:
                    json_start = first_triple + 3 + newline_pos + 1
                else:
                    json_start = first_triple + 3
                json_end = response_content.find("```", json_start)
                if json_end == -1:
                    log.error("🔍 找到```代码块但没有结束标记")
                    return None
                log.info("🔍 找到```代码块")
            # 查找第一个 { 到最后一个 }
            else:
                json_start = response_content.find("{")
                json_end = response_content.rfind("}") + 1
                log.info("🔍 查找{}括号范围")

            if json_start >= 0 and json_end > json_start:
                json_str = response_content[json_start:json_end].strip()
                log.info(f"📋 提取的JSON长度: {len(json_str)} 字符")
                log.info(f"📍 JSON提取位置: {json_start} 到 {json_end}")
                log.info(f"📏 原始响应长度: {len(response_content)} 字符")

                try:
                    result = json.loads(json_str)
                    log.info("✅ JSON代码块解析成功")
                    return result
                except json.JSONDecodeError as e:
                    log.error(f"❌ 提取的JSON解析失败: {str(e)}")
                    log.error(f"📍 错误位置: 第{e.lineno}行，第{e.colno}列")
                    log.error(f"📏 提取的JSON总长度: {len(json_str)} 字符")
                    log.error(f"📝 提取的JSON内容预览: {json_str[:500]}...")
                    log.error(f"📝 提取的JSON结尾预览: ...{json_str[-200:]}")
                    # 不再尝试修复，直接返回None

            # 如果所有方法都失败，返回None
            log.error("❌ 无法从响应中提取有效的JSON")
            log.error(f"📝 响应内容预览: {response_content[:1000]}...")
            return None

        except Exception as e:
            log.error(f"❌ JSON提取过程中发生异常: {str(e)}")
            return None

    def _try_fix_incomplete_json(self, json_str: str) -> Optional[str]:
        """
        尝试修复不完整的JSON字符串

        Args:
            json_str: 不完整的JSON字符串

        Returns:
            Optional[str]: 修复后的JSON字符串，如果无法修复则返回None
        """
        try:
            log.info(f"🔧 开始修复不完整的JSON，长度: {len(json_str)} 字符")

            # 1. 清理字符串
            json_str = json_str.strip()

            # 2. 检查是否以{开始
            if not json_str.startswith('{'):
                log.error("❌ JSON不以{开始，无法修复")
                return None

            # 3. 计算括号数量
            open_braces = json_str.count('{')
            close_braces = json_str.count('}')
            open_brackets = json_str.count('[')
            close_brackets = json_str.count(']')

            log.info(f"🔧 括号统计: {{ {open_braces}, }} {close_braces}, [ {open_brackets}, ] {close_brackets}")

            # 4. 处理字符串截断问题
            # 如果最后一个字符不是完整的，尝试删除不完整的部分
            if json_str and json_str[-1] not in ['}', ']', '"', ',', ':', 'e', 'l', 's', 't', 'r', 'u', 'n']:
                # 可能在值的中间被截断，找到最后一个完整的项目
                last_comma = json_str.rfind(',')
                last_colon = json_str.rfind(':')
                last_brace = json_str.rfind('{')
                last_bracket = json_str.rfind('[')

                # 找到最后一个结构性字符的位置
                last_structural = max(last_comma, last_colon, last_brace, last_bracket)

                if last_comma == last_structural and last_comma > last_colon:
                    # 最后一个逗号之后的内容可能是不完整的，删除它
                    json_str = json_str[:last_comma]
                    log.info("🔧 删除了不完整的最后一项")
                elif last_colon == last_structural:
                    # 最后一个冒号之后的值可能不完整，删除整个键值对
                    # 找到这个键的开始位置
                    key_start = json_str.rfind('"', 0, last_colon - 1)
                    if key_start != -1:
                        key_start = json_str.rfind('"', 0, key_start)
                        if key_start != -1:
                            # 找到前一个逗号或开始位置
                            prev_comma = json_str.rfind(',', 0, key_start)
                            if prev_comma != -1:
                                json_str = json_str[:prev_comma]
                            else:
                                # 如果没有前一个逗号，可能是第一个键值对
                                # 找到包含这个键的对象的开始
                                obj_start = json_str.rfind('{', 0, key_start)
                                if obj_start != -1:
                                    json_str = json_str[:obj_start + 1]  # 保留{
                            log.info("🔧 删除了不完整的最后一个键值对")
                elif last_brace == last_structural or last_bracket == last_structural:
                    # 在对象或数组开始后被截断，删除到前一个完整的项目
                    prev_comma = json_str.rfind(',', 0, last_structural)
                    if prev_comma != -1:
                        json_str = json_str[:prev_comma]
                        log.info("🔧 删除了不完整的嵌套结构")
                    else:
                        # 如果没有前一个逗号，保留到结构开始
                        json_str = json_str[:last_structural + 1]
                        log.info("🔧 保留到结构开始位置")

            # 5. 处理未闭合的字符串
            quote_count = 0
            escaped = False
            for char in json_str:
                if char == '\\' and not escaped:
                    escaped = True
                    continue
                if char == '"' and not escaped:
                    quote_count += 1
                escaped = False

            if quote_count % 2 == 1:
                json_str += '"'
                log.info("🔧 添加了闭合引号")

            # 6. 重新计算括号数量并补全
            open_braces = json_str.count('{')
            close_braces = json_str.count('}')
            open_brackets = json_str.count('[')
            close_brackets = json_str.count(']')

            if open_brackets > close_brackets:
                missing_brackets = open_brackets - close_brackets
                json_str += ']' * missing_brackets
                log.info(f"🔧 添加了 {missing_brackets} 个闭合中括号")

            if open_braces > close_braces:
                missing_braces = open_braces - close_braces
                json_str += '}' * missing_braces
                log.info(f"🔧 添加了 {missing_braces} 个闭合大括号")

            log.info(f"🔧 修复完成，最终长度: {len(json_str)} 字符")
            return json_str

        except Exception as e:
            log.error(f"❌ 修复JSON时发生异常: {str(e)}")
            return None







    async def _generate_unified_chart_result(
        self,
        chart_result: Dict[str, Any],
        step_id: str,
        analysis_id: str,
        tool_name: str,
        event_callback: Optional[Callable[[str, Dict[str, Any]], None]],
        user_language: str,
        tool_result: Dict[str, Any] = None,
        enhance_tool_result: bool = True
    ) -> Dict[str, Any]:
        """生成统一的图表结果格式 - 统一使用多图表格式

        Args:
            chart_result: LLM分析结果
            step_id: 步骤ID
            analysis_id: 分析ID
            tool_name: 工具名称
            event_callback: 事件回调函数
            user_language: 用户语言
            tool_result: 原始工具结果
            enhance_tool_result: 是否增强工具结果

        Returns:
            Dict[str, Any]: 统一格式的图表结果
        """
        try:
            # 处理LLM返回的结果
            should_generate = chart_result.get('should_generate', False)
            reason = chart_result.get('reason', '未提供原因')

            if should_generate:
                # 🔥 统一转换为多图表格式，无论原来是什么类型
                chart_type = chart_result.get('chart_type', 'single')

                if chart_type == 'multi':
                    # 多图表：处理每个图表/表格的配置
                    charts = chart_result.get('charts', [])
                    for chart in charts:
                        if chart.get('chart_type') == 'table':
                            # 表格类型，不需要处理JavaScript函数
                            log.info(f"📋 处理表格: {chart.get('title', '未命名表格')}")
                        else:
                            # 图表类型，处理JavaScript函数
                            chart_config = chart.get('chart_config', {})
                            if chart_config:
                                chart['chart_config'] = self._process_chart_functions(chart_config)
                elif chart_type == 'table':
                    # 单表格：转换为多图表格式
                    table_config = chart_result.get('table_config', {})
                    if not table_config:
                        log.error("LLM返回结果中缺少table_config字段")
                        return None

                    # 转换为多图表格式
                    table_title = table_config.get('title', '数据详情表')
                    chart_result['chart_type'] = 'multi'
                    chart_result['charts'] = [{
                        'chart_id': 'table_001',
                        'title': table_title,
                        'description': '基于数据自动生成的表格',
                        'priority': 1,
                        'chart_type': 'table',
                        'chart_config': table_config  # 🔥 统一使用 chart_config 字段
                    }]
                    log.info(f"📋 单表格转换为多图表格式: {table_title}")
                else:
                    # 单图表：转换为多图表格式
                    chart_config = chart_result.get('chart_config', {})
                    if not chart_config:
                        log.error("LLM返回结果中缺少chart_config字段")
                        return None

                    # 处理JavaScript函数
                    chart_config = self._process_chart_functions(chart_config)

                    # 转换为多图表格式
                    chart_title = chart_config.get('title', {}).get('text', '数据分析图表')
                    chart_result['chart_type'] = 'multi'
                    chart_result['charts'] = [{
                        'chart_id': 'chart_001',
                        'title': chart_title,
                        'description': '基于数据自动生成的图表',
                        'priority': 1,
                        'chart_type': 'echarts',
                        'framework': 'echarts',  # 🔥 明确标识使用ECharts框架
                        'chart_config': chart_config  # 🔥 统一使用 chart_config 字段
                    }]

                # 🔥 构建统一的图表数据列表
                charts_data = []
                charts = chart_result.get('charts', [])
                for i, chart in enumerate(charts):
                    chart_config = chart.get('chart_config', {})
                    
                    # 检查是否是表格
                    if chart.get('chart_type') == 'table':
                        # 🔥 表格配置验证：确保包含必要的columns字段
                        if not chart_config.get('columns'):
                            log.error(f"表格配置缺少columns字段: {chart.get('title', '未命名表格')}")
                            continue
                            
                        # 表格数据 - 统一格式
                        chart_data = {
                            "chart_id": chart.get('chart_id', f'table_{i+1:03d}'),
                            "plan_id": chart.get('chart_id', f'plan_{i+1:03d}'),
                            "title": chart.get('title', f'数据表格{i+1}'),
                            "chart_type": "table",
                            "config": chart_config,  # 🔥 统一使用 config 字段
                            "description": chart.get('description', '基于数据自动生成的表格'),
                            "priority": chart.get('priority', i+1)
                        }
                        
                        # 🔥 调试：打印表格配置结构
                        log.info(f"🔍 表格配置验证: {chart_data['title']}")
                        log.info(f"   - 包含columns: {'columns' in chart_config}")
                        log.info(f"   - 包含dataSource: {'dataSource' in chart_config}")
                        log.info(f"   - columns数量: {len(chart_config.get('columns', []))}")
                        log.info(f"   - dataSource数量: {len(chart_config.get('dataSource', []))}")
                        charts_data.append(chart_data)
                        log.info(f"📋 添加表格: {chart_data['title']}")
                    else:
                        # 图表数据 - 统一格式
                        if not self._validate_chart_config(chart_config):
                            log.warning(f"第{i+1}个图表配置验证失败，跳过")
                            continue

                        chart_data = {
                            "chart_id": chart.get('chart_id', f'chart_{i+1:03d}'),
                            "plan_id": chart.get('chart_id', f'plan_{i+1:03d}'),
                            "title": chart.get('title', f'图表{i+1}'),
                            "chart_type": "echarts",
                            "framework": "echarts",  # 🔥 明确标识使用ECharts框架
                            "config": chart_config,  # 🔥 统一使用 config 字段
                            "description": chart.get('description', '基于数据自动生成的图表'),  
                            "priority": chart.get('priority', i+1)
                        }
                        charts_data.append(chart_data)
                        log.info(f"🎨 添加图表: {chart_data['title']}")

                if not charts_data:
                    log.error("没有有效的图表或表格数据")
                    return None

                # 🔥 统一的多图表格式输出
                chart_event_data = {
                    "message": f"智能图表生成完成，共生成 {len(charts_data)} 个可视化",
                    "step_id": f"simple_chart_step_{step_id}",
                    "analysis_id": analysis_id,
                    "chart_data": {
                        "display_format": "multi_chart",  # 🔥 统一使用多图表格式
                        "data": {
                            "total_charts": len(charts_data),
                            "layout": "grid",
                            "charts": charts_data,
                            "auto_generated": True,
                            "generated_by": "simple_agent"  # 🔥 统一的生成标识字段
                        }
                    }
                }

                # 推送事件（保持与MoE Agent一致）
                if event_callback:
                    # 1. 推送图表步骤开始事件
                    from app.services.system_tools import SystemTools
                    tool_display_name = SystemTools.get_tool_name('chart_generation_tool', user_language or 'zh-CN')

                    event_callback("step_started", {
                        "step_id": chart_event_data["step_id"],
                        "tool_name": tool_display_name,
                        "parameters": {
                            "chart_count": len(charts_data),
                            "chart_types": [chart.get('chart_type', 'echarts') for chart in charts_data],
                            "auto_generated": True
                        },
                        "message": "开始智能图表生成" if user_language == 'zh-CN' else "Starting intelligent chart generation"
                    })

                    # 2. 推送图表步骤完成事件
                    step_result = {
                        "message": chart_event_data["message"],
                        "charts": charts_data,
                        "data": chart_event_data["chart_data"]["data"],
                        "display_format": chart_event_data["chart_data"]["display_format"],
                        "auto_generated": True,
                        "generated_by": "simple_agent"
                    }

                    event_callback("step_completed", {
                        "step_id": chart_event_data["step_id"],
                        "tool_name": "智能图表生成",
                        "result": step_result,
                        "reasoning": f"基于简单Agent自动生成{len(charts_data)}个可视化",
                        "message": chart_event_data["message"]
                    })

                    # 3. 推送图表生成事件（用于图表弹框显示）
                    event_callback("chart_generated", {
                        "message": chart_event_data["message"],
                        "step_id": chart_event_data["step_id"],
                        "analysis_id": analysis_id,
                        "chart_data": chart_event_data["chart_data"],
                        "tool_name": "智能图表生成",
                        "generation_method": "simple_agent",
                        "chart_count": len(charts_data),
                        "user_language": user_language
                    })

                # 🔥 增强工具结果，记录成功状态
                if enhance_tool_result:
                    self._enhance_tool_result_with_chart_status(
                        tool_result=tool_result,
                        chart_status="success",
                        chart_count=len(charts_data),
                        generation_method="simple_agent",
                        message=f"智能图表生成完成，共生成 {len(charts_data)} 个可视化"
                    )

                log.info(f"✅ 简单Agent图表生成完成，共生成 {len(charts_data)} 个可视化")

                # 🔥 新增：存储图表到Redis缓存
                await self._cache_charts_to_redis(
                    charts_data=charts_data,
                    analysis_id=analysis_id,
                    step_id=step_id
                )

                # 🔥 返回前进行结构验证
                validation_result = self._validate_chart_event_data(chart_event_data)
                if not validation_result['valid']:
                    log.error(f"❌ Simple Agent返回数据验证失败: {validation_result['error']}")
                    return None
                    
                # 🔥 返回统一的多图表格式
                return chart_event_data

            else:
                log.info(f"📊 LLM判断数据不适合可视化: {reason}")

                # 🔥 增强工具结果，记录跳过状态
                if enhance_tool_result:
                    self._enhance_tool_result_with_chart_status(
                        tool_result=tool_result,
                        chart_status="skipped",
                        chart_count=0,
                        generation_method="simple_agent",
                        message=f"数据不适合可视化: {reason}"
                    )

                return None

        except Exception as e:
            log.error(f"❌ 生成统一图表结果失败: {str(e)}")
            import traceback
            log.error(f"❌ 异常详情: {traceback.format_exc()}")
            return None

    def _process_chart_functions(self, chart_config: Dict[str, Any]) -> Dict[str, Any]:
        """处理图表配置中的JavaScript函数

        Args:
            chart_config: 图表配置

        Returns:
            Dict[str, Any]: 处理后的图表配置
        """
        import copy
        import json

        def process_value(value):
            """递归处理配置值"""
            if isinstance(value, dict):
                if "__function__" in value:
                    # 将__function__格式转换为JavaScript函数字符串
                    return {"__function__": value["__function__"]}
                else:
                    return {k: process_value(v) for k, v in value.items()}
            elif isinstance(value, list):
                return [process_value(item) for item in value]
            else:
                return value

        try:
            processed_config = process_value(copy.deepcopy(chart_config))
            
            # BUG修复: ECharts配置数据类型不匹配导致图表无数据点显示
            # 修复策略: 确保yAxis.data与dataset.source中的数据类型一致，或移除yAxis.data让ECharts自动处理
            # 影响范围: app/services/chart_generation/simple_agent.py:1167-1200
            # 修复日期: 2025-01-12
            processed_config = self._fix_echarts_data_type_consistency(processed_config)
            
            return processed_config
        except Exception as e:
            log.error(f"处理图表函数时出错: {str(e)}")
            return chart_config

    def _fix_echarts_data_type_consistency(self, chart_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        修复ECharts配置中的数据类型一致性问题
        
        主要解决：yAxis.data与dataset.source中数据类型不匹配导致图表无数据点显示的问题
        
        Args:
            chart_config: ECharts图表配置
            
        Returns:
            Dict[str, Any]: 修复后的图表配置
        """
        try:
            if not isinstance(chart_config, dict):
                return chart_config
            
            dataset = chart_config.get('dataset', {})
            yAxis = chart_config.get('yAxis', {})
            
            # 检查是否同时存在dataset和yAxis.data
            if (isinstance(dataset, dict) and 'source' in dataset and 
                isinstance(yAxis, dict) and 'data' in yAxis):
                
                source_data = dataset['source']
                yaxis_data = yAxis['data']
                
                if isinstance(source_data, list) and len(source_data) > 0 and isinstance(yaxis_data, list):
                    # 获取用于y轴映射的字段名
                    y_field = self._get_y_axis_field(chart_config)
                    
                    if y_field and len(source_data) > 0:
                        first_record = source_data[0]
                        if isinstance(first_record, dict) and y_field in first_record:
                            source_value = first_record[y_field]
                            yaxis_first_value = yaxis_data[0] if yaxis_data else None
                            
                            # 检查数据类型是否匹配
                            source_type = type(source_value)
                            yaxis_type = type(yaxis_first_value) if yaxis_first_value is not None else str
                            
                            if source_type != yaxis_type:
                                log.warning(f"🔧 检测到ECharts数据类型不匹配:")
                                log.warning(f"   dataset.source.{y_field}: {source_type.__name__} (值: {source_value})")
                                log.warning(f"   yAxis.data[0]: {yaxis_type.__name__} (值: {yaxis_first_value})")
                                
                                # 修复策略：移除yAxis.data，让ECharts自动从dataset提取
                                # 这是dataset模式的最佳实践，避免数据重复和类型不匹配
                                chart_config = dict(chart_config)  # 创建副本
                                if 'yAxis' in chart_config:
                                    chart_config['yAxis'] = dict(chart_config['yAxis'])
                                    if 'data' in chart_config['yAxis']:
                                        del chart_config['yAxis']['data']
                                        log.info("✅ 已移除yAxis.data，让ECharts自动从dataset提取数据")
                                        log.info("📊 这将确保数据类型一致性并正确显示图表数据点")
            
            return chart_config
            
        except Exception as e:
            log.error(f"❌ 修复ECharts数据类型一致性时出错: {str(e)}")
            return chart_config
    
    def _get_y_axis_field(self, chart_config: Dict[str, Any]) -> Optional[str]:
        """
        获取ECharts配置中用于y轴映射的字段名
        
        Args:
            chart_config: ECharts图表配置
            
        Returns:
            Optional[str]: y轴字段名
        """
        try:
            # 尝试从series的encode中获取y轴字段
            series = chart_config.get('series', [])
            if isinstance(series, list) and len(series) > 0:
                first_series = series[0]
                if isinstance(first_series, dict) and 'encode' in first_series:
                    encode = first_series['encode']
                    if isinstance(encode, dict) and 'y' in encode:
                        return encode['y']
            
            # 如果没有找到encode.y，尝试从dataset.dimensions获取第一个字段
            dataset = chart_config.get('dataset', {})
            if isinstance(dataset, dict) and 'dimensions' in dataset:
                dimensions = dataset['dimensions']
                if isinstance(dimensions, list) and len(dimensions) > 0:
                    return dimensions[0]
            
            return None
            
        except Exception as e:
            log.error(f"❌ 获取y轴字段名时出错: {str(e)}")
            return None

    def _validate_chart_config(self, chart_config: Dict[str, Any]) -> bool:
        """验证图表配置是否有效

        Args:
            chart_config: 图表配置

        Returns:
            bool: 配置是否有效
        """
        try:
            if not isinstance(chart_config, dict):
                return False

            # 检查必要的字段
            if 'series' not in chart_config:
                log.warning("图表配置缺少series字段")
                return False

            series = chart_config['series']
            if not isinstance(series, list) or len(series) == 0:
                log.warning("图表配置的series字段无效")
                return False

            return True

        except Exception as e:
            log.error(f"验证图表配置时出错: {str(e)}")
            return False

    def _enhance_tool_result_with_chart_status(
        self,
        tool_result: Dict[str, Any],
        chart_status: str,
        chart_count: int,
        generation_method: str,
        message: str
    ) -> None:
        """
        增强工具结果，添加图表生成状态信息，让主Agent感知

        Args:
            tool_result: 原始工具结果（会被直接修改）
            chart_status: 图表生成状态 (success/failed/skipped)
            chart_count: 生成的图表数量
            generation_method: 生成方法 (moe_agent/simple_agent)
            message: 状态消息
        """
        try:
            # 在工具结果中添加图表生成状态信息
            if not isinstance(tool_result, dict):
                log.warning("工具结果不是字典类型，无法增强")
                return

            # 添加图表生成状态信息
            chart_generation_status = {
                "status": chart_status,
                "chart_count": chart_count,
                "generation_method": generation_method,
                "message": message,
                "timestamp": time.time(),
                "agent_type": "chart_generation_agent"
            }

            # 将状态信息添加到工具结果中
            tool_result["_chart_generation_status"] = chart_generation_status

            log.info(f"✅ 工具结果已增强图表生成状态: {chart_status}, 图表数量: {chart_count}")

        except Exception as e:
            log.warning(f"增强工具结果失败，但不影响主流程: {str(e)}")

    async def _cache_charts_to_redis(
        self,
        charts_data: List[Dict[str, Any]],
        analysis_id: str,
        step_id: str
    ) -> None:
        """
        将Simple Agent生成的图表信息缓存到Redis
        
        功能实现: 提取图表信息并存储到Redis缓存
        实现方案: 使用ChartCacheService批量存储图表
        影响范围: app/services/chart_generation/simple_agent.py _cache_charts_to_redis方法
        实现日期: 2025-01-12

        Args:
            charts_data: 图表数据列表
            analysis_id: 分析ID
            step_id: 步骤ID
        """
        try:
            # 从数据库查询分析信息获取conversation_id
            conversation_id = None
            round_number = 1
            
            try:
                from app.models.analysis import Analysis
                analysis = self.db.query(Analysis).filter(Analysis.id == analysis_id).first()
                if analysis:
                    conversation_id = analysis.conversation_id
                    round_number = analysis.round_number or 1
            except Exception as e:
                log.warning(f"从数据库查询conversation_id失败: {str(e)}")
            
            if not conversation_id:
                log.warning("无法获取conversation_id，跳过图表缓存")
                return
            
            # 准备缓存的图表信息列表
            charts_to_cache = []
            
            for chart_data in charts_data:
                try:
                    # 提取图表基本信息
                    chart_info = {
                        "chart_id": chart_data.get('chart_id'),
                        "plan_id": chart_data.get('plan_id'),
                        "title": chart_data.get('title', '未命名图表'),
                        "chart_type": chart_data.get('chart_type', 'echarts'),
                        "description": chart_data.get('description', ''),
                        "priority": chart_data.get('priority', 1),
                        "config_summary": self._extract_config_summary_from_chart_data(chart_data)
                    }
                    
                    charts_to_cache.append(chart_info)
                    
                except Exception as e:
                    log.warning(f"提取图表信息失败: {str(e)}")
                    continue
            
            if charts_to_cache:
                # 批量存储到Redis缓存
                success_count = chart_cache_service.store_charts_batch(
                    conversation_id=conversation_id,
                    analysis_id=analysis_id,
                    step_id=step_id,
                    round_number=round_number,
                    charts_list=charts_to_cache
                )
                
                log.info(f"🔥 Simple Agent图表缓存完成: {success_count}/{len(charts_to_cache)} 个图表已存储到Redis")
            else:
                log.warning("没有有效的图表信息可缓存")
                
        except Exception as e:
            log.error(f"❌ Simple Agent缓存图表到Redis失败: {str(e)}")
            # 不抛出异常，避免影响主流程
    
    def _validate_chart_event_data(self, chart_event_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证图表事件数据结构，确保与前端要求匹配
        
        Args:
            chart_event_data: 图表事件数据
            
        Returns:
            Dict[str, Any]: 验证结果 {'valid': bool, 'error': str}
        """
        try:
            # 检查顶层结构
            required_top_fields = ['message', 'step_id', 'analysis_id', 'chart_data']
            for field in required_top_fields:
                if field not in chart_event_data:
                    return {'valid': False, 'error': f'缺少顶层字段: {field}'}
            
            # 检查chart_data结构
            chart_data = chart_event_data.get('chart_data', {})
            if not isinstance(chart_data, dict):
                return {'valid': False, 'error': 'chart_data必须是字典类型'}
            
            # 检查display_format
            if chart_data.get('display_format') != 'multi_chart':
                return {'valid': False, 'error': 'display_format必须是multi_chart'}
            
            # 检查data结构
            data = chart_data.get('data', {})
            if not isinstance(data, dict):
                return {'valid': False, 'error': 'chart_data.data必须是字典类型'}
            
            # 检查charts数组
            charts = data.get('charts', [])
            if not isinstance(charts, list) or len(charts) == 0:
                return {'valid': False, 'error': 'charts必须是非空数组'}
            
            # 检查每个chart的结构
            for i, chart in enumerate(charts):
                if not isinstance(chart, dict):
                    return {'valid': False, 'error': f'charts[{i}]必须是字典类型'}
                
                # 检查必要字段
                required_chart_fields = ['chart_id', 'title', 'chart_type', 'config']
                for field in required_chart_fields:
                    if field not in chart:
                        return {'valid': False, 'error': f'charts[{i}]缺少字段: {field}'}
                
                # 特别验证表格类型
                if chart.get('chart_type') == 'table':
                    config = chart.get('config', {})
                    if not isinstance(config, dict):
                        return {'valid': False, 'error': f'charts[{i}]的config必须是字典类型'}
                    
                    if 'columns' not in config:
                        return {'valid': False, 'error': f'表格charts[{i}]的config缺少columns字段'}
                    
                    if not isinstance(config['columns'], list):
                        return {'valid': False, 'error': f'表格charts[{i}]的columns必须是数组类型'}
            
            log.info(f"✅ Simple Agent返回数据结构验证通过: {len(charts)}个图表")
            return {'valid': True, 'error': None}
            
        except Exception as e:
            return {'valid': False, 'error': f'验证过程异常: {str(e)}'}

    def _extract_config_summary_from_chart_data(self, chart_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        从Simple Agent的图表数据中提取配置摘要
        
        Args:
            chart_data: Simple Agent的图表数据
            
        Returns:
            Dict[str, Any]: 配置摘要
        """
        try:
            summary = {}
            
            # 提取标题信息
            summary['title'] = chart_data.get('title')
            
            # 提取图表类型
            chart_type = chart_data.get('chart_type')
            if chart_type:
                summary['chart_type'] = chart_type
            
            # 从config中提取更多信息
            config = chart_data.get('config', {})
            if isinstance(config, dict):
                # 提取系列类型
                if 'series' in config:
                    series = config['series']
                    if isinstance(series, list) and len(series) > 0:
                        summary['series_types'] = [s.get('type') for s in series if isinstance(s, dict)]
                    elif isinstance(series, dict):
                        summary['series_types'] = [series.get('type')]
                
                # 提取数据维度信息
                if 'dataset' in config:
                    dataset = config['dataset']
                    if isinstance(dataset, dict) and 'dimensions' in dataset:
                        summary['dimensions'] = dataset['dimensions']
            
            return summary
            
        except Exception as e:
            log.warning(f"提取Simple Agent配置摘要失败: {str(e)}")
            return {}
    

