"""
数据摘要生成器
=============
生成数据摘要，用于减少LLM Token消耗
"""

import json
import logging
import pandas as pd
from typing import Dict, Any, List, Optional

# 配置日志
logger = logging.getLogger(__name__)

class DataSummaryGenerator:
    """
    数据摘要生成器
    
    用于生成数据摘要，减少LLM Token消耗
    """
    
    def generate_summary(
        self,
        df: pd.DataFrame,
        visualization_hints: List[str] = None,
        max_samples: int = 10
    ) -> Dict[str, Any]:
        """
        生成数据摘要 - 纯数据处理，让模型知道数据长什么样

        Args:
            df: 处理后的DataFrame
            visualization_hints: 可视化提示
            max_samples: 最大样本数量

        Returns:
            Dict[str, Any]: 纯数据信息摘要
        """
        if visualization_hints is None:
            visualization_hints = []

        # 纯数据信息摘要
        summary = {
            # 概览说明
            "overview": "这是所有数据的完整概览，已对全量数据进行了检查和分析",
            "checks_performed": [
                "字段类型识别",
                "数值范围检查", 
                "空值统计",
                "唯一值计数",
                "数据样本提取"
            ],
            
            # 基础信息
            "total_rows": len(df),
            "total_columns": len(df.columns),
            "columns": list(df.columns),
            "shape": df.shape,
            
            # 字段详情
            "fields": self._get_field_info(df),
            
            # 数据样本
            "samples": self._extract_samples_smart(df, max_samples),
            
            # 兼容性保持
            "schema": self._extract_schema(df),
            "visualization_hints": visualization_hints
        }

        return summary

    def _extract_samples_smart(self, df: pd.DataFrame, max_samples: int) -> List[Dict]:
        """
        智能提取样本数据，最小化序列化处理

        Args:
            df: DataFrame
            max_samples: 最大样本数量

        Returns:
            List[Dict]: 样本数据（保留原始格式，让模型处理）
        """
        if len(df) <= max_samples:
            return self._safe_to_dict(df)

        # 提取头部和尾部样本
        head_count = max_samples // 2
        tail_count = max_samples - head_count

        head_samples = self._safe_to_dict(df.head(head_count))
        tail_samples = self._safe_to_dict(df.tail(tail_count))

        return head_samples + tail_samples

    def _safe_to_dict(self, df: pd.DataFrame) -> List[Dict]:
        """
        安全地将DataFrame转换为字典列表，只处理明显的序列化问题

        Args:
            df: DataFrame

        Returns:
            List[Dict]: 字典列表
        """
        try:
            # 先尝试直接转换
            records = df.to_dict('records')

            # 只处理明显会导致JSON序列化失败的类型
            safe_records = []
            for record in records:
                safe_record = {}
                for key, value in record.items():
                    if pd.isna(value):
                        safe_record[key] = None
                    elif hasattr(value, 'isoformat'):  # datetime对象
                        safe_record[key] = value.isoformat()
                    elif hasattr(value, 'item') and hasattr(value, 'dtype'):  # numpy标量
                        safe_record[key] = value.item()
                    else:
                        safe_record[key] = value
                safe_records.append(safe_record)

            return safe_records

        except Exception as e:
            # 如果转换失败，返回字符串表示
            return [{"error": f"数据转换失败: {str(e)}", "sample_count": len(df)}]

    def _analyze_data_types(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        分析数据类型，为模型提供上下文信息

        Args:
            df: DataFrame

        Returns:
            Dict[str, Any]: 数据类型分析结果
        """
        type_info = {}

        for col in df.columns:
            col_info = {
                "dtype": str(df[col].dtype),
                "null_count": int(df[col].isnull().sum()),
                "unique_count": int(df[col].nunique())
            }

            # 检测可能的时间列
            if df[col].dtype == 'object':
                sample_values = df[col].dropna().head(3).tolist()
                col_info["sample_values"] = [str(v) for v in sample_values]

                # 简单的时间格式检测
                if any(self._looks_like_datetime(str(v)) for v in sample_values):
                    col_info["likely_datetime"] = True

            # 检测数值列的范围
            elif pd.api.types.is_numeric_dtype(df[col]):
                col_info["min_value"] = float(df[col].min()) if not df[col].empty else None
                col_info["max_value"] = float(df[col].max()) if not df[col].empty else None

            type_info[col] = col_info

        return type_info

    def _looks_like_datetime(self, value_str: str) -> bool:
        """
        简单检测字符串是否像时间格式

        Args:
            value_str: 字符串值

        Returns:
            bool: 是否像时间格式
        """
        # 简单的启发式检测
        datetime_indicators = ['-', '/', ':', 'T', ' ', '年', '月', '日']
        return any(indicator in value_str for indicator in datetime_indicators) and len(value_str) > 8

    def _ensure_dataframe_serializable(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        确保DataFrame中的所有数据都是JSON序列化兼容的

        Args:
            df: 原始DataFrame

        Returns:
            pd.DataFrame: JSON兼容的DataFrame
        """
        # 创建DataFrame的副本，避免修改原始数据
        df_copy = df.copy()

        for col in df_copy.columns:
            # 处理datetime类型
            if pd.api.types.is_datetime64_any_dtype(df_copy[col]):
                # 将datetime转换为ISO格式字符串
                df_copy[col] = df_copy[col].dt.strftime('%Y-%m-%dT%H:%M:%S')

            # 处理numpy数值类型
            elif hasattr(df_copy[col].dtype, 'numpy_dtype'):
                # 转换numpy类型为Python原生类型
                df_copy[col] = df_copy[col].astype(object)

            # 处理其他可能的非JSON兼容类型
            elif df_copy[col].dtype == 'object':
                # 对object类型的列进行逐个检查和转换
                df_copy[col] = df_copy[col].apply(self._convert_value_to_serializable)

        return df_copy

    def _convert_value_to_serializable(self, value):
        """
        将单个值转换为JSON序列化兼容格式

        Args:
            value: 原始值

        Returns:
            JSON兼容的值
        """
        if pd.isna(value):
            return None
        elif hasattr(value, 'isoformat'):  # datetime对象
            return value.isoformat()
        elif hasattr(value, 'item'):  # numpy类型
            return value.item()
        elif isinstance(value, (int, float, str, bool, type(None))):
            return value
        else:
            # 对于其他类型，转换为字符串
            return str(value)

    def _is_complex_chart(self, df: pd.DataFrame, hints: List[str]) -> bool:
        """
        判断是否为复杂图表
        
        Args:
            df: DataFrame
            hints: 可视化提示
            
        Returns:
            bool: 是否为复杂图表
        """
        # 检查数据量
        if len(df) > 100:
            return True
            
        # 检查列数
        if len(df.columns) > 5:
            return True
            
        # 检查提示
        complex_hints = ["multi_series", "dual_axis", "complex_chart"]
        for hint in hints:
            if hint in complex_hints:
                return True
                
        return False
    
    def _extract_schema(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        提取Schema信息
        
        Args:
            df: DataFrame
            
        Returns:
            Dict[str, Any]: Schema信息
        """
        return {
            "columns": list(df.columns),
            "dtypes": {col: str(dtype) for col, dtype in df.dtypes.items()},
            "shape": df.shape
        }
    
    def _generate_statistics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        生成统计信息
        
        Args:
            df: DataFrame
            
        Returns:
            Dict[str, Any]: 统计信息
        """
        stats = {}
        
        for col in df.columns:
            if pd.api.types.is_numeric_dtype(df[col]):
                # 数值列统计
                stats[col] = {
                    "min": float(df[col].min()) if not pd.isna(df[col].min()) else None,
                    "max": float(df[col].max()) if not pd.isna(df[col].max()) else None,
                    "mean": float(df[col].mean()) if not pd.isna(df[col].mean()) else None,
                    "null_count": int(df[col].isnull().sum())
                }
            elif pd.api.types.is_string_dtype(df[col]) or pd.api.types.is_object_dtype(df[col]):
                # 类别列统计
                value_counts = df[col].value_counts().head(5)
                stats[col] = {
                    "unique_count": int(df[col].nunique()),
                    "top_values": value_counts.index.tolist() if not value_counts.empty else [],
                    "null_count": int(df[col].isnull().sum())
                }
            elif pd.api.types.is_datetime64_dtype(df[col]):
                # 日期列统计
                stats[col] = {
                    "min": str(df[col].min()) if not pd.isna(df[col].min()) else None,
                    "max": str(df[col].max()) if not pd.isna(df[col].max()) else None,
                    "null_count": int(df[col].isnull().sum())
                }
                
        return stats

    def _get_field_info(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        获取字段信息 - 纯数据信息
        """
        field_info = {}
        
        for col in df.columns:
            col_info = {
                "dtype": str(df[col].dtype),
                "unique_count": int(df[col].nunique()),
                "null_count": int(df[col].isnull().sum())
            }
            
            # 数值字段：添加取值范围
            if pd.api.types.is_numeric_dtype(df[col]):
                col_info["type"] = "numeric"
                if df[col].notna().any():
                    col_info["min"] = float(df[col].min())
                    col_info["max"] = float(df[col].max())
                    col_info["mean"] = float(df[col].mean())
            # 时间字段
            elif pd.api.types.is_datetime64_any_dtype(df[col]):
                col_info["type"] = "datetime"
                if df[col].notna().any():
                    col_info["min"] = str(df[col].min())
                    col_info["max"] = str(df[col].max())
            # 其他字段（文本/分类）
            else:
                col_info["type"] = "categorical"
                
            field_info[col] = col_info
            
        return field_info

    def _extract_basic_info(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        提取基础数据信息
        """
        return {
            "total_rows": len(df),
            "total_columns": len(df.columns),
            "column_names": list(df.columns),
            "memory_usage_mb": round(df.memory_usage(deep=True).sum() / 1024 / 1024, 2),
            "shape": df.shape
        }

    def _assess_data_quality(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        评估数据质量
        """
        total_cells = len(df) * len(df.columns)
        null_cells = df.isnull().sum().sum()
        
        return {
            "completeness_rate": round((total_cells - null_cells) / total_cells * 100, 2),
            "null_percentage": round(null_cells / total_cells * 100, 2),
            "duplicate_rows": int(df.duplicated().sum()),
            "duplicate_percentage": round(df.duplicated().sum() / len(df) * 100, 2),
            "quality_score": self._calculate_quality_score(df)
        }

    def _analyze_data_scale(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        分析数据量级 - 关键新增，帮助模型理解数据规模
        """
        numeric_cols = df.select_dtypes(include=['number']).columns
        scale_analysis = {}
        
        for col in numeric_cols:
            col_data = df[col].dropna()
            if len(col_data) == 0:
                continue
                
            min_val = float(col_data.min())
            max_val = float(col_data.max())
            mean_val = float(col_data.mean())
            
            # 判断数量级
            magnitude = self._determine_magnitude(max_val)
            
            scale_analysis[col] = {
                "min": min_val,
                "max": max_val,
                "mean": mean_val,
                "magnitude": magnitude,
                "range": max_val - min_val,
                "scale_category": self._categorize_scale(magnitude),
                "has_zeros": bool((col_data == 0).any()),
                "has_negatives": bool((col_data < 0).any()),
                "variance": float(col_data.var()) if len(col_data) > 1 else 0
            }
        
        return {
            "numeric_fields": scale_analysis,
            "overall_scale_complexity": self._assess_scale_complexity(scale_analysis),
            "visualization_challenges": self._identify_scale_challenges(scale_analysis)
        }

    def _analyze_field_characteristics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        分析字段特征
        """
        field_analysis = {}
        
        for col in df.columns:
            col_info = {
                "dtype": str(df[col].dtype),
                "unique_count": int(df[col].nunique()),
                "null_count": int(df[col].isnull().sum()),
                "null_percentage": round(df[col].isnull().sum() / len(df) * 100, 2)
            }
            
            if pd.api.types.is_numeric_dtype(df[col]):
                col_info["field_type"] = "numeric"
                col_info["has_outliers"] = self._detect_outliers(df[col])
            elif pd.api.types.is_datetime64_any_dtype(df[col]):
                col_info["field_type"] = "datetime"
                col_info["time_range"] = self._analyze_time_range(df[col])
            else:
                col_info["field_type"] = "categorical"
                col_info["cardinality"] = self._assess_cardinality(df[col])
                col_info["top_values"] = self._get_top_values(df[col], 5)

            field_analysis[col] = col_info
            
        return field_analysis

    def _assess_visualization_readiness(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        评估数据的可视化适配性 - 关键新增
        """
        readiness = {
            "row_count_assessment": self._assess_row_count(len(df)),
            "column_count_assessment": self._assess_column_count(len(df.columns)),
            "data_density": self._calculate_data_density(df),
            "aggregation_needed": self._check_aggregation_need(df),
            "time_series_potential": self._check_time_series_potential(df),
            "categorical_distribution": self._analyze_categorical_distribution(df),
            "recommended_chart_types": self._suggest_chart_types(df)
        }
        
        return readiness

    def _analyze_data_distribution(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        分析数据分布洞察
        """
        distribution = {}
        numeric_cols = df.select_dtypes(include=['number']).columns
        
        for col in numeric_cols:
            col_data = df[col].dropna()
            if len(col_data) == 0:
                continue
                
            distribution[col] = {
                "distribution_type": self._identify_distribution_type(col_data),
                "skewness": self._calculate_skewness(col_data),
                "has_extreme_values": self._check_extreme_values(col_data),
                "concentration": self._analyze_value_concentration(col_data)
            }
            
        return distribution

    def _generate_visualization_recommendations(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        生成可视化建议
        """
        recommendations = {
            "chart_type_suggestions": [],
            "data_processing_suggestions": [],
            "visualization_warnings": [],
            "scale_recommendations": []
        }
        
        # 基于数据特征生成建议
        if len(df) > 1000:
            recommendations["data_processing_suggestions"].append("建议进行数据聚合以提高可视化性能")
            
        numeric_cols = df.select_dtypes(include=['number']).columns
        if len(numeric_cols) > 0:
            scale_ranges = [df[col].max() - df[col].min() for col in numeric_cols if df[col].notna().any()]
            if scale_ranges and max(scale_ranges) / min(scale_ranges) > 100:
                recommendations["scale_recommendations"].append("存在数量级差异巨大的字段，建议使用多Y轴或分离图表")
                
        # 检查时间序列数据
        datetime_cols = df.select_dtypes(include=['datetime64']).columns
        if len(datetime_cols) > 0:
            recommendations["chart_type_suggestions"].append("检测到时间数据，适合折线图或时间序列图")
            
        # 检查分类数据
        categorical_cols = df.select_dtypes(include=['object']).columns
        for col in categorical_cols:
            unique_count = df[col].nunique()
            if unique_count > 20:
                recommendations["visualization_warnings"].append(f"字段 {col} 有 {unique_count} 个唯一值，可能影响图表可读性")
                
        return recommendations

    # 辅助方法
    def _determine_magnitude(self, value: float) -> str:
        """确定数值的数量级"""
        if value == 0:
            return "zero"
        abs_val = abs(value)
        if abs_val >= 1e9:
            return "billions"
        elif abs_val >= 1e6:
            return "millions" 
        elif abs_val >= 1e3:
            return "thousands"
        elif abs_val >= 1:
            return "units"
        elif abs_val >= 1e-3:
            return "thousandths"
        else:
            return "very_small"

    def _categorize_scale(self, magnitude: str) -> str:
        """对数量级进行分类"""
        large_scales = ["billions", "millions"]
        medium_scales = ["thousands", "units"]
        small_scales = ["thousandths", "very_small"]
        
        if magnitude in large_scales:
            return "large"
        elif magnitude in medium_scales:
            return "medium"
        elif magnitude in small_scales:
            return "small"
        else:
            return "zero"

    def _assess_scale_complexity(self, scale_analysis: Dict) -> str:
        """评估整体数量级复杂度"""
        if not scale_analysis:
            return "simple"
            
        scales = [info["scale_category"] for info in scale_analysis.values()]
        unique_scales = set(scales)
        
        if len(unique_scales) > 2:
            return "high"
        elif len(unique_scales) == 2:
            return "medium"
        else:
            return "low"

    def _identify_scale_challenges(self, scale_analysis: Dict) -> List[str]:
        """识别数量级带来的可视化挑战"""
        challenges = []
        
        if not scale_analysis:
            return challenges
            
        scales = [info["scale_category"] for info in scale_analysis.values()]
        
        if "large" in scales and "small" in scales:
            challenges.append("存在大数值和小数值混合，建议使用对数轴或分离图表")
            
        zero_fields = [col for col, info in scale_analysis.items() if info["has_zeros"]]
        if zero_fields:
            challenges.append(f"字段 {', '.join(zero_fields)} 包含零值，需要特殊处理")
            
        negative_fields = [col for col, info in scale_analysis.items() if info["has_negatives"]]
        if negative_fields:
            challenges.append(f"字段 {', '.join(negative_fields)} 包含负值，影响某些图表类型的选择")
            
        return challenges

    def _calculate_quality_score(self, df: pd.DataFrame) -> int:
        """计算数据质量得分 (0-100)"""
        score = 100
        
        # 空值惩罚
        null_rate = df.isnull().sum().sum() / (len(df) * len(df.columns))
        score -= null_rate * 30
        
        # 重复值惩罚
        dup_rate = df.duplicated().sum() / len(df)
        score -= dup_rate * 20
        
        return max(0, min(100, int(score)))

    def _assess_row_count(self, row_count: int) -> Dict[str, Any]:
        """评估行数对可视化的影响"""
        if row_count < 10:
            return {"level": "too_few", "recommendation": "数据量过少，可视化价值有限"}
        elif row_count < 100:
            return {"level": "sufficient", "recommendation": "数据量适中，适合详细展示"}
        elif row_count < 1000:
            return {"level": "good", "recommendation": "数据量良好，可直接可视化"}
        else:
            return {"level": "large", "recommendation": "数据量较大，建议聚合后可视化"}

    def _assess_column_count(self, column_count: int) -> Dict[str, Any]:
        """评估列数对可视化的影响"""
        if column_count < 2:
            return {"level": "insufficient", "recommendation": "列数过少，可视化选项有限"}
        elif column_count <= 5:
            return {"level": "optimal", "recommendation": "列数适中，适合多种图表类型"}
        elif column_count <= 10:
            return {"level": "manageable", "recommendation": "列数较多，建议选择重点字段"}
        else:
            return {"level": "excessive", "recommendation": "列数过多，强烈建议筛选关键字段"}

    def _calculate_data_density(self, df: pd.DataFrame) -> float:
        """计算数据密度"""
        total_cells = len(df) * len(df.columns)
        non_null_cells = total_cells - df.isnull().sum().sum()
        return round(non_null_cells / total_cells, 3)

    def _check_aggregation_need(self, df: pd.DataFrame) -> bool:
        """检查是否需要聚合"""
        return len(df) > 500 or len(df.columns) > 8

    def _check_time_series_potential(self, df: pd.DataFrame) -> bool:
        """检查时间序列潜力"""
        datetime_cols = df.select_dtypes(include=['datetime64']).columns
        return len(datetime_cols) > 0

    def _analyze_categorical_distribution(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析分类变量分布"""
        categorical_cols = df.select_dtypes(include=['object']).columns
        analysis = {}
        
        for col in categorical_cols:
            unique_count = df[col].nunique()
            analysis[col] = {
                "cardinality": unique_count,
                "cardinality_level": "low" if unique_count < 10 else "medium" if unique_count < 50 else "high"
            }
            
        return analysis

    def _suggest_chart_types(self, df: pd.DataFrame) -> List[str]:
        """建议图表类型"""
        suggestions = []
        
        numeric_cols = len(df.select_dtypes(include=['number']).columns)
        categorical_cols = len(df.select_dtypes(include=['object']).columns)
        datetime_cols = len(df.select_dtypes(include=['datetime64']).columns)
        
        if datetime_cols > 0 and numeric_cols > 0:
            suggestions.append("时间序列图/折线图")
            
        if categorical_cols > 0 and numeric_cols > 0:
            suggestions.append("柱状图/条形图")
            
        if numeric_cols >= 2:
            suggestions.append("散点图")
            
        if categorical_cols == 1 and df[df.select_dtypes(include=['object']).columns[0]].nunique() < 8:
            suggestions.append("饼图")
            
        return suggestions

    # 简化的辅助方法实现
    def _detect_outliers(self, series: pd.Series) -> bool:
        """简单的异常值检测"""
        if series.nunique() < 2:
            return False
        q1, q3 = series.quantile([0.25, 0.75])
        iqr = q3 - q1
        lower_bound = q1 - 1.5 * iqr
        upper_bound = q3 + 1.5 * iqr
        return bool(((series < lower_bound) | (series > upper_bound)).any())

    def _analyze_time_range(self, series: pd.Series) -> Dict[str, Any]:
        """分析时间范围"""
        series_clean = series.dropna()
        if len(series_clean) == 0:
            return {"range": "unknown"}
        return {
            "start": str(series_clean.min()),
            "end": str(series_clean.max()),
            "span_days": (series_clean.max() - series_clean.min()).days
        }

    def _assess_cardinality(self, series: pd.Series) -> str:
        """评估基数"""
        unique_count = series.nunique()
        if unique_count < 10:
            return "low"
        elif unique_count < 50:
            return "medium"
        else:
            return "high"

    def _get_top_values(self, series: pd.Series, n: int = 5) -> List[str]:
        """获取前N个值"""
        return series.value_counts().head(n).index.astype(str).tolist()

    def _identify_distribution_type(self, series: pd.Series) -> str:
        """简单的分布类型识别"""
        if len(series) < 10:
            return "unknown"
        
        # 简单的偏度检查
        try:
            from scipy import stats
            skewness = stats.skew(series)
            if abs(skewness) < 0.5:
                return "normal"
            elif skewness > 0.5:
                return "right_skewed"
            else:
                return "left_skewed"
        except:
            return "unknown"

    def _calculate_skewness(self, series: pd.Series) -> float:
        """计算偏度"""
        try:
            from scipy import stats
            return float(stats.skew(series))
        except:
            return 0.0

    def _check_extreme_values(self, series: pd.Series) -> bool:
        """检查极值"""
        if len(series) < 4:
            return False
        q1, q3 = series.quantile([0.25, 0.75])
        iqr = q3 - q1
        if iqr == 0:
            return False
        lower_bound = q1 - 3 * iqr
        upper_bound = q3 + 3 * iqr
        return bool(((series < lower_bound) | (series > upper_bound)).any())

    def _analyze_value_concentration(self, series: pd.Series) -> str:
        """分析值的集中度"""
        unique_ratio = series.nunique() / len(series)
        if unique_ratio < 0.1:
            return "highly_concentrated"
        elif unique_ratio < 0.5:
            return "moderately_concentrated"
        else:
            return "well_distributed"
