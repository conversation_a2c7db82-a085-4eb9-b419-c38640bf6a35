"""
DataProfiler 服务 - 多维数据写真生成器
=====================================
为整个系统打造统一的、高质量的数据情报源。
生成信息密度极高的"多维数据写真"，作为后续所有智能决策的基石。

🎯 核心思路:
- 专业分工: 后端代码处理确定性数学计算，生成浓缩的"写真"
- 升维思考: 提供数据的宏观特征与分布，而非仅仅是数据样本
- 兼容并包: 为不同数据类型提供专属的、最有意义的统计画像
"""

import json
import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional, Union
from datetime import datetime

# 配置日志
logger = logging.getLogger(__name__)


class DataProfiler:
    """
    多维数据写真生成器
    
    为每一份数据生成一份信息密度极高的"多维数据写真"，
    包含数据的宏观特征、分布信息、统计画像等高级情报。
    """
    
    def __init__(self):
        """初始化数据写真生成器"""
        self.max_samples = 5  # 样本数据数量
        self.histogram_bins = 100  # 直方图分桶数量
        self.top_values_limit = 100  # 高频值统计数量
    
    def generate_profile(
        self,
        df: pd.DataFrame,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        生成多维数据写真
        
        Args:
            df: 待分析的DataFrame
            context: 上下文信息（可选）
            
        Returns:
            Dict[str, Any]: 多维数据写真JSON对象
        """
        try:
            logger.info(f"🔍 开始生成数据写真，数据形状: {df.shape}")
            
            # 全局信息
            global_info = self._generate_global_info(df)
            
            # 列级分析
            column_profiles = {}
            for column in df.columns:
                column_profiles[column] = self._profile_column(df[column], column)
            
            # 关系分析
            relationships = self._analyze_relationships(df)
            
            # 数据质量评估
            quality_assessment = self._assess_data_quality(df)
            
            # 样本数据
            sample_data = self._extract_representative_samples(df)
            
            # 构建完整的数据写真
            profile = {
                "metadata": {
                    "generated_at": datetime.now().isoformat(),
                    "profiler_version": "1.0.0",
                    "context": context or {}
                },
                "global_info": global_info,
                "column_profiles": column_profiles,
                "relationships": relationships,
                "quality_assessment": quality_assessment,
                "sample_data": sample_data
            }

            # 确保所有数据都是JSON可序列化的
            profile = self._ensure_json_serializable(profile)

            logger.info(f"✅ 数据写真生成完成，包含 {len(column_profiles)} 列的详细画像")
            return profile
            
        except Exception as e:
            logger.error(f"❌ 数据写真生成失败: {str(e)}")
            # 返回基础的错误写真
            return self._generate_fallback_profile(df, str(e))
    
    def _generate_global_info(self, df: pd.DataFrame) -> Dict[str, Any]:
        """生成全局数据信息"""
        return {
            "shape": {
                "rows": len(df),
                "columns": len(df.columns)
            },
            "memory_usage": {
                "total_bytes": df.memory_usage(deep=True).sum(),
                "per_column": df.memory_usage(deep=True).to_dict()
            },
            "column_names": list(df.columns),
            "data_density": {
                "total_cells": df.size,
                "non_null_cells": df.count().sum(),
                "null_cells": df.isnull().sum().sum(),
                "density_ratio": float(df.count().sum() / df.size) if df.size > 0 else 0.0
            }
        }
    
    def _profile_column(self, series: pd.Series, column_name: str) -> Dict[str, Any]:
        """为单列生成详细画像"""
        base_profile = {
            "name": column_name,
            "dtype": str(series.dtype),
            "count": len(series),
            "null_count": series.isnull().sum(),
            "null_percentage": float(series.isnull().sum() / len(series)) if len(series) > 0 else 0.0,
            "unique_count": series.nunique(),
            "unique_percentage": float(series.nunique() / len(series)) if len(series) > 0 else 0.0
        }
        
        # 根据数据类型进行专门分析
        # BUG修复: 布尔类型数据特殊处理，避免numpy运算错误
        # 修复策略: 布尔类型使用分类数据的分析方法
        # 影响范围: app/services/chart_generation/utils/data_profiler.py _profile_column方法
        # 修复日期: 2025-01-31
        if pd.api.types.is_bool_dtype(series):
            base_profile.update(self._profile_categorical_column(series))
        elif pd.api.types.is_numeric_dtype(series):
            base_profile.update(self._profile_numeric_column(series))
        elif pd.api.types.is_datetime64_any_dtype(series):
            base_profile.update(self._profile_datetime_column(series))
        elif pd.api.types.is_categorical_dtype(series) or pd.api.types.is_object_dtype(series):
            base_profile.update(self._profile_categorical_column(series))
        else:
            base_profile.update(self._profile_generic_column(series))
        
        return base_profile
    
    def _profile_numeric_column(self, series: pd.Series) -> Dict[str, Any]:
        """数值列专门分析"""
        clean_series = series.dropna()
        
        if len(clean_series) == 0:
            return {"type": "numeric", "analysis": "no_valid_data"}
        
        # 基础统计
        stats = {
            "min": float(clean_series.min()),
            "max": float(clean_series.max()),
            "mean": float(clean_series.mean()),
            "median": float(clean_series.median()),
            "std": float(clean_series.std()) if not pd.isna(clean_series.std()) else 0.0,
            "variance": float(clean_series.var()) if not pd.isna(clean_series.var()) else 0.0
        }
        
        # 百分位数
        percentiles = {}
        for p in [5, 25, 75, 95]:
            percentiles[f"p{p}"] = float(clean_series.quantile(p/100))
        
        # 直方图分布
        histogram = self._generate_histogram(clean_series)
        
        # 异常值检测
        outliers = self._detect_outliers(clean_series)
        
        # 数据分布特征
        distribution_features = self._analyze_distribution(clean_series)
        
        return {
            "type": "numeric",
            "statistics": stats,
            "percentiles": percentiles,
            "histogram": histogram,
            "outliers": outliers,
            "distribution": distribution_features
        }
    
    def _profile_datetime_column(self, series: pd.Series) -> Dict[str, Any]:
        """日期时间列专门分析"""
        clean_series = series.dropna()
        
        if len(clean_series) == 0:
            return {"type": "datetime", "analysis": "no_valid_data"}
        
        # 时间范围
        time_range = {
            "earliest": clean_series.min().isoformat() if hasattr(clean_series.min(), 'isoformat') else str(clean_series.min()),
            "latest": clean_series.max().isoformat() if hasattr(clean_series.max(), 'isoformat') else str(clean_series.max()),
            "span_days": (clean_series.max() - clean_series.min()).days if hasattr((clean_series.max() - clean_series.min()), 'days') else None
        }
        
        # 时间模式分析
        patterns = self._analyze_time_patterns(clean_series)
        
        return {
            "type": "datetime",
            "time_range": time_range,
            "patterns": patterns
        }
    
    def _profile_categorical_column(self, series: pd.Series) -> Dict[str, Any]:
        """分类列专门分析"""
        clean_series = series.dropna()
        
        if len(clean_series) == 0:
            return {"type": "categorical", "analysis": "no_valid_data"}
        
        # 值频次统计
        value_counts = clean_series.value_counts()
        top_values = {str(k): int(v) for k, v in value_counts.head(self.top_values_limit).to_dict().items()}
        
        # 分布特征
        distribution = {
            "most_frequent": value_counts.index[0] if len(value_counts) > 0 else None,
            "most_frequent_count": int(value_counts.iloc[0]) if len(value_counts) > 0 else 0,
            "least_frequent": value_counts.index[-1] if len(value_counts) > 0 else None,
            "least_frequent_count": int(value_counts.iloc[-1]) if len(value_counts) > 0 else 0,
            "concentration_ratio": float(value_counts.iloc[0] / len(clean_series)) if len(value_counts) > 0 and len(clean_series) > 0 else 0.0
        }
        
        return {
            "type": "categorical",
            "top_values": top_values,
            "distribution": distribution
        }
    
    def _profile_generic_column(self, series: pd.Series) -> Dict[str, Any]:
        """通用列分析（兜底方案）"""
        clean_series = series.dropna()
        
        # 尝试提取一些基本信息
        sample_values = clean_series.head(3).tolist() if len(clean_series) > 0 else []
        
        return {
            "type": "generic",
            "sample_values": [str(v) for v in sample_values],
            "analysis": "basic_profiling_only"
        }
    
    def _generate_histogram(self, series: pd.Series, bins: int = None) -> Dict[str, Any]:
        """生成数值列的直方图"""
        if bins is None:
            bins = min(self.histogram_bins, len(series.unique()))
        
        try:
            counts, bin_edges = np.histogram(series, bins=bins)
            
            return {
                "bins": int(bins),
                "counts": [int(x) for x in counts.tolist()],
                "bin_edges": [float(x) for x in bin_edges.tolist()],
                "bin_centers": [float(x) for x in ((bin_edges[:-1] + bin_edges[1:]) / 2).tolist()]
            }
        except Exception as e:
            logger.warning(f"直方图生成失败: {str(e)}")
            return {"error": str(e)}
    
    def _detect_outliers(self, series: pd.Series) -> Dict[str, Any]:
        """检测异常值"""
        try:
            Q1 = series.quantile(0.25)
            Q3 = series.quantile(0.75)
            IQR = Q3 - Q1
            
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            outliers = series[(series < lower_bound) | (series > upper_bound)]
            
            return {
                "method": "IQR",
                "bounds": {"lower": float(lower_bound), "upper": float(upper_bound)},
                "count": int(len(outliers)),
                "percentage": float(len(outliers) / len(series)) if len(series) > 0 else 0.0,
                "values": [float(x) for x in outliers.tolist()[:10]]  # 最多返回10个异常值
            }
        except Exception as e:
            return {"error": str(e)}
    
    def _analyze_distribution(self, series: pd.Series) -> Dict[str, Any]:
        """分析数据分布特征"""
        try:
            # 偏度和峰度
            skewness = float(series.skew())
            kurtosis = float(series.kurtosis())
            
            # 分布形状判断
            shape_analysis = {
                "skewness": skewness,
                "kurtosis": kurtosis,
                "shape_description": self._describe_distribution_shape(skewness, kurtosis)
            }
            
            return shape_analysis
        except Exception as e:
            return {"error": str(e)}
    
    def _describe_distribution_shape(self, skewness: float, kurtosis: float) -> str:
        """描述分布形状"""
        if abs(skewness) < 0.5:
            skew_desc = "approximately_symmetric"
        elif skewness > 0.5:
            skew_desc = "right_skewed"
        else:
            skew_desc = "left_skewed"
        
        if kurtosis > 1:
            kurt_desc = "heavy_tailed"
        elif kurtosis < -1:
            kurt_desc = "light_tailed"
        else:
            kurt_desc = "normal_tailed"
        
        return f"{skew_desc}_{kurt_desc}"
    
    def _analyze_time_patterns(self, series: pd.Series) -> Dict[str, Any]:
        """分析时间模式"""
        try:
            # 提取时间组件
            if hasattr(series.dt, 'hour'):
                hour_dist = series.dt.hour.value_counts().to_dict()
            else:
                hour_dist = {}
            
            if hasattr(series.dt, 'dayofweek'):
                dow_dist = series.dt.dayofweek.value_counts().to_dict()
            else:
                dow_dist = {}
            
            return {
                "hour_distribution": hour_dist,
                "day_of_week_distribution": dow_dist,
                "has_time_component": hasattr(series.dt, 'hour'),
                "frequency_analysis": "time_series_analysis_placeholder"
            }
        except Exception as e:
            return {"error": str(e)}
    
    def _analyze_relationships(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析列间关系"""
        try:
            # 数值列相关性
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) > 1:
                correlation_matrix = df[numeric_cols].corr()
                # 找出强相关关系
                strong_correlations = []
                for i in range(len(correlation_matrix.columns)):
                    for j in range(i+1, len(correlation_matrix.columns)):
                        corr_value = correlation_matrix.iloc[i, j]
                        if abs(corr_value) > 0.7:  # 强相关阈值
                            strong_correlations.append({
                                "column1": correlation_matrix.columns[i],
                                "column2": correlation_matrix.columns[j],
                                "correlation": float(corr_value)
                            })
                
                return {
                    "numeric_correlations": strong_correlations,
                    "correlation_matrix_available": True
                }
            else:
                return {"numeric_correlations": [], "correlation_matrix_available": False}
        except Exception as e:
            return {"error": str(e)}
    
    def _assess_data_quality(self, df: pd.DataFrame) -> Dict[str, Any]:
        """评估数据质量"""
        try:
            # 整体质量指标
            total_cells = df.size
            null_cells = df.isnull().sum().sum()
            
            # 列级质量问题
            quality_issues = []
            for col in df.columns:
                col_issues = []
                
                # 缺失值过多
                null_pct = df[col].isnull().sum() / len(df)
                if null_pct > 0.5:
                    col_issues.append(f"high_null_rate_{null_pct:.2f}")
                
                # 唯一值过少（可能的数据质量问题）
                if df[col].nunique() == 1 and len(df) > 1:
                    col_issues.append("single_value_column")
                
                if col_issues:
                    quality_issues.append({"column": col, "issues": col_issues})
            
            return {
                "overall_completeness": float(1 - null_cells / total_cells) if total_cells > 0 else 1.0,
                "column_issues": quality_issues,
                "quality_score": self._calculate_quality_score(df)
            }
        except Exception as e:
            return {"error": str(e)}
    
    def _calculate_quality_score(self, df: pd.DataFrame) -> float:
        """计算整体数据质量分数 (0-1)"""
        try:
            # 简单的质量评分算法
            completeness = 1 - (df.isnull().sum().sum() / df.size)
            uniqueness = df.nunique().sum() / len(df.columns)  # 平均唯一值比例
            
            # 综合评分
            quality_score = (completeness * 0.7 + min(uniqueness, 1.0) * 0.3)
            return float(quality_score)
        except Exception:
            return 0.5  # 默认中等质量
    
    def _extract_representative_samples(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """提取代表性样本"""
        try:
            # 智能采样：头部、尾部、随机
            sample_size = min(self.max_samples, len(df))
            
            if len(df) <= sample_size:
                samples = df.to_dict('records')
            else:
                # 头部样本
                head_samples = df.head(sample_size // 2)
                # 尾部样本
                tail_samples = df.tail(sample_size - len(head_samples))
                
                samples = pd.concat([head_samples, tail_samples]).to_dict('records')
            
            # 确保JSON序列化安全
            safe_samples = []
            for sample in samples:
                safe_sample = {}
                for key, value in sample.items():
                    if pd.isna(value):
                        safe_sample[key] = None
                    elif hasattr(value, 'isoformat'):
                        safe_sample[key] = value.isoformat()
                    elif hasattr(value, 'item'):
                        safe_sample[key] = value.item()
                    else:
                        safe_sample[key] = value
                safe_samples.append(safe_sample)
            
            return safe_samples
        except Exception as e:
            logger.warning(f"样本提取失败: {str(e)}")
            return []
    
    def _ensure_json_serializable(self, obj):
        """确保对象是JSON可序列化的"""
        if isinstance(obj, dict):
            return {k: self._ensure_json_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._ensure_json_serializable(item) for item in obj]
        elif isinstance(obj, (np.integer, np.int64, np.int32)):
            return int(obj)
        elif isinstance(obj, (np.floating, np.float64, np.float32)):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif pd.isna(obj):
            return None
        else:
            return obj

    def _generate_fallback_profile(self, df: pd.DataFrame, error_msg: str) -> Dict[str, Any]:
        """生成兜底的数据写真"""
        return {
            "metadata": {
                "generated_at": datetime.now().isoformat(),
                "profiler_version": "1.0.0",
                "status": "fallback",
                "error": error_msg
            },
            "global_info": {
                "shape": {"rows": len(df), "columns": len(df.columns)},
                "column_names": list(df.columns)
            },
            "column_profiles": {},
            "relationships": {},
            "quality_assessment": {"quality_score": 0.0},
            "sample_data": []
        }
