from typing import Dict, List, Any
from sqlalchemy.orm import Session
from sqlalchemy import func, and_
from datetime import datetime, timedelta

from app.models.organization import Organization
from app.models.user import User
from app.models.project import Project
from app.models.role import Role
from app.models.analysis import Analysis
from app.utils.time_utils import get_shanghai_time


class StatisticsService:
    """统计服务类"""

    @staticmethod
    def get_system_overview(db: Session) -> Dict[str, Any]:
        """获取系统总览统计"""
        # 基础统计
        total_orgs = db.query(Organization).count()
        active_orgs = db.query(Organization).filter(Organization.status == 1).count()
        total_users = db.query(User).count()
        active_users = db.query(User).filter(User.is_active == True).count()
        total_projects = db.query(Project).count()
        total_roles = db.query(Role).count()
        system_roles = db.query(Role).filter(Role.is_system == True).count()
        
        # 今日新增统计
        today = get_shanghai_time().date()
        today_start = datetime.combine(today, datetime.min.time())
        
        new_orgs_today = db.query(Organization).filter(
            Organization.created_at >= today_start
        ).count()
        
        new_users_today = db.query(User).filter(
            User.created_at >= today_start
        ).count()
        
        new_projects_today = db.query(Project).filter(
            Project.created_at >= today_start
        ).count()
        
        return {
            "organizations": {
                "total": total_orgs,
                "active": active_orgs,
                "inactive": total_orgs - active_orgs,
                "new_today": new_orgs_today
            },
            "users": {
                "total": total_users,
                "active": active_users,
                "inactive": total_users - active_users,
                "new_today": new_users_today
            },
            "projects": {
                "total": total_projects,
                "new_today": new_projects_today
            },
            "roles": {
                "total": total_roles,
                "system": system_roles,
                "custom": total_roles - system_roles
            }
        }

    @staticmethod
    def get_organization_ranking(db: Session, limit: int = 10) -> List[Dict[str, Any]]:
        """获取企业排行榜"""
        # 按用户数量排行
        user_ranking = db.query(
            Organization.id,
            Organization.name,
            func.count(User.id).label('user_count')
        ).outerjoin(User, Organization.id == User.org_id)\
         .filter(User.is_active == True)\
         .group_by(Organization.id)\
         .order_by(func.count(User.id).desc())\
         .limit(limit).all()
        
        # 按项目数量排行
        project_ranking = db.query(
            Organization.id,
            Organization.name,
            func.count(Project.id).label('project_count')
        ).outerjoin(Project, Organization.id == Project.org_id)\
         .group_by(Organization.id)\
         .order_by(func.count(Project.id).desc())\
         .limit(limit).all()
        
        # 合并排行数据
        ranking_data = []
        for org in user_ranking:
            # 查找对应的项目数据
            project_data = next((p for p in project_ranking if p.id == org.id), None)
            project_count = project_data.project_count if project_data else 0
            
            ranking_data.append({
                "org_id": org.id,
                "org_name": org.name,
                "user_count": org.user_count,
                "project_count": project_count
            })
        
        return ranking_data

    @staticmethod
    def get_user_growth_trend(db: Session, days: int = 30) -> List[Dict[str, Any]]:
        """获取用户增长趋势"""
        end_date = get_shanghai_time().date()
        start_date = end_date - timedelta(days=days)
        
        growth_data = []
        current_date = start_date
        
        while current_date <= end_date:
            day_start = datetime.combine(current_date, datetime.min.time())
            day_end = datetime.combine(current_date, datetime.max.time())
            
            # 当日新增用户
            new_users = db.query(User).filter(
                and_(User.created_at >= day_start, User.created_at <= day_end)
            ).count()
            
            # 累计用户数
            total_users = db.query(User).filter(
                User.created_at <= day_end
            ).count()
            
            growth_data.append({
                "date": current_date.strftime("%Y-%m-%d"),
                "new_users": new_users,
                "total_users": total_users
            })
            
            current_date += timedelta(days=1)
        
        return growth_data

    @staticmethod
    def get_role_distribution(db: Session) -> List[Dict[str, Any]]:
        """获取角色分布统计"""
        role_stats = db.query(
            Role.id,
            Role.name,
            Role.level,
            func.count(User.id).label('user_count')
        ).outerjoin(User, Role.id == User.role_id)\
         .filter(User.is_active == True)\
         .group_by(Role.id)\
         .order_by(func.count(User.id).desc()).all()
        
        distribution = []
        for role in role_stats:
            level_name = {0: "系统级", 1: "企业级", 2: "用户级"}.get(role.level, "未知")
            distribution.append({
                "role_id": role.id,
                "role_name": role.name,
                "level": role.level,
                "level_name": level_name,
                "user_count": role.user_count
            })
        
        return distribution

    @staticmethod
    def get_organization_health_check(db: Session) -> List[Dict[str, Any]]:
        """获取企业健康状况检查"""
        organizations = db.query(Organization).all()
        health_data = []
        
        for org in organizations:
            # 统计基础数据
            user_count = db.query(User).filter(
                and_(User.org_id == org.id, User.is_active == True)
            ).count()
            
            # 统计项目数量
            project_count = db.query(Project).filter(Project.org_id == org.id).count()
            
            # 计算使用率
            user_usage_rate = user_count / org.user_limit if org.user_limit > 0 else 0
            project_usage_rate = project_count / org.project_limit if org.project_limit > 0 else 0
            
            # 健康状况评估
            health_score = 100
            warnings = []
            
            # 检查用户使用率
            if user_usage_rate > 0.9:
                health_score -= 20
                warnings.append("用户数量接近上限")
            elif user_usage_rate > 0.8:
                health_score -= 10
                warnings.append("用户数量较多")
            
            # 检查项目使用率
            if project_usage_rate > 0.9:
                health_score -= 20
                warnings.append("项目数量接近上限")
            elif project_usage_rate > 0.8:
                health_score -= 10
                warnings.append("项目数量较多")
            
            # 检查企业状态
            if org.status != 1:
                health_score -= 30
                warnings.append("企业状态异常")
            
            # 检查过期时间
            if org.expire_at and org.expire_at < get_shanghai_time():
                health_score -= 40
                warnings.append("企业已过期")
            elif org.expire_at and (org.expire_at - get_shanghai_time()).days < 30:
                health_score -= 15
                warnings.append("企业即将过期")
            
            health_data.append({
                "org_id": org.id,
                "org_name": org.name,
                "health_score": max(health_score, 0),
                "user_count": user_count,
                "user_limit": org.user_limit,
                "user_usage_rate": user_usage_rate,
                "project_count": project_count,
                "project_limit": org.project_limit,
                "project_usage_rate": project_usage_rate,
                "status": org.status,
                "warnings": warnings
            })
        
        # 按健康分数排序
        health_data.sort(key=lambda x: x["health_score"])
        return health_data

    @staticmethod
    def get_system_alerts(db: Session) -> List[Dict[str, Any]]:
        """获取系统告警信息"""
        alerts = []
        
        # 检查企业用户数超限
        orgs_over_user_limit = db.query(Organization).all()
        for org in orgs_over_user_limit:
            user_count = db.query(User).filter(
                and_(User.org_id == org.id, User.is_active == True)
            ).count()
            
            if user_count >= org.user_limit:
                alerts.append({
                    "type": "user_limit_exceeded",
                    "level": "warning",
                    "message": f"企业 {org.name} 用户数量已达上限 ({user_count}/{org.user_limit})",
                    "org_id": org.id,
                    "org_name": org.name
                })
        
        # 检查企业项目数超限
        for org in orgs_over_user_limit:
            # 统计项目数量
            project_count = db.query(Project).filter(Project.org_id == org.id).count()
            
            if project_count >= org.project_limit:
                alerts.append({
                    "type": "project_limit_exceeded",
                    "level": "warning",
                    "message": f"企业 {org.name} 项目数量已达上限 ({project_count}/{org.project_limit})",
                    "org_id": org.id,
                    "org_name": org.name
                })
        
        # 检查即将过期的企业
        expire_soon = db.query(Organization).filter(
            and_(
                Organization.expire_at.isnot(None),
                Organization.expire_at > get_shanghai_time(),
                Organization.expire_at <= get_shanghai_time() + timedelta(days=30)
            )
        ).all()
        
        for org in expire_soon:
            days_left = (org.expire_at - get_shanghai_time()).days
            alerts.append({
                "type": "organization_expiring",
                "level": "warning" if days_left > 7 else "critical",
                "message": f"企业 {org.name} 将在 {days_left} 天后过期",
                "org_id": org.id,
                "org_name": org.name,
                "expire_at": org.expire_at.isoformat()
            })
        
        return alerts 