"""
会话服务 - 数据库级别的上下文管理
=====================================
管理多轮分析会话的上下文状态和持久化
"""

import uuid
import json
import logging
import re
from typing import List, Dict, Any, Optional
from datetime import datetime
from app.utils.time_utils import get_shanghai_time
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_, text
from openai import AsyncOpenAI

from app.models.conversation import Conversation, ConversationContext, ContextType
from app.models.analysis import Analysis, ToolExecution, AnalysisStepDetail
from app.schemas.conversation import (
    ConversationContextData, AnalysisResult, ConversationContextResponse
)
from app.core.config import settings

log = logging.getLogger(__name__)

class ConversationService:
    """会话服务类"""
    
    def __init__(self, db: Session):
        self.db = db
        # 初始化LLM客户端用于总结（按照llm_analyzer.py的方式）
        try:
            self.llm_client = AsyncOpenAI(
                api_key=settings.OPENAI_API_KEY,
                base_url=settings.OPENAI_BASE_URL
            )
            log.info("LLM客户端初始化成功")
        except Exception as e:
            log.error(f"初始化OpenAI客户端失败: {str(e)}")
            self.llm_client = None
        
        # 初始化步骤服务
        from app.services.analysis_step_service import AnalysisStepService
        self.step_service = AnalysisStepService(db)
    
    def create_conversation(
        self, 
        project_id: str, 
        initial_query: str,
        user_id: int,
        title: str = None
    ) -> Conversation:
        """创建新会话"""
        conversation_id = self._generate_conversation_id()
        
        from app.models.conversation import ConversationStatus
        
        conversation = Conversation(
            id=conversation_id,
            project_id=project_id,
            user_id=user_id,
            title=title or self._generate_title(initial_query),
            status=ConversationStatus.ACTIVE,
            total_rounds=0,
            total_tokens_used=0
        )
        
        self.db.add(conversation)
        self.db.commit()
        
        # 初始化上下文
        self._initialize_conversation_context(conversation.id, initial_query)
        
        log.info(f"创建新会话: {conversation_id}, 项目: {project_id}")
        return conversation
    
    def save_analysis_state(
        self,
        analysis_id: str,
        analysis_steps: List[Dict],
        tool_results: Dict[str, Any],
        mcp_results: Dict[str, Any],
        final_report: str = None,
        status: str = 'running',
        execution_time: float = None,
        context_relevance: float = None
    ):
        """保存分析的完整状态"""
        try:
            analysis = self.db.query(Analysis).filter(Analysis.id == analysis_id).first()
            if not analysis:
                log.error(f"分析记录不存在: {analysis_id}")
                return False
            
            # 更新分析记录
            analysis.analysis_steps = analysis_steps
            analysis.tool_results = tool_results
            analysis.mcp_results = mcp_results
            analysis.status = status
            analysis.execution_time = execution_time
            analysis.context_relevance = context_relevance
            
            if final_report:
                analysis.result = final_report
            
            from datetime import timezone, timedelta
            beijing_tz = timezone(timedelta(hours=8))
            analysis.updated_at = datetime.now(beijing_tz)
            
            # 同时保存详细步骤到专门的表中
            if analysis_steps:
                self.step_service.save_steps_batch(analysis_id, analysis_steps)
            
            self.db.commit()
            
            log.info(f"保存分析状态成功: {analysis_id}, 状态: {status}, 步骤数: {len(analysis_steps)}")
            return True
            
        except Exception as e:
            log.error(f"保存分析状态失败: {str(e)}")
            self.db.rollback()
            return False
    
    def get_analysis_state(self, analysis_id: str) -> Optional[Dict[str, Any]]:
        """获取分析的完整状态"""
        try:
            analysis = self.db.query(Analysis).filter(Analysis.id == analysis_id).first()
            if not analysis:
                return None
            
            # 获取工具执行记录
            tool_executions = self.db.query(ToolExecution).filter(
                ToolExecution.analysis_id == analysis_id
            ).order_by(ToolExecution.step_order.asc()).all()
            
            # 获取详细步骤记录（优先使用详细步骤，如果没有则使用analysis_steps）
            detailed_steps = self.step_service.get_analysis_steps(analysis_id)
            analysis_steps = detailed_steps if detailed_steps else (analysis.analysis_steps or [])
            
            return {
                "analysis_id": analysis.id,
                "query": analysis.query,
                "round_number": analysis.round_number,
                "conversation_id": analysis.conversation_id,
                "analysis_steps": analysis_steps,
                "step_details": detailed_steps,  # 添加详细步骤数据
                "tool_results": analysis.tool_results or {},
                "mcp_results": analysis.mcp_results or {},
                "final_report": analysis.result,
                "status": analysis.status,
                "execution_time": analysis.execution_time,
                "context_relevance": analysis.context_relevance,
                "llm_summary": analysis.llm_summary,
                "intent_analysis": analysis.intent_analysis,
                "tool_executions": [
                    {
                        "id": te.id,
                        "step_id": te.step_id,
                        "tool_name": self._get_tool_name_from_execution(te),
                        "parameters": te.parameters,
                        "result": te.result,
                        "execution_time": te.execution_time,
                        "status": te.status,
                        "error_message": te.error_message,
                        "step_order": te.step_order,
                        "reasoning": te.reasoning,
                        "created_at": te.created_at.isoformat()
                    }
                    for te in tool_executions
                ],
                "created_at": analysis.created_at.isoformat(),
                "updated_at": analysis.updated_at.isoformat()
            }
            
        except Exception as e:
            log.error(f"获取分析状态失败: {str(e)}")
            return None
    
    def get_conversation_analyses(self, conversation_id: str) -> List[Dict[str, Any]]:
        """获取会话的所有分析记录（包含完整状态）"""
        try:
            analyses = self.db.query(Analysis).filter(
                Analysis.conversation_id == conversation_id
            ).order_by(Analysis.round_number.asc()).all()
            
            result = []
            for analysis in analyses:
                analysis_state = self.get_analysis_state(analysis.id)
                if analysis_state:
                    result.append(analysis_state)
            
            return result
            
        except Exception as e:
            log.error(f"获取会话分析记录失败: {str(e)}")
            return []
    
    async def generate_round_summary(self, analysis_id: str) -> Optional[str]:
        """使用LLM生成轮次总结"""
        if not self.llm_client:
            log.warning("LLM客户端未配置，无法生成总结")
            return None
        
        try:
            analysis_state = self.get_analysis_state(analysis_id)
            if not analysis_state:
                return None
            
            # 构建总结提示词
            prompt = self._build_summary_prompt(analysis_state)
            
            # 调用LLM生成总结
            completion = await self.llm_client.chat.completions.create(
                model=settings.OPENAI_MODEL if hasattr(settings, 'OPENAI_MODEL') else "gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "你是一个专业的数据分析总结助手，负责总结分析过程和结果。"},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=500,
                temperature=0.3
            )
            
            summary = completion.choices[0].message.content
            
            # 保存总结到数据库
            analysis = self.db.query(Analysis).filter(Analysis.id == analysis_id).first()
            if analysis:
                analysis.llm_summary = summary
                self.db.commit()
            
            log.info(f"生成轮次总结成功: {analysis_id}")
            return summary
            
        except Exception as e:
            log.error(f"生成轮次总结失败: {str(e)}")
            return None
    
    def _build_summary_prompt(self, analysis_state: Dict[str, Any]) -> str:
        """构建总结提示词"""
        query = analysis_state.get('query', '')
        final_report = analysis_state.get('final_report', '')
        tool_executions = analysis_state.get('tool_executions', [])
        
        # 提取工具使用信息
        tools_used = []
        key_results = []
        
        for te in tool_executions:
            if te.get('status') == 'success':
                tools_used.append(te.get('tool_name', ''))
                
                # 提取关键结果
                result = te.get('result', {})
                if isinstance(result, dict) and 'data' in result:
                    key_results.append(f"使用{te.get('tool_name', '')}获得了数据结果")
        
        prompt = f"""
请为以下数据分析轮次生成一个简洁的总结，重点突出关键发现和使用的方法：

## 用户查询
{query}

## 使用的工具
{', '.join(set(tools_used)) if tools_used else '无'}

## 分析报告
{final_report[:800] if final_report else '无报告'}

## 要求
1. 总结要简洁明了，不超过200字
2. 重点突出关键发现和数据洞察
3. 提及使用的主要分析方法
4. 用中文回答

请生成总结：
"""
        return prompt
    
    async def get_conversation_context_with_llm_summary(
        self, 
        conversation_id: str,
        max_rounds: int = 5
    ) -> str:
        """获取会话上下文并生成LLM总结用于提示词"""
        try:
            # 获取历史分析记录
            analyses = self.db.query(Analysis).filter(
                Analysis.conversation_id == conversation_id,
                Analysis.status == 'completed'
            ).order_by(Analysis.round_number.desc()).limit(max_rounds).all()
            
            if not analyses:
                return ""
            
            # 构建历史总结
            history_summaries = []
            
            for analysis in reversed(analyses):  # 按时间顺序
                # 如果已有LLM总结，直接使用
                if analysis.llm_summary:
                    summary = analysis.llm_summary
                else:
                    # 生成简单总结
                    summary = self._generate_simple_summary(analysis)
                
                history_summaries.append(f"""
第{analysis.round_number}轮分析：
- 查询：{analysis.query}
- 总结：{summary}
- 时间：{analysis.created_at.strftime('%Y-%m-%d %H:%M')}
""")
            
            # 构建上下文提示词
            context_prompt = f"""
## 历史分析上下文

本次是多轮数据分析对话，以下是之前的分析历史：

{''.join(history_summaries)}

## 分析指导
请基于以上历史分析上下文，理解用户的分析需求和已有发现，避免重复分析，在回答中体现对话的连贯性。
"""
            
            return context_prompt
            
        except Exception as e:
            log.error(f"获取LLM上下文总结失败: {str(e)}")
            return ""
    
    def _generate_simple_summary(self, analysis: Analysis) -> str:
        """生成简单的分析总结"""
        if not analysis.result:
            return "分析未完成"
        
        # 提取关键信息
        result_text = analysis.result
        
        # 查找关键数字和结论
        key_findings = []
        
        # 提取数字信息
        numbers = re.findall(r'\d+(?:\.\d+)?[万千百亿]?[元个家次%]', result_text)
        if numbers:
            key_findings.append(f"涉及数据：{', '.join(numbers[:3])}")
        
        # 提取关键词
        keywords = ['发现', '结论', '显示', '表明', '总计', '平均']
        for keyword in keywords:
            if keyword in result_text:
                # 找到包含关键词的句子
                sentences = result_text.split('。')
                for sentence in sentences:
                    if keyword in sentence and len(sentence) < 100:
                        key_findings.append(sentence.strip())
                        break
                break
        
        if key_findings:
            return '；'.join(key_findings[:2])
        else:
            # 返回前100字符
            return result_text[:100] + "..." if len(result_text) > 100 else result_text
    
    def get_conversation_context(
        self, 
        conversation_id: str,
        max_rounds: int = 10,
        context_types: List[str] = None
    ) -> ConversationContextData:
        """获取会话的结构化上下文 - 增强版"""
        
        try:
            # 使用原始SQL查询避免枚举问题
            sql = """
                SELECT id, conversation_id, round_number, context_type, context_data, relevance_score, created_at
                FROM conversation_contexts 
                WHERE conversation_id = :conversation_id
            """
            
            params = {"conversation_id": conversation_id}
            
            if context_types:
                placeholders = ",".join([f":type_{i}" for i in range(len(context_types))])
                sql += f" AND context_type IN ({placeholders})"
                for i, context_type in enumerate(context_types):
                    params[f"type_{i}"] = context_type
            
            sql += """
                ORDER BY relevance_score DESC, round_number DESC
                LIMIT :limit_count
            """
            params["limit_count"] = max_rounds * 10  # 增加获取数量
            
            # 执行查询
            result = self.db.execute(text(sql), params)
            raw_contexts = result.fetchall()
            
            log.info(f"获取到 {len(raw_contexts)} 条上下文记录")
            
            # 手动构建上下文数据
            context_data = ConversationContextData()
            
            # 按轮次组织数据，确保完整性
            round_data = {}
            
            for ctx in raw_contexts:
                round_num = ctx.round_number
                if round_num not in round_data:
                    round_data[round_num] = {
                        'queries': [],
                        'results': [],
                        'insights': [],
                        'tools': [],
                        'data_sources': []
                    }
                
                context_type_str = ctx.context_type
                try:
                    context_json = json.loads(ctx.context_data) if ctx.context_data else {}
                except json.JSONDecodeError:
                    log.warning(f"无法解析上下文数据: {ctx.context_data}")
                    continue
                
                if context_type_str == "query":
                    query = context_json.get('query', '')
                    if query:
                        round_data[round_num]['queries'].append(query)
                        
                elif context_type_str == "result":
                    # 构建更完整的结果对象
                    result_obj = AnalysisResult(
                        round_number=ctx.round_number,
                        query=context_json.get('query', ''),
                        summary=context_json.get('summary', ''),
                        key_data=context_json.get('key_data', []),
                        insights=context_json.get('insights', []),
                        timestamp=ctx.created_at.isoformat()
                    )
                    round_data[round_num]['results'].append(result_obj)
                    
                    # 提取洞察
                    insights = context_json.get('insights', [])
                    round_data[round_num]['insights'].extend(insights)
                    
                elif context_type_str == "insight":
                    insights = context_json.get('insights', [])
                    round_data[round_num]['insights'].extend(insights)
                    
                elif context_type_str == "tool_usage":
                    tools = context_json.get('tools', [])
                    data_sources = context_json.get('data_sources', [])
                    round_data[round_num]['tools'].extend(tools)
                    round_data[round_num]['data_sources'].extend(data_sources)
            
            # 按轮次顺序整合数据
            for round_num in sorted(round_data.keys(), reverse=True):  # 最新的轮次优先
                round_info = round_data[round_num]
                
                # 添加查询
                for query in round_info['queries']:
                    if query and query not in context_data.previous_queries:
                        context_data.previous_queries.append(query)
                
                # 添加结果
                context_data.previous_results.extend(round_info['results'])
                
                # 添加洞察
                context_data.key_findings.extend(round_info['insights'])
                
                # 添加工具和数据源
                context_data.tools_used.extend(round_info['tools'])
                context_data.data_sources_used.extend(round_info['data_sources'])
            
            # 去重并限制数量
            context_data.previous_queries = list(dict.fromkeys(context_data.previous_queries))[:max_rounds]
            context_data.key_findings = list(dict.fromkeys(context_data.key_findings))[:20]  # 最多20个洞察
            context_data.tools_used = list(dict.fromkeys(context_data.tools_used))
            context_data.data_sources_used = list(dict.fromkeys(context_data.data_sources_used))
            
            # 按时间排序结果
            context_data.previous_results.sort(key=lambda x: x.timestamp, reverse=True)
            context_data.previous_results = context_data.previous_results[:max_rounds]
            
            log.info(f"构建上下文完成: 查询{len(context_data.previous_queries)}个, "
                    f"结果{len(context_data.previous_results)}个, "
                    f"洞察{len(context_data.key_findings)}个, "
                    f"工具{len(context_data.tools_used)}个")
            
            return context_data
            
        except Exception as e:
            log.error(f"获取会话上下文失败: {str(e)}")
            # 返回空的上下文数据而不是抛出异常
            return ConversationContextData()
    
    def update_conversation_summary(self, conversation_id: str, summary: str):
        """更新会话摘要"""
        conversation = self.db.query(Conversation).filter(
            Conversation.id == conversation_id
        ).first()
        
        if conversation:
            from datetime import timezone, timedelta
            beijing_tz = timezone(timedelta(hours=8))
            conversation.context_summary = summary
            conversation.updated_at = datetime.now(beijing_tz)
            self.db.commit()
    
    def get_conversation_by_id(self, conversation_id: str) -> Optional[Conversation]:
        """根据ID获取会话"""
        return self.db.query(Conversation).filter(
            Conversation.id == conversation_id
        ).first()
    
    def _generate_conversation_id(self) -> str:
        """生成会话ID"""
        return f"conv_{int(get_shanghai_time().timestamp())}_{str(uuid.uuid4())[:8]}"
    
    def _generate_title(self, initial_query: str) -> str:
        """生成会话标题"""
        if len(initial_query) <= 30:
            return initial_query
        return initial_query[:30] + "..."
    
    def _initialize_conversation_context(self, conversation_id: str, initial_query: str):
        """初始化会话上下文"""
        self._add_context_record(
            conversation_id, 0, "query",
            {
                "initial_query": initial_query, 
                "conversation_start": True,
                "intent": self._extract_query_intent(initial_query),
                "entities": self._extract_entities_from_query(initial_query)
            }
        )
    
    def _add_context_record(
        self,
        conversation_id: str,
        round_number: int,
        context_type: str,
        context_data: Dict[str, Any],
        relevance_score: float = 1.0
    ):
        """添加上下文记录"""
        try:
            context_record = ConversationContext(
                conversation_id=conversation_id,
                round_number=round_number,
                context_type=context_type,
                context_data=context_data,
                relevance_score=relevance_score
            )
            
            self.db.add(context_record)
            self.db.commit()
            
        except Exception as e:
            log.error(f"添加上下文记录失败: {str(e)}")
            self.db.rollback()
    
    def _extract_query_intent(self, query: str) -> str:
        """提取查询意图"""
        intent_keywords = {
            "查询": ["查询", "查看", "获取", "显示", "列出"],
            "分析": ["分析", "统计", "计算", "比较", "趋势"],
            "搜索": ["搜索", "查找", "寻找", "筛选"],
            "汇总": ["汇总", "总计", "合计", "求和", "平均"],
            "关联": ["关联", "关系", "相关", "连接"]
        }
        
        for intent, keywords in intent_keywords.items():
            if any(keyword in query for keyword in keywords):
                return intent
        
        return "其他"
    
    def _extract_entities_from_query(self, query: str) -> List[str]:
        """从查询中提取实体"""
        entities = []
        
        # 提取公司名称
        company_patterns = [
            r'([A-Z]+公司)',
            r'(\w+有限公司)',
            r'(\w+股份有限公司)',
            r'(\w+集团)',
            r'(\w+企业)'
        ]
        
        for pattern in company_patterns:
            matches = re.findall(pattern, query)
            entities.extend(matches)
        
        # 提取时间相关
        time_patterns = [
            r'(\d{4}年)',
            r'(\d{1,2}月)',
            r'(近\d+[天月年])',
            r'(最近)',
            r'(今年)',
            r'(去年)'
        ]
        
        for pattern in time_patterns:
            matches = re.findall(pattern, query)
            entities.extend(matches)
        
        # 提取数值
        number_patterns = [
            r'(\d+(?:\.\d+)?[万千百亿]?[元个家次])',
            r'(\d+%)',
            r'(前\d+名)',
            r'(top\d+)'
        ]
        
        for pattern in number_patterns:
            matches = re.findall(pattern, query, re.IGNORECASE)
            entities.extend(matches)
        
        return list(set(entities))
    
    def _extract_topics_from_query(self, query: str) -> List[str]:
        """从查询中提取主题"""
        topic_keywords = {
            "公司信息": ["公司", "企业", "机构", "组织"],
            "财务数据": ["资金", "金额", "交易", "流水", "转账", "收入", "支出", "利润"],
            "统计分析": ["统计", "数量", "总计", "平均", "比例", "排名", "趋势"],
            "关系网络": ["关系", "关联", "网络", "连接", "合作"],
            "时间序列": ["时间", "日期", "年", "月", "日", "趋势", "变化"],
            "地理位置": ["地区", "城市", "省份", "区域", "位置"]
        }
        
        topics = []
        for topic, keywords in topic_keywords.items():
            if any(keyword in query for keyword in keywords):
                topics.append(topic)
        
        return topics
    
    def _generate_smart_summary(self, result_text: str) -> str:
        """生成智能摘要"""
        if len(result_text) <= 200:
            return result_text
        
        # 提取关键句子
        sentences = result_text.split('。')
        key_sentences = []
        
        for sentence in sentences[:5]:  # 只看前5句
            sentence = sentence.strip()
            if len(sentence) > 10 and any(keyword in sentence for keyword in 
                ['发现', '结果', '显示', '表明', '总计', '平均', '最高', '最低', '主要']):
                key_sentences.append(sentence)
        
        if key_sentences:
            summary = '。'.join(key_sentences[:3]) + '。'
            return summary if len(summary) <= 300 else summary[:300] + "..."
        
        # 如果没有找到关键句子，返回前200字符
        return result_text[:200] + "..."
    
    def _extract_key_insights(self, result_text: str) -> List[str]:
        """从分析结果中提取关键洞察 - 增强版"""
        insights = []
        
        # 按段落分割
        paragraphs = result_text.split('\n\n')
        
        for paragraph in paragraphs:
            lines = paragraph.split('\n')
            for line in lines:
                line = line.strip()
                
                # 更精确的关键词匹配
                insight_indicators = [
                    '发现', '结论', '建议', '洞察', '关键', '重要', '显示', '表明',
                    '总计', '平均', '最高', '最低', '增长', '下降', '趋势', '异常'
                ]
                
                if (len(line) > 15 and len(line) < 150 and 
                    any(keyword in line for keyword in insight_indicators)):
                    
                    # 清理格式
                    clean_line = re.sub(r'^[#*\-\d\.\s]+', '', line)
                    clean_line = clean_line.strip('：:')
                    
                    if clean_line and clean_line not in insights:
                        insights.append(clean_line)
        
        return insights[:8]  # 最多返回8个洞察
    
    def _extract_key_data_points(self, result_text: str) -> List[Dict]:
        """提取关键数据点"""
        data_points = []
        
        # 提取数值型数据
        number_patterns = [
            r'(\w+)[:：]\s*(\d+(?:\.\d+)?[万千百亿]?[元个家次%])',
            r'(\w+)为(\d+(?:\.\d+)?[万千百亿]?[元个家次%])',
            r'总计(\d+(?:\.\d+)?[万千百亿]?[元个家次%])',
            r'平均(\d+(?:\.\d+)?[万千百亿]?[元个家次%])'
        ]
        
        for pattern in number_patterns:
            matches = re.findall(pattern, result_text)
            for match in matches:
                if isinstance(match, tuple) and len(match) == 2:
                    data_points.append({
                        "entity": match[0],
                        "value": match[1],
                        "type": "numeric"
                    })
                elif isinstance(match, str):
                    data_points.append({
                        "entity": "总计/平均",
                        "value": match,
                        "type": "numeric"
                    })
        
        return data_points[:10]  # 最多返回10个数据点
    
    def _classify_analysis_type(self, result_text: str) -> str:
        """分类分析类型"""
        if any(keyword in result_text for keyword in ['SQL', 'SELECT', 'FROM', 'WHERE']):
            return "数据查询"
        elif any(keyword in result_text for keyword in ['统计', '平均', '总计', '比例']):
            return "统计分析"
        elif any(keyword in result_text for keyword in ['趋势', '变化', '增长', '下降']):
            return "趋势分析"
        elif any(keyword in result_text for keyword in ['关系', '关联', '网络']):
            return "关系分析"
        else:
            return "综合分析"
    
    def _extract_tools_used(self, execution_results: List[Dict]) -> List[str]:
        """提取使用的工具列表 - 增强版"""
        tools = []
        for result in execution_results or []:
            tool_name = result.get('tool_name', '')
            if tool_name and tool_name not in tools:
                tools.append(tool_name)
        return tools
    
    def _extract_data_sources(self, execution_results: List[Dict]) -> List[str]:
        """提取使用的数据源列表 - 增强版"""
        data_sources = []
        for result in execution_results or []:
            # 从工具参数中提取数据源信息
            parameters = result.get('parameters', {})
            
            # 提取表名
            if 'table_name' in parameters:
                data_sources.append(parameters['table_name'])
            elif 'sql' in parameters:
                # 从SQL中提取表名
                sql = parameters['sql']
                table_matches = re.findall(r'FROM\s+(\w+)', sql, re.IGNORECASE)
                data_sources.extend(table_matches)
            elif 'database' in parameters:
                data_sources.append(parameters['database'])
        
        return list(set(data_sources))
    
    def _summarize_executions(self, execution_results: List[Dict]) -> str:
        """总结执行过程"""
        if not execution_results:
            return ""
        
        tool_count = len(execution_results)
        tools = [result.get('tool_name', '未知工具') for result in execution_results]
        unique_tools = list(set(tools))
        
        return f"执行了{tool_count}个步骤，使用了{len(unique_tools)}种工具：{', '.join(unique_tools)}"
    
    def _extract_sql_tables(self, execution_results: List[Dict]) -> List[str]:
        """提取SQL查询中的表名"""
        tables = []
        for result in execution_results or []:
            parameters = result.get('parameters', {})
            if 'sql' in parameters:
                sql = parameters['sql']
                # 提取FROM子句中的表名
                from_matches = re.findall(r'FROM\s+(\w+)', sql, re.IGNORECASE)
                # 提取JOIN子句中的表名
                join_matches = re.findall(r'JOIN\s+(\w+)', sql, re.IGNORECASE)
                tables.extend(from_matches + join_matches)
        
        return list(set(tables))
    
    def _calculate_total_execution_time(self, execution_results: List[Dict]) -> float:
        """计算总执行时间"""
        total_time = 0.0
        for result in execution_results or []:
            exec_time = result.get('execution_time', 0)
            if isinstance(exec_time, (int, float)):
                total_time += exec_time
        
        return round(total_time, 3)
    
    def _get_tool_name_from_execution(self, tool_execution) -> str:
        """从工具执行记录中获取工具名称"""
        # 首先尝试从关联的tool对象获取
        if tool_execution.tool and tool_execution.tool.name:
            return tool_execution.tool.name
        
        # 如果没有关联的tool对象，尝试从tool_id推断
        if tool_execution.tool_id:
            tool_id = tool_execution.tool_id
            
            # 首先检查是否是系统工具
            try:
                from app.services.system_tools import SystemTools
                system_tools = SystemTools.get_system_tools()
                
                for system_tool in system_tools:
                    if system_tool.get('id') == tool_id:
                        return system_tool.get('name', '未知系统工具')
                
                log.debug(f"在系统工具中未找到 tool_id: {tool_id}")
                
            except Exception as e:
                log.warning(f"获取系统工具列表失败: {str(e)}")
            
            # 如果不是系统工具，尝试从数据库查询
            try:
                from app.models.tool import Tool
                tool = self.db.query(Tool).filter(Tool.id == tool_id).first()
                if tool:
                    return tool.name
            except Exception as e:
                log.warning(f"从数据库查询工具失败: {str(e)}")
            
            # 最后的fallback映射（保留作为备用）
            tool_name_mapping = {
                'auto_sql_tool': '自动SQL查询',
                'clarification_tool': '智能交互',
                'company_info_tool': '公司基本信息查询',
                'fund_transfer_tool': '资金流转分析',
                'sql_query_tool': 'SQL查询工具',
                'data_analysis_tool': '数据分析工具'
            }
            
            if tool_id in tool_name_mapping:
                return tool_name_mapping[tool_id]
        
        # 最后的fallback
        return "未知工具"
    
    def _update_conversation_stats(self, conversation_id: str):
        """更新会话统计信息"""
        conversation = self.db.query(Conversation).filter(
            Conversation.id == conversation_id
        ).first()
        
        if conversation:
            # 统计轮次数
            round_count = self.db.query(Analysis).filter(
                Analysis.conversation_id == conversation_id
            ).count()
            
            from datetime import timezone, timedelta
            beijing_tz = timezone(timedelta(hours=8))
            conversation.total_rounds = round_count
            conversation.updated_at = datetime.now(beijing_tz)
            self.db.commit() 