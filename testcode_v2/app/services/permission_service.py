"""
权限控制服务
实现基于角色和企业的权限控制逻辑
"""
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from app.models.user import User
from app.models.project import Project
from app.models.project_member import ProjectMember
from app.models.project_role import ProjectRole
from app.models.organization import Organization
from app.core.exceptions import PermissionDeniedException


class PermissionService:
    """权限控制服务类"""
    
    # 系统角色常量
    SUPER_ADMIN = 1
    ORG_ADMIN = 2
    NORMAL_USER = 3
    
    # 项目角色常量
    PROJECT_OWNER = 1
    PROJECT_COLLABORATOR = 2
    PROJECT_OBSERVER = 3
    
    def __init__(self, db: Session):
        self.db = db
    
    def check_project_access(self, user: User, project_id: str) -> bool:
        """检查用户是否可以访问项目"""
        project = self.db.query(Project).filter(Project.id == project_id).first()
        if not project:
            return False
        
        # 超级管理员可以访问所有项目
        if user.role_id == self.SUPER_ADMIN:
            return True
        
        # 企业管理员可以访问本企业所有项目
        if user.role_id == self.ORG_ADMIN and user.org_id == project.org_id:
            return True
        
        # 项目所有者可以访问
        if project.owner_id == user.id:
            return True
        
        # 公开项目只对同企业用户可访问
        if project.visibility == 'PUBLIC' and user.org_id == project.org_id:
            return True
        
        # 检查是否为项目成员
        member = self.db.query(ProjectMember).filter(
            and_(
                ProjectMember.project_id == project_id,
                ProjectMember.user_id == user.id,
                ProjectMember.status == 'ACTIVE'
            )
        ).first()
        
        return member is not None
    
    def check_project_operation(self, user: User, project_id: str, operation: str) -> bool:
        """检查用户是否可以执行特定项目操作"""
        project = self.db.query(Project).filter(Project.id == project_id).first()
        if not project:
            return False
        
        # 超级管理员拥有所有权限
        if user.role_id == self.SUPER_ADMIN:
            return True
        
        # 对于项目修改和删除操作，只允许企业管理员和项目所有者操作
        if operation in ["edit", "delete"]:
            # 企业管理员对本企业项目拥有修改和删除权限
            if user.role_id == self.ORG_ADMIN and user.org_id == project.org_id:
                return True
            
            # 项目所有者拥有修改和删除权限
            if project.owner_id == user.id:
                return True
            
            # 其他用户（包括项目成员）不能修改或删除项目
            return False
        
        # 企业管理员对本企业项目拥有其他所有权限
        if user.role_id == self.ORG_ADMIN and user.org_id == project.org_id:
            return True
        
        # 项目所有者拥有所有权限
        if project.owner_id == user.id:
            return True
        
        # 检查项目成员权限（对于非修改/删除操作）
        member = self.db.query(ProjectMember).filter(
            and_(
                ProjectMember.project_id == project_id,
                ProjectMember.user_id == user.id,
                ProjectMember.status == 'ACTIVE'
            )
        ).first()
        
        if member:
            role_permissions = self.get_project_role_permissions(member.project_role_id)
            return operation in role_permissions
        
        return False
    
    def get_accessible_projects(self, user: User) -> List[str]:
        """获取用户可访问的项目ID列表"""
        # 超级管理员可以访问所有项目
        if user.role_id == self.SUPER_ADMIN:
            projects = self.db.query(Project.id).all()
            return [p.id for p in projects]
        
        # 企业管理员可以访问本企业所有项目
        if user.role_id == self.ORG_ADMIN:
            projects = self.db.query(Project.id).filter(
                Project.org_id == user.org_id
            ).all()
            return [p.id for p in projects]
        
        # 普通用户：自己的项目 + 本企业公开项目 + 被邀请的项目
        # 自己的项目
        own_projects = self.db.query(Project.id).filter(
            Project.owner_id == user.id
        ).all()
        
        # 本企业公开项目
        public_projects = self.db.query(Project.id).filter(
            and_(
                Project.visibility == 'PUBLIC',
                Project.org_id == user.org_id
            )
        ).all()
        
        # 被邀请的项目
        member_projects = self.db.query(ProjectMember.project_id).filter(
            and_(
                ProjectMember.user_id == user.id,
                ProjectMember.status == 'ACTIVE'
            )
        ).all()
        
        project_ids = set()
        project_ids.update([p.id for p in own_projects])
        project_ids.update([p.id for p in public_projects])
        project_ids.update([p.project_id for p in member_projects])
        
        return list(project_ids)
    
    def get_project_role_permissions(self, project_role_id: int) -> List[str]:
        """获取项目角色的权限列表"""
        role = self.db.query(ProjectRole).filter(ProjectRole.id == project_role_id).first()
        if role:
            return role.permissions
        return []
    
    def can_invite_member(self, user: User, project_id: str) -> bool:
        """检查用户是否可以邀请项目成员"""
        return self.check_project_operation(user, project_id, "manage_members")
    
    def can_manage_project(self, user: User, project_id: str) -> bool:
        """检查用户是否可以管理项目（修改、删除等）"""
        return self.check_project_operation(user, project_id, "edit")
    
    def get_invitable_users(self, user: User, project_id: str) -> List[User]:
        """获取可邀请的用户列表（同企业的普通用户，排除owner、企业管理员和已有成员）"""
        if not self.can_invite_member(user, project_id):
            return []
        
        # 获取项目信息
        project = self.db.query(Project).filter(Project.id == project_id).first()
        if not project:
            return []
        
        # 获取已经是项目成员的用户ID
        existing_members = self.db.query(ProjectMember.user_id).filter(
            and_(
                ProjectMember.project_id == project_id,
                ProjectMember.status == 'ACTIVE'
            )
        ).subquery()
        
        # 需要排除的用户ID列表
        excluded_user_ids = []
        
        # 排除项目所有者
        if project.owner_id:
            excluded_user_ids.append(project.owner_id)
        
        # 排除同企业的企业管理员（他们有天然的访问权限，不需要邀请）
        org_admin_ids = self.db.query(User.id).filter(
            and_(
                User.org_id == project.org_id,
                User.role_id == self.ORG_ADMIN,
                User.is_active == True
            )
        ).all()
        excluded_user_ids.extend([admin.id for admin in org_admin_ids])
        
        # 获取同企业的普通用户，排除已经是项目成员的用户和不能邀请的用户
        query_conditions = [
            User.org_id == project.org_id,
            User.role_id == self.NORMAL_USER,
            User.is_active == True,
            ~User.id.in_(existing_members)
        ]
        
        # 如果有需要排除的用户ID，添加排除条件
        if excluded_user_ids:
            query_conditions.append(~User.id.in_(excluded_user_ids))
        
        invitable_users = self.db.query(User).filter(
            and_(*query_conditions)
        ).all()
        
        return invitable_users
    
    def get_user_project_role(self, user_id: int, project_id: str) -> Optional[ProjectRole]:
        """获取用户在项目中的角色"""
        # 检查是否为项目所有者
        project = self.db.query(Project).filter(Project.id == project_id).first()
        if project and project.owner_id == user_id:
            return self.db.query(ProjectRole).filter(ProjectRole.id == self.PROJECT_OWNER).first()
        
        # 检查是否为项目成员
        member = self.db.query(ProjectMember).filter(
            and_(
                ProjectMember.project_id == project_id,
                ProjectMember.user_id == user_id,
                ProjectMember.status == 'ACTIVE'
            )
        ).first()
        
        if member:
            return self.db.query(ProjectRole).filter(ProjectRole.id == member.project_role_id).first()
        
        return None
    
    def validate_project_role_for_invitation(self, role_id: int) -> bool:
        """验证邀请时的项目角色是否有效（只能是协作者或观察者）"""
        return role_id in [self.PROJECT_COLLABORATOR, self.PROJECT_OBSERVER]

    def check_conversation_access(self, user: User, conversation) -> bool:
        """检查用户是否可以访问会话"""
        # 首先检查项目访问权限
        if not self.check_project_access(user, conversation.project_id):
            return False
        
        # 超级管理员可以访问所有会话
        if user.role_id == self.SUPER_ADMIN:
            return True
        
        # 企业级共享会话：user_id为0时，该项目所属企业的所有用户都可以访问
        if conversation.user_id == 0:
            # 获取项目信息，检查项目是否属于用户的企业
            project = self.db.query(Project).filter(Project.id == conversation.project_id).first()
            if project and project.org_id == user.org_id:
                return True
        
        # 企业管理员可以访问本企业所有会话
        if user.role_id == self.ORG_ADMIN:
            # 获取会话创建者信息
            conversation_creator = self.db.query(User).filter(User.id == conversation.user_id).first()
            if conversation_creator and conversation_creator.org_id == user.org_id:
                return True
        
        # 普通用户只能访问自己创建的会话
        if conversation.user_id == user.id:
            return True
        
        return False

    def get_accessible_conversations_filter(self, user: User):
        """获取用户可访问的会话过滤条件"""
        from app.models.conversation import Conversation
        from app.models.user import User as UserModel
        from app.models.project import Project
        from sqlalchemy import or_
        
        # 超级管理员可以访问所有会话
        if user.role_id == self.SUPER_ADMIN:
            return True  # 返回True表示无过滤条件
        
        # 企业管理员可以访问：1. 本企业所有用户创建的会话 2. 本企业项目中user_id为0的企业级共享会话
        if user.role_id == self.ORG_ADMIN:
            # 获取本企业所有用户的ID
            org_user_ids = self.db.query(UserModel.id).filter(
                UserModel.org_id == user.org_id
            ).subquery()
            
            # 获取本企业的项目ID列表
            org_project_ids = self.db.query(Project.id).filter(
                Project.org_id == user.org_id
            ).subquery()
            
            return or_(
                Conversation.user_id.in_(org_user_ids),  # 本企业用户创建的会话
                and_(
                    Conversation.user_id == 0,  # 企业级共享会话
                    Conversation.project_id.in_(org_project_ids)  # 且项目属于本企业
                )
            )
        
        # 普通用户可以访问：1. 自己创建的会话 2. 可访问项目中user_id为0的企业级共享会话
        else:
            # 获取用户可访问的项目ID列表
            accessible_project_ids = self.get_accessible_projects(user)
            
            return or_(
                Conversation.user_id == user.id,  # 自己创建的会话
                and_(
                    Conversation.user_id == 0,  # 企业级共享会话
                    Conversation.project_id.in_(accessible_project_ids)  # 且项目可访问
                )
            )


def get_permission_service(db: Session) -> PermissionService:
    """获取权限服务实例"""
    return PermissionService(db) 