"""
分析步骤拦截器
==============
在流式分析过程中实时拦截和保存分析步骤，确保完整的时间轴记录
"""

import json
import re
from typing import Dict, Any, Optional
from datetime import datetime
from sqlalchemy.orm import Session

from app.services.analysis_step_service import AnalysisStepService
from app.models.analysis import AnalysisStepDetail
from app.core.logger import log
from app.utils.time_utils import get_shanghai_time


class StepInterceptor:
    """分析步骤拦截器"""
    
    def __init__(self, db: Session, analysis_id: str):
        self.db = db
        self.analysis_id = analysis_id
        self.step_service = AnalysisStepService(db)
        self.step_counter = 0
    
    def intercept_event(self, event_type: str, event_data: Dict[str, Any]) -> bool:
        """拦截并保存流式事件对应的分析步骤"""
        try:
            self.step_counter += 1
            
            # 根据事件类型保存对应的步骤
            if event_type == 'start':
                return self._save_start_step()
            
            elif event_type == 'analysis_created':
                return self._save_analysis_created_step(event_data)
            
            elif event_type == 'tools_loaded':
                return self._save_tools_loaded_step(event_data)
            
            elif event_type == 'sql_executor_initialized':
                return self._save_sql_executor_step(event_data)
            
            elif event_type == 'intent_analysis_started':
                return self._save_intent_analysis_started_step()
            
            elif event_type == 'intent_analyzed':
                return self._save_intent_analyzed_step(event_data)
            
            # 新增：意图确认相关事件
            elif event_type == 'intent_confirmation_request':
                return self._save_intent_confirmation_request_step(event_data)
            
            elif event_type == 'user_intent_confirmation':
                return self._save_user_intent_confirmation_step(event_data)
            
            elif event_type == 'intent_confirmation_completed':
                # @fixed_implementation_start
                # 实现标识: 避免重复意图确认记录
                # 功能描述: 跳过intent_confirmation_completed事件，避免与step_completed重复记录
                # 修复内容: 意图确认已通过step_completed事件记录，无需重复处理
                # 状态: 已确认 (CONFIRMED_IMPLEMENTATION)
                # @fixed_implementation_end
                return True  # 跳过，避免重复记录
            
            # 新增：澄清交互相关事件
            elif event_type == 'clarification':
                return self._save_clarification_request_step(event_data)
            
            elif event_type == 'clarification_answered':
                return self._save_clarification_answered_step(event_data)
            
            # 新增：图表生成相关事件
            elif event_type == 'chart_generated':
                return self._save_chart_generated_step(event_data)
            
            elif event_type == 'planning_started' or event_type == 'planning':
                return self._save_planning_step(event_data)
            
            elif event_type == 'planning_decision':
                return self._save_planning_decision_step(event_data)
            
            elif event_type == 'plan_created':
                return self._save_plan_created_step(event_data)
            
            elif event_type == 'step_started':
                return self._save_step_started(event_data)
            
            elif event_type == 'step_completed':
                return self._save_step_completed(event_data)
            
            elif event_type == 'report_generation_started':
                return self._save_report_generation_started_step()
            
            elif event_type == 'report_generated':
                return self._save_report_generated_step(event_data)
            
            elif event_type == 'completed':
                # @fixed_implementation_start
                # 实现标识: 跳过分析完成步骤记录
                # 功能描述: 不记录completed事件，避免显示多余的"分析完成"步骤
                # 修复内容: 分析完成状态由系统自动管理，无需在时间轴中显示
                # 状态: 已确认 (CONFIRMED_IMPLEMENTATION)
                # @fixed_implementation_end
                return True  # 跳过，不记录分析完成步骤
            
            elif event_type == 'evaluation_completed':
                return self._save_evaluation_completed_step(event_data)

            elif event_type == 'insight_discovered':
                return self._save_insight_discovered_step(event_data)

            elif event_type == 'feedback_applied':
                return self._save_feedback_applied_step(event_data)
            
            elif event_type == 'error':
                return self._save_error_step(event_data)
            
            elif event_type == 'step_error':
                return self._save_step_error(event_data)
            
            return True
            
        except Exception as e:
            log.error(f"拦截步骤事件失败: {event_type}, {str(e)}")
            return False
    
    def _save_start_step(self) -> bool:
        """保存分析开始步骤"""
        return self.step_service.save_step(
            analysis_id=self.analysis_id,
            step_id='start',
            step_name='steps.start',  # 改为翻译键
            step_type='system',
            status='finish',
            step_order=self.step_counter
        )
    
    def _save_analysis_created_step(self, event_data: Dict[str, Any]) -> bool:
        """保存创建分析记录步骤"""
        return self.step_service.save_step(
            analysis_id=self.analysis_id,
            step_id='analysis_created',
            step_name='steps.analysisCreated',  # 改为翻译键
            step_type='system',
            status='finish',
            step_order=self.step_counter
        )
    
    def _save_tools_loaded_step(self, event_data: Dict[str, Any]) -> bool:
        """保存工具加载步骤"""
        tool_count = event_data.get('tool_count', 0)
        return self.step_service.save_step(
            analysis_id=self.analysis_id,
            step_id='tools_loaded',
            step_name=f'steps.toolsLoaded|{{"count":{tool_count}}}',  # 改为翻译键+参数格式
            step_type='system',
            status='finish',
            step_order=self.step_counter,
            description=f'成功加载{tool_count}个可用的分析工具'
        )
    
    def _save_sql_executor_step(self, event_data: Dict[str, Any]) -> bool:
        """保存SQL执行器初始化步骤"""
        schema_loaded = event_data.get('schema_loaded', False)
        description = '数据库结构已加载' if schema_loaded else '数据库结构加载失败'
        
        return self.step_service.save_step(
            analysis_id=self.analysis_id,
            step_id='sql_executor_initialized',
            step_name='steps.sqlExecutorInitialized',  # 改为翻译键
            step_type='system',
            status='finish',
            step_order=self.step_counter,
            description=description
        )
    
    def _save_intent_analysis_started_step(self) -> bool:
        """保存意图分析开始步骤"""
        return self.step_service.save_step(
            analysis_id=self.analysis_id,
            step_id='intent_analysis_started',
            step_name='steps.intentAnalysisStarted',  # 改为翻译键
            step_type='system',
            status='finish',
            step_order=self.step_counter
        )
    
    def _save_intent_analyzed_step(self, event_data: Dict[str, Any]) -> bool:
        """保存意图分析完成步骤"""
        execution_time = event_data.get('execution_time')
        
        return self.step_service.save_step(
            analysis_id=self.analysis_id,
            step_id='intent_analyzed',
            step_name='steps.intentAnalyzed',  # 改为翻译键
            step_type='system',
            status='finish',
            step_order=self.step_counter,
            has_intent_data=True,
            execution_time=execution_time
        )
    
    def _save_intent_confirmation_request_step(self, event_data: Dict[str, Any]) -> bool:
        """保存意图确认请求步骤"""
        message = event_data.get('message', '请确认分析意图和计划')
        intent_analysis = event_data.get('intent_analysis', {})
        user_query = event_data.get('user_query', '')

        # 提取意图分析摘要作为描述
        description = intent_analysis.get('intent_description', '等待用户确认分析意图和执行计划')[:200]

        # 构建精简的参数数据，避免存储完整的意图分析大数据结构
        parameters = {
            'message': message,
            'user_query': user_query,
            'intent_summary': intent_analysis.get('intent_description', '')[:200] if intent_analysis else '',
            'needs_clarification': intent_analysis.get('needs_clarification', False),
            'question_count': len(intent_analysis.get('clarification_questions', [])) if intent_analysis else 0
        }

        # 构建完整的交互数据，包含前端渲染所需的完整意图分析信息
        interaction_data = {
            'message': message,
            'user_query': user_query,
            # 保存完整的意图分析数据供前端渲染使用
            'intent_analysis': intent_analysis,
            # 同时保存精简摘要信息
            'intent_summary': intent_analysis.get('intent_description', '')[:200] if intent_analysis else '',
            'needs_clarification': intent_analysis.get('needs_clarification', False),
            'question_count': len(intent_analysis.get('clarification_questions', [])) if intent_analysis else 0
        }

        return self.step_service.save_step(
            analysis_id=self.analysis_id,
            step_id='intent_confirmation_request',
            step_name='intentConfirmation.waitingForConfirmation',  # 改为翻译键
            step_type='interaction',
            status='wait',
            step_order=self.step_counter,
            description=description,
            parameters=parameters,
            has_intent_data=True,
            interaction_type='intent_confirmation',
            interaction_data=interaction_data
        )
    
    def _save_user_intent_confirmation_step(self, event_data: Dict[str, Any]) -> bool:
        """更新现有的意图确认步骤，而不是创建新步骤"""
        original_query = event_data.get('original_query', '')
        user_adjustment = event_data.get('user_adjustment', '')
        has_user_modification = event_data.get('has_user_modification', False)
        intent_analysis = event_data.get('intent_analysis', {})
        clarification_answers = event_data.get('clarification_answers', {})

        # 构建步骤名称和描述
        if has_user_modification and user_adjustment:
            step_name = "intentConfirmation.completed"  # 改为翻译键
            description = f"用户调整意图: {user_adjustment[:100]}..." if len(user_adjustment) > 100 else f"用户调整意图: {user_adjustment}"
        else:
            step_name = "intentConfirmation.completed"  # 改为翻译键
            description = "用户确认原始意图分析，按计划执行"

        # 构建精简的意图确认数据，避免重复存储大量数据
        confirmation_data = {
            'original_query': original_query,
            'user_adjustment': user_adjustment,
            'has_user_modification': has_user_modification,
            'confirmation_completed': True
        }

        # 构建完整的交互数据，包含前端渲染所需的完整意图分析信息
        interaction_data = {
            'original_query': original_query,
            'user_adjustment': user_adjustment,
            'has_user_modification': has_user_modification,
            'confirmation_completed': True,
            # 保存完整的意图分析数据供前端渲染使用
            'intent_analysis': intent_analysis,
            'clarification_answers': clarification_answers,
            # 同时保存精简摘要信息
            'intent_summary': intent_analysis.get('intent_description', '')[:200] if intent_analysis else '',
            'clarification_count': len(clarification_answers) if clarification_answers else 0
        }

        # 更新现有的intent_confirmation_request步骤
        return self.step_service.update_step_with_result(
            analysis_id=self.analysis_id,
            step_id='intent_confirmation_request',
            status='finish',
            step_name=step_name,
            description=description,
            tool_name='意图确认',
            parameters=confirmation_data,
            interaction_data=interaction_data
        )

    def _save_intent_confirmation_completed_step(self, event_data: Dict[str, Any]) -> bool:
        """保存意图确认完成步骤"""
        # 先更新意图确认请求步骤状态
        self.step_service.update_step_status(
            analysis_id=self.analysis_id,
            step_id='intent_confirmation_request',
            status='finish'
        )
        
        # 保存意图确认完成步骤
        original_query = event_data.get('original_query', '')
        user_adjustment = event_data.get('user_adjustment', '')
        enhanced_query = event_data.get('enhanced_query', '')
        
        description = f"用户调整: {user_adjustment}" if user_adjustment else "用户确认按计划执行"
        
        return self.step_service.save_step(
            analysis_id=self.analysis_id,
            step_id='intent_confirmation_completed',
            step_name='intentConfirmation.completed',  # 改为翻译键
            step_type='interaction',
            status='finish',
            step_order=self.step_counter,
            description=description,
            parameters={
                'original_query': original_query,
                'user_adjustment': user_adjustment,
                'enhanced_query': enhanced_query
            },
            has_result=True,
            result_key='intent_confirmation_completed',
            interaction_type='intent_confirmation',
            interaction_data={
                'original_query': original_query,
                'user_adjustment': user_adjustment,
                'enhanced_query': enhanced_query,
                'completed': True
            }
        )
    
    def _save_clarification_request_step(self, event_data: Dict[str, Any]) -> bool:
        """保存澄清请求步骤"""
        questions = event_data.get('questions', [])
        message = event_data.get('message', '需要您提供更多信息以继续分析')
        context = event_data.get('context', {})
        
        # 构建问题摘要
        questions_summary = []
        if isinstance(questions, list):
            for q in questions[:3]:  # 最多显示3个问题
                if isinstance(q, dict):
                    questions_summary.append(q.get('question', str(q)))
                else:
                    questions_summary.append(str(q))
        
        description = f"需要澄清: {'; '.join(questions_summary)}" if questions_summary else message
        
        return self.step_service.save_step(
            analysis_id=self.analysis_id,
            step_id='clarification_request',
            step_name='steps.clarificationRequest',  # 改为翻译键
            step_type='tool',
            status='wait',
            step_order=self.step_counter,
            description=description,
            tool_name='智能交互',
            parameters={
                'questions': questions,
                'message': message,
                'context': context,
                'reasoning': message  # 添加推理信息
            },
            interaction_type='clarification',
            interaction_data={
                'questions': questions,
                'message': message,
                'context': context
            }
        )
    
    def _save_clarification_answered_step(self, event_data: Dict[str, Any]) -> bool:
        """保存澄清回答步骤 - 更新智能交互步骤而不是创建新步骤"""
        answers = event_data.get('answers', {})
        questions = event_data.get('questions', [])
        
        # 构建回答摘要 - 优化显示格式
        answer_summary = []
        if isinstance(answers, dict) and answers:
            for key, value in list(answers.items())[:2]:  # 最多显示2个回答
                # 尝试从questions中找到对应的问题文本
                question_text = key
                if isinstance(questions, list):
                    for q in questions:
                        if isinstance(q, dict) and q.get('field_name') == key:
                            question_text = q.get('question', key)
                            break
                answer_summary.append(f"{question_text}: {value}")
        
        description = f"✅ 智能交互已完成，用户回答: {'; '.join(answer_summary)}" if answer_summary else "✅ 智能交互已完成，用户提供了补充信息"
        
        # 更新智能交互步骤，添加用户回复数据
        return self.step_service.update_step_with_user_response(
            analysis_id=self.analysis_id,
            step_id='clarification_request',
            status='finish',
            description=description,
            user_answers=answers,
            user_questions=questions
        )
    
    def _save_chart_generated_step(self, event_data: Dict[str, Any]) -> bool:
        """保存图表生成步骤"""
        chart_data = event_data.get('chart_data', {})
        original_step_id = event_data.get('step_id', 'chart_generated')
        analysis_id = event_data.get('analysis_id', self.analysis_id)
        
        # BUG修复: 提取原始step_id，避免与step_completed事件冲突
        # 修复策略: 图表Agent生成的step_id格式为"{agent}_chart_step_{原始step_id}"，需要提取原始部分
        # 影响范围: app/services/step_interceptor.py:416-425
        # 修复日期: 2025-01-29
        if original_step_id.startswith('moe_chart_step_'):
            step_id = original_step_id.replace('moe_chart_step_', '')
            log.info(f"🔧 提取MoE Agent原始step_id: {original_step_id} → {step_id}")
        elif original_step_id.startswith('simple_chart_step_'):
            step_id = original_step_id.replace('simple_chart_step_', '')
            log.info(f"🔧 提取Simple Agent原始step_id: {original_step_id} → {step_id}")
        else:
            step_id = original_step_id
        
        # @fixed_implementation_start
        # 实现标识: 智能图表生成Agent步骤保存修复
        # 功能描述: 修复智能图表生成Agent的步骤保存逻辑，确保图表数据正确保存到数据库
        # 修复内容: 完整保存图表数据到analysis_step_details和tool_executions表，支持前端展示和持久化
        # 状态: 已确认 (CONFIRMED_IMPLEMENTATION)
        # @fixed_implementation_end
        
        # 提取图表信息
        chart_info = chart_data.get('data', {})
        chart_type = chart_info.get('chart_type', 'echarts')
        chart_count = chart_info.get('total_charts', 1)
        chart_config = chart_info.get('chart_config', {})
        auto_generated = chart_info.get('auto_generated', False)
        source_tool = chart_info.get('source_tool', '未知工具')
        
        # 构建步骤名称和描述
        step_name = 'tools.intelligentChartGeneration'  # 改为翻译键
        if auto_generated:
            description = f'基于 {source_tool} 的执行结果，智能生成了数据可视化图表'
        else:
            description = f'智能图表生成工具成功生成了数据可视化图表'
        
        try:
            # 保存步骤到analysis_step_details表
            step_saved = self.step_service.save_step(
                analysis_id=self.analysis_id,
                step_id=step_id,
                step_name=step_name,
                step_type='tool',
                status='finish',
                step_order=self.step_counter,
                description=description,
                tool_name='智能图表生成',
                parameters={
                    'chart_type': chart_type,
                    'chart_count': chart_count,
                    'auto_generated': auto_generated,
                    'source_tool': source_tool
                },
                has_result=True,
                result_key=step_id,
                has_chart_data=True,
                interaction_type='chart_display',
                interaction_data=self._make_json_serializable({
                    'chart_type': chart_type,
                    'chart_count': chart_count,
                    'chart_data': chart_data,
                    'auto_generated': auto_generated,
                    'source_tool': source_tool
                })
            )
            
            if step_saved:
                # 同时保存到tool_executions表，确保结果可以被前端获取
                tool_execution_saved = self._save_tool_execution_result(
                    step_id=step_id,
                    tool_name='智能图表生成',
                    result=chart_data  # 保存完整的图表数据
                )
                
                if tool_execution_saved:
                    log.info(f"智能图表生成步骤保存成功: {step_id}, 图表类型: {chart_type}")
                    return True
                else:
                    log.warning(f"智能图表生成步骤保存成功，但工具执行结果保存失败: {step_id}")
                    return True  # 步骤已保存，即使工具执行结果保存失败也返回True
            else:
                log.error(f"智能图表生成步骤保存失败: {step_id}")
                return False
                
        except Exception as e:
            log.error(f"保存智能图表生成步骤时出错: {str(e)}")
            return False
    
    def _save_planning_step(self, event_data: Dict[str, Any]) -> bool:
        """保存规划分析流程步骤 - 跳过，不保存到数据库"""
        # 不保存规划流程步骤，直接返回True
        return True
    
    def _save_planning_decision_step(self, event_data: Dict[str, Any]) -> bool:
        """保存规划决策步骤 - 跳过，由plan_created统一处理"""
        # planning_decision事件现在跳过，所有reasoning由plan_created事件统一处理
        # 这样避免重复保存，并且确保所有工具执行的reasoning都被记录
        return True
    
    def _save_plan_created_step(self, event_data: Dict[str, Any]) -> bool:
        """保存plan_created步骤 - 处理final_answer和tool类型"""
        action_type = event_data.get('action_type', '')
        reasoning = event_data.get('reasoning', '')
        tool_name = event_data.get('tool_name', '')
        
        # 处理final_answer类型的plan_created事件
        if action_type == 'final_answer' and reasoning:
            # 检查reasoning是否包含错误信息
            if "错误" in reasoning or "失败" in reasoning or "Error" in reasoning or "error" in reasoning:
                step_name = "分析规划生成失败"
                error_reasoning = reasoning
            else:
                step_name = reasoning  # 使用reasoning作为步骤名称
                error_reasoning = None
            
            return self.step_service.save_step(
                analysis_id=self.analysis_id,
                step_id='final_answer_planning',
                step_name=step_name,
                step_type='planning',
                status='finish' if not error_reasoning else 'error',
                step_order=self.step_counter,
                tool_name=tool_name,
                reasoning=reasoning,
                error_message=error_reasoning
            )
        
        # 处理tool类型的plan_created事件（所有工具执行的reasoning）
        elif action_type == 'tool' and reasoning and tool_name:
            # 为每个工具执行创建独立的规划步骤，使用step_counter确保唯一性
            step_id = f"tool_planning_{self.step_counter}"
            
            # 检查reasoning是否包含错误信息
            if "错误" in reasoning or "失败" in reasoning or "Error" in reasoning or "error" in reasoning:
                step_name = f"工具规划生成失败 - {tool_name}"
                error_reasoning = reasoning
            else:
                step_name = reasoning  # 使用reasoning作为步骤名称
                error_reasoning = None
            
            return self.step_service.save_step(
                analysis_id=self.analysis_id,
                step_id=step_id,
                step_name=step_name,
                step_type='planning',
                status='finish' if not error_reasoning else 'error',
                step_order=self.step_counter,
                tool_name=tool_name,
                reasoning=reasoning,
                error_message=error_reasoning
            )
        else:
            # 其他类型的plan_created事件不保存，或者已经通过planning_decision处理
            return True
    
    def _save_step_started(self, event_data: Dict[str, Any]) -> bool:
        """保存工具执行开始步骤"""
        step_id = event_data.get('step_id', f'step_{self.step_counter}')
        tool_name = event_data.get('tool_name', '未知工具')
        parameters = event_data.get('parameters', {})
        
        # @fixed_implementation_start
        # 实现标识: 避免重复意图确认工具记录
        # 功能描述: 跳过意图确认完成后的重复意图确认工具记录
        # 修复内容: 检查是否是意图确认完成后的重复记录，如果是则跳过
        # 状态: 已确认 (CONFIRMED_IMPLEMENTATION)
        # @fixed_implementation_end
        
        # 检查是否是重复的意图确认工具
        if tool_name == '意图确认' and 'intent_confirmation_' in step_id:
            # 检查是否已经有意图分析步骤
            from app.models.analysis import AnalysisStepDetail
            existing_intent_steps = self.db.query(AnalysisStepDetail).filter(
                AnalysisStepDetail.analysis_id == self.analysis_id,
                AnalysisStepDetail.tool_name == '意图确认',
                AnalysisStepDetail.step_id.like('intent_analysis_%')
            ).count()
            
            if existing_intent_steps > 0:
                log.info(f"跳过重复的意图确认工具记录: {step_id}")
                return True  # 跳过重复记录
        
        return self.step_service.save_step(
            analysis_id=self.analysis_id,
            step_id=step_id,
            step_name=f'steps.executeTool|{{"toolName":"{tool_name}"}}',  # 改为翻译键+参数格式
            step_type='tool',
            status='process',
            step_order=self.step_counter,
            tool_name=tool_name,
            parameters=parameters
        )
    
    def _save_step_completed(self, event_data: Dict[str, Any]) -> bool:
        """更新工具执行完成步骤"""
        step_id = event_data.get('step_id', f'step_{self.step_counter}')
        tool_name = event_data.get('tool_name', '未知工具')
        result = event_data.get('result', {})
        reasoning = event_data.get('reasoning', '')
        
        # 提取执行时间
        execution_time = None
        if isinstance(result, dict):
            execution_time = result.get('execution_time') or result.get('result', {}).get('execution_time')
        
        # 特殊处理：如果是澄清工具的结果，需要特殊标记
        if tool_name == '智能交互' and isinstance(result, dict):
            # 检查是否是澄清请求结果
            if (result.get('action_type') == 'clarification' or 
                result.get('display_format') == 'clarification' or
                (result.get('data', {}).get('questions') and len(result.get('data', {}).get('questions', [])) > 0)):
                
                # 这是澄清请求，更新步骤状态为等待
                return self.step_service.update_step_status(
                    analysis_id=self.analysis_id,
                    step_id=step_id,
                    status='wait',
                    execution_time=execution_time,
                    reasoning=reasoning,
                    step_name=f'steps.executeTool|{{"toolName":"{tool_name}"}} (steps.waitingForClarification)'  # 改为翻译键格式
                )
        
        # @fixed_implementation_start
        # 实现标识: 工具结果保存修复
        # 功能描述: 确保工具执行完成时结果被正确保存，支持结果展示
        # 修复内容: 在更新步骤状态时同时保存工具结果和相关标记
        # 状态: 已确认 (CONFIRMED_IMPLEMENTATION)
        # @fixed_implementation_end
        
        # 检查是否有结果需要保存
        has_result = bool(result)
        result_key = step_id if has_result else None
        
        # 特殊处理图表生成工具
        has_chart_data = False
        if tool_name == '智能图表生成' and isinstance(result, dict):
            # 检查是否包含图表数据
            chart_data = result.get('chart_data') or result.get('data', {}).get('chart_data')
            if chart_data:
                has_chart_data = True
                log.info(f"检测到图表数据: {step_id}")
        
        # 更新步骤状态，包含结果信息
        try:
            step = self.db.query(AnalysisStepDetail).filter(
                AnalysisStepDetail.analysis_id == self.analysis_id,
                AnalysisStepDetail.step_id == step_id
            ).first()
            
            if step:
                step.status = 'finish'
                step.end_time = get_shanghai_time()
                step.updated_at = get_shanghai_time()
                
                if execution_time is not None:
                    step.execution_time = execution_time
                
                if reasoning:
                    step.reasoning = reasoning
                
                # 保存结果信息
                if has_result:
                    step.has_result = 1
                    step.result_key = result_key
                
                # 保存图表数据标记
                if has_chart_data:
                    step.has_chart_data = 1
                    step.interaction_type = 'chart_display'
                    step.interaction_data = {
                        'chart_type': result.get('chart_type', 'multi_charts'),
                        'chart_count': result.get('chart_count', '1'),
                        'chart_data': result.get('chart_data') or result.get('data', {}).get('chart_data')
                    }
                
                self.db.commit()
                log.info(f"更新工具执行步骤: {step_id} -> finish, 有结果: {has_result}, 有图表: {has_chart_data}")
                
                # 同时保存到tool_executions表（如果需要）
                if has_result:
                    self._save_tool_execution_result(step_id, tool_name, result)
                
                return True
            else:
                log.warning(f"未找到步骤: {step_id}")
                return False
                
        except Exception as e:
            log.error(f"更新工具执行步骤失败: {str(e)}")
            self.db.rollback()
            return False
    
    def _save_tool_execution_result(self, step_id: str, tool_name: str, result: Dict[str, Any]) -> bool:
        """保存工具执行结果到tool_executions表"""
        try:
            from app.models.analysis import ToolExecution
            
            # 检查是否已存在
            existing = self.db.query(ToolExecution).filter(
                ToolExecution.analysis_id == self.analysis_id,
                ToolExecution.step_id == step_id
            ).first()
            
            if existing:
                # 更新现有记录
                existing.result = result
                existing.updated_at = get_shanghai_time()
            else:
                # 创建新记录 - 注意：ToolExecution模型没有tool_name字段，需要通过tool_id关联
                # 先查找或创建一个虚拟工具记录
                tool_id = self._get_or_create_virtual_tool_id(tool_name)

                tool_execution = ToolExecution(
                    analysis_id=self.analysis_id,
                    tool_id=tool_id,
                    step_id=step_id,
                    parameters={},  # 图表生成工具没有输入参数
                    result=result,
                    status='success'
                )
                self.db.add(tool_execution)
            
            self.db.commit()
            log.info(f"保存工具执行结果: {step_id} -> {tool_name}")
            return True
            
        except Exception as e:
            log.error(f"保存工具执行结果失败: {str(e)}")
            self.db.rollback()
            return False
    
    def _save_report_generation_started_step(self) -> bool:
        """保存报告生成开始步骤"""
        return self.step_service.save_step(
            analysis_id=self.analysis_id,
            step_id='generating_report',
            step_name='steps.reportGenerationStarted',  # 改为翻译键
            step_type='report',
            status='process',
            step_order=self.step_counter
        )
    
    def _save_report_generated_step(self, event_data: Dict[str, Any]) -> bool:
        """保存报告生成完成步骤"""
        execution_time = event_data.get('execution_time')
        
        # 先更新generating_report步骤状态
        self.step_service.update_step_status(
            analysis_id=self.analysis_id,
            step_id='generating_report',
            status='finish',
            execution_time=execution_time
        )
        
        # 然后添加报告生成完成步骤
        return self.step_service.save_step(
            analysis_id=self.analysis_id,
            step_id='report_generated',
            step_name='steps.reportGenerated',  # 改为翻译键
            step_type='report',
            status='finish',
            step_order=self.step_counter + 1,
            has_report=True,
            execution_time=execution_time
        )
    
    def _save_completed_step(self) -> bool:
        """保存分析完成步骤"""
        return self.step_service.save_step(
            analysis_id=self.analysis_id,
            step_id='completed',
            step_name='steps.analysisCompleted',  # 改为翻译键
            step_type='system',
            status='finish',
            step_order=self.step_counter
        )
    
    def _save_error_step(self, event_data: Dict[str, Any]) -> bool:
        """保存错误步骤"""
        error_message = event_data.get('message', '未知错误')
        
        return self.step_service.save_step(
            analysis_id=self.analysis_id,
            step_id='error',
            step_name='steps.analysisError',  # 改为翻译键
            step_type='error',
            status='error',
            step_order=self.step_counter,
            error_message=error_message
        )
    
    def _save_step_error(self, event_data: Dict[str, Any]) -> bool:
        """保存步骤错误 - 将错误结果像正常结果一样保存到tool_executions表"""
        step_id = event_data.get('step_id', f'error_step_{self.step_counter}')
        tool_name = event_data.get('tool_name', '未知工具')
        error_message = event_data.get('error_message', event_data.get('message', '步骤执行失败'))
        can_continue = event_data.get('can_continue', True)
        original_parameters = event_data.get('parameters', {})  # 获取原始工具执行参数
        
        # 构建错误结果，格式与正常工具执行结果一致
        # 注意：不包含error_traceback，避免在前端暴露系统内部信息
        error_result = {
            "success": False,
            "error": error_message,
            "error_type": event_data.get('error_type', 'Exception'),
            "severity": event_data.get('severity', 'medium'),
            "can_continue": can_continue,
            "data": None,
            "execution_time": None
        }
        
        # 保存步骤到analysis_step_details表
        step_saved = self.step_service.save_step(
            analysis_id=self.analysis_id,
            step_id=step_id,
            step_name=event_data.get('step_name', f'steps.executeTool|{{"toolName":"{tool_name}"}}'),  # 改为翻译键格式
            step_type='tool',
            status='error',
            step_order=self.step_counter,
            tool_name=tool_name,
            error_message=error_message,
            has_result=True,  # 标记有结果（错误结果）
            result_key=step_id,  # 使用step_id作为结果键
            # 保存原始工具执行参数（如SQL语句）到parameters字段
            parameters=original_parameters
        )
        
        if step_saved:
            # 同时保存错误结果到tool_executions表，就像正常的工具执行结果一样
            # 但是在tool_executions表中，我们需要创建一个包含原始参数的记录
            self._save_tool_execution_error(step_id, tool_name, original_parameters, error_result)
        
        return step_saved
    
    def _save_tool_execution_error(self, step_id: str, tool_name: str, parameters: Dict[str, Any], error_result: Dict[str, Any]) -> bool:
        """保存工具执行错误到tool_executions表，包含原始参数和错误结果"""
        try:
            from app.models.analysis import ToolExecution
            
            # 检查是否已存在
            existing = self.db.query(ToolExecution).filter(
                ToolExecution.analysis_id == self.analysis_id,
                ToolExecution.step_id == step_id
            ).first()
            
            if existing:
                # 更新现有记录
                existing.parameters = parameters  # 保存原始参数
                existing.result = error_result    # 保存错误结果
                existing.status = 'failed'       # 标记为失败状态
                existing.error_message = error_result.get('error', '执行失败')
                existing.updated_at = get_shanghai_time()
            else:
                # 创建新记录 - 修复tool_name字段问题
                tool_id = self._get_or_create_virtual_tool_id(tool_name)

                tool_execution = ToolExecution(
                    analysis_id=self.analysis_id,
                    tool_id=tool_id,
                    step_id=step_id,
                    parameters=parameters,        # 保存原始参数（包含SQL语句）
                    result=error_result,          # 保存错误结果
                    status='failed',              # 标记为失败状态
                    error_message=error_result.get('error', '执行失败')
                )
                self.db.add(tool_execution)
            
            self.db.commit()
            log.info(f"保存工具执行错误: {step_id} -> {tool_name} (包含原始参数)")
            return True

        except Exception as e:
            log.error(f"保存工具执行错误失败: {str(e)}")
            self.db.rollback()
            return False

    def _get_or_create_virtual_tool_id(self, tool_name: str) -> str:
        """获取或创建虚拟工具ID"""
        try:
            from app.models.tool import Tool, ToolType

            # 查找是否已存在同名工具
            existing_tool = self.db.query(Tool).filter(Tool.name == tool_name).first()
            if existing_tool:
                return existing_tool.id

            # 🚀 对于图表生成工具，直接返回虚拟ID，不保存到数据库
            if tool_name == "智能图表生成":
                virtual_id = f"virtual-chart-tool-{hash(tool_name) % 100000}"
                log.info(f"使用虚拟工具ID（不保存数据库）: {tool_name} -> {virtual_id}")
                return virtual_id

            # 获取第一个可用的数据源ID作为默认值
            from app.models.data_source import DataSource
            default_data_source = self.db.query(DataSource).first()
            default_data_source_id = default_data_source.id if default_data_source else "default-datasource"

            # 创建虚拟工具记录
            virtual_tool = Tool(
                name=tool_name,
                description=f"系统生成的虚拟工具: {tool_name}",
                tool_type=ToolType.CUSTOM,
                template="",
                parameters=[],
                data_source_id=default_data_source_id  # 使用默认数据源ID
            )

            self.db.add(virtual_tool)
            self.db.commit()
            self.db.refresh(virtual_tool)

            log.info(f"创建虚拟工具: {tool_name} -> {virtual_tool.id}")
            return virtual_tool.id

        except Exception as e:
            log.error(f"创建虚拟工具失败: {str(e)}")
            # 返回一个虚拟的工具ID，避免外键约束错误
            virtual_id = f"virtual-tool-{hash(tool_name) % 100000}"
            log.info(f"使用fallback虚拟工具ID: {tool_name} -> {virtual_id}")
            return virtual_id

    def _make_json_serializable(self, obj: Any) -> Any:
        """
        确保对象可以JSON序列化

        Args:
            obj: 原始对象

        Returns:
            Any: JSON兼容的对象
        """
        # 直接使用安全转换，避免重复序列化测试
        return self._convert_to_safe_json(obj)

    def _convert_to_safe_json(self, obj: Any) -> Any:
        """
        递归转换对象为JSON安全格式

        Args:
            obj: 原始对象

        Returns:
            Any: JSON安全的对象
        """
        if obj is None:
            return None
        elif isinstance(obj, (str, int, float, bool)):
            return obj
        elif hasattr(obj, 'isoformat'):  # datetime对象
            return obj.isoformat()
        elif hasattr(obj, 'item'):  # numpy类型
            return obj.item()
        elif isinstance(obj, dict):
            return {key: self._convert_to_safe_json(value) for key, value in obj.items()}
        elif isinstance(obj, (list, tuple)):
            return [self._convert_to_safe_json(item) for item in obj]
        else:
            # 对于其他类型，转换为字符串
            try:
                return str(obj)
            except Exception:
                return f"<无法序列化的对象: {type(obj).__name__}>"
    
    def _save_feedback_applied_step(self, event_data: Dict[str, Any]) -> bool:
        """保存用户反馈应用步骤"""
        adjustment_type = event_data.get('adjustment_type', 'strategy')
        feedback = event_data.get('feedback', '')
        interrupt_id = event_data.get('interrupt_id', '')
        
        # 构建步骤名称
        step_name = "interrupt.feedbackApplied"  # 改为翻译键
        
        # 构建描述
        if feedback:
            description = f"用户反馈: {feedback[:100]}..." if len(feedback) > 100 else f"用户反馈: {feedback}"
        else:
            description = "应用用户反馈，调整分析方向"
        
        # 构建参数数据
        parameters = {
            'adjustment_type': adjustment_type,
            'feedback': feedback,
            'interrupt_id': interrupt_id,
            'timestamp': get_shanghai_time().isoformat()
        }
        
        return self.step_service.save_step(
            analysis_id=self.analysis_id,
            step_id=f'feedback_applied_{interrupt_id}',
            step_name=step_name,
            step_type='interaction',
            status='finish',
            step_order=self.step_counter,
            description=description,
            tool_name='用户反馈',
            parameters=parameters
        )

    def _save_evaluation_completed_step(self, event_data: Dict[str, Any]) -> bool:
        """保存评估完成步骤"""
        step_id = event_data.get('step_id', f'evaluation_{self.step_counter}')
        tool_name = event_data.get('tool_name', '未知工具')
        reasoning = event_data.get('reasoning', '')
        should_continue = event_data.get('should_continue', True)
        completion_confidence = event_data.get('completion_confidence', 0.0)
        missing_aspects = event_data.get('missing_aspects', [])
        suggested_next_steps = event_data.get('suggested_next_steps', [])
        evaluation_conclusion = event_data.get('evaluation_conclusion', '')
        
        # 构建步骤名称，包含评估推理内容
        step_name = f'steps.intelligentEvaluation|{{"reasoning":"{reasoning}"}}' if reasoning else f'steps.intelligentEvaluation|{{"toolName":"{tool_name}"}}'  # 改为翻译键格式
        
        # 构建评估结果数据，存储在parameters中
        evaluation_data = {
            "tool_name": tool_name,
            "should_continue": should_continue,
            "completion_confidence": completion_confidence,
            "missing_aspects": missing_aspects,
            "reasoning": reasoning,
            "suggested_next_steps": suggested_next_steps,
            "evaluation_conclusion": evaluation_conclusion,
            "timestamp": event_data.get('timestamp')
        }
        
        # 保存评估步骤
        return self.step_service.save_step(
            analysis_id=self.analysis_id,
            step_id=step_id,
            step_name=step_name,
            step_type='evaluation',  # 新的步骤类型：评估
            status='finish',
            step_order=self.step_counter,
            description=f'对{tool_name}工具执行结果的智能评估',
            tool_name=tool_name,  # 关联的工具名称
            parameters=evaluation_data,  # 存储评估详细数据
            has_result=False,  # 评估步骤本身不产生执行结果
            reasoning=reasoning  # 评估推理过程
        )

    def _save_insight_discovered_step(self, event_data: Dict[str, Any]) -> bool:
        """保存洞察发现步骤"""
        try:
            step_id = event_data.get('step_id', f'insight_{self.step_counter}')
            tool_name = event_data.get('tool_name', '结果分析')
            insights = event_data.get('insights', [])
            key_findings = event_data.get('key_findings', [])

            log.info(f"🔍 开始保存洞察发现步骤: {step_id}, 工具: {tool_name}")
            log.info(f"  - 洞察数量: {len(insights)}")
            log.info(f"  - 关键发现数量: {len(key_findings)}")
            log.info(f"  - 事件数据键: {list(event_data.keys())}")

            # 构建洞察摘要
            insight_summary = []
            if insights:
                insight_summary.extend([f"• {insight.get('title', '')}" for insight in insights[:3]])  # 最多显示3个洞察
            if key_findings:
                insight_summary.extend([f"• {finding}" for finding in key_findings[:2]])  # 最多显示2个关键发现

            summary_text = "\n".join(insight_summary) if insight_summary else "发现数据洞察"

            # 根据用户语言生成步骤名称
            step_name = f'steps.insightDiscovered|{{"toolName":"{tool_name}"}}'

            log.info(f"  - 步骤名称: {step_name}")
            log.info(f"  - 摘要文本: {summary_text}")

            result = self.step_service.save_step(
                analysis_id=self.analysis_id,
                step_id=step_id,
                step_name=step_name,
                step_type='insight',  # 新的步骤类型：洞察
                status='finish',
                step_order=self.step_counter,
                description=summary_text,
                tool_name=tool_name,
                parameters=event_data,  # 存储完整的洞察数据
                has_result=True,
                result_key=step_id
            )

            if result:
                log.info(f"✅ 洞察发现步骤保存成功: {step_id}")
            else:
                log.error(f"❌ 洞察发现步骤保存失败: {step_id}")

            return result

        except Exception as e:
            log.error(f"❌ 保存洞察发现步骤异常: {str(e)}")
            import traceback
            log.error(f"异常堆栈: {traceback.format_exc()}")
            return False