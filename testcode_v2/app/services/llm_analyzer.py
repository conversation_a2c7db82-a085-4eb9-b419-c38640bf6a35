"""
基于LLM的流式分析器
=================
负责管理基于LLM的任务规划、执行和结果生成
"""

import json
import asyncio
import time
from app.core.logger import log
from typing import Dict, List, Optional, Any, AsyncGenerator
from dataclasses import dataclass, field
from datetime import datetime
from openai import AsyncOpenAI
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from abc import ABC, abstractmethod

from app.core.config import settings
from app.services.llm_factory import get_llm_service
from app.models.analysis import Analysis as AnalysisModel
from app.utils.json_utils import DecimalEncoder
from app.utils.time_utils import get_shanghai_time
from app.db.redis import RedisClient
from app.core.context_store import context_store
from app.services.system_tools import SystemTools
from app.services.chart_generation import ChartGenerationAgent
from app.services.report_generator import ReportGenerator


class InterruptTimeoutConfig:
    """打断超时配置"""
    # 打断等待反馈的最大时间（分钟）
    MAX_FEEDBACK_WAIT_TIME = 3  # 3分钟
    
    # 主循环中打断检查的睡眠间隔（秒）
    INTERRUPT_CHECK_SLEEP_INTERVAL = 2  # 2秒
    
    # 打断状态检查的最大次数
    MAX_INTERRUPT_CHECK_COUNT = 90  # 3分钟 / 2秒 = 90次
    
    # Redis信号过期时间（秒）
    INTERRUPT_SIGNAL_TTL = 900  # 15分钟
    
    # 超时后的默认行为
    TIMEOUT_ACTION = "cancel"  # "cancel" 或 "continue"


@dataclass
class ExecutionContext:
    """执行上下文"""
    user_query: str
    task_state: str = 'planning'  # 'planning', 'executing', 'completed', 'failed'
    
    # 执行历史
    execution_history: List[Dict] = field(default_factory=list)
    execution_results: Dict[str, Any] = field(default_factory=dict)
    
    # 规划轮数控制
    planning_rounds: int = 0
    max_planning_rounds: int = 25  # 默认最大规划轮数
    
    current_plan: Optional[Dict] = None
    analysis_id: Optional[str] = None
    
    # 意图分析结果
    intent_analysis_result: Optional[Dict] = None
    # 多轮分析上下文
    multi_round_context: Optional[Dict[str, Any]] = None
    
    # 新增：评估结果历史
    evaluation_history: List[Dict[str, Any]] = field(default_factory=list)

    # 🔧 新增：分析结果历史 - 保存每轮分析的洞察和结论
    analysis_results_history: List[Dict[str, Any]] = field(default_factory=list)

    # 打断相关字段
    interrupt_status: str = 'none'  # 'none', 'interrupted', 'waiting_feedback', 'adjusting'
    interrupt_start_time: Optional[datetime] = None
    interrupt_check_count: int = 0
    interrupt_timeout_reached: bool = False
    last_interrupt_id: Optional[str] = None
    user_constraints: List[str] = field(default_factory=list)
    tool_preferences: List[str] = field(default_factory=list)
    analysis_focus: List[str] = field(default_factory=list)
    
    # 用户语言偏好
    user_language: str = 'zh-CN'

    # 🔧 新增：分析状态管理
    analysis_stage: str = 'data_collection'  # 'data_collection', 'phenomenon_analysis', 'causal_analysis', 'strategy_generation'
    stage_insights: Dict[str, List[str]] = field(default_factory=dict)  # 各阶段的洞察汇总
    latest_evaluation_guidance: Optional[str] = None  # 最新评估的具体指导建议
    
    def add_execution_record(self, tool_name: str, parameters: Dict, result: Any = None, reasoning: str = ''):
        """添加执行记录"""
        record = {
            'timestamp': get_shanghai_time().isoformat(),
            'reasoning': reasoning,
            'tool_name': tool_name,
            'parameters': parameters,
            'result': result
        }
        self.execution_history.append(record)
        
        # 存储结果，使用工具名作为键
        if result is not None:
            result_key = f"{tool_name}_{len(self.execution_history)}"
            self.execution_results[result_key] = result
    
    def add_evaluation_record(self, step_id: str, tool_name: str, evaluation_result: Dict[str, Any]):
        """添加评估记录"""
        evaluation_record = {
            'timestamp': get_shanghai_time().isoformat(),
            'step_id': step_id,
            'tool_name': tool_name,
            'evaluation_result': evaluation_result
        }
        self.evaluation_history.append(evaluation_record)

    def add_analysis_result(self, step_id: str, tool_name: str, analysis_result: Dict[str, Any]):
        """添加分析结果记录"""
        analysis_record = {
            'timestamp': get_shanghai_time().isoformat(),
            'step_id': step_id,
            'tool_name': tool_name,
            'analysis_result': analysis_result
        }
        self.analysis_results_history.append(analysis_record)
    
    def get_latest_evaluation(self) -> Optional[Dict[str, Any]]:
        """获取最新的评估结果"""
        if self.evaluation_history:
            return self.evaluation_history[-1]['evaluation_result']
        return None
    
    def is_completed(self) -> bool:
        """检查任务是否完成"""
        return self.task_state == 'completed' or self.task_state == 'failed'
    
    def is_max_rounds_reached(self) -> bool:
        """检查是否达到最大规划轮数"""
        return self.planning_rounds >= self.max_planning_rounds



    def update_analysis_stage(self, new_stage: str):
        """更新分析阶段"""
        valid_stages = ['data_collection', 'phenomenon_analysis', 'causal_analysis', 'strategy_generation']
        if new_stage in valid_stages:
            self.analysis_stage = new_stage

    def add_stage_insight(self, stage: str, insight: str):
        """添加阶段洞察"""
        if stage not in self.stage_insights:
            self.stage_insights[stage] = []
        if insight not in self.stage_insights[stage]:
            self.stage_insights[stage].append(insight)

    def get_stage_summary(self) -> str:
        """获取各阶段的洞察摘要"""
        summary_parts = []
        stage_names = {
            'data_collection': '数据收集阶段',
            'phenomenon_analysis': '现象分析阶段',
            'causal_analysis': '本质分析阶段',
            'strategy_generation': '策略生成阶段'
        }

        for stage, insights in self.stage_insights.items():
            if insights:
                stage_name = stage_names.get(stage, stage)
                summary_parts.append(f"**{stage_name}**:")
                for insight in insights[:3]:  # 最多显示3个关键洞察
                    summary_parts.append(f"- {insight}")
                summary_parts.append("")

        return "\n".join(summary_parts) if summary_parts else "暂无阶段性洞察"
    
    def start_interrupt(self, interrupt_id: str):
        """开始打断状态"""
        self.interrupt_status = 'interrupted'
        self.interrupt_start_time = get_shanghai_time()
        self.interrupt_check_count = 0
        self.interrupt_timeout_reached = False
        self.last_interrupt_id = interrupt_id
    
    def increment_interrupt_check(self):
        """增加打断检查计数"""
        self.interrupt_check_count += 1
    
    def is_interrupt_timeout(self) -> bool:
        """检查打断是否超时"""
        if not self.interrupt_start_time:
            return False

        current_time = get_shanghai_time()
        elapsed_minutes = (current_time - self.interrupt_start_time).total_seconds() / 60

        # 使用统一的配置，而不是硬编码
        return (elapsed_minutes > InterruptTimeoutConfig.MAX_FEEDBACK_WAIT_TIME or
                self.interrupt_check_count > InterruptTimeoutConfig.MAX_INTERRUPT_CHECK_COUNT)
    
    def reset_interrupt_status(self):
        """重置打断状态"""
        self.interrupt_status = 'none'
        self.interrupt_start_time = None
        self.interrupt_check_count = 0
        self.interrupt_timeout_reached = False
        self.last_interrupt_id = None


class ContextInfoBuilder(ABC):
    """上下文信息构建器抽象基类"""
    
    @abstractmethod
    def build(self, context: ExecutionContext) -> str:
        """构建上下文信息
        
        Args:
            context: 执行上下文
            
        Returns:
            str: 构建的上下文信息字符串，如果不需要则返回空字符串
        """
        pass
    
    @abstractmethod
    def get_priority(self) -> int:
        """获取构建优先级，数字越小优先级越高"""
        pass


class ClarificationContextBuilder(ContextInfoBuilder):
    """澄清历史上下文构建器"""
    
    def build(self, context: ExecutionContext) -> str:
        if not (hasattr(context, 'clarification_history') and context.clarification_history):
            return ""
            
        clarification_rounds = []
        for i, round_data in enumerate(context.clarification_history, 1):
            round_info = f"""
第{i}轮澄清：
- 澄清问题：{round_data.get('clarification_question', '未记录')}
- 用户回复：{round_data.get('user_response', '未记录')}
- 时间：{round_data.get('timestamp', '未记录')}"""
            clarification_rounds.append(round_info)
        
        return f"""
## 澄清历史信息
本次分析已经过 {len(context.clarification_history)} 轮澄清，详细信息如下：
{''.join(clarification_rounds)}

**重要**：请基于以上澄清历史理解用户的真实需求，不要忽略之前澄清过程中用户提供的信息。
"""
    
    def get_priority(self) -> int:
        return 1


class ContextRecoveryBuilder(ContextInfoBuilder):
    """上下文恢复信息构建器"""
    
    def build(self, context: ExecutionContext) -> str:
        if not context.execution_history:
            return ""
            
        return f"""
本次分析已执行了 {len(context.execution_history)} 个步骤：
- 执行历史：{len(context.execution_history)} 条记录
- 执行结果：{len(context.execution_results)} 个结果
- 任务状态：{context.task_state}
- 最大轮数：{context.max_planning_rounds}
- 剩余轮数：{context.max_planning_rounds - context.planning_rounds}

**重要**：请仔细查看执行历史，不要重复已经执行过的操作，继续基于现有结果进行下一步分析。
"""
    
    def get_priority(self) -> int:
        return 2


class IntentAnalysisContextBuilder(ContextInfoBuilder):
    """意图分析上下文构建器"""
    
    def build(self, context: ExecutionContext) -> str:
        if not (hasattr(context, 'intent_analysis_result') and context.intent_analysis_result):
            return ""
            
        intent_analysis = context.intent_analysis_result
        
        # 获取详细的意图描述
        intent_description = intent_analysis.get('intent_description', '未生成意图分析')
        
        # 获取执行步骤
        execution_steps = intent_analysis.get('execution_steps', [])
        
        # 检查是否包含用户修正信息
        has_user_modification = intent_analysis.get('has_user_modification', False)
        user_adjustment = intent_analysis.get('user_adjustment', '')
        
        # 构建执行步骤参考信息
        steps_info = ""
        if execution_steps and isinstance(execution_steps, list) and len(execution_steps) > 0:
            steps_list = []
            for i, step in enumerate(execution_steps, 1):
                step_number = step.get('step_number', i)
                step_intent = step.get('step_intent', '未指定目标')
                recommended_tool = step.get('recommended_tool', '未指定工具')
                tool_usage_reason = step.get('tool_usage_reason', '未指定理由')
                expected_outcome = step.get('expected_outcome', '未指定预期结果')
                
                step_text = f"""
**步骤 {step_number}：{step_intent}**
- 推荐工具：{recommended_tool}
- 选择理由：{tool_usage_reason}
- 预期结果：{expected_outcome}"""
                steps_list.append(step_text)
            
            steps_info = f"""
### 📋 执行步骤规划参考
**重要指导：请参考以下步骤规划进行分析，但可根据实际数据情况和工具执行结果灵活调整：**
{''.join(steps_list)}

**步骤执行原则：**
- **顺序参考**：建议按照步骤顺序执行，但可根据实际情况调整
- **工具选择**：优先使用推荐的工具，如果工具不可用或不适合可选择其他工具
- **目标导向**：每个步骤都要朝着步骤意图的目标前进
- **结果验证**：每步执行后验证是否达到预期结果，如未达到可调整方法
- **灵活应变**：如果某步骤无法执行或结果不理想，可跳过或采用替代方案
"""
        
        if has_user_modification and user_adjustment:
            # 包含用户补充的情况
            intent_info = f"""
## AI意图识别与分析参考（已整合用户补充）

**重要说明：以下意图分析已整合用户的补充说明，作为数据分析的重要参考依据，请根据实际数据情况灵活调整分析方法：**

### 原始意图分析
{intent_description}

### 用户补充说明
{user_adjustment}

{steps_info}
### 分析建议
- **核心目标**：严格围绕原始查询需求进行分析，原始查询是分析的核心目标
- **补充参考**：用户的补充说明主要用于理解数据结构、字段命名或澄清分析细节
- **执行原则**：以原始查询为主线，合理参考补充说明优化分析方法
- **步骤参考**：参考执行步骤规划进行分析，但以实际数据情况为准进行调整
- **数据驱动**：以实际数据情况为准，如果数据无法支持某些分析，可适当调整

**注意：原始查询始终是分析的主要目标，补充说明仅起辅助作用。**
"""
        else:
            # 未包含用户修正的情况（用户直接确认）
            intent_info = f"""
## AI意图识别与分析参考（用户已确认）

**重要说明：以下是基于用户查询的深度意图分析结果，已经过用户确认，作为数据分析的重要参考依据：**

### 意图分析参考
{intent_description}
{steps_info}
### 分析建议
- **参考执行**：以上述意图分析为主要参考，但可根据实际情况调整
- **分析重点**：重点关注参考中提到的数据维度、分析方法和预期输出
- **步骤指导**：参考执行步骤规划进行分析，按步骤逐步推进
- **灵活处理**：如果参考中提到查询存在模糊性，请根据实际数据情况灵活处理
- **结果导向**：努力达成参考中预测的用户期望，但以数据可行性为准

**注意：此参考已经过用户确认，但实际执行请以数据情况和分析可行性为准。**
"""
        
        return intent_info
    
    def get_priority(self) -> int:
        return 3





class ContextBuilderManager:
    """上下文构建器管理器"""
    
    def __init__(self):
        self.builders: List[ContextInfoBuilder] = []
        self._register_default_builders()
    
    def _register_default_builders(self):
        """注册默认的构建器"""
        self.register(ContextRecoveryBuilder())
        self.register(IntentAnalysisContextBuilder())
        # EvaluationContextBuilder已移除，评估历史现在由UnifiedAnalysisEvaluator处理
    
    def register(self, builder: ContextInfoBuilder):
        """注册新的构建器"""
        self.builders.append(builder)
        # 按优先级排序
        self.builders.sort(key=lambda b: b.get_priority())
    
    def build_all_context_info(self, context: ExecutionContext) -> str:
        """构建所有上下文信息"""
        context_parts = []
        
        for builder in self.builders:
            try:
                context_info = builder.build(context)
                if context_info.strip():  # 只添加非空的上下文信息
                    context_parts.append(context_info)
            except Exception as e:
                # 记录错误但不影响其他构建器
                log.warning(f"上下文构建器 {type(builder).__name__} 执行失败: {str(e)}")
        
        return "".join(context_parts)


# ResultEvaluator已移除 - 功能已完全迁移到UnifiedAnalysisEvaluator
# 所有评估功能现在通过UnifiedAnalysisEvaluator.analyze_and_evaluate()提供




class StreamPromptGenerator:
    """流式分析提示词生成器"""
    
    def __init__(self):
        self.context_manager = ContextBuilderManager()
    
    def generate_planning_prompt(self, context: ExecutionContext, available_tools: List[Dict], schema_info: str, db_type: str = None, db_version: str = None) -> tuple[str, str]:
        """生成规划提示词
        
        Args:
            context: 执行上下文
            available_tools: 可用工具列表
            schema_info: 数据库Schema信息
            db_type: 数据库类型（如：mysql, postgresql, oracle, sqlite等）
            db_version: 数据库版本信息（如：Oracle Database 19c, MySQL 8.0.35等）
            
        Returns:
            tuple[str, str]: (用户提示词, 系统提示词)
        """
        # 获取当前时间
        current_time = get_shanghai_time()
        formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
        
        # 生成数据库类型和版本信息
        db_type_display = db_type.upper() if db_type else "标准SQL"
        db_version_display = f" ({db_version})" if db_version else ""
        db_type_info = f"\n## 数据库类型\n当前项目使用的数据库类型：**{db_type_display}{db_version_display}**\n"
        
        # 根据数据库类型添加特定的SQL语法说明
        if db_type == "mysql":
            db_type_info += ''
        elif db_type == "postgresql":
            db_type_info += """
### PostgreSQL特殊语法要求：
- 日期函数：使用 CURRENT_DATE, NOW(), DATE_TRUNC()
- 字符串连接：使用 || 操作符或 CONCAT() 函数
- 限制查询：使用 LIMIT n
- 时间范围：CURRENT_DATE - INTERVAL 'n days/months/years'
- 大小写敏感：表名和字段名通常小写
"""
        elif db_type == "oracle":
            db_type_info += """
### Oracle特殊语法要求：
- 日期函数：使用 SYSDATE, TRUNC(), ADD_MONTHS()
- 字符串连接：使用 || 操作符或 CONCAT() 函数
- 限制查询：使用 ROWNUM <= n 或 FETCH FIRST n ROWS ONLY
- 时间范围：SYSDATE - n (天数)
- 大小写敏感：表名和字段名通常大写
"""
        elif db_type == "sqlite":
            db_type_info += """
### SQLite特殊语法要求：
- 日期函数：使用 DATE(), DATETIME(), strftime()
- 字符串连接：使用 || 操作符
- 限制查询：使用 LIMIT n
- 时间范围：DATE('now', '-n days/months/years')
"""
        else:
            db_type_info += """
### 标准SQL语法要求：
- 使用标准SQL语法
- 限制查询：使用 LIMIT n
- 避免使用数据库特定的函数
"""
        
        # 过滤掉图表工具和智能交互工具
        filtered_tools = [tool for tool in available_tools]
        
        # 格式化工具信息
        tool_descriptions = self._format_tools_for_prompt(filtered_tools)
        
        # 转换执行历史为JSON（保持完整信息）
        execution_history_json = json.dumps(context.execution_history, ensure_ascii=False, indent=2, cls=DecimalEncoder)
        execution_results_json = json.dumps(context.execution_results, ensure_ascii=False, indent=2, cls=DecimalEncoder)
        available_tools_json = json.dumps(filtered_tools, ensure_ascii=False, indent=2, cls=DecimalEncoder)
        
        # 规划轮数信息
        planning_rounds_info = f"""
规划轮数信息：
- 当前轮数：{context.planning_rounds}
- 最大轮数：{context.max_planning_rounds}
- 剩余轮数：{context.max_planning_rounds - context.planning_rounds}
"""
        
        # 构建所有上下文信息（澄清历史、上下文恢复、意图分析等）
        all_context_info = self.context_manager.build_all_context_info(context)

        # 获取多轮分析上下文信息（如果有的话）
        multi_round_context = ""
        if hasattr(context, 'multi_round_context') and context.multi_round_context:
            ctx_data = context.multi_round_context
            context_summary = ctx_data.get('context_summary', '')
            
            if context_summary:
                multi_round_context = f"""
**历史轮次分析摘要**
{context_summary}
"""
        
        # 根据剩余轮数调整提示词策略
        strategy_note = ""
        if context.max_planning_rounds - context.planning_rounds <= 2:
            strategy_note = """
注意：规划轮数即将达到上限，请尽快总结现有信息，准备生成最终答案。如果现有信息足够回答问题，请优先选择生成最终答案而非继续执行工具。
"""
        
        # 检查是否有评估结果需要特别指导
        evaluation_guidance = ""
        if hasattr(context, 'evaluation_history') and context.evaluation_history:
            latest_evaluation = context.get_latest_evaluation()
            if latest_evaluation:
                # 构建基于评估结果的智能指导
                evaluation_guidance = self._build_evaluation_guidance_for_planning(latest_evaluation)
        
        # 构建系统提示词（角色定义、核心原则、输出格式等通用指导）
        system_prompt = f"""你是一个经验丰富、思维缜密、严谨求真的**人类数据分析师**。你的任务是根据用户的自然语言问题，利用提供的数据库Schema信息，规划一个**完整且合理的查询和分析路线**，并提供**下一步的具体行动计划**。

**【重要提示：在以下描述中，所有提及的"示例"（如"中国产"、"手机"、"高端产品"、"近期"、"趋势"等）仅用于说明你何时需要进行数据探索或特殊处理，以及如何从多个维度思考问题。它们本身不是固定的筛选值或预设答案。你必须根据实际的数据库Schema和数据内容，通过探索性查询（如 `DISTINCT` 语句、数据预览）来动态获取和验证精确的筛选条件或可分析的维度。】**

{evaluation_guidance}
        
**人类数据分析师的思维模式：**
1. **整体思考与规划：** 在开始任何具体查询之前，首先对用户的问题进行整体思考。
   - **核心意图：** 准确识别用户问题的核心意图，并考虑所有可能的、合理的解释（即使问题表达模糊）。
   - **分析路线图：** 规划一个清晰、高效、分阶段的分析路线图，以达成用户目标。该路线图应考虑数据的可用性、查询效率和结果的最终呈现形式。
   - **风险评估：** 预判可能遇到的挑战，例如数据量过大、数据格式不匹配、意图不明确等。

2. **数据探索与理解（如果需要）：**
   - **主动探索：** 如果你对数据在数据库中的准确表达（例如，某个字段的确切含义、数据格式、可能的值范围、是否存在缺失值等）不确定，或者认为直接查询可能不合理（例如，用户问"北京用户"，但不知道"北京"在数据库中是以"北京"、"北京市"还是其他形式存储），请**优先规划一个数据探索步骤**来澄清这些疑问。
   - **探索方式：** 数据探索可能包括查询 `DISTINCT` 值、`COUNT`、`MIN/MAX`、查看部分数据样本（`LIMIT N`）、或检查字段的数据类型。

3. **迭代与自我纠正：**
   - 你的分析和查询过程是迭代的。每一步行动完成后，都必须对结果进行**全面、严谨的评估**。
   - **基于评估结果，调整后续计划。** 如果评估发现问题，不要继续按原计划执行，而是返回到规划阶段进行修正或重新规划。

**核心原则与目标：**
   - **以用户为中心**：深入理解真实意图，提供最有价值、最准确的信息。
   - **全面性 (推断能力)：** 用户的提问可能存在多种合理的解释（尤其是模糊问题）。你应基于可用的数据结构和你的通用分析知识，尽可能地推断和涵盖所有**相关的业务问题维度**，并计划查询这些维度的信息，而不是给出单一、狭隘的答案
   - **效率：** 在满足用户需求的前提下，你需要尽可能高效地利用资源。避免不必要的计算和查询。
   - **清晰汇报**：用简洁自然语言解释每个结果，保证易读易懂
   - **智能推理：** 根据用户提供的信息和上下文，运用常识和业务理解进行合理推断，尽可能准确地理解用户的真实意图和所有可能的解释
   - **仅处理数据相关问题**：只回答与系统数据相关的问题
   - **上下文继承**：善用已有执行历史和澄清信息，保持分析连贯性
   - **深度分析优先**：优先继续数据探索和深入分析，避免过早结束分析流程

**可用分析工具：**
{available_tools_json}

**🎨 智能图表生成能力：**
系统配备了专业的图表生成Agent，能够自动为合适的数据生成可视化图表：
- **自动触发**：当你执行数据查询工具后，系统会自动评估数据是否适合可视化
- **智能选择**：根据数据特征自动选择最合适的图表类型（折线图、柱状图、饼图、表格等）
- **多图表支持**：对于复杂数据，可能生成多个互补的图表来全面展示数据洞察
- **无需主动调用**：你不需要主动调用图表生成工具，专注于数据分析逻辑即可
- **状态感知**：系统会告知你图表生成的结果（成功/失败/跳过），你可以在最终报告中相应调整

**工具选择指导：**
- 根据分析需求选择最合适的工具
- 在规划分析时，可以考虑数据的可视化潜力，但无需担心图表生成的技术细节
- **重要**：tool_name必须使用工具的中文name字段（如"自动SQL查询"），不要使用tool_type字段（如"AUTO_SQL"）

**工具使用指南：**
1. **🚨 避免重复查询**：严格检查已完成的查询列表，绝对禁止执行相同或高度相似的查询
2. **高效工具利用**：充分利用可用工具获取数据，确保回答全面详细，避免相同工具相同参数重复调用
3. **专用工具优先：** 优先使用针对特定任务的专用工具，如果专用工具无法满足需求，再使用自动SQL查询工具
4. **数据探索：** 不清楚数据规模时使用COUNT(*)、DISTINCT了解数据规模和分布
5. **渐进式查询：** 先验证少量数据，再进行详细查询
6. **步骤整合：** 调用自动SQL查询工具时，尽量使用复杂SQL查询来减少此工具的调用次数
7. **错误恢复：** 调用失败时使用更保守的条件或替代工具，但要改变参数
8. **🔧 分析深度递进**：基于当前分析阶段，优先执行能推进分析深度的操作，而非重复收集数据

## 自动SQL查询工具使用说明
直接提供完整的SQL查询语句进行执行，**必须严格遵循当前数据库类型的语法规范**。

### 基本示例：
- sql: "SELECT field1, field2 FROM table1 WHERE condition1 = 'value1' AND condition2 = 'value2' LIMIT 10"

### 重要注意事项：
1. **数据库语法：** 必须使用数据库类型对应的语法和函数
2. **完整语句：** SQL必须是完整的语句，包含所有实际值，不使用参数占位符
3. **🚨 强制数据量限制：**
   - **明细查询**：必须添加 LIMIT，最大不超过 500 行
   - **聚合查询**：GROUP BY结果最大不超过 100 组
   - **探索查询**：LIMIT 10-50 行即可
   - **绝对禁止**：无LIMIT的全表查询
4. **字符串处理：** 字符串值需要用单引号包围，数字值直接使用

### 规划整体路线图的重要注意事项：
- 理解用户问题的核心意图
- 如果用户的问题是一个综合性的问题，要从多个常见维度给出数据分析
- 结合Schema信息和你的分析经验，规划一个整体的分析路线图（包含主要阶段和它们之间的逻辑关系）
- **数据探索必要性：** 如果你对任何数据字段的含义或格式存在疑问，或认为可能存在数据录入不规范的情况，请规划一个数据探索步骤作为下一步行动
- **🚨 数据量控制策略：**
  - **预判原则**：如果查询可能涉及超过 500 行数据，优先考虑优化查询方案，使用聚合查询或limit限制
  - **聚合优先**：对于统计分析，优先使用 GROUP BY + 聚合函数（COUNT、SUM、AVG等）
  - **时间限制**：对于时间序列数据，限制查询时间范围（如最近30天、本月等）
  - **采样策略**：必要时使用 TABLESAMPLE 或 WHERE 条件进行数据采样

### 上一步执行结果评估
**上一步的规划和执行结果，是否合理，如果不合理，需要重新规划**
**当自动SQL查询工具返回空结果时，需要优先考虑是生成的SQL不合理还是数据库中确实没有相关数据，不合理需要重新生成SQL再次执行自动SQL查询工具**

### 常见问题处理：
1. **大小写不匹配：** 用户输入"abc公司"，数据库可能存储为"ABC公司"
2. **拼写变体：** 用户输入简称或缩写，需要查找完整名称
3. **部分匹配：** 使用模糊查询（LIKE、ILIKE）查找包含用户输入的记录
4. **数据的字段定义不准确：** 需要先查询相关字段的样例数据，再确认我们对字段的使用是否合理
5. **探索性查询：** 先查询相关字段的所有可能值，再进行精确匹配

### 示例处理流程：
- 用户查询"abc公司" → 空结果 → 查询"包含'abc'或'ABC'的所有公司名称" → 发现"ABC公司" → 重新查询

### 问题范围判断
1. **数据相关性检查：** 确认问题涉及的实体和字段在Schema中存在
2. **超范围问题：** 直接生成最终答案，明确告知超出数据范围

**决策时机：**
仅在满足以下严格条件时才生成最终回答：
- **强制结束**：剩余规划轮数 <= 1（系统强制要求）
- **数据完整性确认**：已通过多个维度验证数据完整性，确认无需补充查询
- **分析深度充分**：已完成必要的数据探索、统计分析和关联分析

**继续分析的优先原则：**
- 如果仍有相关数据维度未探索，应继续查询
- 如果分析深度不够（如只有基础统计，缺乏趋势或关联分析），应继续深入
- 如果用户问题涉及多个方面但只完成了部分分析，应继续补充

**输出格式：**
严格按照以下JSON格式返回，不要添加任何额外字段：

工具调用：
```json
{{
  "action_type": "tool",
  "tool_name": "<工具名称，必须使用可用分析工具列表中的中文name字段，如'自动SQL查询'，不要使用tool_type字段>",
  "parameters": {{ /* 工具参数 */ }},
  "reasoning": "使用中文输出调用此工具的具体目的、预期结果、与用户查询的关联，以及数据量控制策略（必须说明：1.预估返回行数 2.使用的LIMIT值 3.是否采用聚合查询 4.如何避免大批量数据查询）"
}}
```

生成最终回答：
```json
{{
  "action_type": "final_answer",
  "reasoning": "使用中文输出基于哪些工具结果，为什么足够回答问题。（不要暴露模型相关信息，也不要回复敏感问题）"
}}
```

**重要：只返回上述格式的JSON，不要添加display_format等额外字段！**

**🚨 重要格式要求**：
- 必须返回纯JSON格式，不要使用markdown代码块包裹
- 不要添加```json或```标记
- 不要在JSON前后添加任何解释性文字
- 直接输出JSON对象，确保格式正确

{db_type_info}
"""

        # 🔧 新增：构建分析状态信息
        analysis_stage_info = f"""
## 🎯 **当前分析状态**
- **分析阶段**: {context.analysis_stage}
- **阶段性洞察**:
{context.get_stage_summary()}
"""

        # 🔧 新增：构建评估指导信息
        evaluation_guidance_enhanced = ""
        if context.latest_evaluation_guidance:
            evaluation_guidance_enhanced = f"""
## 🔍 **智能评估指导**
**重要：请严格遵循以下评估建议，避免重复性工作**

{context.latest_evaluation_guidance}
"""

        # 构建用户提示词（具体的上下文信息、数据状态、工具列表等动态内容）
        user_prompt = f"""
**当前时间**：{formatted_time}

**用户查询**：{context.user_query}

{multi_round_context}

{analysis_stage_info}

{evaluation_guidance_enhanced}

**本轮分析上下文**
{all_context_info}

**已执行操作及结果**：
{execution_history_json}
{strategy_note}

{evaluation_guidance}

## 📋 查询状态检查
{self._generate_query_status_info(context)}

**数据库Schema信息：**
{schema_info}
"""

        return user_prompt, system_prompt
    
    def generate_result_prompt(self, context: ExecutionContext, schema_info: str, reasoning: str) -> tuple[str, str]:
        """生成最终结果提示词
        
        Args:
            context: 执行上下文
            
        Returns:
            str: 最终结果提示词
        """
        # 获取当前时间
        current_time = get_shanghai_time()
        formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
        
        # 分析执行历史，判断问题复杂度
        execution_count = len(context.execution_history)
        has_multiple_tools = len(set(record.get('tool_name', '') for record in context.execution_history)) > 1
        
        # 获取已有上下文摘要（如果有的话）
        multi_round_context = ""
        if hasattr(context, 'multi_round_context') and context.multi_round_context:
            ctx_data = context.multi_round_context
            context_summary = ctx_data.get('context_summary', '')
            
            if context_summary:
                multi_round_context = f"""
**历史轮次分析摘要**
{context_summary}
"""
        # 如果存在上一步的推理，则需要将推理作为系统提示词的一部分
        reasoning_prompt = ""
        if reasoning:
            reasoning_prompt = f"""
**上一步的推理**
{reasoning}
"""

        system_prompt = f"""
        
你是一个经验丰富、洞察敏锐的**专业数据分析师**。你的核心职责是将所有已完成的数据分析步骤和工具输出进行深度整合、提炼和解读，最终撰写一份**简体中文**的业务分析报告。

**报告撰写核心指导（像人类分析师一样思考）：**

1.  **用户导向与意图匹配：** 你的报告必须直接、清晰地回答用户的原始问题，并涵盖所有已推断出的多维度意图。
2.  **发现的提炼与解读：**
    *   **核心发现 (Findings)：** 这不是原始数据的简单罗列或 JSON 打印。你需要从所有工具的执行结果 (`context.execution_results`) 中**提取最关键的数据点、趋势、模式、关联性和任何值得注意的异常**。
    *   **数据可视化描述：** 想象如何用图表（例如折线图、柱状图、饼图）来展示这些发现，并在文本中进行描述，即使你不直接生成图表。
    *   **业务解读：** 解释这些发现的**业务含义**，它们意味着什么，而不是仅仅陈述数字。
3.  **结论的逻辑推导：**
    *   **主要结论 (Conclusions)：** 结论必须是基于`核心发现`的**直接、逻辑推导**。它们是对核心发现的概括性总结，并直接回答用户的原始问题或其拆解的子问题。
    *   **明确引用：** 确保每个结论都能清晰地追溯到`核心发现`中的具体数据点、趋势或模式。
4.  **建议与决策的可操作性：**
    *   **建议与决策 (Recommendations)：** 建议必须是**具体、可操作的行动方案**，旨在解决`主要结论`中提出的问题、利用发现的机会，或优化现有业务流程。
    *   **业务影响：** 思考这些建议可能带来的业务影响或效益。
5.  **分析过程的总结：**
    *   **数据来源与步骤：** 这不是原始日志的简单复制。你需要将`context.execution_history`中的复杂执行过程，**概括性地总结**为用户可理解的、清晰的分析流程。突出关键的查询、探索和验证步骤，以及它们如何一步步引导至最终结论。避免过多的技术细节，除非其对理解报告至关重要。
5.  **不能伪造数据或者假设数据**

**报告输出形式决策与结构：**

在生成最终报告前，你必须**首先**严格判断用户查询的复杂度和分析深度，并**只能选择以下两种输出形式中的一种**：

1.  **简洁答案（适用于简单查询）：**
    *   **触发条件：**
        *   用户查询需求极其简单，且**核心发现**部分仅包含**一个单一的聚合数值**（例如：总数、平均值、百分比等），或者一个明确的**是/否判断**。
        *   **最关键的判断：** 该答案**无需任何进一步的趋势分析、多维度解读、复杂推理或业务建议。**
    *   **输出格式：** 直接以简洁的自然语言给出答案，无需任何Markdown分节标题

2.  **详细分析报告（适用于复杂查询）：**
    *   **触发条件：**
        *   用户查询需求涉及**多个维度分析**（例如：销量趋势、产品构成、地域分布等）。
        *   需要进行**趋势解读**、**复杂推理**、**比较分析**。
        *   需要提供**业务建议**或**决策思路**。
        *   **核心发现**部分包含多组数据点、或需要解释其业务含义、或需要识别模式和异常。
    *   **输出格式：** 严格遵循以下 Markdown 报告结构，并完整填充每一节内容。

# 详细分析报告格式如下：
## 1. 问题概述
- **用户查询**：<!-- 用户查询 -->

## 2. 核心发现
<!-- 在此部分，请提炼并总结所有工具调用结果中的关键数据点、趋势、模式和异常。
    用清晰的自然语言描述，并想象如何通过图表（例如折线图、柱状图）进行可视化呈现。
    避免直接粘贴原始JSON。 -->

## 3. 主要结论
<!-- 在此部分，基于"核心发现"进行逻辑推导，给出回答用户问题（及其子问题）的核心结论。
    确保结论能清晰追溯到"核心发现"中的具体数据或趋势。 -->

## 4. 建议与决策
<!-- 在此部分，根据"主要结论"提出具体、可操作的业务建议或决策思路。
    思考这些建议如何解决问题或利用机会。 -->

## 5. 数据来源与分析步骤回顾
<!-- 在此部分，概括性地总结整个分析过程，包括关键的查询、探索、验证步骤，以及它们如何逐步帮助你得出结论。
    避免过多技术细节，除非对理解分析流程至关重要。 -->
    """
        
        user_prompt = f"""
**当前时间**：{formatted_time}

**用户查询**：{context.user_query}

{multi_round_context}

{reasoning_prompt}

**本轮分析情况**：
# 执行步骤数：{execution_count}
# 工具执行结果
```json
{json.dumps(context.execution_history, ensure_ascii=False, indent=2, cls=DecimalEncoder)}
```

**数据库Schema信息：**
{schema_info}
    """
        return user_prompt, system_prompt
    
    def _format_tools_for_prompt(self, tools: List[Dict[str, Any]]) -> str:
        """格式化工具信息用于提示"""
        tool_descriptions = []
        for tool in tools:
            tool_descriptions.append(f"- {tool['name']}: {tool['description']}")
        return '\n'.join(tool_descriptions)

    def _generate_query_status_info(self, context: ExecutionContext) -> str:
        """生成查询状态信息，只展示客观的执行状态信息
        
        Args:
            context: 执行上下文
            
        Returns:
            str: 查询状态信息
        """
        status_info = []
        
        # 只展示基本查询状态
        status_info.append("** 查询状态：正常**")
        status_info.append("- 当前查询：已接收用户查询，准备进行分析")
        
        # 检查执行历史状态
        if context.execution_history:
            status_info.append(f"- 已执行步骤：{len(context.execution_history)}个")
            
            # 检查是否有失败的工具调用（支持布尔值和字符串形式）
            failed_tools = []
            for record in context.execution_history:
                result = record.get('result', {})
                success_value = result.get('success')
                if success_value is False or success_value == "False" or success_value == "false":
                    failed_tools.append(record)
            if failed_tools:
                status_info.append(f"- ⚠️ 失败步骤：{len(failed_tools)}个，需要调整策略")
        else:
            status_info.append("- 执行历史：无，这是分析的开始")
        
        return '\n'.join(status_info) if status_info else "查询状态正常"
    
    def _build_evaluation_guidance_for_planning(self, latest_evaluation: Dict[str, Any]) -> str:
        """基于最新评估结果构建规划指导

        Args:
            latest_evaluation: 最新的评估结果

        Returns:
            str: 针对规划的评估指导文本
        """
        should_continue = latest_evaluation.get('should_continue', True)
        completion_confidence = latest_evaluation.get('completion_confidence', 0.0)
        evaluation_conclusion = latest_evaluation.get('evaluation_conclusion', '')
        suggested_next_steps = latest_evaluation.get('suggested_next_steps', [])

        # 构建明确的决策指导
        if not should_continue:
            # 智能评估建议结束分析
            decision_guidance = f"""
🎯 **智能评估决策：建议结束分析**
- **should_continue**: false
- **completion_confidence**: {completion_confidence:.2f}
- **决策建议**: 智能评估认为当前分析已经充分，建议生成最终报告

⚠️ **重要规划指导**:
**请优先选择 action_type: "final_answer"**，除非发现明显的数据质量问题或分析缺陷。
"""
        else:
            # 智能评估建议继续分析
            next_steps_text = "、".join(suggested_next_steps) if suggested_next_steps else "继续深入分析"
            decision_guidance = f"""
🔄 **智能评估决策：建议继续分析**
- **should_continue**: true
- **completion_confidence**: {completion_confidence:.2f}
- **建议下一步**: {next_steps_text}

⚠️ **重要规划指导**:
**请选择 action_type: "tool"**，继续执行分析工具以补充数据或深化分析。
"""

        if evaluation_conclusion:
            return f"""**🔍 基于智能评估结果的规划指导**
{decision_guidance}

**详细评估结论**:
{evaluation_conclusion}

**规划策略提示**: 请严格遵循智能评估的should_continue建议进行决策。"""
        else:
            # 如果没有评估结论，返回基本的指导
            return f"""**🔍 基于智能评估结果的规划指导**
{decision_guidance}

**规划建议**: 基于智能评估的should_continue字段进行决策，注意监控数据质量和结果准确性。"""


class LLMStreamAnalyzer:
    """基于LLM的流式分析器"""
    
    def __init__(self, db: Session = None):
        """初始化分析器

        Args:
            db: 数据库会话
        """
        # 使用LLM工厂获取服务
        self.llm_service = get_llm_service(db)

        # 为了兼容现有代码，保留原有的客户端接口
        if hasattr(self.llm_service, 'client'):
            self.llm_client = self.llm_service.client
        else:
            # 如果没有client属性，创建一个默认的
            self.llm_client = AsyncOpenAI(
                api_key=settings.OPENAI_API_KEY,
                base_url=settings.OPENAI_BASE_URL
            )

        # 获取模型名称
        if hasattr(self.llm_service, 'model'):
            self.model = self.llm_service.model
        else:
            self.model = settings.OPENAI_MODEL
            
        # 获取模型ID（用于Token统计）
        if hasattr(self.llm_service, 'model_id'):
            self.model_id = self.llm_service.model_id
        else:
            self.model_id = None
            log.warning("LLM服务未提供model_id，Token统计可能无法正常工作")

        self.prompt_generator = StreamPromptGenerator()
        self.db = db
        self.schema_description_file = "schema_description.md"
        self.analysis_logger = None  # 将在创建分析记录后初始化
        
        # 初始化智能图表生成Agent
        self.chart_agent = ChartGenerationAgent(db) if db else None
        log.info(f"🎨 图表生成Agent初始化完成: chart_agent={self.chart_agent is not None}, db={db is not None}")

        # ResultEvaluator已移除 - 功能已迁移到UnifiedAnalysisEvaluator
        # 初始化智能报告生成器
        self.report_generator = ReportGenerator(self.llm_service)
        log.info("📊 智能报告生成器初始化完成")

    async def _generate_intelligent_report(self, context: ExecutionContext, schema_info: str, reasoning: str = '', project_id: str = None, user_language: str = 'zh-CN', analysis_insights: Optional[List[Any]] = None) -> tuple[str, dict]:
        """
        使用ReportGenerator生成报告

        Args:
            context: 执行上下文
            schema_info: 数据库Schema信息
            reasoning: 推理过程
            project_id: 项目ID
            user_language: 用户语言偏好

        Returns:
            tuple[str, dict]: (生成的报告内容, token用量数据)
        """
        try:
            log.info("🔥 开始使用ReportGenerator生成报告")
            
            # 获取项目ID（优先使用传入的参数，否则从上下文获取）
            if not project_id:
                project_id = getattr(context, 'project_id', None)

            # 使用ReportGenerator的增强方法（包含分析洞察）
            return await self.report_generator.generate_report_with_context(
                context=context,
                schema_info=schema_info,
                reasoning=reasoning,
                project_id=project_id,
                db_session=self.db,
                user_language=user_language,
                analysis_insights=analysis_insights,
                lifecycle_logger=getattr(self, 'lifecycle_logger', None)  # 传递生命周期日志记录器
            )

        except Exception as e:
            log.error(f"🔥 ReportGenerator生成报告失败，回退到原有方法: {str(e)}")

    async def generate_structured_report(self, context, schema_info: str, reasoning: str = '',
                                       project_id: str = None, user_language: str = 'zh-CN',
                                       analysis_insights: list = None):
        """
        生成结构化报告（JSON格式）
        
        Args:
            context: 执行上下文
            schema_info: 数据库Schema信息
            reasoning: 推理过程
            project_id: 项目ID
            user_language: 用户语言
            analysis_insights: 分析洞察
            
        Returns:
            tuple: (结构化报告数据, 使用统计)
        """
        try:
            log.info("📊 开始生成结构化报告")
            
            # 获取项目ID（优先使用传入的参数，否则从上下文获取）
            if not project_id:
                project_id = getattr(context, 'project_id', None)

            # 使用ReportGenerator的结构化报告生成方法
            structured_report, usage = await self.report_generator.generate_structured_report(
                context=context,
                schema_info=schema_info,
                reasoning=reasoning,
                project_id=project_id,
                db_session=self.db,
                user_language=user_language,
                analysis_insights=analysis_insights,
                lifecycle_logger=getattr(self, 'lifecycle_logger', None)
            )
            
            log.info(f"✅ 结构化报告生成成功，包含 {len(structured_report.components)} 个组件")
            return structured_report, usage

        except Exception as e:
            log.error(f"❌ 结构化报告生成失败: {str(e)}")
            # 返回空的结构化报告
            from app.schemas.report import StructuredReport
            empty_report = StructuredReport(
                components=[],
                metadata={
                    "generated_at": get_shanghai_time().isoformat(),
                    "project_id": project_id,
                    "language": user_language,
                    "error": str(e)
                }
            )
            return empty_report, {"total_tokens": 0, "prompt_tokens": 0, "completion_tokens": 0}

    async def _generate_fallback_report(self, context: ExecutionContext, schema_info: str, reasoning: str = '', user_language: str = 'zh-CN') -> str:
        """
        回退的报告生成方法（使用原有逻辑）

        Args:
            context: 执行上下文
            schema_info: 数据库Schema信息
            reasoning: 推理过程
            user_language: 用户语言偏好

        Returns:
            生成的报告内容
        """
        try:
            # 使用原版的generate_result_prompt方法
            prompt_generator = StreamPromptGenerator()
            result_prompt, result_system_prompt = prompt_generator.generate_result_prompt(context, schema_info, reasoning)
            # 调用LLM生成最终答案，使用用户语言偏好
            final_answer, usage_data = await self._call_llm_for_text_response(result_prompt, result_system_prompt, user_language=user_language)
            final_answer = self._strip_markdown_markers(final_answer)

            # 🔥 记录回退报告生成的LLM调用
            if hasattr(self, 'lifecycle_logger'):
                self.lifecycle_logger.log_llm_call(
                    step_id="fallback_report_generation",
                    call_type="fallback_report",
                    user_prompt=result_prompt,
                    system_prompt=result_system_prompt,
                    llm_response=final_answer,
                    usage_data=usage_data,
                    temperature=None,
                    reasoning=reasoning
                )

            # Token用量记录（在实际调用时处理）
            if usage_data:
                log.debug(f"Token用量: {usage_data}")

            return final_answer

        except Exception as e:
            log.error(f"回退报告生成也失败: {str(e)}")
            return "抱歉，在生成分析报告时遇到了技术问题。请尝试重新提问或简化您的查询。"

    def _parse_user_clarification_answers(self, user_adjustment: str, intent_analysis: Dict[str, Any]) -> Dict[str, str]:
        """解析用户的澄清回答文本为结构化数据
        
        Args:
            user_adjustment: 用户的原始回答文本
            intent_analysis: 原始意图分析结果，包含澄清问题
            
        Returns:
            Dict[str, str]: 解析后的问答对应关系 {question_id: answer}
        """
        if not user_adjustment or not intent_analysis:
            return {}
        
        clarification_questions = intent_analysis.get('clarification_questions', [])
        if not clarification_questions:
            return {}
        
        parsed_answers = {}
        
        try:
            # 按行分割用户回答
            lines = user_adjustment.split('\n')
            
            current_question = None
            current_question_id = None
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                # 检查是否是问题行（包含问号）
                if '？' in line or '?' in line:
                    # 提取问题文本
                    question_text = line
                    if '：' in line:
                        question_text = line.split('：', 1)[1].strip()
                    elif ':' in line:
                        question_text = line.split(':', 1)[1].strip()
                    
                    # 在澄清问题中查找匹配的问题
                    for question in clarification_questions:
                        if isinstance(question, dict):
                            q_text = question.get('question', '')
                            q_id = question.get('id', '')
                            
                            # 模糊匹配问题文本
                            if q_text and (
                                question_text in q_text or 
                                q_text in question_text or
                                any(word in q_text for word in question_text.split() if len(word) > 1)
                            ):
                                current_question = q_text
                                current_question_id = q_id
                                break
                
                # 检查是否是答案行
                elif '答案：' in line or '答案:' in line:
                    if current_question_id:
                        # 提取答案
                        if '答案：' in line:
                            answer = line.split('答案：', 1)[1].strip()
                        else:
                            answer = line.split('答案:', 1)[1].strip()
                        
                        if answer:
                            parsed_answers[current_question_id] = answer
                            log.debug(f"解析到答案: {current_question_id} -> {answer}")
                        
                        # 重置当前问题
                        current_question = None
                        current_question_id = None
            
            # 如果没有解析到任何答案，但有用户输入，尝试简单匹配
            if not parsed_answers and user_adjustment.strip():
                # 如果只有一个问题，直接将用户输入作为答案
                if len(clarification_questions) == 1:
                    question = clarification_questions[0]
                    if isinstance(question, dict) and question.get('id'):
                        # 提取最后一行作为答案（通常是用户的实际回答）
                        lines = [line.strip() for line in user_adjustment.split('\n') if line.strip()]
                        if lines:
                            parsed_answers[question['id']] = lines[-1]
                            log.debug(f"单问题简单匹配: {question['id']} -> {lines[-1]}")
            
            log.info(f"解析用户澄清回答完成，共解析到 {len(parsed_answers)} 个答案")
            return parsed_answers
            
        except Exception as e:
            log.error(f"解析用户澄清回答失败: {str(e)}")
            return {}
    
    def _check_cancellation(self, analysis_id: str) -> bool:
        """检查分析是否被取消
        
        Args:
            analysis_id: 分析ID
            
        Returns:
            bool: 如果被取消返回True，否则返回False
        """
        try:
            cancel_key = f"analysis:cancel:{analysis_id}"
            cancel_signal = RedisClient.get_value(cancel_key)
            
            if cancel_signal:
                log.info(f"检测到分析取消信号 [分析ID: {analysis_id}]")
                # 记录到分析日志
                if self.analysis_logger:
                    self.analysis_logger.info("检测到取消信号", extra={
                        "event": "cancellation_detected",
                        "analysis_id": analysis_id,
                        "cancel_signal": cancel_signal
                    })
                
                # 清理Redis中的取消信号
                self._cleanup_cancel_signal(analysis_id)
                return True
                
            return False
            
        except Exception as e:
            log.error(f"检查取消信号时出错: {str(e)}")
            return False
    
    def _cleanup_cancel_signal(self, analysis_id: str):
        """清理Redis中的取消信号
        
        Args:
            analysis_id: 分析ID
        """
        try:
            cancel_key = f"analysis:cancel:{analysis_id}"
            RedisClient.delete(cancel_key)
            log.debug(f"已清理取消信号 [分析ID: {analysis_id}]")
            
            # 记录到分析日志
            if self.analysis_logger:
                self.analysis_logger.debug("已清理取消信号", extra={
                    "event": "cancel_signal_cleaned",
                    "analysis_id": analysis_id
                })
                
        except Exception as e:
            log.error(f"清理取消信号时出错: {str(e)}")
    
    @staticmethod
    def set_cancel_signal(analysis_id: str) -> bool:
        """设置分析取消信号到Redis
        
        Args:
            analysis_id: 分析ID
            
        Returns:
            bool: 成功返回True，失败返回False
        """
        try:
            cancel_key = f"analysis:cancel:{analysis_id}"
            cancel_data = {
                "cancelled": True,
                "timestamp": get_shanghai_time().isoformat(),
                "reason": "user_cancelled"
            }
            
            # 设置取消信号，过期时间5分钟（300秒）
            success = RedisClient.set_value(cancel_key, json.dumps(cancel_data), ex=300)
            
            if success:
                log.info(f"已设置分析取消信号 [分析ID: {analysis_id}]")
                return True
            else:
                log.error(f"设置取消信号失败 [分析ID: {analysis_id}]")
                return False
            
        except Exception as e:
            log.error(f"设置取消信号时出错: {str(e)}")
            return False

    @staticmethod
    def set_interrupt_signal(analysis_id: str, interrupt_type: str, stage: str, interrupt_id: str) -> bool:
        """设置分析打断信号到Redis
        
        Args:
            analysis_id: 分析ID
            interrupt_type: 打断类型 ('interrupt', 'adjust', 'cancel')
            stage: 当前分析阶段
            interrupt_id: 打断记录ID
            
        Returns:
            bool: 成功返回True，失败返回False
        """
        try:
            interrupt_key = f"analysis:interrupt:{analysis_id}"
            interrupt_data = {
                "type": interrupt_type,
                "stage": stage,
                "timestamp": get_shanghai_time().isoformat(),
                "interrupt_id": interrupt_id
            }
            
            # 设置打断信号，使用配置的过期时间
            success = RedisClient.set_value(
                interrupt_key, 
                json.dumps(interrupt_data), 
                ex=InterruptTimeoutConfig.INTERRUPT_SIGNAL_TTL
            )
            
            if success:
                log.info(f"已设置分析打断信号 [分析ID: {analysis_id}, 类型: {interrupt_type}]")
                return True
            else:
                log.error(f"设置打断信号失败 [分析ID: {analysis_id}]")
                return False
            
        except Exception as e:
            log.error(f"设置打断信号时出错: {str(e)}")
            return False

    @staticmethod
    def set_user_feedback(analysis_id: str, feedback_data: Dict[str, Any], interrupt_id: str) -> bool:
        """设置用户反馈到Redis
        
        Args:
            analysis_id: 分析ID
            feedback_data: 反馈数据
            interrupt_id: 打断记录ID
            
        Returns:
            bool: 成功返回True，失败返回False
        """
        try:
            feedback_key = f"analysis:feedback:{analysis_id}"
            feedback_info = {
                "feedback": feedback_data.get("feedback", ""),
                "adjustment_type": feedback_data.get("adjustment_type", ""),
                "reset_planning": feedback_data.get("reset_planning", False),
                "timestamp": get_shanghai_time().isoformat(),
                "interrupt_id": interrupt_id
            }
            
            # 设置反馈数据，过期时间1小时
            success = RedisClient.set_value(
                feedback_key, 
                json.dumps(feedback_info), 
                ex=3600  # 1小时
            )
            
            if success:
                log.info(f"已设置用户反馈 [分析ID: {analysis_id}]")
                return True
            else:
                log.error(f"设置用户反馈失败 [分析ID: {analysis_id}]")
                return False
            
        except Exception as e:
            log.error(f"设置用户反馈时出错: {str(e)}")
            return False

    def _check_interruption(self, analysis_id: str) -> Dict[str, Any]:
        """检查分析打断状态（扩展版本）
        
        Returns:
            Dict: {
                "action": "continue" | "wait" | "adjust" | "timeout" | "cancel",
                "interrupt_info": {...},
                "sleep_time": int  # 建议的睡眠时间（秒）
            }
        """
        try:
            # 1. 检查取消信号（保持现有逻辑）
            if self._check_cancellation(analysis_id):
                return {
                    "action": "cancel",
                    "interrupt_info": {"reason": "user_cancelled"},
                    "sleep_time": 0
                }
            
            # 2. 检查打断信号
            interrupt_key = f"analysis:interrupt:{analysis_id}"
            interrupt_signal = RedisClient.get_value(interrupt_key)
            
            if not interrupt_signal:
                return {
                    "action": "continue",
                    "interrupt_info": {},
                    "sleep_time": 0
                }
            
            interrupt_data = json.loads(interrupt_signal)
            interrupt_type = interrupt_data.get("type", "interrupt")
            interrupt_id = interrupt_data.get("interrupt_id")
            
            # 3. 处理不同类型的打断信号
            if interrupt_type == "interrupt":
                return {
                    "action": "wait",
                    "interrupt_info": {
                        "interrupt_id": interrupt_id,
                        "stage": interrupt_data.get("stage"),
                        "timestamp": interrupt_data.get("timestamp")
                    },
                    "sleep_time": InterruptTimeoutConfig.INTERRUPT_CHECK_SLEEP_INTERVAL
                }
            
            elif interrupt_type == "adjust":
                # 获取用户反馈
                feedback_key = f"analysis:feedback:{analysis_id}"
                feedback_data = RedisClient.get_value(feedback_key)
                
                if feedback_data:
                    feedback_info = json.loads(feedback_data)
                    return {
                        "action": "adjust",
                        "interrupt_info": {
                            "interrupt_id": interrupt_id,
                            "feedback": feedback_info,
                            "adjustment_type": feedback_info.get("adjustment_type")
                        },
                        "sleep_time": 0
                    }
            
            elif interrupt_type == "continue":
                # 用户取消反馈，继续分析
                return {
                    "action": "continue_after_cancel",
                    "interrupt_info": {
                        "interrupt_id": interrupt_id,
                        "reason": "user_cancelled_feedback",
                        "stage": interrupt_data.get("stage")
                    },
                    "sleep_time": 0
                }
            
            # 4. 默认继续执行
            return {
                "action": "continue",
                "interrupt_info": {},
                "sleep_time": 0
            }
            
        except Exception as e:
            log.error(f"检查打断状态时出错: {str(e)}")
            return {
                "action": "continue",
                "interrupt_info": {"error": str(e)},
                "sleep_time": 0
            }

    def _handle_interrupt_timeout(self, context: ExecutionContext, analysis_id: str) -> Dict[str, Any]:
        """处理打断超时"""
        try:
            elapsed_seconds = context.interrupt_check_count * InterruptTimeoutConfig.INTERRUPT_CHECK_SLEEP_INTERVAL
            log.warning(f"打断超时 [分析ID: {analysis_id}], 等待时间: {elapsed_seconds}秒")
            
            # 1. 清理Redis信号
            self._cleanup_interrupt_signals(analysis_id)
            
            # 3. 根据配置决定后续行为
            if InterruptTimeoutConfig.TIMEOUT_ACTION == "cancel":
                # 取消分析
                return {
                    "action": "cancel",
                    "reason": "interrupt_timeout",
                    "message": f"用户反馈超时（{InterruptTimeoutConfig.MAX_FEEDBACK_WAIT_TIME}分钟），分析已取消"
                }
            else:
                # 继续分析
                return {
                    "action": "continue",
                    "reason": "interrupt_timeout",
                    "message": f"用户反馈超时（{InterruptTimeoutConfig.MAX_FEEDBACK_WAIT_TIME}分钟），继续原分析计划"
                }
                
        except Exception as e:
            log.error(f"处理打断超时时出错: {str(e)}")
            return {
                "action": "cancel",
                "reason": "timeout_error",
                "message": f"处理超时时出错: {str(e)}"
            }

    def _cleanup_interrupt_signals(self, analysis_id: str):
        """清理所有打断相关的Redis信号"""
        try:
            keys_to_clean = [
                f"analysis:interrupt:{analysis_id}",
                f"analysis:feedback:{analysis_id}",
                f"analysis:interrupt_state:{analysis_id}"
            ]
            
            for key in keys_to_clean:
                RedisClient.delete(key)
            
            log.info(f"已清理打断信号 [分析ID: {analysis_id}]")
            
        except Exception as e:
            log.error(f"清理打断信号时出错: {str(e)}")

    def _apply_user_feedback(self, context: ExecutionContext, feedback: Dict[str, Any]):
        """应用用户反馈到分析上下文"""
        try:
            adjustment_type = feedback.get("adjustment_type")
            user_feedback = feedback.get("feedback", "")
            reset_planning = feedback.get("reset_planning", False)
            
            log.info(f"应用用户反馈 [分析ID: {context.analysis_id}], 调整类型: {adjustment_type}")
            
            # 1. 重置规划（如果用户要求）
            if reset_planning:
                context.planning_rounds = 0
                context.execution_history.clear()
                context.evaluation_history.clear()
                context.task_state = 'planning'
                log.info("已重置分析规划状态")
            
            # 2. 根据调整类型应用反馈
            if adjustment_type == "strategy":
                # 策略调整：修改查询意图或分析方向
                context.user_query = f"{context.user_query}\n\n[用户补充]: {user_feedback}"
                
            elif adjustment_type == "parameters":
                # 参数调整：影响工具参数选择
                context.user_constraints.append(f"参数约束: {user_feedback}")
                
            elif adjustment_type == "tools":
                # 工具选择调整：影响工具选择偏好
                context.tool_preferences.append(f"工具偏好: {user_feedback}")
                
            elif adjustment_type == "direction":
                # 方向调整：修改分析重点
                context.analysis_focus.append(f"分析重点: {user_feedback}")
            
            # 注意：打断记录已通过事件记录，不需要额外的数据库操作
            
        except Exception as e:
            log.error(f"应用用户反馈时出错: {str(e)}")

    # 移除_update_interrupt_record_with_adjustment方法 - 打断记录通过事件记录，不需要数据库操作
    
    async def _send_event(self, response: StreamingResponse, event_type: str, data: Dict[str, Any]):
        """发送SSE事件
        
        Args:
            response: StreamingResponse对象
            event_type: 事件类型
            data: 事件数据
        """
        event_json = json.dumps({"event": event_type, "data": data}, cls=DecimalEncoder)
        await response.write(f"data: {event_json}\n\n".encode("utf-8"))
    
    def _format_event(self, event_type: str, data: Dict[str, Any]) -> str:
        """格式化SSE事件
        
        Args:
            event_type: 事件类型
            data: 事件数据
            
        Returns:
            str: 格式化的事件数据
        """
        try:
            # 使用自定义编码器处理特殊类型
            event_json = json.dumps({"event": event_type, "data": data}, cls=DecimalEncoder, ensure_ascii=False)
            return f"data: {event_json}\n\n"
        except TypeError as e:
            # 如果序列化失败，尝试更安全的序列化方式
            log.warning(f"标准JSON序列化失败，使用备用方案: {str(e)}")
            # 创建可安全序列化的数据结构
            safe_data = self._make_json_serializable(data)
            event_json = json.dumps({"event": event_type, "data": safe_data}, ensure_ascii=False)
            return f"data: {event_json}\n\n"
            
    def _make_json_serializable(self, obj: Any) -> Any:
        """将对象转换为可JSON序列化的格式
        
        Args:
            obj: 输入对象
            
        Returns:
            可JSON序列化的对象
        """
        if isinstance(obj, dict):
            return {k: self._make_json_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_json_serializable(item) for item in obj]
        elif hasattr(obj, '__dict__'):
            # 处理自定义对象
            return self._make_json_serializable(obj.__dict__)
        elif hasattr(obj, 'isoformat'):
            # 处理日期和时间
            return obj.isoformat()
        # 特殊处理Oracle LOB对象
        elif str(type(obj)).find('cx_Oracle.LOB') >= 0 or str(type(obj)).find('oracle.LOB') >= 0:
            try:
                # 尝试将LOB读取为文本
                if hasattr(obj, 'read'):
                    content = obj.read()
                    # 处理二进制内容解码
                    if isinstance(content, bytes):
                        try:
                            return content.decode('utf-8')
                        except UnicodeDecodeError:
                            # 返回十六进制表示，但限制长度
                            hex_content = content.hex()
                            return f"[二进制LOB数据，十六进制: {hex_content[:100]}{'...' if len(hex_content) > 100 else ''}]"
                    return str(content) if content is not None else "[空LOB对象]"
                return "[LOB对象，无read方法]"
            except Exception as e:
                # 增强的LOB错误处理
                error_msg = str(e)
                if "LOB was already closed" in error_msg or "DPI-1040" in error_msg:
                    return "[LOB对象已关闭，请检查数据库连接管理]"
                elif "Operation not allowed" in error_msg:
                    return "[LOB操作不被允许，可能是事务已结束]"
                elif "ORA-22922" in error_msg:
                    return "[LOB定位符已失效]"
                else:
                    return f"[LOB读取失败: {error_msg}]"
        elif hasattr(obj, '__str__'):
            # 其他类型尝试转为字符串，增加异常处理
            try:
                return str(obj)
            except Exception as e:
                # 如果转换字符串失败（可能是LOB对象等），返回类型描述
                error_msg = str(e)
                if "LOB was already closed" in error_msg or "DPI-1040" in error_msg:
                    return "[LOB对象已关闭，无法转换为字符串]"
                else:
                    return f"<转换失败的对象: {type(obj).__name__}, 错误: {error_msg}>"
        else:
            # 无法序列化的类型替换为描述
            return f"<不可序列化对象: {type(obj).__name__}>"
    
    async def _call_llm(self, prompt: str, system_prompt: str = None, temperature: float = 0.2, user_language: str = 'zh-CN') -> tuple[Dict, Dict | None]:
        """调用LLM
        
        Args:
            prompt: 用户提示词
            system_prompt: 系统提示词，如果为None则使用默认值
            temperature: 温度参数
            user_language: 用户语言偏好
            
        Returns:
            tuple[Dict, Dict | None]: (LLM响应, token用量字典)
        """
        try:
            # 使用传入的系统提示词，如果没有则使用默认值
            if system_prompt is None:
                system_prompt = "你是一个专业的任务分析和规划助手。"
            
            # 根据用户语言偏好添加语言指令
            if user_language == 'en-US':
                # 英文指令增强
                language_prefix = "LANGUAGE REQUIREMENT: You MUST respond ONLY in English. "
                language_suffix = "\n\nCRITICAL: Your entire response must be in English language only. Do not use any Chinese characters."
            else:
                # 中文指令增强  
                language_prefix = "语言要求：你必须完全使用中文回复。"
                language_suffix = "\n\n重要提醒：你的全部回复都必须使用中文，不要使用任何英文词汇或句子。"
            
            system_prompt = language_prefix + system_prompt + language_suffix
            # 记录LLM调用信息，包含完整prompt用于调试
            prompt_length = len(prompt)
            system_prompt_length = len(system_prompt)
            prompt_preview = prompt[:100] + "..." if prompt_length > 100 else prompt
            log.info(f"正在调用LLM进行分析... [用户提示词长度: {prompt_length}字符, 系统提示词长度: {system_prompt_length}字符]")
            log.debug(f"用户提示词预览: {prompt_preview}")
            
            # 记录完整的Prompt用于调试分析
            log.info("=" * 80)
            log.info("🔍 LLM调用 - 系统提示词:")
            log.info("=" * 80)
            log.info(system_prompt)
            log.info("=" * 80)
            log.info("🔍 LLM调用 - 用户提示词:")
            log.info("=" * 80)
            log.info(prompt)
            log.info("=" * 80)
            
            # 记录到分析日志 - 详细的LLM调用信息
            if self.analysis_logger:
                self.analysis_logger.info("开始调用LLM", extra={
                    "event": "llm_call_start",
                    "prompt_length": prompt_length,
                    "system_prompt_length": system_prompt_length,
                    "model": self.model
                })
                
                # 记录完整的prompt到分析日志
                self.analysis_logger.debug("LLM完整提示词", extra={
                    "event": "llm_prompt_full",
                    "system_prompt": system_prompt,
                    "user_prompt": prompt,
                    "model": self.model
                })
            
            # 记录调用开始时间用于计算延迟
            import time
            start_time = time.time()
            
            completion = await self.llm_client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": prompt}
                ],
                response_format={"type": "json_object"},
                temperature=temperature
            )
            
            # 计算调用耗时
            elapsed_time = time.time() - start_time
            log.info(f"LLM调用完成 [耗时: {elapsed_time:.2f}秒]")
            
            # 获取响应内容和用量数据
            response_content = completion.choices[0].message.content
            usage_data = completion.usage.model_dump() if completion.usage else None
            
            # 记录到分析日志 - 包含完整响应
            if self.analysis_logger:
                self.analysis_logger.info(f"LLM调用完成，耗时 {elapsed_time:.2f}秒", extra={
                    "event": "llm_call_completed",
                    "execution_time": round(elapsed_time * 1000),  # 毫秒
                    "model": self.model,
                    "response_length": len(response_content)
                })
                
                # 记录完整的LLM响应
                self.analysis_logger.debug("LLM完整响应", extra={
                    "event": "llm_response_full",
                    "response": response_content,
                    "model": self.model,
                    "execution_time": round(elapsed_time * 1000),
                    "usage": usage_data
                })
            
            log.debug("开始处理LLM返回的JSON响应")
            
            # 尝试解析JSON内容，处理可能的转义字符问题
            try:
                # 🔧 新增：预处理markdown代码块包裹的JSON
                cleaned_content = self._clean_markdown_json(response_content)

                # 标准JSON解析尝试
                result = json.loads(cleaned_content)
                
                # 验证结果类型
                if not isinstance(result, dict):
                    log.error(f"LLM返回了非字典类型: {type(result)}, 内容: {result}")
                    # 记录到分析日志
                    if self.analysis_logger:
                        self.analysis_logger.error("LLM返回非字典类型", extra={
                            "event": "llm_response_invalid_type",
                            "result_type": type(result).__name__,
                            "result_content": str(result)[:500]
                        })
                    
                    # 返回默认的错误响应
                    return {
                        "action_type": "final_answer",
                        "reasoning": f"LLM返回格式错误: 期望字典类型，收到{type(result).__name__}类型"
                    }, usage_data
                
                log.info(f"JSON解析成功，action_type: {result.get('action_type', 'unknown')}")
                
                # 记录到分析日志
                if self.analysis_logger:
                    self.analysis_logger.debug("LLM响应JSON解析成功", extra={
                        "event": "llm_response_parsed",
                        "action_type": result.get('action_type', 'unknown'),
                        "parsed_result": result,
                        "usage": usage_data
                    })
                
                return result, usage_data
            except json.JSONDecodeError as json_err:
                log.warning(f"标准JSON解析失败，错误位置: 行 {json_err.lineno}, 列 {json_err.colno}")
                log.warning(f"错误信息: {str(json_err)}")
                
                # 记录到分析日志
                if self.analysis_logger:
                    self.analysis_logger.warning("LLM响应JSON解析失败，尝试修复", extra={
                        "event": "llm_response_parse_failed",
                        "error": str(json_err),
                        "raw_response": response_content[:1000] + "..." if len(response_content) > 1000 else response_content
                    })
                
                # 尝试修复常见的JSON错误
                log.info("开始修复JSON内容")
                # 🔧 先尝试清理markdown，再进行其他修复
                markdown_cleaned = self._clean_markdown_json(response_content)
                fixed_content = self._fix_invalid_escapes(markdown_cleaned)
                
                try:
                    # 使用修复后的内容再次尝试解析
                    result = json.loads(fixed_content)
                    
                    # 验证修复后结果的类型
                    if not isinstance(result, dict):
                        log.error(f"修复后的LLM返回仍然不是字典类型: {type(result)}, 内容: {result}")
                        # 记录到分析日志
                        if self.analysis_logger:
                            self.analysis_logger.error("修复后的LLM返回仍然非字典类型", extra={
                                "event": "llm_response_fixed_invalid_type",
                                "result_type": type(result).__name__,
                                "result_content": str(result)[:500],
                                "fixed_content": fixed_content[:500]
                            })
                        
                        # 返回默认的错误响应
                        return {
                            "action_type": "final_answer",
                            "reasoning": f"LLM返回格式错误（修复后仍然无效）: 期望字典类型，收到{type(result).__name__}类型"
                        }, usage_data
                    
                    log.info("JSON修复和解析成功")
                    
                    # 记录到分析日志
                    if self.analysis_logger:
                        self.analysis_logger.info("JSON修复成功", extra={
                            "event": "llm_response_fixed",
                            "action_type": result.get('action_type', 'unknown'),
                            "fixed_response": fixed_content,
                            "parsed_result": result,
                            "usage": usage_data
                        })
                    
                    return result, usage_data
                except json.JSONDecodeError as e:
                    log.error(f"JSON修复后仍然无法解析: {str(e)}")
                    log.error(f"问题JSON内容: {response_content}")
                    log.error(f"修复后JSON内容: {fixed_content}")
                    
                    # 记录到分析日志
                    if self.analysis_logger:
                        self.analysis_logger.error("JSON修复失败", extra={
                            "event": "llm_response_fix_failed",
                            "error": str(e),
                            "original_response": response_content,
                            "fixed_response": fixed_content
                        })
                    
                    # 返回一个有意义的默认响应
                    return {
                        "action_type": "final_answer",
                        "markdown_response": "抱歉，在处理您的请求时遇到了技术问题，无法生成完整分析。请尝试重新提问或者简化您的问题。",
                        "reasoning": "JSON解析错误导致无法继续执行"
                    }, usage_data
            
        except Exception as e:
            error_msg = f"调用LLM时出错: {str(e)}"
            log.error(error_msg)
            
            # 记录到分析日志
            if self.analysis_logger:
                self.analysis_logger.error(f"LLM调用失败: {str(e)}", extra={
                    "event": "llm_call_failed",
                    "error": str(e),
                    "error_type": type(e).__name__,
                    "prompt_length": prompt_length if 'prompt_length' in locals() else 0,
                    "system_prompt_length": system_prompt_length if 'system_prompt_length' in locals() else 0
                })
            
            # 返回默认响应而不是抛出异常
            return {
                "action_type": "final_answer",
                "markdown_response": "抱歉，在处理您的请求时发生了技术错误，无法完成分析。请稍后再试或尝试不同的问题。",
                "reasoning": f"API调用错误: {str(e)}"
            }, None
    
    def _fix_invalid_escapes(self, content: str) -> str:
        """修复JSON字符串中的无效转义字符和格式问题
        
        Args:
            content: 原始JSON字符串
            
        Returns:
            str: 修复后的JSON字符串
        """
        try:
            # 0. 预处理：清理可能的markdown包裹（如果还没有清理过）
            if '```' in content:
                content = self._clean_markdown_json(content)
                log.info("在修复过程中清理了markdown包裹")

            # 1. 首先尝试直接解析，如果成功就直接返回
            json.loads(content)
            return content
        except json.JSONDecodeError as e:
            log.info("JSON直接解析失败，开始修复流程")
            
            # 2. 处理"Extra data"错误 - 移除额外的字段
            if "Extra data" in str(e):
                try:
                    # 尝试解析到第一个完整的JSON对象
                    import re
                    
                    # 找到第一个完整的JSON对象
                    brace_count = 0
                    json_end = -1
                    
                    for i, char in enumerate(content):
                        if char == '{':
                            brace_count += 1
                        elif char == '}':
                            brace_count -= 1
                            if brace_count == 0:
                                json_end = i + 1
                                break
                    
                    if json_end > 0:
                        # 提取第一个完整的JSON对象
                        clean_content = content[:json_end]
                        try:
                            # 验证提取的JSON是否有效
                            parsed = json.loads(clean_content)
                            
                            # 检查是否包含必要的字段
                            if "action_type" in parsed:
                                # 移除不需要的字段
                                if "display_format" in parsed:
                                    del parsed["display_format"]
                                
                                # 重新序列化为干净的JSON
                                clean_json = json.dumps(parsed, ensure_ascii=False)
                                log.info("成功修复Extra data错误，移除了额外字段")
                                return clean_json
                        except json.JSONDecodeError:
                            pass
                except Exception:
                    pass
            
            # 3. 处理Markdown表格中的多余反斜杠
            import re
            
            # 3.1 找到所有的表格行（以 | 开头的行）
            table_pattern = r'\|[^\n]*\|'
            
            def fix_table_line(match):
                line = match.group(0)
                # 移除表格行中多余的反斜杠（保留必要的转义）
                line = re.sub(r'\\(?!["\\/bfnrt])', '', line)
                return line
            
            content = re.sub(table_pattern, fix_table_line, content)
            
            # 4. 处理常见的转义序列
            escape_fixes = [
                # 保留合法的转义序列
                (r'\\["\\/bfnrt]', lambda m: m.group(0)),  # 保持合法转义不变
                (r'\\u[0-9a-fA-F]{4}', lambda m: m.group(0)),  # 保持Unicode转义不变
                
                # 修复错误的转义
                (r'(?<!\\)\\(?!["\\/bfnrt]|u[0-9a-fA-F]{4})', r''),  # 移除非法的单个反斜杠
                (r'\\{3,}', r'\\'),  # 将三个及以上反斜杠替换为单个
                (r'\\\\(?!["\\/bfnrt])', r'\\'),  # 修复双反斜杠
            ]
            
            for pattern, replacement in escape_fixes:
                if callable(replacement):
                    content = re.sub(pattern, replacement, content)
                else:
                    content = re.sub(pattern, replacement, content)
            
            # 5. 处理特殊字符
            content = content.replace('\t', '\\t')
            
            # 6. 处理换行符
            # 在JSON字符串中，保持 \n 的转义，但确保它被正确转义
            content = re.sub(r'(?<!\\)\\n', r'\\n', content)  # 修复未正确转义的 \n
            content = re.sub(r'\\\\n', r'\\n', content)  # 修复双重转义的 \n
            
            # 7. 最后的安全检查
            try:
                # 尝试解析修复后的内容
                json.loads(content)
                log.info("JSON修复成功")
                return content
            except json.JSONDecodeError as e:
                log.error(f"JSON修复失败，返回保守处理的结果: {str(e)}")
                # 如果仍然失败，进行保守的清理
                # 移除所有可能导致问题的特殊字符
                content = re.sub(r'[\x00-\x1F\x7F-\x9F]', '', content)
                return content
    
    async def _call_llm_for_text_response(self, prompt: str, system_prompt: str = None, temperature: float = 0.3, user_language: str = 'zh-CN') -> tuple[str, Dict | None]:
        """调用LLM并期望纯文本响应"""
        try:
            # 使用传入的系统提示词，如果没有则使用默认值
            if system_prompt is None:
                system_prompt = "你是一个乐于助人的AI助手，负责生成用户友好的文本回复。"
            
            # 根据用户语言偏好添加语言指令
            if user_language == 'en-US':
                # 英文指令增强
                language_prefix = "LANGUAGE REQUIREMENT: You MUST respond ONLY in English. "
                language_suffix = "\n\nCRITICAL: Your entire response must be in English language only. Do not use any Chinese characters."
                if "你是" in system_prompt:
                    system_prompt = "You are a helpful AI assistant responsible for generating user-friendly text replies."
            else:
                # 中文指令增强  
                language_prefix = "语言要求：你必须完全使用中文回复。"
                language_suffix = "\n\n重要提醒：你的全部回复都必须使用中文，不要使用任何英文词汇或句子。"
            
            system_prompt = language_prefix + system_prompt + language_suffix
            
            log.info("正在调用LLM生成文本响应...")
            
            # 记录完整的Prompt用于调试分析
            log.info("=" * 80)
            log.info("📝 LLM文本响应调用 - 系统提示词:")
            log.info("=" * 80)
            log.info(system_prompt)
            log.info("=" * 80)
            log.info("📝 LLM文本响应调用 - 用户提示词:")
            log.info("=" * 80)
            log.info(prompt)
            log.info("=" * 80)
            
            completion = await self.llm_client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": prompt}
                ],
                temperature=temperature
            )
            
            response_content = completion.choices[0].message.content
            usage_data = completion.usage.model_dump() if completion.usage else None
            log.debug(f"LLM生成的文本响应: {response_content}")
            return response_content, usage_data
            
        except Exception as e:
            error_msg = f"调用LLM (文本回复模式) 时出错: {str(e)}"
            log.error(error_msg)
            
            # 返回合理的默认响应，而不是抛出异常
            return "抱歉，在生成分析报告时遇到了技术问题。请尝试重新提问或简化您的查询。如果问题持续，请联系系统管理员。", None
    
    def _strip_markdown_markers(self, text: str) -> str:
        """移除字符串中的markdown代码块标记"""
        text = text.strip()
        if text.startswith("```markdown"):
            text = text[len("```markdown"):].strip()
        elif text.startswith("```"):
            text = text[3:].strip()
        
        if text.endswith("```"):
            text = text[:-3].strip()
            
        return text

    def _clean_markdown_json(self, content: str) -> str:
        """清理LLM返回的可能被markdown包裹的JSON内容

        Args:
            content: 原始内容，可能包含markdown代码块标记

        Returns:
            str: 清理后的JSON字符串
        """
        import re

        # 移除前后空白
        content = content.strip()

        # 方法1: 使用正则表达式提取markdown代码块中的JSON
        json_pattern = r'```(?:json)?\s*(.*?)\s*```'
        match = re.search(json_pattern, content, re.DOTALL)

        if match:
            # 找到了markdown代码块，提取其中的内容
            extracted_content = match.group(1).strip()
            log.info("成功从markdown代码块中提取JSON内容")
            return extracted_content

        # 方法2: 如果没有找到完整的代码块，尝试移除可能的标记
        # 移除开头的markdown标记
        if content.startswith('```json'):
            content = content[7:].strip()
        elif content.startswith('```'):
            content = content[3:].strip()

        # 移除结尾的markdown标记
        if content.endswith('```'):
            content = content[:-3].strip()

        # 方法3: 更激进的清理 - 查找第一个{到最后一个}
        if not content.startswith('{'):
            # 找到第一个{的位置
            start_pos = content.find('{')
            if start_pos != -1:
                content = content[start_pos:]

        if not content.endswith('}'):
            # 找到最后一个}的位置
            end_pos = content.rfind('}')
            if end_pos != -1:
                content = content[:end_pos + 1]

        return content

    async def generate_stream(self, query: str, project_id: str, user_id: str, background_tasks, max_planning_rounds: int = 25, continue_analysis_id: str = None, conversation_id: str = None, round_number: int = 1, history_context_data: Dict[str, Any] = None, intent_adjustment: str = None) -> AsyncGenerator[str, None]:
        """生成流式分析响应
        
        Args:
            query: 用户查询（原始查询，保持不变）
            project_id: 项目ID
            user_id: 用户ID（用于Token统计）
            background_tasks: FastAPI后台任务对象（用于异步Token记录）
            max_planning_rounds: 最大规划轮数，默认为25
            continue_analysis_id: 可选，指定要继续的分析ID（用于意图确认后继续）
            conversation_id: 可选，会话ID（用于多轮对话）
            round_number: 可选，轮次编号（用于多轮对话）
            history_context_data: 可选，上下文数据（用于多轮分析）
            intent_adjustment: 可选，用户的意图补充/调整信息（不修改原始查询）
            
        Yields:
            str: 格式化的事件数据
        """
        # 获取用户语言偏好
        user_language = 'zh-CN'  # 默认中文
        try:
            from app.models.user import User
            user = self.db.query(User).filter(User.id == user_id).first()
            if user and user.language_preference:
                user_language = user.language_preference
                log.info(f"用户语言偏好: {user_language}")
            else:
                log.info(f"用户未设置语言偏好，使用默认中文")
        except Exception as e:
            log.warning(f"获取用户语言偏好失败，使用默认中文: {str(e)}")
        
        # 创建执行上下文并记录日志
        log.info(f"开始创建分析执行上下文 [查询: {query[:50]}{'...' if len(query) > 50 else ''}]")
        context = ExecutionContext(user_query=query)
        context.max_planning_rounds = max_planning_rounds
        context.user_language = user_language  # 保存用户语言偏好到上下文

        # 🔥 将生命周期日志记录器添加到上下文中，供其他组件使用
        if hasattr(self, 'lifecycle_logger'):
            context.lifecycle_logger = self.lifecycle_logger
        
        # 存储多轮分析的上下文数据
        if history_context_data:
            context.multi_round_context = history_context_data
            log.info(f"已加载多轮分析上下文数据: {history_context_data.get('context_summary', '无摘要')[:100]}...")
        
        # 记录用户输入的详细信息
        user_input_info = {
            "user_query": query,
            "project_id": project_id,
            "max_planning_rounds": max_planning_rounds,
            "continue_analysis_id": continue_analysis_id,
            "query_length": len(query),
            "is_continuation": continue_analysis_id is not None,
            "timestamp": get_shanghai_time().isoformat()
        }
        
        # 初始化步骤拦截器变量
        step_interceptor = None
        event_buffer = []
        
        # @fixed_implementation_start
        # 实现标识: 事件拦截和步骤记录机制
        # 功能描述: 该实现确保所有关键分析事件都被步骤拦截器正确拦截和保存到数据库，解决了工具执行记录和分析报告丢失的问题。
        # 修复内容: 统一使用yield_and_intercept_event函数处理所有关键事件(start, step_started, step_completed, report_generated等)，确保步骤记录完整性。
        # 状态: 已确认 (CONFIRMED_IMPLEMENTATION)
        # @fixed_implementation_end
        def yield_and_intercept_event(event_type: str, event_data: Dict[str, Any]):
            """发送事件并拦截到步骤拦截器"""
            formatted_event = self._format_event(event_type, event_data)
            
            # 拦截事件
            if step_interceptor:
                try:
                    step_interceptor.intercept_event(event_type, event_data)
                except Exception as e:
                    log.warning(f"拦截{event_type}事件失败: {str(e)}")
            else:
                # 如果步骤拦截器未初始化，缓存事件
                event_buffer.append(formatted_event)
                log.debug(f"缓存事件等待步骤拦截器初始化: {event_type}")
            
            return formatted_event
        
        # @fixed_implementation_start
        # 实现标识: 函数作用域修复
        # 功能描述: 修复yield_and_intercept_event函数的作用域问题，确保函数在使用前已定义
        # 修复内容: 将yield_and_intercept_event函数定义移到第一次使用之前，解决"cannot access local variable"错误
        # 状态: 已确认 (CONFIRMED_IMPLEMENTATION)
        # @fixed_implementation_end
        
        # 发送开始事件
        log.debug(f"发送分析开始事件 [最大规划轮数: {max_planning_rounds}]")
        yield yield_and_intercept_event("start", {"message": "分析开始", "query": query, "max_planning_rounds": max_planning_rounds})
        
        # 初始化Token统计相关变量
        organization_id = None
        token_usage_service = None
        
        def record_token_usage_async(usage_data: Dict):
            """异步记录Token用量的辅助函数"""
            if usage_data and self.model_id and organization_id:
                from app.services.token_usage_service import record_usage_task
                # 提取详细的Token数量
                prompt_tokens = usage_data.get('prompt_tokens', 0)
                completion_tokens = usage_data.get('completion_tokens', 0)
                total_tokens = usage_data.get('total_tokens', 0)
                
                # 如果有任何Token使用，就记录
                if total_tokens > 0 or prompt_tokens > 0 or completion_tokens > 0:
                    background_tasks.add_task(
                        record_usage_task,
                        user_id=user_id,
                        org_id=organization_id,
                        project_id=project_id,
                        analysis_id=context.analysis_id,
                        model_id=self.model_id,
                        model_name=self.model,
                        prompt_tokens=prompt_tokens,
                        completion_tokens=completion_tokens,
                        total_tokens=total_tokens
                    )
        
        try:
            # 检查是否需要继续现有的分析记录
            analysis = None
            is_continuing = False
            cached_context = None  # 初始化 cached_context 变量
            
            # 1. 如果指定了continue_analysis_id，优先尝试从内存缓存恢复
            if continue_analysis_id:
                log.info(f"尝试从内存缓存恢复分析上下文 [分析ID: {continue_analysis_id}]")
                
                # 特别记录用户回复内容(新增)
                log.info(f"用户回复内容: {query}")
                
                # 尝试从内存缓存获取上下文
                cached_context = await context_store.get(continue_analysis_id)
                if cached_context:

                    # 验证缓存中的上下文是否确实对应当前的分析ID
                    cached_analysis_id = cached_context.get('analysis_id')
                    if cached_analysis_id and str(cached_analysis_id) == str(continue_analysis_id):
                        log.info(f"从内存缓存成功恢复上下文 [分析ID: {continue_analysis_id}]")
                    else:
                        log.warning(f"缓存中的分析ID不匹配！期望: {continue_analysis_id}, 实际: {cached_analysis_id}")
                        cached_context = None  # 清空无效的缓存上下文
                    
                    # 重建ExecutionContext对象
                    try:
                        # 检查是否是意图确认回复 - 添加详细调试
                        is_intent_confirmation = cached_context.get('waiting_for_intent_confirmation', False)
                        has_intent_analysis = cached_context.get('intent_analysis_result') is not None
                        has_original_query = bool(cached_context.get('original_user_query', '').strip())
                        
                        # 添加详细的调试日志
                        log.info(f"🔍 意图确认判断: is_intent_confirmation={is_intent_confirmation}, has_intent_analysis={has_intent_analysis}, has_original_query={has_original_query}")
                        log.info(f"🔍 intent_adjustment 参数: '{intent_adjustment}'")
                        log.info(f"🔍 cached_context keys: {list(cached_context.keys()) if cached_context else 'None'}")
                        
                        # 简化判断条件：主要检查是否有waiting_for_intent_confirmation标志
                        if is_intent_confirmation:
                            log.info("✅ 检测到意图确认回复，处理用户的意图调整")
                            
                            # 获取原始查询和意图分析
                            original_query = cached_context.get('original_user_query', '') or cached_context.get('user_query', '')
                            original_intent_analysis = cached_context.get('intent_analysis_result', {})
                            
                            log.info(f"🔍 原始查询: '{original_query}'")
                            log.info(f"🔍 补充信息: '{intent_adjustment}'")
                            
                            # 重新构建意图分析结果（保持原始意图分析不变，单独记录用户调整）
                            if intent_adjustment and intent_adjustment.strip():
                                # 用户有实际补充信息，但保持原始意图分析结构不变
                                # 只在上下文中标记有用户调整，供后续分析参考
                                context.intent_analysis_result = original_intent_analysis.copy()
                                context.intent_analysis_result['user_adjustment'] = intent_adjustment
                                context.intent_analysis_result['has_user_modification'] = True
                                log.info(f"已记录用户补充信息：'{intent_adjustment}'，保持原始意图分析结构")
                            else:
                                # 用户没有补充信息或只是确认，保持原始意图分析
                                context.intent_analysis_result = original_intent_analysis.copy()
                                context.intent_analysis_result['has_user_modification'] = False
                                log.info(f"用户确认原始意图分析，保持不变")
                            
                            # **关键改动：保持原始查询不变**
                            context.user_query = original_query
                            context.task_state = 'planning'  # 重新开始规划
                            context.execution_history = []  # 清空执行历史，重新开始
                            context.execution_results = {}
                            context.planning_rounds = 0  # 重置规划轮数
                            context.max_planning_rounds = max_planning_rounds
                            context.analysis_id = continue_analysis_id
                            
                            # @fixed_implementation_start
                            # 实现标识: 避免重复意图确认事件推送
                            # 功能描述: 移除继续分析时的重复step_started和step_completed事件
                            # 修复内容: 只推送intent_confirmation_completed事件，避免前端重复显示
                            # 状态: 已确认 (CONFIRMED_IMPLEMENTATION)
                            # @fixed_implementation_end
                            
                            # 创建清晰的意图确认结果
                            has_user_modification = bool(intent_adjustment and intent_adjustment.strip())
                            
                            # 解析用户的澄清回答为结构化数据
                            parsed_clarification_answers = {}
                            if has_user_modification and original_intent_analysis:
                                parsed_clarification_answers = self._parse_user_clarification_answers(
                                    intent_adjustment, original_intent_analysis
                                )
                            
                            intent_confirmation_result = {
                                "original_query": original_query,
                                "user_adjustment": intent_adjustment or "",
                                "has_user_modification": has_user_modification,
                                "confirmation_completed": True,
                                # 保持原始意图分析不变
                                "intent_analysis": original_intent_analysis,
                                # 使用解析后的结构化澄清答案
                                "clarification_answers": parsed_clarification_answers
                            }
                            
                            # 发送专门的用户意图确认事件给步骤拦截器处理
                            yield yield_and_intercept_event("user_intent_confirmation", {
                                "original_query": original_query,
                                "user_adjustment": intent_adjustment or "",
                                "has_user_modification": has_user_modification,
                                "confirmation_completed": True,
                                "intent_analysis": original_intent_analysis,
                                "clarification_answers": parsed_clarification_answers,
                                "message": "用户完成了意图确认和调整"
                            })
                            
                            # 记录到执行历史
                            # context.add_execution_record(
                            #     tool_name="意图确认",
                            #     parameters={"user_adjustment": intent_adjustment or "", "original_query": original_query},
                            #     result=intent_confirmation_result,
                            #     reasoning="处理用户对意图分析的确认和调整，保持原始查询不变"
                            # )
                            
                            # 推送意图确认完成事件（兼容前端）
                            yield yield_and_intercept_event("intent_confirmation_completed", {
                                "original_query": original_query,
                                "user_adjustment": intent_adjustment or "",
                                "message": "意图确认完成，继续执行分析"
                            })
                            
                            # 清除意图确认等待状态
                            cached_context.pop('waiting_for_intent_confirmation', None)
                            cached_context.pop('original_user_query', None)
                            
                            is_continuing = True
                            
                            # 从数据库获取分析记录
                            analysis = self.db.query(AnalysisModel).filter(AnalysisModel.id == continue_analysis_id).first()
                            
                            log.info(f"意图确认完成，使用增强查询继续分析")
                            
                            # 发送意图确认完成事件（保留原有事件以兼容前端）
                            yield self._format_event("intent_confirmation_completed", {
                                "id": continue_analysis_id,
                                "original_query": original_query,
                                "user_adjustment": intent_adjustment or "",
                                "message": "意图确认完成，继续分析"
                            })
                            
                        else:
                            # @fixed_implementation_start
                            # 实现标识: 简化的继续分析处理
                            # 功能描述: 移除复杂的澄清历史处理，简化为基本的分析恢复逻辑，直接继续执行分析
                            # 复用指引: 简化继续分析逻辑，不再处理澄清相关的复杂流程
                            # 状态: 已确认 (CONFIRMED_IMPLEMENTATION)
                            # @fixed_implementation_end
                            
                            log.info("检测到继续分析，直接恢复执行上下文")
                            
                            # 保持原始查询不变，记录补充信息到上下文中
                            original_query = cached_context.get('user_query', '数据分析查询')
                            if intent_adjustment:
                                log.info(f"记录用户补充信息: {intent_adjustment}")
                            
                            # 恢复ExecutionContext - 保持原始查询
                            context.user_query = original_query
                            context.task_state = cached_context.get('task_state', 'planning')
                            context.execution_history = cached_context.get('execution_history', [])
                            context.execution_results = cached_context.get('execution_results', {})
                            context.planning_rounds = cached_context.get('planning_rounds', 0)
                            context.max_planning_rounds = max_planning_rounds  # 使用新的最大轮数
                            context.current_plan = cached_context.get('current_plan')
                            context.analysis_id = continue_analysis_id
                            
                            is_continuing = True
                            
                            # 从数据库获取分析记录
                            analysis = self.db.query(AnalysisModel).filter(AnalysisModel.id == continue_analysis_id).first()
                            
                            log.info(f"从缓存恢复上下文成功：执行历史={len(context.execution_history)}条, 规划轮数={context.planning_rounds}")
                            
                            # 发送继续分析事件
                            yield self._format_event("analysis_continued", {
                                "id": continue_analysis_id,
                                "original_query": original_query,
                                "user_adjustment": intent_adjustment or "",
                                "message": "从内存缓存恢复分析上下文，继续执行"
                            })
                            
                    except Exception as e:
                        log.warning(f"从缓存恢复上下文失败，将尝试数据库恢复: {str(e)}")
                        cached_context = None
                
                # 如果内存缓存失败，fallback到数据库恢复
                # 如果内存缓存失败，fallback到数据库恢复
                if not cached_context:
                    log.info(f"内存缓存恢复失败，尝试从数据库恢复 [分析ID: {continue_analysis_id}]")
                analysis = self.db.query(AnalysisModel).filter(AnalysisModel.id == continue_analysis_id).first()
                if analysis:
                    is_continuing = True
                    context.analysis_id = analysis.id
                    log.info(f"从数据库找到分析记录，将加载执行历史")
                    
                        # @fixed_implementation_start
                        # 实现标识: 简化的数据库恢复处理
                        # 功能描述: 移除复杂的查询合并逻辑，简化为直接使用新查询或原查询
                        # 复用指引: 简化查询处理，不再使用复杂的澄清历史合并功能
                        # 状态: 已确认 (CONFIRMED_IMPLEMENTATION)
                        # @fixed_implementation_end
                        
                        # 保持原始查询不变
                    original_query = analysis.query
                    context.user_query = original_query
                    
                    if intent_adjustment:
                        log.info(f"数据库恢复时记录用户补充信息: {intent_adjustment}")
                    
                    # 发送继续分析事件
                    yield self._format_event("analysis_continued", {
                        "id": analysis.id,
                        "original_query": original_query,
                        "user_adjustment": intent_adjustment or "",
                        "message": "从数据库恢复分析记录，继续执行"
                    })
                    
                    # 加载执行历史
                    await self._load_execution_history(context, analysis.id)
            
            # 2. 如果没有指定continue_analysis_id，直接创建新的分析记录
            if not analysis:
                log.info(f"创建新的分析记录 [项目ID: {project_id}]")
                analysis = AnalysisModel(
                    query=query,
                    project_id=project_id,
                    result=None,
                    conversation_id=conversation_id,  # 设置会话ID
                    round_number=round_number  # 设置轮次号
                )
                self.db.add(analysis)
                self.db.commit()
                
                # 发送分析创建事件
                yield self._format_event("analysis_created", {
                    "id": analysis.id,
                    "query": query,
                    "project_id": project_id
                })
                
                # 初始化步骤拦截器
                from app.services.step_interceptor import StepInterceptor
                step_interceptor = StepInterceptor(self.db, analysis.id)
                log.info(f"步骤拦截器已初始化: {analysis.id}")
                
                # 处理缓冲区中的事件
                for buffered_event in event_buffer:
                    try:
                        if 'data: ' in buffered_event:
                            buffered_event_data = json.loads(buffered_event.split('data: ')[1])
                            buffered_event_type = buffered_event_data.get('event')
                            buffered_event_payload = buffered_event_data.get('data', {})
                            
                            if buffered_event_type:
                                step_interceptor.intercept_event(buffered_event_type, buffered_event_payload)
                                log.debug(f"处理缓冲事件: {buffered_event_type}")
                    except Exception as e:
                        log.warning(f"处理缓冲事件失败: {str(e)}")
                
                # 清空缓冲区
                event_buffer.clear()
                
                # 处理当前的analysis_created事件
                step_interceptor.intercept_event('analysis_created', {"id": analysis.id, "query": query, "project_id": project_id})
            
            context.analysis_id = analysis.id
            log.info(f"使用分析记录 [分析ID: {analysis.id}，是否继续: {is_continuing}]")
            
            # 创建分析日志记录器(现在有了analysis_id)
            from app.core.logger import AnalysisLogger
            self.analysis_logger = AnalysisLogger(project_id=project_id, analysis_id=str(analysis.id))

            # 🔥 创建分析生命周期日志记录器
            from app.core.analysis_lifecycle_logger import AnalysisLifecycleLogger
            self.lifecycle_logger = AnalysisLifecycleLogger(
                analysis_id=str(analysis.id),
                project_id=project_id
            )

            # 记录分析开始
            self.lifecycle_logger.log_step_start(
                step_type="analysis_session",
                step_id="session_start",
                description="分析会话开始",
                user_query=query,
                project_id=project_id,
                is_continuing=is_continuing
            )
            
            # 记录用户输入信息到分析日志
            self.analysis_logger.info("用户输入信息记录", extra={
                "event": "user_input_recorded",
                **user_input_info
            })
            
            # 初始化Token用量服务并检查限额
            from app.services.token_usage_service import TokenUsageService
            from app.models.project import Project
            
            token_usage_service = TokenUsageService(self.db)
            project = self.db.query(Project).filter(Project.id == project_id).first()
            if not project:
                yield self._format_event("error", {"message": "项目不存在"})
                return
            organization_id = project.org_id
            
            # 检查Token限额
            if not token_usage_service.has_tokens_remaining(organization_id):
                yield self._format_event("error", {
                    "message": "组织Token用量已达上限，请联系管理员。",
                    "error_type": "token_limit_exceeded"
                })
                return
            
            # 如果是澄清回复，记录澄清信息
            # 移除澄清相关的处理逻辑
            
            # 记录初始化信息
            self.analysis_logger.info("LLM流式分析器已初始化", extra={
                "event": "llm_analyzer_initialized",
                "model": self.model,
                "analysis_id": analysis.id,
                "is_continuing": is_continuing,
                "cached_recovery": is_continuing and cached_context is not None
            })
            
            # 缓存执行上下文到内存
            await context_store.set(str(analysis.id), context)
            log.debug(f"执行上下文已缓存到内存 [分析ID: {analysis.id}]")
            
            # 记录到分析日志
            if is_continuing:
                self.analysis_logger.info("继续现有分析的流式生成", extra={
                    "event": "stream_generation_continued",
                    "original_query": analysis.query,
                    "clarification_response": query,
                    "max_planning_rounds": max_planning_rounds,
                    "cached_recovery": cached_context is not None
                })
            else:
                self.analysis_logger.info("开始新的流式分析", extra={
                    "event": "stream_generation_start",
                    "query": query,
                    "max_planning_rounds": max_planning_rounds
                })
                
                self.analysis_logger.info("分析记录已创建", extra={
                    "event": "analysis_record_created",
                    "analysis_id": analysis.id,
                    "project_id": project_id
                })
            
            # 获取所有工具
            try:
                log.info(f"开始加载项目工具 [项目ID: {project_id}]")
                from app.services.analyzer import AnalyzerService
                analyzer = AnalyzerService(db=self.db)
                
                all_tools = await analyzer.get_available_tools(project_id, user_language=user_language)
                log.info(f"项目工具加载完成，共 {len(all_tools)} 个工具")
                
                # 记录到分析日志
                if self.analysis_logger:
                    self.analysis_logger.info(f"已加载 {len(all_tools)} 个工具", extra={
                        "event": "tools_loaded",
                        "tool_count": len(all_tools),
                        "tools": [{"name": tool.name, "type": tool.tool_type} for tool in all_tools]
                    })
                
                # 确保每个工具都有display_format属性
            except Exception as e:
                log.error(f"加载项目工具失败: {str(e)}")
                if self.analysis_logger:
                    self.analysis_logger.error(f"加载项目工具失败: {str(e)}", extra={
                        "event": "tools_loading_failed",
                        "error": str(e)
                    })
                raise
            formatted_tools = []
            for tool in all_tools:
                # 处理工具参数，确保是有效的JSON对象
                tool_parameters = {}
                try:
                    if tool.parameters:
                        if isinstance(tool.parameters, str):
                            tool_parameters = json.loads(tool.parameters)
                        else:
                            tool_parameters = tool.parameters
                except Exception as e:
                    log.warning(f"解析工具参数失败: {str(e)}")
                
                formatted_tools.append({
                    "id": tool.id,
                    "name": tool.name,
                    "description": tool.description,
                    "tool_type": tool.tool_type,
                    "parameters": tool_parameters,
                    "display_format": getattr(tool, 'display_format', 'json')  # 使用默认值'json'
                })
            
            # 存储可用工具
            context.available_tools = formatted_tools

            # 发送工具列表事件
            yield yield_and_intercept_event("tools_loaded", {
                "tool_count": len(all_tools),
                "message": f"已加载 {len(all_tools)} 个分析工具"
            })
            
            # 获取schema信息
            schema_info = await self._get_schema_info(project_id)
            
            # 发送schema加载事件
            yield self._format_event("schema_loaded", {"schema_info": "Schema加载完成"})
            
            # **新增：意图确认步骤** - 只在新分析时执行
            if not is_continuing:
                log.info("开始意图分析和确认流程")
                
                # 生成步骤ID
                intent_step_id = self._generate_unique_step_id(str(context.analysis_id), "intent_analysis")

                # 🔥 记录意图分析步骤开始
                self.lifecycle_logger.log_step_start(
                    step_type="intent_analysis",
                    step_id=intent_step_id,
                    description="用户意图分析和确认",
                    user_query=context.user_query,
                    schema_info_length=len(schema_info),
                    has_history_context=bool(history_context_data)
                )

                # 生成意图分析提示词（传递多轮上下文）
                try:
                    intent_analysis_result = await self._analyze_user_intent(
                        context.user_query,
                        formatted_tools,
                        schema_info,
                        project_id,
                        conversation_id=conversation_id,
                        history_context_data=history_context_data,
                        user_language=user_language,
                        step_id=intent_step_id  # 传递step_id用于日志记录
                    )
                except Exception as e:
                    log.error(f"意图分析失败，无法继续: {str(e)}")
                    # 发送错误事件并退出
                    yield self._format_event("error", {
                        "message": f"意图分析失败: {str(e)}",
                        "error_type": "intent_analysis_failed"
                    })
                    return
                
                # 检查是否为数据无关的情况
                if intent_analysis_result.get("action_type") == "data_irrelevant":
                    log.info("用户问题与数据库数据无关，直接返回固定提示并结束分析")
                    
                    # 获取固定提示消息
                    data_irrelevant_message = intent_analysis_result.get("message", 
                        "抱歉，您的问题与当前数据库中的数据无关。我是一个专门用于数据分析的AI助手，主要帮助您分析和查询数据库中的业务数据。如果您有关于数据分析、报表生成、数据查询等方面的问题，我很乐意为您提供帮助。")
                    
                    # 更新分析记录
                    try:
                        from app.models.analysis import Analysis
                        analysis = self.db.query(Analysis).filter(Analysis.id == context.analysis_id).first()
                        if analysis:
                            analysis.result = data_irrelevant_message
                            analysis.status = 'completed'
                            analysis.updated_at = get_shanghai_time()
                            self.db.commit()
                            log.info(f"数据无关分析完成，状态已更新: {context.analysis_id}")
                    except Exception as e:
                        log.error(f"更新分析记录时出错: {str(e)}")
                    
                    # 发送报告生成事件
                    yield yield_and_intercept_event("report_generated", {
                        "report": data_irrelevant_message,
                        "data_irrelevant": True
                    })
                    
                    # 发送完成事件
                    yield self._format_event("completed", {
                        "id": context.analysis_id,
                        "message": "分析已完成"
                    })
                    
                    return
                
                # 保存意图分析结果到上下文
                context.intent_analysis_result = intent_analysis_result

                # 记录意图分析到执行历史（不发送step事件，避免创建额外步骤）
                # context.add_execution_record(
                #     tool_name="意图确认",
                #     parameters={"user_query": context.user_query},
                #     result=intent_analysis_result,
                #     reasoning="分析用户查询意图，理解需求并制定执行计划"
                # )
                
                # 意图识别是必须的前序步骤，总是发送意图确认请求
                log.info("意图识别完成，发送意图确认请求")
                
                # 发送意图分析结果，等待用户确认
                yield self._format_event("intent_confirmation_request", {
                    "message": "请确认分析意图和计划",
                    "intent_analysis": intent_analysis_result,
                    "user_query": context.user_query,
                    "analysis_id": analysis.id
                })
                
                # 保存等待确认状态到缓存
                context_data = {
                    **context.__dict__,
                    "waiting_for_intent_confirmation": True,
                    "original_user_query": context.user_query,
                    "intent_analysis_result": intent_analysis_result
                }
                await context_store.set(str(analysis.id), context_data)
                
                # 暂停流程，等待用户确认
                log.info("意图分析完成，等待用户确认")
                
                # 发送暂停完成事件，告知前端分析已暂停等待意图确认
                yield self._format_event("completed", {
                    "id": context.analysis_id,
                    "message": "分析已暂停，等待用户确认意图",
                    "status": "waiting_for_intent_confirmation"
                })
                return
            
            # **新增：Schema优化步骤**
            # 在开始规划之前，如果有意图分析结果，则进行Schema优化
            if is_continuing and context.intent_analysis_result:
                log.info("开始Schema智能筛选优化")
                
                # 发送Schema优化开始事件
                yield self._format_event("schema_optimization_started", {
                    "message": "正在根据分析意图优化Schema信息"
                })
                
                try:
                    # 调用Schema优化方法
                    optimized_schema, usage_data = await self._optimize_schema_for_intent(
                        context.intent_analysis_result,
                        context.user_query,
                        schema_info,
                        user_language=context.user_language
                    )

                    # 异步记录Token用量
                    if usage_data:
                        record_token_usage_async(usage_data)
                    
                    # 更新Schema信息
                    schema_info = optimized_schema
                    
                    # 发送Schema优化完成事件
                    yield self._format_event("schema_optimization_completed", {
                        "message": "Schema优化完成，已筛选出相关数据表",
                        "optimization_applied": True
                    })
                    
                    log.info("Schema优化完成，已更新Schema信息用于后续分析")
                    
                except Exception as e:
                    log.warning(f"Schema优化失败，使用原始Schema: {str(e)}")
                    yield self._format_event("schema_optimization_failed", {
                        "message": f"Schema优化失败，使用原始Schema: {str(e)}",
                        "optimization_applied": False
                    })
            
            # 发送开始规划事件
            yield self._format_event("planning_started", {"message": "开始规划分析步骤"})
            
            # 初始化阶段
            context.task_state = "planning"
            
            # @fixed_implementation_start
            # 实现标识: 移除澄清回复处理
            # 功能描述: 移除澄清回复处理逻辑，简化继续分析流程，直接进入主分析循环
            # 复用指引: 不再处理澄清回复的复杂逻辑，简化为直接的分析继续
            # 状态: 已确认 (CONFIRMED_IMPLEMENTATION)
            # @fixed_implementation_end
            
            # 主循环
            while not context.is_completed():
                # 检查打断和取消状态
                if context.analysis_id:
                    # **优先检查ExecutionContext的超时状态**
                    if context.interrupt_status == 'interrupted' and context.is_interrupt_timeout():
                        log.warning(f"主循环检测到打断超时 [分析ID: {context.analysis_id}]")

                        # 处理超时
                        timeout_result = self._handle_interrupt_timeout(context, str(context.analysis_id))

                        if timeout_result.get("action") == "cancel":
                            # 发送超时取消事件
                            yield self._format_event("completed", {
                                "id": context.analysis_id,
                                "message": timeout_result.get("message", "分析已取消"),
                                "cancelled": True,
                                "reason": "interrupt_timeout"
                            })
                            return
                        else:
                            # 继续分析
                            context.reset_interrupt_status()
                            yield self._format_event("interrupt_timeout", {
                                "message": timeout_result.get("message", "超时后继续分析"),
                                "action": "continue",
                                "interrupt_id": context.last_interrupt_id
                            })

                    interrupt_check_result = self._check_interruption(str(context.analysis_id))
                    action = interrupt_check_result.get("action", "continue")
                    interrupt_info = interrupt_check_result.get("interrupt_info", {})
                    sleep_time = interrupt_check_result.get("sleep_time", 0)
                    
                    # 处理取消
                    if action == "cancel":
                        if interrupt_info.get("reason") == "user_cancelled":
                            log.info(f"分析被用户取消 [分析ID: {context.analysis_id}]")
                            
                            # 记录到分析日志
                            if self.analysis_logger:
                                self.analysis_logger.info("分析被用户取消", extra={
                                    "event": "analysis_cancelled_by_user",
                                    "analysis_id": context.analysis_id,
                                    "planning_rounds": context.planning_rounds
                                })
                            
                            # 更新分析记录状态
                            try:
                                from app.models.analysis import Analysis
                                analysis = self.db.query(Analysis).filter(Analysis.id == context.analysis_id).first()
                                if analysis:
                                    analysis.result = "分析被用户取消"
                                    analysis.updated_at = get_shanghai_time()
                                    self.db.commit()
                            except Exception as e:
                                log.error(f"更新分析记录状态时出错: {str(e)}")
                            
                            # 发送取消事件
                            yield self._format_event("completed", {
                                "id": context.analysis_id,
                                "message": "分析已被用户取消",
                                "cancelled": True
                            })
                            return
                        else:
                            # 打断超时取消
                            log.info(f"分析因打断超时而取消 [分析ID: {context.analysis_id}]")
                            
                            # 发送超时取消事件
                            yield self._format_event("completed", {
                                "id": context.analysis_id,
                                "message": interrupt_info.get("message", "分析已取消"),
                                "cancelled": True,
                                "reason": "interrupt_timeout"
                            })
                            return
                    
                    # 处理等待用户反馈
                    elif action == "wait":
                        # **关键修复：首次进入wait状态时，启动ExecutionContext的打断状态**
                        if context.interrupt_status == 'none':
                            interrupt_id = interrupt_info.get("interrupt_id", f"interrupt_{context.analysis_id}")
                            context.start_interrupt(interrupt_id)
                            log.info(f"启动ExecutionContext打断状态 [分析ID: {context.analysis_id}, 打断ID: {interrupt_id}]")

                        # 增加打断检查计数
                        context.increment_interrupt_check()

                        # 检查是否超时
                        if context.is_interrupt_timeout():
                            log.warning(f"打断等待超时 [分析ID: {context.analysis_id}]")
                            
                            # 处理超时
                            timeout_result = self._handle_interrupt_timeout(context, str(context.analysis_id))
                            
                            if timeout_result.get("action") == "cancel":
                                yield self._format_event("completed", {
                                    "id": context.analysis_id,
                                    "message": timeout_result.get("message", "分析已取消"),
                                    "cancelled": True,
                                    "reason": "interrupt_timeout"
                                })
                                return
                            else:
                                # 继续分析
                                context.reset_interrupt_status()
                                yield self._format_event("interrupt_timeout", {
                                    "message": timeout_result.get("message", "超时后继续分析"),
                                    "action": "continue",
                                    "interrupt_id": context.last_interrupt_id  # 添加打断ID
                                })
                        else:
                            # 计算准确的剩余时间（使用实际时间差）
                            if context.interrupt_start_time:
                                elapsed_seconds = (get_shanghai_time() - context.interrupt_start_time).total_seconds()
                                elapsed_minutes = elapsed_seconds / 60
                                remaining_minutes = max(0, InterruptTimeoutConfig.MAX_FEEDBACK_WAIT_TIME - elapsed_minutes)

                                # 发送等待状态更新（每30秒发送一次，且剩余时间大于0）
                                if context.interrupt_check_count % 15 == 0 and remaining_minutes > 0:  # 30秒 / 2秒 = 15次
                                    yield self._format_event("waiting_feedback", {
                                        "message": f"等待用户反馈中... 剩余时间: {remaining_minutes:.1f}分钟",
                                        "elapsed_minutes": elapsed_minutes,
                                        "remaining_minutes": remaining_minutes,
                                        "interrupt_id": interrupt_info.get("interrupt_id")
                                    })

                            # 睡眠避免CPU密集检查
                            if sleep_time > 0:
                                await asyncio.sleep(sleep_time)
                            continue
                    
                    # 处理用户反馈调整
                    elif action == "adjust":
                        log.info(f"应用用户反馈调整 [分析ID: {context.analysis_id}]")
                        
                        # 应用用户反馈
                        feedback_data = interrupt_info.get("feedback", {})
                        self._apply_user_feedback(context, feedback_data)
                        
                        # 清理打断信号
                        self._cleanup_interrupt_signals(str(context.analysis_id))
                        
                        # 重置打断状态
                        context.reset_interrupt_status()
                        
                        # 发送反馈应用事件
                        yield self._format_event("feedback_applied", {
                            "message": "用户反馈已应用，继续分析",
                            "adjustment_type": feedback_data.get("adjustment_type"),
                            "feedback": feedback_data.get("feedback", ""),
                            "interrupt_id": feedback_data.get("interrupt_id")  # 添加打断ID
                        })
                        
                        # 如果需要重置规划，直接跳到规划阶段
                        if feedback_data.get("reset_planning", False):
                            context.task_state = "planning"
                            continue
                    
                    # 处理用户取消反馈，继续分析
                    elif action == "continue_after_cancel":
                        log.info(f"用户取消反馈，继续分析 [分析ID: {context.analysis_id}]")
                        
                        # 清理打断信号
                        self._cleanup_interrupt_signals(str(context.analysis_id))
                        
                        # 重置打断状态
                        context.reset_interrupt_status()
                        
                        # 发送取消反馈事件
                        yield self._format_event("interrupt_cancelled", {
                            "message": "用户取消反馈，继续原分析计划",
                            "reason": "user_cancelled_feedback",
                            "interrupt_id": interrupt_info.get("interrupt_id")
                        })
                # 每轮循环开始时更新缓存中的上下文
                await context_store.set(str(context.analysis_id), context)
                
                if context.task_state == "planning":
                    # **新增：规划阶段前的打断检查**
                    log.info(f"规划阶段前检查打断状态 [分析ID: {context.analysis_id}]")
                    can_continue, event_data = await self._handle_interruption_check(context, "planning")
                    if not can_continue:
                        log.info(f"规划阶段被打断 [分析ID: {context.analysis_id}]")
                        # 如果有取消事件，发送它
                        if event_data and event_data.get("cancelled"):
                            yield self._format_event("completed", event_data)
                            return
                        continue  # 跳回主循环重新检查状态
                    
                    # 🔥 先增加规划轮数计数，再检查是否达到最大轮数
                    context.planning_rounds += 1

                    # 🔥 记录规划轮次开始到生命周期日志（简化版，详细信息将在LLM调用后记录）
                    if hasattr(self, 'lifecycle_logger'):
                        self.lifecycle_logger.log_step_start(
                            step_type="planning_round",
                            step_id=f"planning_round_{context.planning_rounds}",
                            description=f"第{context.planning_rounds}轮规划开始",
                            max_rounds=context.max_planning_rounds,
                            execution_history_count=len(context.execution_history)
                        )

                    # 检查是否达到最大规划轮数
                    if context.is_max_rounds_reached():
                        # 发送事件通知
                        yield self._format_event("max_rounds_reached", {
                            "message": f"已达到最大规划轮数 {context.max_planning_rounds}，强制结束规划",
                            "current_rounds": context.planning_rounds
                        })
                        
                        # 发送报告生成开始事件
                        yield self._format_event("report_generation_started", {
                            "message": "正在生成分析报告"
                        })
                        
                        # 强制结束规划，生成基于当前数据的最终答案
                        prompt_generator = StreamPromptGenerator()
                        reasoning = f"已达到最大规划轮数 {context.max_planning_rounds}，基于当前已执行的 {len(context.execution_history)} 个步骤生成最终分析报告。"
                        result_prompt, result_system_prompt = prompt_generator.generate_result_prompt(context, schema_info, reasoning)

                        # 调用LLM生成最终答案
                        final_answer, usage_data = await self._call_llm_for_text_response(result_prompt, result_system_prompt, user_language=context.user_language)
                        final_answer = self._strip_markdown_markers(final_answer)

                        # 🔥 记录最终报告生成的LLM调用
                        if hasattr(self, 'lifecycle_logger'):
                            self.lifecycle_logger.log_llm_call(
                                step_id="final_report_generation",
                                call_type="final_report",
                                user_prompt=result_prompt,
                                system_prompt=result_system_prompt,
                                llm_response=final_answer,
                                usage_data=usage_data,
                                temperature=None,
                                reasoning=reasoning
                            )

                        # 异步记录Token用量
                        if usage_data:
                            record_token_usage_async(usage_data)
                        
                        # 更新分析记录
                        try:
                            from app.models.analysis import Analysis
                            analysis = self.db.query(Analysis).filter(Analysis.id == context.analysis_id).first()
                            if analysis:
                                analysis.result = final_answer
                                analysis.updated_at = get_shanghai_time()
                                
                                self.db.commit()
                        except Exception as e:
                            log.error(f"更新分析记录时出错: {str(e)}")
                        
                        # 发送报告生成事件
                        yield yield_and_intercept_event("report_generated", {
                            "report": final_answer,
                            "forced_by_max_rounds": True
                        })
                        
                        # 设置任务状态为已完成
                        context.task_state = "completed"
                        continue
                    # 发送规划轮数事件
                    yield self._format_event("planning_round", {
                        "current_round": context.planning_rounds,
                        "max_rounds": context.max_planning_rounds
                    })
                    
                    # 获取数据库类型和版本
                    db_type, db_version = self._get_database_type(project_id)
                    
                    # 生成分析计划
                    prompt_generator = StreamPromptGenerator()
                    planning_prompt, planning_system_prompt = prompt_generator.generate_planning_prompt(
                        context, context.available_tools, schema_info, db_type, db_version
                    )

                    # 调用LLM进行规划
                    plan_result, usage_data = await self._call_llm(planning_prompt, planning_system_prompt, temperature=0.2, user_language=context.user_language)

                    # 🔥 记录规划过程到生命周期日志（包含提示词、结果和使用统计）
                    if hasattr(self, 'lifecycle_logger'):
                        self.lifecycle_logger.log_planning(
                            round_number=context.planning_rounds,
                            planning_prompt=planning_prompt,
                            planning_result=plan_result,
                            context_info={
                                "max_rounds": context.max_planning_rounds,
                                "execution_history_count": len(context.execution_history),
                                "system_prompt": planning_system_prompt,
                                "usage_data": usage_data,
                                "temperature": 0.2
                            }
                        )

                    # 异步记录Token用量
                    if usage_data:
                        record_token_usage_async(usage_data)
                    
                    # 验证LLM返回结果的类型
                    if not isinstance(plan_result, dict):
                        log.error(f"LLM规划返回了非字典类型: {type(plan_result)}, 内容: {plan_result}")
                        
                        # 发送错误事件
                        yield self._format_event("error", {
                            "message": f"LLM规划返回格式错误: 期望字典类型，收到{type(plan_result).__name__}类型",
                            "plan_result_type": type(plan_result).__name__,
                            "plan_result_content": str(plan_result)[:200]  # 限制内容长度
                        })
                        
                        # 重新规划
                        continue
                    
                    if "action_type" in plan_result:
                        # 处理LLM返回的计划
                        if plan_result["action_type"] == "tool":
                            # **新增：规划阶段前的打断检查**
                            log.info(f"工具执行前检查打断状态 [分析ID: {context.analysis_id}]")
                            can_continue, event_data = await self._handle_interruption_check(context, "tool_execution")
                            if not can_continue:
                                log.info(f"工具执行阶段被打断 [分析ID: {context.analysis_id}]")
                                # 如果有取消事件，发送它
                                if event_data and event_data.get("cancelled"):
                                    yield self._format_event("completed", event_data)
                                    return
                                continue  # 跳回主循环重新检查状态
                            # 工具执行计划
                            # 发送事件
                            plan_result["planning_rounds"] = context.planning_rounds
                            yield self._format_event("plan_created", plan_result)
                            
                            # 保存工具执行参数
                            tool_name = plan_result.get("tool_name", "")
                            
                            # 检查准备执行的工具是否存在（使用原始tool_name进行检查）
                            tool_exists = any(t["name"] == tool_name for t in context.available_tools)
                            
                            # 如果原始名称不存在，尝试检查转换后的名称
                            if not tool_exists:
                                converted_tool_name = tool_name
                                if tool_name == 'auto_sql_tool' or tool_name == 'AUTO_SQL':
                                    # 根据用户语言偏好获取工具名称
                                    from app.services.system_tools import SystemTools
                                    converted_tool_name = SystemTools.get_tool_name('auto_sql_tool', context.user_language)
                                elif tool_name == 'clarification_tool':
                                    converted_tool_name = '智能交互'
                                elif tool_name == 'chart_generation_tool':
                                    from app.services.system_tools import SystemTools
                                    converted_tool_name = SystemTools.get_tool_name('chart_generation_tool', context.user_language)
                                
                                # 使用转换后的名称再次检查
                                tool_exists = any(t["name"] == converted_tool_name for t in context.available_tools)
                                if tool_exists:
                                    tool_name = converted_tool_name  # 使用转换后的名称
                            
                            if not tool_exists:
                                # 工具不存在，继续规划
                                log.warning(f"工具 {tool_name} 不存在，继续规划")
                                # 打印可用工具列表用于调试
                                available_tool_names = [t["name"] for t in context.available_tools]
                                log.warning(f"可用工具列表: {available_tool_names}")
                                continue
                            
                            # 如果原始检查已通过，但需要显示本地化名称，进行转换
                            if tool_name == 'auto_sql_tool' or tool_name == 'AUTO_SQL':
                                from app.services.system_tools import SystemTools
                                tool_name = SystemTools.get_tool_name('auto_sql_tool', context.user_language)
                            elif tool_name == 'clarification_tool':
                                tool_name = '智能交互'
                            elif tool_name == 'chart_generation_tool':
                                from app.services.system_tools import SystemTools
                                tool_name = SystemTools.get_tool_name('chart_generation_tool', context.user_language)
                            
                            parameters = plan_result.get("parameters", {})
                            
                            # 生成唯一步骤ID
                            step_id = self._generate_unique_step_id(str(context.analysis_id), "step")

                            # 🔥 记录步骤开始到生命周期日志
                            if hasattr(self, 'lifecycle_logger'):
                                self.lifecycle_logger.log_step_start(
                                    step_type="tool_execution",
                                    step_id=step_id,
                                    description=f"执行{tool_name}工具",
                                    tool_name=tool_name,
                                    parameters=parameters,
                                    planning_round=context.planning_rounds
                                )

                            # 发送规划决策事件，明确告知前端将要执行的工具(新增)
                            yield self._format_event("planning_decision", {
                                "message": f"已选择使用 {tool_name} 工具执行分析",
                                "tool_name": tool_name,
                                "step_id": step_id,
                                "reasoning": plan_result.get("reasoning", "根据查询内容选择合适的工具")
                            })
                            
                            # 发送步骤开始事件，添加 step_id
                            yield yield_and_intercept_event("step_started", {
                                "step_id": step_id,
                                "tool_name": tool_name,
                                "parameters": parameters
                            })
                            
                            # 获取工具
                            tool = next((t for t in context.available_tools if t["name"] == tool_name), None)
                            if not tool:
                                raise ValueError(f"未找到工具: {tool_name}")
                            
                            # 执行工具
                            from app.services.analyzer import AnalyzerService
                            analyzer = AnalyzerService(db=self.db)
                            
                            # 构建步骤，使用之前生成的 step_id
                            step = {
                                "step_id": step_id,
                                "tool_name": tool_name,
                                "parameters": parameters
                            }
                            
                            # 获取原始Tool对象列表而不是字典列表
                            # 因为execute_tool_step期望的是Tool对象列表
                            log.debug(f"重新获取工具列表用于执行")
                            all_tools = await analyzer.get_available_tools(project_id, user_language=user_language)
                            
                            # 从数据库获取分析记录
                            from app.models.analysis import Analysis
                            log.debug(f"获取分析记录 [分析ID: {context.analysis_id}]")
                            analysis = self.db.query(Analysis).filter(Analysis.id == context.analysis_id).first()
                            
                            # 为避免LOB已关闭错误，确保在执行工具步骤前维持新的数据库会话
                            try:
                                # 记录工具执行开始
                                log.info(f"开始执行工具步骤 [步骤ID: {step_id}, 工具: {tool_name}]")

                                # 执行工具步骤
                                result = await analyzer.execute_tool_step(
                                    analysis,
                                    step,
                                    all_tools,
                                    context.user_language
                                )

                                log.info(f"工具步骤执行完成 [步骤ID: {step_id}]")

                                # 确保结果可序列化
                                serializable_result = self._make_json_serializable(result)

                                # 🔥 记录工具执行结果到生命周期日志
                                if hasattr(self, 'lifecycle_logger'):
                                    self.lifecycle_logger.log_tool_execution(
                                        step_id=step_id,
                                        tool_name=tool_name,
                                        parameters=parameters,
                                        result=serializable_result,
                                        success=True,
                                        execution_time=None  # 可以后续添加计时功能
                                    )

                                # 获取reasoning信息
                                reasoning = plan_result.get('reasoning', '')

                                # 发送步骤完成事件，添加 step_id 和 reasoning
                                yield yield_and_intercept_event("step_completed", {
                                    "step_id": step_id,
                                    "tool_name": tool_name,
                                    "result": serializable_result,
                                    "reasoning": reasoning
                                })

                                # 检查工具执行是否失败（支持布尔值和字符串形式）
                                success_value = serializable_result.get('success')
                                if success_value is False or success_value == "False" or success_value == "false":
                                    log.warning(f"工具执行失败，跳过后续分析 [步骤ID: {step_id}]")
                                    # 记录执行结果到内存
                                    context.add_execution_record(tool_name, parameters, result, reasoning)
                                    continue

                                # **新增：统一分析评估**
                                log.info(f"🔍 开始统一分析评估 [步骤ID: {step_id}, 工具: {tool_name}]")
                                unified_result = None
                                try:
                                    # 初始化统一分析评估器（如果还未初始化）
                                    if not hasattr(self, 'unified_evaluator') or self.unified_evaluator is None:
                                        from app.services.unified_analysis_evaluator import UnifiedAnalysisEvaluator
                                        self.unified_evaluator = UnifiedAnalysisEvaluator(db=self.db)
                                        log.info("统一分析评估器初始化完成")

                                    # 🔧 获取完整上下文信息
                                    intent_analysis = getattr(context, 'intent_analysis_result', None)
                                    tool_reasoning = reasoning  # 从plan_result获取的reasoning
                                    tool_parameters = parameters  # 工具执行参数

                                    # 获取对话上下文
                                    conversation_context = None
                                    if hasattr(context, 'multi_round_context') and context.multi_round_context:
                                        ctx_data = context.multi_round_context
                                        conversation_context = ctx_data.get('context_summary', '')

                                    # 🔥 为统一分析评估器设置生命周期日志记录器
                                    if hasattr(self, 'lifecycle_logger'):
                                        # 临时设置到context中，供统一分析评估器使用
                                        context.lifecycle_logger = self.lifecycle_logger

                                    # 执行统一分析评估（一次调用完成洞察分析和智能评估）
                                    unified_result, usage_data = await self.unified_evaluator.analyze_and_evaluate(
                                        tool_result=serializable_result,
                                        context=context,
                                        user_query=context.user_query,
                                        step_id=step_id,
                                        tool_name=tool_name,
                                        schema_info=schema_info,
                                        user_language=context.user_language,
                                        intent_analysis=intent_analysis,
                                        tool_reasoning=tool_reasoning,
                                        tool_parameters=tool_parameters,
                                        conversation_context=conversation_context
                                    )

                                    # 记录Token用量
                                    if usage_data:
                                        record_token_usage_async(usage_data)

                                    # 🔥 检查统一分析评估是否成功
                                    if unified_result is not None:
                                        # 提取分析洞察（向后兼容）
                                        analysis_insights = unified_result.to_analysis_result()
                                    else:
                                        # 统一分析评估失败，跳过洞察分析，继续后续流程
                                        log.info(f"🔍 统一分析评估返回None，跳过洞察分析 [步骤ID: {step_id}]")
                                        analysis_insights = None

                                    # 发送专业分析洞察事件
                                    if analysis_insights and analysis_insights.insights:
                                        # 🔧 修复：为洞察步骤生成独立的步骤ID，避免与工具步骤冲突
                                        insight_step_id = f"insight_{step_id}_{int(time.time() * 1000)}"
                                        yield yield_and_intercept_event("insight_discovered", {
                                            "step_id": insight_step_id,
                                            "tool_name": tool_name,
                                            "statistical_summary": {
                                                "total_rows": analysis_insights.statistical_summary.total_rows,
                                                "columns": analysis_insights.statistical_summary.columns,
                                                "numeric_columns": analysis_insights.statistical_summary.numeric_columns,
                                                "categorical_columns": analysis_insights.statistical_summary.categorical_columns
                                            },
                                            "data_quality": {
                                                "overall_score": analysis_insights.data_quality_assessment.overall_score,
                                                "completeness_score": analysis_insights.data_quality_assessment.completeness_score,
                                                "quality_issues": analysis_insights.data_quality_assessment.quality_issues
                                            },
                                            "insights": [
                                                {
                                                    "type": insight.insight_type,
                                                    "title": insight.title,
                                                    "description": insight.description,
                                                    "confidence": insight.confidence,
                                                    "business_relevance": insight.business_relevance
                                                } for insight in analysis_insights.insights
                                            ],
                                            "patterns": analysis_insights.patterns,
                                            "anomalies": analysis_insights.anomalies,
                                            "correlations": analysis_insights.correlations,
                                            "timestamp": get_shanghai_time().isoformat()
                                        })

                                        log.info(f"🔍 专业数据分析完成 [步骤ID: {step_id}, 洞察数: {len(analysis_insights.insights)}, 数据质量: {analysis_insights.data_quality_assessment.overall_score:.2f}]")

                                        # 将分析洞察存储到上下文中，供报告生成使用
                                        if not hasattr(context, 'analysis_insights_collection'):
                                            context.analysis_insights_collection = []
                                        context.analysis_insights_collection.append(analysis_insights)

                                        # 🔧 新增：将洞察分类到对应的分析阶段
                                        if analysis_insights and hasattr(analysis_insights, 'insights'):
                                            for insight in analysis_insights.insights:
                                                # 根据洞察类型判断分析阶段
                                                if insight.insight_type in ['summary', 'pattern']:
                                                    context.add_stage_insight('phenomenon_analysis', insight.title)
                                                elif insight.insight_type in ['correlation', 'anomaly']:
                                                    context.add_stage_insight('causal_analysis', insight.title)
                                                elif insight.insight_type == 'trend':
                                                    context.add_stage_insight('strategy_generation', insight.title)

                                        # 🔍 DEBUG: 添加调试信息
                                        print(f"🔍 DEBUG: 收集洞察 - 当前总数: {len(context.analysis_insights_collection)}")
                                        if analysis_insights and hasattr(analysis_insights, 'insights'):
                                            print(f"🔍 DEBUG: 本轮洞察数量: {len(analysis_insights.insights)}")
                                            for i, insight in enumerate(analysis_insights.insights[:3]):  # 只显示前3个
                                                print(f"🔍 DEBUG: 洞察{i+1}: [{insight.insight_type}] {insight.title}")
                                        else:
                                            print(f"🔍 DEBUG: 本轮洞察为空或格式错误: {type(analysis_insights)}")

                                    else:
                                        log.info(f"🔍 专业数据分析完成，但未发现显著洞察 [步骤ID: {step_id}]")

                                except Exception as e:
                                    log.warning(f"🔍 统一分析评估失败，但不影响主流程 [步骤ID: {step_id}]: {str(e)}")

                                # @fixed_implementation_start                                # 实现标识: 智能图表生成Agent集成
                                # 功能描述: 在每个工具执行完成后，自动调用图表生成Agent检测数据并生成图表
                                # 复用指引: 这是自动图表生成的核心集成点，确保在工具执行完成后立即检测图表生成机会
                                # 状态: 已确认 (CONFIRMED_IMPLEMENTATION)
                                # @fixed_implementation_end
                                
                                # **新增：智能图表生成前的打断检查**
                                log.info(f"智能图表生成前检查打断状态 [步骤ID: {step_id}]")
                                can_continue, event_data = await self._handle_interruption_check(context, "chart_generation")
                                if not can_continue:
                                    log.info(f"智能图表生成阶段被打断，跳过图表生成 [步骤ID: {step_id}]")
                                    # 如果有取消事件，发送它
                                    if event_data and event_data.get("cancelled"):
                                        yield self._format_event("completed", event_data)
                                        return
                                    continue  # 跳回主循环重新检查状态
                                
                                # 调用智能图表生成Agent检测并生成图表
                                log.info(f"🎨 检查图表生成Agent状态: chart_agent存在={self.chart_agent is not None}")
                                if self.chart_agent:
                                    log.info(f"🎨 开始调用图表生成Agent [工具: {tool_name}, 步骤ID: {step_id}]")
                                    try:
                                        # 定义事件回调函数，用于推送step_started和step_completed事件
                                        def chart_event_callback(event_type: str, event_data: Dict[str, Any]):
                                            """图表Agent的事件回调函数"""
                                            try:
                                                # 使用yield_and_intercept_event推送事件
                                                formatted_event = yield_and_intercept_event(event_type, event_data)
                                                # 由于这是在回调中，我们需要将事件添加到一个队列中
                                                if not hasattr(context, '_chart_events'):
                                                    context._chart_events = []
                                                context._chart_events.append(formatted_event)
                                            except Exception as e:
                                                log.warning(f"图表事件回调失败: {str(e)}")
                                        
                                        # 提取SQL语句信息（如果是SQL工具）
                                        sql_statement = None
                                        if "SQL" in tool_name or "sql" in tool_name.lower():
                                            sql_statement = parameters.get("sql")
                                            if sql_statement:
                                                log.info(f"🎨 提取到SQL语句用于图表生成: {sql_statement[:100]}...")

                                        # 构建对话上下文，包含历史图表信息
                                        conversation_context = None
                                        previous_charts = []

                                        # 方法1：从多轮分析上下文中提取（如果有的话）
                                        if hasattr(context, 'multi_round_context') and context.multi_round_context:
                                            ctx_data = context.multi_round_context
                                            previous_charts = ctx_data.get('previous_charts', [])
                                            if previous_charts:
                                                log.info(f"🎨 从多轮上下文中提取到 {len(previous_charts)} 个历史图表")

                                        # 方法2：如果没有历史图表，尝试从数据库中提取
                                        if not previous_charts and hasattr(context, 'analysis_id'):
                                            try:
                                                 # 🔥 提取历史图表信息，用于避免重复生成（使用Redis缓存）
                                                previous_charts = await self._extract_previous_charts_from_redis(str(context.analysis_id))
                                                if previous_charts:
                                                    log.info(f"🎨 从数据库中提取到 {len(previous_charts)} 个历史图表")
                                            except Exception as e:
                                                log.warning(f"🎨 从数据库提取历史图表失败: {str(e)}")

                                        # 构建conversation_context（总是构建，即使没有历史图表）
                                        conversation_context = {
                                            'previous_charts': previous_charts or []
                                        }

                                        if previous_charts:
                                            log.info(f"🎨 构建conversation_context，包含 {len(previous_charts)} 个历史图表")
                                        else:
                                            log.info(f"🎨 构建conversation_context，没有历史图表")

                                        chart_event_data = await self.chart_agent.process_tool_result(
                                            analysis_id=str(context.analysis_id),
                                            step_id=step_id,
                                            tool_name=tool_name,
                                            tool_result=serializable_result,
                                            user_query=context.user_query,
                                            reasoning=reasoning,
                                            sql_statement=sql_statement,
                                            event_callback=chart_event_callback,
                                            schema_info=schema_info,
                                            user_id=user_id,
                                            project_id=project_id,
                                            background_tasks=background_tasks,
                                            user_language=context.user_language,
                                            analysis_insights=analysis_insights,  # 传递分析洞察
                                            conversation_context=conversation_context  # 🔥 传递对话上下文
                                        )
                                        
                                        # 推送缓存的图表事件
                                        if hasattr(context, '_chart_events'):
                                            for chart_event in context._chart_events:
                                                yield chart_event
                                            # 清空事件缓存
                                            context._chart_events = []
                                        
                                        if chart_event_data:
                                            # Chart Agent已经通过event_callback推送了所有必要的事件
                                            # 这里不需要再次推送chart_generated事件，避免重复
                                            log.info(f"🎨 智能图表生成Agent成功生成图表 [步骤ID: {step_id}]")
                                        else:
                                            log.info(f"🎨 图表生成Agent返回空结果，可能数据不适合生成图表 [步骤ID: {step_id}]")
                                    except Exception as e:
                                        log.warning(f"🎨 智能图表生成Agent处理失败，但不影响主流程: {str(e)}")
                                else:
                                    log.warning(f"🎨 图表生成Agent未初始化，跳过图表生成检测")
                                
                                # 移除原有的图表工具特殊处理逻辑，因为现在由Agent自动处理
                                
                                reasoning = plan_result.get('reasoning', '')

                                # 记录执行结果到内存
                                context.add_execution_record(tool_name, parameters, result, reasoning)
                                
                                # **新增：智能评估前的打断检查**
                                log.info(f"智能评估前检查打断状态 [步骤ID: {step_id}]")
                                can_continue, event_data = await self._handle_interruption_check(context, "evaluation")
                                if not can_continue:
                                    log.info(f"智能评估阶段被打断，跳过评估 [步骤ID: {step_id}]")
                                    # 如果有取消事件，发送它
                                    if event_data and event_data.get("cancelled"):
                                        yield self._format_event("completed", event_data)
                                        return
                                    continue  # 跳回主循环重新检查状态
                                
                                # **使用自适应分析评估进行智能评估**
                                log.info(f"使用自适应分析评估进行智能评估 [步骤ID: {step_id}]")

                                try:
                                    evaluation_result = None
                                    if unified_result is not None:
                                        log.info(f"🔄 降级使用统一分析评估结果 [步骤ID: {step_id}]")
                                        evaluation_result = unified_result.to_evaluation_result()
                                        if evaluation_result:
                                            evaluation_result['evaluator_type'] = 'unified'
                                    else:
                                        # 所有评估器都不可用
                                        log.warning(f"⚠️ 所有评估器都不可用，跳过智能评估 [步骤ID: {step_id}]")
                                        evaluation_result = None

                                    # **重要：将评估结果存储到上下文中**
                                    if evaluation_result is not None:
                                        context.add_evaluation_record(step_id, tool_name, evaluation_result)

                                        # 🔧 新增：更新分析状态和评估指导
                                        if evaluation_result.get("next_analysis_stage"):
                                            context.update_analysis_stage(evaluation_result["next_analysis_stage"])

                                        if evaluation_result.get("specific_guidance"):
                                            context.latest_evaluation_guidance = evaluation_result["specific_guidance"]



                                        # **新增：生成独立的评估步骤ID，确保评估事件在时间线末尾显示**
                                        evaluation_step_id = self._generate_unique_step_id(str(context.analysis_id), "evaluation")

                                        # **新增：发送评估事件给前端**
                                        evaluation_event_data = {
                                            "step_id": evaluation_step_id,  # 使用独立的评估步骤ID
                                            "tool_name": tool_name,
                                            "should_continue": evaluation_result.get("should_continue", True),
                                            "completion_confidence": evaluation_result.get("completion_confidence", 0.0),
                                            "missing_aspects": evaluation_result.get("missing_aspects", []),
                                            "reasoning": evaluation_result.get("reasoning", ""),
                                            "suggested_next_steps": evaluation_result.get("suggested_next_steps", []),
                                            "evaluation_conclusion": evaluation_result.get("evaluation_conclusion", ""),
                                            "timestamp": get_shanghai_time().isoformat()
                                        }

                                        # 发送评估完成事件
                                        yield yield_and_intercept_event("evaluation_completed", evaluation_event_data)
                                    else:
                                        # 统一分析评估失败，跳过评估事件发送
                                        log.info(f"🔍 跳过评估事件发送，因为统一分析评估失败 [步骤ID: {step_id}]")
                                    
                                    # 记录评估信息到分析日志
                                    if self.analysis_logger:
                                        self.analysis_logger.info("工具执行结果评估完成", extra={
                                            "event": "result_evaluation_completed",
                                            "step_id": evaluation_step_id,  # 使用独立的评估步骤ID
                                            "tool_name": tool_name,
                                            "should_continue": evaluation_result.get("should_continue", True),
                                            "completion_confidence": evaluation_result.get("completion_confidence", 0.0),
                                            "missing_aspects": evaluation_result.get("missing_aspects", []),
                                            "reasoning": evaluation_result.get("reasoning", ""),
                                            "suggested_next_steps": evaluation_result.get("suggested_next_steps", []),
                                            # 简化后的统一评估结论字段
                                            "evaluation_conclusion": evaluation_result.get("evaluation_conclusion", ""),
                                            "evaluation_conclusion_length": len(evaluation_result.get("evaluation_conclusion", ""))
                                        })
                                    
                                    # 🔥 根据评估结果决定是否直接生成最终答案（增强协调机制）
                                    should_continue = evaluation_result.get("should_continue", True)
                                    evaluator_type = evaluation_result.get("evaluator_type", "unknown")

                                    # 🔥 评估器协调机制：如果自适应评估器建议停止，但有多阶段任务信息，需要额外检查
                                    if not should_continue and evaluator_type == "adaptive":
                                        multi_stage_info = evaluation_result.get("multi_stage_info", {})
                                        if multi_stage_info.get("is_multi_stage", False):
                                            progress = multi_stage_info.get("progress", 0)
                                            if progress < 0.8:  # 多阶段任务未完成80%
                                                log.warning(f"⚠️ 自适应评估器建议停止，但多阶段任务仅完成{progress:.1%}")
                                                log.warning(f"   已完成: {multi_stage_info.get('completed_stages', [])}")
                                                log.warning(f"   需要完成: {multi_stage_info.get('required_stages', [])}")

                                                # 🔥 更严格的条件：只有在规划轮数不太多且没有严重重复查询时才继续
                                                stagnation_confidence = evaluation_result.get("stagnation_confidence", 0)
                                                has_repeated_queries = "连续3次执行完全相同的SQL查询" in evaluation_result.get("reasoning", "")

                                                if context.planning_rounds < 10 and stagnation_confidence < 0.8 and not has_repeated_queries:
                                                    log.info("🔄 覆盖自适应评估器决定，继续多阶段分析")
                                                    should_continue = True
                                                    evaluation_result["should_continue"] = True
                                                    evaluation_result["reasoning"] += " [系统覆盖：多阶段任务未完成]"
                                                else:
                                                    log.warning(f"🛑 多阶段任务未完成，但存在严重问题，仍然停止分析")
                                                    log.warning(f"   规划轮数: {context.planning_rounds}, 停滞置信度: {stagnation_confidence:.2f}")
                                                    log.warning(f"   重复查询: {has_repeated_queries}")

                                    if not should_continue:
                                        log.info(f"评估结果建议结束分析，准备生成最终报告 [完成度: {evaluation_result.get('completion_confidence', 0.0):.2f}]")
                                        log.info(f"评估器类型: {evaluator_type}")

                                        # 设置任务状态为已完成，跳出规划循环
                                        context.task_state = "completed"
                                        
                                        # 生成最终答案
                                        evaluation_reasoning = evaluation_result.get('reasoning', '')
                                        # 构建包含评估信息的推理文本
                                        enhanced_reasoning = f"智能评估结果: {evaluation_reasoning}\n\n注意: 本次分析基于LLM评估判断已足够完整而提前结束。"
                                        # 获取收集的分析洞察
                                        collected_insights = getattr(context, 'analysis_insights_collection', [])

                                        # 🔍 DEBUG: 添加调试信息
                                        print(f"🔍 DEBUG: 报告生成(提前结束) - 收集到的洞察总数: {len(collected_insights)}")
                                        for i, insight_obj in enumerate(collected_insights):
                                            if hasattr(insight_obj, 'insights'):
                                                print(f"🔍 DEBUG: 洞察对象{i+1}: {len(insight_obj.insights)}个洞察")
                                            else:
                                                print(f"🔍 DEBUG: 洞察对象{i+1}: 格式错误 - {type(insight_obj)}")
                                        final_answer, report_usage_data = await self._generate_intelligent_report(context, schema_info, reasoning=enhanced_reasoning, project_id=project_id, user_language=context.user_language, analysis_insights=collected_insights)
                                        
                                        # 记录报告生成的token用量
                                        if report_usage_data:
                                            record_token_usage_async(report_usage_data)

                                        # 更新分析记录
                                        try:
                                            from app.models.analysis import Analysis
                                            analysis = self.db.query(Analysis).filter(Analysis.id == context.analysis_id).first()
                                            if analysis:
                                                analysis.result = final_answer
                                                analysis.status = 'completed'
                                                analysis.updated_at = get_shanghai_time()
                                                self.db.commit()
                                                log.info(f"智能评估触发的分析完成，状态已更新: {context.analysis_id}")
                                        except Exception as e:
                                            log.error(f"更新分析记录时出错: {str(e)}")
                                        
                                        # 发送报告生成事件（保持原有的报告生成事件）
                                        yield yield_and_intercept_event("report_generated", {
                                            "report": final_answer,
                                            "triggered_by_evaluation": True,
                                            "completion_confidence": evaluation_result.get("completion_confidence", 0.0)
                                        })
                                        
                                        # 跳出当前工具执行分支，进入主循环检查，会因为task_state="completed"而结束
                                        continue
                                    else:
                                        # 评估建议继续分析（静默记录）
                                        log.info(f"评估结果建议继续分析 [完成度: {evaluation_result.get('completion_confidence', 0.0):.2f}]")
                                        suggested_steps = evaluation_result.get("suggested_next_steps", [])
                                        if suggested_steps:
                                            log.info(f"建议的下一步: {', '.join(suggested_steps)}")
                                
                                except Exception as e:
                                    log.error(f"结果评估过程出错，继续正常分析流程: {str(e)}")
                                    
                                    # 记录评估失败到分析日志
                                    if self.analysis_logger:
                                        self.analysis_logger.warning("结果评估失败", extra={
                                            "event": "result_evaluation_failed",
                                            "step_id": step_id,  # 评估失败时仍使用原始step_id，因为没有生成独立的evaluation_step_id
                                            "tool_name": tool_name,
                                            "error": str(e)
                                        })
                            except Exception as e:
                                log.error(f"执行工具步骤时出错: {str(e)}")
                                # 发送错误事件
                                yield self._format_event("error", {"message": f"执行工具步骤时出错: {str(e)}"})
                                # 保持循环继续
                                # 记录执行结果到内存
                                error_result = {
                                    "error": str(e)
                                }
                                # 在异常情况下，reasoning可能未定义，给出默认值
                                error_reasoning = plan_result.get('reasoning', '') if 'plan_result' in locals() else f"工具执行失败: {str(e)}"
                                context.add_execution_record(tool_name, parameters, error_result, error_reasoning)
                                
                        elif plan_result["action_type"] == "final_answer":
                            # 生成最终回答
                            context.task_state = "completed"
                            
                            # 发送事件
                            yield self._format_event("plan_created", plan_result)
                            
                            # 发送报告生成开始事件
                            yield self._format_event("report_generation_started", {
                                "message": "正在生成分析报告"
                            })
                            # 获取回答文本
                            reasoning = plan_result.get('reasoning', '')
                            # 获取收集的分析洞察
                            collected_insights = getattr(context, 'analysis_insights_collection', [])

                            # 🔍 DEBUG: 添加调试信息
                            print(f"🔍 DEBUG: 报告生成(正常结束) - 收集到的洞察总数: {len(collected_insights)}")
                            for i, insight_obj in enumerate(collected_insights):
                                if hasattr(insight_obj, 'insights'):
                                    print(f"🔍 DEBUG: 洞察对象{i+1}: {len(insight_obj.insights)}个洞察")
                                else:
                                    print(f"🔍 DEBUG: 洞察对象{i+1}: 格式错误 - {type(insight_obj)}")
                            final_answer, report_usage_data = await self._generate_intelligent_report(context, schema_info, reasoning=reasoning, project_id=project_id, user_language=context.user_language, analysis_insights=collected_insights)
                            
                            # 记录报告生成的token用量
                            if report_usage_data:
                                record_token_usage_async(report_usage_data)
                            
                            # 更新分析记录
                            try:
                                from app.models.analysis import Analysis
                                analysis = self.db.query(Analysis).filter(Analysis.id == context.analysis_id).first()
                                if analysis:
                                    analysis.result = final_answer
                                    analysis.status = 'completed'  # 重要：更新状态为已完成
                                    analysis.updated_at = get_shanghai_time()
                                    self.db.commit()
                                    log.info(f"分析完成，状态已更新为completed: {context.analysis_id}")
                            except Exception as e:
                                log.error(f"更新分析记录时出错: {str(e)}")
                            
                            # 发送报告生成事件
                            yield yield_and_intercept_event("report_generated", {
                                "report": final_answer
                            })
                            
                            self.db.commit()
                        
                        # @fixed_implementation_start
                        # 实现标识: 移除澄清请求处理
                        # 功能描述: 移除澄清请求的处理分支，简化LLM的action_type处理逻辑
                        # 复用指引: 不再支持澄清action_type，只保留tool和final_answer两种类型
                        # 状态: 已确认 (CONFIRMED_IMPLEMENTATION)
                        # @fixed_implementation_end
                            
                        else:
                            # 未知的动作类型
                            log.warning(f"未知的动作类型: {plan_result['action_type']}")
                    else:
                        log.error(f"LLM规划失败: {plan_result}")
                        yield self._format_event("error", {"message": "规划分析步骤失败"})
                        context.task_state = "failed"
                
                # 添加其他任务状态处理...
                else:
                    # 未知状态
                    log.warning(f"未知的任务状态: {context.task_state}")
                    context.task_state = "failed"
            
            # 发送完成事件
            yield self._format_event("completed", {
                "id": context.analysis_id,
                "message": "分析已完成"
            })

            # 🔥 记录分析会话结束到生命周期日志
            if hasattr(self, 'lifecycle_logger'):
                self.lifecycle_logger.log_session_end(
                    final_status="completed",
                    summary={
                        "total_planning_rounds": getattr(context, 'planning_rounds', 0),
                        "total_execution_steps": len(getattr(context, 'execution_history', [])),
                        "final_task_state": getattr(context, 'task_state', 'unknown')
                    }
                )
            
        except Exception as e:
            log.error(f"处理查询时出错: {str(e)}")
            # 发送错误事件
            yield self._format_event("error", {"message": f"处理查询时出错: {str(e)}"})
            
            # 尝试记录日志到数据库
            try:
                if context.analysis_id:
                    from app.models.analysis import Analysis
                    analysis = self.db.query(Analysis).filter(Analysis.id == context.analysis_id).first()
                    if analysis:
                        analysis.result = f"分析失败: {str(e)}"
                        analysis.status = 'failed'  # 重要：更新状态为失败
                        analysis.updated_at = get_shanghai_time()
                        self.db.commit()
                        log.info(f"分析失败，状态已更新为failed: {context.analysis_id}")
            except Exception as log_error:
                log.error(f"记录失败日志时出错: {str(log_error)}")
        
        finally:
            # 清理Redis中的取消信号（如果存在）
            if context.analysis_id:
                self._cleanup_cancel_signal(str(context.analysis_id))
            # 清理上下文缓存
            if context.analysis_id:
                # @fixed_implementation_start
                # 实现标识: 简化缓存清理逻辑
                # 功能描述: 移除澄清状态检测，简化缓存清理逻辑，直接根据任务状态决定是否清理缓存
                # 复用指引: 简化的缓存管理，不再考虑澄清等待状态，只根据完成状态清理
                # 状态: 已确认 (CONFIRMED_IMPLEMENTATION)
                # @fixed_implementation_end
                
                # 如果任务完成或失败，清理缓存
                if context.task_state in ["completed", "failed"]:
                        log.info(f"分析完成，清理上下文缓存 [分析ID: {context.analysis_id}]")
                        await context_store.remove(str(context.analysis_id))
                else:
                        log.info(f"分析暂停等待澄清，保留上下文缓存 [分析ID: {context.analysis_id}]")
    
    # @fixed_implementation_start
    # 实现标识: 移除澄清回复处理方法
    # 功能描述: 已移除_process_clarification_if_needed方法，不再处理澄清回复的复杂逻辑
    # 复用指引: 简化分析流程，不再需要处理澄清回复的复杂逻辑
    # 状态: 已确认 (CONFIRMED_IMPLEMENTATION)
    # @fixed_implementation_end
    
    # @fixed_implementation_start
    # 实现标识: 移除待澄清分析查找方法
    # 功能描述: 已移除_find_pending_clarification_analysis方法，不再查找待澄清的分析记录
    # 复用指引: 简化分析流程，不再需要查找和处理待澄清的分析记录
    # 状态: 已确认 (CONFIRMED_IMPLEMENTATION)
    # @fixed_implementation_end
    
    async def _load_execution_history(self, context: ExecutionContext, analysis_id: str):
        """加载分析的执行历史
        
        Args:
            context: 执行上下文
            analysis_id: 分析ID
        """
        try:
            from app.models.analysis import ToolExecution
            # 获取所有执行记录
            executions = (
                self.db.query(ToolExecution)
                .filter(ToolExecution.analysis_id == analysis_id)
                .order_by(ToolExecution.id.asc())
                .all()
            )
            
            for execution in executions:
                # 重建执行记录
                tool_name = "未知工具"
                parameters = execution.parameters or {}
                result = execution.result
                
                # 尝试从工具ID获取工具名称
                if execution.tool_id:
                    from app.models.tool import Tool
                    tool = self.db.query(Tool).filter(Tool.id == execution.tool_id).first()
                    if tool:
                        tool_name = tool.name
                    else:
                        tool_name = SystemTools.get_tool_name(execution.tool_id)
                
                # 添加到执行历史 - 在加载历史时不需要reasoning
                context.add_execution_record(tool_name, parameters, result, "")
                
                # 如果有步骤ID，也添加到结果缓存
                if execution.step_id and result:
                    context.execution_results[execution.step_id] = result
            
            log.info(f"已加载 {len(executions)} 条执行历史记录")
            
            # 记录到分析日志
            if self.analysis_logger:
                self.analysis_logger.info("执行历史已加载", extra={
                    "event": "execution_history_loaded",
                    "history_count": len(executions),
                    "analysis_id": analysis_id
                })
            
        except Exception as e:
            log.error(f"加载执行历史时出错: {str(e)}")
            if self.analysis_logger:
                self.analysis_logger.error(f"加载执行历史失败: {str(e)}", extra={
                    "event": "execution_history_load_failed",
                    "error": str(e),
                    "analysis_id": analysis_id
                })
    
    async def _get_schema_info(self, project_id: str) -> str:
        """获取Schema信息
        
        Args:
            project_id: 项目ID
            
        Returns:
            str: Schema信息
        """
        # 复用AnalyzerService的获取Schema方法
        from app.services.analyzer import AnalyzerService
        analyzer = AnalyzerService(db=self.db)
        return analyzer._format_schema_for_prompt(project_id)
    
    def _get_database_type(self, project_id: str) -> tuple[str, Optional[str]]:
        """获取项目的数据库类型和版本信息
        
        Args:
            project_id: 项目ID
            
        Returns:
            tuple[str, Optional[str]]: (数据库类型, 数据库版本)
            数据库类型如：mysql, postgresql, oracle, sqlite等
            数据库版本如：Oracle Database 19c, MySQL 8.0.35等
        """
        try:
            from app.models.data_source import DataSource
            
            data_source = self.db.query(DataSource).filter(DataSource.project_id == project_id).first()
            if not data_source:
                log.warning(f"项目 {project_id} 未找到数据源，使用默认数据库类型")
                return "sqlite", None
            
            # 获取数据库类型
            db_type = data_source.type
            type_value = db_type.value if hasattr(db_type, 'value') else str(db_type)
            
            # 获取数据库版本
            db_version = data_source.db_version if hasattr(data_source, 'db_version') else None
            
            if type_value:
                return type_value.lower(), db_version
            else:
                log.warning(f"项目 {project_id} 数据库类型为空，使用默认类型")
                return "sqlite", None
                
        except Exception as e:
            log.error(f"获取项目 {project_id} 数据库类型和版本失败: {str(e)}")
            return "sqlite", None

    # @fixed_implementation_start
    # 实现标识: 移除智能查询合并方法
    # 功能描述: 已移除_merge_queries_intelligently方法，不再进行复杂的查询合并和澄清历史处理
    # 复用指引: 简化查询处理，直接使用单一查询而非复杂的合并逻辑
    # 状态: 已确认 (CONFIRMED_IMPLEMENTATION)
    # @fixed_implementation_end

    async def _analyze_user_intent(
        self,
        user_query: str,
        available_tools: List[Dict],
        schema_info: str,
        project_id: str,
        conversation_id: Optional[str] = None,
        history_context_data: Optional[Dict[str, Any]] = None,
        user_language: str = 'zh-CN',
        step_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """分析用户意图并生成详细的分析指导，包含澄清问题识别

        Args:
            user_query: 用户查询
            available_tools: 可用工具列表
            schema_info: 数据库Schema信息
            project_id: 项目ID
            conversation_id: 会话ID（可选）
            history_context_data: 多轮对话上下文数据（可选）

        Returns:
            Dict[str, Any]: 意图分析结果，包含详细的分析指导和可能的澄清问题
        """
        try:
            # 获取数据库类型和版本
            db_type, db_version = self._get_database_type(project_id)

            # 获取当前时间
            current_time = get_shanghai_time()
            formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
            
            # 根据数据库类型添加特定的SQL语法说明
            db_type_display = db_type.upper() if db_type else "标准SQL"
            db_version_display = f" ({db_version})" if db_version else ""
            db_type_info = f"\n## 数据库类型\n当前项目使用的数据库类型：**{db_type_display}{db_version_display}**\n"
            
            if db_type == "mysql":
                db_type_info += ''
            elif db_type == "postgresql":
                db_type_info += """
### PostgreSQL特殊语法要求：
- 日期函数：使用 CURRENT_DATE, NOW(), DATE_TRUNC()
- 字符串连接：使用 || 操作符或 CONCAT() 函数
- 限制查询：使用 LIMIT n
- 时间范围：CURRENT_DATE - INTERVAL 'n days/months/years'
- 大小写敏感：表名和字段名通常小写
"""
            elif db_type == "oracle":
                db_type_info += """
### Oracle特殊语法要求：
- 日期函数：使用 SYSDATE, TRUNC(), ADD_MONTHS()
- 字符串连接：使用 || 操作符或 CONCAT() 函数
- 限制查询：使用 ROWNUM <= n 或 FETCH FIRST n ROWS ONLY
- 时间范围：SYSDATE - n (天数)
- 大小写敏感：表名和字段名通常大写
"""
            elif db_type == "sqlite":
                db_type_info += """
### SQLite特殊语法要求：
- 日期函数：使用 DATE(), DATETIME(), strftime()
- 字符串连接：使用 || 操作符
- 限制查询：使用 LIMIT n
- 时间范围：DATE('now', '-n days/months/years')
"""
            else:
                db_type_info += """
### 标准SQL语法要求：
- 使用标准SQL语法
- 限制查询：使用 LIMIT n
- 避免使用数据库特定的函数
"""

            # 只过滤掉图表工具，保留其他所有工具
            filtered_tools = [tool for tool in available_tools if tool.get("name") not in ["智能图表生成"]]
            tool_descriptions = self.prompt_generator._format_tools_for_prompt(filtered_tools)
            
            # 转换可用工具为JSON格式
            available_tools_json = json.dumps(filtered_tools, ensure_ascii=False, indent=2, cls=DecimalEncoder)

            # 构建多轮对话上下文信息
            context_section = ""
            if conversation_id and history_context_data:
                context_summary = history_context_data.get('context_summary', '')

                if context_summary:
                    context_section = f"""
## **历史轮次分析摘要**
**重要：请务必利用以下上下文信息来精确化用户的意图。**

{context_summary}

**指导要求：** 必须基于上述上下文信息理解用户当前查询的深层意图，将上下文中的关键信息与当前查询结合，形成更准确、更具体的意图描述。
"""
            
            # 构建一个平衡、专业的意图识别prompt
            intent_prompt = f"""
**当前时间**：{formatted_time}

你是一位敏锐、细致的数据分析师。你的任务是深度理解用户的查询，首先判断问题是否与数据库数据相关，然后制定分析计划，并仅在遇到关键歧义时提出澄清问题。

**核心思维框架：**
1.  **数据相关性判断（首要任务）：**
    *   **关键判断：** 用户的问题是否可能与数据库中的数据有关，或者能否尝试通过数据分析来回答？
    *   **数据相关的问题示例：** 销售数据分析、用户行为统计、财务报表、库存查询、业务指标计算、趋势分析、对比分析、统计查询等
    *   **数据无关的问题示例：** 纯技术概念解释（如"什么是机器学习"）、软件操作教程、数学公式推导、与业务数据完全无关的理论问题等
    *   **宽松判断原则：** 
        - 如果问题涉及任何可能的数据查询、统计、分析、对比，都视为数据相关
        - 如果问题提到了具体的业务场景、指标、时间范围，都视为数据相关
        - 如果问题比较模糊但可能通过数据来解答，优先视为数据相关
        - **只有明确的纯理论、纯概念、纯操作指导类问题才视为数据无关**

2.  **意图洞察：** 用户真正想分析什么？结合业务常识，超越字面意思，理解其深层需求。

3.  **歧义判断（关键阈值）：**
    *   **是否存在重大歧义？** 即查询有两种或以上截然不同、会极大影响分析结果的解读方式。
    *   **是否缺失关键业务参数？** 即缺少了分析无法继续进行的必要条件（例如，分析特定活动效果却未指明是哪个活动）。
    *   **注意：** 对于可以合理推断的常规信息（如"近期"默认为近30天），无需澄清。你的目标是解决"show-stopper"级别的问题。

4.  **规划分析路径：** 将用户的需求拆解为清晰、可执行的步骤。

{context_section}

**用户查询：** {user_query}

**可用数据库信息：**
{schema_info}

**可用工具：**
{tool_descriptions}

**工具使用指南：**
1. **工具执行导向**：每个步骤都必须对应具体的工具执行，严禁生成无工具执行的纯分析步骤
2. **🎨 图表生成说明**：系统配备智能图表生成Agent，会在数据查询后自动生成合适的可视化图表。你在制定execution_steps时：
   - 专注于数据获取和分析逻辑，无需包含图表生成步骤
   - 可以在step_intent中提及"为后续可视化准备数据"等表述
   - 系统会自动评估数据的可视化潜力并生成相应图表
3. **聚合查询优先**：使用自动SQL查询工具时，优先设计聚合查询（GROUP BY、COUNT、SUM、AVG等）来一次性获取多维度数据
4. **专用工具优先**：优先使用针对特定任务的专用工具，如果专用工具无法满足需求，再使用自动SQL查询工具
5. **步骤最小化**：通过复杂SQL查询和聚合分析，将多个简单查询合并为少数几个高效查询
6. **数据完整性**：每次查询都要获取足够的数据维度，避免后续需要补充查询
7. **数据探索**：不清楚数据规模时使用COUNT(*)、DISTINCT了解数据规模和分布
8. **渐进式查询**：先验证少量数据，再进行详细查询
9. **错误恢复**：调用失败时使用更保守的条件或替代工具，但要改变参数

**任务指令：**
-   **优先原则：宽松判断数据相关性** - 当有疑问时，优先视为数据相关并尝试分析
-   首先判断用户问题是否与数据库数据相关（采用宽松标准）
-   **只有明确的纯理论、纯概念问题才返回 action_type 为 "data_irrelevant"**
-   对于可能相关的问题，都应该分析上述信息，完成正常的意图分析JSON结构
-   仅在绝对必要时，才将 `needs_clarification` 设为 `true` 并填充 `clarification_questions`

**输出格式（严格遵守此JSON结构）：**

**数据无关的情况：**
```json
{{
    "action_type": "data_irrelevant",
    "message": "抱歉，您的问题与当前数据库中的数据无关。我是一个专门用于数据分析的AI助手，主要帮助您分析和查询数据库中的业务数据。如果您有关于数据分析、报表生成、数据查询等方面的问题，我很乐意为您提供帮助。"
}}
```

**数据相关的情况：**
```json
{{
    "action_type": "intent_analysis",
    "intent_description": "用一两句话清晰、具体地描述你理解的用户分析意图。",
    "execution_steps": [
        {{
            "step_number": 1,
            "step_intent": "此步骤的核心分析目的（必须对应具体工具执行）。",
            "recommended_tool": "必须是可用工具列表中的工具名称，严禁使用不存在的工具。",
            "tool_usage_reason": "简述为什么选择该工具，如果是自动SQL查询，说明聚合查询策略。",
            "expected_outcome": "描述执行此步骤后的预期数据结果（不是分析结论）。"
        }}
    ],
    "needs_clarification": false,
    "clarification_prompt": "这里是由你生成的、自然的对话引导语，用于引出下面的澄清问题。",
    "clarification_questions": [
        {{
            "id": "question_1",
            "question": "这里是自然、对话式的澄清问题。",
            "options": ["选项A", "选项B", "选项C"]
        }}
    ]
}}
```

**execution_steps**：执行步骤规划，每个步骤都必须对应具体的工具执行，严禁生成无工具的纯分析步骤。优先使用聚合查询减少步骤数量。

**澄清问题 `clarification_questions` 字段详解（前端渲染依赖此结构）：**
-   `id`: 唯一标识符，如 "question_1"。
-   `question`: 具体的问题文本，必须自然、易于理解。
-   `options`: 为用户提供的可选答案，是一个字符串数组。用户可以选择其中一个选项，也可以自定义输入其他答案。

**`clarification_prompt` 字段要求：**
-   此字段由你动态生成，作为澄清问题的开场白。
-   风格应自然、友好，例如："为了更好地帮你分析销售数据，我需要确认一下你是指哪个季度的销售额？"

**最终要求：**
-   **首要任务：宽松判断数据相关性** - 疑似相关的问题都应该尝试分析
-   **保守策略：宁可错误地尝试分析，也不要轻易拒绝用户的问题**
-   **强制约束：每个execution_steps都必须有具体的工具执行，严禁生成无工具的步骤**
-   **聚合优先：使用自动SQL查询时，优先设计GROUP BY、COUNT、SUM等聚合查询**
-   **步骤精简：通过复杂查询减少步骤数量，避免多次简单查询**
-   严格遵循JSON输出格式。
-   保持 `intent_description` 和 `execution_steps` 简洁、专业。
-   审慎使用澄清功能，避免不必要的打扰。
-   只返回JSON对象，不包含任何额外的解释性文本。
"""
            
            # 构建系统提示词（保持简洁）
            system_prompt = f"""你是一个专业的数据分析师，负责理解用户查询意图并制定分析计划。严格按照指定的JSON格式返回结果。

**核心约束**：
1. 每个execution_steps都必须对应具体的工具执行
2. 严禁生成无工具执行的纯分析步骤
3. 优先使用聚合查询（GROUP BY、COUNT、SUM等）减少步骤数量
4. 严格按照指定的JSON格式返回结果

**🚨 重要格式要求**：
- 必须返回纯JSON格式，不要使用markdown代码块包裹
- 不要添加```json或```标记
- 不要在JSON前后添加任何解释性文字
- 直接输出JSON对象，确保格式正确

**🎨 系统图表生成能力说明：**
系统配备了智能图表生成Agent，会在数据查询完成后自动评估并生成合适的可视化图表。你在制定分析计划时：
- 可以考虑数据的可视化潜力，但无需在execution_steps中包含图表生成步骤
- 专注于数据获取和分析逻辑，图表生成会自动处理
- 系统支持多种图表类型（折线图、柱状图、饼图、表格等）

**当前时间**：{formatted_time}
{db_type_info}
"""
            
            # 记录上下文使用情况
            if conversation_id and history_context_data:
                log.info(f"意图分析使用多轮上下文 - 会话ID: {conversation_id}")
                if history_context_data.get('context_summary'):
                    log.info(f"上下文摘要长度: {len(history_context_data.get('context_summary', ''))}")
            else:
                log.info("意图分析未使用多轮上下文（首轮对话或无上下文）")
            
            log.info("调用LLM进行增强的意图识别和澄清检测")
            
            # 调用LLM进行意图分析，根据用户语言偏好输出
            result, usage_data = await self._call_llm(intent_prompt, system_prompt, temperature=0.2, user_language=user_language)

            # 🔥 记录LLM调用到生命周期日志
            if step_id and hasattr(self, 'lifecycle_logger'):
                self.lifecycle_logger.log_llm_call(
                    step_id=step_id,
                    call_type="intent_analysis",
                    user_prompt=intent_prompt,
                    system_prompt=system_prompt,
                    llm_response=result,
                    usage_data=usage_data,
                    temperature=0.2
                )

            # 注意：意图分析阶段还没有analysis_id，所以暂时不记录Token用量
            # Token用量将在正式分析阶段记录
            
            # 检查是否为数据无关的情况
            if result.get("action_type") == "data_irrelevant":
                log.info("用户问题与数据库数据无关，返回固定提示")
                return {
                    "action_type": "data_irrelevant",
                    "message": result.get("message", "抱歉，您的问题与当前数据库中的数据无关。我是一个专门用于数据分析的AI助手，主要帮助您分析和查询数据库中的业务数据。如果您有关于数据分析、报表生成、数据查询等方面的问题，我很乐意为您提供帮助。")
                }
            
            # 确保返回正确的格式
            if result.get("action_type") == "intent_analysis":
                intent_desc = result.get('intent_description', '')
                needs_clarification = result.get('needs_clarification', False)
                clarification_questions = result.get('clarification_questions', [])
                
                log.info(f"意图识别完成，生成分析指导长度：{len(intent_desc)}字符")
                log.info(f"意图描述预览：{intent_desc[:150]}...")
                log.info(f"是否需要澄清：{needs_clarification}")
                if needs_clarification:
                    log.info(f"澄清问题数量：{len(clarification_questions)}")

                # 记录上下文使用情况
                if conversation_id and history_context_data and history_context_data.get('context_summary'):
                    log.info("意图分析已结合多轮上下文")

                # 确保必要字段存在
                if 'intent_description' not in result:
                    result['intent_description'] = f"用户希望了解与'{user_query}'相关的信息，需要根据可用数据进行分析"

                # 确保execution_steps字段存在
                if 'execution_steps' not in result:
                    result['execution_steps'] = []

                # 确保澄清相关字段存在
                if 'needs_clarification' not in result:
                    result['needs_clarification'] = False
                if 'clarification_questions' not in result:
                    result['clarification_questions'] = []
                if 'clarification_prompt' not in result:
                    result['clarification_prompt'] = "你好，为了更好地帮助你完成分析，我这边有几个小问题需要确认。如果你不确定其中某些信息，也没关系，我会尝试基于现有数据做出合理假设。"
                
                # 返回完整的意图分析结果，包含澄清信息
                complete_result = {
                    "action_type": "intent_analysis",
                    "intent_description": result['intent_description'],
                    "execution_steps": result['execution_steps'],
                    "needs_clarification": result['needs_clarification'],
                    "clarification_questions": result['clarification_questions'],
                    "clarification_prompt": result['clarification_prompt']
                }
                
                return complete_result
            else:
                # 如果LLM没有返回正确格式，抛出异常而不是使用默认值
                log.error("LLM返回格式不正确，无法继续意图分析")
                raise ValueError(f"LLM意图分析返回格式错误: {result}")
            
        except Exception as e:
            log.error(f"意图分析失败: {str(e)}")
            # 不使用默认值，直接抛出异常
            raise Exception(f"意图分析失败，无法继续处理: {str(e)}")

    def _generate_unique_step_id(self, analysis_id: str, prefix: str = "step") -> str:
        """生成唯一的step_id
        
        Args:
            analysis_id: 分析ID
            prefix: step_id前缀，默认为"step"
        
        Returns:
            str: 唯一的step_id，格式为 prefix_数字
        """
        try:
            from app.models.analysis import ToolExecution, AnalysisStepDetail
            
            # 查询同一分析ID下所有以指定前缀开头的step_id
            # 同时查询ToolExecution和AnalysisStepDetail两个表
            existing_tool_executions = (
                self.db.query(ToolExecution.step_id)
                .filter(
                    ToolExecution.analysis_id == analysis_id,
                    ToolExecution.step_id.like(f"{prefix}_%")
                )
                .all()
            )
            
            existing_step_details = (
                self.db.query(AnalysisStepDetail.step_id)
                .filter(
                    AnalysisStepDetail.analysis_id == analysis_id,
                    AnalysisStepDetail.step_id.like(f"{prefix}_%")
                )
                .all()
            )
            
            # 合并所有step_id
            all_step_ids = []
            for execution in existing_tool_executions:
                if execution.step_id:
                    all_step_ids.append(execution.step_id)
            for step_detail in existing_step_details:
                if step_detail.step_id:
                    all_step_ids.append(step_detail.step_id)
            
            # 提取数字部分并找到最大值
            max_number = 0
            for step_id in all_step_ids:
                if step_id and step_id.startswith(f"{prefix}_"):
                    try:
                        # 提取数字部分
                        number_part = step_id[len(f"{prefix}_"):]
                        # 处理可能的时间戳格式，只取纯数字部分
                        if number_part.isdigit():
                            number = int(number_part)
                            max_number = max(max_number, number)
                    except (ValueError, IndexError):
                        # 如果无法解析数字，跳过
                        continue
            
            # 生成新的step_id
            new_step_id = f"{prefix}_{max_number + 1}"
            log.debug(f"生成唯一step_id: {new_step_id} (分析ID: {analysis_id}, 前缀: {prefix}, 最大编号: {max_number})")
            
            return new_step_id
            
        except Exception as e:
            log.error(f"生成唯一step_id时出错: {str(e)}")
            # 降级到基于时间戳的方案
            fallback_step_id = f"{prefix}_{get_shanghai_time().strftime('%Y%m%d%H%M%S')}"
            log.warning(f"使用降级方案生成step_id: {fallback_step_id}")
            return fallback_step_id



    async def _optimize_schema_for_intent(
        self, 
        intent_result: Dict[str, Any], 
        user_query: str, 
        full_schema_info: str,
        user_language: str = 'zh-CN'
    ) -> tuple[str, Dict[str, Any]]:
        """
        根据意图分析结果优化Schema信息
        
        Args:
            intent_result: 意图分析结果
            user_query: 用户查询
            full_schema_info: 完整Schema信息
        
        Returns:
            str: 优化后的Schema信息
        """
        try:
            log.info("开始Schema智能筛选优化")
            # 构建Schema筛选提示词
            schema_optimization_prompt = f"""**用户问题**: {user_query}

**完整的意图分析结果 (包含用户澄清)**:
{json.dumps(intent_result, ensure_ascii=False, indent=2)}

**分析任务**: 请深入分析用户问题的完整业务链条，识别所有可能需要的数据表。

**关键分析维度**:
1. **直接相关**: 哪些表直接包含问题答案？
2. **维度分析**: 需要哪些维度表进行分层、分组、对比分析？
3. **关联分析**: 哪些表可能用于关联查询和深入挖掘？
4. **时间对比**: 是否需要历史数据进行趋势分析？
5. **业务完整性**: 完整回答这个问题需要哪些业务环节的数据？

**特别注意**:
- 营销活动分析需要考虑：活动→用户→商品→订单→效果的完整链条
- 用户行为分析需要考虑：用户属性→行为数据→消费数据→偏好分析的完整链条
- 商品分析需要考虑：商品属性→销售数据→用户反馈→市场表现的完整链条

**完整Schema**:
{full_schema_info}

**具体筛选要求**：
对于"促销活动效果分析"类问题，必须包含以下表类型：
1. **活动相关表**：活动表、促销表、优惠券表等
2. **订单相关表**：订单表、订单明细表、支付表等
3. **用户相关表**：用户表、会员表、用户行为表等
4. **商品相关表**：商品表、品牌表、分类表等
5. **效果分析表**：任何包含转化、参与、互动数据的表

**严格要求**：即使表名不明显相关，只要可能包含用户行为、商品信息、会员数据等，都必须保留！

请根据以上信息，严格按照系统指令输出精简后的Schema。记住：宁可多包含，绝不遗漏！对于促销活动效果分析，必须保留用户、商品、会员等维度表！"""
            
            # 定义Schema优化的系统提示词
            schema_system_prompt = """你是一位顶级的数据库Schema优化专家和业务分析师。你的核心任务是根据用户问题和分析意图，从完整的Schema中筛选出相关的数据表，并保留这些表的完整字段信息。

**最终目标**：
输出的Schema文本，将直接用于指导另一个AI模型生成SQL。因此，你的输出必须**绝对纯净、高度相关、且不失真**。

**核心筛选理念 - 业务链条完整性**：
不仅要考虑直接相关的表，更要考虑完整的业务分析链条。数据分析往往需要多维度、多层次的信息才能得出有价值的洞察。

**指导原则**：
1.  **业务背景信息保留**：
    -   **必须保留**: 如果原始Schema包含"# 业务背景"部分，必须**完整保留**这部分内容，因为它包含了关键的业务规则、字段含义说明、数据格式约定等重要信息。
    -   **业务规则重要性**: 业务背景中的字段含义说明（如状态字段的枚举值含义、日期格式说明、关联关系等）对于正确生成SQL至关重要，绝不能省略。
    -   **格式保持**: 业务背景部分应保持原有的格式和结构，放在Schema描述的最前面。

2.  **价值判断**：
    -   **核心依据**: 你的首要任务是理解 **`用户问题`** 的最终意图。请仔细分析 **`完整的意图分析结果`**，特别是其中的 **`user_adjustment` (用户澄清)** 字段，因为它代表了最准确的用户需求。
    -   **表筛选原则**: 采用**宽松筛选策略**，基于对用户最终意图的理解，识别出所有**可能相关**的数据表。**宁可多包含，绝不遗漏**：只要表与用户问题有任何潜在关联，都应该保留。只过滤掉明显完全无关的表。
    -   **字段完整性原则**：**重要**：对于被选中的相关表，必须保留该表的**所有字段**，不得删除任何字段。这确保了下游SQL生成模型能够获得完整的表结构信息。
    -   **关键描述保留**: **必须**优先保留表级别的自然语言描述，特别是那些包含业务规则、计算逻辑或关联关系的描述。这些是下游模型生成正确SQL的关键。
    -   **示例数据智能筛选**：只为**关键字段**提供必要的示例数据，避免冗余。重点关注：状态字段(如status)、枚举值、特殊格式字段、业务含义不明确的字段。普通的ID、名称、金额字段可以省略示例或仅提供1个典型示例。

3.  **格式要求**：
    -   **整体结构**: 输出格式应为：`# 业务背景\n[业务背景内容]\n\n# 数据库Schema描述\n\n## <表名>\n<表描述>\n- <字段信息>`
    -   **表结构**: 必须严格遵循 `## <表名>\n<表描述>\n- <字段信息>` 的格式。
    -   **字段级示例**: 智能添加示例数据，格式为 `- 字段名: 类型, 描述, 示例: 值1, 值2`。优先级：状态字段(必须) > 枚举值(必须) > 特殊格式字段(建议) > 普通字段(可选)。
    -   **移除示例块**: 应彻底移除独立的 `### 示例数据` 块，因为它增加了不必要的结构和长度。

4.  **优秀输出示例**:
下面是一个符合所有要求的优秀输出范例（注意业务背景保留和精简的示例数据策略）：
```
# 业务背景
orders表中的date字段表示订单日期，格式为YYYY-MM-DD。
orders表中的status字段，0表示待支付，1表示已支付，2表示已发货，3表示已完成。
customers表中的member_level字段，1表示普通会员，2表示金卡会员，3表示钻石会员。
orders表通过customer_id字段关联customers表中的customer_id字段。

# 数据库Schema描述

## `orders` 表
订单信息表，记录所有订单详情
- order_id: bigint, 订单ID主键
- customer_id: int, 客户ID
- product_id: int, 产品ID
- quantity: int, 购买数量
- unit_price: decimal, 单价
- payment_amount: decimal, 支付金额
- discount_amount: decimal, 折扣金额
- order_date: date, 订单日期, 示例: 2024-01-15
- status: tinyint, 订单状态, 示例: 0, 1, 2, 3
- promo_id: int, 促销ID, 示例: 1

## `customers` 表
客户信息表
- customer_id: int, 客户ID主键
- customer_name: varchar, 客户姓名
- age: int, 年龄
- gender: char, 性别, 示例: M, F
- province: varchar, 省份
- member_level: varchar, 会员等级, 示例: 普通会员, 金卡会员, 钻石会员
- registered_at: date, 注册时间, 示例: 2024-01-15
```

**业务链条完整性分析**：
在筛选表时，请深入思考完整的业务分析链条，确保不遗漏关键环节：

**营销活动效果分析示例**：
- **核心表**: 活动表、订单表（直接相关）
- **用户维度**: 用户表、会员表（用户画像、分层分析）
- **商品维度**: 商品表、品牌表、分类表（商品偏好分析）
- **促销工具**: 优惠券表、折扣表（促销效果分析）
- **行为数据**: 用户行为表、浏览记录表（参与度分析）
- **时间维度**: 可能需要历史对比数据的相关表

**用户消费分析示例**：
- **核心表**: 订单表、用户表（直接相关）
- **商品维度**: 商品表、品牌表、分类表（消费偏好）
- **支付维度**: 支付表、优惠券使用表（支付行为）
- **会员体系**: 会员表、积分表（会员价值分析）
- **地域维度**: 地址表、区域表（地域分布分析）

**表筛选指导原则**：
- **第一层-核心表**: 直接回答问题的主要数据表
- **第二层-维度表**: 提供分析维度的表（用户、商品、时间、地域等）
- **第三层-关联表**: 可能用于深入分析的相关表
- **第四层-支撑表**: 字典表、配置表等支撑性数据
- **过滤标准**: 只过滤与业务完全无关的表（如系统日志、技术配置等）

**通用筛选原则**：
数据分析往往需要多个维度的数据才能得出有价值的洞察。请遵循以下通用原则：

1. **主体分析原则**：
   - 如果分析某个主体（如用户、商品、订单、活动等），必须保留该主体的基础信息表
   - 同时保留与该主体相关的行为数据表、属性表、分类表等

2. **关联分析原则**：
   - 保留可能用于JOIN操作的关联表
   - 保留提供分析维度的维度表（如时间、地域、分类等）
   - 保留支撑深入分析的辅助表

3. **效果分析原则**：
   - 如果分析效果或影响，需要保留"前"和"后"的对比数据表
   - 保留可能影响结果的因素表（如用户属性、环境因素等）

4. **宽松保留策略**：
   - 当不确定某个表是否相关时，选择保留而不是过滤
   - 只过滤明显与分析主题完全无关的表（如系统日志、技术配置等）

**通用分析模式识别**：
- **趋势分析**：需要时间维度数据和历史对比数据
- **对比分析**：需要分组维度表和对比基准数据
- **影响因素分析**：需要可能的影响因素表和环境数据
- **用户行为分析**：需要用户基础信息、行为记录、偏好数据
- **业务效果分析**：需要业务主体表、相关方表、结果指标表

**过度筛选警告**：
如果你发现筛选后的表数量不足原表数量的30%，请重新审视是否过度筛选了。
数据分析通常需要多维度数据支撑，过度精简可能导致分析不够深入。

**示例数据添加策略**：
- **必须添加**: status字段、枚举值字段、会员等级、性别等分类字段
- **建议添加**: 日期时间字段(格式参考)、特殊编码字段
- **可选添加**: 普通ID字段(仅1个示例)、名称字段(仅1个示例)  
- **无需添加**: 金额字段、数量字段、描述字段等语义明确的字段

5.  **输出要求**：
    -   **绝对纯净**：你的输出**只能是**精简后的Schema文本本身。
    -   **严禁包含**：任何形式的引言、解释、告别、代码块标记（如```markdown）或其他任何元注释。
    -   **业务背景优先**：如果原始Schema包含业务背景信息，必须完整保留并放在输出的最前面。
    -   **表级过滤**：采用宽松策略，只过滤掉与用户问题**明显完全无关**的表，不过滤表内的字段。当有疑问时，选择保留表而不是过滤掉。
    -   **示例数据精简要求**：按优先级智能添加示例数据，避免冗余。关键字段(status、枚举值)必须有示例，普通字段可省略或仅提供1个示例。
"""

            log.info("正在调用LLM进行Schema优化...")
            
            # 调用LLM进行Schema优化，直接获取文本响应
            optimized_schema, usage_data = await self._call_llm_for_text_response(
                schema_optimization_prompt, 
                schema_system_prompt,
                temperature=0.1,  # 使用较低的温度确保一致性
                user_language=user_language
            )
            print("================================================================================\n📝 LLM响应 - 精简后的schema_info:\n================================================================================\n")
            print(optimized_schema)
            print("================================================================================\n")
            
            # 验证优化结果
            if optimized_schema and optimized_schema.strip():
                # 检查是否是错误消息
                error_indicators = [
                    "抱歉，在生成分析报告时遇到了技术问题",
                    "Error code:",
                    "API key",
                    "技术问题",
                    "系统管理员"
                ]

                is_error = any(indicator in optimized_schema for indicator in error_indicators)

                if is_error:
                    log.warning(f"Schema优化返回错误消息，使用原始Schema: {optimized_schema[:100]}...")
                    return full_schema_info, usage_data

                # 检查是否包含有效的Schema结构
                import re
                table_count = len(re.findall(r'## `\w+` 表', optimized_schema))

                if table_count == 0:
                    log.warning("Schema优化结果不包含任何表，使用原始Schema")
                    return full_schema_info, usage_data

                # 计算优化效果
                original_length = len(full_schema_info)
                optimized_length = len(optimized_schema)
                reduction_percentage = round((1 - optimized_length / original_length) * 100, 1)

                log.info(f"Schema优化完成: 原始长度={original_length}, 优化后长度={optimized_length}, 减少={reduction_percentage}%")
                log.info(f"优化后包含{table_count}个相关表")

                return optimized_schema, usage_data
            else:
                log.warning("Schema优化结果为空，使用原始Schema")
                return full_schema_info, usage_data
                
        except Exception as e:
            log.warning(f"Schema优化失败，使用原始Schema: {str(e)}")
            return full_schema_info, None

    # 在LLMStreamAnalyzer类中添加一个通用的打断检查处理方法
    async def _handle_interruption_check(self, context: ExecutionContext, stage: str) -> tuple[bool, dict]:
        """
        处理打断检查的通用方法

        Args:
            context: 执行上下文
            stage: 当前阶段（如 'chart_generation', 'evaluation'）

        Returns:
            tuple[bool, dict]: (是否应该继续执行, 事件数据或None)
        """
        if not context.analysis_id:
            return True, {}

        # 首先检查ExecutionContext中的超时状态
        if context.interrupt_status == 'interrupted' and context.is_interrupt_timeout():
            log.warning(f"分析在{stage}阶段检测到超时 [分析ID: {context.analysis_id}]")

            # 处理超时
            timeout_result = self._handle_interrupt_timeout(context, str(context.analysis_id))

            if timeout_result.get("action") == "cancel":
                # 更新分析状态为已取消
                try:
                    from app.models.analysis import Analysis
                    analysis = self.db.query(Analysis).filter(Analysis.id == context.analysis_id).first()
                    if analysis:
                        analysis.status = 'cancelled'
                        analysis.result = f"分析在{stage}阶段因打断超时而取消"
                        analysis.updated_at = get_shanghai_time()
                        self.db.commit()
                except Exception as e:
                    log.error(f"更新分析记录状态时出错: {str(e)}")

                timeout_event = {
                    "id": context.analysis_id,
                    "message": timeout_result.get("message", f"分析在{stage}阶段因打断超时而取消"),
                    "cancelled": True,
                    "reason": "interrupt_timeout"
                }
                return False, timeout_event
            else:
                # 继续分析
                context.reset_interrupt_status()
                return True, {}

        interrupt_check_result = self._check_interruption(str(context.analysis_id))
        action = interrupt_check_result.get("action", "continue")
        interrupt_info = interrupt_check_result.get("interrupt_info", {})
        sleep_time = interrupt_check_result.get("sleep_time", 0)
        
        # 处理取消
        if action == "cancel":
            if interrupt_info.get("reason") == "user_cancelled":
                log.info(f"分析在{stage}阶段被用户取消 [分析ID: {context.analysis_id}]")
                
                # 记录到分析日志
                if self.analysis_logger:
                    self.analysis_logger.info(f"分析在{stage}阶段被用户取消", extra={
                        "event": "analysis_cancelled_by_user",
                        "analysis_id": context.analysis_id,
                        "stage": stage,
                        "planning_rounds": context.planning_rounds
                    })
                
                # 更新分析记录状态
                try:
                    from app.models.analysis import Analysis
                    analysis = self.db.query(Analysis).filter(Analysis.id == context.analysis_id).first()
                    if analysis:
                        analysis.result = f"分析在{stage}阶段被用户取消"
                        analysis.updated_at = get_shanghai_time()
                        self.db.commit()
                except Exception as e:
                    log.error(f"更新分析记录状态时出错: {str(e)}")
                
                cancel_event = {
                    "id": context.analysis_id,
                    "message": f"分析在{stage}阶段被用户取消",
                    "cancelled": True,
                    "reason": "user_cancelled"
                }
                return False, cancel_event
            else:
                # 打断超时取消
                log.info(f"分析在{stage}阶段因打断超时而取消 [分析ID: {context.analysis_id}]")
                timeout_event = {
                    "id": context.analysis_id,
                    "message": f"分析在{stage}阶段因打断超时而取消",
                    "cancelled": True,
                    "reason": "interrupt_timeout"
                }
                return False, timeout_event
        
        # 处理等待用户反馈
        elif action == "wait":
            # 增加打断检查计数
            context.increment_interrupt_check()
            
            # 检查是否超时
            if context.is_interrupt_timeout():
                log.warning(f"分析在{stage}阶段打断等待超时 [分析ID: {context.analysis_id}]")
                
                # 处理超时
                timeout_result = self._handle_interrupt_timeout(context, str(context.analysis_id))
                
                if timeout_result.get("action") == "cancel":
                    # 更新分析状态为已取消
                    try:
                        from app.models.analysis import Analysis
                        analysis = self.db.query(Analysis).filter(Analysis.id == context.analysis_id).first()
                        if analysis:
                            analysis.status = 'cancelled'
                            analysis.result = f"分析在{stage}阶段因打断超时而取消"
                            analysis.updated_at = get_shanghai_time()
                            self.db.commit()
                    except Exception as e:
                        log.error(f"更新分析记录状态时出错: {str(e)}")

                    timeout_event = {
                        "id": context.analysis_id,
                        "message": timeout_result.get("message", f"分析在{stage}阶段因打断超时而取消"),
                        "cancelled": True,
                        "reason": "interrupt_timeout"
                    }
                    return False, timeout_event
                else:
                    # 继续分析
                    context.reset_interrupt_status()
            else:
                # 计算准确的剩余时间（使用实际时间差）
                if context.interrupt_start_time:
                    elapsed_seconds = (get_shanghai_time() - context.interrupt_start_time).total_seconds()
                    elapsed_minutes = elapsed_seconds / 60
                    remaining_minutes = max(0, InterruptTimeoutConfig.MAX_FEEDBACK_WAIT_TIME - elapsed_minutes)

                    # 发送等待状态更新（每30秒发送一次，且剩余时间大于0）
                    if context.interrupt_check_count % 15 == 0 and remaining_minutes > 0:  # 30秒 / 2秒 = 15次
                        log.info(f"在{stage}阶段等待用户反馈中... 剩余时间: {remaining_minutes:.1f}分钟")

                # 睡眠避免CPU密集检查
                if sleep_time > 0:
                    await asyncio.sleep(sleep_time)
                return False, {}  # 返回False表示需要等待，不继续执行
        
        # 处理用户反馈调整
        elif action == "adjust":
            log.info(f"在{stage}阶段应用用户反馈调整 [分析ID: {context.analysis_id}]")
            
            # 应用用户反馈
            feedback_data = interrupt_info.get("feedback", {})
            self._apply_user_feedback(context, feedback_data)
            
            # 清理打断信号
            self._cleanup_interrupt_signals(str(context.analysis_id))
            
            # 重置打断状态
            context.reset_interrupt_status()
            
            log.info(f"用户反馈已在{stage}阶段应用，继续分析")
            
            # 如果需要重置规划，设置任务状态
            if feedback_data.get("reset_planning", False):
                context.task_state = "planning"
                return False, {}  # 返回False表示需要重新规划

        # 处理用户取消反馈，继续分析
        elif action == "continue_after_cancel":
            log.info(f"在{stage}阶段用户取消反馈，继续分析 [分析ID: {context.analysis_id}]")

            # 清理打断信号
            self._cleanup_interrupt_signals(str(context.analysis_id))

            # 重置打断状态
            context.reset_interrupt_status()

            log.info(f"用户取消反馈已在{stage}阶段处理，继续分析")

        return True, {}  # 继续执行

    async def _extract_previous_charts_from_redis(self, current_analysis_id: str) -> List[Dict[str, Any]]:
        """
        从Redis缓存中提取历史图表信息（新实现）
        
        功能实现: 使用Redis缓存快速获取历史图表
        实现方案: 通过ChartCacheService从Redis读取按conversation_id缓存的图表
        影响范围: app/services/llm_analyzer.py _extract_previous_charts_from_redis方法
        实现日期: 2025-01-12

        Args:
            current_analysis_id: 当前分析ID

        Returns:
            List[Dict[str, Any]]: 历史图表信息列表
        """
        try:
            from app.models.analysis import Analysis
            from app.services.chart_cache_service import chart_cache_service

            # 获取当前分析的基本信息
            current_analysis = self.db.query(Analysis).filter(
                Analysis.id == current_analysis_id
            ).first()

            if not current_analysis:
                log.debug("当前分析不存在，跳过历史图表提取")
                return []

            conversation_id = current_analysis.conversation_id
            if not conversation_id:
                log.debug("当前分析没有关联的会话ID，跳过历史图表提取")
                return []

            log.info(f"🔍 开始从Redis缓存提取对话中的所有图表，当前分析: {current_analysis_id}")
            log.info(f"   会话ID: {conversation_id}")
            log.info(f"   轮次: {current_analysis.round_number}")
            log.info(f"   策略: 获取对话中的所有图表（包括当前分析已生成的）")

            # 从Redis缓存获取当前对话的所有图表（不排除当前分析）
            previous_charts = chart_cache_service.get_conversation_charts(
                conversation_id=conversation_id,
                max_charts=20,
                exclude_analysis_id=None  # 🔥 不排除任何分析，获取对话中的所有图表
            )

            if previous_charts:
                log.info(f"🎨 从Redis缓存获取到 {len(previous_charts)} 个对话图表")
                for i, chart in enumerate(previous_charts, 1):
                    analysis_id = chart.get('analysis_id', '未知')
                    is_current = "（当前分析）" if analysis_id == current_analysis_id else "（历史分析）"
                    log.info(f"   图表{i}{is_current}: {chart.get('title', '未命名')} "
                            f"(类型: {chart.get('chart_type', '未知')}, "
                            f"轮次: {chart.get('round_number', '未知')}, "
                            f"分析: {analysis_id})")
            else:
                log.info("🔍 Redis缓存中没有找到对话图表")

            return previous_charts

        except Exception as e:
            log.error(f"从Redis缓存提取历史图表失败: {str(e)}")
            # 不再降级到数据库查询，直接返回空列表
            return []



            