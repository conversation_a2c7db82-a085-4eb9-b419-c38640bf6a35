"""
统一分析评估器 - 整合洞察分析和智能评估功能
将ProfessionalResultAnalyzer和ResultEvaluator的功能合并到一次LLM调用中
"""

import json
import logging
from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

try:
    from app.services.result_analyzer import (
        AnalysisResult, AnalysisInsight, StatisticalSummary,
        DataQualityAssessment, ProfessionalResultAnalyzer
    )
except ImportError:
    # 如果导入失败，提供模拟的类
    from dataclasses import dataclass
    from typing import List, Dict, Any

    @dataclass
    class StatisticalSummary:
        total_rows: int
        columns: List[str]
        numeric_columns: List[str]
        categorical_columns: List[str]
        missing_values: Dict[str, int]
        basic_stats: Dict[str, Dict[str, float]]
        value_distributions: Dict[str, Dict[str, int]]

    @dataclass
    class DataQualityAssessment:
        completeness_score: float
        consistency_score: float
        validity_score: float
        overall_score: float
        quality_issues: List[str]

    @dataclass
    class AnalysisInsight:
        insight_type: str
        title: str
        description: str
        confidence: float
        supporting_data: str

    @dataclass
    class AnalysisResult:
        statistical_summary: StatisticalSummary
        data_quality_assessment: DataQualityAssessment
        insights: List[AnalysisInsight]
        patterns: List[str]
        anomalies: List[str]
        correlations: List[Dict[str, Any]]

    class ProfessionalResultAnalyzer:
        def __init__(self, db=None):
            pass

try:
    from app.services.llm_factory import get_llm_service
except ImportError:
    # 如果导入失败，提供一个模拟的服务
    def get_llm_service(db=None):
        return None

try:
    from app.utils.time_utils import get_shanghai_time
except ImportError:
    from datetime import datetime
    def get_shanghai_time():
        return datetime.now()

try:
    from app.utils.json_utils import DecimalEncoder
except ImportError:
    import json
    class DecimalEncoder(json.JSONEncoder):
        def default(self, obj):
            return str(obj)

log = logging.getLogger(__name__)

@dataclass
class UnifiedAnalysisResult:
    """统一分析评估结果"""
    # 原洞察分析结果
    statistical_summary: StatisticalSummary
    data_quality_assessment: DataQualityAssessment
    insights: List[AnalysisInsight]
    patterns: List[str]
    anomalies: List[str]
    correlations: List[Dict[str, Any]]
    
    # 原智能评估结果
    should_continue: bool
    completion_confidence: float
    missing_aspects: List[str]
    reasoning: str
    suggested_next_steps: List[str]
    evaluation_conclusion: str
    
    # 新增：统一的分析评估摘要
    unified_summary: str
    next_analysis_stage: str = 'data_collection'
    specific_guidance: str = ''
    avoid_duplicate_queries: List[str] = field(default_factory=list)
    
    def to_analysis_result(self) -> AnalysisResult:
        """转换为AnalysisResult格式（向后兼容）"""
        return AnalysisResult(
            statistical_summary=self.statistical_summary,
            data_quality_assessment=self.data_quality_assessment,
            insights=self.insights,
            patterns=self.patterns,
            anomalies=self.anomalies,
            correlations=self.correlations
        )
    
    def to_evaluation_result(self) -> Dict[str, Any]:
        """转换为评估结果格式（向后兼容）"""
        return {
            "should_continue": self.should_continue,
            "completion_confidence": self.completion_confidence,
            "missing_aspects": self.missing_aspects,
            "reasoning": self.reasoning,
            "suggested_next_steps": self.suggested_next_steps,
            "evaluation_conclusion": self.evaluation_conclusion,
            "next_analysis_stage": self.next_analysis_stage,
            "specific_guidance": self.specific_guidance,
            "avoid_duplicate_queries": self.avoid_duplicate_queries
        }
    
    def to_insight_event(self) -> Dict[str, Any]:
        """转换为洞察发现事件格式"""
        return {
            "insights": [
                {
                    "insight_type": insight.insight_type,
                    "title": insight.title,
                    "description": insight.description,
                    "confidence": insight.confidence,
                    "supporting_data": insight.supporting_data
                } for insight in self.insights
            ],
            "patterns": self.patterns,
            "anomalies": self.anomalies,
            "data_quality_score": self.data_quality_assessment.overall_score if hasattr(self.data_quality_assessment, 'overall_score') else 0.0,
            "total_rows": self.statistical_summary.total_rows,
            "columns_count": len(self.statistical_summary.columns)
        }
    
    def to_evaluation_event(self) -> Dict[str, Any]:
        """转换为评估完成事件格式"""
        return {
            "should_continue": self.should_continue,
            "completion_confidence": self.completion_confidence,
            "missing_aspects": self.missing_aspects,
            "reasoning": self.reasoning,
            "suggested_next_steps": self.suggested_next_steps,
            "evaluation_conclusion": self.evaluation_conclusion,
            "next_analysis_stage": self.next_analysis_stage,
            "specific_guidance": self.specific_guidance
        }


class UnifiedAnalysisEvaluator:
    """统一分析评估器 - 在一次LLM调用中完成洞察分析和智能评估"""
    
    def __init__(self, db=None):
        self.db = db
        try:
            self.llm_service = get_llm_service(db)
        except Exception as e:
            log.warning(f"初始化LLM服务失败: {str(e)}")
            self.llm_service = None
    
    async def analyze_and_evaluate(
        self,
        tool_result: Dict[str, Any],
        context: Any,
        user_query: str,
        step_id: str,
        tool_name: str,
        schema_info: str,
        user_language: str = 'zh-CN',
        intent_analysis: Dict[str, Any] = None,
        tool_reasoning: str = None,
        tool_parameters: Dict[str, Any] = None,
        conversation_context: str = None
    ) -> Tuple[UnifiedAnalysisResult, Dict[str, Any]]:
        """统一的分析和评估流程
        
        Args:
            tool_result: 工具执行结果
            context: 执行上下文
            user_query: 用户查询
            step_id: 步骤ID
            tool_name: 工具名称
            schema_info: Schema信息
            user_language: 用户语言偏好
            intent_analysis: 意图分析结果
            tool_reasoning: 工具推理
            tool_parameters: 工具参数
            conversation_context: 对话上下文
            
        Returns:
            Tuple[UnifiedAnalysisResult, Dict[str, Any]]: 统一结果和使用数据
        """
        try:
            log.info(f"开始统一分析评估 [步骤ID: {step_id}, 工具: {tool_name}]")

            # 检查服务是否可用
            if self.llm_service is None:
                log.warning(f"LLM服务不可用，跳过统一分析评估 [步骤ID: {step_id}]")
                return None, {}

            # 检查工具结果是否成功
            success_value = tool_result.get('success', True)
            if success_value is False or success_value == "False" or success_value == "false":
                log.warning(f"工具执行失败，跳过统一分析评估 [步骤ID: {step_id}]")
                return None, {}

            # 🔥 核心改进：直接进行统一的LLM分析评估，不再调用原有分析器
            unified_result, usage_data = await self._perform_unified_llm_analysis(
                tool_result=tool_result,
                context=context,
                user_query=user_query,
                step_id=step_id,
                tool_name=tool_name,
                schema_info=schema_info,
                user_language=user_language,
                intent_analysis=intent_analysis,
                tool_reasoning=tool_reasoning,
                tool_parameters=tool_parameters,
                conversation_context=conversation_context
            )

            if unified_result is not None:
                log.info(f"统一分析评估完成 [步骤ID: {step_id}, 洞察数: {len(unified_result.insights)}, 继续分析: {unified_result.should_continue}]")
            else:
                log.info(f"统一分析评估返回None [步骤ID: {step_id}]")

            return unified_result, usage_data

        except Exception as e:
            log.error(f"统一分析评估失败: {str(e)}")
            # 不返回错误结果，让流程继续进行下一步
            return None, {}
    
    async def _perform_unified_llm_analysis(
        self,
        tool_result: Dict[str, Any],
        context: Any,
        user_query: str,
        step_id: str,
        tool_name: str,
        schema_info: str,
        user_language: str,
        intent_analysis: Dict[str, Any] = None,
        tool_reasoning: str = None,
        tool_parameters: Dict[str, Any] = None,
        conversation_context: str = None,

    ) -> Tuple[UnifiedAnalysisResult, Dict[str, Any]]:
        """执行统一的LLM分析评估 - 一次调用完成洞察分析和智能评估"""
        try:
            # 🔥 构建统一的提示词（直接基于工具结果，不依赖预处理）
            user_prompt = self._build_unified_user_prompt(
                tool_result=tool_result,
                context=context,
                user_query=user_query,
                schema_info=schema_info,
                user_language=user_language,
                intent_analysis=intent_analysis,
                tool_reasoning=tool_reasoning,
                tool_parameters=tool_parameters,
                conversation_context=conversation_context
            )
            system_prompt = self._build_unified_system_prompt(user_language)

            # 🔥 单次LLM调用，同时完成洞察分析和智能评估
            llm_result, usage_data = await self._call_unified_llm(
                user_prompt, system_prompt, user_language
            )

            # 🔥 记录统一分析评估的LLM调用到生命周期日志
            lifecycle_logger = getattr(context, 'lifecycle_logger', None) if hasattr(context, 'lifecycle_logger') else None
            if lifecycle_logger:
                try:
                    lifecycle_logger.log_llm_call(
                        step_id=step_id,
                        call_type="unified_analysis_evaluation",
                        user_prompt=user_prompt,
                        system_prompt=system_prompt,
                        llm_response=llm_result,
                        usage_data=usage_data,
                        temperature=0.2,
                        tool_name=tool_name
                    )
                    log.info(f"✅ 统一分析评估LLM调用已记录到生命周期日志 [步骤ID: {step_id}]")
                except Exception as e:
                    log.error(f"❌ 记录统一分析评估LLM调用失败: {str(e)}")

            # 🔥 验证LLM结果格式
            if not self._validate_llm_result(llm_result):
                log.warning(f"LLM结果格式验证失败，跳过统一分析评估 [步骤ID: {step_id}]")
                return None, usage_data

            # 🔥 解析统一结果（基于简化的输出格式）
            unified_result = self._parse_unified_llm_result(llm_result, tool_result, context)

            # 🔥 记录解析结果到日志
            if lifecycle_logger and unified_result:
                try:
                    lifecycle_logger.log_analysis_result(
                        step_id=step_id,
                        analysis_type="unified_analysis_evaluation",
                        insights_count=len(unified_result.insights),
                        should_continue=unified_result.should_continue,
                        completion_confidence=unified_result.completion_confidence,
                        evaluation_conclusion=unified_result.evaluation_conclusion[:200] + "..." if len(unified_result.evaluation_conclusion) > 200 else unified_result.evaluation_conclusion,
                        next_analysis_stage=unified_result.next_analysis_stage,
                        specific_guidance=unified_result.specific_guidance[:100] + "..." if len(unified_result.specific_guidance) > 100 else unified_result.specific_guidance
                    )
                    log.info(f"✅ 统一分析评估结果已记录到生命周期日志 [步骤ID: {step_id}]")
                except Exception as e:
                    log.error(f"❌ 记录统一分析评估结果失败: {str(e)}")

            return unified_result, usage_data

        except Exception as e:
            log.error(f"统一LLM分析失败: {str(e)}")
            # 不返回错误结果，让流程继续进行下一步
            return None, {}
    


    def _build_unified_system_prompt(self, user_language: str = 'zh-CN') -> str:
        """构建统一的分析评估系统提示词"""
        if user_language == 'en-US':
            return """You are a top-tier Data Analysis and Quality Evaluation Expert with deep expertise in data science, database querying, and business analysis. Your task is to perform unified analysis and evaluation in a single comprehensive assessment.

**UNIFIED ANALYSIS & EVALUATION FRAMEWORK:**

### PHASE 1: THREE-DIMENSIONAL INSIGHT ANALYSIS
**Dimension 1: Phenomenon Insights** - What does the data show?
1. Identify key patterns, trends, and statistical features
2. Discover anomalies and special phenomena
3. Describe intuitive data manifestations

**Dimension 2: Causal Analysis** - Why is this happening?
1. Analyze root causes behind phenomena
2. Explain business logic and driving factors
3. Identify causal relationships and impact mechanisms
4. Assess external factor influences

**Dimension 3: Deep Direction** - What should be analyzed next?
1. Propose hypotheses that need verification
2. Suggest specific deep analysis directions
3. Recommend relevant data sources and analysis methods
4. Predict potential insight types

### PHASE 2: INTELLIGENT QUALITY EVALUATION
**Core Evaluation Dimensions:**
1. **Data Quality & Reasonableness Check**
2. **Data Completeness Assessment**
3. **Query Strategy Verification**
4. **Problem Diagnosis & Solutions**
5. **Direct Computational Analysis** (when data is sufficient)

**Computational Analysis Authorization:**
When sufficient data is obtained, you are authorized to perform:
- **Basic Statistics**: mean, median, standard deviation, quantiles
- **Comparative Analysis**: year-over-year, period-over-period comparisons
- **Change Analysis**: growth rates, change magnitude, trend identification
- **Anomaly Detection**: identify outliers and anomalous values
- **Correlation Analysis**: correlation analysis, causal inference
- **Aggregation**: grouping statistics, summary analysis
- **Business Metrics**: calculate derived indicators based on business logic

**Analysis Depth & Efficiency Balance Standards:**

**1. Analysis Depth Assessment Guidelines:**
- **Query-type questions**: "Query XX data", "Show XX info" → Accurate data is sufficient
- **Analysis-type questions**: "Analyze XX", "XX causes", "XX trends", "Compare XX" → Requires deep analysis

**Required depth for analysis-type questions:**
- **Comparative Analysis**: temporal, group, benchmark comparisons
- **Change Analysis**: growth rates, change magnitude, change causes
- **Pattern Recognition**: trends, periodicity, anomalies
- **Causal Analysis**: influencing factors, driving causes, correlations
- **Business Insights**: business implications, impact assessment, improvement recommendations

**2. Analysis Completion Criteria:**
**Set should_continue=false when meeting any of the following:**

**A. Force End Conditions (Highest Priority):**
- planning_rounds >= 15: Unconditional end to avoid infinite loops
- remaining_rounds <= 1: System forced termination

**B. Efficiency Priority End Conditions:**
- planning_rounds >= 10 AND has_basic_analysis: Efficiency priority end
- planning_rounds >= 8 AND user_core_question_basically_answered

**C. Reasonable End Conditions (Recommended):**
- user_core_question_basically_answered + certain_analysis_depth + planning_rounds >= 5
- sufficient_data + basic_computation_completed + completion_confidence >= 0.7
- For analysis questions: completed at least 2 of: comparative analysis, change analysis, pattern recognition

**D. Deep Analysis Completion (Ideal):**
- Deep analysis complete: computation, comparison, insights, recommendations all comprehensive
- completion_confidence >= 0.8: High analysis quality
- user_question_fully_answered: Exceeds user expectations

**3. Must Continue Analysis (Only in these cases):**
- User core question completely unanswered: No basic information provided
- Critical data completely missing: Cannot perform meaningful analysis
- Serious data quality issues: Data errors, format anomalies affecting conclusion reliability
- planning_rounds < 3 AND analysis_depth_obviously_insufficient: Just started, needs basic data
- Analysis question but basic depth not reached: Only data display, lacks analytical insights

**OUTPUT FORMAT:**
```json
{
    "action_type": "unified_analysis_evaluation_result",
    "insights": [{"type": "trend|pattern|anomaly|computation", "title": "...", "description": "...", "confidence": 0.0-1.0}],
    "should_continue": true/false,
    "completion_confidence": 0.0-1.0,
    "next_steps": ["strategic direction 1", "strategic direction 2"],
    "reasoning": "concise evaluation reasoning",
    "stage": "data_collection|phenomenon_analysis|causal_analysis|strategy_generation"
}
```

**🚨 CRITICAL FORMAT REQUIREMENTS:**
- Must return pure JSON format, do NOT wrap in markdown code blocks
- Do NOT add ```json or ``` markers
- Do NOT add any explanatory text before or after JSON
- Output JSON object directly, ensure correct format"""

        else:
            return """你是一个顶级的数据分析与质量评估专家，具备深厚的数据科学、数据库查询和业务分析经验。你的任务是在一次综合评估中同时完成洞察分析和智能评估。

**统一分析评估框架：**

### 阶段一：三维递进式洞察分析
**【第一维：现象洞察】** - 数据呈现了什么？
1. 识别关键模式、趋势和统计特征
2. 发现异常和特殊现象
3. 描述数据的直观表现

**【第二维：本质分析】** - 为什么会这样？
1. 分析现象背后的根本原因
2. 解释业务逻辑和驱动因素
3. 识别因果关系和影响机制
4. 评估外部因素的作用

**【第三维：深入方向】** - 接下来应该分析什么？
1. 提出需要验证的假设
2. 建议具体的深入分析方向
3. 推荐相关的数据源和分析方法
4. 预测可能获得的洞察类型

### 阶段二：智能质量评估与计算分析
**核心评估维度：**
1. **数据质量与合理性检查**
2. **数据完整性评估**
3. **查询策略验证**
4. **问题诊断与解决方案**
5. **直接计算分析**（当数据充足时）

**通用计算授权：**
当获取到充足的数据时，你有权直接进行以下类型的计算：
- **基础统计**：均值、中位数、标准差、分位数等
- **比较分析**：同比、环比、对比分析
- **变化分析**：增长率、变化幅度、趋势识别
- **异常检测**：识别异常值、离群点
- **关联分析**：相关性分析、因果推断
- **聚合计算**：分组统计、汇总分析
- **业务指标**：根据业务逻辑计算衍生指标

**通用计算规则：**
- **除零处理**：分母为0时合理标记（如"无数据"、"新增"等）
- **精度控制**：数值保留合适的小数位数
- **异常标记**：识别并标记异常或显著变化
- **单位处理**：保持数据单位的一致性和可读性
- **缺失值**：合理处理空值和缺失数据

**分析深度与效率平衡标准：**

**1. 分析深度评估指南：**
- **查询类问题**："查询XX数据"、"显示XX信息" → 获得准确数据即可
- **分析类问题**："分析XX"、"XX的原因"、"XX趋势"、"对比XX" → 需要深度分析

**分析类问题的必要深度要求：**
- **对比分析**：时间对比、分组对比、基准对比
- **变化分析**：增长率、变化幅度、变化原因
- **模式识别**：趋势、周期性、异常值
- **因果分析**：影响因素、驱动原因、相关性
- **业务洞察**：业务含义、影响评估、改进建议

**2. 分析完成的判断标准：**
**满足以下条件之一即可设置 should_continue=false：**

**A. 强制结束条件（优先级最高）：**
- 规划轮次 >= 15：无条件结束，避免无限循环
- 剩余轮次 <= 1：系统强制结束

**B. 效率优先结束条件：**
- 规划轮次 >= 10 且 有基础分析结果：效率优先结束
- 规划轮次 >= 8 且 用户核心问题已基本回答

**C. 合理结束条件（推荐）：**
- 用户核心问题已基本回答 + 有一定分析深度 + 规划轮次 >= 5
- 数据充足 + 完成基础计算 + completion_confidence >= 0.7
- 对于分析类问题：已完成对比分析、变化分析、模式识别中的至少2项

**D. 深度分析完成条件（理想）：**
- 深度分析完成：计算、对比、洞察、建议都很完整
- completion_confidence >= 0.8：分析质量很高
- 用户问题完全回答：超出用户期望的分析深度

**3. 必须继续分析的情况（仅限以下情况）：**
- 用户核心问题完全未回答：连基础信息都没有提供
- 关键数据完全缺失：无法进行任何有意义的分析
- 数据质量严重问题：数据错误、格式异常等影响结论可靠性
- 规划轮次 < 3 且 分析深度明显不足：刚开始分析，需要基础数据
- 分析类问题但未达到基本深度要求：仅有数据展示，缺乏分析洞察

**4. 分析深度不足的常见情况：**
- 获得数据但未计算关键指标（如增长率、占比等）
- 有对比数据但未分析差异原因
- 发现异常但未深入调查
- 有趋势数据但未分析驱动因素
- 缺乏业务层面的解释和建议

**计算洞察格式要求：**
当进行了详细计算分析时，请在insights中添加一个"computation"类型的洞察：
```json
{
  "type": "computation",
  "title": "数据计算分析结果",
  "description": "基于获取数据进行的完整计算分析，包括关键指标计算、变化率分析、业务洞察等详细结果",
  "confidence": 0.9
}
```

**next_steps指导原则：**
- **方向性建议**：提供分析方向和策略，而非具体的技术执行步骤
- **避免技术细节**：不要包含具体的SQL语句、计算公式或工具参数
- **基于已有数据**：充分利用已获得的数据，避免重复查询
- **分析结束判断**：如果所需数据已经获得，仅剩下计算、分析工作，可以直接设置should_continue=false结束分析

**简化输出格式：**
```json
{
    "action_type": "unified_analysis_evaluation_result",
    "insights": [{"type": "trend|pattern|anomaly|computation", "title": "...", "description": "...", "confidence": 0.0-1.0}],
    "should_continue": true/false,
    "completion_confidence": 0.0-1.0,
    "next_steps": ["分析方向性建议1", "分析方向性建议2"],
    "reasoning": "基于数据进行分析评估",
    "stage": "data_collection|phenomenon_analysis|causal_analysis|strategy_generation"
}
```

**🚨 重要格式要求：**
- 必须返回纯JSON格式，不要使用markdown代码块包裹
- 不要添加```json或```标记
- 不要在JSON前后添加任何解释性文字
- 直接输出JSON对象，确保格式正确"""

    def _build_unified_user_prompt(
        self,
        tool_result: Dict[str, Any],
        context: Any,
        user_query: str,
        schema_info: str,
        user_language: str,
        intent_analysis: Dict[str, Any] = None,
        tool_reasoning: str = None,
        tool_parameters: Dict[str, Any] = None,
        conversation_context: str = None
    ) -> str:
        """构建统一的分析评估用户提示词 - 按重要性分层组织"""
        # 获取当前时间
        current_time = get_shanghai_time()
        formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S")

        # 🎯 第一层：核心评估任务（最重要）
        core_task = f"""**🎯 核心评估任务**
**用户查询**: {user_query}
**当前时间**: {formatted_time}
**评估目标**: 分析本次工具执行结果，判断是否继续分析并指导下一轮规划"""

        # 🚨 规划轮次检查（防止无限循环）
        planning_rounds = getattr(context, 'planning_rounds', 0)
        max_rounds = getattr(context, 'max_planning_rounds', 25)
        remaining_rounds = max_rounds - planning_rounds

        rounds_warning = ""
        if planning_rounds >= 15:
            rounds_warning = f"""
🚨 **强制结束警告**: 当前已进行 {planning_rounds} 轮规划，必须结束分析！
⚠️ **效率要求**: 无论分析深度如何，都应设置 should_continue=false
🎯 **用户体验**: 避免无限循环，提供现有分析结果"""
        elif planning_rounds >= 10:
            rounds_warning = f"""
⚠️ **效率提醒**: 当前已进行 {planning_rounds} 轮规划，强烈建议结束分析
🎯 **平衡原则**: 如果有基础分析结果，应设置 should_continue=false
📊 **剩余轮次**: {remaining_rounds} 轮"""
        elif planning_rounds >= 5:
            rounds_warning = f"""
📊 **轮次提醒**: 当前已进行 {planning_rounds} 轮规划，应考虑结束分析
⚖️ **效率平衡**: 如果用户问题基本回答，建议设置 should_continue=false
📊 **剩余轮次**: {remaining_rounds} 轮"""

        # 📊 第二层：本次工具执行结果（当前焦点）
        tool_execution_block = self._build_tool_execution_block(
            tool_result, tool_reasoning, tool_parameters, context
        )

        # 📈 第三层：执行历史脉络（分析连贯性保障）
        execution_history_block = self._build_execution_history_block(context)

        # 🧠 第四层：历史上下文参考（辅助信息）
        history_context_block = self._build_history_context_block(
            context, conversation_context, intent_analysis
        )

        # 📋 第五层：技术细节（支撑信息）
        technical_details_block = self._build_technical_details_block(
            context, schema_info
        )

        # 组装完整提示词
        if user_language == 'en-US':
            return f"""{core_task}
{rounds_warning}

{tool_execution_block}

{execution_history_block}

{history_context_block}

{technical_details_block}

Please perform unified analysis and evaluation based on the above information. Focus on:
1. Extract insights from the current tool execution result
2. Evaluate data quality, completeness, and analysis progress
3. Make quantified decision on whether to continue analysis
4. Provide specific guidance for next planning round"""
        else:
            return f"""{core_task}
{rounds_warning}

{tool_execution_block}

{execution_history_block}

{history_context_block}

{technical_details_block}

**🔥 量化评估要求**:
1. **完成度评估**: 基于已有信息充分性，给出0-1的完成度评分
2. **循环检测**: 检查是否存在重复分析模式，避免无效循环
3. **决策量化**: 严格按照量化标准进行继续/结束决策
4. **指导明确**: 如建议继续，必须给出具体可执行的下一步方向

请基于以上信息进行统一的分析评估。"""

    def _build_tool_execution_block(self, tool_result, tool_reasoning, tool_parameters, context):
        """构建工具执行信息块"""
        # 预处理工具结果，提取关键信息
        processed_result = self._preprocess_tool_result(tool_result)

        block = "**📊 本次工具执行结果** (当前分析焦点)\n"

        if tool_reasoning:
            block += f"**执行推理**: {tool_reasoning}\n"

        # 添加工具参数信息
        if tool_parameters:
            block += f"**工具参数**: {json.dumps(tool_parameters, ensure_ascii=False, indent=2, cls=DecimalEncoder)}\n"

        if processed_result:
            block += f"**执行结果**: {json.dumps(processed_result, ensure_ascii=False, indent=2, cls=DecimalEncoder)}\n"

        return block

    def _build_history_context_block(self, context, conversation_context, intent_analysis):
        """构建历史上下文信息块"""
        block = "**🧠 历史上下文参考** (辅助信息)\n"

        # 增强的评估历史感知 - 包含评估内容和结论
        if hasattr(context, 'evaluation_history') and context.evaluation_history:
            evaluation_count = len(context.evaluation_history)

            # 计算历史统计
            avg_confidence = sum(e.get('completion_confidence', 0) for e in context.evaluation_history) / evaluation_count
            continue_count = sum(1 for e in context.evaluation_history if e.get('should_continue', True))

            block += f"**🔍 评估历史感知** (共{evaluation_count}次评估): 平均完成度{avg_confidence:.2f}, {continue_count}次建议继续\n"

            # 显示最近的评估结果内容（最多3次）
            recent_evaluations = context.evaluation_history[-3:]
            block += "\n**📋 近期评估详情**:\n"

            for i, eval_record in enumerate(recent_evaluations, 1):
                eval_index = evaluation_count - len(recent_evaluations) + i

                # 🔥 关键修复：从 evaluation_result 字段中提取真正的评估内容
                evaluation_result = eval_record.get('evaluation_result', {})

                # 如果 evaluation_result 是字典，从中提取数据
                if isinstance(evaluation_result, dict):
                    should_continue = evaluation_result.get('should_continue', True)
                    completion_confidence = evaluation_result.get('completion_confidence', 0.0)

                    # 从 evaluation_result 中提取评估内容
                    reasoning = (evaluation_result.get('reasoning', '') or
                               evaluation_result.get('evaluation_reasoning', '') or
                               evaluation_result.get('analysis_reasoning', '') or
                               evaluation_result.get('unified_summary', '') or
                               evaluation_result.get('思路', '') or
                               evaluation_result.get('推理', ''))

                    evaluation_conclusion = (evaluation_result.get('evaluation_conclusion', '') or
                                           evaluation_result.get('conclusion', '') or
                                           evaluation_result.get('analysis_conclusion', '') or
                                           evaluation_result.get('结论', '') or
                                           evaluation_result.get('评估结论', ''))

                    suggested_next_steps = (evaluation_result.get('suggested_next_steps', []) or
                                          evaluation_result.get('next_steps', []) or
                                          evaluation_result.get('recommendations', []) or
                                          evaluation_result.get('建议', []) or
                                          evaluation_result.get('下一步', []))
                else:
                    # 兜底：从顶层字段提取
                    should_continue = eval_record.get('should_continue', True)
                    completion_confidence = eval_record.get('completion_confidence', 0.0)
                    reasoning = ''
                    evaluation_conclusion = ''
                    suggested_next_steps = []

                # 🔥 调试信息 - 显示实际的数据结构
                if completion_confidence == 0.0 and not reasoning and not evaluation_conclusion:
                    # 如果主要字段都为空，显示实际的数据结构帮助调试
                    available_keys = list(eval_record.keys())
                    block += f"  📊 评估{eval_index}: {'继续' if should_continue else '结束'} (置信度{completion_confidence:.2f})\n"
                    block += f"    🔧 调试: 顶层字段 {available_keys}\n"

                    # 🔥 重点显示 evaluation_result 的内容
                    evaluation_result = eval_record.get('evaluation_result', {})
                    if isinstance(evaluation_result, dict):
                        result_keys = list(evaluation_result.keys())
                        block += f"    🔍 evaluation_result字段: {result_keys}\n"

                        # 显示 evaluation_result 中的关键内容
                        for key, value in evaluation_result.items():
                            if isinstance(value, str) and len(value) > 5:
                                display_value = value[:150] + "..." if len(value) > 150 else value
                                block += f"    📝 {key}: {display_value}\n"
                            elif isinstance(value, list) and len(value) > 0:
                                display_value = '; '.join(str(v)[:50] for v in value[:2])
                                block += f"    📋 {key}: {display_value}\n"
                            elif isinstance(value, (int, float, bool)):
                                block += f"    🔢 {key}: {value}\n"
                    else:
                        block += f"    ⚠️ evaluation_result不是字典: {type(evaluation_result)} = {str(evaluation_result)[:100]}\n"

                    # 显示其他顶层字段的有用信息
                    for key, value in eval_record.items():
                        if key != 'evaluation_result' and isinstance(value, str) and len(value) > 10:
                            block += f"    📝 {key}: {value[:100]}{'...' if len(value) > 100 else ''}\n"
                else:
                    block += f"  📊 评估{eval_index}: {'继续' if should_continue else '结束'} (置信度{completion_confidence:.2f})\n"

                    # 显示评估推理
                    if reasoning:
                        block += f"    💭 推理: {reasoning}\n"

                    # 显示完整的评估结论
                    if evaluation_conclusion:
                        block += f"    🎯 关键结论: {evaluation_conclusion}\n"

                    # 显示完整的建议内容
                    if suggested_next_steps:
                        if isinstance(suggested_next_steps, list):
                            # 显示所有建议，每个建议单独一行
                            block += f"    📝 建议:\n"
                            for i, step in enumerate(suggested_next_steps, 1):
                                block += f"      {i}. {str(step)}\n"
                        else:
                            block += f"    📝 建议: {str(suggested_next_steps)}\n"

                block += "\n"

            # 循环风险和模式检测
            if evaluation_count >= 3:
                # 检测重复的评估模式
                recent_conclusions = []
                for eval_record in recent_evaluations:
                    conclusion = eval_record.get('evaluation_conclusion', '')
                    if conclusion:
                        # 提取关键词进行模式匹配
                        key_phrases = self._extract_key_evaluation_phrases(conclusion)
                        recent_conclusions.append(key_phrases)

                # 检测是否有重复的关键词模式
                if len(recent_conclusions) >= 2:
                    common_phrases = set(recent_conclusions[-1]) & set(recent_conclusions[-2])
                    if common_phrases:
                        block += f"  - 检测到重复评估模式: {', '.join(list(common_phrases)[:3])}\n"

                block += "  - 避免重复相同的评估思路和建议\n"
                block += "  - 如果问题持续存在，考虑调整分析策略或结束分析\n"

        # 多轮对话上下文
        if conversation_context:
            block += f"**对话上下文**: {conversation_context}\n"
        elif hasattr(context, 'multi_round_context') and context.multi_round_context:
            ctx_summary = context.multi_round_context.get('context_summary', '')
            if ctx_summary:
                block += f"**历史摘要**: {ctx_summary}\n"

        # 意图分析（完整保留，全局指导）
        if intent_analysis:
            block += "**用户意图分析** (全局指导):\n"

            # 核心意图描述
            intent_desc = intent_analysis.get('intent_description', '')
            if intent_desc:
                block += f"**分析目标**: {intent_desc}\n"

            # 执行步骤规划
            execution_steps = intent_analysis.get('execution_steps', [])
            if execution_steps:
                block += f"**规划步骤** (共{len(execution_steps)}步):\n"
                for step in execution_steps[:3]:  # 最多显示前3步，避免过长
                    step_num = step.get('step_number', '?')
                    step_intent = step.get('step_intent', '')
                    recommended_tool = step.get('recommended_tool', '')
                    expected_outcome = step.get('expected_outcome', '')

                    block += f"  步骤{step_num}: {step_intent}\n"
                    block += f"    推荐工具: {recommended_tool}\n"
                    block += f"    预期结果: {expected_outcome}\n"

                if len(execution_steps) > 3:
                    block += f"  ... 还有{len(execution_steps) - 3}个步骤\n"

            # 用户调整信息
            if intent_analysis.get('has_user_modification') and intent_analysis.get('user_adjustment'):
                user_adjustment = intent_analysis.get('user_adjustment', '')
                block += f"**用户确认**: {user_adjustment}\n"

        return block

    def _build_execution_history_block(self, context):
        """构建执行历史信息块 - 保留完整工具执行记录"""
        block = "**📈 执行历史脉络** (分析连贯性保障)\n"

        if not context.execution_history:
            return block + "暂无执行历史\n"

        block += f"**总执行步骤**: {len(context.execution_history)}步\n"

        # 显示所有执行记录的完整信息
        for i, execution in enumerate(context.execution_history, 1):
            tool_name = execution.get('tool_name', '未知工具')
            reasoning = execution.get('reasoning', '无推理记录')
            parameters = execution.get('parameters', {})

            # 显示完整参数信息（原样展示）
            param_display = json.dumps(parameters, ensure_ascii=False, indent=2, cls=DecimalEncoder) if parameters else "无参数"

            # 提取结果状态（不显示结果明细）
            result = execution.get('result', {})
            result_status = self._summarize_result_status(result)

            block += f"\n**步骤{i}**: {tool_name}\n"
            block += f"  推理: {reasoning}\n"
            block += f"  参数: {param_display}\n"
            block += f"  状态: {result_status}\n"

        return block

    def _build_technical_details_block(self, context, schema_info):
        """构建技术细节信息块"""
        block = "**📋 技术细节** (支撑信息)\n"

        # 执行状态
        block += f"**执行状态**: 步骤{len(context.execution_history)}/{context.max_planning_rounds}, "
        block += f"剩余{context.max_planning_rounds - context.planning_rounds}轮\n"

        # 分析阶段
        if hasattr(context, 'analysis_stage'):
            stage_names = {
                'data_collection': '数据收集',
                'phenomenon_analysis': '现象分析',
                'causal_analysis': '本质分析',
                'strategy_generation': '策略生成'
            }
            current_stage = stage_names.get(context.analysis_stage, context.analysis_stage)
            block += f"**当前阶段**: {current_stage}\n"

        # 完整的Schema信息（不简化）
        block += f"**数据库Schema**:\n{schema_info}\n"

        return block





    def _summarize_result_status(self, result: dict) -> str:
        """总结结果状态"""
        if not result:
            return "无结果"

        success = result.get('success', True)
        if not success:
            error = result.get('error', '未知错误')
            return f"失败: {error[:50]}..." if len(error) > 50 else f"失败: {error}"

        # 成功情况下的数据摘要
        if 'data' in result and isinstance(result['data'], list):
            data_count = len(result['data'])
            return f"成功: 获得{data_count}条数据"

        return "成功: 执行完成"

    def _preprocess_tool_result(self, tool_result):
        """预处理工具结果，提取关键信息"""
        if not tool_result:
            return None

        processed = {
            "success": tool_result.get("success", True),
            "error": tool_result.get("error", None)
        }

        # 处理不同格式的数据结果
        data = None

        # 优先检查 "results" 字段（新格式）
        if "results" in tool_result and isinstance(tool_result["results"], list):
            data = tool_result["results"]
        # 兼容旧格式的 "data" 字段
        elif "data" in tool_result and isinstance(tool_result["data"], list):
            data = tool_result["data"]

        if data:
            processed.update({
                "row_count": len(data),
                "full_data": data,  # 提供完整数据给大模型分析
                "columns": list(data[0].keys()) if data and isinstance(data[0], dict) else []
            })

        # 处理其他关键字段
        key_fields = ["message", "query", "sql", "execution_time", "display_format", "columns", "row_count"]
        for key in key_fields:
            if key in tool_result:
                processed[key] = tool_result[key]

        return processed

    def _extract_key_evaluation_phrases(self, evaluation_text: str) -> set:
        """从评估文本中提取关键短语，用于模式检测"""
        if not evaluation_text:
            return set()

        # 定义关键词模式
        key_patterns = [
            # 数据质量相关
            r'数据[质量|完整性|准确性]',
            r'[缺失|不足|不完整].*数据',
            r'数据.*[问题|错误|异常]',

            # 分析深度相关
            r'[需要|应该|建议].*[深入|进一步|详细].*分析',
            r'分析.*[不够|不足|浅层]',
            r'[现象|表面].*分析',

            # 查询策略相关
            r'[重复|相似].*查询',
            r'查询.*[策略|方法|方式]',
            r'SQL.*[问题|错误|优化]',

            # 业务理解相关
            r'业务.*[逻辑|含义|理解]',
            r'[缺乏|不足].*业务.*理解',

            # 完成度相关
            r'分析.*[完成|结束|充分]',
            r'[目标|任务].*[达成|完成]',
            r'信息.*[充分|足够|完整]'
        ]

        import re
        key_phrases = set()

        for pattern in key_patterns:
            matches = re.findall(pattern, evaluation_text)
            for match in matches:
                if isinstance(match, str) and len(match) > 2:
                    key_phrases.add(match[:20])  # 限制长度

        # 如果没有匹配到模式，提取包含关键词的短句
        if not key_phrases:
            keywords = ['数据质量', '分析深度', '业务理解', '查询策略', '完成度', '循环', '重复']
            sentences = evaluation_text.split('。')

            for sentence in sentences[:5]:  # 只检查前5句
                sentence = sentence.strip()
                for keyword in keywords:
                    if keyword in sentence and len(sentence) <= 50:
                        key_phrases.add(sentence)
                        break

        return key_phrases

    async def _call_unified_llm(
        self,
        user_prompt: str,
        system_prompt: str,
        user_language: str
    ) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """调用LLM进行统一分析评估"""
        try:
            # 根据用户语言偏好添加语言指令
            if user_language == 'en-US':
                language_prefix = "LANGUAGE REQUIREMENT: You MUST respond ONLY in English. "
                language_suffix = "\n\nCRITICAL: Your entire response must be in English language only."
            else:
                language_prefix = "语言要求：你必须完全使用中文回复。"
                language_suffix = "\n\n重要提醒：你的全部回复都必须使用中文。"

            system_prompt = language_prefix + system_prompt + language_suffix

            # 直接使用llm_service进行调用（参考result_analyzer.py的方式）
            if hasattr(self.llm_service, 'client'):
                completion = await self.llm_service.client.chat.completions.create(
                    model=self.llm_service.model,
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": user_prompt}
                    ],
                    response_format={"type": "json_object"},
                    temperature=0.2
                )

                result_text = completion.choices[0].message.content

                # 🔧 增强的JSON解析，处理可能的markdown包裹
                try:
                    result_json = json.loads(result_text)
                except json.JSONDecodeError:
                    # 如果直接解析失败，尝试清理markdown标记
                    import re
                    cleaned_content = result_text.strip()

                    # 方法1: 使用正则表达式提取markdown代码块中的JSON
                    json_pattern = r'```(?:json)?\s*(.*?)\s*```'
                    match = re.search(json_pattern, cleaned_content, re.DOTALL)

                    if match:
                        # 找到了markdown代码块，提取其中的内容
                        extracted_content = match.group(1).strip()
                        log.info("统一分析评估器：成功从markdown代码块中提取JSON内容")
                        result_json = json.loads(extracted_content)
                    else:
                        # 方法2: 移除可能的markdown标记
                        if cleaned_content.startswith('```json'):
                            cleaned_content = cleaned_content[7:].strip()
                        elif cleaned_content.startswith('```'):
                            cleaned_content = cleaned_content[3:].strip()

                        if cleaned_content.endswith('```'):
                            cleaned_content = cleaned_content[:-3].strip()

                        # 方法3: 查找JSON对象边界
                        start_pos = cleaned_content.find('{')
                        end_pos = cleaned_content.rfind('}')

                        if start_pos != -1 and end_pos != -1 and end_pos > start_pos:
                            json_content = cleaned_content[start_pos:end_pos + 1]
                            result_json = json.loads(json_content)
                            log.info("统一分析评估器：通过边界查找成功提取JSON内容")
                        else:
                            # 如果所有方法都失败，抛出错误
                            log.error(f"统一分析评估器：无法从响应中提取有效JSON: {result_text[:200]}...")
                            raise json.JSONDecodeError("无法提取有效JSON", result_text, 0)

                # 构建使用数据
                usage_data = {
                    "prompt_tokens": completion.usage.prompt_tokens,
                    "completion_tokens": completion.usage.completion_tokens,
                    "total_tokens": completion.usage.total_tokens,
                    "model": self.llm_service.model
                }

                return result_json, usage_data
            else:
                # 降级处理
                log.warning("LLM服务不可用，返回默认结果")
                return {"action_type": "unified_analysis_evaluation_result"}, {}

        except Exception as e:
            log.error(f"LLM调用失败: {str(e)}")
            return {"action_type": "unified_analysis_evaluation_result"}, {}

    def _parse_unified_llm_result(
        self,
        llm_result: Dict[str, Any],
        tool_result: Dict[str, Any],
        context: Any = None
    ) -> UnifiedAnalysisResult:
        """解析统一的LLM结果 - 适配简化的输出格式"""
        try:
            # 🔥 解析洞察分析结果（适配简化格式）
            insights = []
            insights_data = llm_result.get("insights", [])
            if insights_data:
                for insight_data in insights_data:
                    insight = AnalysisInsight(
                        insight_type=insight_data.get("type", "summary"),
                        title=insight_data.get("title", ""),
                        description=insight_data.get("description", ""),
                        confidence=insight_data.get("confidence", 0.8),
                        supporting_data=insight_data.get("supporting_data", "")
                    )
                    insights.append(insight)

            # 🔥 从工具结果推断基本统计信息
            data_rows = 0
            data_columns = []

            # 尝试从不同的字段获取数据信息
            data_source = None
            if isinstance(tool_result.get('full_data'), list) and tool_result['full_data']:
                data_source = tool_result['full_data']
                data_rows = len(data_source)
            elif isinstance(tool_result.get('data'), list) and tool_result['data']:
                data_source = tool_result['data']
                data_rows = len(data_source)
            elif tool_result.get('row_count'):
                # 如果有row_count字段，使用它
                try:
                    data_rows = int(tool_result['row_count'])
                except (ValueError, TypeError):
                    data_rows = 0

            # 获取列信息
            if data_source and data_rows > 0 and isinstance(data_source[0], dict):
                data_columns = list(data_source[0].keys())

            # 🔥 构建基本的统计摘要
            basic_stats = StatisticalSummary(
                total_rows=data_rows,
                columns=data_columns,
                numeric_columns=[],
                categorical_columns=[],
                missing_values={},
                basic_stats={},
                value_distributions={}
            )

            # 🔥 构建基本的数据质量评估
            basic_quality = DataQualityAssessment(
                completeness_score=0.8,
                consistency_score=0.8,
                validity_score=0.8,
                overall_score=0.8,
                quality_issues=[]
            )

            # 🔥 智能调整completion_confidence（防止过度分析）
            raw_confidence = llm_result.get("completion_confidence", 0.5)
            planning_rounds = getattr(context, 'planning_rounds', 0)

            # 🔥 智能调整should_continue（防止无限循环）
            raw_should_continue = llm_result.get("should_continue", True)

            # 🔥 构建统一结果（基于简化的LLM输出）
            unified_result = UnifiedAnalysisResult(
                # 洞察分析结果
                statistical_summary=basic_stats,
                data_quality_assessment=basic_quality,
                insights=insights,
                patterns=[],  # 简化格式中合并到insights
                anomalies=[],  # 简化格式中合并到insights
                correlations=[],

                # 智能评估结果
                should_continue=raw_should_continue,
                completion_confidence=raw_confidence,
                missing_aspects=[],  # 简化格式中移除
                reasoning=llm_result.get("reasoning", ""),
                suggested_next_steps=llm_result.get("next_steps", []),
                evaluation_conclusion=llm_result.get("reasoning", ""),  # 使用reasoning作为结论

                # 统一分析评估摘要
                unified_summary=llm_result.get("reasoning", "统一分析评估完成"),
                next_analysis_stage=llm_result.get("stage", "data_collection"),
                specific_guidance="; ".join(llm_result.get("next_steps", [])),  # 将next_steps转为指导
                avoid_duplicate_queries=[]
            )

            return unified_result

        except Exception as e:
            log.error(f"解析LLM结果失败: {str(e)}")
            return None

    def _validate_llm_result(self, llm_result: Dict[str, Any]) -> bool:
        """验证LLM结果的格式正确性"""
        try:
            # 检查必需字段
            required_fields = ["action_type", "should_continue", "completion_confidence"]
            for field in required_fields:
                if field not in llm_result:
                    log.warning(f"LLM结果缺少必需字段: {field}")
                    return False

            # 检查action_type
            if llm_result.get("action_type") != "unified_analysis_evaluation_result":
                log.warning(f"LLM结果action_type错误: {llm_result.get('action_type')}")
                return False

            # 检查completion_confidence范围
            confidence = llm_result.get("completion_confidence", 0)
            if not isinstance(confidence, (int, float)) or not (0 <= confidence <= 1):
                log.warning(f"completion_confidence值无效: {confidence}")
                return False

            # 检查should_continue类型
            should_continue = llm_result.get("should_continue")
            if not isinstance(should_continue, bool):
                log.warning(f"should_continue类型错误: {type(should_continue)}")
                return False

            return True

        except Exception as e:
            log.error(f"验证LLM结果时出错: {str(e)}")
            return False
