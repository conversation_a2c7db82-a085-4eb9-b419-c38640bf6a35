from typing import Dict, Any, List, Optional
import logging
import time
import asyncio
from datetime import datetime
from app.utils.time_utils import get_shanghai_time
import uuid
import json

from sqlalchemy.orm import Session
from fastapi import Depends

from app.db.session import get_db
from app.models.tool import Tool, ToolType
from app.models.data_source import DataSource
from app.models.selected_table import SelectedTable
from app.models.project import Project
from app.models.analysis import Analysis, ToolExecution
from app.services.executor.factory import ExecutorFactory
from app.services.llm_factory import get_llm_service
from app.services.system_tools import SystemTools
from app.services.result_formatter import ResultFormatter
from app.services.report_generator import ReportGenerator
from app.services.chart_report_integrator import ChartReportIntegrator
from app.utils.json_utils import DecimalEncoder

class AnalyzerService:
    """分析服务"""

    def __init__(self, db: Session = None, llm_service = None):
        """
        初始化分析服务

        Args:
            db: 数据库会话
            llm_service: LLM服务
        """
        self.schema_description_file = "schema_description.md"
        self.logger = logging.getLogger("app.services.analyzer")
        self.db = db
        # 使用LLM工厂获取服务，优先使用传入的llm_service
        self.llm_service = llm_service or get_llm_service(db)
        # 初始化报告生成器
        self.report_generator = ReportGenerator(self.llm_service)
        # 初始化图表报告集成器 (传递db参数以支持MoE)
        self.chart_report_integrator = ChartReportIntegrator(self.llm_service, db)
        # 添加内存缓存，用于存储系统工具的执行结果
        self.system_tool_results = {}

    def _format_schema_for_prompt(self, project_id: str = None) -> str:
        """
        根据项目ID动态获取表结构信息
        
        Args:
            project_id: 项目ID，如果提供则获取该项目的表结构，否则尝试从文件读取
            
        Returns:
            str: 格式化的数据库Schema描述文本
        """
        # 如果没有提供项目ID，则尝试从文件读取（向后兼容）
        if not project_id:
            try:
                # 读取schema描述文件
                self.logger.info(f"\n=== 开始读取Schema描述文件: {self.schema_description_file} ===")
                
                with open(self.schema_description_file, 'r', encoding='utf-8') as f:
                    schema_content = f.read()
                
                self.logger.info("\n=== Schema描述文件内容 ===")
                self.logger.info(schema_content)
                
                return schema_content
                
            except Exception as e:
                error_msg = f"读取Schema描述文件失败: {str(e)}"
                self.logger.error(error_msg)
                return self._get_default_schema_description()
        
        # 如果提供了项目ID，则从数据库获取表结构
        try:
            # 获取项目关联的数据源
            data_source = self.db.query(DataSource).filter(DataSource.project_id == project_id).first()
            if not data_source:
                self.logger.error(f"项目 {project_id} 未找到关联的数据源")
                return self._get_default_schema_description()
            
            # 获取数据源ID
            data_source_id = data_source.id
            
            # 获取所有选定的表
            selected_tables = self.db.query(SelectedTable).filter(
                SelectedTable.data_source_id == data_source_id
            ).all()
            
            if not selected_tables or len(selected_tables) == 0:
                self.logger.warning(f"数据源 {data_source_id} 未找到选定的表")
                return self._get_default_schema_description()
            
            # 构建Schema描述
            schema_parts = []
            
            # 添加数据源业务描述
            if data_source and data_source.description:
                schema_parts.append("# 业务背景")
                schema_parts.append(data_source.description)
                schema_parts.append("")  # 空行分隔
            
            schema_parts.append("# 数据库Schema描述\n")
            
            for table in selected_tables:
                table_name = table.table_name
                table_description = table.table_description or ""
                table_schema = table.table_schema or {}
                sample_data = table.sample_data or []
                
                # 表描述
                schema_parts.append(f"\n## `{table_name}` 表")
                if table_description:
                    schema_parts.append(f"{table_description}\n")
                
                # 表结构
                for column_name, column_info in table_schema.items():
                    data_type = column_info.get('DATA_TYPE', '未知类型')
                    nullable = column_info.get('nullable', 'YES')
                    nullable_text = "可为空" if nullable == "YES" else "非空"
                    description = column_info.get('description', '')
                    
                    column_desc = f"- {column_name}: {data_type}, {nullable_text}"
                    if description:
                        column_desc += f", {description}"
                    
                    # 新增：如果有外键信息，添加到描述中
                    foreign_key = column_info.get('foreign_key')
                    if foreign_key:
                        ref_table = foreign_key.get('referenced_table')
                        ref_column = foreign_key.get('referenced_column')
                        if ref_table and ref_column:
                            column_desc += f", 外键 -> {ref_table}.{ref_column}"
                    
                    schema_parts.append(column_desc)
                
                # 添加示例数据
                if sample_data and len(sample_data) > 0:
                    schema_parts.append("\n### 示例数据")
                    
                    # 确保sample_data是列表
                    if not isinstance(sample_data, list):
                        if isinstance(sample_data, dict):
                            sample_data = [sample_data]
                        else:
                            try:
                                sample_data = json.loads(sample_data)
                                if not isinstance(sample_data, list):
                                    sample_data = [sample_data]
                            except:
                                sample_data = []
                    
                    # 限制示例数据条数，避免提示过长
                    max_samples = 3
                    if len(sample_data) > max_samples:
                        sample_data = sample_data[:max_samples]
                    
                    # 添加示例数据行
                    for i, row in enumerate(sample_data):
                        schema_parts.append(f"行 {i+1}: {json.dumps(row, ensure_ascii=False, cls=DecimalEncoder)}")
            
            schema_description = "\n".join(schema_parts)
            self.logger.info(f"已生成数据库Schema描述，长度: {len(schema_description)}")
            
            return schema_description
            
        except Exception as e:
            self.logger.exception(f"生成Schema描述时发生错误: {str(e)}")
            return self._get_default_schema_description()
    
    def _get_default_schema_description(self) -> str:
        """返回默认的Schema描述
        
        当无法获取真实表结构时使用的默认描述
        
        Returns:
            str: 默认的Schema描述文本
        """
        return """
        # 数据库Schema描述
        
        ## 用户表 (users)
        - id: 主键，用户ID
        - name: 用户名
        - email: 用户电子邮件
        - created_at: 创建时间
        
        ## 项目表 (projects)
        - id: 主键，项目ID
        - name: 项目名称
        - description: 项目描述
        - owner_id: 外键，关联到users表的id字段
        - config: JSON格式的项目配置
        - created_at: 创建时间
        
        ## 分析记录表 (analysis)
        - id: 主键，分析记录ID
        - query: 查询内容
        - project_id: 外键，关联到projects表的id字段
        - intent_analysis: JSON格式的意图分析结果
        - result: JSON格式的分析结果
        - created_at: 创建时间
        """

    def _format_tools_for_prompt(self, tools: List[Dict[str, Any]]) -> str:
        """格式化工具信息用于提示"""
        tool_info = []
        for tool in tools:
            tool_info.append(f"\n工具名称：{tool['name']}")
            tool_info.append(f"说明：{tool['description']}")
            if tool.get("parameters"):
                tool_info.append("参数：")
                for param in tool["parameters"]:
                    tool_info.append(f"- {param['name']}: {param['description']}")
        return "\n".join(tool_info)

    def _build_intent_prompt(self, query: str, tools: List[Dict[str, Any]], project_id: str = None) -> str:
        """构建意图分析提示"""
        return f"""请分析以下用户查询，并生成分析计划。

用户查询：{query}

可用的数据表：
{self._format_schema_for_prompt(project_id)}

可用的分析工具：
{self._format_tools_for_prompt(tools)}

你的任务是生成一个JSON格式的分析计划。必须严格按照以下格式：

{{
    "problem_understanding": "简短的一句话描述问题，不要包含分析过程",
    "required_data": ["所需数据类型1", "所需数据类型2"],
    "intent_complexity": "simple" 或 "complex",
    "analysis_steps": [
        {{
            "step_id": "1",
            "purpose": "这个步骤的目的",
            "tool_name": "使用的工具名称",
            "parameters": {{
                "参数名1": "参数值1",
                "参数名2": "参数值2"
            }}
        }}
    ]
}}

严格要求：
1. 必须是合法的JSON格式
2. 不要添加任何额外的解释文字
3. 不要在JSON外添加任何内容
4. 确保所有字段名称完全匹配示例
5. 使用双引号而不是单引号
6. 每个字段都必须存在
7. 参数必须与提供的工具要求的参数匹配
8. 参数值应该是从查询中提取的具体值，不要包含无关的语气词或修饰词
9. intent_complexity必须为"simple"或"complex"：
   - 简单意图（simple）：通常对应单一、高效的SQL查询，只查询单个或少量数据，无需额外解释。
   - 复杂意图（complex）：需要执行多步分析、涉及报告生成，或无法通过单一高效SQL完成的查询。
## 重要原则：

1. **专注意图理解**：重点分析用户想要什么，而不是如何实现
2. **避免技术细节**：不要在意图分析中涉及具体的SQL语句或技术实现
3. **业务导向思考**：从业务角度理解用户需求
4. **数据关联性**：基于表结构判断哪些数据与用户意图相关

## 示例：

查询："上个月销售情况怎么样？"

正确的意图理解：
{{
    "user_intent": "用户想了解上个月的销售业绩表现，可能关心销售额、订单量、增长趋势等关键指标",
    "business_context": "业务管理者或销售人员想要了解近期销售表现，用于业绩评估或决策参考",
    "key_information_needs": ["销售总额", "订单数量", "与前期对比", "主要产品表现"],
    "data_relevance": "销售订单表、产品表、客户表等包含销售相关的核心数据",
    "intent_complexity": "complex",
    "analysis_steps": [
        {{
            "step_id": "1",
            "purpose": "获取上个月销售数据汇总",
            "tool_name": "自动SQL查询",
            "parameters": {{
                "intent": {{
                    "query_type": "汇总分析",
                    "description": "查询上个月销售总额和订单量",
                    "time_range": "上个月"
                }}
            }}
        }}
    ]
}}

请严格按照上述格式返回JSON，专注于理解用户意图而不是执行细节。"""
    
    def _get_default_analysis_plan(self, query: str) -> Dict[str, Any]:
        """生成默认的分析计划"""
        # 提取公司名称，去除问句中的语气词
        company_name = query.split("是")[0] if "是" in query else query
        company_name = company_name.split("的")[0] if "的" in company_name else company_name
        
        # 基于查询关键词生成简单计划
        plan = {
            "problem_understanding": f"分析查询: {query}",
            "required_data": ["企业信息"],
            "analysis_steps": [
                {
                    "step_id": "1",
                    "purpose": "基础信息查询",
                    "tool_name": "auto_sql_query",
                    "parameters": {
                        "intent": {
                            "type": "企业基本信息",
                            "company_name": company_name
                        }
                    }
                }
            ]
        }
        
        return plan
    
    async def _execute_tools(self, analysis: Analysis, intent_analysis: Dict[str, Any], tools: List[Tool]) -> Dict[str, Any]:
        """
        执行工具
        
        Args:
            analysis: 分析记录
            intent_analysis: 意图分析结果
            tools: 可用工具列表
            
        Returns:
            执行结果
        """
        self.logger.info(f"开始执行工具: {analysis.id}")
        
        # 创建工具名称到工具对象的映射
        tool_map = {tool.name: tool for tool in tools}
        
        # 执行结果
        execution_results = {}
        
        for step in intent_analysis.get("analysis_steps", []):
            step_id = step.get("step_id")
            tool_name = step.get("tool_name")
            parameters = step.get("parameters", {})
            
            self.logger.info(f"执行步骤 {step_id}: {tool_name}")
            
            # 获取工具
            tool = tool_map.get(tool_name)
            if not tool:
                self.logger.warning(f"未找到工具: {tool_name}")
                execution_results[step_id] = {
                    "success": False,
                    "error": f"未找到工具: {tool_name}",
                    "data": None
                }
                continue
            
            # 检查是否为系统内置工具
            is_system_tool = tool.data_source_id is None
            
            try:
                # 根据工具类型执行
                if is_system_tool:
                    # 系统内置工具直接使用执行器
                    executor = ExecutorFactory.get_executor(tool.tool_type)
                    
                    # 为系统工具添加project_id参数
                    if "自动SQL" in tool.name or tool.tool_type == ToolType.AUTO_SQL:
                        self.logger.info(f"为自动SQL工具添加project_id参数: {analysis.project_id}")
                        parameters["project_id"] = analysis.project_id
                    
                    # 记录完整参数
                    self.logger.info(f"执行系统工具最终参数: {parameters}")
                    
                    # 执行工具
                    start_time = time.time()
                    result = await executor.execute(tool, parameters, self.db)
                    execution_time = time.time() - start_time
                    
                    # 根据工具类型确定展示格式
                    display_format = "json"  # 默认JSON格式
                    tool_type = str(tool.tool_type).upper() if tool.tool_type else None
                    
                    if tool_type:
                        if tool_type == "SQL":
                            display_format = "table"
                        elif tool_type == "GRAPH":
                            display_format = "graph"
                        elif tool_type == "VECTOR":
                            display_format = "text"
                        elif tool_type == "CHART":
                            display_format = "chart"
                    
                    # 使用ResultFormatter格式化结果
                    formatted_result = ResultFormatter.format_result(result, display_format)
                    
                    # 检查结果是否为字典类型，如果不是，转换为字典
                    if not isinstance(formatted_result, dict):
                        formatted_result = {
                            "success": True,
                            "data": formatted_result,
                            "display_format": display_format
                        }
                    else:
                        formatted_result["display_format"] = display_format
                    
                    execution_result = {
                        **formatted_result,
                        "execution_time": execution_time
                    }
                else:
                    # 创建工具执行记录
                    tool_execution = ToolExecution(
                        analysis_id=analysis.id,
                        tool_id=tool.id,
                        parameters=parameters,
                        step_id=step_id
                    )
                    
                    # 保存执行记录
                    self.db.add(tool_execution)
                    self.db.commit()
                    self.db.refresh(tool_execution)
                    
                    # 普通工具需要获取数据源
                    data_source = self.db.query(DataSource).filter(DataSource.id == tool.data_source_id).first()
                    if not data_source:
                        raise ValueError(f"未找到数据源: {tool.data_source_id}")
                    
                    # 获取执行器
                    executor = ExecutorFactory.get_executor(tool.tool_type)
                    
                    # 构建工具配置
                    tool_config = {
                        "type": data_source.type,
                        "config": data_source.config
                    }
                    
                    # 执行工具
                    start_time = time.time()
                    result = await executor.execute(tool.template, parameters, tool_config)
                    execution_time = time.time() - start_time
                    
                    # 根据工具类型确定展示格式
                    display_format = "json"  # 默认JSON格式
                    tool_type = str(tool.tool_type).upper() if tool.tool_type else None
                    
                    if tool_type:
                        if tool_type == "SQL":
                            display_format = "table"
                        elif tool_type == "GRAPH":
                            display_format = "graph"
                        elif tool_type == "VECTOR":
                            display_format = "text"
                        elif tool_type == "CHART":
                            display_format = "chart"
                    
                    # 使用ResultFormatter格式化结果
                    formatted_result = ResultFormatter.format_result(result, display_format)
                    
                    # 检查结果是否为字典类型，如果不是，转换为字典
                    if not isinstance(formatted_result, dict):
                        formatted_result = {
                            "success": True,
                            "data": formatted_result,
                            "display_format": display_format
                        }
                    else:
                        formatted_result["display_format"] = display_format
                    
                    # 更新执行记录
                    tool_execution.result = formatted_result
                    tool_execution.execution_time = execution_time
                    self.db.commit()
                    
                    execution_result = formatted_result
                
                # 添加到执行结果
                execution_results[step_id] = execution_result
                
            except Exception as e:
                self.logger.exception(f"工具执行失败: {str(e)}")
                
                # 更新执行记录（如果不是系统工具）
                if not is_system_tool and 'tool_execution' in locals():
                    tool_execution.result = {
                        "success": False,
                        "error": str(e),
                        "data": None
                    }
                    self.db.commit()
                
                # 添加到执行结果
                execution_results[step_id] = {
                    "success": False,
                    "error": str(e),
                    "data": None
                }
        
        return execution_results 

    async def get_available_tools(self, project_id: str, user_language: str = 'zh-CN') -> List[Tool]:
        """
        获取项目可用的工具列表，包括项目工具和系统内置工具
        
        Args:
            project_id: 项目ID
            user_language: 用户语言偏好，用于本地化工具名称
            
        Returns:
            工具列表
        """
        self.logger.info(f"获取项目 {project_id} 可用工具")
        
        # 获取项目下的所有工具
        tools = (
            self.db.query(Tool)
            .join(DataSource, DataSource.id == Tool.data_source_id)
            .filter(DataSource.project_id == project_id)
            .all()
        )
        
        # 添加系统内置工具（支持多语言）
        system_tools = []
        for tool_data in SystemTools.get_system_tools(user_language):
            system_tool = SystemTools.create_system_tool_object(tool_data)
            system_tools.append(system_tool)
        
        # 合并工具列表
        all_tools = tools + system_tools
        self.logger.info(f"获取到 {len(all_tools)} 个工具 (项目工具: {len(tools)}, 系统工具: {len(system_tools)})")
        
        return all_tools
    
    async def analyze_intent_stream(self, user_query: str, available_tools: List[Tool], project_id: str = None) -> Dict[str, Any]:
        """
        分析用户意图（流式）
        
        Args:
            user_query: 用户查询
            available_tools: 可用工具列表
            project_id: 项目ID，用于获取表结构
            
        Returns:
            意图分析结果
        """
        self.logger.info(f"流式分析用户意图: {user_query}")
        
        # 转换工具列表为LLM可用的格式
        tools_for_llm = []
        for tool in available_tools:
            tools_for_llm.append({
                "name": tool.name,
                "description": tool.description,
                "type": tool.tool_type.value if hasattr(tool.tool_type, 'value') else str(tool.tool_type),
                "parameters": tool.parameters
            })
        
        # 添加project_id参数到意图提示构建函数
        intent_prompt = self._build_intent_prompt(user_query, tools_for_llm, project_id)
        # 使用LLM分析用户意图
        intent_analysis = await self.llm_service.analyze_intent(intent_prompt)
        
        # 确保意图分析结果包含intent_complexity字段
        if "intent_complexity" not in intent_analysis:
            # 如果LLM没有返回意图复杂度，默认设置为complex
            intent_analysis["intent_complexity"] = "complex"
        
        self.logger.info(f"意图分析完成，共 {len(intent_analysis.get('analysis_steps', []))} 个步骤")
        return intent_analysis
    
    async def execute_tool_step(self, analysis: Analysis, step: Dict[str, Any], tools: List[Tool], user_language: str = 'zh-CN') -> Dict[str, Any]:
        """
        执行单个工具步骤（流式）
        
        Args:
            analysis: 分析记录
            step: 分析步骤
            tools: 工具列表
            user_language: 用户语言偏好 ('zh-CN' 或 'en-US')
            
        Returns:
            执行结果
        """
        from app.services.result_formatter import ResultFormatter
        
        step_id = step.get("step_id")
        tool_name = step.get("tool_name")
        parameters = step.get("parameters", {}).copy()  # 创建参数的副本，避免修改原始数据
        
        self.logger.info(f"流式执行步骤 {step_id}: {tool_name}")
        self.logger.info(f"步骤 {step_id} 初始参数: {parameters}")
        
        # 尝试获取分析日志记录器
        analysis_logger = None
        try:
            from app.core.logger import AnalysisLogger
            # 从analysis对象获取project_id
            project_id = analysis.project_id if analysis else None
            if project_id:
                analysis_logger = AnalysisLogger(project_id=project_id, request_id=f"tool_step_{step_id}")
                analysis_logger.info(f"开始执行工具步骤: {tool_name}", extra={
                    "event": "tool_step_start",
                    "step_id": step_id,
                    "tool_name": tool_name,
                    "initial_parameters": parameters,
                    "analysis_id": analysis.id if analysis else None
                })
        except Exception as e:
            self.logger.warning(f"无法创建分析日志记录器: {str(e)}")
        
        # 检查是否需要使用前序步骤的结果
        # 支持新的多依赖格式和旧的单依赖格式
        source_steps = step.get("source_steps", [])  # 新格式：多个依赖步骤
        dependencies_mapping = step.get("dependencies_mapping", {})  # 新格式：依赖映射 {"step_id": ["field1", "field2"]}

        # 如果指定了源步骤和依赖映射，则从多个步骤提取参数
        if source_steps and dependencies_mapping:
            self.logger.info(f"步骤 {step_id} 依赖以下步骤: {source_steps}")
            self.logger.info(f"依赖映射: {dependencies_mapping}")
            
            # 记录提取的参数，用于后续替换嵌套结构中的值
            extracted_params_all = {}
            
            # 查询所有前序步骤的执行结果（优先从内存缓存中获取）
            prev_results = {}
            for source_step in source_steps:
                # 首先尝试从内存缓存中获取系统工具的执行结果
                if source_step in self.system_tool_results.get(analysis.id, {}):
                    self.logger.info(f"从内存缓存中获取步骤 {source_step} 的执行结果")
                    prev_results[source_step] = self.system_tool_results[analysis.id][source_step]
                else:
                    # 如果内存缓存中没有，尝试从数据库中查询
                    self.logger.info(f"从数据库中查询步骤 {source_step} 的执行结果")
                    prev_execution = (
                        self.db.query(ToolExecution)
                        .filter(
                            ToolExecution.analysis_id == analysis.id,
                            ToolExecution.step_id == source_step
                        )
                        .first()
                    )
                    
                    if prev_execution and prev_execution.result:
                        prev_results[source_step] = prev_execution.result
                    else:
                        self.logger.warning(f"未找到步骤 {source_step} 的执行结果，将尝试继续执行")
            
            # 处理每个依赖步骤
            for source_step in source_steps:
                # 获取当前依赖步骤所需的字段
                required_fields = dependencies_mapping.get(source_step, [])
                if not required_fields:
                    self.logger.warning(f"步骤 {source_step} 没有指定所需字段，跳过")
                    continue
                
                # 获取前序步骤的执行结果
                prev_result = prev_results.get(source_step)
                if not prev_result:
                    self.logger.warning(f"无法获取步骤 {source_step} 的执行结果，跳过参数提取")
                    continue
                
                # 使用LLM服务提取参数
                try:
                    self.logger.info(f"从步骤 {source_step} 提取字段: {required_fields}")
                    self.logger.info(f"源结果: {json.dumps(prev_result, ensure_ascii=False, cls=DecimalEncoder)[:1000]}")
                    
                    # 手动处理结果数据结构中的列表数据，特别是用于IN查询的参数
                    # 这段逻辑直接处理结果集中的列表类型
                    extracted_params = {}
                    
                    # 如果有results字段且是列表，这通常是结果集数据
                    if "results" in prev_result and isinstance(prev_result["results"], list):
                        for field in required_fields:
                            # 从results中提取指定字段的所有值作为列表
                            if prev_result["results"]:
                                # 提取字段对应的所有值
                                extracted_values = [row.get(field) for row in prev_result["results"] if field in row]
                                if extracted_values:
                                    extracted_params[field] = extracted_values
                                    self.logger.info(f"从results中提取字段 {field} 的值列表: {extracted_values}")
                    
                    # 如果没有从results提取到值，再尝试从其他结构提取
                    if not extracted_params:
                        # 尝试从顶层结构或其他位置提取参数
                        # 调用LLM服务的complete_parameters方法
                        try:
                            extracted_params = await self.llm_service.complete_parameters(prev_result, required_fields)
                        except Exception as llm_error:
                            self.logger.warning(f"LLM提取参数失败: {str(llm_error)}，尝试直接提取")
                            # 如果LLM提取失败，尝试直接从数据结构中查找
                            for field in required_fields:
                                if field in prev_result:
                                    extracted_params[field] = prev_result[field]
                                    self.logger.info(f"直接从结果中提取字段 {field}: {prev_result[field]}")
                    
                    self.logger.info(f"从步骤 {source_step} 成功提取参数: {extracted_params}")
                    
                    # 将提取的参数添加到顶层参数中
                    parameters.update(extracted_params)
                    
                    # 记录所有提取的参数
                    extracted_params_all.update(extracted_params)
                except Exception as e:
                    self.logger.error(f"从步骤 {source_step} 提取参数失败: {str(e)}")
            
            # 递归更新嵌套结构中的参数值
            def update_nested_params(obj, extracted_params):
                if isinstance(obj, dict):
                    for key, value in list(obj.items()):
                        if isinstance(value, (dict, list)):
                            update_nested_params(value, extracted_params)
                        elif key == "value_from_step" and value in source_steps:
                            # 处理特定的值引用模式，例如{value_from_step: "1", value_column: "customer_id"}
                            if "value_column" in obj and obj["value_column"] in extracted_params:
                                column_name = obj["value_column"]
                                obj["value"] = extracted_params[column_name]
                                self.logger.info(f"更新参数引用: 将来自步骤 {value} 的 {column_name} 值设置为 {extracted_params[column_name]}")
                        elif isinstance(value, str) and ("步骤" in value or "来自" in value or "step" in value.lower()):
                            # 尝试找到匹配的提取参数来替换
                            param_name = key
                            if param_name in extracted_params:
                                obj[key] = extracted_params[param_name]
                                self.logger.info(f"更新嵌套参数: {key} 从 '{value}' 替换为 '{extracted_params[param_name]}'")
                elif isinstance(obj, list):
                    for item in obj:
                        update_nested_params(item, extracted_params)
            
                            # 特别处理intent中的filters部分，这是SQL查询的关键
                if "intent" in parameters and isinstance(parameters["intent"], dict) and "filters" in parameters["intent"]:
                    filters = parameters["intent"]["filters"]
                    self.logger.info(f"特别处理SQL过滤条件: {filters}")
                    
                    # 支持两种格式的filters：字典和列表
                    if isinstance(filters, dict):
                        # 处理字典格式的filters
                        for field, value in filters.items():
                            # 检查是否有参数占位符（例如"指定客户ID"）
                            if isinstance(value, str) and ("指定" in value or "从步骤" in value or "来自" in value):
                                # 查找对应的提取参数
                                if field in extracted_params_all:
                                    filters[field] = extracted_params_all[field]
                                    self.logger.info(f"更新SQL过滤条件: {field}={value} -> {extracted_params_all[field]}")
                    
                    elif isinstance(filters, list):
                        # 处理列表格式的filters
                        for filter_item in filters:
                            if isinstance(filter_item, dict):
                                # 针对带column和operator的过滤条件，通常用于SQL条件
                                if "column" in filter_item:
                                    column = filter_item.get("column")
                                    
                                    # 处理source_step和source_column这种依赖前序步骤的情况
                                    if "source_step" in filter_item and "source_column" in filter_item:
                                        source_step = filter_item.get("source_step")
                                        source_column = filter_item.get("source_column")
                                        
                                        self.logger.info(f"处理依赖步骤的过滤条件: column={column}, source_step={source_step}, source_column={source_column}")
                                        
                                        # 如果已经提取了对应的参数值
                                        if source_column in extracted_params_all:
                                            # 如果是列表，直接用于IN查询
                                            filter_item["value"] = extracted_params_all[source_column]
                                            self.logger.info(f"从步骤 {source_step} 提取字段 {source_column} 的值: {extracted_params_all[source_column]}")
                                
                                # 也处理字段名是field的情况
                                elif "field" in filter_item and "value" in filter_item:
                                    field = filter_item.get("field")
                                    value = filter_item.get("value")
                                    
                                    # 检查值是否为需要替换的占位符
                                    if isinstance(value, str) and ("指定" in value or "从步骤" in value or "来自" in value):
                                        # 字段名直接匹配
                                        if field in extracted_params_all:
                                            filter_item["value"] = extracted_params_all[field]
                                            self.logger.info(f"更新SQL过滤条件: field={field}, value={extracted_params_all[field]}")
                                        # 尝试匹配客户ID等通用字段
                                        elif "customer_id" in extracted_params_all and field.lower().endswith("_id"):
                                            filter_item["value"] = extracted_params_all["customer_id"]
                                            self.logger.info(f"更新SQL过滤条件: field={field}, value={extracted_params_all['customer_id']}")
                                
                                # 处理value_from_step和value_column格式
                                elif "value_from_step" in filter_item and "value_column" in filter_item:
                                    step_id = filter_item.get("value_from_step")
                                    column = filter_item.get("value_column")
                                    
                                    # 检查是否有对应的提取参数
                                    if column in extracted_params_all:
                                        filter_item["value"] = extracted_params_all[column]
                                        self.logger.info(f"更新SQL过滤条件: column={column}, value={extracted_params_all[column]}")
            
            # 更新嵌套参数
            update_nested_params(parameters, extracted_params_all)
            
            # 将提取的参数值也添加到SQL查询的参数中
            if "intent" in parameters and isinstance(parameters["intent"], dict) and extracted_params_all:
                # 对于自动SQL查询，确保参数直接可用于SQL执行
                parameters.update(extracted_params_all)
                self.logger.info(f"将所有提取的参数添加到顶层: {extracted_params_all}")
            
            # 输出最终合并后的参数
            self.logger.info(f"所有依赖步骤参数合并后: {parameters}")
        
        # 创建工具名称到工具对象的映射
        tool_map = {tool.name: tool for tool in tools}
        
        # 获取工具
        tool = tool_map.get(tool_name)
        if not tool:
            self.logger.warning(f"未找到工具: {tool_name}")
            return {
                "success": False,
                "error": f"未找到工具: {tool_name}",
                "data": None
            }
        
        # 检查是否为系统内置工具
        is_system_tool = tool.data_source_id is None
        
        try:
            # 根据工具类型执行
            if is_system_tool:
                # 系统内置工具直接使用执行器
                executor = ExecutorFactory.get_executor(tool.tool_type)
                
                # 记录到分析日志
                if analysis_logger:
                    analysis_logger.info(f"准备执行系统工具: {tool_name}", extra={
                        "event": "system_tool_execution_start",
                        "tool_id": tool.id,
                        "tool_type": str(tool.tool_type),
                        "executor_type": type(executor).__name__
                    })
                
                # 工具类型特定处理
                if "自动SQL" in tool.name or tool.tool_type == ToolType.AUTO_SQL:
                    # 为自动SQL工具添加project_id和user_language参数
                    self.logger.info(f"为自动SQL工具添加project_id参数: {analysis.project_id}")
                    parameters["project_id"] = analysis.project_id
                    parameters["user_language"] = user_language
                    self.logger.info(f"为自动SQL工具添加用户语言参数: {user_language}")
                    print(f"parameters+++: {parameters}")
                
                elif "信息澄清" in tool.name or "澄清" in tool.name or tool.tool_type == ToolType.CLARIFICATION:
                    # 为澄清工具添加上下文参数
                    self.logger.info(f"为澄清工具添加上下文参数: analysis_id={analysis.id}, project_id={analysis.project_id}")
                    parameters["analysis_id"] = str(analysis.id)
                    parameters["project_id"] = analysis.project_id
                    
                    # 记录澄清工具的详细参数
                    if analysis_logger:
                        analysis_logger.info("澄清工具上下文参数已添加", extra={
                            "event": "clarification_context_added",
                            "analysis_id": analysis.id,
                            "project_id": analysis.project_id,
                            "clarification_parameters": parameters
                        })
                
                elif "图表生成" in tool.name or tool.tool_type == ToolType.CHART_GENERATION:
                    # 为图表生成工具添加上下文参数
                    self.logger.info(f"为图表生成工具添加上下文参数: analysis_id={analysis.id}, project_id={analysis.project_id}")
                    parameters["analysis_id"] = str(analysis.id)
                    parameters["project_id"] = analysis.project_id
                    # 为图表生成工具提供数据库会话
                    parameters["db_session"] = self.db
                    
                    # 记录图表生成工具的详细参数
                    if analysis_logger:
                        analysis_logger.info("图表生成工具上下文参数已添加", extra={
                            "event": "chart_generation_context_added",
                            "analysis_id": analysis.id,
                            "project_id": analysis.project_id,
                            "chart_parameters": {k: v for k, v in parameters.items() if k != "db_session"}  # 不记录数据库会话对象
                        })
                
                # 用户回复处理（新增记录）
                if "user_response" in parameters and "智能交互" in tool_name:
                    user_response = parameters.get("user_response", "")
                    self.logger.info(f"正在处理用户回复: \"{user_response}\"")
                    
                    # 记录用户回复的详细信息到分析日志
                    if analysis_logger:
                        analysis_logger.info("处理用户澄清回复", extra={
                            "event": "user_response_processing",
                            "user_response": user_response,
                            "response_length": len(user_response),
                            "analysis_id": analysis.id,
                            "timestamp": get_shanghai_time().isoformat()
                        })
                
                # 记录完整参数
                self.logger.info(f"执行系统工具最终参数: {parameters}")
                
                # 记录到分析日志 - 工具执行前的最终参数
                if analysis_logger:
                    analysis_logger.info("工具执行前的最终参数", extra={
                        "event": "tool_execution_parameters",
                        "final_parameters": parameters,
                        "tool_name": tool_name,
                        "tool_type": str(tool.tool_type)
                    })
                
                # 执行工具
                start_time = time.time()
                result = await executor.execute(tool, parameters)
                execution_time = time.time() - start_time
                
                # 记录到分析日志 - 工具执行完成
                if analysis_logger:
                    analysis_logger.info(f"工具执行完成，耗时 {execution_time:.3f}秒", extra={
                        "event": "tool_execution_completed",
                        "execution_time": round(execution_time * 1000),  # 毫秒
                        "result_success": result.get("success", False) if isinstance(result, dict) else True,
                        "tool_name": tool_name
                    })
                    
                    # 记录工具执行的详细结果
                    analysis_logger.debug("工具执行详细结果", extra={
                        "event": "tool_execution_result_detail",
                        "result": result,
                        "tool_name": tool_name,
                        "execution_time": round(execution_time * 1000)
                    })
                
                # 根据工具类型确定展示格式
                display_format = "json"  # 默认JSON格式
                tool_type = str(tool.tool_type).upper() if tool.tool_type else None
                if tool_type:
                    if tool_type == "TOOLTYPE.SQL" or tool_type == "AUTO_SQL":
                        display_format = "table"
                    elif tool_type == "TOOLTYPE.GRAPH" or tool_type == "GRAPH":
                        display_format = "graph"
                    elif tool_type == "TOOLTYPE.VECTOR" or tool_type == "VECTOR":
                        display_format = "text"
                    elif tool_type == "TOOLTYPE.CHART" or tool_type == "CHART":
                        display_format = "chart"
                    elif tool_type == "CHART_GENERATION":
                        display_format = "chart"
                
                # 使用ResultFormatter格式化结果
                print(f"result+++: {result}")
                formatted_result = ResultFormatter.format_result(result, display_format)
                
                # 检查结果是否为字典类型，如果不是，转换为字典
                if not isinstance(formatted_result, dict):
                    formatted_result = {
                        "success": True,
                        "data": formatted_result,
                        "display_format": display_format
                    }
                else:
                    formatted_result["display_format"] = display_format
                
                execution_result = {
                    **formatted_result,
                    "execution_time": execution_time
                }
                print(f"execution_result+++: {execution_result}")
                
                # 记录到分析日志 - 格式化后的最终结果
                if analysis_logger:
                    analysis_logger.info("工具执行结果已格式化", extra={
                        "event": "tool_result_formatted",
                        "display_format": display_format,
                        "result_size": len(str(execution_result)),
                        "tool_name": tool_name
                    })
                
                # 将系统工具执行结果存储在内存缓存中，同时也保存到数据库
                try:
                    # 先将结果转换为JSON兼容格式，处理Decimal类型
                    serialized_result = json.loads(json.dumps(execution_result, cls=DecimalEncoder))
                    
                    # 确保分析ID的字典已初始化
                    if analysis.id not in self.system_tool_results:
                        self.system_tool_results[analysis.id] = {}
                    
                    # 存储执行结果到内存缓存
                    self.system_tool_results[analysis.id][step_id] = serialized_result
                    self.logger.info(f"系统工具执行结果已保存到内存缓存，分析ID: {analysis.id}, 步骤ID: {step_id}")
                    
                    # 将参数序列化为JSON兼容格式
                    serialized_params = json.loads(json.dumps(parameters, cls=DecimalEncoder))
                    
                    # 同时保存执行结果到数据库
                    tool_execution = ToolExecution(
                        analysis_id=analysis.id,
                        tool_id=tool.id,
                        parameters=serialized_params,
                        result=serialized_result,
                        execution_time=execution_time,
                        step_id=step_id
                    )
                    self.db.add(tool_execution)
                    self.db.commit()
                    self.db.refresh(tool_execution)
                    self.logger.info(f"系统工具执行结果已同时保存到数据库，分析ID: {analysis.id}, 步骤ID: {step_id}")
                    
                    # 记录到分析日志 - 数据保存完成
                    if analysis_logger:
                        analysis_logger.info("工具执行结果已保存", extra={
                            "event": "tool_result_saved",
                            "cache_saved": True,
                            "db_saved": True,
                            "step_id": step_id
                        })
                    
                except Exception as cache_error:
                    self.logger.error(f"保存系统工具执行结果失败: {str(cache_error)}")
                    if analysis_logger:
                        analysis_logger.error(f"保存工具执行结果失败: {str(cache_error)}", extra={
                            "event": "tool_result_save_failed",
                            "error": str(cache_error),
                            "step_id": step_id
                        })
            else:
                # 创建工具执行记录
                # 先将参数转换为JSON兼容格式
                serialized_params = json.loads(json.dumps(parameters, cls=DecimalEncoder))
                
                tool_execution = ToolExecution(
                    analysis_id=analysis.id,
                    tool_id=tool.id,
                    parameters=serialized_params,
                    step_id=step_id
                )
                
                # 保存执行记录
                self.db.add(tool_execution)
                self.db.commit()
                self.db.refresh(tool_execution)
                
                # 普通工具需要获取数据源
                data_source = self.db.query(DataSource).filter(DataSource.id == tool.data_source_id).first()
                if not data_source:
                    raise ValueError(f"未找到数据源: {tool.data_source_id}")
                
                # 获取执行器
                executor = ExecutorFactory.get_executor(tool.tool_type)
                
                # 构建工具配置
                tool_config = {
                    "type": data_source.type,
                    "config": data_source.config
                }
                
                # 执行工具
                start_time = time.time()
                result = await executor.execute(tool.template, parameters, tool_config)
                execution_time = time.time() - start_time
                
                # 根据工具类型确定展示格式
                display_format = "json"  # 默认JSON格式
                tool_type = str(tool.tool_type).upper() if tool.tool_type else None
                if tool_type:
                    if tool_type == "TOOLTYPE.SQL":
                        display_format = "table"
                    elif tool_type == "TOOLTYPE.GRAPH":
                        display_format = "graph"
                    elif tool_type == "TOOLTYPE.VECTOR":
                        display_format = "text"
                    elif tool_type == "chart":
                        display_format = "chart"
                
                # 使用ResultFormatter格式化结果
                formatted_result = ResultFormatter.format_result(result, display_format)
                
                # 检查结果是否为字典类型，如果不是，转换为字典
                if not isinstance(formatted_result, dict):
                    formatted_result = {
                        "success": True,
                        "data": formatted_result,
                        "display_format": display_format
                    }
                else:
                    formatted_result["display_format"] = display_format
                
                # 先将结果转换为JSON兼容格式
                serialized_result = json.loads(json.dumps(formatted_result, cls=DecimalEncoder))
                
                # 更新执行记录
                tool_execution.result = serialized_result
                tool_execution.execution_time = float(execution_time)  # 确保是普通的float类型
                self.db.commit()
                
                execution_result = formatted_result
                execution_result["execution_time"] = execution_time
            
            # 将源步骤和依赖映射加入结果，确保前端能获取到步骤依赖关系
            if source_steps and dependencies_mapping:
                execution_result["source_steps"] = source_steps
                execution_result["dependencies_mapping"] = dependencies_mapping
            
            # 返回结果
            return execution_result
                
        except Exception as e:
            self.logger.exception(f"工具执行失败: {str(e)}")
            
            # 回滚当前事务
            self.db.rollback()
            
            # 更新执行记录（如果不是系统工具且执行记录已创建）
            if not is_system_tool and 'tool_execution' in locals():
                try:
                    # 先将错误信息转换为JSON兼容格式
                    error_result = {
                        "success": False,
                        "error": str(e),
                        "data": None
                    }
                    serialized_error = json.loads(json.dumps(error_result, cls=DecimalEncoder))
                    
                    # 使用新的事务保存错误信息
                    tool_execution.result = serialized_error
                    self.db.add(tool_execution)
                    self.db.commit()
                except Exception as save_error:
                    self.logger.error(f"保存错误信息失败: {str(save_error)}")
                    self.db.rollback()
                
            # 返回错误结果
            return {
                "success": False,
                "error": str(e),
                "data": None
            }
    
    async def get_execution_results(self, analysis: Analysis) -> Dict[str, Any]:
        """
        获取所有执行结果
        
        Args:
            analysis: 分析记录
            
        Returns:
            所有执行结果
        """
        # 从数据库获取最新的执行记录
        tool_executions = (
            self.db.query(ToolExecution)
            .filter(ToolExecution.analysis_id == analysis.id)
            .all()
        )
        
        # 构建结果字典
        execution_results = {}
        for execution in tool_executions:
            if hasattr(execution, 'step_id') and execution.step_id:
                # 如果有步骤ID，按步骤ID组织
                execution_results[execution.step_id] = execution.result
            else:
                # 否则以自增序号组织
                execution_results[f"STEP{len(execution_results)+1}"] = execution.result
        
        return execution_results 
    
    async def generate_report_stream(self, user_query: str, intent_analysis: Dict[str, Any],
                          execution_results: List[Dict[str, Any]], project_id: str = None,
                          template_id: str = None) -> str:
        """
        生成分析报告（流式）

        Args:
            user_query: 用户查询
            intent_analysis: 意图分析结果
            execution_results: 执行结果
            project_id: 项目ID，用于获取自定义报告提示词
            template_id: 报告模板ID

        Returns:
            分析报告
        """
        self.logger.info(f"流式生成分析报告: {user_query}")

        # 获取项目自定义报告提示词
        custom_prompt = None
        if project_id and self.db:
            try:
                project = self.db.query(Project).filter(Project.id == project_id).first()
                if project and project.report_prompt:
                    custom_prompt = project.report_prompt
                    self.logger.info("使用项目自定义报告提示词")
                else:
                    self.logger.info("使用默认报告提示词")
            except Exception as e:
                self.logger.warning(f"获取项目自定义提示词失败: {str(e)}")

        # 使用报告生成器生成报告
        report = await self.report_generator.generate_report(
            user_query, intent_analysis, execution_results, custom_prompt, template_id
        )

        return report

    async def generate_intelligent_report(self, user_query: str, intent_analysis: Dict[str, Any],
                                        execution_results: List[Dict[str, Any]], project_id: str = None) -> Dict[str, Any]:
        """
        生成智能分析报告

        Args:
            user_query: 用户查询
            intent_analysis: 意图分析结果
            execution_results: 执行结果
            project_id: 项目ID，用于获取自定义报告提示词

        Returns:
            智能报告生成结果
        """
        self.logger.info(f"智能生成分析报告: {user_query}")

        # 获取项目自定义报告提示词
        custom_prompt = None
        if project_id and self.db:
            try:
                project = self.db.query(Project).filter(Project.id == project_id).first()
                if project and project.report_prompt:
                    custom_prompt = project.report_prompt
                    self.logger.info("使用项目自定义报告提示词")
                else:
                    self.logger.info("使用智能模板推荐")
            except Exception as e:
                self.logger.warning(f"获取项目自定义提示词失败: {str(e)}")

        # 使用报告生成器生成报告
        report_content = await self.report_generator.generate_report(
            user_query, intent_analysis, execution_results, custom_prompt, None, []
        )

        # 返回与原来格式兼容的结果
        return {
            "report_content": report_content,
            "generated_charts": [],
            "generation_metadata": {
                "generated_at": datetime.now().isoformat(),
                "custom_prompt_used": bool(custom_prompt),
                "charts_included": 0
            }
        }

    async def generate_integrated_chart_report(self, user_query: str, intent_analysis: Dict[str, Any],
                                             execution_results: List[Dict[str, Any]], project_id: str = None) -> Dict[str, Any]:
        """
        生成集成图表的智能分析报告

        Args:
            user_query: 用户查询
            intent_analysis: 意图分析结果
            execution_results: 执行结果
            project_id: 项目ID，用于获取自定义报告提示词

        Returns:
            集成图表的智能报告结果
        """
        self.logger.info(f"生成集成图表的智能报告: {user_query}")

        # 获取项目自定义报告提示词
        custom_prompt = None
        if project_id and self.db:
            try:
                project = self.db.query(Project).filter(Project.id == project_id).first()
                if project and project.report_prompt:
                    custom_prompt = project.report_prompt
                    self.logger.info("使用项目自定义报告提示词")
                else:
                    self.logger.info("使用智能模板推荐和图表集成")
            except Exception as e:
                self.logger.warning(f"获取项目自定义提示词失败: {str(e)}")

        # 使用图表报告集成器生成报告
        result = await self.chart_report_integrator.generate_integrated_report(
            user_query, intent_analysis, execution_results, custom_prompt
        )

        return result