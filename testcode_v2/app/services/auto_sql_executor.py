import logging
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session

from app.models.data_source import DataSource
from app.models.selected_table import SelectedTable

logger = logging.getLogger(__name__)

class AutoSQLExecutor:
    """自动SQL执行器
    
    负责构建SQL生成的提示词，并执行生成的SQL查询
    """
    
    def __init__(self, db: Session, project_id: str):
        """初始化自动SQL执行器
        
        Args:
            db: 数据库会话
            project_id: 项目ID
        """
        self.db = db
        self.project_id = project_id
        self.schema_info = None
        self.selected_tables_info = None
        
    def load_schema_info(self) -> bool:
        """加载数据库schema信息
        
        Returns:
            bool: 是否成功加载schema信息
        """
        try:
            # 加载该项目下所有数据源的schema信息
            data_sources = self.db.query(DataSource).filter(
                DataSource.project_id == self.project_id
            ).all()
            
            if not data_sources:
                logger.warning(f"项目 {self.project_id} 没有可用的数据源")
                return False
            
            # 构建schema信息
            self.schema_info = {}
            for ds in data_sources:
                self.schema_info[ds.id] = {
                    "id": ds.id,
                    "name": ds.name,
                    "type": ds.type,
                    "config": ds.config
                }
            
            # 加载用户选择的表信息
            self.load_selected_tables_info()
            
            return True
        except Exception as e:
            logger.error(f"加载schema信息失败: {str(e)}")
            return False
    
    def load_selected_tables_info(self) -> bool:
        """加载用户选择的表信息
        
        Returns:
            bool: 是否成功加载表信息
        """
        try:
            self.selected_tables_info = {}
            
            # 获取所有数据源ID
            data_source_ids = list(self.schema_info.keys()) if self.schema_info else []
            
            if not data_source_ids:
                logger.warning(f"项目 {self.project_id} 没有可用的数据源")
                return False
            
            # 查询所有选定的表
            selected_tables = self.db.query(SelectedTable).filter(
                SelectedTable.data_source_id.in_(data_source_ids)
            ).all()
            
            if not selected_tables:
                logger.warning(f"项目 {self.project_id} 没有选定的表")
                return False
            
            # 整理表信息
            for table in selected_tables:
                if table.data_source_id not in self.selected_tables_info:
                    self.selected_tables_info[table.data_source_id] = []
                
                self.selected_tables_info[table.data_source_id].append({
                    "id": table.id,
                    "table_name": table.table_name,
                    "table_description": table.table_description,
                    "schema": table.table_schema,
                    "sample_data": table.sample_data
                })
            
            return True
        except Exception as e:
            logger.error(f"加载选定表信息失败: {str(e)}")
            return False
    
    def build_prompt_for_sql_generation(self, query: str) -> str:
        """构建SQL生成的提示词
        
        Args:
            query: 用户查询
            
        Returns:
            str: 构建好的提示词
        """
        prompt = f"""请根据用户的查询："{query}"，生成适当的SQL查询语句。

可用的数据库表信息如下：
"""
        
        if not self.selected_tables_info:
            prompt += "警告：没有选定的表信息可用。请确保已经选择了需要分析的表。\n"
            return prompt
        
        # 添加所有数据源和表的信息
        for ds_id, tables in self.selected_tables_info.items():
            ds_info = self.schema_info.get(ds_id, {})
            ds_name = ds_info.get("name", "未知数据源")
            ds_type = ds_info.get("type", "未知类型")
            
            prompt += f"\n数据源：{ds_name} (类型: {ds_type})\n"
            
            for table in tables:
                table_name = table["table_name"]
                table_desc = table["table_description"] or "无描述"
                
                prompt += f"\n表名: {table_name}\n"
                prompt += f"描述: {table_desc}\n"
                
                # 添加表结构
                prompt += "列信息:\n"
                for column in table["schema"]:
                    col_name = column.get("column_name", column.get("COLUMN_NAME", "未知列名"))
                    data_type = column.get("data_type", column.get("DATA_TYPE", "未知类型"))
                    description = column.get("description", "")
                    
                    prompt += f"- {col_name} ({data_type})"
                    if description:
                        prompt += f" - {description}"
                    prompt += "\n"
                
                # 添加样本数据
                sample_data = table["sample_data"]
                if sample_data and len(sample_data) > 0:
                    prompt += "\n样本数据(最多3行):\n"
                    for i, row in enumerate(sample_data[:3]):
                        prompt += f"行 {i+1}: {str(row)}\n"
                else:
                    prompt += "\n无样本数据\n"
        
        # 添加指导性建议
        prompt += "\n根据上述信息，请生成能够回答用户查询的SQL语句。考虑使用适当的表、列和关联查询。如果可能，请解释SQL的思路。"
        
        return prompt
    
    def execute_sql(self, sql: str, params: Dict[str, Any] = None, datasource_id: Optional[str] = None) -> Dict[str, Any]:
        """执行SQL查询
        
        Args:
            sql: SQL查询语句
            params: 查询参数
            datasource_id: 数据源ID，如果为None则使用第一个可用的数据源
            
        Returns:
            Dict: 执行结果
        """
        # 实现SQL执行逻辑
        # 这里需要根据实际情况实现
        pass 