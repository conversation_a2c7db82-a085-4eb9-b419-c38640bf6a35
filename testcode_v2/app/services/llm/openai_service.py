from typing import Dict, Any, List, Optional
import json
import logging
from openai import Async<PERSON>penAI
import httpx

from app.core.config import settings
from app.services.llm import LLMService
from app.utils.json_utils import DecimalEncoder

class OpenAIService(LLMService):
    """OpenAI LLM服务"""
    
    def __init__(self, api_key: str = None, base_url: str = None, model: str = None, model_id: str = None):
        """
        初始化OpenAI LLM服务
        
        Args:
            api_key: OpenAI API密钥
            base_url: OpenAI API基础URL
            model: 使用的模型
            model_id: 数据库中的模型ID，用于Token用量统计
        """
        super().__init__(model_id=model_id)
        self.logger = logging.getLogger("app.services.llm.openai")
        
        # 使用配置的API密钥和模型
        self.api_key = api_key or settings.OPENAI_API_KEY
        self.base_url = base_url or settings.OPENAI_BASE_URL
        self.model = model or settings.OPENAI_MODEL
        
        # 创建OpenAI客户端
        self.client = AsyncOpenAI(
            api_key=self.api_key,
            base_url=self.base_url
        )

    async def call_llm(self, messages: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        调用OpenAI API
        """
        completion = await self.client.chat.completions.create(
            model=self.model,
            messages=messages)
        return completion.choices[0].message.content
    
    async def analyze_intent(self, prompt: str) -> Dict[str, Any]:
        """
        分析用户意图
        
        Args:
            user_query: 用户查询
            available_tools: 可用工具列表
            
        Returns:
            意图分析结果
        """

        try:
            # 调用OpenAI API
            completion = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": """你是一个专业的数据分析师，负责理解用户意图并规划分析任务。
                        请直接返回JSON格式的分析方案，不要包含任何其他文字。
                        确保返回的是合法的JSON格式。"""
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                response_format={"type": "json_object"}
            )
            
            # 获取响应内容
            content = completion.choices[0].message.content
            self.logger.debug(f"LLM原始响应: {content}")

            # 🔧 增强的JSON解析，处理可能的markdown包裹
            try:
                return json.loads(content)
            except json.JSONDecodeError:
                # 如果直接解析失败，尝试清理markdown标记
                import re
                cleaned_content = content.strip()

                # 移除可能的markdown代码块标记
                if cleaned_content.startswith('```json'):
                    cleaned_content = cleaned_content[7:].strip()
                elif cleaned_content.startswith('```'):
                    cleaned_content = cleaned_content[3:].strip()

                if cleaned_content.endswith('```'):
                    cleaned_content = cleaned_content[:-3].strip()

                # 再次尝试解析
                return json.loads(cleaned_content)
            
        except Exception as e:
            self.logger.error(f"意图分析失败: {str(e)}")

            # 返回默认分析方案（字典格式）
            return {
                "problem_understanding": {
                    "core_question": "意图分析失败",
                    "analysis_target": "unknown"
                },
                "analysis_steps": [],
                "intent_complexity": "simple"
            }
    
    async def generate_report(self, user_query: str, intent_analysis: Dict[str, Any], 
                             execution_results: Dict[str, Any]) -> str:
        """
        生成分析报告
        
        Args:
            user_query: 用户查询
            intent_analysis: 意图分析结果
            execution_results: 执行结果
            
        Returns:
            分析报告
        """
        self.logger.info("开始生成分析报告")
        
        # 生成分析报告
        prompt = f"""请根据以下分析结果生成一份详细的分析报告：

问题理解：{intent_analysis['problem_understanding']}

分析步骤和结果：
{json.dumps(execution_results, ensure_ascii=False, indent=2, cls=DecimalEncoder)}

请生成一份结构化的分析报告，要求：
1. 总体情况概述
   - 简要说明分析目的
   - 概括主要发现

2. 详细分析
   - 针对每个维度进行具体分析
   - 突出关键数据和发现
   - 对异常或特殊情况进行说明

3. 风险评估（如果适用）
   - 识别潜在风险点
   - 评估风险等级
   - 分析风险影响

4. 建议措施
   - 提供具体可行的建议
   - 说明建议的优先级
   - 预期效果分析

注意事项：
1. 使用清晰的标题和分段
2. 数据分析要有具体数字支持
3. 对重要发现进行重点标注
4. 使用客观专业的语言
5. 如果数据不足，请说明原因

请按照以上要求生成报告，确保内容完整、逻辑清晰、重点突出。
"""
        try:
            # 调用OpenAI API
            completion = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "你是一个专业的数据分析师，负责生成数据分析报告。请生成一份清晰、专业的分析报告。"
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ]
            )
            
            # 获取响应内容
            return completion.choices[0].message.content
            
        except Exception as e:
            self.logger.error(f"报告生成失败: {str(e)}")
            return f"报告生成失败: {str(e)}"
    
    async def complete_parameters(self, previous_result: Dict[str, Any], required_fields: List[str]) -> Dict[str, Any]:
        """
        从前一步执行结果中提取所需参数
        
        Args:
            previous_result: 前一步执行结果
            required_fields: 所需字段列表
            
        Returns:
            提取的参数字典
        """
        self.logger.info(f"从执行结果中提取参数: {required_fields}")
        
        if not previous_result:
            self.logger.warning("前一步执行结果为空")
            return {}
        
        # 构建提示词
        prompt = f"""请从以下执行结果中提取所需的参数。
        
执行结果：
{json.dumps(previous_result, ensure_ascii=False, indent=2, cls=DecimalEncoder)}

需要提取的字段：
{json.dumps(required_fields, ensure_ascii=False, indent=2)}

你的任务是：
1. 仔细分析执行结果
2. 提取指定字段的值
3. 如果字段存在于结果中，直接提取相应的值
4. 如果字段不直接存在，但可以从结果中推断，请推断其值
5. 如果无法提取或推断，请在相应字段返回null

提取的参数需要以JSON格式返回，格式如下：
{{
  "字段1": "值1",
  "字段2": "值2"
}}

仅返回JSON格式的结果，不要添加任何其他解释。
"""
        
        try:
            # 调用OpenAI API
            completion = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": """你是一个专业的数据参数提取专家。
                        你的任务是从执行结果中提取指定的参数。
                        请仅返回JSON格式的提取结果，不要添加任何其他解释或分析。
                        请确保所有必需的字段都被提取。
                        如果无法提取某个字段，请返回null。"""
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                response_format={"type": "json_object"}
            )
            
            # 获取响应内容
            content = completion.choices[0].message.content
            self.logger.debug(f"参数提取原始响应: {content}")

            # 🔧 增强的JSON解析，处理可能的markdown包裹
            try:
                extracted_params = json.loads(content)
            except json.JSONDecodeError:
                # 如果直接解析失败，尝试清理markdown标记
                import re
                cleaned_content = content.strip()

                # 移除可能的markdown代码块标记
                if cleaned_content.startswith('```json'):
                    cleaned_content = cleaned_content[7:].strip()
                elif cleaned_content.startswith('```'):
                    cleaned_content = cleaned_content[3:].strip()

                if cleaned_content.endswith('```'):
                    cleaned_content = cleaned_content[:-3].strip()

                # 再次尝试解析
                extracted_params = json.loads(cleaned_content)
            
            # 过滤掉值为None的参数
            filtered_params = {k: v for k, v in extracted_params.items() if v is not None}
            
            self.logger.info(f"成功提取参数: {filtered_params}")
            return filtered_params
            
        except Exception as e:
            self.logger.error(f"参数提取失败: {str(e)}")
            return {}
            
    def _get_default_analysis_plan(self, user_query: str, available_tools: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        获取默认分析方案
        
        Args:
            user_query: 用户查询
            available_tools: 可用工具列表
            
        Returns:
            默认分析方案
        """
        self.logger.info("使用默认分析方案")
        
        # 获取第一个可用的工具
        default_tool = None
        if available_tools:
            default_tool = available_tools[0]
        
        if not default_tool:
            return {
                "problem_understanding": {
                    "core_question": "无法分析用户意图",
                    "analysis_target": "unknown"
                },
                "analysis_steps": []
            }
        
        # 构建默认参数
        default_params = {}
        for param in default_tool.get("parameters", []):
            param_name = param.get("name")
            default_params[param_name] = param.get("default", "%")
        
        return {
            "problem_understanding": {
                "core_question": user_query,
                "analysis_target": "all"
            },
            "analysis_steps": [
                {
                    "step_id": "STEP1",
                    "tool_name": default_tool.get("name"),
                    "purpose": f"分析{user_query}",
                    "parameters": default_params
                }
            ]
        } 