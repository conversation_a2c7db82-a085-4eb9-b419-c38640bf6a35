from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional

class LLMService(ABC):
    """LLM服务基类"""
    
    def __init__(self, model_id: Optional[str] = None):
        """
        初始化LLM服务
        
        Args:
            model_id: 数据库中的模型ID，用于Token用量统计
        """
        self.model_id = model_id
    
    @abstractmethod
    async def analyze_intent(self, user_query: str, available_tools: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        分析用户意图
        
        Args:
            user_query: 用户查询
            available_tools: 可用工具列表
            
        Returns:
            意图分析结果
        """
        pass
    
    @abstractmethod
    async def generate_report(self, user_query: str, intent_analysis: Dict[str, Any], 
                              execution_results: Dict[str, Any]) -> str:
        """
        生成分析报告
        
        Args:
            user_query: 用户查询
            intent_analysis: 意图分析结果
            execution_results: 执行结果
            
        Returns:
            分析报告
        """
        pass 