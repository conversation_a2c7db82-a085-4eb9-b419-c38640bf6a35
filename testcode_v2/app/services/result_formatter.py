#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
工具执行结果格式化服务
根据工具的display_format属性，将执行结果转换为适合前端展示的格式
"""

from typing import Dict, Any, List, Optional
import json

class ResultFormatter:
    """工具执行结果格式化器"""
    
    @staticmethod
    def format_result(result: Dict[str, Any], display_format: str) -> Dict[str, Any]:
        """
        根据展示格式格式化工具执行结果
        
        Args:
            result: 工具执行原始结果
            display_format: 展示格式
            
        Returns:
            格式化后的结果
        """
        # 如果执行失败，直接返回原结果（支持布尔值和字符串形式）
        success_value = result.get("success", False)
        if success_value is False or success_value == "False" or success_value == "false":
            return result
        
        # 根据不同展示格式处理
        formatter_method = getattr(
            ResultFormatter, 
            f"_format_{display_format.lower()}", 
            ResultFormatter._format_json
        )
        
        formatted_result = formatter_method(result)
        
        # 添加展示格式信息，便于前端处理
        formatted_result["display_format"] = display_format
        
        return formatted_result
    
    @staticmethod
    def _format_json(result: Dict[str, Any]) -> Dict[str, Any]:
        """JSON格式处理（原样返回）"""
        return result
    
    @staticmethod
    def _format_table(result: Dict[str, Any]) -> Dict[str, Any]:
        """表格格式处理"""
        try:
            data = result.get("data", [])
            
            # 如果数据为空，直接返回
            if not data:
                return {
                    "success": True,
                    "data": [],
                    "columns": [],
                    "display_data": {
                        "type": "table",
                        "data": [],
                        "columns": []
                    }
                }
            
            # 如果是单条JSON数据，尝试解析
            if len(data) == 1 and isinstance(data[0], dict) and "JSON_DOC" in data[0]:
                try:
                    # 尝试解析JSON_DOC字段
                    json_data = json.loads(data[0]["JSON_DOC"])
                    
                    # 检查是否为数组
                    if isinstance(json_data, list) and json_data:
                        data = json_data
                except:
                    # 解析失败时，保持原样
                    pass
            
            # 提取列名
            if data and isinstance(data[0], dict):
                columns = list(data[0].keys())
            else:
                columns = []
            
            # 构建表格数据
            return {
                "success": True,
                "data": data,
                "columns": columns,
                "display_data": {
                    "type": "table",
                    "data": data,
                    "columns": columns
                },
                "sql": result.get("sql"),
                "params": result.get("params")
            }
        except Exception as e:
            # 处理失败时，返回原结果并添加错误信息
            return {
                **result,
                "format_error": str(e)
            }
    
    @staticmethod
    def _format_graph(result: Dict[str, Any]) -> Dict[str, Any]:
        """图形格式处理"""
        try:
            data = result.get("data", [])
            
            # 提取节点和边信息
            nodes = []
            edges = []
            node_set = set()
            
            # 如果是资金流转分析等数据，提取源公司和目标公司作为节点，构建边关系
            for item in data:
                source = item.get("源公司名称")
                target = item.get("目标公司名称")
                
                if source and source not in node_set:
                    nodes.append({
                        "id": source,
                        "label": source,
                        "type": "company"
                    })
                    node_set.add(source)
                
                if target and target not in node_set:
                    nodes.append({
                        "id": target,
                        "label": target,
                        "type": "company"
                    })
                    node_set.add(target)
                
                if source and target:
                    edges.append({
                        "id": f"{source}-{target}",
                        "source": source,
                        "target": target,
                        "label": f"交易 {item.get('交易次数', 0)} 次",
                        "value": item.get("总金额", 0),
                        "risk": item.get("风险等级", "低风险")
                    })
            
            # 构建图形数据
            return {
                "success": True,
                "data": data,
                "display_data": {
                    "type": "graph",
                    "nodes": nodes,
                    "edges": edges
                },
                "sql": result.get("sql"),
                "params": result.get("params")
            }
        except Exception as e:
            # 处理失败时，返回原结果并添加错误信息
            return {
                **result,
                "format_error": str(e)
            }
    
    @staticmethod
    def _format_text(result: Dict[str, Any]) -> Dict[str, Any]:
        """文本格式处理"""
        try:
            data = result.get("data", [])
            
            # 如果有多条数据，尝试提取第一条数据的第一个字段
            text_content = ""
            if data:
                if isinstance(data[0], dict):
                    # 提取第一个值作为文本内容
                    text_content = next(iter(data[0].values()), "")
                elif isinstance(data[0], (list, tuple)) and data[0]:
                    # 如果是列表或元组，取第一个元素
                    text_content = data[0][0]
            
            # 构建文本数据
            return {
                "success": True,
                "data": data,
                "display_data": {
                    "type": "text",
                    "content": text_content
                },
                "sql": result.get("sql"),
                "params": result.get("params")
            }
        except Exception as e:
            # 处理失败时，返回原结果并添加错误信息
            return {
                **result,
                "format_error": str(e)
            }
    
    @staticmethod
    def _format_chart(result: Dict[str, Any]) -> Dict[str, Any]:
        """图表格式处理"""
        try:
            data = result.get("data", [])
            
            # 适配Echarts格式
            if data and isinstance(data[0], dict):
                # 提取所有键作为维度
                dimensions = list(data[0].keys())
                
                # 提取数据
                series_data = []
                for item in data:
                    series_data.append(list(item.values()))
                
                # 构建图表数据
                return {
                    "success": True,
                    "data": data,
                    "display_data": {
                        "type": "chart",
                        "chartType": "bar",  # 默认使用柱状图
                        "dimensions": dimensions,
                        "source": data
                    },
                    "sql": result.get("sql"),
                    "params": result.get("params")
                }
            else:
                # 数据格式不适合图表展示
                return {
                    "success": True,
                    "data": data,
                    "display_data": {
                        "type": "json",  # 退化为JSON展示
                        "data": data
                    },
                    "sql": result.get("sql"),
                    "params": result.get("params")
                }
        except Exception as e:
            # 处理失败时，返回原结果并添加错误信息
            return {
                **result,
                "format_error": str(e)
            }