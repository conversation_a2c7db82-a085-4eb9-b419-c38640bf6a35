"""
图表与报告集成服务
将图表生成功能与报告系统深度整合，实现数据驱动的可视化报告
"""

import json
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

from app.services.report_generator import ReportGenerator
from app.services.chart_generation import ChartGenerationAgent
from app.utils.json_utils import DecimalEncoder

log = logging.getLogger(__name__)


class ChartReportIntegrator:
    """图表与报告集成器"""
    
    def __init__(self, llm_service, db=None):
        """
        初始化集成器

        Args:
            llm_service: LLM服务实例
            db: 数据库会话（用于图表生成Agent）
        """
        self.llm_service = llm_service
        self.db = db
        self.report_generator = ReportGenerator(llm_service)
        # 使用图表生成Agent
        if db:
            self.chart_generation_agent = ChartGenerationAgent(db)
        else:
            # 如果没有db，暂时设为None，需要时再处理
            self.chart_generation_agent = None
        self.logger = log
    
    async def generate_integrated_report(self, 
                                       user_query: str, 
                                       intent_analysis: Dict[str, Any], 
                                       execution_results: List[Dict[str, Any]],
                                       project_custom_prompt: Optional[str] = None) -> Dict[str, Any]:
        """
        生成集成了图表的智能报告
        
        Args:
            user_query: 用户查询
            intent_analysis: 意图分析结果
            execution_results: 执行结果
            project_custom_prompt: 项目自定义提示词
            
        Returns:
            包含报告内容和图表的完整结果
        """
        try:
            self.logger.info("开始生成集成图表的智能报告")
            
            # 1. 生成基础智能报告
            base_report_content = await self.report_generator.generate_report(
                user_query, intent_analysis, execution_results, project_custom_prompt, None, []
            )
            
            # 构建与原格式兼容的基础报告结构
            base_report = {
                "report_content": base_report_content,
                "visualization_suggestions": [],
                "generated_charts": [],
                "generation_metadata": {
                    "generated_at": datetime.now().isoformat(),
                    "custom_prompt_used": bool(project_custom_prompt),
                    "charts_included": 0
                }
            }
            
            # 2. 为每个执行结果生成图表
            chart_results = []
            for i, result in enumerate(execution_results):
                try:
                    chart_data = await self._generate_chart_for_result(
                        result, user_query, f"step_{i+1}"
                    )
                    if chart_data:
                        chart_results.append(chart_data)
                except Exception as e:
                    self.logger.warning(f"为步骤 {i+1} 生成图表失败: {str(e)}")
                    continue
            
            # 3. 增强可视化建议
            enhanced_suggestions = self._enhance_visualization_suggestions(
                base_report['visualization_suggestions'], chart_results
            )
            
            # 4. 生成图表增强的报告内容
            enhanced_report_content = await self._enhance_report_with_charts(
                base_report['report_content'], chart_results, user_query
            )
            
            # 5. 构建完整的集成结果
            integrated_result = {
                **base_report,
                "report_content": enhanced_report_content,
                "visualization_suggestions": enhanced_suggestions,
                "generated_charts": chart_results,
                "integration_metadata": {
                    "charts_generated": len(chart_results),
                    "integration_type": "full",
                    "enhanced_at": datetime.now().isoformat(),
                    "has_interactive_charts": len(chart_results) > 0
                }
            }
            
            self.logger.info(f"集成报告生成完成，包含 {len(chart_results)} 个图表")
            return integrated_result
            
        except Exception as e:
            self.logger.error(f"集成报告生成失败: {str(e)}")
            raise
    
    async def _generate_chart_for_result(self, 
                                       result: Dict[str, Any], 
                                       user_query: str, 
                                       step_id: str) -> Optional[Dict[str, Any]]:
        """
        为单个执行结果生成图表
        
        Args:
            result: 执行结果
            user_query: 用户查询
            step_id: 步骤ID
            
        Returns:
            图表数据或None
        """
        try:
            tool_name = result.get('tool_name', 'Unknown Tool')
            tool_result = result.get('result', {})
            
            # 检查是否有可视化的数据
            if not self._has_visualizable_data(tool_result):
                return None
            
            # 使用图表生成代理
            chart_event_data = await self.chart_generation_agent.process_tool_result(
                analysis_id="report_integration",
                step_id=step_id,
                tool_name=tool_name,
                tool_result=tool_result,
                user_query=user_query,
                reasoning=f"为{tool_name}生成可视化图表",
                schema_info=None,
                event_callback=None
            )
            
            if chart_event_data and chart_event_data.get('chart_data'):
                chart_config = chart_event_data['chart_data']['data'].get('chart_config')
                if chart_config:
                    return {
                        "step_id": step_id,
                        "tool_name": tool_name,
                        "chart_config": chart_config,
                        "chart_title": self._extract_chart_title(chart_config),
                        "chart_type": chart_config.get('series', [{}])[0].get('type', 'unknown'),
                        "data_source": tool_result,
                        "auto_generated": True
                    }
            
            return None
            
        except Exception as e:
            self.logger.error(f"为结果生成图表失败: {str(e)}")
            return None
    
    def _has_visualizable_data(self, tool_result: Dict[str, Any]) -> bool:
        """
        检查工具结果是否包含可视化的数据
        
        Args:
            tool_result: 工具执行结果
            
        Returns:
            是否包含可视化数据
        """
        if not isinstance(tool_result, dict):
            return False
        
        # 检查是否有数据字段
        data = tool_result.get('data')
        if not data:
            return False
        
        # 检查数据是否为列表且不为空
        if isinstance(data, list) and len(data) > 0:
            # 检查第一个元素是否为字典（表格数据）
            if isinstance(data[0], dict) and len(data[0]) > 0:
                return True
        
        # 检查是否为其他可视化的数据结构
        if isinstance(data, dict) and len(data) > 0:
            return True
        
        return False
    
    def _extract_chart_title(self, chart_config: Dict[str, Any]) -> str:
        """
        从图表配置中提取标题
        
        Args:
            chart_config: 图表配置
            
        Returns:
            图表标题
        """
        # 简化逻辑：直接获取title.text，如果不存在就用默认值
        return chart_config.get('title', {}).get('text', '数据可视化图表')
    
    def _enhance_visualization_suggestions(self, 
                                         base_suggestions: List[Dict[str, Any]], 
                                         chart_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        增强可视化建议
        
        Args:
            base_suggestions: 基础可视化建议
            chart_results: 生成的图表结果
            
        Returns:
            增强后的可视化建议
        """
        enhanced_suggestions = base_suggestions.copy()
        
        # 为每个生成的图表添加建议
        for chart in chart_results:
            chart_suggestion = {
                "chart_type": chart['chart_type'],
                "title": chart['chart_title'],
                "description": f"基于{chart['tool_name']}数据自动生成的{chart['chart_type']}图表",
                "priority": "high",
                "fields": {
                    "auto_generated": True,
                    "step_id": chart['step_id'],
                    "tool_name": chart['tool_name']
                },
                "chart_config": chart['chart_config'],
                "implementation_status": "generated"
            }
            enhanced_suggestions.append(chart_suggestion)
        
        return enhanced_suggestions
    
    async def _enhance_report_with_charts(self, 
                                        base_report: str, 
                                        chart_results: List[Dict[str, Any]], 
                                        user_query: str) -> str:
        """
        使用图表增强报告内容
        
        Args:
            base_report: 基础报告内容
            chart_results: 图表结果
            user_query: 用户查询
            
        Returns:
            增强后的报告内容
        """
        if not chart_results:
            return base_report
        
        try:
            # 构建图表描述
            chart_descriptions = []
            for i, chart in enumerate(chart_results, 1):
                chart_desc = f"""
### 图表 {i}: {chart['chart_title']}

**数据来源**: {chart['tool_name']}
**图表类型**: {chart['chart_type']}
**生成方式**: 基于执行结果自动生成

该图表展示了{chart['tool_name']}的执行结果，通过{chart['chart_type']}的形式直观地呈现数据特征和趋势。
"""
                chart_descriptions.append(chart_desc)
            
            # 在报告中插入图表部分
            chart_section = f"""
## 数据可视化

本报告包含 {len(chart_results)} 个自动生成的数据可视化图表，帮助更直观地理解分析结果：

{''.join(chart_descriptions)}

> **说明**: 以上图表基于分析过程中的数据自动生成，可在报告界面中查看交互式图表。
"""
            
            # 在报告结论前插入图表部分
            if "## 结论" in base_report or "## 4. 结论" in base_report:
                # 在结论部分前插入
                if "## 结论" in base_report:
                    enhanced_report = base_report.replace("## 结论", f"{chart_section}\n## 结论")
                else:
                    enhanced_report = base_report.replace("## 4. 结论", f"{chart_section}\n## 4. 结论")
            else:
                # 在报告末尾添加
                enhanced_report = f"{base_report}\n{chart_section}"
            
            return enhanced_report
            
        except Exception as e:
            self.logger.error(f"增强报告内容失败: {str(e)}")
            return base_report
    
    def get_chart_export_data(self, chart_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        获取图表导出数据
        
        Args:
            chart_results: 图表结果
            
        Returns:
            可导出的图表数据
        """
        export_data = []
        
        for chart in chart_results:
            export_item = {
                "title": chart['chart_title'],
                "type": chart['chart_type'],
                "config": chart['chart_config'],
                "source": chart['tool_name'],
                "step_id": chart['step_id']
            }
            export_data.append(export_item)
        
        return export_data
