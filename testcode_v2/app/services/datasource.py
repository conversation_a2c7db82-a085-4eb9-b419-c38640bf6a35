import uuid
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from app.utils.time_utils import get_shanghai_time
from sqlalchemy.orm import Session

from app.db.engines import engine_factory
from app.models.data_source import DataSource
from app.models.selected_table import SelectedTable
from app.schemas.data_source import DataSourceType
from app.schemas.selected_table import SelectedTableCreate

logger = logging.getLogger(__name__)

class DataSourceService:
    """数据源服务"""

    @staticmethod
    def get_database_version(db_type: str, config: Dict[str, Any]) -> Optional[str]:
        """获取数据库版本信息
        
        Args:
            db_type: 数据库类型
            config: 数据库配置
            
        Returns:
            Optional[str]: 数据库版本信息，如果获取失败返回None
        """
        try:
            # 导入text函数用于SQL查询
            from sqlalchemy import text
            
            # 创建临时连接
            engine = engine_factory(db_type, config)
            
            with engine.connect() as connection:
                version_query = None
                
                # 根据不同数据库类型构建版本查询语句
                if db_type.lower() == 'oracle':
                    version_query = text("SELECT banner FROM v$version WHERE banner LIKE 'Oracle%'")
                elif db_type.lower() == 'mysql':
                    # 修复MySQL版本查询，使用更稳健的方式
                    version_query = text("SELECT VERSION()")
                elif db_type.lower() == 'postgresql':
                    version_query = text("SELECT version()")
                elif db_type.lower() == 'mssql':
                    version_query = text("SELECT @@VERSION")
                else:
                    logger.info(f"暂不支持获取 {db_type} 数据库的版本信息")
                    return None
                
                if version_query is not None:
                    result = connection.execute(version_query)
                    row = result.fetchone()
                    if row:
                        # 获取版本信息，使用更稳健的方式处理不同数据库返回格式
                        try:
                            # 尝试多种方式获取版本信息
                            version_info = None
                            
                            # 方法1: 直接通过索引获取
                            if hasattr(row, '__getitem__'):
                                version_info = row[0]
                            
                            # 方法2: 通过_mapping获取（SQLAlchemy 1.4+）
                            elif hasattr(row, '_mapping') and row._mapping:
                                version_info = list(row._mapping.values())[0]
                            
                            # 方法3: 通过属性名获取
                            elif hasattr(row, 'version'):
                                version_info = row.version
                            
                            # 方法4: 转换为元组后获取
                            else:
                                version_info = tuple(row)[0]
                            
                            # 清理版本信息，移除多余的空白字符
                            if version_info and isinstance(version_info, str):
                                version_info = version_info.strip()
                                # 对于某些数据库，截取主要版本信息（避免过长）
                                if len(version_info) > 200:
                                    version_info = version_info[:200] + "..."
                            
                            if version_info:
                                logger.info(f"成功获取 {db_type} 数据库版本: {version_info}")
                                return version_info
                            
                        except Exception as parse_error:
                            logger.warning(f"解析 {db_type} 数据库版本信息时出错: {str(parse_error)}")
                            # 尝试将整个row转换为字符串
                            try:
                                version_info = str(row).strip()
                                if version_info and version_info != "()":
                                    logger.info(f"成功获取 {db_type} 数据库版本（备用方法）: {version_info}")
                                    return version_info
                            except Exception:
                                pass
                    
        except Exception as e:
            logger.warning(f"获取 {db_type} 数据库版本信息失败: {str(e)}")
            
        return None

    @staticmethod
    def test_connection(db_type: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """测试数据源连接
        
        Args:
            db_type: 数据库类型
            config: 数据库配置
            
        Returns:
            Dict: 包含连接结果的字典
        """
        try:
            # 先检查config中的必要参数
            if db_type.lower() in ['mysql', 'postgresql', 'mssql']:
                required_fields = ['host', 'port', 'database', 'username', 'password']
                missing_fields = [field for field in required_fields if field not in config or config[field] == '']
                
                if missing_fields:
                    return {
                        "success": False,
                        "message": f"缺少必要的连接参数: {', '.join(missing_fields)}",
                        "tables": [],
                        "db_version": None
                    }
            elif db_type.lower() == 'oracle':
                required_fields = ['host', 'port', 'service_name', 'username', 'password']
                missing_fields = [field for field in required_fields if field not in config or config[field] == '']
                
                if missing_fields:
                    return {
                        "success": False,
                        "message": f"缺少必要的连接参数: {', '.join(missing_fields)}",
                        "tables": [],
                        "db_version": None
                    }
            elif db_type.lower() == 'http_api':
                if 'base_url' not in config or config['base_url'] == '':
                    return {
                        "success": False,
                        "message": "缺少必要的连接参数: base_url",
                        "tables": [],
                        "db_version": None
                    }
                
            # 创建临时连接
            engine = engine_factory(db_type, config)
            
            # 获取数据库版本信息
            db_version = DataSourceService.get_database_version(db_type, config)
            
            # 获取所有表名
            with engine.connect() as connection:
                if db_type.lower() in ['oracle', 'mysql', 'postgresql']:
                    # 导入text函数用于SQL查询
                    from sqlalchemy import text
                    
                    # 根据不同数据库类型获取表名查询
                    if db_type.lower() == 'oracle':
                        tables_query = text("SELECT table_name FROM user_tables ORDER BY table_name")
                        result = connection.execute(tables_query)
                    elif db_type.lower() == 'mysql':
                        # 使用参数化查询，避免SQL注入
                        database_name = config.get('database', '')
                        if not database_name:
                            return {
                                "success": False,
                                "message": "请提供有效的数据库名",
                                "tables": [],
                                "db_version": db_version
                            }
                        # 修复MySQL查询，使用更明确的查询方式
                        tables_query = text("SELECT table_name FROM information_schema.tables WHERE table_schema = :db_name AND table_type = 'BASE TABLE' ORDER BY table_name")
                        result = connection.execute(tables_query, {"db_name": database_name})
                    elif db_type.lower() == 'postgresql':
                        tables_query = text("SELECT tablename as table_name FROM pg_catalog.pg_tables WHERE schemaname = 'public' ORDER BY tablename")
                        result = connection.execute(tables_query)
                    
                    # 从结果中提取表名
                    try:
                        tables = [row[0] for row in result]
                    except Exception as e:
                        logger.error(f"提取表名时出错: {str(e)}")
                        tables = []
                else:
                    # 对于不支持的数据库类型，返回空表列表
                    tables = []
            
            return {
                "success": True,
                "message": "连接成功",
                "tables": tables,
                "db_version": db_version
            }
        except Exception as e:
            logger.error(f"数据源连接测试失败: {str(e)}")
            error_message = str(e)
            
            # 针对常见错误提供更友好的提示
            if "Access denied" in error_message:
                error_message = "访问被拒绝，请检查用户名和密码"
            elif "Unknown database" in error_message:
                error_message = "未知数据库，请检查数据库名是否正确"
            elif "Connection refused" in error_message:
                error_message = "连接被拒绝，请检查主机地址和端口"
            elif "Operation timed out" in error_message:
                error_message = "连接超时，请检查网络和服务器状态"
            
            return {
                "success": False,
                "message": f"连接失败: {error_message}",
                "tables": [],
                "db_version": None
            }
    
    @staticmethod
    def get_table_info(db_type: str, config: Dict[str, Any], table_name: str, max_field_length: int = 200) -> Dict[str, Any]:
        """获取表结构和样本数据
        
        Args:
            db_type: 数据库类型
            config: 数据库配置
            table_name: 表名
            max_field_length: 字段值的最大长度，超过此长度将被截断
            
        Returns:
            Dict: 包含表结构和样本数据的字典
        """
        try:
            # 导入text函数用于SQL查询
            from sqlalchemy import text
            
            # 创建临时连接
            engine = engine_factory(db_type, config)
            
            with engine.connect() as connection:
                # 获取表注释
                table_comment = ""
                try:
                    if db_type.lower() == 'oracle':
                        comment_query = text("""
                            SELECT comments 
                            FROM user_tab_comments 
                            WHERE table_name = :table_name
                        """)
                        comment_result = connection.execute(comment_query, {"table_name": table_name.upper()})
                        comment_row = comment_result.fetchone()
                        if comment_row and comment_row[0]:
                            table_comment = comment_row[0]
                    elif db_type.lower() == 'mysql':
                        comment_query = text("""
                            SELECT table_comment 
                            FROM information_schema.tables 
                            WHERE table_schema = :db_name 
                            AND table_name = :table_name
                        """)
                        comment_result = connection.execute(comment_query, {
                            "db_name": config.get('database', ''),
                            "table_name": table_name
                        })
                        comment_row = comment_result.fetchone()
                        if comment_row and comment_row[0]:
                            table_comment = comment_row[0]
                    elif db_type.lower() == 'postgresql':
                        comment_query = text("""
                            SELECT obj_description(c.oid) as table_comment
                            FROM pg_class c
                            JOIN pg_namespace n ON n.oid = c.relnamespace
                            WHERE c.relname = :table_name 
                            AND n.nspname = 'public'
                        """)
                        comment_result = connection.execute(comment_query, {"table_name": table_name})
                        comment_row = comment_result.fetchone()
                        if comment_row and comment_row[0]:
                            table_comment = comment_row[0]
                except Exception as e:
                    logger.warning(f"获取表 {table_name} 注释失败: {str(e)}")
                    table_comment = ""
                
                # 获取表结构
                if db_type.lower() == 'oracle':
                    schema_query = text("""
                        SELECT column_name, data_type, data_length, nullable 
                        FROM user_tab_columns 
                        WHERE table_name = :table_name
                        ORDER BY column_id
                    """)
                    schema_result = connection.execute(schema_query, {"table_name": table_name.upper()})
                elif db_type.lower() == 'mysql':
                    schema_query = text("""
                        SELECT column_name, data_type, character_maximum_length as data_length, 
                               is_nullable as nullable, column_comment as description
                        FROM information_schema.columns 
                        WHERE table_schema = :db_name 
                        AND table_name = :table_name
                        ORDER BY ordinal_position
                    """)
                    schema_result = connection.execute(schema_query, {
                        "db_name": config.get('database', ''),
                        "table_name": table_name
                    })
                elif db_type.lower() == 'postgresql':
                    schema_query = text("""
                        SELECT column_name, data_type, character_maximum_length as data_length,
                               is_nullable as nullable
                        FROM information_schema.columns 
                        WHERE table_name = :table_name
                        ORDER BY ordinal_position
                    """)
                    schema_result = connection.execute(schema_query, {"table_name": table_name})
                else:
                    return {"error": "不支持的数据库类型"}
                
                # 执行查询获取表结构
                schema_list = []
                for row in schema_result:
                    # 使用兼容性更好的方式处理行数据
                    column_info = {}
                    # 使用._mapping或直接将行对象转为dict
                    try:
                        # 尝试使用较新SQLAlchemy版本的方法
                        if hasattr(row, '_mapping'):
                            # SQLAlchemy 1.4+
                            column_info = dict(row._mapping)
                        else:
                            # 旧版本或替代方法
                            column_info = dict(row.items())
                    except Exception as e:
                        # 备用方法：遍历结果集中的列名
                        try:
                            # 获取结果集的列名
                            columns = schema_result.keys()
                            for i, col_name in enumerate(columns):
                                column_info[col_name] = row[i]
                        except Exception as inner_e:
                            # 最后的备用方法：简单地将行转换为dict
                            logger.warning(f"处理表结构行数据时出错，使用备用方法：{str(e)} -> {str(inner_e)}")
                            try:
                                column_info = dict(row)
                            except:
                                # 仍然失败，尝试直接使用索引
                                if hasattr(schema_result, 'keys'):
                                    columns = schema_result.keys()
                                    column_info = {col: row[i] for i, col in enumerate(columns)}
                    
                    schema_list.append(column_info)
                
                # 将列表转换为字典格式，以符合Pydantic模型的要求
                # 使用列名作为字典的键
                schema_dict = {}
                for column in schema_list:
                    # 获取列名，处理不同数据库返回的大小写差异
                    col_name = None
                    for key in ['column_name', 'COLUMN_NAME']:
                        if key in column:
                            col_name = column[key]
                            break
                    
                    if col_name:
                        # 删除列名字段，避免重复
                        column_copy = column.copy()
                        for key in ['column_name', 'COLUMN_NAME']:
                            if key in column_copy:
                                del column_copy[key]
                        
                        # 使用列名作为键
                        schema_dict[col_name] = column_copy
                    else:
                        # 如果找不到列名，使用递增索引作为键
                        schema_dict[f"column_{len(schema_dict)}"] = column
                
                # 新增：获取外键信息并添加到schema_dict中
                try:
                    foreign_key_info = {}
                    
                    if db_type.lower() == 'oracle':
                        fk_query = text("""
                            SELECT 
                                c.column_name,
                                r.table_name as referenced_table,
                                r.column_name as referenced_column,
                                uc.constraint_name
                            FROM user_cons_columns c
                            JOIN user_constraints uc ON c.constraint_name = uc.constraint_name
                            JOIN user_cons_columns r ON uc.r_constraint_name = r.constraint_name
                            WHERE uc.constraint_type = 'R' 
                            AND c.table_name = :table_name
                        """)
                        fk_result = connection.execute(fk_query, {"table_name": table_name.upper()})
                        
                    elif db_type.lower() == 'mysql':
                        # 获取当前数据库名
                        db_name = config.get('database', '')
                        logger.info(f"执行外键查询 - 数据库: {db_name}, 表名: {table_name}")
                        
                        # 先验证表是否存在
                        table_check_query = text("""
                            SELECT table_name FROM information_schema.tables 
                            WHERE table_schema = :db_name AND table_name = :table_name
                        """)
                        table_check_result = connection.execute(table_check_query, {
                            "db_name": db_name, 
                            "table_name": table_name
                        })
                        table_exists = table_check_result.fetchone()
                        logger.info(f"表 {table_name} 是否存在: {bool(table_exists)}")
                        
                        # 查看该数据库中是否有任何外键约束
                        all_fk_check_query = text("""
                            SELECT COUNT(*) as fk_count
                            FROM information_schema.key_column_usage kcu
                            WHERE kcu.table_schema = :db_name
                            AND kcu.referenced_table_name IS NOT NULL
                        """)
                        all_fk_result = connection.execute(all_fk_check_query, {"db_name": db_name})
                        total_fk_count = all_fk_result.fetchone()[0]
                        logger.info(f"数据库 {db_name} 中总共有 {total_fk_count} 个外键约束")
                        
                        # MySQL外键查询
                        fk_query = text("""
                            SELECT 
                                kcu.column_name,
                                kcu.referenced_table_name as referenced_table,
                                kcu.referenced_column_name as referenced_column,
                                kcu.constraint_name
                            FROM information_schema.key_column_usage kcu
                            WHERE kcu.table_schema = :db_name
                            AND kcu.table_name = :table_name
                            AND kcu.referenced_table_name IS NOT NULL
                            AND kcu.referenced_column_name IS NOT NULL
                        """)
                        
                        logger.info(f"执行外键查询SQL，参数: db_name='{db_name}', table_name='{table_name}'")
                        fk_result = connection.execute(fk_query, {
                            "db_name": db_name,
                            "table_name": table_name
                        })
                        
                        # 立即检查结果数量
                        fk_rows = list(fk_result)
                        logger.info(f"外键查询直接返回 {len(fk_rows)} 行结果")
                        
                        # 如果没有结果，尝试不区分大小写的查询
                        if len(fk_rows) == 0:
                            logger.info("尝试不区分大小写的外键查询...")
                            fk_query_ci = text("""
                                SELECT 
                                    kcu.column_name,
                                    kcu.referenced_table_name as referenced_table,
                                    kcu.referenced_column_name as referenced_column,
                                    kcu.constraint_name
                                FROM information_schema.key_column_usage kcu
                                WHERE LOWER(kcu.table_schema) = LOWER(:db_name)
                                AND LOWER(kcu.table_name) = LOWER(:table_name)
                                AND kcu.referenced_table_name IS NOT NULL
                                AND kcu.referenced_column_name IS NOT NULL
                            """)
                            fk_result_ci = connection.execute(fk_query_ci, {
                                "db_name": db_name,
                                "table_name": table_name
                            })
                            fk_rows = list(fk_result_ci)
                            logger.info(f"不区分大小写查询返回 {len(fk_rows)} 行结果")
                        
                        # 如果还是没有结果，尝试使用约束表联合查询
                        if len(fk_rows) == 0:
                            logger.info("尝试使用约束表联合查询...")
                            fk_query_alt = text("""
                                SELECT 
                                    kcu.column_name,
                                    kcu.referenced_table_name as referenced_table,
                                    kcu.referenced_column_name as referenced_column,
                                    tc.constraint_name
                                FROM information_schema.table_constraints tc
                                JOIN information_schema.key_column_usage kcu 
                                    ON tc.constraint_name = kcu.constraint_name 
                                    AND tc.table_schema = kcu.table_schema
                                WHERE tc.table_schema = :db_name 
                                AND tc.table_name = :table_name
                                AND tc.constraint_type = 'FOREIGN KEY'
                                AND kcu.referenced_table_name IS NOT NULL
                            """)
                            fk_result_alt = connection.execute(fk_query_alt, {
                                "db_name": db_name,
                                "table_name": table_name
                            })
                            fk_rows = list(fk_result_alt)
                            logger.info(f"约束表联合查询返回 {len(fk_rows)} 行结果")
                        
                        # 重新创建迭代器
                        fk_result = iter(fk_rows)
                        
                    elif db_type.lower() == 'postgresql':
                        fk_query = text("""
                            SELECT 
                                kcu.column_name,
                                ccu.table_name AS referenced_table,
                                ccu.column_name AS referenced_column,
                                tc.constraint_name
                            FROM information_schema.table_constraints tc
                            JOIN information_schema.key_column_usage kcu 
                                ON tc.constraint_name = kcu.constraint_name
                            JOIN information_schema.constraint_column_usage ccu 
                                ON ccu.constraint_name = tc.constraint_name
                            WHERE tc.constraint_type = 'FOREIGN KEY' 
                            AND tc.table_name = :table_name
                        """)
                        fk_result = connection.execute(fk_query, {"table_name": table_name})
                    else:
                        # 对于不支持的数据库类型，跳过外键查询
                        fk_result = []
                    
                    # 将外键信息存储为字典，键为列名
                    fk_count = 0
                    # 处理不同数据库类型的外键结果
                    if db_type.lower() == 'mysql':
                        # MySQL使用fk_rows列表
                        result_to_iterate = fk_rows
                    else:
                        # 其他数据库使用fk_result迭代器
                        result_to_iterate = fk_result
                    
                    for row in result_to_iterate:
                        try:
                            # 统一的行数据处理方式
                            if hasattr(row, '_mapping'):
                                row_dict = dict(row._mapping)
                            else:
                                row_dict = dict(row.items())
                            
                            # 处理字段名大小写问题，MySQL返回大写字段名
                            column_name = row_dict.get('column_name') or row_dict.get('COLUMN_NAME')
                            referenced_table = row_dict.get('referenced_table') or row_dict.get('REFERENCED_TABLE')
                            referenced_column = row_dict.get('referenced_column') or row_dict.get('REFERENCED_COLUMN')
                            constraint_name = row_dict.get('constraint_name') or row_dict.get('CONSTRAINT_NAME')
                            
                            if column_name and referenced_table and referenced_column:
                                foreign_key_info[column_name] = {
                                    'referenced_table': referenced_table,
                                    'referenced_column': referenced_column,
                                    'constraint_name': constraint_name
                                }
                                fk_count += 1
                                logger.info(f"找到外键: {column_name} -> {referenced_table}.{referenced_column}")
                        except Exception as e:
                            logger.warning(f"处理外键信息行时出错: {str(e)}")
                    
                    logger.info(f"表 {table_name} 共找到 {fk_count} 个外键")
                    
                    # 将外键信息添加到现有的schema_dict中
                    added_fk_count = 0
                    for col_name, col_info in schema_dict.items():
                        if col_name in foreign_key_info:
                            col_info['foreign_key'] = foreign_key_info[col_name]
                            added_fk_count += 1
                            logger.info(f"为列 {col_name} 添加外键信息: {foreign_key_info[col_name]}")
                    
                    logger.info(f"表 {table_name} 共为 {added_fk_count} 个列添加了外键信息")
                            
                except Exception as e:
                    logger.error(f"获取表 {table_name} 外键信息失败: {str(e)}")
                    import traceback
                    logger.error(f"外键查询错误详情: {traceback.format_exc()}")
                    # 添加更详细的调试信息
                    logger.error(f"数据库类型: {db_type}, 表名: {table_name}")
                    if db_type.lower() == 'mysql':
                        logger.error(f"数据库配置: {config}")
                        # 尝试简单的连接测试
                        try:
                            test_result = connection.execute(text("SELECT 1 as test"))
                            logger.info("数据库连接正常")
                        except Exception as conn_e:
                            logger.error(f"数据库连接测试失败: {str(conn_e)}")
                
                # 获取样本数据（前3条）
                try:
                    if db_type.lower() == 'oracle':
                        # Oracle使用特殊的ROWNUM语法
                        sample_query = text(f"SELECT * FROM {table_name} WHERE ROWNUM <= 3")
                    else:
                        # MySQL/PostgreSQL等使用LIMIT语法
                        sample_query = text(f"SELECT * FROM {table_name} LIMIT 3")
                    
                    sample_result = connection.execute(sample_query)
                    samples = []
                    for row in sample_result:
                        # 使用兼容性更好的方式处理行数据
                        sample = {}
                        try:
                            # 尝试使用较新SQLAlchemy版本的方法
                            if hasattr(row, '_mapping'):
                                # SQLAlchemy 1.4+
                                row_dict = dict(row._mapping)
                            else:
                                # 旧版本或替代方法
                                row_dict = dict(row.items())
                                
                            # 确保值是JSON可序列化的，并处理过长的字段
                            for key, value in row_dict.items():
                                if isinstance(value, datetime):
                                    sample[key] = value.isoformat()
                                elif isinstance(value, str) and len(value) > max_field_length:
                                    # 处理过长的字符串，替换为占位符
                                    truncated_value = value[:max_field_length]
                                    sample[key] = f"{truncated_value}... [内容过长已截断，完整长度:{len(value)}]"
                                elif hasattr(value, '__str__') and not isinstance(value, (int, float, bool, str, type(None))):
                                    # 处理其他类型转为字符串，并检查长度
                                    str_value = str(value)
                                    if len(str_value) > max_field_length:
                                        truncated_value = str_value[:max_field_length]
                                        sample[key] = f"{truncated_value}... [内容过长已截断，完整长度:{len(str_value)}]"
                                    else:
                                        sample[key] = str_value
                                else:
                                    sample[key] = value
                        except Exception as e:
                            # 备用方法
                            logger.warning(f"处理样本数据行时出错，使用备用方法：{str(e)}")
                            try:
                                # 获取结果集的列名
                                columns = sample_result.keys()
                                for i, col_name in enumerate(columns):
                                    value = row[i]
                                    if isinstance(value, datetime):
                                        sample[col_name] = value.isoformat()
                                    elif isinstance(value, str) and len(value) > max_field_length:
                                        # 处理过长的字符串
                                        truncated_value = value[:max_field_length]
                                        sample[col_name] = f"{truncated_value}... [内容过长已截断，完整长度:{len(value)}]"
                                    elif hasattr(value, '__str__') and not isinstance(value, (int, float, bool, str, type(None))):
                                        # 处理其他类型转为字符串，并检查长度
                                        str_value = str(value)
                                        if len(str_value) > max_field_length:
                                            truncated_value = str_value[:max_field_length]
                                            sample[col_name] = f"{truncated_value}... [内容过长已截断，完整长度:{len(str_value)}]"
                                        else:
                                            sample[col_name] = str_value
                                    else:
                                        sample[col_name] = value
                            except Exception as inner_e:
                                logger.error(f"处理样本数据失败: {str(e)} -> {str(inner_e)}")
                        
                        samples.append(sample)
                except Exception as e:
                    logger.error(f"获取表 {table_name} 的样本数据失败: {str(e)}")
                    samples = []
                
                return {
                    "schema": schema_dict,  # 现在返回字典而不是列表
                    "sample_data": samples,
                    "table_comment": table_comment  # 添加表注释
                }
        except Exception as e:
            logger.error(f"获取表 {table_name} 的结构和样本数据失败: {str(e)}")
            return {"error": str(e)}
    
    @staticmethod
    def save_selected_tables(
        db: Session, 
        data_source_id: str, 
        selected_tables: List[str],
        data_source_type: str,
        data_source_config: Dict[str, Any],
        max_field_length: int = 200
    ) -> List[Dict[str, Any]]:
        """保存选定的表及其结构和样本数据
        
        Args:
            db: 数据库会话
            data_source_id: 数据源ID
            selected_tables: 选定的表名列表
            data_source_type: 数据源类型
            data_source_config: 数据源配置
            max_field_length: 字段值的最大长度，超过此长度将被截断
            
        Returns:
            List[Dict]: 保存结果列表
        """
        results = []
        
        # 先检查数据源是否存在
        from sqlalchemy import text
        try:
            # 使用参数化查询检查数据源存在性
            check_query = text("SELECT id FROM data_sources WHERE id = :id")
            result = db.execute(check_query, {"id": data_source_id}).fetchone()
            if not result:
                return [{
                    "table_name": table_name,
                    "success": False,
                    "message": f"数据源ID {data_source_id} 不存在"
                } for table_name in selected_tables]
                
            # 直接从主会话中验证数据源对象存在性
            datasource = db.query(DataSource).filter(DataSource.id == data_source_id).first()
            if not datasource:
                return [{
                    "table_name": table_name,
                    "success": False,
                    "message": f"数据源ID {data_source_id} 不存在或无法访问"
                } for table_name in selected_tables]
        except Exception as e:
            logger.error(f"检查数据源时出错: {str(e)}")
            return [{
                "table_name": table_name,
                "success": False,
                "message": f"检查数据源时出错: {str(e)}"
            } for table_name in selected_tables]
        
        for table_name in selected_tables:
            try:
                # 获取表信息
                table_info = DataSourceService.get_table_info(
                    data_source_type,
                    data_source_config,
                    table_name,
                    max_field_length
                )
                
                if "error" in table_info:
                    results.append({
                        "table_name": table_name,
                        "success": False,
                        "message": table_info["error"]
                    })
                    continue
                
                # 检查表是否已存在，使用主会话
                existing_table = db.query(SelectedTable).filter(
                    SelectedTable.data_source_id == data_source_id,
                    SelectedTable.table_name == table_name
                ).first()
                
                if existing_table:
                    # 更新现有表
                    existing_table.table_schema = table_info["schema"]
                    existing_table.sample_data = table_info["sample_data"]
                    # 如果获取到了表注释，且现有表的描述为空，则更新描述
                    table_comment = table_info.get("table_comment", "")
                    if table_comment and not existing_table.table_description:
                        existing_table.table_description = table_comment
                    existing_table.updated_at = get_shanghai_time()
                    
                    # 使用主会话提交
                    db.commit()
                    
                    results.append({
                        "table_name": table_name,
                        "success": True,
                        "message": "表信息已更新",
                        "id": existing_table.id
                    })
                else:
                    # 创建新表记录
                    table_id = str(uuid.uuid4())
                    
                    # 获取表注释作为描述
                    table_comment = table_info.get("table_comment", "")
                    
                    # 创建新表对象
                    new_table = SelectedTable(
                        id=table_id,
                        data_source_id=data_source_id,
                        table_name=table_name,
                        table_description=table_comment,  # 使用获取到的表注释
                        table_schema=table_info["schema"],
                        sample_data=table_info["sample_data"]
                    )
                    
                    try:
                        # 尝试添加新表并使用主会话
                        db.add(new_table)
                        db.commit()
                        
                        results.append({
                            "table_name": table_name,
                            "success": True,
                            "message": "表信息已保存",
                            "id": table_id
                        })
                    except Exception as inner_e:
                        # 回滚主会话
                        db.rollback()
                        logger.error(f"保存表 {table_name} 时数据库错误: {str(inner_e)}")
                        
                        results.append({
                            "table_name": table_name,
                            "success": False,
                            "message": f"数据库错误: {str(inner_e)}"
                        })
            except Exception as e:
                # 回滚主会话
                db.rollback()
                logger.error(f"保存表 {table_name} 信息失败: {str(e)}")
                results.append({
                    "table_name": table_name,
                    "success": False,
                    "message": f"保存失败: {str(e)}"
                })
                
        return results 