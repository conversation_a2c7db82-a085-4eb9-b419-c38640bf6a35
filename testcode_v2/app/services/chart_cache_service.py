"""
图表缓存服务
============
基于Redis的历史图表缓存系统，用于多轮对话中快速提取历史图表信息
"""

import json
import time
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

from app.db.redis import RedisClient
from app.core.logger import log


class ChartCacheService:
    """
    图表缓存服务
    
    功能实现: 基于Redis的历史图表缓存和查询系统
    实现方案: 使用Redis List存储按conversation_id分组的图表信息
    影响范围: app/services/chart_cache_service.py (新增文件)
    实现日期: 2025-01-12
    """
    
    def __init__(self):
        self.redis_client = RedisClient
        self.cache_prefix = "chart_history:"
        self.default_ttl = 7 * 24 * 3600  # 7天过期时间（秒）
        self.max_charts_per_conversation = 50  # 每个对话最多缓存50个图表
        
    def _get_cache_key(self, conversation_id: str) -> str:
        """获取对话图表缓存的Redis键名"""
        return f"{self.cache_prefix}{conversation_id}"
    
    def store_chart(
        self,
        conversation_id: str,
        analysis_id: str,
        step_id: str,
        round_number: int,
        chart_info: Dict[str, Any]
    ) -> bool:
        """
        存储图表信息到Redis缓存
        
        Args:
            conversation_id: 对话ID
            analysis_id: 分析ID
            step_id: 步骤ID
            round_number: 轮次
            chart_info: 图表信息字典，包含title, chart_type, config等
            
        Returns:
            bool: 是否存储成功
        """
        try:
            if not conversation_id:
                log.warning("conversation_id为空，跳过图表缓存")
                return False
                
            cache_key = self._get_cache_key(conversation_id)
            
            # 构建缓存数据
            cache_data = {
                "conversation_id": conversation_id,
                "analysis_id": analysis_id,
                "step_id": step_id,
                "round_number": round_number,
                "chart_info": chart_info,
                "cached_at": time.time(),
                "created_at": datetime.now().isoformat()
            }
            
            # 序列化数据
            serialized_data = json.dumps(cache_data, ensure_ascii=False)
            
            # 使用Redis List存储（左推入，保持时间顺序）
            client = self.redis_client.get_client()
            client.lpush(cache_key, serialized_data)
            
            # 限制列表长度，防止无限增长
            client.ltrim(cache_key, 0, self.max_charts_per_conversation - 1)
            
            # 设置过期时间
            client.expire(cache_key, self.default_ttl)
            
            log.info(f"✅ 图表已缓存到Redis: {conversation_id} -> {chart_info.get('title', '未命名图表')}")
            return True
            
        except Exception as e:
            log.error(f"❌ 存储图表缓存失败: {str(e)}")
            return False
    
    def store_charts_batch(
        self,
        conversation_id: str,
        analysis_id: str,
        step_id: str,
        round_number: int,
        charts_list: List[Dict[str, Any]]
    ) -> int:
        """
        批量存储多个图表信息
        
        Args:
            conversation_id: 对话ID
            analysis_id: 分析ID
            step_id: 步骤ID
            round_number: 轮次
            charts_list: 图表信息列表
            
        Returns:
            int: 成功存储的图表数量
        """
        try:
            if not conversation_id or not charts_list:
                return 0
                
            success_count = 0
            for i, chart_info in enumerate(charts_list):
                # 为每个图表生成唯一的步骤ID
                chart_step_id = f"{step_id}_chart_{i+1}"
                
                if self.store_chart(
                    conversation_id=conversation_id,
                    analysis_id=analysis_id,
                    step_id=chart_step_id,
                    round_number=round_number,
                    chart_info=chart_info
                ):
                    success_count += 1
            
            log.info(f"✅ 批量缓存完成: {success_count}/{len(charts_list)} 个图表")
            return success_count
            
        except Exception as e:
            log.error(f"❌ 批量存储图表缓存失败: {str(e)}")
            return 0
    
    def get_conversation_charts(
        self,
        conversation_id: str,
        max_charts: int = 20,
        exclude_analysis_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        获取对话的历史图表信息
        
        Args:
            conversation_id: 对话ID
            max_charts: 最大返回图表数量
            exclude_analysis_id: 排除的分析ID（通常是当前分析）
            
        Returns:
            List[Dict[str, Any]]: 历史图表信息列表
        """
        try:
            if not conversation_id:
                return []
                
            cache_key = self._get_cache_key(conversation_id)
            client = self.redis_client.get_client()
            
            # 获取缓存的图表数据
            cached_items = client.lrange(cache_key, 0, max_charts * 2)  # 多取一些，以防需要过滤
            
            if not cached_items:
                log.info(f"📊 对话 {conversation_id} 没有缓存的历史图表")
                return []
            
            charts = []
            for item in cached_items:
                try:
                    cache_data = json.loads(item)
                    
                    # 排除指定的分析ID
                    if exclude_analysis_id and cache_data.get('analysis_id') == exclude_analysis_id:
                        continue
                    
                    # 提取图表信息
                    chart_info = cache_data.get('chart_info', {})
                    
                    # 构建标准格式的图表信息
                    chart_record = {
                        "step_id": cache_data.get('step_id'),
                        "analysis_id": cache_data.get('analysis_id'),
                        "round_number": cache_data.get('round_number'),
                        "chart_type": chart_info.get('chart_type', 'echarts'),
                        "title": chart_info.get('title', '历史图表'),
                        "description": chart_info.get('description', ''),
                        "data_mapping": chart_info.get('data_mapping', {}),
                        "created_at": cache_data.get('created_at'),
                        "chart_id": chart_info.get('chart_id'),
                        "cached_at": cache_data.get('cached_at')
                    }
                    
                    charts.append(chart_record)
                    
                    if len(charts) >= max_charts:
                        break
                        
                except json.JSONDecodeError as e:
                    log.warning(f"⚠️ 解析缓存图表数据失败: {str(e)}")
                    continue
            
            log.info(f"📊 从Redis缓存获取到 {len(charts)} 个历史图表")
            return charts
            
        except Exception as e:
            log.error(f"❌ 获取对话图表缓存失败: {str(e)}")
            return []
    
    def clear_conversation_charts(self, conversation_id: str) -> bool:
        """
        清除对话的所有图表缓存
        
        Args:
            conversation_id: 对话ID
            
        Returns:
            bool: 是否清除成功
        """
        try:
            if not conversation_id:
                return False
                
            cache_key = self._get_cache_key(conversation_id)
            success = self.redis_client.delete(cache_key)
            
            if success:
                log.info(f"✅ 已清除对话 {conversation_id} 的图表缓存")
            else:
                log.warning(f"⚠️ 清除对话 {conversation_id} 的图表缓存失败")
                
            return success
            
        except Exception as e:
            log.error(f"❌ 清除图表缓存失败: {str(e)}")
            return False
    
    def get_cache_stats(self, conversation_id: str) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        Args:
            conversation_id: 对话ID
            
        Returns:
            Dict[str, Any]: 缓存统计信息
        """
        try:
            cache_key = self._get_cache_key(conversation_id)
            client = self.redis_client.get_client()
            
            # 获取列表长度
            list_length = client.llen(cache_key)
            
            # 获取TTL
            ttl = client.ttl(cache_key)
            
            stats = {
                "conversation_id": conversation_id,
                "cached_charts_count": list_length,
                "cache_ttl_seconds": ttl,
                "cache_expires_at": (datetime.now() + timedelta(seconds=ttl)).isoformat() if ttl > 0 else None,
                "cache_key": cache_key
            }
            
            return stats
            
        except Exception as e:
            log.error(f"❌ 获取缓存统计失败: {str(e)}")
            return {"error": str(e)}


# 创建全局图表缓存服务实例
chart_cache_service = ChartCacheService() 