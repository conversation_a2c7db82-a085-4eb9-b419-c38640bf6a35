"""
多轮分析器 - 专门处理多轮对话分析
================================
基于现有的LLM分析器，增强上下文感知能力
"""

import json
from app.core.logger import log
import re
from typing import AsyncGenerator, Dict, Any, Optional, List
from datetime import datetime
from sqlalchemy.orm import Session

from app.services.llm_analyzer import LLMStreamAnalyzer
from app.services.chart_generation import ChartGenerationAgent
from app.services.conversation_service import ConversationService
from app.services.analysis_step_service import AnalysisStepService
from app.services.step_interceptor import StepInterceptor
from app.models.analysis import Analysis, ToolExecution
from app.schemas.conversation import ConversationContextData
from app.services.llm_factory import get_llm_service
from app.core.config import settings
from openai import AsyncOpenAI

class MultiRoundLLMAnalyzer(LLMStreamAnalyzer):
    """多轮分析专用的LLM分析器 - 使用LLM智能上下文整合"""
    
    def __init__(self, db: Session):
        super().__init__(db)
        self.conversation_service = ConversationService(db)
        self.step_service = AnalysisStepService(db)
        # 使用LLM工厂获取服务
        self.llm_service = get_llm_service(db)
        # 为了兼容现有代码，保留原有的客户端接口
        if hasattr(self.llm_service, 'client'):
            self.llm_client = self.llm_service.client
        else:
            # 如果没有client属性，创建一个默认的
            self.llm_client = AsyncOpenAI(
                api_key=settings.OPENAI_API_KEY,
                base_url=settings.OPENAI_BASE_URL
            )
        # 获取模型名称
        if hasattr(self.llm_service, 'model'):
            self.model = self.llm_service.model
        else:
            self.model = settings.OPENAI_MODEL
        # 获取模型ID（用于Token统计）
        if hasattr(self.llm_service, 'model_id'):
            self.model_id = self.llm_service.model_id
        else:
            self.model_id = None
            log.warning("LLM服务未提供model_id，Token统计可能无法正常工作")
        
        # 重新初始化图表生成Agent，确保父类使用正确的实例
        self.chart_agent = ChartGenerationAgent(db)
        log.info(f"多轮分析器图表Agent初始化完成: {self.chart_agent is not None}")
    
    def _format_tools_for_prompt(self, tools: List[Dict[str, Any]]) -> str:
        """格式化工具信息用于提示"""
        tool_descriptions = []
        for tool in tools:
            tool_descriptions.append(f"- {tool['name']}: {tool['description']}")
        return '\n'.join(tool_descriptions)
    
    async def generate_conversation_stream(
        self, 
        conversation_id: str,
        query: str, 
        round_number: int,
        project_id: str,
        user_id: str,
        background_tasks,
        max_planning_rounds: int = 25,
        use_full_context: bool = True,
        context_depth: int = 5,
        continue_analysis_id: Optional[str] = None,
        intent_adjustment: Optional[str] = None
    ) -> AsyncGenerator[str, None]:
        """生成多轮对话的流式分析响应 - 使用LLM智能上下文整合"""
        
        log.info(f"开始多轮分析: 会话{conversation_id}, 轮次{round_number}, 查询: {query}")
        
        try:
            # 1. 获取并整合历史上下文
            context_summary = ""
            
            if round_number > 1 and use_full_context:
                try:
                    # 获取历史分析数据
                    historical_data = await self._get_historical_analysis_data(
                        conversation_id, max_rounds=context_depth
                    )
                    
                    if historical_data:
                        # 使用LLM生成纯文本摘要
                        context_summary = await self._llm_extract_relevant_context(
                            current_query=query,
                            historical_data=historical_data
                        )
                        
                        if context_summary:
                            log.info(f"LLM上下文摘要生成完成: 会话{conversation_id}, 摘要长度: {len(context_summary)}")
                        else:
                            log.info(f"LLM判断当前查询为新话题，不使用历史上下文")
                    
                except Exception as e:
                    log.warning(f"获取或整合历史上下文失败: {str(e)}")
            
            # # 2. 发送上下文加载事件
            # yield self._format_event("context_loaded", {
            #     "conversation_id": conversation_id,
            #     "round_number": round_number,
            #     "context_summary": context_summary[:200] + "..." if context_summary and len(context_summary) > 200 else context_summary or "",
            #     "context_method": "llm_simple_summary",
            #     "has_context": bool(context_summary)
            # })
            # 3. 执行分析（传递原始查询和上下文数据）
            analysis_id = None
            step_interceptor = None
            
            # 创建一个事件缓冲区，用于在步骤拦截器初始化前缓存事件
            event_buffer = []
            
            async for event in self.generate_stream(
                query=query,  # 保持原始查询
                project_id=project_id,
                user_id=user_id,
                background_tasks=background_tasks,
                max_planning_rounds=max_planning_rounds,
                conversation_id=conversation_id,
                round_number=round_number,
                history_context_data={"context_summary": context_summary} if context_summary else None,  # 构造简单字典
                continue_analysis_id=continue_analysis_id,  # 传递继续分析ID
                intent_adjustment=intent_adjustment  # 传递意图补充信息
            ):
                # @fixed_implementation_start
                # 实现标识: 多轮分析步骤拦截器初始化修复
                # 功能描述: 修复continue_analysis_id场景下步骤拦截器无法初始化的问题
                # 修复内容: 当使用continue_analysis_id时，直接使用该ID初始化步骤拦截器，不等待analysis_created事件
                # 状态: 已确认 (CONFIRMED_IMPLEMENTATION)
                # @fixed_implementation_end
                
                # 如果使用continue_analysis_id且步骤拦截器未初始化，直接初始化
                if continue_analysis_id and continue_analysis_id.strip() and not step_interceptor and not analysis_id:
                    try:
                        analysis_id = continue_analysis_id
                        step_interceptor = StepInterceptor(self.db, analysis_id)
                        log.info(f"使用continue_analysis_id初始化步骤拦截器: {analysis_id}")
                        
                        # 处理缓冲区中的事件
                        for buffered_event in event_buffer:
                            try:
                                buffered_event_data = json.loads(buffered_event.split('data: ')[1])
                                buffered_event_type = buffered_event_data.get('event')
                                buffered_event_payload = buffered_event_data.get('data', {})
                                
                                if buffered_event_type:
                                    step_interceptor.intercept_event(buffered_event_type, buffered_event_payload)
                                    log.debug(f"处理缓冲事件: {buffered_event_type}")
                            except Exception as e:
                                log.warning(f"处理缓冲事件失败: {str(e)}")
                        
                        # 清空缓冲区
                        event_buffer.clear()
                        
                        # 更新分析记录信息
                        analysis = self.db.query(Analysis).filter(
                            Analysis.id == analysis_id
                        ).first()
                        if analysis:
                            # 检查是否为意图确认，如果是则保存原始查询
                            query_to_save = query
                            
                            # 如果query是意图确认的默认文本，尝试从已有的analysis记录中保持原始查询
                            if query.strip() in ['确认', '确认按照计划执行分析', '按计划执行', '继续', '开始分析']:
                                # 保持原有的query不变，因为这是意图确认而不是新的查询
                                log.info(f"检测到意图确认文本，保持原始查询: {analysis.query}")
                                query_to_save = analysis.query  # 保持原有查询不变
                            else:
                                # 正常的查询更新
                                query_to_save = query
                            
                            analysis.query = query_to_save
                            analysis.round_number = round_number
                            analysis.context_summary = context_summary
                            self.db.commit()
                            log.info(f"更新分析记录: {analysis_id}, 轮次: {round_number}, 查询: {query_to_save}")
                            self.db.refresh(analysis)
                    except Exception as e:
                        log.error(f"使用continue_analysis_id初始化步骤拦截器失败: {str(e)}")
                
                # 拦截分析创建事件，获取分析ID并初始化步骤拦截器（新分析场景）
                if 'analysis_created' in event and '"event"' in event:
                    try:
                        event_data = json.loads(event.split('data: ')[1])
                        analysis_id = event_data.get('data', {}).get('id')
                        if analysis_id and not step_interceptor:
                            # 初始化步骤拦截器
                            step_interceptor = StepInterceptor(self.db, analysis_id)
                            log.info(f"通过analysis_created事件初始化步骤拦截器: {analysis_id}")
                            
                            # 处理缓冲区中的事件
                            for buffered_event in event_buffer:
                                try:
                                    buffered_event_data = json.loads(buffered_event.split('data: ')[1])
                                    buffered_event_type = buffered_event_data.get('event')
                                    buffered_event_payload = buffered_event_data.get('data', {})
                                    
                                    if buffered_event_type:
                                        step_interceptor.intercept_event(buffered_event_type, buffered_event_payload)
                                        log.debug(f"处理缓冲事件: {buffered_event_type}")
                                except Exception as e:
                                    log.warning(f"处理缓冲事件失败: {str(e)}")
                            
                            # 清空缓冲区
                            event_buffer.clear()
                            
                            # 处理当前的analysis_created事件
                            step_interceptor.intercept_event('analysis_created', event_data.get('data', {}))
                            
                            analysis = self.db.query(Analysis).filter(
                                Analysis.id == analysis_id
                            ).first()
                            if analysis:
                                # 检查是否为意图确认，如果是则保存原始查询
                                query_to_save = query
                                
                                # 如果query是意图确认的默认文本，尝试从已有的analysis记录中保持原始查询
                                if query.strip() in ['确认', '确认按照计划执行分析', '按计划执行', '继续', '开始分析']:
                                    # 保持原有的query不变，因为这是意图确认而不是新的查询
                                    log.info(f"检测到意图确认文本，保持原始查询: {analysis.query}")
                                    query_to_save = analysis.query  # 保持原有查询不变
                                else:
                                    # 正常的查询更新
                                    query_to_save = query
                                
                                analysis.query = query_to_save
                                analysis.round_number = round_number
                                analysis.context_summary = context_summary
                                self.db.commit()
                                log.info(f"更新分析记录: {analysis_id}, 轮次: {round_number}, 查询: {query_to_save}")
                                self.db.refresh(analysis)
                    except Exception as e:
                        log.error(f"更新分析记录失败: {str(e)}")
                
                # 处理其他事件（无论步骤拦截器是否已初始化）
                if 'data: ' in event and event != '':
                    try:
                        event_data = json.loads(event.split('data: ')[1])
                        event_type = event_data.get('event')
                        event_payload = event_data.get('data', {})
                        
                        # 特殊处理意图确认完成事件，更新数据库中的原始查询
                        if event_type == 'intent_confirmation_completed' and analysis_id:
                            try:
                                original_query = event_payload.get('original_query')
                                if original_query:
                                    analysis = self.db.query(Analysis).filter(
                                        Analysis.id == analysis_id
                                    ).first()
                                    if analysis:
                                        analysis.query = original_query
                                        self.db.commit()
                                        log.info(f"意图确认完成，更新为原始查询: {original_query}")
                                        self.db.refresh(analysis)
                            except Exception as e:
                                log.error(f"处理意图确认完成事件失败: {str(e)}")
                        
                        if event_type and event_type != 'analysis_created':  # analysis_created已在上面处理
                            if step_interceptor:
                                # 步骤拦截器已初始化，直接处理
                                step_interceptor.intercept_event(event_type, event_payload)
                                log.debug(f"处理事件: {event_type}")
                            else:
                                # 步骤拦截器未初始化，缓存事件
                                event_buffer.append(event)
                                log.debug(f"缓存事件等待步骤拦截器初始化: {event_type}")
                    except Exception as e:
                        log.warning(f"事件处理失败: {str(e)}")
                
                yield event
            
            # 4. 分析完成后的处理
            if analysis_id:
                try:
                    # 🔧 简化：只更新分析记录，不再使用复杂的上下文存储
                    analysis = self.db.query(Analysis).filter(Analysis.id == analysis_id).first()
                    
                    if analysis and analysis.status == 'completed':
                        # 更新分析记录的上下文摘要（如果有的话）
                        if context_summary:
                            analysis.context_summary = context_summary
                            self.db.commit()
                            self.db.refresh(analysis)
                        
                        log.info(f"轮次分析完成: {conversation_id}, 轮次: {round_number}")
                            
                        # 发送分析完成事件
                        yield self._format_event("analysis_completed", {
                            "conversation_id": conversation_id,
                            "round_number": round_number,
                            "analysis_id": analysis_id,
                            "has_context": bool(context_summary)
                        })
                        
                except Exception as e:
                    log.error(f"分析完成后处理失败: {str(e)}")
            
            log.info(f"多轮分析流程完成: {conversation_id}, 轮次: {round_number}")
        
        except Exception as e:
            log.error(f"多轮分析流程异常: {str(e)}")
            yield self._format_event("error", {
                "conversation_id": conversation_id,
                "round_number": round_number,
                "error": str(e)
            })
    
    async def _get_historical_analysis_data(
        self, 
        conversation_id: str, 
        max_rounds: int = 5
    ) -> Optional[List[Dict[str, Any]]]:
        """获取历史分析数据 - 极简版本，直接返回原始数据"""
        try:
            # 🔧 极简：直接使用ConversationService的get_conversation_analyses方法
            analyses_data = self.conversation_service.get_conversation_analyses(conversation_id)
            
            if not analyses_data:
                return None
            
            # 只取最近的几轮分析，直接返回原始数据
            recent_analyses = analyses_data[-max_rounds:] if len(analyses_data) > max_rounds else analyses_data
            
            return recent_analyses
            
        except Exception as e:
            log.error(f"获取历史分析数据失败: {str(e)}")
            return None

    async def _llm_extract_relevant_context(
        self, 
        current_query: str, 
        historical_data: List[Dict[str, Any]]
    ) -> Optional[str]:
        """使用LLM生成上下文摘要 - 完全不处理原始数据版本"""
        try:
            # 🔧 完全不处理：直接将完整的historical_data转为字符串传给大模型
            import json
            historical_text = json.dumps(historical_data, ensure_ascii=False, indent=2)
            
            # 系统提示词
            system_prompt = """
# 角色与目标
你是一个具备全局视角的顶级对话分析AI，专注于从用户的多轮分析对话中提炼上下文摘要。

你的任务是：先判断当前用户的问题是否与历史对话分析内容相关；如果相关，则生成一个能够支持下一轮分析推理的**结构化摘要**。该摘要应帮助系统快速理解用户的最终分析目标、实现路径和已知结论。

# 输入说明
你将收到以下两部分信息：
1. 【完整对话历史】：包含从第一轮到当前的所有交互，包括 user_input（用户输入）、tool_code（工具执行）和 final_response（系统回复），按时间顺序排列；
2. 【当前用户提问】：最新一轮的用户提问。

# 判断标准
- “相关”：当前问题在主题、目标或数据维度上与历史分析内容具有延续性或演进关系；
- “不相关”：当前问题完全更换了分析目标、主题或数据主体，与历史内容无直接联系。

# 输出要求

## 如果当前提问与历史上下文不相关，请仅输出：
无相关上下文

## 如果相关，请严格按照以下格式输出（不要添加多余解释）：

【用户意图】
xxx

【分析路径】
1. xxx
2. xxx
3. xxx

【最终结论】
xxx

# 重要提示
- 始终使用中文回复
- 输出必须符合上述格式要求
- 不要添加任何额外的修饰、解释或Markdown标记
- 始终使用中文回复
"""

            # 🔧 极简：让大模型直接处理完整的原始数据
            prompt = f"""
## 完整历史分析数据（包含所有工具执行结果）
{historical_text}

## 当前用户查询
{current_query}
"""
            print('--------------------------------')
            print(prompt)
            print('--------------------------------')
            # 调用LLM生成摘要
            completion = await self.llm_client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=300,
                temperature=0.1
            )
            
            summary_text = completion.choices[0].message.content.strip()
            
            # 直接返回摘要文本
            if summary_text and summary_text != "无相关上下文":
                log.info(f"LLM生成上下文摘要成功，长度：{len(summary_text)}")
                return summary_text
            else:
                log.info("LLM判断当前查询与历史上下文不相关")
                return None
                
        except Exception as e:
            log.error(f"LLM上下文摘要生成失败: {str(e)}")
            return None

    def _format_event(self, event_type: str, data: Dict[str, Any]) -> str:
        """格式化事件数据"""
        event_data = {
            "event": event_type,
            "data": data
        }
        return f"data: {json.dumps(event_data, ensure_ascii=False)}\n\n" 
