import uuid
import logging
from sqlalchemy.orm import Session
from app.models.token_usage import TokenUsage
from app.models.organization import Organization
from app.db.session import SessionLocal

logger = logging.getLogger(__name__)


def _get_db():
    """工具函数，用于获取独立的数据库会话。"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def record_usage_task(
    user_id: str,
    org_id: int,
    project_id: str,
    analysis_id: str,
    model_id: str,
    model_name: str,
    prompt_tokens: int = 0,
    completion_tokens: int = 0,
    total_tokens: int = 0
):
    """
    后台任务函数：记录并聚合Token用量。
    此函数独立运行，自己管理数据库会话。
    
    Args:
        user_id: 用户ID
        org_id: 组织ID
        project_id: 项目ID
        analysis_id: 分析ID
        model_id: 模型ID
        model_name: 模型名称
        prompt_tokens: 输入Token数量
        completion_tokens: 输出Token数量
        total_tokens: 总Token数量
    """
    db: Session = next(_get_db())
    try:
        # 计算实际使用的总Token数
        actual_total = total_tokens or (prompt_tokens + completion_tokens)
        
        if actual_total == 0:
            logger.debug(f"Token用量为0，跳过记录")
            return

        # 1. 记录用量明细
        token_usage_record = TokenUsage(
            id=str(uuid.uuid4()),
            user_id=user_id,
            org_id=org_id,
            project_id=project_id,
            analysis_id=analysis_id,
            model_id=model_id,
            model_name=model_name,
            prompt_tokens=prompt_tokens,
            completion_tokens=completion_tokens,
            total_tokens=actual_total,
        )
        db.add(token_usage_record)

        # 2. 聚合用量到组织的累计总数
        # 使用 with_for_update() 来锁定行，防止并发更新问题
        org = db.query(Organization).filter(Organization.id == org_id).with_for_update().first()
        if org:
            # 累加到 total_token_usage 字段
            org.total_token_usage += actual_total
            logger.info(f"组织 {org_id} Token用量更新: +{actual_total} (输入:{prompt_tokens}, 输出:{completion_tokens}), 总计: {org.total_token_usage}")
        else:
            logger.warning(f"无法为Token记录找到组织: {org_id}")

        db.commit()
        logger.info(f"成功异步记录Token用量 for org {org_id}: {actual_total} tokens (输入:{prompt_tokens}, 输出:{completion_tokens})")
        
    except Exception as e:
        logger.error(f"后台记录Token用量失败: {e}")
        db.rollback()
        raise
    finally:
        db.close()


class TokenUsageService:
    """Token用量管理服务"""
    
    def __init__(self, db: Session):
        self.db = db

    def has_tokens_remaining(self, org_id: int) -> bool:
        """
        检查组织是否还有可用的Token额度。
        这是一个读操作，可以保持同步快速执行。
        
        Args:
            org_id: 组织ID
            
        Returns:
            bool: 如果还有可用额度返回True，否则返回False
        """
        try:
            org = self.db.query(Organization).filter(Organization.id == org_id).first()
            if not org:
                logger.warning(f"检查Token限额时未找到组织: {org_id}")
                return True  # 如果没找到组织，默认不限制
            
            # 使用 total_token_usage 进行比较
            remaining = org.token_limit - org.total_token_usage
            logger.debug(f"组织 {org_id} Token检查: 已用={org.total_token_usage}, 限额={org.token_limit}, 剩余={remaining}")
            
            return org.total_token_usage < org.token_limit
            
        except Exception as e:
            logger.error(f"检查Token限额时出错: {e}")
            return True  # 出错时默认不限制，避免阻塞用户
    
    def get_usage_stats(self, org_id: int) -> dict:
        """
        获取组织的Token用量统计信息。
        
        Args:
            org_id: 组织ID
            
        Returns:
            dict: 包含用量统计的字典
        """
        try:
            org = self.db.query(Organization).filter(Organization.id == org_id).first()
            if not org:
                return {
                    "total_usage": 0,
                    "token_limit": 0,
                    "remaining": 0,
                    "usage_percentage": 0.0
                }
            
            remaining = max(0, org.token_limit - org.total_token_usage)
            usage_percentage = (org.total_token_usage / org.token_limit * 100) if org.token_limit > 0 else 0.0
            
            return {
                "total_usage": org.total_token_usage,
                "token_limit": org.token_limit,
                "remaining": remaining,
                "usage_percentage": round(usage_percentage, 2)
            }
            
        except Exception as e:
            logger.error(f"获取用量统计时出错: {e}")
            return {
                "total_usage": 0,
                "token_limit": 0,
                "remaining": 0,
                "usage_percentage": 0.0
            } 