from typing import Dict, Type
import enum

from app.services.executor import ToolExecutor
from app.services.executor.sql_executor import SQLExecutor
from app.services.executor.api_executor import APIExecutor
from app.services.executor.python_executor import PythonExecutor
from app.services.executor.auto_sql_executor import AutoSQLExecutor
from app.models.tool import ToolType

class ExecutorFactory:
    """工具执行器工厂"""
    
    _executors: Dict[str, Type[ToolExecutor]] = {
        "SQL": SQLExecutor,
        "API": APIExecutor,
        "PYTHON": PythonExecutor,
        "GRAPH": SQLExecutor,  # 图查询也使用SQL执行器
        "VECTOR": SQLExecutor,  # 向量检索也使用SQL执行器
        "CUSTOM": SQLExecutor,  # 自定义工具默认使用SQL执行器
        "AUTO_SQL": AutoSQLExecutor # 自动SQL生成执行器
    }
    
    @classmethod
    def get_executor(cls, tool_type: str) -> ToolExecutor:
        """
        获取执行器
        
        Args:
            tool_type: 工具类型
            
        Returns:
            工具执行器
        
        Raises:
            ValueError: 如果不支持的工具类型
        """
        print(f"获取执行器，工具类型: {tool_type}, 类型: {type(tool_type)}")
        
        # 确保工具类型是字符串
        if tool_type is None:
            print("工具类型为空，使用SQL作为默认")
            return cls._executors["SQL"]()
            
        if isinstance(tool_type, enum.Enum):
            tool_type = tool_type.value
            
        # 转换为大写字符串以便查找
        if isinstance(tool_type, str):
            tool_type = tool_type.upper()
        
        print(f"规范化后的工具类型: {tool_type}")
        
        # 直接查找字典
        executor_class = cls._executors.get(tool_type)
        if not executor_class:
            print(f"未找到匹配的执行器类型: {tool_type}，使用SQL作为默认")
            return cls._executors["SQL"]()
            
        print(f"找到执行器: {executor_class.__name__}")
        return executor_class()
    
    @classmethod
    def get_executor_with_format(cls, tool_type: str) -> ToolExecutor:
        """
        获取支持结果格式化的执行器
        
        Args:
            tool_type: 工具类型
            
        Returns:
            支持结果格式化的工具执行器
        """
        executor = cls.get_executor(tool_type)
        # 为executor添加格式化功能的包装方法
        executor.original_execute = executor.execute
        # 不需要重新赋值execute，使用original_execute和execute_and_format方法
        return executor
    
    @classmethod
    def register_executor(cls, tool_type: str, executor_class: Type[ToolExecutor]) -> None:
        """
        注册执行器
        
        Args:
            tool_type: 工具类型
            executor_class: 执行器类
        """
        if isinstance(tool_type, enum.Enum):
            tool_type = tool_type.value
            
        if isinstance(tool_type, str):
            tool_type = tool_type.upper()
            
        cls._executors[tool_type] = executor_class 