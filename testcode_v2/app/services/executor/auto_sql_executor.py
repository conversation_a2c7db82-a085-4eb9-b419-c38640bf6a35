import logging
import json
from typing import Dict, Any, List, Optional, Tuple, Union
from sqlalchemy.orm import Session
from sqlalchemy import text, inspect, create_engine, select
from sqlalchemy.engine import Engine
from sqlalchemy.ext.asyncio import AsyncEngine
from sqlalchemy.orm import sessionmaker
import time
import os
import re
import cx_Oracle
import asyncio
import traceback
from pathlib import Path
from datetime import datetime
from app.utils.time_utils import get_shanghai_time
from app.services.executor import ToolExecutor
from app.models.tool import Tool, ToolType
from app.models.data_source import DataSource
from app.models.selected_table import SelectedTable
from app.db.session import get_db, SessionLocal, engine
from app.services.llm.openai_service import OpenAIService
from app.core.config import settings
from app.db.engines import engine_factory
from app.utils.json_utils import DecimalEncoder
from app.utils.oracle_init import init_oracle_client, reconnect_oracle, is_oracle_initialized, get_oracle_client_version
from app.core.logger import AnalysisLogger

# 配置日志
logger = logging.getLogger(__name__)

class AutoSQLExecutor(ToolExecutor):
    """自动SQL执行器
    
    基于用户意图自动生成并执行SQL查询，使用LLM进行SQL生成
    """
    
    def __init__(self, db: Optional[Session] = None):
        """初始化SQL执行器
        
        Args:
            db: 数据库会话，如果为None则会创建新的会话
        """
        super().__init__()
        self.db = db if db is not None else SessionLocal()
        self.openai_service = OpenAIService()
        self.schema_description = None
        self.current_db_type = None
        self.max_retries = 3
        self.retry_delay = 2  # 重试间隔秒数
        self.extra_params = {}  # 存储从前序步骤传递的参数
        logger.info("初始化AutoSQLExecutor完成")        
    
    def _load_schema_description(self, project_id: str) -> str:
        """根据项目ID加载数据库表结构描述（已废弃，保留用于兼容性）
        
        注意：此方法已不再使用，新的工具直接执行提供的SQL而不需要Schema描述
        
        Args:
            project_id: 项目ID
            
        Returns:
            str: 格式化的数据库Schema描述文本
        """
        try:
            # 获取项目关联的数据源
            data_source = self.db.query(DataSource).filter(DataSource.project_id == project_id).first()
            if not data_source:
                logger.error(f"项目 {project_id} 未找到关联的数据源")
                return self._get_default_schema_description()
            
            # 获取数据源ID
            data_source_id = data_source.id
            
            # 获取所有选定的表
            selected_tables = self.db.query(SelectedTable).filter(
                SelectedTable.data_source_id == data_source_id
            ).all()
            
            if not selected_tables or len(selected_tables) == 0:
                logger.warning(f"数据源 {data_source_id} 未找到选定的表")
                return self._get_default_schema_description()
            
            # 保存数据库类型
            self.current_db_type = data_source.type.value if hasattr(data_source.type, 'value') else str(data_source.type)
            logger.info(f"当前数据库类型: {self.current_db_type}")
            
            # 构建Schema描述
            schema_parts = ["# 数据库Schema描述\n"]
            
            for table in selected_tables:
                table_name = table.table_name
                table_description = table.table_description or ""
                table_schema = table.table_schema or {}
                sample_data = table.sample_data or []
                
                # 表描述
                schema_parts.append(f"\n## {table_name} 表")
                if table_description:
                    schema_parts.append(f"{table_description}\n")
                
                # 表结构
                for column_name, column_info in table_schema.items():
                    data_type = column_info.get('data_type', '未知类型')
                    nullable = column_info.get('nullable', 'YES')
                    nullable_text = "可为空" if nullable == "YES" else "非空"
                    description = column_info.get('description', '')
                    
                    column_desc = f"- {column_name}: {data_type}, {nullable_text}"
                    if description:
                        column_desc += f", {description}"
                    
                    schema_parts.append(column_desc)
                
                # 添加示例数据
                if sample_data and len(sample_data) > 0:
                    schema_parts.append("\n### 示例数据")
                    
                    # 确保sample_data是列表
                    if not isinstance(sample_data, list):
                        if isinstance(sample_data, dict):
                            sample_data = [sample_data]
                        else:
                            try:
                                sample_data = json.loads(sample_data)
                                if not isinstance(sample_data, list):
                                    sample_data = [sample_data]
                            except:
                                sample_data = []
                    
                    # 限制示例数据条数，避免提示过长
                    max_samples = 3
                    if len(sample_data) > max_samples:
                        sample_data = sample_data[:max_samples]
                    
                    # 添加示例数据行
                    for i, row in enumerate(sample_data):
                        schema_parts.append(f"行 {i+1}: {json.dumps(row, ensure_ascii=False, cls=DecimalEncoder)}")
            
            schema_description = "\n".join(schema_parts)
            logger.info(f"已生成数据库Schema描述，长度: {len(schema_description)}")
            
            return schema_description
            
        except Exception as e:
            logger.exception(f"生成Schema描述时发生错误: {str(e)}")
            return self._get_default_schema_description()
    
    def _get_default_schema_description(self) -> str:
        """返回默认的Schema描述
        
        当无法获取真实表结构时使用的默认描述
        
        Returns:
            str: 默认的Schema描述文本
        """
        return """
        # 数据库Schema描述
        
        ## 用户表 (users)
        - id: 主键，用户ID
        - name: 用户名
        - email: 用户电子邮件
        - created_at: 创建时间
        
        ## 项目表 (projects)
        - id: 主键，项目ID
        - name: 项目名称
        - description: 项目描述
        - owner_id: 外键，关联到users表的id字段
        - config: JSON格式的项目配置
        - created_at: 创建时间
        
        ## 分析记录表 (analysis)
        - id: 主键，分析记录ID
        - query: 查询内容
        - project_id: 外键，关联到projects表的id字段
        - intent_analysis: JSON格式的意图分析结果
        - result: JSON格式的分析结果
        - created_at: 创建时间
        """
    
    async def execute(self, tool: Tool, params: Dict[str, Any]) -> Dict[str, Any]:
        """执行SQL查询
        
        直接执行提供的SQL语句，并返回结果
        
        Args:
            tool: 工具对象
            params: 参数字典，必须包含sql字段，可选parameters字段
        
        Returns:
            Dict[str, Any]: 包含执行结果的字典
        """
        logger.info(f"执行SQL查询，参数: {params}")

        # 获取用户语言偏好
        user_language = params.get("user_language", "zh-CN")

        # 尝试创建分析日志记录器
        analysis_logger = None
        try:
            project_id = params.get("project_id")
            if project_id:
                analysis_logger = AnalysisLogger(project_id=project_id, request_id="auto_sql")
                analysis_logger.info("开始执行SQL查询", extra={
                    "event": "sql_execution_start",
                    "tool_id": tool.id if tool else None,
                    "input_parameters": params
                })
        except Exception as e:
            logger.warning(f"无法创建分析日志记录器: {str(e)}")

        try:
            # 验证必要参数
            if not params.get("sql"):
                error_msg = "缺少sql参数" if user_language == "zh-CN" else "Missing sql parameter"
                raise ValueError(error_msg)
            
            sql = params["sql"]
            
            project_id = params.get("project_id")
            if not project_id:
                error_msg = "缺少必要参数: project_id" if user_language == "zh-CN" else "Missing required parameter: project_id"
                logger.error(error_msg)
                return {
                    "success": False,
                    "error": error_msg
                }
            
            # 获取数据库连接信息
            conn_info = self._get_database_connection(project_id)
            if not conn_info:
                error_msg = f"无法获取项目 {project_id} 的数据库连接信息" if user_language == "zh-CN" else f"Unable to get database connection info for project {project_id}"
                return {
                    "success": False,
                    "error": error_msg
                }
            
            # 记录到分析日志
            if analysis_logger:
                analysis_logger.info("开始执行SQL", extra={
                    "event": "sql_execution_start",
                    "sql": sql,
                    "project_id": project_id
                })

        except Exception as e:
            logger.error(f"SQL查询参数验证失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
        
        # 直接执行SQL查询（不使用参数化）
        return await self._execute_sql_directly(sql, {}, conn_info, analysis_logger, user_language)
    
    async def _execute_sql_directly(self, sql: str, parameters: Dict[str, Any], conn_info: Dict[str, Any], analysis_logger=None, user_language: str = 'zh-CN') -> Dict[str, Any]:
        """直接执行SQL查询
        
        Args:
            sql: SQL查询语句
            parameters: 查询参数
            conn_info: 数据库连接信息
            analysis_logger: 分析日志记录器
            user_language: 用户语言偏好 ('zh-CN' 或 'en-US')
            
        Returns:
            Dict[str, Any]: 包含执行结果的字典
        """
        try:
            # 获取数据库连接
            db_type = conn_info.get("type")
            config = conn_info.get("config")
            
            # 不再使用参数化查询，直接执行完整SQL
            logger.info(f"执行完整SQL: {sql}")
            
            if db_type == "oracle":
                # Oracle 连接
                import cx_Oracle
                try:
                    # 使用全局初始化的Oracle客户端
                    if not is_oracle_initialized():
                        if not init_oracle_client():
                            raise ValueError("Oracle客户端初始化失败，无法连接到Oracle数据库")
                            
                    # 记录客户端版本信息以便调试
                    client_version = get_oracle_client_version()
                    logger.info(f"Oracle客户端版本: {client_version}")
                    
                    # 尝试连接
                    dsn = cx_Oracle.makedsn(
                        config.get("host"),
                        config.get("port"),
                        service_name=config.get("service_name")
                    )
                    logger.info(f"Oracle DSN: {dsn}")
                    
                    connection = cx_Oracle.connect(
                        user=config.get("username"),
                        password=config.get("password"),
                        dsn=dsn
                    )
                    cursor = connection.cursor()
                except cx_Oracle.DatabaseError as e:
                    # 特别处理客户端库错误
                    error_msg = str(e)
                    if "DPI-1047" in error_msg and "Cannot locate a 64-bit Oracle Client library" in error_msg:
                        logger.error("无法找到Oracle客户端库，尝试使用reconnect_oracle重新连接...")
                        
                        # 使用工具类中的重连方法
                        connection, cursor = reconnect_oracle(
                            host=config.get("host"),
                            port=config.get("port"),
                            service_name=config.get("service_name"),
                            username=config.get("username"),
                            password=config.get("password")
                        )
                        
                        if connection is None or cursor is None:
                            raise ValueError(f"无法初始化Oracle客户端，请确保Oracle Instant Client已正确安装: {error_msg}")
                    else:
                        # 其他Oracle错误直接抛出
                        raise
            elif db_type in ["mysql", "postgresql", "mssql"]:
                # 创建SQLAlchemy引擎和连接
                temp_engine = engine_factory(db_type, config)
                connection = temp_engine.connect()
                cursor = connection.connection.cursor()
            else:
                # 默认使用SQLAlchemy连接
                connection = engine.connect()
                cursor = connection.connection.cursor()
            
            # 执行查询
            start_time = time.time()
            
            # 直接执行完整SQL，不使用参数化
            cursor.execute(sql)
            
            columns = [desc[0] for desc in cursor.description] if cursor.description else []
            result_rows = cursor.fetchall()
            
            # 转换为列表字典，并立即处理LOB对象
            results = []
            for row in result_rows:
                result_dict = {}
                for i, column in enumerate(columns):
                    value = row[i]
                    # 立即处理LOB对象，在连接关闭前读取内容
                    if db_type == "oracle" and self._is_oracle_lob(value):
                        try:
                            processed_value = self._process_oracle_lob_immediately(value)
                            result_dict[column] = processed_value
                            logger.debug(f"已处理LOB对象: 列 {column}, 类型 {type(value).__name__}, 内容长度 {len(str(processed_value))}")
                        except Exception as lob_error:
                            logger.warning(f"处理LOB对象失败: 列 {column}, 错误 {str(lob_error)}")
                            result_dict[column] = f"[LOB处理失败: {str(lob_error)}]"
                    else:
                        result_dict[column] = value
                results.append(result_dict)
            
            execution_time = time.time() - start_time
            logger.info(f"SQL执行成功，耗时: {execution_time:.2f}秒，返回 {len(results)} 条结果")
            
            # 记录到分析日志 - SQL执行成功
            if analysis_logger:
                analysis_logger.info(f"SQL执行成功，耗时 {execution_time:.3f}秒", extra={
                    "event": "sql_execution_success",
                    "execution_time": round(execution_time * 1000),  # 毫秒
                    "result_count": len(results),
                    "columns": columns,
                    "final_sql": sql
                })
                
                # 记录详细的查询结果数据（如果数据量不大的话）
                if len(results) <= 100:  # 只记录前100条结果，避免日志过大
                    analysis_logger.debug("SQL查询结果详情", extra={
                        "event": "sql_result_data",
                        "result_data": results,
                        "result_count": len(results)
                    })
                else:
                    # 对于大量数据，只记录前几条和统计信息
                    analysis_logger.debug("SQL查询结果统计（数据量较大）", extra={
                        "event": "sql_result_summary",
                        "result_count": len(results),
                        "sample_data": results[:5] if results else [],
                        "columns": columns
                    })
            
            # 关闭连接
            cursor.close()
            connection.close()
            
            return {
                "success": True,
                "sql": sql,  # 返回执行的SQL
                "results": results,
                "columns": columns,
                "row_count": len(results),
                "execution_time": execution_time
            }
            
        except Exception as e:
            error_msg = str(e)
            
            # 根据用户语言偏好生成错误信息
            if user_language == 'en-US':
                log_error_prefix = "SQL execution failed"
                user_error_prefix = "SQL execution failed"
            else:
                log_error_prefix = "SQL执行失败"
                user_error_prefix = "SQL执行失败"
            
            # 记录完整错误到日志，但不返回给用户
            full_error = f"{log_error_prefix}: {error_msg}\n{traceback.format_exc()}"
            logger.error(full_error)
            
            # 只返回简化的错误信息给用户，不包含堆栈信息
            user_error = f"{user_error_prefix}: {error_msg}"
            
            # 记录到分析日志
            if analysis_logger:
                analysis_logger.error(f"{log_error_prefix}: {error_msg}", extra={
                    "event": "sql_execution_failed",
                    "error": error_msg,
                    "sql": sql,
                    "user_language": user_language,
                    "full_traceback": traceback.format_exc()  # 完整堆栈只记录到日志
                })
            
            return {
                "success": False,
                "error": user_error,  # 返回简化的错误信息
                "sql": sql  # 返回执行的SQL
            }
        
    async def _execute_with_retry(self, intent: Dict[str, Any], conn_info: Dict[str, Any], extra_params: Dict[str, Any] = None, user_language: str = 'zh-CN') -> Dict[str, Any]:
        """使用重试机制执行SQL查询（已废弃，保留用于兼容性）
        
        注意：此方法已不再使用，新的工具直接执行SQL而不生成SQL
        
        Args:
            intent: 查询意图
            conn_info: 数据库连接信息
            extra_params: 额外的参数，可能包含从前序步骤提取的值
            user_language: 用户语言偏好 ('zh-CN' 或 'en-US')
            
        Returns:
            Dict[str, Any]: 包含执行结果的字典
        """
        error = None
        sql = None
        parameters = {}
        
        # 尝试创建分析日志记录器
        analysis_logger = None
        try:
            from app.core.logger import AnalysisLogger
            # 从extra_params或intent中获取project_id
            project_id = None
            if extra_params and "project_id" in extra_params:
                project_id = extra_params["project_id"]
            elif hasattr(self, 'extra_params') and self.extra_params and "project_id" in self.extra_params:
                project_id = self.extra_params["project_id"]
            
            if project_id:
                analysis_logger = AnalysisLogger(project_id=project_id, request_id="sql_retry")
                analysis_logger.info("开始SQL重试执行", extra={
                    "event": "sql_retry_start",
                    "intent": intent,
                    "max_retries": self.max_retries,
                    "extra_params": extra_params
                })
        except Exception as e:
            logger.warning(f"SQL执行中无法创建分析日志记录器: {str(e)}")
        
        if extra_params is None:
            extra_params = {}
            
        # 保存extra_params为实例变量，这样_generate_sql可以访问到
        self.extra_params = extra_params
        
        for attempt in range(self.max_retries):
            logger.info(f"SQL执行尝试 {attempt + 1}/{self.max_retries}")
            
            # 记录到分析日志
            if analysis_logger:
                analysis_logger.info(f"开始第 {attempt + 1} 次SQL执行尝试", extra={
                    "event": "sql_attempt_start",
                    "attempt": attempt + 1,
                    "max_retries": self.max_retries,
                    "previous_error": error
                })
            
            try:
                # 生成SQL
                sql_result = await self._generate_sql(intent, conn_info, error)
                if not sql_result.get("success", False):
                    error = sql_result.get("error", "SQL生成失败")
                    logger.error(f"SQL生成失败: {error}")
                    
                    # 记录到分析日志
                    if analysis_logger:
                        analysis_logger.error(f"SQL生成失败: {error}", extra={
                            "event": "sql_generation_failed",
                            "attempt": attempt + 1,
                            "error": error,
                            "intent": intent
                        })
                    continue
                
                sql = sql_result.get("sql")
                parameters = sql_result.get("parameters", {})
                
                # 记录到分析日志 - SQL生成成功
                if analysis_logger:
                    analysis_logger.info("SQL生成成功", extra={
                        "event": "sql_generated",
                        "attempt": attempt + 1,
                        "sql": sql,
                        "parameters": parameters
                    })
                
                if not sql:
                    error = "生成的SQL为空"
                    logger.error(error)
                    continue
                    
                # 合并额外参数到SQL参数中，确保前序步骤提取的值可用于SQL执行
                if hasattr(self, 'extra_params') and self.extra_params:
                    # 检查SQL参数是否有null值需要替换
                    for key, value in parameters.items():
                        if value is None and key in self.extra_params:
                            parameters[key] = self.extra_params[key]
                            logger.info(f"使用前序步骤参数替换SQL参数中的null值: {key}={self.extra_params[key]}")
                        # 检查是否包含参数占位符
                        elif isinstance(value, str) and ("指定" in value or "从步骤" in value or "来自" in value):
                            if key in self.extra_params:
                                parameters[key] = self.extra_params[key]
                                logger.info(f"替换参数占位符: {key}={value} -> {self.extra_params[key]}")
                        # 处理空列表的特殊情况，避免IN()语法错误
                        elif isinstance(value, list) and len(value) == 0:
                            # 空列表的处理：把IN条件替换为一个不可能匹配的条件，确保SQL语法正确
                            logger.warning(f"参数 {key} 是空列表，SQL执行可能需要特殊处理")
                            # 在SQL生成阶段会处理这个情况，这里只是记录下来
                    
                    # 添加缺失的额外参数
                    for key, value in self.extra_params.items():
                        if key not in parameters:
                            parameters[key] = value
                            logger.info(f"添加额外参数到SQL执行中: {key}={value}")
                
                # 特殊处理SQL中的IN子句参数，当参数为空列表时
                for key, value in parameters.items():
                    if isinstance(value, list) and len(value) == 0:
                        # 检查SQL中是否有对应的IN条件
                        in_pattern = rf'(\w+)\s+IN\s*\(:{key}\)'
                        import re
                        match = re.search(in_pattern, sql)
                        if match:
                            column_name = match.group(1)
                            # 替换为一个永假条件，确保SQL语法有效
                            old_condition = f"{column_name} IN (:{key})"
                            new_condition = f"{column_name} = -1"  # 假设ID列不可能是负数
                            sql = sql.replace(old_condition, new_condition)
                            logger.info(f"替换空列表IN条件: {old_condition} -> {new_condition}")
                
                logger.info(f"最终执行参数: {parameters}")
                
                # 执行SQL查询
                try:
                    # 获取数据库连接
                    # 这里可以根据conn_info选择不同的数据库驱动
                    db_type = conn_info.get("type")
                    config = conn_info.get("config")
                    
                    # 转换参数格式（针对MySQL特殊处理）
                    converted_sql = sql
                    converted_params = parameters
                    param_style = "named"  # 默认使用命名参数风格(:param_name)
                    
                    if db_type == "mysql":
                        # MySQL需要将:param_name转换为%s，并将参数转为元组
                        converted_sql, converted_params, param_style = self._convert_params_for_mysql(sql, parameters)
                        logger.info(f"MySQL参数转换: \n原始SQL: {sql} \n转换后SQL: {converted_sql} \n参数: {converted_params}")
                    
                    if db_type == "oracle":
                        # Oracle 连接
                        import cx_Oracle
                        try:
                            # 使用全局初始化的Oracle客户端
                            if not is_oracle_initialized():
                                if not init_oracle_client():
                                    raise ValueError("Oracle客户端初始化失败，无法连接到Oracle数据库")
                                
                            # 记录客户端版本信息以便调试
                            client_version = get_oracle_client_version()
                            logger.info(f"Oracle客户端版本: {client_version}")
                            
                            # 尝试连接
                            dsn = cx_Oracle.makedsn(
                                config.get("host"),
                                config.get("port"),
                                service_name=config.get("service_name")
                            )
                            logger.info(f"Oracle DSN: {dsn}")
                            
                            connection = cx_Oracle.connect(
                                user=config.get("username"),
                                password=config.get("password"),
                                dsn=dsn
                            )
                            cursor = connection.cursor()
                        except cx_Oracle.DatabaseError as e:
                            # 特别处理客户端库错误
                            error_msg = str(e)
                            if "DPI-1047" in error_msg and "Cannot locate a 64-bit Oracle Client library" in error_msg:
                                logger.error("无法找到Oracle客户端库，尝试使用reconnect_oracle重新连接...")
                                
                                # 使用工具类中的重连方法
                                connection, cursor = reconnect_oracle(
                                    host=config.get("host"),
                                    port=config.get("port"),
                                    service_name=config.get("service_name"),
                                    username=config.get("username"),
                                    password=config.get("password")
                                )
                                
                                if connection is None or cursor is None:
                                    raise ValueError(f"无法初始化Oracle客户端，请确保Oracle Instant Client已正确安装: {error_msg}")
                            else:
                                # 其他Oracle错误直接抛出
                                raise
                    elif db_type in ["mysql", "postgresql", "mssql"]:
                        # 创建SQLAlchemy引擎和连接
                        temp_engine = engine_factory(db_type, config)
                        connection = temp_engine.connect()
                        cursor = connection.connection.cursor()
                    else:
                        # 默认使用SQLAlchemy连接
                        connection = engine.connect()
                        cursor = connection.connection.cursor()
                    
                    # 执行查询
                    start_time = time.time()
                    
                    # 根据不同数据库类型执行查询
                    if db_type == "mysql" and param_style == "positional":
                        # MySQL使用位置参数
                        cursor.execute(converted_sql, converted_params)
                    else:
                        # 其他数据库使用命名参数
                        cursor.execute(converted_sql, converted_params)
                    
                    columns = [desc[0] for desc in cursor.description] if cursor.description else []
                    result_rows = cursor.fetchall()
                    
                    # 转换为列表字典
                    results = []
                    for row in result_rows:
                        result_dict = {}
                        for i, column in enumerate(columns):
                            result_dict[column] = row[i]
                        results.append(result_dict)
                    
                    execution_time = time.time() - start_time
                    logger.info(f"SQL执行成功，耗时: {execution_time:.2f}秒，返回 {len(results)} 条结果")
                    
                    # 记录到分析日志 - SQL执行成功
                    if analysis_logger:
                        analysis_logger.info(f"SQL执行成功，耗时 {execution_time:.3f}秒", extra={
                            "event": "sql_execution_success",
                            "attempt": attempt + 1,
                            "execution_time": round(execution_time * 1000),  # 毫秒
                            "result_count": len(results),
                            "columns": columns,
                            "final_sql": converted_sql if 'converted_sql' in locals() else sql,
                            "final_parameters": converted_params if 'converted_params' in locals() else parameters
                        })
                        
                        # 记录详细的查询结果数据（如果数据量不大的话）
                        if len(results) <= 100:  # 只记录前100条结果，避免日志过大
                            analysis_logger.debug("SQL查询结果详情", extra={
                                "event": "sql_result_data",
                                "attempt": attempt + 1,
                                "result_data": results,
                                "result_count": len(results)
                            })
                        else:
                            # 对于大量数据，只记录前几条和统计信息
                            analysis_logger.debug("SQL查询结果统计（数据量较大）", extra={
                                "event": "sql_result_summary",
                                "attempt": attempt + 1,
                                "result_count": len(results),
                                "sample_data": results[:5] if results else [],
                                "columns": columns
                            })
                    
                    # 关闭连接
                    cursor.close()
                    connection.close()
                    
                    # 生成可执行的SQL（替换参数为实际值）
                    executable_sql = self._generate_executable_sql(sql, parameters, db_type)
                    
                    return {
                        "success": True,
                        "sql": executable_sql,  # 返回可执行的SQL（已替换参数）
                        "parameterized_sql": sql,  # 返回参数化SQL
                        "parameters": parameters,  # 返回原始参数
                        "results": results,
                        "columns": columns,
                        "row_count": len(results),
                        "execution_time": execution_time
                    }
                    
                except Exception as e:
                    error_msg = str(e)
                    
                    # 根据用户语言偏好生成错误信息
                    if user_language == 'en-US':
                        log_error_prefix = "SQL execution failed"
                        mysql_format_msg = "This is a formatting character issue in MySQL date functions ('%Y', '%m', etc.). In DATE_FORMAT functions, % needs to be replaced with %% to escape."
                        mysql_detection_msg = "Detected MySQL date format error, attempting auto-fix"
                    else:
                        log_error_prefix = "SQL执行失败"
                        mysql_format_msg = "这是MySQL日期函数中的格式字符('%Y', '%m'等)引起的问题。在DATE_FORMAT等函数中，需要将%替换为%%以转义。"
                        mysql_detection_msg = "检测到MySQL日期格式错误，尝试自动修复"
                    
                    # 记录完整错误到日志，但不返回给用户
                    full_error = f"{log_error_prefix}: {error_msg}\n{traceback.format_exc()}"
                    logger.error(full_error)
                    # 只保留简化的错误信息
                    error = f"{log_error_prefix}: {error_msg}"
                    
                    # 特殊处理MySQL格式化错误
                    if "unsupported format character" in error_msg and db_type == "mysql":
                        # 这可能是DATE_FORMAT等函数中的%字符引起的格式化错误
                        # 尝试直接修复SQL
                        logger.info(mysql_detection_msg)
                        # 在下一次重试时，_convert_params_for_mysql 函数会处理这个问题
                        error += f"\n\n{mysql_format_msg}"
                    
                    # 记录下一次重试的错误信息
                    continue
                
            except Exception as e:
                # 记录完整错误到日志，但不返回给用户
                full_error = f"处理SQL查询过程中发生错误: {str(e)}\n{traceback.format_exc()}"
                logger.exception(full_error)
                # 只保留简化的错误信息
                error = f"处理SQL查询过程中发生错误: {str(e)}"
            
            # 如果失败且还有重试次数，等待一段时间后重试
            if attempt < self.max_retries - 1:
                await asyncio.sleep(self.retry_delay)
        
        # 所有重试都失败后返回错误
        # 即使失败也尝试生成可执行SQL用于调试
        executable_sql = sql
        if sql and parameters:
            try:
                db_type = conn_info.get("type", "")
                executable_sql = self._generate_executable_sql(sql, parameters, db_type)
            except:
                pass
        
        return {
            "success": False,
            "error": error,
            "sql": executable_sql,  # 返回可执行的SQL（已替换参数）
            "parameterized_sql": sql,  # 返回参数化SQL
            "parameters": parameters
        }
    
    def _convert_params_for_mysql(self, sql: str, parameters: Dict[str, Any]) -> Tuple[str, Any, str]:
        """转换SQL参数占位符为MySQL支持的格式
        
        将:param_name格式的命名参数转换为%s格式的位置参数
        同时处理MySQL日期函数中的%Y、%m等格式字符，避免与Python格式化字符冲突
        
        Args:
            sql: 原始SQL查询
            parameters: 参数字典
            
        Returns:
            Tuple[str, Any, str]: 转换后的SQL, 参数值列表, 参数风格
        """
        if not parameters:
            return sql, parameters, "named"
        
        # 预处理参数 - 特别处理IN条件的列表参数
        processed_parameters = {}
        for key, value in parameters.items():
            # 检查参数是否为列表类型，通常用于IN查询
            if isinstance(value, list):
                # 空列表特殊处理为单个空字符串防止SQL语法错误
                if not value:
                    logger.warning(f"参数 {key} A是空列表，将被处理为默认值1以避免SQL语法错误")
                    processed_parameters[key] = "1"  # 使用占位值，确保SQL语法有效
                else:
                    # 为非空列表，每个元素作为单独参数处理
                    for i, item in enumerate(value):
                        processed_parameters[f"{key}_{i}"] = item
                    
                    # 检测是否在SQL中使用IN查询
                    in_pattern = r'IN\s*\(:[a-zA-Z0-9_]+\)'
                    if re.search(in_pattern, sql):
                        # 在SQL中找到这个参数的IN子句
                        param_placeholder = f":{key}"
                        # 创建逗号分隔的占位符列表
                        placeholders = ", ".join([f":{key}_{i}" for i in range(len(value))])
                        # 替换原来的单个占位符为多个
                        sql = sql.replace(f"IN ({param_placeholder})", f"IN ({placeholders})")
                        logger.info(f"转换IN查询参数 {key}，从 :{key} 到 {placeholders}")
            else:
                processed_parameters[key] = value
            
        # 匹配:param_name格式的参数
        param_pattern = r':([a-zA-Z0-9_]+)'
        param_names = re.findall(param_pattern, sql)
        
        if not param_names:
            return sql, parameters, "named"
            
        # 替换参数占位符并构建参数值列表
        new_sql = sql
        param_values = []
        
        for param_name in param_names:
            if param_name in processed_parameters:
                # 替换:param_name为%s
                new_sql = new_sql.replace(f":{param_name}", "%s")
                param_values.append(processed_parameters[param_name])
            else:
                logger.warning(f"参数 {param_name} 未在处理后的参数字典中找到")
        
        return new_sql, tuple(param_values), "positional"
    
    def _generate_executable_sql(self, sql: str, parameters: Dict[str, Any], db_type: str = None) -> str:
        """生成可执行的SQL（替换参数占位符为实际值）
        
        Args:
            sql: 包含参数占位符的SQL
            parameters: 参数字典
            db_type: 数据库类型
            
        Returns:
            str: 替换了参数的可执行SQL
        """
        if not parameters:
            return sql
        
        executable_sql = sql
        
        try:
            # 首先处理列表参数（IN查询）
            processed_parameters = {}
            for key, value in parameters.items():
                if isinstance(value, list):
                    # 列表类型（用于IN查询）
                    if not value:
                        # 空列表处理
                        formatted_value = "NULL"
                        placeholder = f":{key}"
                        if placeholder in executable_sql:
                            executable_sql = executable_sql.replace(placeholder, formatted_value)
                    else:
                        # 非空列表，需要展开为多个参数
                        formatted_items = []
                        for item in value:
                            if isinstance(item, str):
                                escaped_item = item.replace("'", "''")
                                formatted_items.append(f"'{escaped_item}'")
                            else:
                                formatted_items.append(str(item))
                        
                        # 检查是否有IN查询模式
                        import re
                        in_pattern = rf'IN\s*\(\s*:{key}\s*\)'
                        if re.search(in_pattern, executable_sql):
                            # 替换IN查询中的参数
                            formatted_value = f"({', '.join(formatted_items)})"
                            executable_sql = re.sub(in_pattern, f"IN {formatted_value}", executable_sql)
                        else:
                            # 检查是否有展开的参数（如:key_0, :key_1等）
                            for i, item in enumerate(value):
                                expanded_key = f"{key}_{i}"
                                processed_parameters[expanded_key] = item
                else:
                    processed_parameters[key] = value
            
            # 处理普通参数
            for param_name, param_value in processed_parameters.items():
                placeholder = f":{param_name}"
                
                if placeholder in executable_sql:
                    # 根据参数类型进行格式化
                    if param_value is None:
                        formatted_value = "NULL"
                    elif isinstance(param_value, str):
                        # 字符串类型，需要加引号并转义单引号
                        escaped_value = param_value.replace("'", "''")
                        formatted_value = f"'{escaped_value}'"
                    elif isinstance(param_value, (int, float)):
                        # 数字类型，直接转换
                        formatted_value = str(param_value)
                    elif isinstance(param_value, bool):
                        # 布尔类型
                        if db_type == "mysql":
                            formatted_value = "1" if param_value else "0"
                        elif db_type == "postgresql":
                            formatted_value = "TRUE" if param_value else "FALSE"
                        else:
                            formatted_value = "1" if param_value else "0"
                    else:
                        # 其他类型，尝试转换为字符串
                        formatted_value = f"'{str(param_value)}'"
                    
                    # 替换占位符
                    executable_sql = executable_sql.replace(placeholder, formatted_value)
            
            logger.debug(f"生成可执行SQL: {executable_sql}")
            return executable_sql
            
        except Exception as e:
            logger.warning(f"生成可执行SQL失败: {str(e)}，返回原始SQL")
            return sql
    
    def _build_sql_prompt(self, intent: Dict[str, Any], conn_info: Dict[str, Any], last_error: Optional[str] = None) -> str:
        """构建SQL生成提示（已废弃，保留用于兼容性）
        
        注意：此方法已不再使用，新的工具直接执行提供的SQL而不生成SQL
        
        Args:
            intent: 查询意图
            conn_info: 数据库连接信息
            last_error: 上次执行错误
            
        Returns:
            str: 完整的提示文本
        """
        # 获取数据库类型
        db_type = conn_info.get("type")
        if not db_type:
            db_type = self.current_db_type or "标准SQL"
        
        # 格式化数据库类型显示
        db_type_display = db_type.upper() if db_type else "标准SQL"
        
        # 简化时间信息，只提供必要的时间参考
        current_time = get_shanghai_time()
        time_info = (
            f"当前时间：{current_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"本月：{current_time.year}-{current_time.month:02d}\n"
            f"上月：{current_time.year if current_time.month > 1 else current_time.year - 1}-{current_time.month - 1 if current_time.month > 1 else 12:02d}\n"
        )
        
        prompt_parts = [
            f"根据以下信息生成{db_type_display} SQL查询：\n",
            "## Schema信息",
            self.schema_description,
            "\n## 时间参考",
            time_info,
            "\n## 查询意图",
            json.dumps(intent, ensure_ascii=False, indent=2, cls=DecimalEncoder)
        ]
        
        # 错误分析和修复
        if last_error:
            prompt_parts.extend([
                "\n## 错误分析",
                f"上次执行错误：{last_error}",
                "请分析错误原因并生成修正的SQL。"
            ])
        
        # 核心SQL生成要求
        prompt_parts.extend([
            "\n## 核心要求",
            "1. **数据安全**：必须使用LIMIT限制返回行数，避免大批量数据查询",
            "2. **参数化查询**：使用:param_name语法，确保SQL安全",
            "3. **字段匹配**：严格按照Schema中的表名和字段名",
            "4. **模糊匹配**：当需要文本匹配时，优先使用LIKE进行模糊查询",
            "5. **时间处理**：使用上述时间参考信息处理相对时间查询"
        ])
        
        # 数据库特定要求（简化版）
        if db_type == "mysql":
            prompt_parts.extend([
                "\n## MySQL特殊要求",
                "- 日期格式化函数使用双百分号：DATE_FORMAT(col, '%%Y-%%m-%%d')",
                "- 优先使用：CURDATE(), DATE(), CAST(col AS DATE)",
                "- 时间范围：DATE_SUB/DATE_ADD(CURDATE(), INTERVAL n DAY/MONTH)"
            ])
        elif db_type == "postgresql":
            prompt_parts.extend([
                "\n## PostgreSQL特殊要求",
                "- 表名字段名通常小写",
                "- 使用PostgreSQL特定函数：EXTRACT, AGE等"
            ])
        elif db_type == "oracle":
            prompt_parts.extend([
                "\n## Oracle特殊要求",
                "- 表名字段名通常大写",
                "- 使用Oracle函数：TO_DATE, TRUNC, SYSDATE等"
            ])
        
        return "\n".join(prompt_parts)

    async def _generate_sql(self, intent: Dict[str, Any], conn_info: Dict[str, Any], 
                        error: Optional[str] = None) -> Dict[str, Any]:
        """生成SQL查询（已废弃，保留用于兼容性）
        
        注意：此方法已不再使用，新的工具直接执行提供的SQL而不生成SQL
        
        Args:
            intent: 意图描述
            conn_info: 数据库连接信息
            error: 之前执行时的错误信息，用于改进SQL生成
        
        Returns:
            Dict[str, Any]: 包含生成的SQL和参数的字典
        """
        # 尝试创建分析日志记录器
        analysis_logger = None
        try:
            from app.core.logger import AnalysisLogger
            # 从extra_params中获取project_id
            project_id = None
            if hasattr(self, 'extra_params') and self.extra_params and "project_id" in self.extra_params:
                project_id = self.extra_params["project_id"]
            
            if project_id:
                analysis_logger = AnalysisLogger(project_id=project_id, request_id="sql_generation")
                analysis_logger.info("开始SQL生成", extra={
                    "event": "sql_generation_start",
                    "intent": intent,
                    "conn_info": {k: v for k, v in conn_info.items() if k != "config"},  # 不记录敏感信息
                    "has_error": error is not None,
                    "error": error
                })
        except Exception as e:
            logger.warning(f"SQL生成中无法创建分析日志记录器: {str(e)}")
        
        try:
            # 获取数据库类型
            db_type = conn_info.get("type")
            if not db_type:
                db_type = self.current_db_type or "标准SQL"
            
            # 在发送给LLM前，替换intent中的占位符参数值
            intent_copy = intent.copy()
            # 处理查询过滤条件
            if "filters" in intent_copy and isinstance(intent_copy["filters"], dict):
                for key, value in intent_copy["filters"].items():
                    # 检查是否是占位符值
                    if isinstance(value, str) and ("指定" in value or "来自步骤" in value):
                        # 从extra_params中查找实际值
                        if hasattr(self, 'extra_params') and self.extra_params and key in self.extra_params:
                            intent_copy["filters"][key] = self.extra_params[key]
                            logger.info(f"SQL生成前替换占位符参数: {key}={value} -> {self.extra_params[key]}")
            
            # 准备生成SQL的提示
            db_type_display = db_type.upper() if db_type else "标准SQL"
            system_prompt = f"""你是一个{db_type_display}专家，负责根据用户意图生成安全、高效的SQL查询。

核心原则：
1. **数据安全优先**：必须使用LIMIT限制返回行数，防止大批量数据查询
2. **模糊匹配优先**：文本查询优先使用LIKE进行模糊匹配，提高查询成功率
3. **参数化查询**：使用:param_name语法确保SQL安全
4. **严格字段匹配**：表名和字段名必须与Schema完全一致

输出要求：
- 只返回一条SQL语句
- 只返回JSON格式：{{"sql": "SQL语句", "parameters": {{"param": "value"}}}}
- 不返回任何解释或其他内容
- 如有错误信息，分析并修正SQL"""

            user_prompt = self._build_sql_prompt(intent_copy, conn_info, error)
            
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            print(f"messages---: {messages}")
            
            # 记录到分析日志 - LLM调用前
            if analysis_logger:
                analysis_logger.info("开始调用LLM生成SQL", extra={
                    "event": "sql_llm_call_start",
                    "db_type": db_type,
                    "system_prompt": system_prompt,
                    "user_prompt": user_prompt,
                    "intent_processed": intent_copy
                })
            
            # 调用语言模型生成SQL
            start_time = time.time()
            response_content = await self.openai_service.call_llm(messages=messages)
            execution_time = time.time() - start_time
            
            print(f"response_content---: {response_content}")
            
            # 记录到分析日志 - LLM调用完成
            if analysis_logger:
                analysis_logger.info(f"LLM SQL生成完成，耗时 {execution_time:.3f}秒", extra={
                    "event": "sql_llm_call_completed",
                    "execution_time": round(execution_time * 1000),  # 毫秒
                    "response_length": len(response_content),
                    "raw_response": response_content
                })
            
            # 提取JSON部分
            try:
                # 尝试直接解析JSON
                result = json.loads(response_content)
                sql = result.get("sql", "")
                parameters = result.get("parameters", {})
                
                # 记录到分析日志 - JSON解析成功
                if analysis_logger:
                    analysis_logger.info("SQL生成JSON解析成功", extra={
                        "event": "sql_json_parsed",
                        "generated_sql": sql,
                        "generated_parameters": parameters
                    })
                
                # 替换parameters中的占位符值
                for key, value in parameters.items():
                    # 检查是否是占位符值
                    if isinstance(value, str) and ("指定" in value or "来自步骤" in value):
                        # 从extra_params中查找实际值
                        if hasattr(self, 'extra_params') and self.extra_params and key in self.extra_params:
                            parameters[key] = self.extra_params[key]
                            logger.info(f"SQL执行前替换参数占位符: {key}={value} -> {self.extra_params[key]}")
                
                # 记录到分析日志 - 最终SQL和参数
                if analysis_logger:
                    analysis_logger.info("SQL生成完成", extra={
                        "event": "sql_generation_completed",
                        "final_sql": sql,
                        "final_parameters": parameters,
                        "parameter_replacements": len([v for v in parameters.values() if not isinstance(v, str) or ("指定" not in v and "来自步骤" not in v)])
                    })
                
                return {
                    "success": True,
                    "sql": sql,
                    "parameters": parameters
                }
            except json.JSONDecodeError:
                # 记录到分析日志 - JSON解析失败
                if analysis_logger:
                    analysis_logger.warning("SQL生成JSON解析失败，尝试提取", extra={
                        "event": "sql_json_parse_failed",
                        "raw_response": response_content
                    })
                
                # 如果直接解析失败，尝试提取JSON部分
                try:
                    # 查找并提取JSON代码块
                    json_start = response_content.find("```json") + 7 if "```json" in response_content else response_content.find("{")
                    json_end = response_content.rfind("```") if "```" in response_content[json_start:] else response_content.rfind("}") + 1
                    
                    if json_start >= 0 and json_end > json_start:
                        json_str = response_content[json_start:json_end].strip()
                        result = json.loads(json_str)
                        sql = result.get("sql", "")
                        parameters = result.get("parameters", {})
                        
                        # 记录到分析日志 - JSON提取成功
                        if analysis_logger:
                            analysis_logger.info("SQL生成JSON提取成功", extra={
                                "event": "sql_json_extracted",
                                "extracted_json": json_str,
                                "generated_sql": sql,
                                "generated_parameters": parameters
                            })
                        
                        # 替换parameters中的占位符值
                        for key, value in parameters.items():
                            # 检查是否是占位符值
                            if isinstance(value, str) and ("指定" in value or "来自步骤" in value):
                                # 从extra_params中查找实际值
                                if hasattr(self, 'extra_params') and self.extra_params and key in self.extra_params:
                                    parameters[key] = self.extra_params[key]
                                    logger.info(f"SQL执行前替换参数占位符: {key}={value} -> {self.extra_params[key]}")
                        
                        return {
                            "success": True,
                            "sql": sql,
                            "parameters": parameters
                        }
                    else:
                        raise ValueError("无法找到有效的JSON内容")
                except Exception as e:
                    logger.error(f"解析SQL生成结果失败: {str(e)}\n原始响应: {response_content}")
                    
                    # 记录到分析日志 - JSON提取失败
                    if analysis_logger:
                        analysis_logger.error(f"SQL生成JSON提取失败: {str(e)}", extra={
                            "event": "sql_json_extract_failed",
                            "error": str(e),
                            "raw_response": response_content
                        })
                    
                    return {
                        "success": False,
                        "error": f"无法解析SQL生成结果: {str(e)}"
                    }
        
        except Exception as e:
            logger.exception(f"生成SQL时发生错误: {str(e)}")
            
            # 记录到分析日志 - SQL生成异常
            if analysis_logger:
                analysis_logger.error(f"SQL生成异常: {str(e)}", extra={
                    "event": "sql_generation_exception",
                    "error": str(e),
                    "error_type": type(e).__name__
                })
            
            return {
                "success": False,
                "error": f"生成SQL时发生错误: {str(e)}"
            }
    
    def _get_database_connection(self, project_id: str) -> Dict[str, Any]:
        """获取项目的数据库连接信息
        
        Args:
            project_id: 项目ID
        
        Returns:
            Dict[str, Any]: 数据库连接信息
        """
        try:
            from app.models.data_source import DataSource
            
            data_source = self.db.query(DataSource).filter(DataSource.project_id == project_id).first()
            if not data_source:
                logger.error(f"项目不存在: {project_id}")
                return {}
            
            # 检查项目配置中是否包含数据库连接信息
            type = data_source.type
            type_value = type.value if hasattr(type, 'value') else str(type)
            config = data_source.config if data_source.config else {}
            
            if isinstance(config, str):
                try:
                    config = json.loads(config)
                except:
                    config = {}
            
            # 获取数据库连接信息
            conn_info = {
                "type": type_value.lower() if type_value else "",
                "config": config
            }
            
            # 如果没有配置，使用默认连接
            if not conn_info or not conn_info.get("type"):
                logger.warning(f"项目 {project_id} 未配置数据库连接信息，使用默认连接")
                return {
                    "type": "sqlite",
                    "connection_string": str(engine.url)
                }
            
            return conn_info
            
        except Exception as e:
            logger.exception(f"获取数据库连接信息失败: {str(e)}")
            return {}
    def _is_oracle_lob(self, value) -> bool:
        """检测值是否为Oracle LOB对象
        
        Args:
            value: 要检测的值
            
        Returns:
            bool: 如果是LOB对象返回True，否则返回False
        """
        if value is None:
            return False
        
        # 检查是否为Oracle LOB对象
        type_str = str(type(value))
        return (hasattr(value, 'read') and 
                ('cx_Oracle.LOB' in type_str or 'oracle.LOB' in type_str or 'LOB' in type_str))
    
    def _process_oracle_lob_immediately(self, lob_value):
        """立即处理Oracle LOB对象，读取其内容
        
        Args:
            lob_value: Oracle LOB对象
            
        Returns:
            处理后的内容（字符串）
        """
        try:
            if not hasattr(lob_value, 'read'):
                return str(lob_value)
            
            # 根据LOB类型进行不同处理
            type_str = str(type(lob_value))
            
            if 'CLOB' in type_str:
                # 处理CLOB（字符LOB）
                content = lob_value.read()
                logger.debug(f"成功读取CLOB内容，长度: {len(content) if content else 0}")
                return content if content is not None else ""
                
            elif 'BLOB' in type_str:
                # 处理BLOB（二进制LOB）
                blob_data = lob_value.read()
                if blob_data is None:
                    return ""
                
                # 尝试将BLOB解码为UTF-8字符串
                try:
                    decoded_content = blob_data.decode('utf-8')
                    logger.debug(f"成功将BLOB解码为UTF-8字符串，长度: {len(decoded_content)}")
                    return decoded_content
                except UnicodeDecodeError:
                    # 如果无法解码为字符串，返回十六进制表示
                    hex_content = blob_data.hex()
                    logger.debug(f"BLOB无法解码为UTF-8，返回十六进制表示，长度: {len(hex_content)}")
                    return f"[二进制数据，十六进制: {hex_content[:100]}{'...' if len(hex_content) > 100 else ''}]"
                    
            else:
                # 其他类型的LOB，尝试直接读取
                content = lob_value.read()
                logger.debug(f"读取其他类型LOB内容，长度: {len(str(content)) if content else 0}")
                return str(content) if content is not None else ""
                
        except Exception as e:
            logger.error(f"处理LOB对象时出错: {str(e)}")
            return f"[LOB读取失败: {str(e)}]"

    def __del__(self):
        """析构函数，确保数据库连接被正确关闭"""
        if hasattr(self, 'db') and self.db is not None:
            try:
                self.db.close()
            except:
                pass

