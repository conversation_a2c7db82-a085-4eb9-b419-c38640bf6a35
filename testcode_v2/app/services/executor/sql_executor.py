from typing import Dict, Any, Optional, List
import pandas as pd
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
import logging

from app.services.executor import ToolExecutor
from app.models.data_source import DataSourceType
from app.utils.oracle_init import init_oracle_client, reconnect_oracle, is_oracle_initialized

# 有条件地导入cx_Oracle，不存在时使用模拟实现
try:
    import cx_Oracle
except ImportError:
    # 创建模拟的cx_Oracle
    class cx_Oracle_Mock:
        @staticmethod
        def makedsn(host, port, service_name=None, sid=None):
            return f"(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST={host})(PORT={port}))(CONNECT_DATA=(SERVICE_NAME={service_name or sid})))"
        
        @staticmethod
        def connect(user, password, dsn):
            class MockConnection:
                def cursor(self):
                    class MockCursor:
                        @property
                        def description(self):
                            return [("ID",), ("NAME",), ("VALUE",)]
                        
                        def execute(self, query, params=None):
                            # 无实际执行操作
                            return None
                        
                        def fetchall(self):
                            # 返回模拟数据
                            return [("1", "测试", 100), ("2", "示例", 200)]
                    
                    return MockCursor()
                
                def close(self):
                    pass
                
                def __enter__(self):
                    return self
                
                def __exit__(self, exc_type, exc_val, exc_tb):
                    self.close()
            
            return MockConnection()
    
    # 替换为模拟实现
    cx_Oracle = cx_Oracle_Mock

# 有条件地导入pymysql
try:
    import pymysql
except ImportError:
    # 这里不需要模拟，因为我们使用SQLAlchemy作为后备
    pass

# 有条件地导入psycopg2
try:
    import psycopg2
except ImportError:
    # 这里不需要模拟，因为我们使用SQLAlchemy作为后备
    pass

# 有条件地导入pyodbc
try:
    import pyodbc
except ImportError:
    # 这里不需要模拟，因为我们使用SQLAlchemy作为后备
    pass

class SQLExecutor(ToolExecutor):
    """SQL工具执行器"""
    
    async def execute(self, tool_template: str, parameters: Dict[str, Any], tool_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行SQL查询
        
        Args:
            tool_template: SQL模板
            parameters: 查询参数
            tool_config: 工具配置
            
        Returns:
            查询结果
        """
        logger = logging.getLogger("app.services.executor.sql")
        
        try:
            # 获取数据源配置
            ds_type = tool_config.get("type")
            ds_config = tool_config.get("config", {})
            
            # 确保ds_type是一个字符串
            ds_type_str = str(ds_type).lower() if ds_type else ""
            logger.info(f"数据源类型: {ds_type}, 转换后: {ds_type_str}")
            
            if ds_type_str == "oracle" or ds_type == DataSourceType.ORACLE:
                result = await self._execute_oracle(tool_template, parameters, ds_config)
            elif ds_type_str == "mysql" or ds_type == DataSourceType.MYSQL:
                result = await self._execute_mysql(tool_template, parameters, ds_config)
            elif ds_type_str == "postgresql" or ds_type == DataSourceType.POSTGRESQL:
                result = await self._execute_postgresql(tool_template, parameters, ds_config)
            elif ds_type_str == "mssql" or ds_type == DataSourceType.MSSQL:
                result = await self._execute_mssql(tool_template, parameters, ds_config)
            else:
                raise ValueError(f"不支持的数据源类型: {ds_type}，规范化后: {ds_type_str}")
                
            return {
                "success": True,
                "data": result,
                "error": None
            }
        except Exception as e:
            logger.exception(f"SQL执行失败: {str(e)}")
            return {
                "success": False,
                "data": None,
                "error": str(e)
            }
    
    async def _execute_oracle(self, sql_template: str, parameters: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """执行Oracle SQL查询"""
        logger = logging.getLogger("app.services.executor.sql")
        
        # 记录SQL和参数
        logger.info(f"执行Oracle SQL查询: {sql_template}")
        logger.info(f"参数: {parameters}")
        
        # 获取连接参数
        host = config.get("host")
        port = config.get("port")
        service_name = config.get("service_name")
        username = config.get("username")
        password = config.get("password")
        
        # 验证连接参数
        if not all([host, port, service_name, username, password]):
            missing = []
            if not host: missing.append("host")
            if not port: missing.append("port")
            if not service_name: missing.append("service_name")
            if not username: missing.append("username")
            if not password: missing.append("password")
            error_msg = f"Oracle连接参数不完整，缺少: {', '.join(missing)}"
            logger.error(error_msg)
            raise ValueError(error_msg)
        
        try:
            # 确保Oracle客户端已初始化
            if not is_oracle_initialized():
                logger.info("Oracle客户端未初始化，尝试初始化...")
                if not init_oracle_client():
                    raise ValueError("Oracle客户端初始化失败")
            
            # 使用reconnect_oracle方法创建连接
            connection, cursor = reconnect_oracle(
                host=host,
                port=port,
                service_name=service_name,
                username=username,
                password=password
            )
            
            if connection is None or cursor is None:
                raise ValueError("无法创建Oracle连接")
            
            try:
                # 使用with语句确保连接会被正确关闭
                # 执行查询
                logger.info("执行Oracle查询...")
                cursor.execute(sql_template, parameters)
                
                # 获取列名和数据
                columns = [col[0] for col in cursor.description]
                logger.info(f"查询返回列: {columns}")
                
                data = cursor.fetchall()
                logger.info(f"查询返回{len(data)}行数据")
                
                # 转换为DataFrame
                df = pd.DataFrame(data, columns=columns)
                logger.info(f"DataFrame形状: {df.shape}")
                
                # 转换为字典并返回
                result = self.dataframe_to_dict(df)
                logger.info("Oracle查询成功完成")
                
                # 关闭连接
                cursor.close()
                connection.close()
                
                return result
            except Exception as e:
                # 确保关闭连接
                try:
                    cursor.close()
                    connection.close()
                except:
                    pass
                raise e
        except Exception as e:
            error_msg = f"Oracle查询执行失败: {str(e)}"
            logger.exception(error_msg)
            raise ValueError(error_msg)
    
    async def _execute_mysql(self, sql_template: str, parameters: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """执行MySQL SQL查询"""
        host = config.get("host")
        port = config.get("port")
        database = config.get("database")
        username = config.get("username")
        password = config.get("password")
        
        # 构建连接字符串
        connection_str = f"mysql+pymysql://{username}:{password}@{host}:{port}/{database}"
        engine = create_engine(connection_str)
        
        with engine.connect() as connection:
            # 使用SQLAlchemy的text构造参数化查询
            result = connection.execute(text(sql_template), parameters)
            columns = result.keys()
            data = result.fetchall()
            df = pd.DataFrame(data, columns=columns)
            
            return self.dataframe_to_dict(df)
    
    async def _execute_postgresql(self, sql_template: str, parameters: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """执行PostgreSQL SQL查询"""
        host = config.get("host")
        port = config.get("port")
        database = config.get("database")
        username = config.get("username")
        password = config.get("password")
        
        # 构建连接字符串
        connection_str = f"postgresql://{username}:{password}@{host}:{port}/{database}"
        engine = create_engine(connection_str)
        
        with engine.connect() as connection:
            # 使用SQLAlchemy的text构造参数化查询
            result = connection.execute(text(sql_template), parameters)
            columns = result.keys()
            data = result.fetchall()
            df = pd.DataFrame(data, columns=columns)
            
            return self.dataframe_to_dict(df)
    
    async def _execute_mssql(self, sql_template: str, parameters: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """执行MSSQL SQL查询"""
        host = config.get("host")
        port = config.get("port")
        database = config.get("database")
        username = config.get("username")
        password = config.get("password")
        
        # 构建连接字符串
        connection_str = f"mssql+pyodbc://{username}:{password}@{host}:{port}/{database}?driver=ODBC+Driver+17+for+SQL+Server"
        engine = create_engine(connection_str)
        
        with engine.connect() as connection:
            # 使用SQLAlchemy的text构造参数化查询
            result = connection.execute(text(sql_template), parameters)
            columns = result.keys()
            data = result.fetchall()
            df = pd.DataFrame(data, columns=columns)
            
            return self.dataframe_to_dict(df) 