from typing import Dict, Any, Optional
import pandas as pd
import logging
import asyncio
import importlib
import io
import sys
import traceback
from contextlib import redirect_stdout, redirect_stderr

from app.services.executor import ToolExecutor

class PythonExecutor(ToolExecutor):
    """Python工具执行器"""
    
    async def execute(self, tool_template: str, parameters: Dict[str, Any], tool_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行Python代码
        
        Args:
            tool_template: Python代码模板
            parameters: 执行参数
            tool_config: 工具配置
            
        Returns:
            执行结果
        """
        logger = logging.getLogger("app.services.executor.python")
        print(f"Python执行器被调用, 参数: {parameters}")
        print(f"Python代码模板: {tool_template[:100]}...")
        
        # 捕获输出和错误
        stdout_capture = io.StringIO()
        stderr_capture = io.StringIO()
        
        try:
            # 准备代码
            code = tool_template
            
            # 创建局部变量空间，包含参数
            local_vars = {
                'pd': pd,
                'parameters': parameters,
                'result': None
            }
            
            # 执行代码
            with redirect_stdout(stdout_capture), redirect_stderr(stderr_capture):
                # 在单独的线程中执行，避免阻塞
                loop = asyncio.get_running_loop()
                result = await loop.run_in_executor(
                    None, 
                    self._execute_code,
                    code,
                    local_vars
                )
            
            # 获取标准输出和错误输出
            stdout = stdout_capture.getvalue()
            stderr = stderr_capture.getvalue()
            
            # 获取执行结果
            execution_result = local_vars.get('result')
            
            # 如果结果是DataFrame，转换为字典
            if isinstance(execution_result, pd.DataFrame):
                execution_result = self.dataframe_to_dict(execution_result)
                
            return {
                "success": True,
                "data": execution_result,
                "stdout": stdout,
                "stderr": stderr,
                "error": None
            }
        except Exception as e:
            # 记录完整错误到日志，但不返回给用户
            exc_info = sys.exc_info()
            tb = ''.join(traceback.format_exception(*exc_info))
            full_error = f"Python执行失败: {str(e)}\n{tb}"
            logger.exception(full_error)
            
            # 只返回简化的错误信息给用户
            user_error = f"Python执行失败: {str(e)}"
            
            return {
                "success": False,
                "data": None,
                "stdout": stdout_capture.getvalue(),
                "stderr": stderr_capture.getvalue(),
                "error": user_error  # 返回简化的错误信息
            }
    
    def _execute_code(self, code: str, local_vars: Dict[str, Any]) -> Any:
        """
        执行Python代码
        
        Args:
            code: 要执行的代码
            local_vars: 局部变量
            
        Returns:
            执行结果
        """
        # 执行代码
        exec(code, {'__builtins__': __builtins__}, local_vars)
        
        # 返回结果
        return local_vars.get('result') 