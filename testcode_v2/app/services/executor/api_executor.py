from typing import Dict, Any, Optional
import pandas as pd
import httpx
import json
import asyncio
import logging
import jinja2
from jinja2 import Template
import re

from app.services.executor import ToolExecutor

class APIExecutor(ToolExecutor):
    """API工具执行器"""
    
    async def execute(self, tool_template: str, parameters: Dict[str, Any], tool_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行API调用
        
        Args:
            tool_template: API调用模板
            parameters: 调用参数
            tool_config: 工具配置
            
        Returns:
            API调用结果
        """
        logger = logging.getLogger("app.services.executor.api")
        
        try:
            # 打印调试信息
            print(f"API执行器被调用，模板: {tool_template}")
            print(f"API参数: {parameters}")
            print(f"API配置: {tool_config}")
            
            # 解析API调用模板
            try:
                if isinstance(tool_template, str):
                    api_template = json.loads(tool_template)
                else:
                    print("API模板不是字符串，直接使用")
                    api_template = tool_template
            except Exception as e:
                print(f"API模板解析失败: {str(e)}")
                # 创建一个默认的GET请求模板
                api_template = {
                    "url": tool_template if isinstance(tool_template, str) else "",
                    "method": "GET"
                }
            
            # 获取API相关配置
            base_url = tool_config.get("config", {}).get("base_url", "")
            headers = tool_config.get("config", {}).get("headers", {})
            
            # 渲染URL
            url = api_template.get("url", "")
            if base_url and not url.startswith(("http://", "https://")):
                url = f"{base_url}/{url.lstrip('/')}"

            # 处理路由参数 - 在Jinja2渲染前处理${param}格式的占位符
            route_params = api_template.get("route_params", {})
            if route_params:
                print(f"处理路由参数: {route_params}")
                for param_name, param_value in route_params.items():
                    if param_name in parameters:
                        # 替换${param}格式的占位符
                        placeholder = f"${{{param_name}}}"
                        url = url.replace(placeholder, str(parameters[param_name]))
                
            print(f"API最终URL: {url}")
            print(f"API最终模板: {api_template}")
            
            # 使用Jinja2渲染模板
            url_template = Template(url)
            rendered_url = url_template.render(**parameters)
            
            # 获取HTTP方法
            method = api_template.get("method", "GET").upper()
            
            # 渲染请求体
            body = api_template.get("body", None)
            if body and isinstance(body, str):
                body_template = Template(body)
                body = body_template.render(**parameters)
                try:
                    body = json.loads(body)
                except:
                    pass  # 如果不是JSON格式，保持原样
            elif body and isinstance(body, dict):
                # 渲染字典中的每个值
                for key, value in body.items():
                    if isinstance(value, str):
                        template = Template(value)
                        body[key] = template.render(**parameters)
            
            # 渲染参数
            query_params = api_template.get("params", {})
            if query_params:
                for key, value in query_params.items():
                    if isinstance(value, str):
                        template = Template(value)
                        query_params[key] = template.render(**parameters)
            
            # 合并自定义Headers
            custom_headers = api_template.get("headers", {})
            merged_headers = {**headers, **custom_headers}
            
            # 设置超时
            timeout = api_template.get("timeout", 30.0)
            
            # 执行HTTP请求
            async with httpx.AsyncClient(timeout=timeout) as client:
                if method == "GET":
                    response = await client.get(rendered_url, headers=merged_headers, params=query_params)
                elif method == "POST":
                    response = await client.post(rendered_url, headers=merged_headers, json=body, params=query_params)
                elif method == "PUT":
                    response = await client.put(rendered_url, headers=merged_headers, json=body, params=query_params)
                elif method == "DELETE":
                    response = await client.delete(rendered_url, headers=merged_headers, params=query_params)
                else:
                    raise ValueError(f"不支持的HTTP方法: {method}")
                
                # 处理响应
                try:
                    content_type = response.headers.get("content-type", "")
                    if "application/json" in content_type:
                        result = response.json()
                    else:
                        result = {"text": response.text}
                except:
                    result = {"text": response.text}
                
                # 提取数据
                data_extractor = api_template.get("data_extractor", None)
                if data_extractor:
                    result = self._extract_data(result, data_extractor)
                    
                # 如果是表格数据，尝试转换为DataFrame
                if isinstance(result, list) and len(result) > 0 and isinstance(result[0], dict):
                    df = pd.DataFrame(result)
                    return self.dataframe_to_dict(df)
                else:
                    return {
                        "result": result,
                        "status_code": response.status_code
                    }
                    
            return {
                "success": True,
                "data": result,
                "error": None
            }
        except Exception as e:
            logger.exception(f"API执行失败: {str(e)}")
            return {
                "success": False,
                "data": None,
                "error": str(e)
            }
    
    def _extract_data(self, data: Any, extractor: str) -> Any:
        """
        从结果中提取数据
        
        Args:
            data: 原始数据
            extractor: 提取器表达式
            
        Returns:
            提取的数据
        """
        # 支持简单的JSON Path
        if extractor.startswith("$"):
            parts = extractor[1:].split(".")
            result = data
            
            for part in parts:
                if part == "":
                    continue
                    
                # 处理数组索引，如 data[0]
                array_match = re.match(r"(\w+)\[(\d+)\]", part)
                if array_match:
                    key = array_match.group(1)
                    index = int(array_match.group(2))
                    if isinstance(result, dict) and key in result:
                        result = result[key]
                        if isinstance(result, list) and 0 <= index < len(result):
                            result = result[index]
                        else:
                            return None
                    else:
                        return None
                elif isinstance(result, dict) and part in result:
                    result = result[part]
                else:
                    return None
                    
            return result
            
        return data 