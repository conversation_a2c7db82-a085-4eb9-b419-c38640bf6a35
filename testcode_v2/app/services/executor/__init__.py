from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Union
import pandas as pd
import time
import json
from pydantic import BaseModel
import os
import traceback
from sqlalchemy.orm import Session

class ToolExecutor(ABC):
    """工具执行器基类"""
    
    @abstractmethod
    def execute(self, tool_or_id: Any, parameters: Dict[str, Any], db: Session = None) -> Dict[str, Any]:
        """
        执行工具
        
        参数:
            tool_or_id: 工具对象或ID
            parameters: 执行参数
            db: 数据库会话
            
        返回:
            执行结果
        """
        pass
    
    async def execute_and_format(self, tool_or_id: Any, parameters: Dict[str, Any], db: Session = None) -> Dict[str, Any]:
        """
        执行工具并格式化结果
        
        参数:
            tool_or_id: 工具对象或ID
            parameters: 执行参数
            db: 数据库会话
            
        返回:
            格式化后的执行结果
        """
        from app.services.result_formatter import ResultFormatter
        
        # 执行工具 - 使用原始execute方法避免递归
        if hasattr(self, 'original_execute'):
            result = await self.original_execute(tool_or_id, parameters, db)
        else:
            result = await self.execute(tool_or_id, parameters, db)
        
        # 如果执行失败，直接返回
        if not result.get("success", False):
            return result
        
        # 根据工具类型确定展示格式
        display_format = "json"  # 默认为JSON格式
        
        tool_type = None
        # 获取工具类型
        if hasattr(tool_or_id, "tool_type"):
            tool_type = str(tool_or_id.tool_type).upper() if tool_or_id.tool_type else None
        elif isinstance(tool_or_id, str) and db:
            try:
                from app.models.tool import Tool
                tool = db.query(Tool).filter(Tool.id == tool_or_id).first()
                if tool and tool.tool_type:
                    tool_type = str(tool.tool_type).upper()
            except Exception as e:
                print(f"获取工具类型失败: {str(e)}")
        
        # 根据工具类型确定展示格式
        if tool_type:
            if tool_type == "SQL":
                display_format = "table"
            elif tool_type == "GRAPH":
                display_format = "graph"
            elif tool_type == "VECTOR":
                display_format = "text"
            elif tool_type == "CHART":
                display_format = "chart"
        
        # 使用适当的展示格式格式化结果
        formatted_result = ResultFormatter.format_result(result, display_format)
        
        # 检查结果是否为字典类型，如果不是，转换为字典
        if not isinstance(formatted_result, dict):
            formatted_result = {
                "success": True,
                "data": formatted_result,
                "display_format": display_format
            }
        else:
            # 添加display_format到结果中，以便前端识别
            formatted_result["display_format"] = display_format
        
        return formatted_result
    
    @staticmethod
    def dataframe_to_dict(df: pd.DataFrame) -> Dict[str, Any]:
        """
        将DataFrame转换为字典
        
        Args:
            df: DataFrame对象
            
        Returns:
            转换后的字典
        """
        if df is None or df.empty:
            return {"columns": [], "data": [], "count": 0}
        
        try:
            # 尝试导入cx_Oracle处理LOB类型
            try:
                import cx_Oracle
                
                # 定义处理LOB对象的函数
                def process_lob(value):
                    """处理可能的LOB对象，将其转换为字符串"""
                    if value is None:
                        return None
                        
                    # 检查是否为LOB对象 - 使用str类型判断而不是isinstance
                    if hasattr(value, 'read') and 'LOB' in str(type(value)):
                        print(f"检测到LOB对象: {str(type(value).__name__)}")
                        try:
                            # 读取LOB对象内容
                            if 'CLOB' in str(type(value)):
                                print(f"处理CLOB对象")
                                result = value.read()
                                print(f"CLOB内容长度: {len(result) if result else 0}")
                                return result
                            elif 'BLOB' in str(type(value)):
                                print(f"处理BLOB对象")
                                # 对于BLOB，先转为bytes，然后尝试解码为字符串
                                # 如果无法解码，则返回bytes的十六进制表示
                                blob_data = value.read()
                                try:
                                    result = blob_data.decode('utf-8')
                                    print(f"BLOB解码为UTF-8字符串，长度: {len(result)}")
                                    return result
                                except UnicodeDecodeError:
                                    result = blob_data.hex()
                                    print(f"BLOB无法解码为UTF-8，转换为十六进制，长度: {len(result)}")
                                    return result
                            else:
                                # 其他LOB类型，尝试直接读取
                                print(f"处理其他LOB类型")
                                result = str(value.read())
                                print(f"其他LOB类型内容长度: {len(result) if result else 0}")
                                return result
                        except Exception as e:
                            print(f"读取LOB对象失败: {str(e)}")
                            # 返回空字符串而不是引发错误
                            return ""
                    return value
                
                # 使用DataFrame.map替代applymap (针对每列应用函数)
                processed_df = df.copy()
                for col in processed_df.columns:
                    processed_df[col] = processed_df[col].map(process_lob)
                df = processed_df
                
            except ImportError:
                # 如果无法导入cx_Oracle，则跳过LOB处理
                pass
            except Exception as e:
                print(f"处理LOB对象时出错: {str(e)}")
                traceback.print_exc()
            
            # 转换DataFrame为字典
            return {
                "columns": df.columns.tolist(),
                "data": df.values.tolist(),
                "count": len(df)
            }
        except Exception as e:
            print(f"DataFrame转换为字典时出错: {str(e)}")
            traceback.print_exc()
            # 出错时返回空结果
            return {
                "columns": [],
                "data": [],
                "count": 0
            }
    
    @staticmethod
    def execute_with_timer(func, *args, **kwargs) -> tuple:
        """
        执行函数并计时
        
        Args:
            func: 要执行的函数
            args: 位置参数
            kwargs: 关键字参数
            
        Returns:
            (执行结果, 执行时间)
        """
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        return result, end_time - start_time 

class SQLExecutor:
    """SQL执行器基类"""
    
    def __init__(self, connection_params: Dict[str, Any]):
        """
        初始化SQL执行器
        
        Args:
            connection_params: 连接参数
        """
        self.connection_params = connection_params
        self.connection = None
        
    def connect(self) -> None:
        """建立数据库连接，子类需要实现此方法"""
        raise NotImplementedError
        
    def close(self) -> None:
        """关闭数据库连接，子类需要实现此方法"""
        raise NotImplementedError
        
    def execute(self, sql: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        执行SQL语句
        
        Args:
            sql: SQL语句
            params: SQL参数
            
        Returns:
            执行结果
        """
        raise NotImplementedError
    
    def dataframe_to_dict(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        将Pandas DataFrame转换为字典
        
        Args:
            df: Pandas DataFrame
            
        Returns:
            Dict[str, Any]: 包含数据的字典
        """
        # 如果DataFrame为空，返回空列表和列名
        if df is None or df.empty:
            return {
                "columns": [],
                "data": [],
                "count": 0
            }
        
        try:
            # 尝试导入cx_Oracle处理LOB类型
            try:
                import cx_Oracle
                
                # 定义处理LOB对象的函数
                def process_lob(value):
                    """处理可能的LOB对象，将其转换为字符串"""
                    if value is None:
                        return None
                        
                    # 检查是否为LOB对象 - 使用str类型判断而不是isinstance
                    if hasattr(value, 'read') and 'LOB' in str(type(value)):
                        print(f"SQLExecutor: 检测到LOB对象: {str(type(value).__name__)}")
                        try:
                            # 读取LOB对象内容
                            if 'CLOB' in str(type(value)):
                                print(f"SQLExecutor: 处理CLOB对象")
                                result = value.read()
                                print(f"SQLExecutor: CLOB内容长度: {len(result) if result else 0}")
                                return result
                            elif 'BLOB' in str(type(value)):
                                print(f"SQLExecutor: 处理BLOB对象")
                                # 对于BLOB，先转为bytes，然后尝试解码为字符串
                                # 如果无法解码，则返回bytes的十六进制表示
                                blob_data = value.read()
                                try:
                                    result = blob_data.decode('utf-8')
                                    print(f"SQLExecutor: BLOB解码为UTF-8字符串，长度: {len(result)}")
                                    return result
                                except UnicodeDecodeError:
                                    result = blob_data.hex()
                                    print(f"SQLExecutor: BLOB无法解码为UTF-8，转换为十六进制，长度: {len(result)}")
                                    return result
                            else:
                                # 其他LOB类型，尝试直接读取
                                print(f"SQLExecutor: 处理其他LOB类型")
                                result = str(value.read())
                                print(f"SQLExecutor: 其他LOB类型内容长度: {len(result) if result else 0}")
                                return result
                        except Exception as e:
                            print(f"SQLExecutor: 读取LOB对象失败: {str(e)}")
                            # 返回空字符串而不是引发错误
                            return ""
                    return value
                
                # 使用DataFrame.map替代applymap (针对每列应用函数)
                processed_df = df.copy()
                for col in processed_df.columns:
                    processed_df[col] = processed_df[col].map(process_lob)
                df = processed_df
                
            except ImportError:
                # 如果无法导入cx_Oracle，则跳过LOB处理
                pass
            except Exception as e:
                print(f"处理LOB对象时出错: {str(e)}")
                traceback.print_exc()
            
            # 转换DataFrame为字典
            return {
                "columns": df.columns.tolist(),
                "data": df.values.tolist(),
                "count": len(df)
            }
        except Exception as e:
            print(f"DataFrame转换为字典时出错: {str(e)}")
            traceback.print_exc()
            # 出错时返回空结果
            return {
                "columns": [],
                "data": [],
                "count": 0
            } 

# 移除循环导入 - ExecutorFactory应该从外部导入
# from app.services.executor.factory import ExecutorFactory 