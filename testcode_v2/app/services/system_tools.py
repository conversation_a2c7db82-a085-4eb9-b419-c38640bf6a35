import logging
import uuid
from typing import List, Dict, Any

from app.models.tool import Tool, ToolType
from app.services.executor.auto_sql_executor import AutoSQLExecutor
from app.services.executor.factory import ExecutorFactory

# 配置日志
logger = logging.getLogger(__name__)

# 定义系统内置工具
SYSTEM_TOOLS = [
    {
        "id": "auto_sql_tool",
        "name": "自动SQL查询",
        "description": "直接执行提供的完整SQL查询语句（不使用参数化）",
        "tool_type": ToolType.AUTO_SQL,
        "template": "",  # 不需要模板，由执行器执行
        "parameters": [
            {
                "name": "sql",
                "description": "要执行的完整SQL查询语句，包含所有实际值（不使用参数占位符）",
                "type": "string",
                "required": True
            }
        ],
        "data_source_id": None  # 系统内置工具不需要关联数据源
    }
]

# 工具名称多语言映射
TOOL_NAME_I18N = {
    "auto_sql_tool": {
        "zh-CN": "自动SQL查询",
        "en-US": "Auto SQL Query"
    },
    "chart_generation_tool": {
        "zh-CN": "智能图表生成",
        "en-US": "Intelligent Chart Generation"
    }
}

class SystemTools:
    """系统内置工具服务"""
    
    @staticmethod
    def get_system_tools(language: str = 'zh-CN') -> List[Dict[str, Any]]:
        """
        获取系统内置工具列表，根据语言替换工具名称
        
        参数:
            language: 语言代码，默认为中文
        
        返回:
            内置工具列表
        """
        tools = []
        for tool in SYSTEM_TOOLS:
            # 复制工具配置
            tool_copy = tool.copy()
            
            # 根据语言替换工具名称
            tool_id = tool['id']
            if tool_id in TOOL_NAME_I18N and language in TOOL_NAME_I18N[tool_id]:
                tool_copy['name'] = TOOL_NAME_I18N[tool_id][language]
            
            tools.append(tool_copy)
        
        return tools
    
    @staticmethod
    def create_system_tool_object(tool_data: Dict[str, Any]) -> Tool:
        """
        创建系统内置工具对象（但不保存到数据库）
        
        参数:
            tool_data: 工具数据
            
        返回:
            工具对象
        """
        tool = Tool(
            id=tool_data.get("id", str(uuid.uuid4())),
            name=tool_data["name"],
            description=tool_data.get("description", ""),
            tool_type=tool_data["tool_type"],
            template=tool_data.get("template", ""),
            parameters=tool_data.get("parameters", []),
            data_source_id=tool_data.get("data_source_id")
        )
        return tool
    
    @staticmethod
    def register_system_tools():
        """
        注册系统内置工具到执行器工厂
        
        这个函数应该在应用启动时调用
        """
        logger.info("开始注册系统内置工具")
        
        # 注册自动SQL执行器
        ExecutorFactory.register_executor(ToolType.AUTO_SQL, AutoSQLExecutor)
        logger.info("自动SQL执行器注册成功")
        
        # 注意：智能图表生成工具已移除，现在由ChartGenerationAgent自动处理
        
        # 初始化执行器实例
        try:
            # 预先创建一个AutoSQLExecutor实例，确保schema加载正常
            auto_sql_executor = AutoSQLExecutor()
            logger.info(f"AutoSQLExecutor初始化成功，已加载schema描述")
        except Exception as e:
            logger.error(f"AutoSQLExecutor初始化失败: {str(e)}")
        
        # 检查系统工具定义
        for tool_def in SYSTEM_TOOLS:
            logger.info(f"注册系统工具: {tool_def['name']} ({tool_def['id']})")
            # 这里可以添加逻辑，将工具定义存入数据库，或者其他初始化工作
        
        logger.info("系统内置工具注册完成") 

    @staticmethod
    def get_tool_name(tool_id: str, language: str = 'zh-CN') -> str:
        """
        获取工具名称
        
        参数:
            tool_id: 工具ID
            language: 语言代码，默认为中文
            
        返回:
            工具名称
        """
        # 如果有多语言映射，使用映射的名称
        if tool_id in TOOL_NAME_I18N and language in TOOL_NAME_I18N[tool_id]:
            return TOOL_NAME_I18N[tool_id][language]
        
        # 否则从默认工具列表中查找
        for tool in SYSTEM_TOOLS:
            if tool['id'] == tool_id:
                return tool['name']
        
        return "Unknown Tool" if language == 'en-US' else "未知工具"