"""
专业数据分析器服务 - 重构版
专注于纯数据分析，不涉及业务决策
"""

import json
import logging
import statistics
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime

from app.services.llm_factory import get_llm_service
from app.utils.time_utils import get_shanghai_time

log = logging.getLogger(__name__)

@dataclass
class StatisticalSummary:
    """统计摘要数据结构"""
    total_rows: int
    columns: List[str]
    numeric_columns: List[str]
    categorical_columns: List[str]
    missing_values: Dict[str, int]
    basic_stats: Dict[str, Dict[str, float]]  # {column: {mean, median, std, min, max}}
    value_distributions: Dict[str, Dict[str, int]]  # {column: {value: count}}

@dataclass
class DataQualityAssessment:
    """数据质量评估数据结构"""
    completeness_score: float  # 完整性评分
    consistency_score: float   # 一致性评分
    validity_score: float      # 有效性评分
    overall_score: float       # 总体质量评分
    quality_issues: List[str]  # 质量问题列表

@dataclass
class AnalysisInsight:
    """分析洞察数据结构 - 支持三维递进式洞察"""
    insight_type: str  # trend, anomaly, correlation, pattern, summary
    title: str         # 洞察标题
    description: str   # 详细描述
    confidence: float  # 置信度 0-1
    supporting_data: Dict[str, Any] = None  # 支撑数据
    business_relevance: str = ""            # 业务相关性
    # 新增：三维洞察字段
    phenomenon_insight: str = ""      # 现象层洞察：数据呈现的直观模式
    causal_analysis: str = ""         # 本质分析：现象背后的根本原因
    next_analysis_direction: str = "" # 深入方向：建议的后续分析目标

@dataclass
class AnalysisResult:
    """专业数据分析结果数据结构"""
    statistical_summary: StatisticalSummary
    data_quality_assessment: DataQualityAssessment
    insights: List[AnalysisInsight]
    patterns: List[str]
    anomalies: List[str]
    correlations: List[Dict[str, Any]]  # 相关性分析结果

    # 为了兼容性，添加旧版本的属性
    @property
    def key_findings(self) -> List[str]:
        """从洞察中提取关键发现（兼容性属性）"""
        findings = []
        for insight in self.insights:
            findings.append(f"[{insight.insight_type}] {insight.title}")

        # 添加统计摘要信息
        if self.statistical_summary.total_rows > 0:
            findings.append(f"数据规模: {self.statistical_summary.total_rows}行 x {len(self.statistical_summary.columns)}列")

        # 添加数据质量信息
        if self.data_quality_assessment.overall_score > 0:
            findings.append(f"数据质量评分: {self.data_quality_assessment.overall_score:.2f}")

        return findings[:5]  # 限制数量

    @property
    def hypotheses(self) -> List[Dict[str, Any]]:
        """生成假设（兼容性属性）"""
        hypotheses = []

        # 基于异常生成假设
        for anomaly in self.anomalies[:2]:
            hypotheses.append({
                "hypothesis": f"异常现象: {anomaly}",
                "reasoning": "基于统计分析检测到的异常模式",
                "verification_methods": ["深入数据调查", "业务流程核实"],
                "priority": "medium"
            })

        # 基于相关性生成假设
        for corr in self.correlations[:1]:
            if corr['correlation'] > 0.5:
                hypotheses.append({
                    "hypothesis": f"{corr['column1']}与{corr['column2']}存在正相关关系",
                    "reasoning": f"相关系数为{corr['correlation']:.3f}，显示{corr['strength']}相关性",
                    "verification_methods": ["因果关系分析", "时间序列分析"],
                    "priority": "high" if abs(corr['correlation']) > 0.7 else "medium"
                })

        return hypotheses

    @property
    def recommended_next_steps(self) -> List[str]:
        """生成建议步骤（兼容性属性）"""
        steps = []

        # 基于数据质量给建议
        if self.data_quality_assessment.overall_score < 0.8:
            steps.append("改善数据质量：处理缺失值和异常值")

        # 基于异常给建议
        if self.anomalies:
            steps.append("调查检测到的数据异常")

        # 基于相关性给建议
        if self.correlations:
            steps.append("深入分析变量间的相关关系")

        # 基于数据规模给建议
        if self.statistical_summary.total_rows < 100:
            steps.append("考虑收集更多数据以提高分析可靠性")

        return steps[:5]  # 限制数量

    @property
    def data_quality_score(self) -> float:
        """数据质量评分（兼容性属性）"""
        return self.data_quality_assessment.overall_score

    @property
    def analysis_completeness(self) -> float:
        """分析完整度评分（兼容性属性）"""
        completeness = 0.0

        # 基于各个分析维度计算完整度
        if self.insights:
            completeness += 0.3
        if self.patterns:
            completeness += 0.2
        if self.anomalies:
            completeness += 0.2
        if self.correlations:
            completeness += 0.2
        if self.data_quality_assessment.overall_score > 0:
            completeness += 0.1

        return min(1.0, completeness)

class ProfessionalResultAnalyzer:
    """专业结果分析器 - 专注于纯数据分析"""
    
    def __init__(self, db=None):
        self.db = db
        self.llm_service = get_llm_service(db)
        
    async def analyze_result(
        self,
        tool_result: Dict[str, Any],
        context: Any,
        user_query: str,
        step_id: str,
        tool_name: str = "",
        user_language: str = 'zh-CN',
        intent_analysis: Dict[str, Any] = None,
        tool_reasoning: str = None,
        tool_parameters: Dict[str, Any] = None,
        conversation_context: str = None
    ) -> AnalysisResult:
        """
        对工具执行结果进行专业数据分析
        
        Args:
            tool_result: 工具执行结果
            context: 执行上下文
            user_query: 用户查询
            step_id: 步骤ID
            tool_name: 工具名称
            user_language: 用户语言偏好
            
        Returns:
            AnalysisResult: 专业分析结果
        """
        try:
            log.info(f"开始专业数据分析 [步骤ID: {step_id}, 工具: {tool_name}]")
            
            # 检查工具结果是否成功（支持布尔值和字符串形式）
            success_value = tool_result.get('success', True)
            if success_value is False or success_value == "False" or success_value == "false":
                log.warning(f"工具执行失败，跳过结果分析 [步骤ID: {step_id}]")
                return self._create_empty_analysis_result()
            
            # 提取数据内容
            data_content = self._extract_data_content(tool_result)
            if not data_content:
                log.info(f"未找到可分析的数据内容 [步骤ID: {step_id}]")
                return self._create_empty_analysis_result()
            
            # 1. 计算统计摘要
            statistical_summary = self._compute_statistical_summary(data_content)
            
            # 2. 评估数据质量（支持多语言）
            data_quality = self._assess_data_quality(data_content, statistical_summary, user_language)
            
            # 3. 检测模式和异常（支持多语言）
            patterns = self._detect_patterns(data_content, statistical_summary, user_language)
            anomalies = self._detect_anomalies(data_content, statistical_summary, user_language)
            
            # 4. 分析相关性（传入用户语言）
            correlations = self._analyze_correlations(data_content, statistical_summary, user_language)
            
            # 5. 提取业务洞察（使用LLM，包含完整上下文）
            insights = await self._extract_business_insights(
                data_content, statistical_summary, patterns, anomalies,
                user_query, tool_name, user_language,
                intent_analysis, tool_reasoning, tool_parameters, conversation_context, context
            )
            
            analysis_result = AnalysisResult(
                statistical_summary=statistical_summary,
                data_quality_assessment=data_quality,
                insights=insights,
                patterns=patterns,
                anomalies=anomalies,
                correlations=correlations
            )

            # 🔧 新增：保存分析结果到上下文历史
            if context and hasattr(context, 'add_analysis_result'):
                # 提取关键洞察信息用于历史摘要
                key_insights = []
                for insight in insights[:3]:  # 保存前3个关键洞察
                    key_insights.append({
                        'title': insight.title,
                        'description': insight.description,
                        'confidence': insight.confidence,
                        'insight_type': insight.insight_type
                    })

                # 构建分析结果摘要
                analysis_summary = {
                    'tool_name': tool_name,
                    'user_query': user_query,
                    'data_summary': {
                        'total_rows': statistical_summary.total_rows,
                        'columns': len(statistical_summary.columns),
                        'numeric_columns': len(statistical_summary.numeric_columns),
                        'categorical_columns': len(statistical_summary.categorical_columns)
                    },
                    'key_insights': key_insights,
                    'patterns_count': len(patterns),
                    'anomalies_count': len(anomalies),
                    'data_quality_score': data_quality.overall_score if hasattr(data_quality, 'overall_score') else 'N/A'
                }

                # 保存到上下文
                context.add_analysis_result(step_id, tool_name, analysis_summary)
                log.info(f"分析结果已保存到上下文历史 [步骤ID: {step_id}, 洞察数: {len(key_insights)}]")

            log.info(f"专业数据分析完成 [步骤ID: {step_id}, 洞察数: {len(insights)}]")

            return analysis_result
            
        except Exception as e:
            log.error(f"数据分析失败 [步骤ID: {step_id}]: {str(e)}")
            return self._create_empty_analysis_result()
    
    def _extract_data_content(self, tool_result: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """提取工具结果中的数据内容"""
        try:
            # 尝试从不同的字段中提取数据
            data_fields = ['results', 'data', 'result', 'rows', 'records', 'content']
            
            for field in data_fields:
                if field in tool_result and tool_result[field]:
                    data = tool_result[field]
                    
                    # 如果是列表且不为空
                    if isinstance(data, list) and len(data) > 0:
                        # 尝试从工具结果中获取列信息
                        columns = tool_result.get('columns', [])
                        if not columns and isinstance(data[0], dict):
                            columns = list(data[0].keys())
                        
                        # 尝试从工具结果中获取行数信息
                        row_count = tool_result.get('row_count', len(data))
                        if isinstance(row_count, str):
                            try:
                                row_count = int(row_count)
                            except ValueError:
                                row_count = len(data)
                        
                        return {
                            'type': 'tabular',
                            'data': data,
                            'row_count': row_count,
                            'columns': columns,
                            'sql': tool_result.get('sql', ''),
                            'execution_time': tool_result.get('execution_time', '')
                        }
                    
                    # 如果是字典
                    elif isinstance(data, dict):
                        return {
                            'type': 'structured',
                            'data': data,
                            'keys': list(data.keys())
                        }
                    
                    # 如果是字符串
                    elif isinstance(data, str) and len(data.strip()) > 0:
                        return {
                            'type': 'text',
                            'data': data,
                            'length': len(data)
                        }
            
            return None
            
        except Exception as e:
            log.warning(f"提取数据内容失败: {str(e)}")
            return None
    
    def _compute_statistical_summary(self, data_content: Dict[str, Any]) -> StatisticalSummary:
        """计算统计摘要"""
        try:
            if data_content['type'] != 'tabular':
                # 非表格数据的简单摘要
                return StatisticalSummary(
                    total_rows=1,
                    columns=[],
                    numeric_columns=[],
                    categorical_columns=[],
                    missing_values={},
                    basic_stats={},
                    value_distributions={}
                )
            
            data = data_content['data']
            columns = data_content['columns']
            total_rows = len(data)
            
            # 分析列类型
            numeric_columns = []
            categorical_columns = []
            missing_values = {}
            basic_stats = {}
            value_distributions = {}
            
            for col in columns:
                values = []
                missing_count = 0
                
                # 收集列值
                for row in data:
                    if col in row:
                        val = row[col]
                        if val is None or val == '' or val == 'null':
                            missing_count += 1
                        else:
                            values.append(val)
                    else:
                        missing_count += 1
                
                missing_values[col] = missing_count
                
                if not values:
                    continue
                
                # 尝试转换为数值
                numeric_values = []
                for val in values:
                    try:
                        if isinstance(val, str):
                            # 移除可能的货币符号和逗号
                            clean_val = val.replace(',', '').replace('$', '').replace('€', '').replace('¥', '')
                            numeric_values.append(float(clean_val))
                        else:
                            numeric_values.append(float(val))
                    except (ValueError, TypeError):
                        break
                
                # 判断是否为数值列
                if len(numeric_values) >= len(values) * 0.8:  # 80%以上可转换为数值
                    numeric_columns.append(col)
                    if numeric_values:
                        basic_stats[col] = {
                            'mean': statistics.mean(numeric_values),
                            'median': statistics.median(numeric_values),
                            'min': min(numeric_values),
                            'max': max(numeric_values),
                            'std': statistics.stdev(numeric_values) if len(numeric_values) > 1 else 0
                        }
                else:
                    categorical_columns.append(col)
                    # 计算值分布
                    value_counts = {}
                    for val in values:
                        str_val = str(val)
                        value_counts[str_val] = value_counts.get(str_val, 0) + 1
                    value_distributions[col] = value_counts
            
            return StatisticalSummary(
                total_rows=total_rows,
                columns=columns,
                numeric_columns=numeric_columns,
                categorical_columns=categorical_columns,
                missing_values=missing_values,
                basic_stats=basic_stats,
                value_distributions=value_distributions
            )
            
        except Exception as e:
            log.warning(f"计算统计摘要失败: {str(e)}")
            return StatisticalSummary(
                total_rows=0, columns=[], numeric_columns=[], categorical_columns=[],
                missing_values={}, basic_stats={}, value_distributions={}
            )

    def _assess_data_quality(self, data_content: Dict[str, Any], stats: StatisticalSummary, user_language: str = 'zh-CN') -> DataQualityAssessment:
        """评估数据质量（支持多语言）"""
        try:
            quality_issues = []

            # 计算完整性评分
            if stats.total_rows == 0:
                completeness_score = 0.0
                if user_language == 'en-US':
                    quality_issues.append("Dataset is empty")
                else:
                    quality_issues.append("数据集为空")
            else:
                total_cells = stats.total_rows * len(stats.columns)
                missing_cells = sum(stats.missing_values.values())
                completeness_score = max(0.0, (total_cells - missing_cells) / total_cells) if total_cells > 0 else 0.0

                if completeness_score < 0.9:
                    if user_language == 'en-US':
                        quality_issues.append(f"Low data completeness: {completeness_score:.2%}")
                    else:
                        quality_issues.append(f"数据完整性较低: {completeness_score:.2%}")

            # 计算一致性评分（基于数据类型一致性）
            consistency_score = 1.0
            for col in stats.columns:
                if col in stats.numeric_columns and col in stats.value_distributions:
                    # 数值列不应该有太多不同的字符串值
                    consistency_score *= 0.9
                    if user_language == 'en-US':
                        quality_issues.append(f"Column {col} has inconsistent data types")
                    else:
                        quality_issues.append(f"列 {col} 数据类型不一致")

            # 计算有效性评分（基于异常值检测）
            validity_score = 1.0
            for col, col_stats in stats.basic_stats.items():
                if col_stats['std'] == 0 and stats.total_rows > 1:
                    validity_score *= 0.95
                    if user_language == 'en-US':
                        quality_issues.append(f"Column {col} has identical values, potential issue")
                    else:
                        quality_issues.append(f"列 {col} 所有值相同，可能存在问题")

            # 总体评分
            overall_score = (completeness_score + consistency_score + validity_score) / 3

            return DataQualityAssessment(
                completeness_score=completeness_score,
                consistency_score=consistency_score,
                validity_score=validity_score,
                overall_score=overall_score,
                quality_issues=quality_issues
            )

        except Exception as e:
            if user_language == 'en-US':
                log.warning(f"Data quality assessment failed: {str(e)}")
                return DataQualityAssessment(0.0, 0.0, 0.0, 0.0, ["Data quality assessment failed"])
            else:
                log.warning(f"数据质量评估失败: {str(e)}")
                return DataQualityAssessment(0.0, 0.0, 0.0, 0.0, ["数据质量评估失败"])

    def _detect_patterns(self, data_content: Dict[str, Any], stats: StatisticalSummary, user_language: str = 'zh-CN') -> List[str]:
        """检测数据特征（智能判断是否显示）"""
        patterns = []

        try:
            if data_content['type'] == 'tabular':
                # 🔧 智能判断：对于聚合数据（行数少但列名包含聚合字段），跳过技术性特征检测
                is_aggregated_data = self._is_aggregated_data(data_content, stats)

                if is_aggregated_data:
                    # 🔧 优化：对于聚合数据，不再生成低价值的分析要点
                    # 直接返回空的patterns，让洞察生成专注于业务价值
                    return []

                # 检测数值分布特征（业务化描述）
                for col in stats.numeric_columns:
                    if col in stats.basic_stats:
                        col_stats = stats.basic_stats[col]
                        mean_val = col_stats['mean']
                        std_val = col_stats['std']

                        if std_val / mean_val < 0.1 if mean_val != 0 else False:
                            if user_language == 'en-US':
                                patterns.append(f"{col} shows consistent performance across records")
                            else:
                                patterns.append(f"{col}表现稳定，差异较小")
                        elif std_val / mean_val > 1.0 if mean_val != 0 else False:
                            if user_language == 'en-US':
                                patterns.append(f"{col} shows significant variation, indicating diverse patterns")
                            else:
                                patterns.append(f"{col}差异显著，呈现多样化特征")

                # 检测分类数据特征（业务化描述）
                for col in stats.categorical_columns:
                    if col in stats.value_distributions:
                        dist = stats.value_distributions[col]
                        unique_count = len(dist)
                        total_count = sum(dist.values())

                        if unique_count == total_count:
                            if user_language == 'en-US':
                                patterns.append(f"Complete diversity in {col} - each record is distinct")
                            else:
                                patterns.append(f"{col}完全多样化，每条记录都不同")
                        elif unique_count < total_count * 0.1:
                            if user_language == 'en-US':
                                patterns.append(f"{col} shows high concentration in few categories")
                            else:
                                patterns.append(f"{col}高度集中在少数类别")

                # 🔧 优化：移除数据规模特征描述，这些都是低价值的技术性描述
                # 让洞察生成专注于业务价值而非技术特征
                pass

        except Exception as e:
            if user_language == 'en-US':
                log.warning(f"Pattern detection failed: {str(e)}")
            else:
                log.warning(f"数据特征检测失败: {str(e)}")

        return patterns

    def _is_aggregated_data(self, data_content: Dict[str, Any], stats: StatisticalSummary) -> bool:
        """判断是否为聚合数据"""
        try:
            # 检查列名是否包含聚合字段的关键词
            aggregation_keywords = [
                'count', 'sum', 'avg', 'average', 'total', 'max', 'min',
                'group', '_count', '_sum', '_avg', '_total', '_amount',
                'customer_count', 'payment_amount', 'order_count'
            ]

            columns = stats.columns
            has_aggregation_columns = any(
                any(keyword in col.lower() for keyword in aggregation_keywords)
                for col in columns
            )

            # 如果行数较少（<20）且包含聚合字段，很可能是聚合数据
            is_small_with_aggregation = stats.total_rows < 20 and has_aggregation_columns

            # 检查是否有明显的分组字段（如age_group, category等）
            grouping_keywords = ['group', 'category', 'type', 'segment', 'range']
            has_grouping_columns = any(
                any(keyword in col.lower() for keyword in grouping_keywords)
                for col in columns
            )

            return is_small_with_aggregation or (stats.total_rows < 50 and has_grouping_columns)

        except Exception:
            return False

    def _detect_anomalies(self, data_content: Dict[str, Any], stats: StatisticalSummary, user_language: str = 'zh-CN') -> List[str]:
        """检测数据异常（支持多语言）"""
        anomalies = []

        try:
            if data_content['type'] == 'tabular':
                data = data_content['data']

                # 🔧 优化：移除技术性的数值异常检测
                # 零值检测、异常值检测等对业务决策价值不大
                # 让洞察生成专注于有业务意义的发现
                pass

                # 检测缺失值异常
                for col, missing_count in stats.missing_values.items():
                    if missing_count > stats.total_rows * 0.5:
                        if user_language == 'en-US':
                            anomalies.append(f"Column {col} has too many missing values ({missing_count}/{stats.total_rows})")
                        else:
                            anomalies.append(f"{col}列缺失值过多({missing_count}/{stats.total_rows})")

        except Exception as e:
            if user_language == 'en-US':
                log.warning(f"Anomaly detection failed: {str(e)}")
            else:
                log.warning(f"异常检测失败: {str(e)}")

        return anomalies

    def _analyze_correlations(self, data_content: Dict[str, Any], stats: StatisticalSummary, user_language: str = 'zh-CN') -> List[Dict[str, Any]]:
        """分析相关性"""
        correlations = []

        try:
            if data_content['type'] == 'tabular' and len(stats.numeric_columns) >= 2:
                data = data_content['data']

                # 🔧 优化：智能相关性分析，过滤显而易见的技术关联
                for i, col1 in enumerate(stats.numeric_columns):
                    for col2 in stats.numeric_columns[i+1:]:
                        try:
                            # 过滤显而易见的关联
                            if self._is_obvious_technical_correlation(col1, col2):
                                continue

                            values1 = []
                            values2 = []

                            for row in data:
                                if col1 in row and col2 in row:
                                    try:
                                        val1 = float(str(row[col1]).replace(',', ''))
                                        val2 = float(str(row[col2]).replace(',', ''))
                                        values1.append(val1)
                                        values2.append(val2)
                                    except (ValueError, TypeError):
                                        continue

                            if len(values1) >= 3:  # 至少需要3个数据点
                                correlation = self._calculate_correlation(values1, values2)
                                # 🔧 提高阈值，只保留强相关性且有业务意义的关联
                                if abs(correlation) > 0.6:  # 提高到60%以上
                                    # 生成业务友好的描述
                                    business_description = self._generate_business_correlation_description(
                                        col1, col2, correlation, user_language
                                    )
                                    if business_description:  # 只有能生成业务描述的才保留
                                        correlations.append({
                                            'column1': col1,
                                            'column2': col2,
                                            'correlation': correlation,
                                            'strength': 'strong' if abs(correlation) > 0.7 else 'moderate',
                                            'business_description': business_description
                                        })
                        except Exception:
                            continue

        except Exception as e:
            log.warning(f"相关性分析失败: {str(e)}")

        return correlations

    def _is_obvious_technical_correlation(self, col1: str, col2: str) -> bool:
        """判断是否是显而易见的技术关联"""
        obvious_pairs = [
            ('total', 'sum'),
            ('total', 'amount'),
            ('payment', 'amount'),
            ('average', 'mean'),
            ('count', 'number'),
            ('quantity', 'amount')
        ]

        col1_lower = col1.lower()
        col2_lower = col2.lower()

        for pair in obvious_pairs:
            if (pair[0] in col1_lower and pair[1] in col2_lower) or \
               (pair[1] in col1_lower and pair[0] in col2_lower):
                return True

        return False

    def _generate_business_correlation_description(self, col1: str, col2: str, correlation: float, user_language: str) -> str:
        """生成业务友好的相关性描述"""
        # 只为有业务意义的关联生成描述
        business_patterns = {
            ('age', 'payment'): {
                'zh-CN': f"年龄与支付金额呈{'正' if correlation > 0 else '负'}相关",
                'en-US': f"Age shows {'positive' if correlation > 0 else 'negative'} correlation with payment amount"
            },
            ('age', 'quantity'): {
                'zh-CN': f"年龄与购买数量呈{'正' if correlation > 0 else '负'}相关",
                'en-US': f"Age shows {'positive' if correlation > 0 else 'negative'} correlation with purchase quantity"
            }
        }

        col1_lower = col1.lower()
        col2_lower = col2.lower()

        # 检查是否匹配业务模式
        for (key1, key2), descriptions in business_patterns.items():
            if (key1 in col1_lower and key2 in col2_lower) or \
               (key2 in col1_lower and key1 in col2_lower):
                return descriptions.get(user_language, descriptions['zh-CN'])

        # 如果没有匹配的业务模式，返回空字符串（将被过滤掉）
        return ""

    def _calculate_correlation(self, x: List[float], y: List[float]) -> float:
        """计算皮尔逊相关系数"""
        if len(x) != len(y) or len(x) < 2:
            return 0.0

        n = len(x)
        sum_x = sum(x)
        sum_y = sum(y)
        sum_xy = sum(x[i] * y[i] for i in range(n))
        sum_x2 = sum(x[i] ** 2 for i in range(n))
        sum_y2 = sum(y[i] ** 2 for i in range(n))

        numerator = n * sum_xy - sum_x * sum_y
        denominator = ((n * sum_x2 - sum_x ** 2) * (n * sum_y2 - sum_y ** 2)) ** 0.5

        if denominator == 0:
            return 0.0

        return numerator / denominator

    async def _extract_business_insights(
        self,
        data_content: Dict[str, Any],
        stats: StatisticalSummary,
        patterns: List[str],
        anomalies: List[str],
        user_query: str,
        tool_name: str,
        user_language: str,
        intent_analysis: Dict[str, Any] = None,
        tool_reasoning: str = None,
        tool_parameters: Dict[str, Any] = None,
        conversation_context: str = None,
        context: Any = None
    ) -> List[AnalysisInsight]:
        """提取业务洞察（使用LLM）"""
        try:
            # 构建专业的分析上下文（包含完整上下文信息）
            analysis_context = self._build_analysis_context(
                data_content, stats, patterns, anomalies, user_query, tool_name,
                intent_analysis, tool_reasoning, tool_parameters, conversation_context, context
            )

            # 🔧 修复：构建多语言支持的洞察提取提示词
            if user_language == 'en-US':
                system_prompt = """You are a professional data analyst specializing in extracting business insights from statistical analysis results.
Your tasks are:
1. Identify key data characteristics based on statistical summaries
2. Discover important business insights by combining patterns and anomalies
3. Evaluate the confidence and business relevance of insights
4. Describe data phenomena only, do not provide business recommendations

Please return results strictly in JSON format."""

                user_prompt = f"""Please extract business insights based on the following data analysis results:

{analysis_context}

Please return a JSON list of insights:
{{
  "insights": [
    {{
      "insight_type": "trend|anomaly|correlation|pattern|summary",
      "title": "Insight title",
      "description": "Core finding description",
      "confidence": 0.8,
      "business_relevance": "Description of business relevance"
    }}
  ]
}}

**Important Requirements:**
1. Titles should be concise and powerful, highlighting core findings
2. Descriptions should be precise, emphasizing key numbers and trends
3. Avoid lengthy explanations, directly state findings
4. Focus on significant features in the data, avoid giving business recommendations"""
            else:
                system_prompt = """你是一个专业的商业数据分析师，专注于从数据中提取有价值的业务洞察。

你的核心任务是进行三维递进式洞察分析：

**【第一维：现象洞察】** - 数据呈现了什么？
1. 识别关键模式、趋势和统计特征
2. 发现异常和特殊现象
3. 描述数据的直观表现

**【第二维：本质分析】** - 为什么会这样？
1. 分析现象背后的根本原因
2. 解释业务逻辑和驱动因素
3. 识别因果关系和影响机制
4. 评估外部因素的作用

**【第三维：深入方向】** - 接下来应该分析什么？
1. 提出需要验证的假设
2. 建议具体的深入分析方向
3. 推荐相关的数据源和分析方法
4. 预测可能获得的洞察类型

洞察质量标准：
- 现象描述必须包含具体数字和比例
- 本质分析必须有清晰的因果逻辑
- 深入方向必须具体可执行
- 避免"可能"、"或许"等模糊表述
- 突出商业价值和决策指导意义

请严格按照JSON格式返回三维洞察结果。"""

                user_prompt = f"""请基于以下数据分析结果进行三维递进式洞察分析：

{analysis_context}

请返回JSON格式的三维洞察结果：
{{
  "insights": [
    {{
      "insight_type": "trend|anomaly|correlation|pattern|summary",
      "title": "洞察标题",
      "description": "核心发现描述",
      "confidence": 0.8,
      "business_relevance": "与业务的相关性描述",
      "phenomenon_insight": "现象层描述：数据呈现的直观模式和发现",
      "causal_analysis": "本质分析：现象背后的根本原因和业务逻辑",
      "next_analysis_direction": "深入方向：建议的后续分析目标和方法"
    }}
  ]
}}

**高价值洞察要求：**
1. 标题必须突出具体的业务发现（如"核心客户群"、"消费能力突出"）
2. 描述必须包含具体数字和明确的商业含义
3. 专注于客户群体、消费行为、市场机会等业务洞察
4. 避免技术术语（如"异常值"、"数据质量"、"统计摘要"）
5. 避免模糊表述（如"可能"、"或许"、"需要核实"）"""
            # 🔧 修复：根据用户语言偏好添加语言指令
            if user_language == 'en-US':
                # 英文指令增强
                language_suffix = "\n\nCRITICAL: Your entire response must be in English language only. Do not use any Chinese characters."
                system_prompt = "LANGUAGE REQUIREMENT: You MUST respond ONLY in English. " + system_prompt + language_suffix
            else:
                # 中文指令增强
                language_suffix = "\n\n重要提醒：你的全部回复都必须使用中文，不要使用任何英文词汇或句子。"
                system_prompt = "语言要求：你必须完全使用中文回复。" + system_prompt + language_suffix

            print("分析用户提示词：：：",user_prompt)
            print("分析系统提示词",system_prompt)
            # 调用LLM
            if hasattr(self.llm_service, 'client'):
                log.info("开始调用LLM进行业务洞察提取...")
                try:
                    completion = await self.llm_service.client.chat.completions.create(
                        model=self.llm_service.model,
                        messages=[
                            {"role": "system", "content": system_prompt},
                            {"role": "user", "content": user_prompt}
                        ],
                        response_format={"type": "json_object"},
                        temperature=0.2
                    )
                    log.info("LLM调用成功，开始解析响应...")
                except Exception as llm_error:
                    log.error(f"LLM调用失败: {str(llm_error)}")
                    raise llm_error

                response_text = completion.choices[0].message.content
                response_data = json.loads(response_text)
                # 解析三维洞察
                insights = []
                for insight_data in response_data.get('insights', []):
                    insight = AnalysisInsight(
                        insight_type=insight_data.get('insight_type', 'summary'),
                        title=insight_data.get('title', ''),
                        description=insight_data.get('description', ''),
                        confidence=float(insight_data.get('confidence', 0.5)),
                        business_relevance=insight_data.get('business_relevance', ''),
                        # 新增：三维洞察字段
                        phenomenon_insight=insight_data.get('phenomenon_insight', ''),
                        causal_analysis=insight_data.get('causal_analysis', ''),
                        next_analysis_direction=insight_data.get('next_analysis_direction', '')
                    )
                    insights.append(insight)

                return insights

            else:
                log.warning("LLM服务不可用，返回基础洞察")
                return self._generate_basic_insights(stats, patterns, anomalies, user_language)

        except Exception as e:
            log.warning(f"业务洞察提取失败: {str(e)}")
            # 即使LLM调用失败，也要返回基础洞察，确保分析流程继续
            return self._generate_basic_insights(stats, patterns, anomalies, user_language)

    def _build_analysis_context(
        self,
        data_content: Dict[str, Any],
        stats: StatisticalSummary,
        patterns: List[str],
        anomalies: List[str],
        user_query: str,
        tool_name: str,
        intent_analysis: Dict[str, Any] = None,
        tool_reasoning: str = None,
        tool_parameters: Dict[str, Any] = None,
        conversation_context: str = None,
        context: Any = None
    ) -> str:
        """构建分析上下文（增强版：包含关键业务数据）"""
        context_parts = [
            f"**用户查询**: {user_query}",
            f"**工具名称**: {tool_name}",
            f"**数据类型**: {data_content['type']}",
            "",
            "**统计摘要**:",
            f"- 总行数: {stats.total_rows}",
            f"- 总列数: {len(stats.columns)}",
            f"- 数值列: {len(stats.numeric_columns)} ({', '.join(stats.numeric_columns[:5])}{'...' if len(stats.numeric_columns) > 5 else ''})",
            f"- 分类列: {len(stats.categorical_columns)} ({', '.join(stats.categorical_columns[:5])}{'...' if len(stats.categorical_columns) > 5 else ''})",
        ]

        # 添加数值列统计
        if stats.basic_stats:
            context_parts.append("\n**数值列统计**:")
            for col, col_stats in list(stats.basic_stats.items())[:3]:  # 只显示前3列
                context_parts.append(f"- {col}: 均值={col_stats['mean']:.2f}, 中位数={col_stats['median']:.2f}, 标准差={col_stats['std']:.2f}")

        # 🔧 删除重复：这部分信息将在后面统一添加

        # 添加模式
        if patterns:
            context_parts.append(f"\n**检测到的模式**: {', '.join(patterns[:5])}")

        # 添加异常
        if anomalies:
            context_parts.append(f"\n**检测到的异常**: {', '.join(anomalies[:5])}")

        # 添加数据质量
        context_parts.append(f"\n**数据质量评分**: {stats.total_rows}")  # 这里应该传入quality assessment

        # 🔧 新增：添加意图分析结果
        if intent_analysis:
            context_parts.append("\n**意图分析结果**:")
            context_parts.append(f"- 分析意图: {intent_analysis.get('intent_description', '未知')}")

            execution_steps = intent_analysis.get('execution_steps', [])
            if execution_steps:
                context_parts.append("- 执行步骤:")
                for i, step in enumerate(execution_steps[:3], 1):  # 只显示前3个步骤
                    step_intent = step.get('step_intent', '未知步骤')
                    context_parts.append(f"  {i}. {step_intent}")

            if intent_analysis.get('needs_clarification'):
                context_parts.append("- 需要澄清: 是")

            confidence = intent_analysis.get('confidence', 0)
            if confidence:
                context_parts.append(f"- 意图置信度: {confidence:.0%}")

        # 🔧 新增：添加工具执行信息
        if tool_reasoning:
            context_parts.append(f"\n**工具选择理由**: {tool_reasoning}")

        if tool_parameters:
            context_parts.append("\n**工具执行参数**:")
            for key, value in tool_parameters.items():
                # 对长SQL进行适当格式化
                if key.lower() == 'sql' and isinstance(value, str) and len(value) > 200:
                    formatted_sql = value.replace('SELECT', '\nSELECT').replace('FROM', '\nFROM').replace('JOIN', '\nJOIN').replace('WHERE', '\nWHERE').replace('GROUP BY', '\nGROUP BY').replace('ORDER BY', '\nORDER BY')
                    context_parts.append(f"- {key}:{formatted_sql}")
                else:
                    context_parts.append(f"- {key}: {value}")

        # 🔧 新增：添加完整的工具执行结果数据
        if data_content['type'] == 'tabular' and 'data' in data_content:
            context_parts.append("\n**工具执行结果**:")
            data = data_content['data']
            columns = data_content.get('columns', [])

            if columns:
                context_parts.append(f"列名: {', '.join(columns)}")

            context_parts.append(f"完整数据（共{len(data)}行）:")

            # 🔧 关键：传递全部数据，不进行采样
            for i, row in enumerate(data):
                if isinstance(row, dict):
                    row_str = ', '.join([f"{k}={v}" for k, v in row.items()])
                    context_parts.append(f"  行{i+1}: {row_str}")
                else:
                    context_parts.append(f"  行{i+1}: {row}")

        # 🔧 新增：添加对话上下文
        if conversation_context:
            context_parts.append(f"\n**对话上下文**: {conversation_context}")

        # 🔧 新增：添加历史分析摘要（解决渐进式分析问题）
        if context and hasattr(context, 'analysis_results_history') and context.analysis_results_history:
            # 优先使用analysis_results_history（包含分析洞察）
            recent_analyses = context.analysis_results_history[-3:]  # 最近3步分析
            if recent_analyses:
                context_parts.append("\n**历史分析摘要**:")
                context_parts.append("之前的分析步骤和关键发现：")
                for i, analysis in enumerate(recent_analyses, 1):
                    analysis_result = analysis.get('analysis_result', {})
                    tool_name = analysis_result.get('tool_name', '未知工具')
                    user_query = analysis_result.get('user_query', '')
                    data_summary = analysis_result.get('data_summary', {})
                    key_insights = analysis_result.get('key_insights', [])

                    # 显示分析步骤摘要
                    query_summary = user_query[:40] + "..." if len(user_query) > 40 else user_query
                    context_parts.append(f"{i}. {tool_name}: {query_summary}")

                    # 显示数据摘要
                    if data_summary:
                        total_rows = data_summary.get('total_rows', 0)
                        context_parts.append(f"   - 分析了{total_rows}行数据")

                    # 显示关键洞察
                    if key_insights:
                        context_parts.append(f"   - 关键发现:")
                        for insight in key_insights[:2]:  # 最多显示2个洞察
                            title = insight.get('title', '未知洞察')
                            context_parts.append(f"     • {title}")

        elif context and hasattr(context, 'execution_history') and context.execution_history:
            # 回退到execution_history（如果没有analysis_results_history）
            recent_executions = context.execution_history[-3:]  # 最近3步
            if recent_executions:
                context_parts.append("\n**历史分析摘要**:")
                context_parts.append("之前的分析步骤和工具执行：")
                for i, execution in enumerate(recent_executions, 1):
                    tool_name = execution.get('tool_name', '未知工具')
                    reasoning = execution.get('reasoning', '无说明')
                    # 截取reasoning的前50个字符作为摘要
                    summary = reasoning[:50] + "..." if len(reasoning) > 50 else reasoning
                    context_parts.append(f"{i}. {tool_name}: {summary}")

                    # 如果有结果，尝试提取关键信息
                    result = execution.get('result')
                    if result and isinstance(result, dict):
                        if 'data' in result and isinstance(result['data'], list):
                            data_count = len(result['data'])
                            context_parts.append(f"   - 获得{data_count}行数据")
                        elif 'insights' in result and isinstance(result['insights'], list):
                            insights_count = len(result['insights'])
                            context_parts.append(f"   - 生成{insights_count}个洞察")

        # 🔧 修复：添加评估历史摘要（修复空标题问题）
        if context and hasattr(context, 'evaluation_history') and context.evaluation_history:
            recent_evaluations = context.evaluation_history[-2:]  # 最近2次评估
            # 🔧 关键修复：先检查是否有有效的评估内容，再添加标题
            valid_evaluations = []
            for evaluation in recent_evaluations:
                eval_result = evaluation.get('evaluation_result', {})
                if isinstance(eval_result, dict):
                    coverage = eval_result.get('coverage_analysis', '')
                    missing = eval_result.get('missing_elements', '')
                    if coverage or missing:  # 只有当有实际内容时才认为是有效评估
                        valid_evaluations.append(evaluation)

            # 只有当有有效评估时才添加标题和内容
            if valid_evaluations:
                context_parts.append("\n**评估历史摘要**:")
                context_parts.append("之前的分析评估结果：")
                for i, evaluation in enumerate(valid_evaluations, 1):
                    tool_name = evaluation.get('tool_name', '未知工具')
                    eval_result = evaluation.get('evaluation_result', {})
                    # 提取关键评估信息
                    coverage = eval_result.get('coverage_analysis', '')
                    missing = eval_result.get('missing_elements', '')
                    if coverage:
                        coverage_summary = coverage[:40] + "..." if len(coverage) > 40 else coverage
                        context_parts.append(f"{i}. {tool_name}评估: {coverage_summary}")
                    if missing:
                        missing_summary = missing[:40] + "..." if len(missing) > 40 else missing
                        context_parts.append(f"   - 缺失: {missing_summary}")

        # 🔧 优化：专注高价值业务洞察的生成指导
        context_parts.append("\n**高价值洞察生成要求**:")
        context_parts.append("请专注生成具有业务价值的洞察，避免技术性描述：")
        context_parts.append("1. 识别核心客户群体和消费特征")
        context_parts.append("2. 发现年龄段与消费行为的关联模式")
        context_parts.append("3. 挖掘品类偏好和消费能力的关系")
        context_parts.append("4. 突出显著的业务机会或风险点")
        context_parts.append("5. 避免纯技术性的数据质量或异常值描述")

        return "\n".join(context_parts)

    def _build_smart_data_summary(self, data: List[Dict], columns: List[str], user_query: str) -> List[str]:
        """构建智能数据摘要（针对大数据集）"""
        summary_parts = []

        try:
            # 基础信息
            summary_parts.append("\n**数据摘要**:")
            summary_parts.append(f"列名: {', '.join(columns)}")
            summary_parts.append(f"总行数: {len(data)}行")

            # 智能采样：显示代表性数据
            if len(data) > 20:
                # 显示前5行、中间5行、后5行
                summary_parts.append("\n**代表性数据样本**:")

                # 前5行
                summary_parts.append("前5行:")
                for i in range(min(5, len(data))):
                    row = data[i]
                    if isinstance(row, dict):
                        row_str = ', '.join([f"{k}={v}" for k, v in row.items()])
                        summary_parts.append(f"  行{i+1}: {row_str}")

                # 中间5行
                if len(data) > 10:
                    mid_start = len(data) // 2 - 2
                    summary_parts.append(f"\n中间5行（第{mid_start+1}-{mid_start+5}行）:")
                    for i in range(mid_start, min(mid_start + 5, len(data))):
                        row = data[i]
                        if isinstance(row, dict):
                            row_str = ', '.join([f"{k}={v}" for k, v in row.items()])
                            summary_parts.append(f"  行{i+1}: {row_str}")

                # 后5行
                if len(data) > 15:
                    summary_parts.append(f"\n后5行（第{len(data)-4}-{len(data)}行）:")
                    for i in range(max(0, len(data) - 5), len(data)):
                        row = data[i]
                        if isinstance(row, dict):
                            row_str = ', '.join([f"{k}={v}" for k, v in row.items()])
                            summary_parts.append(f"  行{i+1}: {row_str}")

            # 🔧 关键：添加分组统计（针对年龄段分析）
            if 'age' in columns and 'category_name' in columns:
                summary_parts.extend(self._build_age_category_analysis(data))

        except Exception as e:
            # 如果智能摘要失败，回退到简单显示
            summary_parts = [
                "\n**数据样本**:",
                f"列名: {', '.join(columns)}",
                f"数据内容（显示前10行）:"
            ]
            for i in range(min(10, len(data))):
                row = data[i]
                if isinstance(row, dict):
                    row_str = ', '.join([f"{k}={v}" for k, v in row.items()])
                    summary_parts.append(f"  行{i+1}: {row_str}")

            if len(data) > 10:
                summary_parts.append(f"  ... (还有{len(data) - 10}行数据)")

        return summary_parts

    def _build_age_category_analysis(self, data: List[Dict]) -> List[str]:
        """构建年龄段-品类分析摘要"""
        analysis_parts = []

        try:
            # 统计年龄段分布
            age_groups = {}
            category_counts = {}

            for row in data:
                age = row.get('age')
                category = row.get('category_name')

                if age is not None:
                    # 按10岁分组
                    age_group = f"{(age // 10) * 10}-{(age // 10) * 10 + 9}岁"
                    age_groups[age_group] = age_groups.get(age_group, 0) + 1

                if category:
                    category_counts[category] = category_counts.get(category, 0) + 1

            # 显示年龄段分布（前5个）
            if age_groups:
                analysis_parts.append("\n**年龄段分布**:")
                sorted_age_groups = sorted(age_groups.items(), key=lambda x: x[1], reverse=True)
                for age_group, count in sorted_age_groups[:5]:
                    percentage = (count / len(data)) * 100
                    analysis_parts.append(f"- {age_group}: {count}人 ({percentage:.1f}%)")

                if len(sorted_age_groups) > 5:
                    analysis_parts.append(f"- 其他年龄段: {len(sorted_age_groups) - 5}个")

            # 显示品类分布（前5个）
            if category_counts:
                analysis_parts.append("\n**品类分布**:")
                sorted_categories = sorted(category_counts.items(), key=lambda x: x[1], reverse=True)
                for category, count in sorted_categories[:5]:
                    percentage = (count / len(data)) * 100
                    analysis_parts.append(f"- {category}: {count}笔 ({percentage:.1f}%)")

                if len(sorted_categories) > 5:
                    analysis_parts.append(f"- 其他品类: {len(sorted_categories) - 5}个")

        except Exception:
            analysis_parts.append("\n**分组统计**: 数据处理中遇到问题，使用原始数据进行分析")

        return analysis_parts

    def _generate_basic_insights(self, stats: StatisticalSummary, patterns: List[str], anomalies: List[str], user_language: str = 'zh-CN') -> List[AnalysisInsight]:
        """生成基础洞察（不使用LLM，支持多语言）"""
        insights = []

        # 数据规模洞察
        if stats.total_rows > 1000:
            if user_language == 'en-US':
                insights.append(AnalysisInsight(
                    insight_type="summary",
                    title="Large Dataset",
                    description=f"The dataset contains {stats.total_rows} records, classified as a large dataset",
                    confidence=1.0,
                    business_relevance="Large datasets may contain more valuable patterns and trends"
                ))
            else:
                insights.append(AnalysisInsight(
                    insight_type="summary",
                    title="大规模数据集",
                    description=f"数据集包含{stats.total_rows}行记录，属于大规模数据集",
                    confidence=1.0,
                    business_relevance="大数据集可能包含更多有价值的模式和趋势"
                ))

        # 数据质量洞察
        missing_ratio = sum(stats.missing_values.values()) / (stats.total_rows * len(stats.columns)) if stats.total_rows > 0 and stats.columns else 0
        if missing_ratio > 0.1:
            if user_language == 'en-US':
                insights.append(AnalysisInsight(
                    insight_type="anomaly",
                    title="Data Completeness Issue",
                    description=f"The dataset has {missing_ratio:.1%} missing values",
                    confidence=0.9,
                    business_relevance="Data completeness affects the reliability of analysis results"
                ))
            else:
                insights.append(AnalysisInsight(
                    insight_type="anomaly",
                    title="数据完整性问题",
                    description=f"数据集存在{missing_ratio:.1%}的缺失值",
                    confidence=0.9,
                    business_relevance="数据完整性影响分析结果的可靠性"
                ))

        # 数值分布洞察
        for col, col_stats in stats.basic_stats.items():
            if col_stats['std'] / col_stats['mean'] > 1.0 if col_stats['mean'] != 0 else False:
                if user_language == 'en-US':
                    insights.append(AnalysisInsight(
                        insight_type="pattern",
                        title=f"High Volatility in Column {col}",
                        description=f"Column {col} has a coefficient of variation of {col_stats['std']/col_stats['mean']:.2f}, indicating high volatility",
                        confidence=0.8,
                        business_relevance="High volatility may reflect business instability or diversity"
                    ))
                else:
                    insights.append(AnalysisInsight(
                        insight_type="pattern",
                        title=f"{col}列数值波动大",
                        description=f"{col}列的变异系数为{col_stats['std']/col_stats['mean']:.2f}，数值波动较大",
                        confidence=0.8,
                        business_relevance="高波动性可能反映业务的不稳定性或多样性"
                    ))

        return insights

    def _create_empty_analysis_result(self) -> AnalysisResult:
        """创建空的分析结果"""
        return AnalysisResult(
            statistical_summary=StatisticalSummary(
                total_rows=0, columns=[], numeric_columns=[], categorical_columns=[],
                missing_values={}, basic_stats={}, value_distributions={}
            ),
            data_quality_assessment=DataQualityAssessment(0.0, 0.0, 0.0, 0.0, []),
            insights=[],
            patterns=[],
            anomalies=[],
            correlations=[]
        )
