"""
场景Agent调用器
================
专门用于场景预测的Agent调用器，完全隔离不影响原有Agent流程
"""

import uuid
import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from sqlalchemy.orm import Session
from asyncio import Semaphore

from app.services.analyzer import AnalyzerService
from app.services.llm_analyzer import LLMStreamAnalyzer, ExecutionContext, StreamPromptGenerator
from app.utils.time_utils import get_shanghai_time

logger = logging.getLogger(__name__)

class ScenarioAgentCaller:
    """专门用于场景预测的Agent调用器，完全隔离"""
    
    def __init__(self, db: Session):
        self.db = db
        # 限制并发数，避免性能问题
        self.semaphore = Semaphore(2)  # 最多2个并发
    
    async def analyze_scenario_with_agent(self, predicted_question: str, project_id: str, user_language: str = 'zh-CN', session_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """使用Agent能力分析预测场景，完全隔离

        Args:
            predicted_question: 预测的用户问题
            project_id: 项目ID
            user_language: 用户语言偏好，默认为中文
            session_data: 会话数据，包含业务理解结果和用户回答

        Returns:
            Dict[str, Any]: 分析结果
        """
        temp_session_id = f"scenario_temp_{uuid.uuid4()}"
        
        async with self.semaphore:  # 控制并发
            try:
                logger.info(f"开始Agent场景分析: {predicted_question[:50]}...")
                
                # 1. 创建隔离的服务实例
                analyzer = AnalyzerService(db=self.db)
                
                # 2. 获取Schema和工具（只读操作）
                schema_info = analyzer._format_schema_for_prompt(project_id)
                available_tools = await analyzer.get_available_tools(project_id, user_language=user_language)

                # 3. 构建增强的业务上下文信息
                enhanced_context = self._build_enhanced_context(
                    schema_info, session_data, project_id
                )
                # 3. 创建LLMStreamAnalyzer实例（用于调用新版意图识别）
                llm_analyzer = LLMStreamAnalyzer(db=self.db)

                # 4. 格式化工具列表
                formatted_tools = [
                    {
                        "name": tool.name,
                        "description": tool.description,
                        "tool_type": tool.tool_type,
                        "parameters": tool.parameters
                    }
                    for tool in available_tools
                ]

                # 5. 调用新版意图识别（使用增强上下文）
                intent_analysis = await llm_analyzer._analyze_user_intent(
                    user_query=predicted_question,
                    available_tools=formatted_tools,
                    schema_info=enhanced_context,  # 使用增强的上下文
                    project_id=project_id,
                    user_language=user_language
                )
                logger.info(f"使用新版意图识别完成，结果类型: {intent_analysis.get('action_type', 'unknown')}")

                # 6. 提取关键信息（基于意图识别结果）
                understanding_points = self._extract_understanding_points_from_intent(intent_analysis)
                
                logger.info(f"Agent场景分析完成: {predicted_question[:30]}...")

                # 构建结果对象（重点突出意图识别）
                result = {
                    "success": True,
                    "predicted_question": predicted_question,
                    "understanding_points": understanding_points,
                    "intent_analysis": {
                        "action_type": intent_analysis.get("action_type", "intent_analysis"),
                        "intent_description": intent_analysis.get("intent_description", ""),
                        "execution_steps": intent_analysis.get("execution_steps", []),  # 使用正确的字段名
                        "needs_clarification": intent_analysis.get("needs_clarification", False),
                        "confidence": intent_analysis.get("confidence", 0.95),  # 添加置信度
                        "reasoning": intent_analysis.get("reasoning", "")  # 添加推理过程
                    },
                    "analysis_time": get_shanghai_time().isoformat()
                }

                # 如果是数据无关的情况，添加message字段
                if intent_analysis.get("action_type") == "data_irrelevant":
                    result["intent_analysis"]["message"] = intent_analysis.get("message", "")
                return result
                
            except asyncio.TimeoutError:
                logger.warning(f"Agent场景分析超时: {predicted_question[:30]}...")
                return {
                    "success": False,
                    "error": "分析超时",
                    "understanding_points": ["Agent分析超时，使用默认理解"]
                }
            except Exception as e:
                logger.error(f"Agent场景分析失败: {str(e)}")
                return {
                    "success": False,
                    "error": str(e),
                    "understanding_points": ["Agent分析失败，使用默认理解"]
                }
    
    def _extract_understanding_points(self, intent_analysis: Dict, planning_result: Dict) -> List[str]:
        """从Agent结果中提取理解要点
        
        Args:
            intent_analysis: 意图分析结果
            planning_result: 规划结果
            
        Returns:
            List[str]: 理解要点列表
        """
        points = []
        
        try:
            # 从意图分析中提取
            if isinstance(intent_analysis, dict):
                if intent_analysis.get("problem_understanding"):
                    points.append(f"问题理解: {intent_analysis['problem_understanding']}")
                
                if intent_analysis.get("required_data"):
                    data_types = intent_analysis["required_data"]
                    if isinstance(data_types, list):
                        points.append(f"所需数据: {', '.join(data_types)}")
                
                # 从分析步骤中提取
                analysis_steps = intent_analysis.get("analysis_steps", [])
                if isinstance(analysis_steps, list) and analysis_steps:
                    points.append(f"分析步骤: 共{len(analysis_steps)}个步骤")
                    for i, step in enumerate(analysis_steps[:3], 1):  # 只显示前3步
                        if isinstance(step, dict):
                            step_purpose = step.get("purpose", step.get("tool_name", f"步骤{i}"))
                            points.append(f"步骤{i}: {step_purpose}")
            
            # 从规划结果中提取
            if isinstance(planning_result, dict):
                if planning_result.get("reasoning"):
                    points.append(f"分析思路: {planning_result['reasoning']}")
                
                # 从执行步骤中提取
                steps = planning_result.get("steps", [])
                if isinstance(steps, list) and steps:
                    points.append(f"执行计划: 共{len(steps)}个步骤")
                    for i, step in enumerate(steps[:2], 1):  # 只显示前2步
                        if isinstance(step, dict):
                            step_desc = step.get("reasoning", step.get("tool_name", f"步骤{i}"))
                            points.append(f"执行步骤{i}: {step_desc}")
        
        except Exception as e:
            logger.warning(f"提取理解要点失败: {str(e)}")
            points = ["Agent分析完成，详细信息提取失败"]
        
        return points if points else ["Agent分析完成"]

    def _extract_understanding_points_from_intent(self, intent_analysis: Dict[str, Any]) -> List[str]:
        """从意图识别结果中提取理解要点"""
        points = []

        try:
            # 1. 添加意图理解说明
            intent_desc = intent_analysis.get("intent_description", "")
            if intent_desc and intent_desc != "用一两句话清晰、具体地描述你理解的用户分析意图。":
                points.append(f"意图理解: {intent_desc}")

            # 2. 处理执行步骤 (execution_steps)
            execution_steps = intent_analysis.get("execution_steps", [])
            if execution_steps:
                points.append(f"分析步骤: 共{len(execution_steps)}个步骤")

                for i, step in enumerate(execution_steps, 1):
                    if isinstance(step, dict):
                        step_intent = step.get("step_intent", "")
                        recommended_tool = step.get("recommended_tool", "")
                        tool_usage_reason = step.get("tool_usage_reason", "")
                        expected_outcome = step.get("expected_outcome", "")

                        # 构建步骤描述
                        if step_intent and step_intent != "此步骤的核心分析目的。":
                            points.append(f"步骤{i}: {step_intent}")

                            # 添加工具推荐信息
                            if recommended_tool and recommended_tool != "最适合此步骤的工具名称。":
                                tool_info = f"推荐工具: {recommended_tool}"
                                if tool_usage_reason and tool_usage_reason != "简述为什么选择该工具以实现步骤意图。":
                                    tool_info += f" - {tool_usage_reason}"
                                points.append(f"  {tool_info}")

                            # 添加预期结果
                            if expected_outcome and expected_outcome != "描述执行此步骤后的预期结果。":
                                points.append(f"  预期结果: {expected_outcome}")

            # 3. 添加分析思路（如果存在）
            reasoning = intent_analysis.get("reasoning", "")
            if reasoning:
                points.append(f"分析思路: {reasoning}")

            # 4. 添加置信度信息（如果存在）
            confidence = intent_analysis.get("confidence", 0)
            if confidence > 0:
                confidence_text = f"{confidence*100:.0f}%" if confidence <= 1 else f"{confidence:.0f}%"
                points.append(f"置信度: {confidence_text}")

        except Exception as e:
            logger.error(f"提取意图理解要点失败: {str(e)}")
            points.append("基于意图识别的分析")

        return points if points else ["意图识别完成"]

    def _build_enhanced_context(self, schema_info: str, session_data: Dict[str, Any], project_id: str) -> str:
        """构建增强的业务上下文信息

        Args:
            schema_info: 基础schema信息
            session_data: 会话数据，包含业务理解和用户回答
            project_id: 项目ID

        Returns:
            str: 增强的上下文信息
        """
        context_parts = [schema_info]

        if session_data:
            # 添加业务理解分析结果
            readiness_evaluation = session_data.get("readiness_evaluation", {})
            if readiness_evaluation:
                context_parts.append("\n## 业务理解分析结果")

                # 项目理解
                project_understanding = readiness_evaluation.get("project_understanding", {})
                if project_understanding:
                    context_parts.append("### 项目业务理解:")
                    context_parts.append(f"- 业务领域: {project_understanding.get('business_domain', '未知')}")
                    context_parts.append(f"- 业务类型: {project_understanding.get('business_type', '未知')}")

                    key_entities = project_understanding.get('key_entities', [])
                    if key_entities:
                        context_parts.append(f"- 关键业务实体: {', '.join(key_entities)}")

                    core_metrics = project_understanding.get('core_metrics', [])
                    if core_metrics:
                        context_parts.append(f"- 核心业务指标: {', '.join(core_metrics)}")

                    data_relationships = project_understanding.get('data_relationships', '')
                    if data_relationships:
                        context_parts.append(f"- 数据关系描述: {data_relationships}")

                # 分析能力边界
                can_answer_examples = readiness_evaluation.get("can_answer_examples", [])
                if can_answer_examples:
                    context_parts.append("### 可以回答的问题举例:")
                    for example in can_answer_examples:
                        context_parts.append(f"- {example}")

                boundaries = readiness_evaluation.get("boundaries", [])
                if boundaries:
                    context_parts.append("### 分析边界和限制:")
                    for boundary in boundaries:
                        context_parts.append(f"- {boundary}")

            # 添加用户补充回答
            user_answers = session_data.get("user_answers", {})
            if user_answers:
                context_parts.append("\n## 用户补充信息")
                for question, answer in user_answers.items():
                    if answer and answer.strip():
                        context_parts.append(f"- {question}: {answer}")

        return "\n".join(context_parts)

    def _extract_understanding_points_new(self, intent_analysis: Dict, planning_result: Dict) -> List[str]:
        """从新版Agent结果中提取理解要点

        Args:
            intent_analysis: 新版意图分析结果
            planning_result: 规划结果

        Returns:
            List[str]: 理解要点列表
        """
        points = []

        try:
            # 检查是否为数据无关的情况
            if intent_analysis.get("action_type") == "data_irrelevant":
                points.append("问题与数据分析无关")
                return points

            # 从新版意图分析中提取
            if isinstance(intent_analysis, dict) and intent_analysis.get("action_type") == "intent_analysis":
                # 提取意图描述
                intent_desc = intent_analysis.get("intent_description", "")
                if intent_desc:
                    points.append(f"意图理解: {intent_desc}")

                # 提取执行步骤
                execution_steps = intent_analysis.get("execution_steps", [])
                if isinstance(execution_steps, list) and execution_steps:
                    points.append(f"分析步骤: 共{len(execution_steps)}个步骤")
                    for i, step in enumerate(execution_steps, 1):  # 只显示前3步
                        if isinstance(step, dict):
                            step_intent = step.get("step_intent", step.get("recommended_tool", f"步骤{i}"))
                            points.append(f"步骤{i}: {step_intent}")

                # 检查是否需要澄清
                needs_clarification = intent_analysis.get("needs_clarification", False)
                if needs_clarification:
                    clarification_questions = intent_analysis.get("clarification_questions", [])
                    points.append(f"需要澄清: {len(clarification_questions)}个问题")

            # 从规划结果中提取
            if isinstance(planning_result, dict):
                if planning_result.get("reasoning"):
                    reasoning = planning_result["reasoning"]
                    points.append(f"分析思路: {reasoning}")

                # 从执行步骤中提取
                steps = planning_result.get("steps", [])
                if isinstance(steps, list) and steps:
                    points.append(f"执行计划: 共{len(steps)}个步骤")
                    for i, step in enumerate(steps[:2], 1):  # 只显示前2步
                        if isinstance(step, dict):
                            step_desc = step.get("reasoning", step.get("tool_name", f"步骤{i}"))
                            if len(step_desc) > 60:
                                step_desc = step_desc
                            points.append(f"执行步骤{i}: {step_desc}")

        except Exception as e:
            logger.warning(f"提取新版理解要点失败: {str(e)}")
            points = ["Agent分析完成，详细信息提取失败"]

        return points if points else ["Agent分析完成"]

    async def batch_analyze_scenarios(self, predicted_questions: List[str], project_id: str, user_language: str = 'zh-CN', session_data: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """批量分析场景

        Args:
            predicted_questions: 预测问题列表
            project_id: 项目ID
            user_language: 用户语言偏好，默认为中文
            session_data: 会话数据，包含业务理解结果和用户回答

        Returns:
            List[Dict[str, Any]]: 分析结果列表
        """
        logger.info(f"开始批量Agent场景分析，共{len(predicted_questions)}个场景")
        
        # 并行处理，但限制并发数
        tasks = [
            self.analyze_scenario_with_agent(question, project_id, user_language, session_data)
            for question in predicted_questions
        ]
        
        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理异常结果
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"场景{i+1}分析异常: {str(result)}")
                    processed_results.append({
                        "success": False,
                        "error": str(result),
                        "understanding_points": ["分析过程中发生异常"]
                    })
                else:
                    processed_results.append(result)
            
            logger.info(f"批量Agent场景分析完成，成功: {sum(1 for r in processed_results if r.get('success', False))}/{len(processed_results)}")
            return processed_results
            
        except Exception as e:
            logger.error(f"批量Agent场景分析失败: {str(e)}")
            return [
                {
                    "success": False,
                    "error": str(e),
                    "understanding_points": ["批量分析失败"]
                }
                for _ in predicted_questions
            ] 