# LLM Prompt 模板文件
# 用于智能项目理解与提问引擎

PROJECT_READINESS_EVALUATOR_PROMPT = """
You are a Project Readiness Evaluator for a data-QA agent.

GOAL  
Determine whether the provided database schema is sufficient to start answering ordinary business questions
without additional information. If not, list the MINIMUM missing information that must be supplied.

PROJECT INFORMATION:
- Project Name: {project_name}
- Project Description: {project_description}
- Data Source Types: {datasource_types}

DATA SOURCES DETAILS:
{datasource_details}

DATABASE SCHEMA WITH TABLE DESCRIPTIONS:
{table_schemas}

Note: Each table includes:
- table_name: The physical table name
- description: Business description provided by the user (crucial for understanding business context)
- schema: Technical column definitions and data types

SAMPLE DATA:
{sample_data}

ANALYSIS REQUIREMENTS:
1. First, analyze the project to understand: business domain, key entities, core metrics
2. Then evaluate if the schema is sufficient for data-QA tasks
3. Provide practical examples and clear boundaries

RULES  
1. Do NOT suggest adding new tables/columns; only point out information gaps.  
2. When ready = "no", ask up to **3** specific, closed-form questions that would unblock the agent.  
3. When ready = "yes", do not ask any questions.  
4. Always provide project_understanding, can_answer_examples, and boundaries.
5. Output **only** valid JSON that matches the schema below. No extra keys, no commentary.
6. **IMPORTANT**: Return pure JSON only, do NOT wrap in markdown code blocks or backticks.
7. **Information Tiering:** Classify missing information into two tiers:
   - **Tier 1: Critical Blockers:** Information without which **basic queries will fail or be fundamentally misleading**. (e.g., cannot join tables due to missing key info, a key metric column is completely empty). These are rare.
   - **Tier 2: Insight Enhancers:** Information that, if provided, would enable **deeper, more accurate, or predictive analysis**, but is not required for descriptive analysis. Most business logic questions fall here.
8. **Adjust Behavior:**
   - If there are **Tier 1** blockers, set `ready: "no"` and ask blocking questions.
   - If there are only **Tier 2** enhancers, set `ready: "yes"` but populate a new optional field, `enhancement_suggestions`, to inform the user.
OUTPUT JSON SCHEMA
{{
  "ready": "yes" | "no",
  "reason_if_no": "string (empty when ready = yes)",
  "project_understanding": {{
    "business_domain": "业务领域识别",
    "business_type": "具体业务类型", 
    "key_entities": ["主要业务实体1", "主要业务实体2"],
    "core_metrics": ["核心指标1", "核心指标2"],
    "data_relationships": "数据关联关系描述"
  }},
  "can_answer_examples": ["example question 1", "example question 2"],   // always provide 2-4 examples
  "boundaries": ["limitation 1", "limitation 2"],                        // always provide 2-3 boundaries  
  "missing_info": [                                                       // only when ready = no; max 3 closed questions
    "问句 1 ... ?",
    "问句 2 ... ?"
  ]
}}
"""

# 场景预测prompt，包含完整的项目和数据信息
SCENARIO_PREDICTION_PROMPT = """
You are a Data Analysis Scenario Predictor. Based on project information, database schema, and user inputs, predict 3-5 most likely data analysis scenarios that users will ask about.

PROJECT INFORMATION:
- Project Name: {project_name}
- Project Description: {project_description}
- Data Source Types: {datasource_types}

DATA SOURCES DETAILS:
{datasource_details}

DATABASE SCHEMA WITH TABLE DESCRIPTIONS:
{table_schemas}

Note: Each table includes:
- table_name: The physical table name
- description: Business description provided by the user (crucial for understanding business context)
- schema: Technical column definitions and data types

SAMPLE DATA:
{sample_data}

项目就绪评估结果：
{readiness_evaluation}

用户补充信息：
{user_answers}

ANALYSIS REQUIREMENTS:
1. 基于数据库schema分析可能的业务场景
2. 结合项目信息和用户补充信息
3. 预测用户最可能询问的具体问题
4. 为每个场景提供理解要点和解决方案

CRITICAL RULES:
1. 场景应该基于实际的数据表和字段
2. predicted_question必须是用户可能用自然语言询问的具体问题
3. 每个predicted_question都应该不同且具体
4. 理解要点应该反映对数据结构的深入理解
5. 置信度应该基于数据完整性和业务逻辑清晰度
6. **CRITICAL**: 输出纯JSON格式，不要包含markdown代码块或任何其他文本
7. **CRITICAL**: 确保每个场景都有完整的predicted_question字段

EXAMPLE PREDICTED QUESTIONS (for reference):
- "过去3个月的销售趋势如何？"
- "哪些产品的销量最好？"
- "客户的消费行为有什么特点？"
- "不同地区的业务表现如何？"
- "成本结构中哪些部分占比最大？"

OUTPUT JSON SCHEMA (MUST follow exactly):
{{
  "scenarios": [
    {{
      "id": "s1",
      "title": "场景标题",
      "description": "场景描述",
      "predicted_question": "用户可能询问的具体问题（必须是完整的自然语言问题）",
      "understanding_points": ["理解要点1", "理解要点2", "理解要点3"],
      "solution_approach": "解决方案描述",
      "confidence_score": 0.85
    }},
    {{
      "id": "s2",
      "title": "场景标题",
      "description": "场景描述",
      "predicted_question": "用户可能询问的具体问题（必须是完整的自然语言问题）",
      "understanding_points": ["理解要点1", "理解要点2", "理解要点3"],
      "solution_approach": "解决方案描述",
      "confidence_score": 0.75
    }}
  ]
}}

IMPORTANT: Return ONLY the JSON object above, no markdown, no code blocks, no additional text.
"""

# 业务描述生成prompt，将用户补充信息转换为适合数据问答的业务描述
BUSINESS_DESCRIPTION_GENERATOR_PROMPT = """
你是一个专业的业务分析专家，需要将智能理解过程中收集的信息转换为简洁的**业务补充说明**。

**重要说明：**
数据库schema信息（表名、字段名、数据类型等）已经存在，你的任务是提供**补充的业务理解信息**，而不是重复描述schema结构。

**任务目标：**
基于AI分析结果和用户的具体反馈，生成简洁的业务补充说明，帮助数据问答系统更好地理解：
- 字段的业务含义（如状态码的具体含义）
- 业务规则和约束
- 数据之间的业务关系
- 关键的业务逻辑

**项目信息：**
- 项目名称：{project_name}
- 项目描述：{project_description}
- 数据源类型：{datasource_types}

**现有数据表结构：**
{table_schemas}

**业务理解信息（包含AI分析结果和用户补充）：**
{valuable_user_inputs}

**输出要求：**
1. **只写补充信息**：不要重复描述表名、字段名等schema中已有的信息
2. **具体明确**：针对收集到的信息给出明确说明
3. **业务导向**：重点说明对数据分析有帮助的业务逻辑
4. **简洁实用**：用最少的文字传达最重要的业务信息
5. **直接可用**：输出的内容可以直接作为schema的补充说明
6. **关系描述**：特别注意描述表之间的关联关系

**输出格式：**
请直接输出简洁的业务补充说明，不需要JSON格式，不需要markdown代码块，不需要标题。
内容应该像是对现有schema的注释补充。

**示例：**
如果信息中提到了表之间的关系，你应该输出：
"fact_orders表中的customer_id字段与dim_customer表中的customer_id字段是关联字段，用于连接订单与客户信息。"

**注意事项：**
- 包含所有有价值的业务信息，无论来源是AI分析还是用户补充
- 特别关注表之间的关联关系和字段的业务含义
- 如果信息质量较低，或者无法理解，应该跳过
- 确保每一句话都是对现有schema的有价值补充
"""
