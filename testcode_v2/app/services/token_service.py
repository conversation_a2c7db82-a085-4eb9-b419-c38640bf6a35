import json
from typing import Any, Dict, Optional
from datetime import datetime, timedelta

from app.db.redis import redis_client
from app.core.config import settings
from app.core.logger import log
from app.utils.time_utils import get_shanghai_time


class TokenService:
    """
    Token管理服务
    基于Redis实现token存储和单点登录功能
    """
    
    # Redis键前缀
    USER_TOKEN_PREFIX = "user_token:"  # user_id -> token
    TOKEN_INFO_PREFIX = "token_info:"  # token -> user_info
    
    @classmethod
    def _get_user_token_key(cls, user_id: int) -> str:
        """获取用户token的Redis键"""
        return f"{cls.USER_TOKEN_PREFIX}{user_id}"
    
    @classmethod
    def _get_token_info_key(cls, token: str) -> str:
        """获取token信息的Redis键"""
        return f"{cls.TOKEN_INFO_PREFIX}{token}"
    
    @classmethod
    def store_token(
        cls, 
        user_id: int, 
        token: str, 
        user_info: Dict[str, Any],
        expires_minutes: Optional[int] = None
    ) -> bool:
        """
        存储token到Redis
        
        Args:
            user_id: 用户ID
            token: JWT token
            user_info: 用户信息（用于token验证时快速获取）
            expires_minutes: 过期时间（分钟），默认使用配置值
            
        Returns:
            bool: 是否存储成功
        """
        try:
            expires_minutes = expires_minutes or settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES
            expires_seconds = expires_minutes * 60
            
            # 1. 检查用户是否已有活跃token（单点登录：挤下线）
            old_token = cls.get_user_token(user_id)
            if old_token:
                log.info(f"用户 {user_id} 已有活跃token，将被挤下线")
                cls.revoke_token(old_token)
            
            # 2. 存储新的用户token映射
            user_token_key = cls._get_user_token_key(user_id)
            redis_client.set_value(user_token_key, token, ex=expires_seconds)
            
            # 3. 存储token信息
            token_info_key = cls._get_token_info_key(token)
            token_data = {
                "user_id": user_id,
                "user_info": user_info,
                "created_at": get_shanghai_time().isoformat(),
                "expires_at": (get_shanghai_time() + timedelta(minutes=expires_minutes)).isoformat()
            }
            redis_client.set_value(
                token_info_key, 
                json.dumps(token_data), 
                ex=expires_seconds
            )
            
            log.info(f"Token存储成功，用户ID: {user_id}, Token过期时间: {expires_minutes}分钟")
            return True
            
        except Exception as e:
            log.error(f"存储token失败: {str(e)}")
            return False
    
    @classmethod
    def get_user_token(cls, user_id: int) -> Optional[str]:
        """
        获取用户当前的活跃token
        
        Args:
            user_id: 用户ID
            
        Returns:
            str: token字符串，如果没有则返回None
        """
        try:
            user_token_key = cls._get_user_token_key(user_id)
            return redis_client.get_value(user_token_key)
        except Exception as e:
            log.error(f"获取用户token失败: {str(e)}")
            return None
    
    @classmethod
    def get_token_info(cls, token: str) -> Optional[Dict[str, Any]]:
        """
        获取token信息
        
        Args:
            token: JWT token
            
        Returns:
            dict: token信息，如果token无效则返回None
        """
        try:
            token_info_key = cls._get_token_info_key(token)
            token_data = redis_client.get_value(token_info_key)
            
            if not token_data:
                return None
                
            return json.loads(token_data)
            
        except Exception as e:
            log.error(f"获取token信息失败: {str(e)}")
            return None
    
    @classmethod
    def is_token_valid(cls, token: str) -> bool:
        """
        验证token是否有效（存在于Redis中）
        
        Args:
            token: JWT token
            
        Returns:
            bool: token是否有效
        """
        try:
            token_info_key = cls._get_token_info_key(token)
            return redis_client.exists(token_info_key)
        except Exception as e:
            log.error(f"验证token失败: {str(e)}")
            return False
    
    @classmethod
    def revoke_token(cls, token: str) -> bool:
        """
        撤销token（删除）
        
        Args:
            token: JWT token
            
        Returns:
            bool: 是否撤销成功
        """
        try:
            # 1. 获取token信息以获得user_id
            token_info = cls.get_token_info(token)
            if not token_info:
                log.warning(f"尝试撤销不存在的token")
                return True  # token已经不存在，视为撤销成功
            
            user_id = token_info.get("user_id")
            
            # 2. 删除token信息
            token_info_key = cls._get_token_info_key(token)
            redis_client.delete(token_info_key)
            
            # 3. 删除用户token映射（如果当前token是该用户的活跃token）
            if user_id:
                current_token = cls.get_user_token(user_id)
                if current_token == token:
                    user_token_key = cls._get_user_token_key(user_id)
                    redis_client.delete(user_token_key)
            
            log.info(f"Token撤销成功，用户ID: {user_id}")
            return True
            
        except Exception as e:
            log.error(f"撤销token失败: {str(e)}")
            return False
    

    
    @classmethod
    def refresh_token_expiry(cls, token: str, expires_minutes: Optional[int] = None) -> bool:
        """
        刷新token过期时间
        
        Args:
            token: JWT token
            expires_minutes: 新的过期时间（分钟）
            
        Returns:
            bool: 是否刷新成功
        """
        try:
            expires_minutes = expires_minutes or settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES
            expires_seconds = expires_minutes * 60
            
            # 获取token信息
            token_info = cls.get_token_info(token)
            if not token_info:
                log.warning(f"尝试刷新不存在的token")
                return False
            
            user_id = token_info.get("user_id")
            
            # 刷新token信息的过期时间
            token_info_key = cls._get_token_info_key(token)
            redis_client.expire(token_info_key, expires_seconds)
            
            # 刷新用户token映射的过期时间
            user_token_key = cls._get_user_token_key(user_id)
            redis_client.expire(user_token_key, expires_seconds)
            
            log.info(f"Token过期时间刷新成功，用户ID: {user_id}")
            return True
            
        except Exception as e:
            log.error(f"刷新token过期时间失败: {str(e)}")
            return False
    
 