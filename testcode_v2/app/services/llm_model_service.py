import logging
import json
from typing import List, Optional
from sqlalchemy.orm import Session

from app.models.llm_model import LLMModel
from app.schemas.llm_model import LLMModelCreate, LLMModelUpdate, LLMModelTestResult, LLMModelTestConfig
from app.db.redis import RedisClient


class LLMModelService:
    """LLM模型管理服务"""
    
    # Redis缓存key常量
    ACTIVE_MODEL_CACHE_KEY = "llm:active_model"
    
    def __init__(self, db: Session):
        self.db = db
        self.logger = logging.getLogger("app.services.llm_model")
        
        # 初始化时检查并设置Redis缓存
        self._initialize_cache()
    
    # Redis缓存相关方法
    def _cache_active_model(self, model: LLMModel) -> bool:
        """
        将活跃模型缓存到Redis
        
        Args:
            model: 要缓存的模型实例
            
        Returns:
            bool: 缓存是否成功
        """
        try:
            model_data = {
                "id": model.id,
                "name": model.name,
                "description": model.description,
                "model_name": model.model_name,
                "api_key": model.api_key,
                "base_url": model.base_url,
                "config": model.config,
                "is_active": model.is_active,
                "created_at": model.created_at.isoformat() if model.created_at else None,
                "updated_at": model.updated_at.isoformat() if model.updated_at else None,
            }
            
            success = RedisClient.set_value(
                self.ACTIVE_MODEL_CACHE_KEY, 
                json.dumps(model_data),
                ex=3600  # 缓存1小时
            )
            
            if success:
                self.logger.info(f"成功缓存活跃模型到Redis: {model.name} (ID: {model.id})")
            else:
                self.logger.error(f"缓存活跃模型到Redis失败: {model.name} (ID: {model.id})")
                
            return success
            
        except Exception as e:
            self.logger.error(f"缓存活跃模型到Redis异常: {str(e)}")
            return False
    
    def _get_cached_active_model(self) -> Optional[dict]:
        """
        从Redis获取缓存的活跃模型
        
        Returns:
            Optional[dict]: 模型数据字典，如果不存在则返回None
        """
        try:
            cached_data = RedisClient.get_value(self.ACTIVE_MODEL_CACHE_KEY)
            if cached_data:
                model_data = json.loads(cached_data)
                self.logger.debug("从Redis获取到缓存的活跃模型")
                return model_data
            else:
                self.logger.debug("Redis中没有缓存的活跃模型")
                return None
                
        except Exception as e:
            self.logger.error(f"从Redis获取活跃模型缓存异常: {str(e)}")
            return None
    
    def _clear_active_model_cache(self) -> bool:
        """
        清除Redis中的活跃模型缓存
        
        Returns:
            bool: 清除是否成功
        """
        try:
            success = RedisClient.delete(self.ACTIVE_MODEL_CACHE_KEY)
            if success:
                self.logger.info("成功清除Redis中的活跃模型缓存")
            else:
                self.logger.debug("Redis中没有活跃模型缓存需要清除")
            return success
            
        except Exception as e:
            self.logger.error(f"清除Redis活跃模型缓存异常: {str(e)}")
            return False
    
    def _refresh_active_model_cache(self) -> bool:
        """
        刷新Redis中的活跃模型缓存
        从数据库获取最新的活跃模型并更新到Redis
        
        Returns:
            bool: 刷新是否成功
        """
        try:
            # 从数据库获取第一个活跃模型
            active_model = self.db.query(LLMModel).filter(LLMModel.is_active == True).first()
            
            if active_model:
                return self._cache_active_model(active_model)
            else:
                # 如果没有活跃模型，清除缓存
                self._clear_active_model_cache()
                self.logger.warning("没有找到活跃的模型，已清除Redis缓存")
                return True
                
        except Exception as e:
            self.logger.error(f"刷新活跃模型缓存异常: {str(e)}")
            return False
    
    def _initialize_cache(self) -> None:
        """
        初始化Redis缓存
        在服务启动时检查Redis中是否有活跃模型缓存，如果没有则从数据库加载
        """
        try:
            # 检查Redis中是否已有缓存
            cached_model = self._get_cached_active_model()
            if cached_model:
                self.logger.info("Redis中已存在活跃模型缓存")
                return
            
            # 如果没有缓存，从数据库加载并缓存
            self.logger.info("初始化活跃模型缓存...")
            success = self._refresh_active_model_cache()
            if success:
                self.logger.info("活跃模型缓存初始化完成")
            else:
                self.logger.warning("活跃模型缓存初始化失败")
                
        except Exception as e:
            self.logger.error(f"初始化活跃模型缓存异常: {str(e)}")
    
    def get_models(self, skip: int = 0, limit: int = 100, active_only: bool = False) -> List[LLMModel]:
        """获取模型列表"""
        query = self.db.query(LLMModel)
        if active_only:
            query = query.filter(LLMModel.is_active == True)
        return query.offset(skip).limit(limit).all()
    
    def get_model(self, model_id: str) -> Optional[LLMModel]:
        """获取单个模型"""
        return self.db.query(LLMModel).filter(LLMModel.id == model_id).first()
    
    def create_model(self, model_data: LLMModelCreate) -> LLMModel:
        """创建模型"""
        db_model = LLMModel(**model_data.model_dump())
        self.db.add(db_model)
        self.db.commit()
        self.db.refresh(db_model)

        self.logger.info(f"创建LLM模型: {db_model.name} (ID: {db_model.id})")
        
        # 如果新创建的模型是活跃的，需要更新Redis缓存
        if db_model.is_active:
            # 先禁用其他活跃模型，确保只有一个活跃模型
            self.db.query(LLMModel).filter(
                LLMModel.id != db_model.id, 
                LLMModel.is_active == True
            ).update({"is_active": False})
            self.db.commit()
            
            # 缓存新的活跃模型
            self._cache_active_model(db_model)
            self.logger.info(f"新创建的活跃模型已缓存: {db_model.name}")
        
        return db_model

    def update_model(self, model_id: str, model_data: LLMModelUpdate) -> Optional[LLMModel]:
        """更新模型"""
        db_model = self.get_model(model_id)
        if not db_model:
            return None

        # 记录更新前的状态
        was_active_before = db_model.is_active
        
        update_data = model_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_model, field, value)

        # 如果模型被设置为活跃，需要禁用其他活跃模型
        if db_model.is_active and not was_active_before:
            self.db.query(LLMModel).filter(
                LLMModel.id != db_model.id, 
                LLMModel.is_active == True
            ).update({"is_active": False})

        self.db.commit()
        self.db.refresh(db_model)

        self.logger.info(f"更新LLM模型: {db_model.name} (ID: {db_model.id})")
        
        # 同步更新Redis缓存
        if db_model.is_active:
            # 如果模型现在是活跃的，缓存它
            self._cache_active_model(db_model)
            self.logger.info(f"已更新活跃模型缓存: {db_model.name}")
        elif was_active_before and not db_model.is_active:
            # 如果模型从活跃变为非活跃，需要重新刷新缓存
            self._refresh_active_model_cache()
            self.logger.info(f"模型 {db_model.name} 已禁用，已刷新活跃模型缓存")
        
        return db_model
    
    def delete_model(self, model_id: str) -> bool:
        """删除模型"""
        db_model = self.get_model(model_id)
        if not db_model:
            return False
        
        # 记录删除前的状态
        was_active = db_model.is_active
        model_name = db_model.name
        
        self.db.delete(db_model)
        self.db.commit()
        
        self.logger.info(f"删除LLM模型: {model_name} (ID: {model_id})")
        
        # 如果删除的是活跃模型，需要重新刷新缓存
        if was_active:
            self._refresh_active_model_cache()
            self.logger.info(f"已删除活跃模型 {model_name}，已刷新活跃模型缓存")
        
        return True
    
    def get_active_model_for_service(self) -> Optional[LLMModel]:
        """
        获取用于服务的活跃模型
        优先从Redis缓存获取，如果缓存不存在则从数据库获取并缓存
        """
        # 首先尝试从Redis缓存获取
        cached_model_data = self._get_cached_active_model()
        if cached_model_data:
            # 构造模型对象返回
            try:
                # 创建一个临时的模型对象用于返回
                # 这里不需要完整的ORM对象，只需要包含必要的字段
                model = LLMModel()
                model.id = cached_model_data["id"]
                model.name = cached_model_data["name"]
                model.description = cached_model_data.get("description")
                model.model_name = cached_model_data["model_name"]
                model.api_key = cached_model_data["api_key"]
                model.base_url = cached_model_data.get("base_url")
                model.config = cached_model_data.get("config")
                model.is_active = cached_model_data.get("is_active", True)
                
                self.logger.debug("从Redis缓存返回活跃模型")
                return model
                
            except Exception as e:
                self.logger.error(f"从Redis缓存构造模型对象失败: {str(e)}")
                # 如果缓存数据有问题，清除缓存并从数据库获取
                self._clear_active_model_cache()
        
        # 如果缓存不存在或有问题，从数据库获取
        self.logger.debug("从数据库获取活跃模型")
        active_models = self.get_models(active_only=True, limit=1)
        active_model = active_models[0] if active_models else None
        
        # 如果找到活跃模型，缓存到Redis
        if active_model:
            self._cache_active_model(active_model)
            self.logger.debug("已将数据库中的活跃模型缓存到Redis")
        else:
            self.logger.warning("没有找到活跃的LLM模型")
        
        return active_model

    async def test_model_with_config(self, model_config: LLMModelTestConfig, test_message: str = None) -> LLMModelTestResult:
        """使用配置直接测试模型，无需保存到数据库"""
        try:
            # 真实调用LLM模型进行测试
            import time
            import httpx
            import json

            start_time = time.time()

            # 检查必要的配置项
            if not model_config.api_key:
                return LLMModelTestResult(
                    success=False,
                    error="API密钥未配置"
                )

            if not model_config.model_name:
                return LLMModelTestResult(
                    success=False,
                    error="模型名称未配置"
                )

            # 固定使用简单的测试消息
            fixed_test_message = "hello"

            # 准备请求数据
            headers = {
                "Authorization": f"Bearer {model_config.api_key}",
                "Content-Type": "application/json"
            }

            data = {
                "model": model_config.model_name,
                "messages": [
                    {"role": "user", "content": fixed_test_message}
                ],
                "max_tokens": 10,  # 限制为10个token
                "temperature": 0.3
            }

            # 构建完整的API URL
            base_url = model_config.base_url or "https://api.openai.com/v1"
            if not base_url.endswith('/'):
                base_url += '/'
            api_url = f"{base_url}chat/completions"

            # 发送HTTP请求
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    api_url,
                    headers=headers,
                    json=data
                )

                latency = time.time() - start_time

                if response.status_code == 200:
                    result = response.json()
                    if "choices" in result and len(result["choices"]) > 0:
                        content = result["choices"][0]["message"]["content"]
                        return LLMModelTestResult(
                            success=True,
                            response=content.strip(),
                            latency=latency
                        )
                    else:
                        return LLMModelTestResult(
                            success=False,
                            error="模型响应格式异常",
                            latency=latency
                        )
                else:
                    # 返回详细的API错误响应
                    try:
                        error_detail = response.json()
                        if "error" in error_detail:
                            api_error = error_detail["error"]
                            error_msg = f"API错误: {api_error.get('message', '未知错误')}"
                            if "code" in api_error:
                                error_msg += f" (错误代码: {api_error['code']})"
                        else:
                            error_msg = f"HTTP {response.status_code}: {response.text}"
                    except:
                        error_msg = f"HTTP {response.status_code}: {response.text}"

                    return LLMModelTestResult(
                        success=False,
                        error=error_msg,
                        latency=latency
                    )
                
        except httpx.TimeoutException:
            latency = round(time.time() - start_time, 3)
            error_msg = "请求超时，请检查网络连接或API服务状态"
            self.logger.error(f"模型配置测试超时: {error_msg}")
            return LLMModelTestResult(
                success=False,
                error=error_msg,
                latency=latency
            )
        except Exception as e:
            latency = round(time.time() - start_time, 3)
            error_msg = f"测试过程中发生错误: {str(e)}"
            self.logger.error(f"模型配置测试异常: {error_msg}")
            return LLMModelTestResult(
                success=False,
                error=error_msg,
                latency=latency
            )
