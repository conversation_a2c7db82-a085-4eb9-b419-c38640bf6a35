import json
import time
import logging
import re
from typing import Dict, Any, Optional, List
from datetime import datetime
from sqlalchemy.orm import Session

from app.services.llm_factory import get_llm_service
from app.services.datasource import DataSourceService
from app.models.project import Project
from app.models.data_source import DataSource
from app.models.selected_table import SelectedTable
from app.db.redis import RedisClient

logger = logging.getLogger(__name__)

# 全局共享内存存储，确保跨请求共享
_GLOBAL_SESSION_STORE: Dict[str, Dict[str, Any]] = {}

class ProjectIntelligenceService:
    """基于Redis+内存降级的项目智能理解服务"""
    
    def __init__(self, db: Session):
        self.db = db
        self.llm_service = get_llm_service(db)
        self._current_session_id = None  # 临时存储当前会话ID
        
        # 使用项目统一的Redis客户端
        self.use_redis = False
        try:
            # 测试Redis连接
            test_key = "intelligence_health_check"
            RedisClient.set_value(test_key, "ok", ex=10)
            if RedisClient.get_value(test_key) == "ok":
                RedisClient.delete(test_key)
                self.use_redis = True
                logger.info("智能理解服务使用Redis存储临时数据")
            else:
                raise Exception("Redis连接测试失败")
        except Exception as e:
            logger.warning(f"Redis连接失败，降级到内存存储: {str(e)}")
            self.use_redis = False
            logger.info("智能理解服务使用内存存储临时数据（降级模式）")
    
    def _parse_json_with_fallback(self, result_text: str, context_name: str = "JSON") -> Optional[Dict[str, Any]]:
        """
        统一的JSON解析工具函数，支持多种格式的JSON解析
        
        Args:
            result_text: 待解析的JSON字符串
            context_name: 上下文名称，用于日志记录
            
        Returns:
            解析后的字典对象，如果解析失败返回None
        """
        if not result_text or not result_text.strip():
            logger.warning(f"{context_name}内容为空")
            return None
            
        try:
            # 1. 先尝试直接解析
            result = json.loads(result_text)
            logger.info(f"✅ {context_name}直接解析成功")
            return result
            
        except json.JSONDecodeError as e:
            logger.warning(f"{context_name}直接解析失败: {str(e)}")
            logger.debug(f"解析失败的内容: {result_text[:500]}...")
            
            # 2. 尝试提取markdown代码块中的JSON
            import re
            json_match = re.search(r'```(?:json)?\s*\n(.*?)\n```', result_text, re.DOTALL)
            if json_match:
                json_content = json_match.group(1).strip()
                logger.info(f"找到{context_name}的markdown代码块，尝试解析...")
                logger.debug(f"提取的JSON内容: {json_content[:300]}...")
                
                try:
                    result = json.loads(json_content)
                    logger.info(f"✅ {context_name}的markdown代码块解析成功")
                    return result
                    
                except json.JSONDecodeError as e2:
                    logger.error(f"{context_name}的markdown代码块解析也失败: {str(e2)}")
                    logger.error(f"提取的JSON内容: {json_content}")
            else:
                logger.error(f"未找到{context_name}的markdown代码块")
            
            # 3. 尝试修复常见的JSON格式问题
            try:
                fixed_content = self._fix_json_format_issues(result_text)
                if fixed_content != result_text:
                    logger.info(f"尝试修复{context_name}的格式问题...")
                    result = json.loads(fixed_content)
                    logger.info(f"✅ {context_name}修复后解析成功")
                    return result
            except json.JSONDecodeError:
                logger.error(f"{context_name}修复后仍然无法解析")
            
            # 4. 如果所有方法都失败，记录详细错误信息
            logger.error(f"所有{context_name}解析方法都失败")
            logger.error(f"完整的{context_name}内容: {result_text}")
            return None
    
    def _fix_json_format_issues(self, content: str) -> str:
        """
        修复常见的JSON格式问题
        
        Args:
            content: 原始JSON字符串
            
        Returns:
            修复后的JSON字符串
        """
        try:
            # 1. 首先尝试直接解析，如果成功就直接返回
            json.loads(content)
            return content
        except json.JSONDecodeError:
            pass
        
        # 2. 处理常见的转义问题
        # 移除多余的反斜杠
        content = re.sub(r'\\{2,}', '\\', content)
        
        # 3. 处理换行符问题
        content = re.sub(r'(?<!\\)\\n', '\\n', content)
        content = re.sub(r'\\\\n', '\\n', content)
        
        # 4. 处理特殊字符
        content = content.replace('\t', '\\t')
        
        # 5. 移除控制字符
        content = re.sub(r'[\x00-\x1F\x7F-\x9F]', '', content)
        
        return content
    
    async def start_intelligence_session(self, project_id: str, user_id: int) -> str:
        """启动智能理解会话"""
        session_id = f"intelligence_{project_id}_{user_id}_{int(time.time())}"
        
        try:
            # 获取项目数据
            project_data = await self._get_project_data(project_id)
            
            # 存储会话数据
            session_data = {
                "session_id": session_id,
                "project_id": project_id,
                "user_id": user_id,
                "status": "started",
                "project_data": project_data,
                "created_at": datetime.now().isoformat()
            }
            
            self._store_session_data(session_id, session_data)
            logger.info(f"启动智能理解会话: {session_id}")
            
            return session_id
        except Exception as e:
            logger.error(f"启动智能理解会话失败: {str(e)}")
            raise
    
    async def analyze_business_understanding(self, session_id: str) -> Dict[str, Any]:
        """分析项目就绪状态（合并了业务理解和问题生成）"""
        try:
            session_data = self._get_session_data(session_id)
            if not session_data:
                raise ValueError("会话不存在或已过期")
            
            # 使用新的Project Readiness Evaluator进行分析
            evaluation = await self._evaluate_project_readiness(session_data["project_data"])
            
            # 更新会话数据
            session_data["readiness_evaluation"] = evaluation
            session_data["status"] = "readiness_evaluated"
            self._store_session_data(session_id, session_data)
            
            logger.info(f"完成项目就绪状态评估: {session_id}, 就绪状态: {evaluation.get('ready')}")
            return evaluation
        except Exception as e:
            logger.error(f"项目就绪状态评估失败: {str(e)}")
            raise
    
    async def generate_intelligent_questions(self, session_id: str) -> List[Dict[str, Any]]:
        """生成智能问题（基于就绪状态评估）"""
        try:
            session_data = self._get_session_data(session_id)
            if not session_data:
                raise ValueError("会话不存在或已过期")
                
            evaluation = session_data.get("readiness_evaluation")
            if not evaluation:
                raise ValueError("项目就绪状态评估未完成")
            
            # 如果项目已就绪，返回空问题列表
            if evaluation.get("ready") == "yes":
                questions = []
            else:
                # 将missing_info转换为问题格式
                missing_info = evaluation.get("missing_info", [])
                questions = [
                    {
                        "id": f"q{i+1}",
                        "question": question,
                        "priority": "critical",
                        "type": "text",
                        "options": []
                    }
                    for i, question in enumerate(missing_info)
                ]
            
            # 更新会话数据
            session_data["questions"] = questions
            session_data["status"] = "questions_generated" if questions else "ready_no_questions"
            self._store_session_data(session_id, session_data)
            
            logger.info(f"生成智能问题: {session_id}, 问题数量: {len(questions)}")
            return questions
        except Exception as e:
            logger.error(f"生成智能问题失败: {str(e)}")
            raise
    
    async def save_user_answers(self, session_id: str, answers: Dict[str, Any]) -> bool:
        """保存用户回答"""
        try:
            session_data = self._get_session_data(session_id)
            if not session_data:
                raise ValueError("会话不存在或已过期")
            
            session_data["user_answers"] = answers
            session_data["status"] = "answers_collected"
            self._store_session_data(session_id, session_data)
            
            logger.info(f"保存用户回答: {session_id}")
            return True
        except Exception as e:
            logger.error(f"保存用户回答失败: {str(e)}")
            raise
    
    async def predict_scenarios(self, session_id: str) -> List[Dict[str, Any]]:
        """预测常见场景"""
        try:
            session_data = self._get_session_data(session_id)
            if not session_data:
                raise ValueError("会话不存在或已过期")
                
            evaluation = session_data.get("readiness_evaluation")
            answers = session_data.get("user_answers", {})
            
            # 设置当前会话ID，供Agent调用器使用
            self._current_session_id = session_id
            
            # LLM预测场景（包含Agent分析）
            predictions = await self._llm_predict_scenarios(evaluation, answers)
            
            # 清理临时会话ID
            self._current_session_id = None
            
            # 更新会话数据
            session_data["scenario_predictions"] = predictions
            session_data["status"] = "scenarios_predicted"
            self._store_session_data(session_id, session_data)
            
            logger.info(f"预测场景: {session_id}, 场景数量: {len(predictions)}")
            return predictions
        except Exception as e:
            logger.error(f"场景预测失败: {str(e)}")
            raise
    
    async def save_scenario_confirmations(self, session_id: str, confirmations: Dict[str, Any]) -> bool:
        """保存场景确认信息"""
        try:
            session_data = self._get_session_data(session_id)
            if not session_data:
                raise ValueError("会话不存在或已过期")
            
            # 保存场景确认信息
            session_data["scenario_confirmations"] = confirmations
            session_data["status"] = "scenarios_confirmed"
            self._store_session_data(session_id, session_data)
            
            logger.info(f"保存场景确认: {session_id}, 确认数量: {len(confirmations)}")
            return True
        except Exception as e:
            logger.error(f"保存场景确认失败: {str(e)}")
            raise
    
    async def complete_intelligence_session(self, session_id: str) -> Dict[str, Any]:
        """完成智能理解会话"""
        try:
            session_data = self._get_session_data(session_id)
            if not session_data:
                raise ValueError("会话不存在或已过期")
            
            # 生成最终总结
            summary = {
                "readiness_evaluation": session_data.get("readiness_evaluation"),
                "user_supplements": session_data.get("user_answers"),
                "confirmed_scenarios": session_data.get("scenario_confirmations"),
                "completion_time": datetime.now().isoformat()
            }
            
            # 将用户自检信息保存到DataSource.description
            await self._save_intelligence_to_datasource(session_data)
            
            # 清理临时会话数据（重要数据已保存到数据库）
            self._cleanup_session(session_id)
            
            logger.info(f"完成智能理解会话并清理临时数据: {session_id}")
            return summary
        except Exception as e:
            logger.error(f"完成智能理解会话失败: {str(e)}")
            raise
    
    async def _save_intelligence_to_datasource(self, session_data: Dict[str, Any]) -> None:
        """将智能理解信息智能转换后保存到DataSource.description字段"""
        try:
            project_id = session_data.get("project_id")
            if not project_id:
                logger.warning("会话数据中缺少project_id，无法保存到数据源")
                return
            
            # 获取项目关联的数据源
            data_source = self.db.query(DataSource).filter(DataSource.project_id == project_id).first()
            if not data_source:
                logger.warning(f"项目 {project_id} 未找到关联的数据源")
                return
            
            # 智能筛选有价值的用户输入
            valuable_inputs = await self._extract_valuable_user_inputs(session_data)

            # 提取核心业务理解信息（即使用户没有补充输入也要保存）
            core_business_info = self._extract_core_business_understanding(session_data)

            # 如果既没有用户输入也没有核心业务理解，才跳过保存
            if not valuable_inputs and not core_business_info:
                logger.info("没有发现任何有价值的业务理解信息，跳过保存")
                return

            # 使用LLM将信息转换为专业的业务描述
            business_description = await self._generate_business_description(
                session_data, valuable_inputs, core_business_info
            )
            
            if business_description:
                # 格式化最终的业务描述
                formatted_description = self._format_business_description(business_description)
                
                # 更新数据源描述
                if data_source.description and data_source.description.strip():
                    # 检查是否已存在智能理解补充，如果存在则替换
                    existing_desc = data_source.description
                    if "## 业务理解补充" in existing_desc:
                        # 替换已有的智能理解部分
                        parts = existing_desc.split("## 业务理解补充")
                        data_source.description = f"{parts[0].strip()}\n\n## 业务理解补充\n{formatted_description}"
                    else:
                        # 添加新的智能理解部分
                        data_source.description = f"{existing_desc}\n\n## 业务理解补充\n{formatted_description}"
                else:
                    data_source.description = formatted_description
                
                self.db.commit()
                logger.info(f"已将智能业务描述保存到数据源 {data_source.id}")
            else:
                logger.warning("LLM生成业务描述失败，跳过保存")
                
        except Exception as e:
            logger.error(f"保存智能理解信息到数据源失败: {str(e)}")
            # 不抛出异常，避免影响会话完成流程

    async def _extract_valuable_user_inputs(self, session_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """智能筛选有价值的用户输入"""
        valuable_inputs = []
        
        # 处理问题阶段的用户回答
        user_answers = session_data.get("user_answers", {})
        questions = session_data.get("questions", [])
        
        if user_answers and questions:
            # 创建问题ID到问题文本的映射
            question_map = {q.get("id"): q.get("question") for q in questions if q.get("id")}
            
            for answer_id, answer_value in user_answers.items():
                if answer_value and str(answer_value).strip():
                    answer_text = str(answer_value).strip()
                    
                    # 过滤明显的测试输入（连续重复字符、纯数字等）
                    if self._is_valuable_input(answer_text):
                        question_text = question_map.get(answer_id, f"问题 {answer_id}")
                        valuable_inputs.append({
                            "type": "question_answer",
                            "question": question_text,
                            "answer": answer_text,
                            "source": "user_questions"
                        })
        
        # 处理场景验证阶段的有效反馈
        scenario_confirmations = session_data.get("scenario_confirmations", {})
        scenarios = session_data.get("scenario_predictions", [])
        
        if scenario_confirmations and scenarios:
            # 创建场景ID到场景信息的映射
            scenario_map = {s.get("id"): s for s in scenarios if s.get("id")}
            
            for scenario_id, confirmation_data in scenario_confirmations.items():
                if isinstance(confirmation_data, dict):
                    status = confirmation_data.get("status")
                    feedback = confirmation_data.get("feedback")
                    
                    # 只处理需要调整或理解错误的场景，且有有效反馈
                    if status in ["needs_revision", "rejected"] and feedback and feedback.strip():
                        feedback_text = feedback.strip()
                        
                        if self._is_valuable_input(feedback_text):
                            scenario_info = scenario_map.get(scenario_id, {})
                            valuable_inputs.append({
                                "type": "scenario_feedback",
                                "scenario_title": scenario_info.get("title", f"场景 {scenario_id}"),
                                "scenario_question": scenario_info.get("predicted_question", ""),
                                "feedback_type": status,
                                "feedback": feedback_text,
                                "source": "scenario_validation"
                            })
        
        logger.info(f"筛选出 {len(valuable_inputs)} 条有价值的用户输入")
        return valuable_inputs

    def _extract_core_business_understanding(self, session_data: Dict[str, Any]) -> Dict[str, Any]:
        """提取核心业务理解信息，即使用户没有补充输入也要保存这些信息"""
        core_info = {}

        # 提取业务理解分析结果
        readiness_evaluation = session_data.get("readiness_evaluation", {})
        if readiness_evaluation:
            # 提取项目理解信息
            project_understanding = readiness_evaluation.get("project_understanding", {})
            if project_understanding:
                core_info["business_domain"] = project_understanding.get("business_domain")
                core_info["business_type"] = project_understanding.get("business_type")
                core_info["key_entities"] = project_understanding.get("key_entities", [])
                core_info["core_metrics"] = project_understanding.get("core_metrics", [])
                core_info["data_relationships"] = project_understanding.get("data_relationships")

            # 提取可回答问题示例
            core_info["can_answer_examples"] = readiness_evaluation.get("can_answer_examples", [])

            # 提取分析边界
            core_info["boundaries"] = readiness_evaluation.get("boundaries", [])

        # 提取场景预测结果
        scenarios = session_data.get("scenario_predictions", [])
        if scenarios:
            # 只保留被用户确认的场景
            scenario_confirmations = session_data.get("scenario_confirmations", {})
            confirmed_scenarios = []

            for scenario in scenarios:
                scenario_id = scenario.get("id")
                if scenario_id:
                    confirmation = scenario_confirmations.get(scenario_id, {})
                    status = confirmation.get("status") if isinstance(confirmation, dict) else None

                    # 只保留被接受的场景
                    if status == "accepted":
                        confirmed_scenarios.append({
                            "title": scenario.get("title", ""),
                            "question": scenario.get("predicted_question", ""),
                            "understanding_points": scenario.get("understanding_points", [])
                        })

            if confirmed_scenarios:
                core_info["confirmed_scenarios"] = confirmed_scenarios

        # 过滤掉空值
        return {k: v for k, v in core_info.items() if v}

    def _is_valuable_input(self, text: str) -> bool:
        """判断用户输入是否有价值"""
        if not text or len(text.strip()) < 3:
            return False
        
        # 过滤明显的测试输入
        test_patterns = [
            r'^[0-9]+$',  # 纯数字
            r'^[a-zA-Z]+$',  # 纯字母
            r'^(.)\1{4,}',  # 连续重复字符
            r'^(test|测试|111|222|333|444|555|666|777|888|999)',  # 常见测试词
        ]
        
        import re
        for pattern in test_patterns:
            if re.match(pattern, text.strip(), re.IGNORECASE):
                logger.debug(f"过滤测试输入: {text[:20]}...")
                return False
        
        return True

    async def _generate_business_description(self, session_data: Dict[str, Any], valuable_inputs: List[Dict[str, Any]], core_business_info: Dict[str, Any] = None) -> Optional[str]:
        """使用LLM生成专业的业务描述"""
        try:
            from app.services.intelligence_prompts import BUSINESS_DESCRIPTION_GENERATOR_PROMPT
            
            # 获取项目数据
            project_id = session_data.get("project_id")
            project_data = await self._get_project_data(project_id)
            
            # 格式化所有业务理解信息
            formatted_inputs = []

            # 1. 格式化有价值的用户输入
            if valuable_inputs:
                formatted_inputs.append("## 用户补充信息")
                for input_item in valuable_inputs:
                    if input_item["type"] == "question_answer":
                        formatted_inputs.append(f"问题：{input_item['question']}")
                        formatted_inputs.append(f"回答：{input_item['answer']}")
                        formatted_inputs.append("")
                    elif input_item["type"] == "scenario_feedback":
                        feedback_type_text = "需要调整" if input_item["feedback_type"] == "needs_revision" else "理解有误"
                        formatted_inputs.append(f"场景：{input_item['scenario_title']}")
                        formatted_inputs.append(f"预测问题：{input_item['scenario_question']}")
                        formatted_inputs.append(f"用户反馈（{feedback_type_text}）：{input_item['feedback']}")
                        formatted_inputs.append("")

            # 2. 格式化核心业务理解信息
            if core_business_info:
                formatted_inputs.append("## AI业务理解分析结果")

                if core_business_info.get("business_domain"):
                    formatted_inputs.append(f"业务领域：{core_business_info['business_domain']}")
                if core_business_info.get("business_type"):
                    formatted_inputs.append(f"业务类型：{core_business_info['business_type']}")
                if core_business_info.get("key_entities"):
                    formatted_inputs.append(f"关键业务实体：{', '.join(core_business_info['key_entities'])}")
                if core_business_info.get("core_metrics"):
                    formatted_inputs.append(f"核心业务指标：{', '.join(core_business_info['core_metrics'])}")
                if core_business_info.get("data_relationships"):
                    formatted_inputs.append(f"数据关系：{core_business_info['data_relationships']}")

                if core_business_info.get("can_answer_examples"):
                    formatted_inputs.append("可回答问题示例：")
                    for example in core_business_info["can_answer_examples"]:
                        formatted_inputs.append(f"- {example}")

                if core_business_info.get("boundaries"):
                    formatted_inputs.append("分析边界和限制：")
                    for boundary in core_business_info["boundaries"]:
                        formatted_inputs.append(f"- {boundary}")

                if core_business_info.get("confirmed_scenarios"):
                    formatted_inputs.append("用户确认的分析场景：")
                    for scenario in core_business_info["confirmed_scenarios"]:
                        formatted_inputs.append(f"- {scenario['title']}: {scenario['question']}")

                formatted_inputs.append("")

            # 如果没有任何信息，返回None
            if not formatted_inputs:
                logger.warning("没有任何业务理解信息可以生成描述")
                return None

            valuable_user_inputs = "\n".join(formatted_inputs)
            
            # 构建提示词
            prompt = BUSINESS_DESCRIPTION_GENERATOR_PROMPT.format(
                project_name=project_data["project"]["name"],
                project_description=project_data["project"]["description"] or "无描述",
                datasource_types=", ".join([ds["type"] for ds in project_data["data_sources"]]),
                table_schemas=json.dumps([{
                    "table_name": table["table_name"],
                    "description": table["table_description"] or "无描述",
                    "schema": table["table_schema"]
                } for table in project_data["selected_tables"]], ensure_ascii=False, indent=2),
                valuable_user_inputs=valuable_user_inputs
            )
            
            logger.info("开始调用LLM生成业务描述...")
            
            # 调用LLM
            if hasattr(self.llm_service, 'client'):
                response = await self.llm_service.client.chat.completions.create(
                    model=self.llm_service.model,
                    messages=[
                        {"role": "system", "content": "你是一个专业的业务分析专家。你的任务是将用户的业务补充信息转换为简洁的schema补充说明，不要重复描述已有的表结构信息。请直接输出补充说明文本，不要使用JSON或markdown格式。"},
                        {"role": "user", "content": prompt}
                    ],
                    temperature=0.2  # 较低温度确保输出稳定
                )
                result_text = response.choices[0].message.content
            else:
                result_text = await self.llm_service.generate_text(prompt)
            
            # 直接返回LLM生成的文本，进行基本清理
            if result_text and result_text.strip():
                # 移除可能的markdown代码块标记
                cleaned_text = result_text.strip()
                if cleaned_text.startswith("```"):
                    lines = cleaned_text.split('\n')
                    if len(lines) > 2 and lines[-1].strip() == "```":
                        cleaned_text = '\n'.join(lines[1:-1])
                
                logger.info("✅ 业务描述生成成功")
                return cleaned_text.strip()
            else:
                logger.warning("LLM返回空内容")
                return None
                
        except Exception as e:
            logger.error(f"生成业务描述失败: {str(e)}")
            return None

    def _format_business_description(self, business_desc: str) -> str:
        """格式化业务描述为适合保存的文本"""
        # 由于LLM直接输出补充说明，这里只需要基本的格式清理
        if not business_desc:
            return ""

        # 确保描述是简洁的补充信息格式
        formatted_desc = business_desc.strip()

        # 移除可能的冗余前缀（如果LLM添加了不必要的介绍）
        unnecessary_prefixes = [
            "业务背景：", "系统描述：", "项目说明：",
            "这是一个", "该系统", "本项目", "数据库包含",
            "根据提供的信息", "基于以上信息", "综合分析"
        ]

        for prefix in unnecessary_prefixes:
            if formatted_desc.startswith(prefix):
                formatted_desc = formatted_desc[len(prefix):].strip()

        # 移除可能的冗余后缀
        unnecessary_suffixes = [
            "以上是对数据库的补充说明", "这些信息有助于数据分析",
            "希望这些信息对您有帮助"
        ]

        for suffix in unnecessary_suffixes:
            if formatted_desc.endswith(suffix):
                formatted_desc = formatted_desc[:-len(suffix)].strip()

        # 规范化句子结尾
        lines = formatted_desc.split('\n')
        formatted_lines = []

        for line in lines:
            line = line.strip()
            if line:
                # 确保每行都以适当的标点结尾
                if not line.endswith(('。', '.', '；', ';', '：', ':')):
                    line += '。'
                formatted_lines.append(line)

        return '\n'.join(formatted_lines)
    
    def get_session_status(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取会话状态"""
        try:
            session_data = self._get_session_data(session_id)
            if not session_data:
                return None
            
            return {
                "status": session_data.get("status"),
                "created_at": session_data.get("created_at"),
                "updated_at": session_data.get("updated_at")
            }
        except Exception as e:
            logger.error(f"获取会话状态失败: {str(e)}")
            return None
    
    async def _get_project_data(self, project_id: str) -> Dict[str, Any]:
        """获取项目上下文数据"""
        try:
            # 获取项目基本信息
            project = self.db.query(Project).filter(Project.id == project_id).first()
            if not project:
                raise ValueError(f"项目 {project_id} 不存在")
            
            # 获取数据源信息
            data_sources = self.db.query(DataSource).filter(
                DataSource.project_id == project_id
            ).all()
            
            # 获取选定的表信息
            selected_tables = []
            for ds in data_sources:
                tables = self.db.query(SelectedTable).filter(
                    SelectedTable.data_source_id == ds.id
                ).all()
                selected_tables.extend(tables)
            
            return {
                "project": {
                    "id": project.id,
                    "name": project.name,
                    "description": project.description
                },
                "data_sources": [
                    {
                        "id": ds.id,
                        "name": ds.name,
                        "type": ds.type,
                        "description": ds.description
                    } for ds in data_sources
                ],
                "selected_tables": [
                    {
                        "table_name": table.table_name,
                        "table_description": table.table_description,
                        "table_schema": table.table_schema,
                        "sample_data": table.sample_data[:3] if table.sample_data else []  # 只取前3条样本
                    } for table in selected_tables
                ]
            }
        except Exception as e:
            logger.error(f"获取项目数据失败: {str(e)}")
            raise
    

    
    async def _evaluate_project_readiness(self, project_data: Dict[str, Any]) -> Dict[str, Any]:
        """使用新的Project Readiness Evaluator评估项目就绪状态"""
        try:
            from app.services.intelligence_prompts import PROJECT_READINESS_EVALUATOR_PROMPT
            
            # 构建评估提示词
            prompt = PROJECT_READINESS_EVALUATOR_PROMPT.format(
                project_name=project_data["project"]["name"],
                project_description=project_data["project"]["description"] or "无描述",
                datasource_types=", ".join([ds["type"] for ds in project_data["data_sources"]]),
                datasource_details=json.dumps([{
                    "name": ds["name"],
                    "type": ds["type"],
                    "description": ds["description"] or "无描述"
                } for ds in project_data["data_sources"]], ensure_ascii=False, indent=2),
                table_schemas=json.dumps([{
                    "table_name": table["table_name"],
                    "description": table["table_description"] or "无描述",
                    "schema": table["table_schema"]
                } for table in project_data["selected_tables"]], ensure_ascii=False, indent=2),
                sample_data=json.dumps([{
                    "table_name": table["table_name"],
                    "samples": table["sample_data"]
                } for table in project_data["selected_tables"]], ensure_ascii=False, indent=2)
            )
            
            # 调用LLM
            if hasattr(self.llm_service, 'client'):
                response = await self.llm_service.client.chat.completions.create(
                    model=self.llm_service.model,
                    messages=[
                        {"role": "system", "content": "你是一个专业的项目就绪状态评估专家，请严格按照JSON格式输出评估结果。"},
                        {"role": "user", "content": prompt}
                    ],
                    temperature=0.1  # 降低温度以获得更一致的结果
                )
                result_text = response.choices[0].message.content
            else:
                # 备用方法
                result_text = await self.llm_service.generate_text(prompt)
            # 尝试解析JSON结果
            try:
                # 先尝试直接解析
                result = json.loads(result_text)
            except json.JSONDecodeError:
                # 如果直接解析失败，尝试提取markdown代码块中的JSON
                import re
                # 🔧 增强的markdown清理逻辑
                cleaned_text = result_text.strip()

                # 方法1: 匹配完整的markdown代码块
                json_match = re.search(r'```(?:json)?\s*(.*?)\s*```', cleaned_text, re.DOTALL)
                if json_match:
                    json_content = json_match.group(1).strip()
                    result = json.loads(json_content)
                    logger.info("成功从markdown代码块中提取JSON内容")
                else:
                    # 方法2: 移除可能的markdown标记
                    if cleaned_text.startswith('```json'):
                        cleaned_text = cleaned_text[7:].strip()
                    elif cleaned_text.startswith('```'):
                        cleaned_text = cleaned_text[3:].strip()

                    if cleaned_text.endswith('```'):
                        cleaned_text = cleaned_text[:-3].strip()

                    # 方法3: 查找JSON对象边界
                    start_pos = cleaned_text.find('{')
                    end_pos = cleaned_text.rfind('}')

                    if start_pos != -1 and end_pos != -1 and end_pos > start_pos:
                        json_content = cleaned_text[start_pos:end_pos + 1]
                        result = json.loads(json_content)
                        logger.info("通过边界查找成功提取JSON内容")
                    else:
                        # 如果所有方法都失败，抛出错误
                        logger.error(f"无法从响应中提取有效JSON: {result_text[:200]}...")
                        raise json.JSONDecodeError("无法提取有效JSON", result_text, 0)
            
                # 验证必需字段
                if "ready" not in result:
                    raise ValueError("缺少ready字段")
                
                # 确保字段格式正确
                if result["ready"] not in ["yes", "no"]:
                    result["ready"] = "no"
                
                # 设置默认值
                result.setdefault("reason_if_no", "")
                result.setdefault("project_understanding", {})
                result.setdefault("can_answer_examples", [])
                result.setdefault("boundaries", [])
                result.setdefault("missing_info", [])
                
                # 确保project_understanding有完整结构
                if not isinstance(result["project_understanding"], dict):
                    result["project_understanding"] = {}
                
                understanding = result["project_understanding"]
                understanding.setdefault("business_domain", "未知领域")
                understanding.setdefault("business_type", "数据分析项目")
                understanding.setdefault("key_entities", [])
                understanding.setdefault("core_metrics", [])
                understanding.setdefault("data_relationships", "需要进一步分析")
                
                # 如果ready=yes，清空missing_info
                if result["ready"] == "yes":
                    result["missing_info"] = []
                    result["reason_if_no"] = ""
                
            except (json.JSONDecodeError, ValueError) as e:
                # 如果解析失败，返回默认结构
                logger.warning(f"LLM返回的JSON格式错误: {result_text}")
                result = {
                    "ready": "no",
                    "reason_if_no": "无法解析项目结构，需要更多信息",
                    "project_understanding": {
                        "business_domain": "待确认",
                    "business_type": "数据分析项目",
                        "key_entities": ["数据表"],
                        "core_metrics": ["基础统计"],
                        "data_relationships": "需要进一步确认"
                    },
                    "can_answer_examples": [
                        "数据总量统计",
                        "基础字段查询"
                    ],
                    "boundaries": [
                        "数据分析受限于当前schema理解",
                        "复杂业务逻辑需要补充说明"
                    ],
                    "missing_info": [
                        "请描述主要的业务流程是什么？",
                        "数据表之间的关联关系如何？",
                        "最关心的业务指标有哪些？"
                    ]
                }
            
            return result
        except Exception as e:
            logger.error(f"项目就绪状态评估失败: {str(e)}")
            # 返回默认评估结果
            return {
                "ready": "no",
                "reason_if_no": f"评估过程出错: {str(e)}",
                "project_understanding": {
                    "business_domain": "系统错误",
                    "business_type": "无法确定",
                    "key_entities": [],
                    "core_metrics": [],
                    "data_relationships": "评估失败"
                },
                "can_answer_examples": [
                    "基础数据查询",
                    "简单统计分析"
                ],
                "boundaries": [
                    "系统暂时无法完成评估",
                    "需要重新尝试分析"
                ],
                "missing_info": [
                    "请提供更详细的项目描述",
                    "确认数据表的业务含义",
                    "说明主要的分析需求"
                ]
            }
    
    async def _llm_predict_scenarios(self, readiness_evaluation: Dict[str, Any], answers: Dict[str, Any]) -> List[Dict[str, Any]]:
        """LLM预测场景，并使用Agent能力分析理解要点"""
        try:
            from app.services.intelligence_prompts import SCENARIO_PREDICTION_PROMPT
            from app.services.scenario_agent_caller import ScenarioAgentCaller
            
            # 获取项目数据用于场景预测
            session_data = self._get_session_data(getattr(self, '_current_session_id', None))
            project_id = session_data.get("project_id") if session_data else None
            
            if not project_id:
                logger.warning("无法获取project_id，使用简化的场景预测")
                return self._get_default_scenarios()
            
            project_data = await self._get_project_data(project_id)
            
            # 第一步：LLM预测场景，包含完整的项目和数据信息
            prompt = SCENARIO_PREDICTION_PROMPT.format(
                project_name=project_data["project"]["name"],
                project_description=project_data["project"]["description"] or "无描述",
                datasource_types=", ".join([ds["type"] for ds in project_data["data_sources"]]),
                datasource_details=json.dumps([{
                    "name": ds["name"],
                    "type": ds["type"],
                    "description": ds["description"] or "无描述"
                } for ds in project_data["data_sources"]], ensure_ascii=False, indent=2),
                table_schemas=json.dumps([{
                    "table_name": table["table_name"],
                    "description": table["table_description"] or "无描述",
                    "schema": table["table_schema"]
                } for table in project_data["selected_tables"]], ensure_ascii=False, indent=2),
                sample_data=json.dumps([{
                    "table_name": table["table_name"],
                    "samples": table["sample_data"]
                } for table in project_data["selected_tables"]], ensure_ascii=False, indent=2),
                readiness_evaluation=json.dumps(readiness_evaluation, ensure_ascii=False, indent=2),
                user_answers=json.dumps(answers, ensure_ascii=False, indent=2)
            )
            
            logger.info("开始调用LLM生成场景预测...")
            
            # 调用LLM生成场景
            if hasattr(self.llm_service, 'client'):
                response = await self.llm_service.client.chat.completions.create(
                    model=self.llm_service.model,
                    messages=[
                        {"role": "system", "content": "你是一个专业的数据分析专家，请预测用户可能的分析需求场景。严格按照JSON格式返回，不要包含markdown代码块。"},
                        {"role": "user", "content": prompt}
                    ],
                    temperature=0.4
                )
                result_text = response.choices[0].message.content
            else:
                result_text = await self.llm_service.generate_text(prompt)
            logger.info(f"LLM场景预测响应长度: {len(result_text)} 字符")
            logger.debug(f"LLM场景预测原始响应: {result_text[:500]}...")
            
            # 解析场景结果 - 使用统一的JSON解析逻辑
            scenarios = []
            parsed_result = self._parse_json_with_fallback(result_text, "场景预测")
            
            if parsed_result:
                scenarios = parsed_result.get("scenarios", [])
                logger.info(f"✅ 场景预测解析成功，获得{len(scenarios)}个场景")
                
                # 验证场景数据完整性
                for i, scenario in enumerate(scenarios):
                    if not scenario.get("predicted_question"):
                        logger.warning(f"场景{i+1}缺少predicted_question字段")
                        scenario["predicted_question"] = scenario.get("title", f"场景{i+1}的分析问题")
                        
                    # 确保基本字段都存在
                    scenario.setdefault("id", f"s{i+1}")
                    scenario.setdefault("title", f"场景{i+1}")
                    scenario.setdefault("description", "数据分析场景")
                    scenario.setdefault("understanding_points", ["数据分析", "业务理解"])
                    scenario.setdefault("solution_approach", "基于数据的分析方法")
                    scenario.setdefault("confidence_score", 0.7)
            else:
                logger.error("场景预测JSON解析完全失败，使用默认场景")
                scenarios = self._get_default_scenarios()
            
            # 确保至少有一些场景
            if not scenarios:
                logger.warning("没有解析到任何场景，使用默认场景")
                scenarios = self._get_default_scenarios()
            
            logger.info(f"场景预测完成，共{len(scenarios)}个场景")
            for i, scenario in enumerate(scenarios):
                logger.info(f"场景{i+1}: {scenario.get('title', '无标题')} - {scenario.get('predicted_question', '无问题')[:50]}...")
            
            # 第二步：使用Agent能力分析每个场景的理解要点
            logger.info(f"开始使用Agent分析{len(scenarios)}个场景的理解要点")
            
            # 从会话数据中获取project_id
            session_data = self._get_session_data(getattr(self, '_current_session_id', None))
            project_id = session_data.get("project_id") if session_data else None
            
            if project_id and scenarios:
                try:
                    # 创建Agent调用器
                    agent_caller = ScenarioAgentCaller(db=self.db)
                    
                    # 提取预测问题
                    predicted_questions = [
                        scenario.get("predicted_question", scenario.get("title", ""))
                        for scenario in scenarios
                        if scenario.get("predicted_question") or scenario.get("title")
                    ]
                    
                    logger.info(f"提取到{len(predicted_questions)}个预测问题进行Agent分析")
                    for i, q in enumerate(predicted_questions):
                        logger.debug(f"预测问题{i+1}: {q[:100]}...")
                    
                    if predicted_questions:
                        # 获取用户语言偏好
                        user_id = session_data.get("user_id")
                        user_language = 'zh-CN'  # 默认中文
                        if user_id:
                            try:
                                from app.models.user import User
                                user = self.db.query(User).filter(User.id == user_id).first()
                                if user and user.language_preference:
                                    user_language = user.language_preference
                            except Exception as e:
                                logger.warning(f"获取用户语言偏好失败，使用默认中文: {str(e)}")
                        
                        # 批量调用Agent分析（传入会话数据）
                        agent_results = await agent_caller.batch_analyze_scenarios(
                            predicted_questions, project_id, user_language, session_data
                        )
                        
                        # 将Agent分析结果合并到场景中
                        for i, (scenario, agent_result) in enumerate(zip(scenarios, agent_results)):
                            if agent_result.get("success"):
                                # 使用Agent的真实理解要点
                                scenario["understanding_points"] = agent_result.get("understanding_points", [])
                                scenario["agent_analysis"] = {
                                    "success": True,
                                    "reasoning": agent_result.get("reasoning", ""),
                                    "planning_steps": agent_result.get("planning_steps", []),
                                    "token_usage": agent_result.get("token_usage", {}),
                                    "analysis_time": agent_result.get("analysis_time")
                                }
                                logger.info(f"场景{i+1}使用Agent分析成功")
                            else:
                                # Agent分析失败时保留原有理解要点
                                scenario["agent_analysis"] = {
                                    "success": False,
                                    "error": agent_result.get("error", "未知错误"),
                                    "fallback_used": True
                                }
                                logger.warning(f"场景{i+1}Agent分析失败，保留默认理解要点")
                    
                    logger.info(f"Agent场景分析完成，成功: {sum(1 for s in scenarios if s.get('agent_analysis', {}).get('success', False))}/{len(scenarios)}")
                    
                except Exception as e:
                    logger.error(f"Agent场景分析过程失败: {str(e)}")
                    # 如果Agent分析失败，保留原有的理解要点
                    for scenario in scenarios:
                        scenario["agent_analysis"] = {
                            "success": False,
                            "error": f"Agent分析失败: {str(e)}",
                            "fallback_used": True
                        }
            else:
                logger.warning("缺少project_id或scenarios为空，跳过Agent分析")
            
            return scenarios
            
        except Exception as e:
            logger.error(f"场景预测失败: {str(e)}")
            logger.error(f"场景预测异常详情: ", exc_info=True)
            return self._get_default_scenarios()
    
    def _get_default_questions(self) -> List[Dict[str, Any]]:
        """获取默认问题"""
        return [
            {
                "id": "q1",
                "question": "您最关心的业务指标是什么？",
                "priority": "critical",
                "type": "text",
                "options": []
            },
            {
                "id": "q2", 
                "question": "您通常需要多久查看一次数据报告？",
                "priority": "important",
                "type": "single_choice",
                "options": ["每天", "每周", "每月", "按需查看"]
            },
            {
                "id": "q3",
                "question": "在数据分析中，您最希望发现什么样的洞察？",
                "priority": "important",
                "type": "text",
                "options": []
            }
        ]
    
    def _get_default_scenarios(self) -> List[Dict[str, Any]]:
        """获取默认场景"""
        return [
            {
                "id": "s1",
                "title": "数据概览分析",
                "description": "查看整体数据情况和基础统计",
                "predicted_question": "帮我看看整体的数据情况如何？",
                "understanding_points": ["数据总量", "基础统计指标"],
                "solution_approach": "提供数据概览和基础统计信息",
                "confidence_score": 0.8
            },
            {
                "id": "s2",
                "title": "趋势变化分析",
                "description": "分析数据随时间的变化趋势",
                "predicted_question": "最近的数据趋势是怎样的？",
                "understanding_points": ["时间序列分析", "变化趋势"],
                "solution_approach": "生成时间序列图表和趋势分析",
                "confidence_score": 0.75
            }
        ]
    
    def _store_session_data(self, session_id: str, session_data: Dict[str, Any] = None):
        """存储会话数据，如果session_data为None则删除会话"""

        # 如果session_data为None，删除会话
        if session_data is None:
            if self.use_redis:
                try:
                    RedisClient.delete(f"intelligence_session:{session_id}")
                    logger.debug(f"从Redis删除会话数据: {session_id}")
                    return
                except Exception as e:
                    logger.warning(f"Redis删除失败: {str(e)}")

            # 从内存中删除
            if session_id in _GLOBAL_SESSION_STORE:
                del _GLOBAL_SESSION_STORE[session_id]
                logger.debug(f"从内存删除会话数据: {session_id}")
            return

        # 存储会话数据
        session_data["updated_at"] = datetime.now().isoformat()

        if self.use_redis:
            try:
                # 使用Redis存储，设置2小时过期
                success = RedisClient.set_value(
                    f"intelligence_session:{session_id}",
                    json.dumps(session_data, ensure_ascii=False),
                    ex=7200  # 2小时过期
                )
                if success:
                    logger.debug(f"存储会话数据到Redis: {session_id}")
                    return
                else:
                    raise Exception("Redis存储操作返回False")
            except Exception as e:
                logger.warning(f"Redis存储失败，降级到内存存储: {str(e)}")
                self.use_redis = False
        
        # 降级到内存存储
        _GLOBAL_SESSION_STORE[session_id] = session_data
        logger.debug(f"存储会话数据到内存: {session_id}, 当前内存中有 {len(_GLOBAL_SESSION_STORE)} 个会话")
    
    def _get_session_data(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取会话数据"""
        if self.use_redis:
            try:
                data = RedisClient.get_value(f"intelligence_session:{session_id}")
                if data:
                    session_data = json.loads(data)
                    logger.debug(f"从Redis获取会话数据: {session_id}")
                    return session_data
                else:
                    logger.warning(f"Redis中未找到会话: {session_id}")
                    return None
            except Exception as e:
                logger.warning(f"Redis读取失败，尝试内存存储: {str(e)}")
                self.use_redis = False
        
        # 从内存获取
        session_data = _GLOBAL_SESSION_STORE.get(session_id)
        if session_data:
            logger.debug(f"从内存获取会话数据: {session_id}")
        else:
            logger.warning(f"内存中未找到会话: {session_id}, 当前存储的会话: {list(_GLOBAL_SESSION_STORE.keys())}")
        return session_data
    
    def _cleanup_session(self, session_id: str) -> None:
        """清理指定会话的临时数据"""
        if self.use_redis:
            try:
                success = RedisClient.delete(f"intelligence_session:{session_id}")
                if success:
                    logger.debug(f"从Redis清理会话数据: {session_id}")
                    return
                else:
                    logger.warning(f"Redis清理操作返回False: {session_id}")
            except Exception as e:
                logger.warning(f"Redis清理失败: {str(e)}")
        
        # 从内存清理
        if session_id in _GLOBAL_SESSION_STORE:
            del _GLOBAL_SESSION_STORE[session_id]
            logger.debug(f"从内存清理会话数据: {session_id}")
    

    
    def get_active_sessions_count(self) -> int:
        """获取当前活跃会话数量"""
        if self.use_redis:
            try:
                keys = RedisClient.scan_keys("intelligence_session:*")
                return len(keys)
            except Exception as e:
                logger.warning(f"Redis统计失败: {str(e)}")
        
        return len(_GLOBAL_SESSION_STORE) 