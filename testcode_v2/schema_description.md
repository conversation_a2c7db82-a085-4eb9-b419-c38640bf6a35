# 业务表结构说明

## 表: FINRISK_BANK_ACCOUNTS
### 列信息:
- ID (NUMBER(22), 可空)
- NAME (VARCHAR2(400), 可空)
- ACCOUNT (VARCHAR2(40), 可空)
- BALANCE (NUMBER(20,2), 可空)

--------------------------------------------------

## 表: FINRISK_BANK_TRANSFERS
### 列信息:
- TXN_ID (NUMBER(22), 可空)
- SRC_ACCT_ID (NUMBER(22), 可空)
- DST_ACCT_ID (NUMBER(22), 可空)
- DESCRIPTION (VARCHAR2(400), 可空)
- AMOUNT (NUMBER(22), 可空)

--------------------------------------------------

## 表: FINRISK_BASE_INFO
### 列信息:
- ID (NUMBER(22), 非空, 默认值: "CMCM"."ISEQ$$_92299".nextval)
- COMPANY_NAME (VARCHAR2(400), 可空)
- COMPANY_TYPE (VARCHAR2(100), 可空)
- REG_DATE (DATE(7), 可空)
- REG_CAPITAL (NUMBER(15,2), 可空)
- REG_ADDR (VARCHAR2(400), 可空)
- LEGAL_PERSON (VARCHAR2(100), 可空)
- LEGAL_PHONE (VARCHAR2(100), 可空)
- LSRZZ (VARCHAR2(100), 可空)
- LSRXYDJ (VARCHAR2(100), 可空)
- BIZ_SCOPE (VARCHAR2(4000), 可空)
- BIZ_STATUS (VARCHAR2(100), 可空)
- IS_SXQY (VARCHAR2(10), 可空)
- IS_SXBZXR (VARCHAR2(10), 可空)
- IS_WDQKTGG (VARCHAR2(10), 可空)
- BGSL (VARCHAR2(10), 可空)
- FMYQSL (VARCHAR2(10), 可空)

--------------------------------------------------

## 表: FINRISK_FILES
### 列信息:
- ID (NUMBER(22), 非空, 默认值: "CMCM"."ISEQ$$_92305".nextval)
- FILE_NAME (VARCHAR2(4000), 可空)
- FILE_MIME_TYPE (VARCHAR2(100), 可空)
- FILE_BLOB (BLOB(4000), 可空)
- FILE_VEC (VECTOR(8200), 可空)
- FILE_CONTENT (CLOB(4000), 可空)
- COMPANY_ID (NUMBER(22), 可空)
- ACTIVE_YN (VARCHAR2(10), 可空, 默认值: 'Y')
- UPDATED_ON (DATE(7), 可空, 默认值: current_date)

--------------------------------------------------

## 表: FINRISK_FILES_EMBEDDING
### 列信息:
- ID (NUMBER(22), 非空, 默认值: "CMCM"."ISEQ$$_92313".nextval)
- FILE_ID (NUMBER(22), 非空)
- CHUNK_OFFSET (NUMBER(22), 可空)
- CHUNK_LENGTH (NUMBER(22), 可空)
- CHUNK_TEXT (VARCHAR2(4000), 可空)
- VECTOR_DATA (VECTOR(8200), 可空)

--------------------------------------------------

## 表: FINRISK_NEWS
### 列信息:
- ID (NUMBER(22), 非空, 默认值: "CMCM"."ISEQ$$_92301".nextval)
- COMPANY_ID (NUMBER(22), 可空)
- HAPPEN_DATE (DATE(7), 可空)
- NEWS_CONTENT (CLOB(4000), 可空)
- TAG (VARCHAR2(4000), 可空)
- URL (VARCHAR2(4000), 可空)

--------------------------------------------------

# 业务视图说明
