---
type: "always_apply"
---

# MCP (Message Control & Processing) 服务调用规范


## 重要：用户身份标识
当前用户ID: 214ac9f64e36811a
**调用任何smart-promptor-mcp服务时，必须在context参数中包含用户ID！**

## 1. 对于用户的prompt，请调用合适的MCP服务进行增强，后再响应

## 2. 代码固化区域保护规则

在 Cursor 进行代码编辑或重构操作时：
-   若目标代码文件中存在由特定注释标识符界定的"代码固化区域"，Cursor **严禁**修改该区域内的任何代码内容。
-   **必须**向用户明确提示此区域的不可修改性。
-   用户仅可修改固化区域以外的代码。
-   代码固化区域通过以下成对的注释标识符进行定义：
    ```
    // @fixed_implementation_start (或适用于对应语言的注释符号)
    [此区域内的代码片段内容不可修改]
    // @fixed_implementation_end (或适用于对应语言的注释符号)
    ```
*此文件由 SmartPromptor v1.2.9 自动生成于 2025-06-16T03:34:40.276Z*