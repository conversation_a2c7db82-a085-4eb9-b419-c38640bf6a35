# 统一应用镜像 - 基于基础镜像进行快速构建
FROM harbor-china.cmcm.com/public/decisionai:demo-base

WORKDIR /app

# 复制并安装Python依赖（确保最新依赖）
COPY requirements.txt .
RUN pip install --no-cache-dir -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt

# 复制前端构建结果
COPY frontend/build /var/www/html

# 复制后端代码
COPY main.py ./
COPY app/ ./app/

# 复制配置文件
COPY nginx.conf /etc/nginx/sites-available/default

# 复制环境配置文件（如果存在）
COPY .env.ami ./.env.prod

# 创建必要的日志目录
RUN mkdir -p /var/log/nginx

# 复制启动脚本并设置权限
COPY start.sh /start.sh
RUN chmod +x /start.sh && \
    sed -i 's/\r$//' /start.sh && \
    ls -la /start.sh

ENV APP_ENV=prod

# 暴露端口
EXPOSE 8301 9310

# 使用启动脚本启动nginx和后端应用
CMD ["/start.sh"]
