# 多轮分析完整数据持久化方案

## 概述

本方案实现了多轮分析的完整数据持久化和还原功能，确保用户在切换会话时能够完整还原每一轮的分析内容，包括工具执行步骤、分析报告等。同时集成了LLM总结功能，为后续轮次提供智能化的上下文。

## 核心功能

### 1. 完整数据持久化

#### 1.1 数据库模型扩展
- **Analysis表新增字段**：
  - `analysis_steps`: 分析步骤详情（JSON）
  - `tool_results`: 工具执行结果映射（JSON）
  - `mcp_results`: MCP执行结果（JSON）
  - `status`: 分析状态（running/completed/failed）
  - `execution_time`: 总执行时间
  - `context_relevance`: 上下文相关性评分
  - `llm_summary`: LLM生成的本轮次总结

- **ToolExecution表新增字段**：
  - `status`: 执行状态
  - `error_message`: 错误信息
  - `step_order`: 步骤顺序
  - `reasoning`: 执行推理过程

#### 1.2 数据保存机制
```python
# 在分析完成时保存完整状态
conversation_service.save_analysis_state(
    analysis_id=analysis_id,
    analysis_steps=analysis_steps,      # 完整的步骤列表
    tool_results=tool_results,          # 工具结果映射
    mcp_results=mcp_results,            # MCP结果
    final_report=final_report,          # 最终报告
    status='completed',                 # 状态
    execution_time=execution_time,      # 执行时间
    context_relevance=context_relevance # 上下文相关性
)
```

### 2. 数据还原机制

#### 2.1 会话加载
```typescript
// 前端加载会话时获取完整状态
const analysesResponse = await conversationApi.getConversationAnalyses(convId);
const analyses = analysesResponse.data;

// 还原每一轮的完整状态
const rounds: AnalysisRound[] = analyses.map((analysis: any) => ({
    roundId: `round_${analysis.analysis_id}`,
    query: analysis.query,
    analysisId: analysis.analysis_id,
    analysisSteps: analysis.analysis_steps || [],      // 还原分析步骤
    toolResults: analysis.tool_executions || [],       // 还原工具结果
    finalReport: analysis.final_report || '',          // 还原最终报告
    status: analysis.status,                           // 还原状态
    intentAnalysis: analysis.intent_analysis,          // 还原意图分析
    mcpResults: analysis.mcp_results || {},            // 还原MCP结果
    contextRelevance: analysis.context_relevance,      // 还原上下文相关性
    contextSummary: analysis.llm_summary               // 还原LLM总结
}));
```

#### 2.2 UI还原
- **时间线步骤**：完整还原每个工具执行步骤
- **工具结果**：还原每个步骤的执行结果和数据
- **分析报告**：还原完整的Markdown格式报告
- **上下文信息**：显示轮次间的相关性和总结

### 3. LLM智能总结

#### 3.1 轮次总结生成
```python
async def generate_round_summary(self, analysis_id: str) -> Optional[str]:
    """使用LLM生成轮次总结"""
    # 构建总结提示词
    prompt = self._build_summary_prompt(analysis_state)
    
    # 调用LLM生成总结
    completion = await self.llm_client.chat.completions.create(
        model=settings.OPENAI_MODEL,
        messages=[
            {"role": "system", "content": "你是一个专业的数据分析总结助手"},
            {"role": "user", "content": prompt}
        ],
        max_tokens=500,
        temperature=0.3
    )
    
    # 保存总结到数据库
    analysis.llm_summary = summary
```

#### 3.2 上下文构建
```python
async def get_conversation_context_with_llm_summary(
    self, 
    conversation_id: str,
    max_rounds: int = 5
) -> str:
    """获取会话上下文并生成LLM总结用于提示词"""
    
    # 获取历史分析记录
    analyses = self.db.query(Analysis).filter(
        Analysis.conversation_id == conversation_id,
        Analysis.status == 'completed'
    ).order_by(Analysis.round_number.desc()).limit(max_rounds).all()
    
    # 构建历史总结
    history_summaries = []
    for analysis in reversed(analyses):
        if analysis.llm_summary:
            summary = analysis.llm_summary
        else:
            summary = self._generate_simple_summary(analysis)
        
        history_summaries.append(f"""
第{analysis.round_number}轮分析：
- 查询：{analysis.query}
- 总结：{summary}
- 时间：{analysis.created_at.strftime('%Y-%m-%d %H:%M')}
""")
    
    # 构建上下文提示词
    context_prompt = f"""
## 历史分析上下文

本次是多轮数据分析对话，以下是之前的分析历史：

{''.join(history_summaries)}

## 分析指导
请基于以上历史分析上下文，理解用户的分析需求和已有发现，避免重复分析，在回答中体现对话的连贯性。
"""
    
    return context_prompt
```

## 技术实现

### 1. 数据库迁移

执行以下SQL脚本添加新字段：

```sql
-- 为 analyses 表添加新字段
ALTER TABLE analyses 
ADD COLUMN analysis_steps JSON COMMENT '分析步骤详情',
ADD COLUMN tool_results JSON COMMENT '工具执行结果映射',
ADD COLUMN mcp_results JSON COMMENT 'MCP执行结果',
ADD COLUMN status VARCHAR(20) DEFAULT 'running' COMMENT '分析状态',
ADD COLUMN execution_time FLOAT COMMENT '总执行时间(秒)',
ADD COLUMN context_relevance FLOAT COMMENT '上下文相关性评分',
ADD COLUMN llm_summary TEXT COMMENT 'LLM生成的本轮次总结';

-- 为 tool_executions 表添加新字段
ALTER TABLE tool_executions
ADD COLUMN status VARCHAR(20) DEFAULT 'success' COMMENT '执行状态',
ADD COLUMN error_message TEXT COMMENT '错误信息',
ADD COLUMN step_order INT COMMENT '步骤顺序',
ADD COLUMN reasoning TEXT COMMENT '执行推理过程';
```

### 2. API接口更新

#### 2.1 获取会话分析记录
```python
@router.get("/{conversation_id}/analyses")
async def get_conversation_analyses(
    conversation_id: str,
    include_full_state: bool = Query(True, description="是否包含完整的分析状态"),
    db: Session = Depends(get_db)
):
    """获取会话的所有分析记录（包含完整状态用于还原）"""
    if include_full_state:
        conversation_service = ConversationService(db)
        analyses_with_state = conversation_service.get_conversation_analyses(conversation_id)
        return {"code": 0, "data": analyses_with_state}
```

#### 2.2 多轮分析流式接口
```python
@router.post("/{conversation_id}/rounds/stream")
async def execute_conversation_round_stream(
    conversation_id: str,
    request: ConversationRoundRequest,
    db: Session = Depends(get_db)
):
    """在会话中执行新轮次的流式分析"""
    analyzer = MultiRoundLLMAnalyzer(db, conversation_service)
    
    return StreamingResponse(
        analyzer.generate_conversation_stream(
            conversation_id=conversation_id,
            query=request.query,
            round_number=request.round_number,
            project_id=conversation.project_id,
            use_full_context=request.use_full_context,
            context_depth=request.context_depth
        ),
        media_type="text/event-stream"
    )
```

### 3. 前端实现

#### 3.1 状态管理
```typescript
interface AnalysisRound {
  roundId: string;
  query: string;
  analysisId: string;
  analysisSteps: any[];           // 完整的分析步骤
  toolResults: any[];             // 工具执行记录
  finalReport: string;            // 最终报告
  status: 'completed' | 'running' | 'failed';
  timestamp: string;
  intentAnalysis?: any;           // 意图分析
  mcpResults?: Record<string, any>; // MCP结果
  contextRelevance?: number;      // 上下文相关性
  contextSummary?: string;        // LLM总结
}
```

#### 3.2 数据还原
```typescript
// 加载会话时还原完整状态
const loadConversation = async (convId: string) => {
  const analysesResponse = await conversationApi.getConversationAnalyses(convId);
  const analyses = analysesResponse.data;
  
  const rounds: AnalysisRound[] = analyses.map((analysis: any) => ({
    // 还原所有字段...
    analysisSteps: analysis.analysis_steps || [],
    toolResults: analysis.tool_executions || [],
    finalReport: analysis.final_report || '',
    // ...
  }));
  
  setConversationState({
    conversationId: convId,
    rounds: rounds,
    // ...
  });
};
```

## 使用效果

### 1. 完整数据还原
- ✅ 切换会话时完整还原所有轮次的分析内容
- ✅ 每个工具执行步骤都能正确显示
- ✅ 分析报告完整保留格式和内容
- ✅ 时间线显示准确的执行状态

### 2. 智能上下文感知
- ✅ LLM自动总结每轮次的关键发现
- ✅ 后续轮次基于历史总结进行分析
- ✅ 避免重复分析，提高效率
- ✅ 上下文相关性评分指导分析策略

### 3. 用户体验提升
- ✅ 无缝的会话切换体验
- ✅ 完整的分析历史回顾
- ✅ 智能化的多轮对话
- ✅ 高效的数据分析流程

## 部署说明

1. **执行数据库迁移**：运行 `add_analysis_state_fields.sql`
2. **更新后端代码**：部署新的服务代码
3. **更新前端代码**：部署新的前端代码
4. **配置LLM**：确保OpenAI API配置正确
5. **测试验证**：验证多轮分析和数据还原功能

## 注意事项

1. **数据量控制**：JSON字段可能较大，注意数据库性能
2. **LLM调用**：总结生成是异步的，不影响主流程
3. **向后兼容**：新字段都是可选的，兼容现有数据
4. **错误处理**：完善的异常处理，确保系统稳定性

这个方案完全解决了多轮分析的数据持久化和还原问题，同时通过LLM总结提升了上下文感知能力，为用户提供了完整、智能的多轮分析体验。 