# 多轮分析功能改造方案（优化版）

## 1. 概述

基于现有的LLM流式分析接口，设计多轮分析功能，支持用户在分析完成后继续提问，形成连续的对话式分析体验。通过优化上下文传递机制和API接口设计，确保多轮对话的连贯性和智能性。

## 2. 核心问题分析

### 2.1 当前架构问题
- **上下文传递不完善**：前端只传递文本摘要，后端未真正利用会话上下文
- **API接口设计缺陷**：现有接口不支持真正的多轮对话上下文
- **状态管理复杂**：前端状态管理复杂，容易出现不一致
- **数据持久化缺失**：缺乏会话和轮次的持久化机制

### 2.2 优化目标
- **智能上下文理解**：LLM能够理解和利用之前轮次的完整分析结果
- **无缝对话体验**：用户感受不到轮次切换，如同与AI进行连续对话
- **状态一致性**：确保前后端状态同步，避免数据丢失
- **可扩展性**：支持未来更复杂的多轮分析场景

## 3. 技术架构设计（优化版）

### 3.1 前端架构调整

#### 3.1.1 状态管理优化
```typescript
// 优化后的会话状态管理
interface ConversationState {
  conversationId: string;
  rounds: AnalysisRound[];
  currentRound: number;
  isMultiRound: boolean;
  projectId: string;
  title: string;
  // 新增：会话级别的上下文信息
  contextSummary?: string;
  totalTokensUsed?: number;
  createdAt: string;
  updatedAt: string;
}

interface AnalysisRound {
  roundId: string;
  query: string;
  analysisId: string;
  analysisSteps: any[];
  toolResults: any[];
  finalReport: string;
  status: 'completed' | 'running' | 'failed';
  timestamp: string;
  intentAnalysis?: any;
  mcpResults?: Record<string, any>;
  // 新增：轮次级别的上下文信息
  contextUsed?: ConversationContext;
  tokensUsed?: number;
  executionTime?: number;
}

// 新增：结构化的上下文信息
interface ConversationContext {
  previousQueries: string[];
  previousResults: AnalysisResult[];
  keyFindings: string[];
  dataSourcesUsed: string[];
  toolsUsed: string[];
  userPreferences?: Record<string, any>;
}

interface AnalysisResult {
  roundNumber: number;
  query: string;
  summary: string;
  keyData: any[];
  insights: string[];
  timestamp: string;
}
```

#### 3.1.2 API服务优化
```typescript
// 优化后的API服务
export const conversationApi = {
  // 创建会话
  createConversation: (data: { 
    project_id: string; 
    initial_query: string;
    title?: string;
  }) => request.post('/api/v1/conversations', data),
  
  // 获取会话详情（包含完整上下文）
  getConversation: (conversationId: string) =>
    request.get(`/api/v1/conversations/${conversationId}`),
  
  // 获取会话的结构化上下文
  getConversationContext: (conversationId: string) =>
    request.get(`/api/v1/conversations/${conversationId}/context`),
  
  // 更新会话上下文
  updateConversationContext: (conversationId: string, context: ConversationContext) =>
    request.put(`/api/v1/conversations/${conversationId}/context`, context),
  
  // 获取会话分析列表
  getConversationAnalyses: (conversationId: string) =>
    request.get(`/api/v1/conversations/${conversationId}/analyses`),
};

// 优化后的多轮分析API
export const multiRoundAnalysisApi = {
  // 在会话中执行新轮次分析
  executeRoundAnalysis: (
    conversationId: string,
    roundData: {
      query: string;
      round_number: number;
      use_full_context?: boolean;
      context_depth?: number;
    },
    callbacks: StreamCallbacks
  ) => {
    const url = `/api/v1/conversations/${conversationId}/rounds/stream`;
    return createEventSource(url, roundData, callbacks);
  },
  
  // 获取轮次的详细上下文
  getRoundContext: (conversationId: string, roundNumber: number) =>
    request.get(`/api/v1/conversations/${conversationId}/rounds/${roundNumber}/context`),
};
```

### 3.2 后端架构优化

#### 3.2.1 数据模型扩展
```sql
-- 会话表（优化）
CREATE TABLE conversations (
    id VARCHAR(50) PRIMARY KEY,
    project_id VARCHAR(50) NOT NULL,
    title VARCHAR(500),
    context_summary TEXT,  -- 会话级别的上下文摘要
    total_rounds INT DEFAULT 0,
    total_tokens_used INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('active', 'archived', 'paused') DEFAULT 'active',
    INDEX idx_project_id (project_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- 会话上下文表（新增）
CREATE TABLE conversation_contexts (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    conversation_id VARCHAR(50) NOT NULL,
    round_number INT NOT NULL,
    context_type ENUM('query', 'result', 'insight', 'data_source', 'tool_usage') NOT NULL,
    context_data JSON NOT NULL,
    relevance_score FLOAT DEFAULT 1.0,  -- 上下文相关性评分
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE,
    INDEX idx_conversation_round (conversation_id, round_number),
    INDEX idx_context_type (context_type),
    INDEX idx_relevance_score (relevance_score)
);

-- 分析记录关联会话（优化）
ALTER TABLE analysis 
ADD COLUMN conversation_id VARCHAR(50),
ADD COLUMN round_number INT DEFAULT 1,
ADD COLUMN context_tokens_used INT DEFAULT 0,
ADD COLUMN context_summary TEXT,
ADD INDEX idx_conversation_id (conversation_id),
ADD INDEX idx_round_number (round_number);
```

#### 3.2.2 会话管理服务优化
```python
# app/services/conversation_service.py（优化版）
class ConversationService:
    def __init__(self, db: Session):
        self.db = db
    
    async def create_conversation(
        self, 
        project_id: str, 
        initial_query: str,
        title: str = None
    ) -> ConversationModel:
        """创建新会话"""
        conversation = ConversationModel(
            id=self._generate_conversation_id(),
            project_id=project_id,
            title=title or self._generate_title(initial_query),
            status='active'
        )
        self.db.add(conversation)
        self.db.commit()
        
        # 初始化上下文
        await self._initialize_conversation_context(conversation.id, initial_query)
        
        return conversation
    
    async def get_conversation_context(
        self, 
        conversation_id: str,
        max_rounds: int = 10,
        context_types: List[str] = None
    ) -> ConversationContext:
        """获取会话的结构化上下文"""
        
        # 查询上下文记录
        query = self.db.query(ConversationContextModel).filter(
            ConversationContextModel.conversation_id == conversation_id
        )
        
        if context_types:
            query = query.filter(ConversationContextModel.context_type.in_(context_types))
        
        # 按相关性评分和轮次排序，取最相关的上下文
        contexts = query.order_by(
            ConversationContextModel.relevance_score.desc(),
            ConversationContextModel.round_number.desc()
        ).limit(max_rounds * 5).all()  # 每轮最多5个上下文
        
        # 构建结构化上下文
        return self._build_structured_context(contexts)
    
    async def add_round_context(
        self,
        conversation_id: str,
        round_number: int,
        analysis_result: AnalysisModel,
        execution_results: List[Dict]
    ):
        """添加轮次上下文"""
        
        # 提取查询上下文
        await self._add_context_record(
            conversation_id, round_number, 'query',
            {"query": analysis_result.query, "intent": "分析请求"}
        )
        
        # 提取结果上下文
        if analysis_result.result:
            key_insights = self._extract_key_insights(analysis_result.result)
            await self._add_context_record(
                conversation_id, round_number, 'result',
                {"summary": analysis_result.result[:500], "insights": key_insights}
            )
        
        # 提取工具使用上下文
        tools_used = self._extract_tools_used(execution_results)
        if tools_used:
            await self._add_context_record(
                conversation_id, round_number, 'tool_usage',
                {"tools": tools_used, "data_sources": self._extract_data_sources(execution_results)}
            )
        
        # 更新会话统计
        await self._update_conversation_stats(conversation_id)
    
    def _build_structured_context(self, contexts: List[ConversationContextModel]) -> ConversationContext:
        """构建结构化上下文"""
        context = ConversationContext(
            previousQueries=[],
            previousResults=[],
            keyFindings=[],
            dataSourcesUsed=[],
            toolsUsed=[],
            userPreferences={}
        )
        
        for ctx in contexts:
            if ctx.context_type == 'query':
                context.previousQueries.append(ctx.context_data.get('query', ''))
            elif ctx.context_type == 'result':
                context.previousResults.append(AnalysisResult(
                    roundNumber=ctx.round_number,
                    query=ctx.context_data.get('query', ''),
                    summary=ctx.context_data.get('summary', ''),
                    keyData=ctx.context_data.get('key_data', []),
                    insights=ctx.context_data.get('insights', []),
                    timestamp=ctx.created_at.isoformat()
                ))
            elif ctx.context_type == 'insight':
                context.keyFindings.extend(ctx.context_data.get('insights', []))
            elif ctx.context_type == 'tool_usage':
                context.toolsUsed.extend(ctx.context_data.get('tools', []))
                context.dataSourcesUsed.extend(ctx.context_data.get('data_sources', []))
        
        return context
```

#### 3.2.3 LLM分析器增强（重点优化）
```python
# app/services/llm_analyzer.py（多轮分析优化）
class MultiRoundLLMAnalyzer(LLMStreamAnalyzer):
    """多轮分析专用的LLM分析器"""
    
    def __init__(self, db: Session, conversation_service: ConversationService):
        super().__init__(db)
        self.conversation_service = conversation_service
    
    async def generate_conversation_stream(
        self, 
        conversation_id: str,
        query: str, 
        round_number: int,
        project_id: str, 
        max_planning_rounds: int = 25,
        use_full_context: bool = True,
        context_depth: int = 5
    ) -> AsyncGenerator[str, None]:
        """生成多轮对话的流式分析响应"""
        
        # 1. 获取会话上下文
        conversation_context = None
        if round_number > 1 and use_full_context:
            conversation_context = await self.conversation_service.get_conversation_context(
                conversation_id, max_rounds=context_depth
            )
        
        # 2. 构建增强查询
        enhanced_query = await self._build_context_aware_query(
            query, conversation_context, round_number
        )
        
        # 3. 发送上下文加载事件
        if conversation_context:
            yield self._format_event("context_loaded", {
                "conversation_id": conversation_id,
                "round_number": round_number,
                "context_summary": self._summarize_context(conversation_context),
                "previous_rounds": len(conversation_context.previousResults),
                "key_findings_count": len(conversation_context.keyFindings)
            })
        
        # 4. 执行分析（使用增强查询）
        analysis = None
        try:
        async for event in self.generate_stream(
                query=enhanced_query,
                project_id=project_id,
                max_planning_rounds=max_planning_rounds,
                conversation_id=conversation_id
            ):
                # 拦截分析创建事件，获取分析ID
                if '"event":"analysis_created"' in event:
                    import json
                    event_data = json.loads(event.split('data: ')[1])
                    analysis_id = event_data.get('data', {}).get('id')
                    if analysis_id:
                        analysis = self.db.query(AnalysisModel).filter(
                            AnalysisModel.id == analysis_id
                        ).first()
                        if analysis:
                            # 更新分析记录的会话信息
                            analysis.conversation_id = conversation_id
                            analysis.round_number = round_number
                            analysis.context_summary = self._summarize_context(conversation_context) if conversation_context else None
                            self.db.commit()
                
            yield event
    
            # 5. 分析完成后，添加轮次上下文
            if analysis:
                # 获取执行结果
                execution_results = await self._get_execution_results(analysis.id)
        
                # 添加到会话上下文
                await self.conversation_service.add_round_context(
                    conversation_id, round_number, analysis, execution_results
                )
        
                # 发送上下文更新事件
                yield self._format_event("context_updated", {
                    "conversation_id": conversation_id,
                    "round_number": round_number,
                    "context_added": True
                })
        
        except Exception as e:
            log.error(f"多轮分析出错: {str(e)}")
            yield self._format_event("error", {
                "message": f"多轮分析出错: {str(e)}",
            "conversation_id": conversation_id,
                "round_number": round_number
            })
    
    async def _build_context_aware_query(
        self, 
        current_query: str,
        context: ConversationContext,
        round_number: int
    ) -> str:
        """构建上下文感知的查询"""
        
        if not context or round_number == 1:
            return current_query
        
        # 构建上下文提示
        context_prompt = f"""
这是一个多轮数据分析对话的第 {round_number} 轮。

## 对话历史上下文

### 之前的查询：
{chr(10).join([f"第{i+1}轮: {q}" for i, q in enumerate(context.previousQueries[-3:])])}

### 关键发现：
{chr(10).join([f"- {finding}" for finding in context.keyFindings[-5:]])}

### 已使用的数据源：
{', '.join(list(set(context.dataSourcesUsed)))}

### 已使用的分析工具：
{', '.join(list(set(context.toolsUsed)))}

## 当前用户问题：
{current_query}

## 分析指导：
请基于以上对话历史和当前问题，进行智能分析。如果当前问题与之前的分析相关，请：
1. 充分利用之前的分析结果和发现
2. 避免重复执行相同的分析步骤
3. 在回答中体现对话的连贯性
4. 如果需要补充分析，请说明与之前分析的关联性

请开始分析。
"""
        return context_prompt
    
    def _summarize_context(self, context: ConversationContext) -> str:
        """总结上下文信息"""
        if not context:
            return ""
        
        summary_parts = []
        
        if context.previousQueries:
            summary_parts.append(f"已进行{len(context.previousQueries)}轮查询")
        
        if context.keyFindings:
            summary_parts.append(f"发现{len(context.keyFindings)}个关键洞察")
        
        if context.dataSourcesUsed:
            summary_parts.append(f"使用了{len(set(context.dataSourcesUsed))}个数据源")
        
        if context.toolsUsed:
            summary_parts.append(f"调用了{len(set(context.toolsUsed))}种分析工具")
        
        return "；".join(summary_parts)
```

### 3.3 API接口优化

#### 3.3.1 新增多轮分析专用接口
```python
# app/api/v1/endpoints/conversation.py（新增）
@router.post("/{conversation_id}/rounds/stream")
async def execute_conversation_round_stream(
    conversation_id: str,
    request: ConversationRoundRequest,
    db: Session = Depends(get_db)
):
    """在会话中执行新轮次的流式分析"""
    
    # 验证会话存在
    conversation = db.query(ConversationModel).filter(
        ConversationModel.id == conversation_id
    ).first()
    
    if not conversation:
        raise HTTPException(status_code=404, detail="会话不存在")
    
    # 创建多轮分析器
    conversation_service = ConversationService(db)
    analyzer = MultiRoundLLMAnalyzer(db, conversation_service)
    
    # 返回流式响应
    return StreamingResponse(
        analyzer.generate_conversation_stream(
            conversation_id=conversation_id,
            query=request.query,
            round_number=request.round_number,
            project_id=conversation.project_id,
            max_planning_rounds=request.max_planning_rounds,
            use_full_context=request.use_full_context,
            context_depth=request.context_depth
        ),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no"
        }
    )

@router.get("/{conversation_id}/context")
async def get_conversation_context(
    conversation_id: str,
    max_rounds: int = Query(10, ge=1, le=50),
    context_types: List[str] = Query(None),
    db: Session = Depends(get_db)
):
    """获取会话的结构化上下文"""
    
    conversation_service = ConversationService(db)
    context = await conversation_service.get_conversation_context(
        conversation_id, max_rounds, context_types
    )
    
    return {
        "code": 0,
        "data": context,
        "message": "获取会话上下文成功"
    }
```

## 4. 前端实现优化

### 4.1 状态管理优化
```typescript
// 优化后的前端实现
const MultiRoundAnalysis: React.FC = () => {
  // ... 现有状态 ...
  
  // 新增：上下文管理状态
  const [conversationContext, setConversationContext] = useState<ConversationContext | null>(null);
  const [contextLoading, setContextLoading] = useState(false);
  
  // 优化后的新轮次分析
  const startNewRoundInConversation = async () => {
    if (!conversationState) return;
    
    try {
      const formValues = await form.validateFields();
      
      // 1. 加载会话上下文
      setContextLoading(true);
      try {
        const contextResponse = await conversationApi.getConversationContext(
          conversationState.conversationId
        );
        setConversationContext(contextResponse.data);
      } catch (error) {
        console.warn('加载会话上下文失败:', error);
      } finally {
        setContextLoading(false);
      }
      
      // 2. 创建新轮次
      const newRound: AnalysisRound = {
        roundId: generateRoundId(),
        query: formValues.query,
        analysisId: '',
        analysisSteps: [],
        toolResults: [],
        finalReport: '',
        status: 'running',
        timestamp: new Date().toISOString(),
        mcpResults: {},
        contextUsed: conversationContext
      };
      
      // 3. 更新会话状态
      const updatedConversation = {
        ...conversationState,
        rounds: [...conversationState.rounds, newRound],
        currentRound: conversationState.rounds.length,
        updatedAt: new Date().toISOString()
      };
      setConversationState(updatedConversation);
      
      // 4. 重置当前分析状态
      resetCurrentAnalysisState();

      // 5. 使用多轮分析API
      eventSourceRef.current = multiRoundAnalysisApi.executeRoundAnalysis(
        conversationState.conversationId,
        {
          query: formValues.query,
          round_number: conversationState.rounds.length + 1,
          use_full_context: true,
          context_depth: 5
        },
        {
          onStart: () => {
            setAnalysisSteps([{
              id: 'start',
              name: `第 ${conversationState.rounds.length + 1} 轮分析开始`,
              status: 'process',
              time: new Date().toISOString()
            }]);
          },
          onEvent: (eventType: string, eventData: any) => {
            // 处理新的事件类型
            if (eventType === 'context_loaded') {
              setAnalysisSteps(prev => [...prev, {
                id: 'context_loaded',
                name: `已加载对话上下文：${eventData.context_summary}`,
                status: 'finish',
                time: new Date().toISOString()
              }]);
            } else if (eventType === 'context_updated') {
              // 上下文更新完成
              console.log('会话上下文已更新');
            } else {
              handleLlmStreamEvent(eventType, eventData);
            }
          },
          onError: (error: any) => {
            setError('多轮分析出错: ' + error);
            updateCurrentRoundStatus('failed');
          },
          onComplete: (id: string) => {
            setAnalysisId(id);
            setStreaming(false);
            setStreamComplete(true);
            updateCurrentRoundStatus('completed');
            message.success('分析完成');
  }
        }
      );
      
      form.resetFields();
      
    } catch (error: any) {
      console.error('多轮分析执行出错:', error);
      message.error('分析执行失败: ' + (error.message || '未知错误'));
      updateCurrentRoundStatus('failed');
  }
  };
  
  // 重置当前分析状态
  const resetCurrentAnalysisState = () => {
    setStreaming(true);
    setError(null);
    setAnalysisSteps([]);
    setCurrentStep('');
    setIntentAnalysis(null);
    setToolResults([]);
    setFinalReport('');
    setClarificationRequest(null);
    setShowClarificationDialog(false);
    setClarificationAnswers({});
    setClarificationHistory([]);
    setAnalysisId('');
    setStreamComplete(false);
    setMcpResults({});
    setShowContinueInput(false);
  };
  
  // ... 其他方法 ...
};
```

## 5. 实施计划（优化版）

### 5.1 第一阶段：核心架构优化（2-3周）
1. **后端优化**：
   - 实现 `ConversationService` 和上下文管理
   - 优化 `MultiRoundLLMAnalyzer`
   - 新增多轮分析专用API接口
   - 数据库表结构调整

2. **前端优化**：
   - 优化状态管理和API调用
   - 实现上下文感知的分析流程
   - 优化用户界面和交互体验

### 5.2 第二阶段：功能完善（1-2周）
1. **上下文优化**：
   - 实现智能上下文相关性评分
   - 优化上下文摘要算法
   - 添加用户偏好学习

2. **性能优化**：
   - 实现上下文缓存机制
   - 优化数据库查询性能
   - 添加分析结果缓存

### 5.3 第三阶段：测试和部署（1周）
1. **测试验证**：
   - 多轮对话场景测试
   - 上下文连贯性测试
   - 性能压力测试

2. **部署上线**：
   - 灰度发布
   - 监控和日志完善
   - 用户反馈收集

## 6. 预期效果

### 6.1 用户体验提升
- **智能对话**：AI能够理解和记住之前的分析内容
- **无缝衔接**：后续问题能够基于前面的结果进行深入分析
- **高效分析**：避免重复分析，提高分析效率

### 6.2 技术架构优化
- **可扩展性**：支持更复杂的多轮分析场景
- **可维护性**：清晰的架构设计，易于维护和扩展
- **性能优化**：通过上下文缓存和智能查询优化性能

这个优化方案解决了当前架构的核心问题，通过结构化的上下文管理和专用的多轮分析接口，实现了真正意义上的智能多轮对话分析功能。