# 前端多轮分析优化补丁

## 需要在 MultiRoundAnalysis.tsx 中进行的关键修改

### 1. 添加上下文相关性状态

在组件状态中添加：

```typescript
// 在现有状态后添加
const [contextRelevance, setContextRelevance] = useState<number>(0);
```

### 2. 修改 handleLlmStreamEvent 函数

在 `handleLlmStreamEvent` 函数中的 `case 'context_loaded':` 部分添加：

```typescript
case 'context_loaded':
  const contextRelevance = eventData.context_relevance || 0;
  setContextRelevance(contextRelevance);
  setAnalysisSteps(prev => [...prev.map(s => ({...s, status: 'finish'})), {
    id: 'context_loaded',
    name: `已加载对话上下文：${eventData.context_summary || ''}`,
    status: 'finish',
    time: new Date().toISOString(),
    contextRelevance: contextRelevance,
    description: contextRelevance > 0.7 ? '高度相关的历史分析' : 
                 contextRelevance > 0.3 ? '部分相关的历史分析' : '新的分析方向'
  }]);
  break;
```

### 3. 添加上下文相关性指示器组件

在组件内部添加：

```typescript
// 渲染上下文相关性指示器
const renderContextRelevanceIndicator = (relevance: number) => {
  if (relevance === 0) return null;
  
  let color = '#52c41a'; // 绿色 - 高相关性
  let text = '高度相关';
  
  if (relevance < 0.3) {
    color = '#faad14'; // 橙色 - 低相关性
    text = '新话题';
  } else if (relevance < 0.7) {
    color = '#1890ff'; // 蓝色 - 中等相关性
    text = '部分相关';
  }
  
  return (
    <Tooltip title={`与历史对话的相关性: ${(relevance * 100).toFixed(1)}%`}>
      <Badge 
        color={color} 
        text={text}
        style={{ fontSize: '12px' }}
      />
    </Tooltip>
  );
};
```

### 4. 在时间线中显示相关性

在 `renderCurrentAnalysis` 函数的时间线渲染部分，为 `context_loaded` 步骤添加相关性显示：

```typescript
// 在 Timeline.Item 的内容中添加
{step.id === 'context_loaded' && step.contextRelevance !== undefined && (
  <div style={{ marginTop: 8 }}>
    {renderContextRelevanceIndicator(step.contextRelevance)}
  </div>
)}
```

### 5. 在用户问题气泡中显示相关性

在用户问题气泡的底部信息中添加相关性显示：

```typescript
// 在用户问题气泡的底部信息中
<div style={{ 
  fontSize: '11px', 
  opacity: 0.8, 
  marginTop: 4,
  textAlign: 'right',
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center'
}}>
  <span>第 {roundIndex + 1} 轮提问</span>
  {/* 显示上下文相关性 */}
  {roundSteps.find(s => s.contextRelevance !== undefined) && (
    <div style={{ marginLeft: 8 }}>
      {renderContextRelevanceIndicator(
        roundSteps.find(s => s.contextRelevance !== undefined)?.contextRelevance || 0
      )}
    </div>
  )}
</div>
```

### 6. 优化多轮分析API调用

在 `startNewRoundInConversation` 函数的事件处理中添加：

```typescript
onEvent: (eventType: string, eventData: any) => {
  // 处理新的事件类型
  if (eventType === 'context_loaded') {
    setContextRelevance(eventData.context_relevance || 0);
    setAnalysisSteps(prev => [...prev.map(s => ({...s, status: 'finish'})), {
      id: 'context_loaded',
      name: `已加载对话上下文：${eventData.context_summary || ''}`,
      status: 'finish',
      time: new Date().toISOString(),
      contextRelevance: eventData.context_relevance
    }]);
  } else if (eventType === 'context_updated') {
    // 上下文更新完成
    console.log('会话上下文已更新');
  } else {
    handleLlmStreamEvent(eventType, eventData);
  }
}
```

## 实施说明

1. **逐步实施**：建议先实施状态添加和指示器组件，然后再添加显示逻辑
2. **测试验证**：每个修改后都要测试多轮对话功能是否正常
3. **样式调整**：根据实际UI效果调整颜色和布局

## 预期效果

实施这些修改后，用户将能够：

1. **看到上下文相关性**：每轮对话都会显示与历史对话的相关程度
2. **理解AI的思考过程**：通过相关性指示器了解AI如何利用历史信息
3. **更好的对话体验**：知道何时开始新话题，何时深入现有话题

## 注意事项

- 确保所有新添加的状态都有适当的初始化
- 在组件卸载时清理相关状态
- 考虑添加错误处理，防止相关性数据异常时影响正常功能 