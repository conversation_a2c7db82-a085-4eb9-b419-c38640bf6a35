# 多轮分析系统完整指南

## 📋 目录

1. [系统概述](#系统概述)
2. [核心架构](#核心架构)
3. [技术实现](#技术实现)
4. [API接口](#api接口)
5. [前端实现](#前端实现)
6. [数据持久化](#数据持久化)
7. [上下文感知](#上下文感知)
8. [部署配置](#部署配置)
9. [故障排查](#故障排查)
10. [性能优化](#性能优化)

---

## 系统概述

多轮分析系统是基于LLM的智能数据分析对话平台，支持用户与AI进行连续的、上下文感知的数据分析对话。系统能够记住历史分析内容，智能关联前后轮次，避免重复分析，提供连贯的分析体验。

### 核心特性

- **🧠 智能上下文感知**：AI能理解和利用历史分析结果
- **🔄 无缝对话体验**：支持连续多轮问答，如同与专家对话
- **💾 完整数据持久化**：所有分析步骤和结果完整保存和还原
- **📊 可视化分析结果**：支持图表、表格等多种数据展示
- **⚡ 流式响应**：实时显示分析进度和结果
- **🎯 相关性评估**：智能评估问题间的关联程度

---

## 核心架构

### 系统架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   后端API       │    │   数据存储      │
│                 │    │                 │    │                 │
│ MultiRoundUI    │◄──►│ ConversationAPI │◄──►│ PostgreSQL      │
│ - 会话管理      │    │ - 多轮分析      │    │ - 会话数据      │
│ - 实时显示      │    │ - 上下文管理    │    │ - 分析记录      │
│ - 状态管理      │    │ - 流式响应      │    │ - 工具执行      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   LLM服务       │
                       │                 │
                       │ - OpenAI API    │
                       │ - 智能分析      │
                       │ - 上下文理解    │
                       └─────────────────┘
```

### 核心组件

#### 后端组件

1. **MultiRoundLLMAnalyzer** (`app/services/multi_round_analyzer.py`)
   - 多轮分析的核心引擎
   - 负责上下文感知和智能查询增强
   - 管理分析流程和状态

2. **ConversationService** (`app/services/conversation_service.py`)
   - 会话和上下文管理
   - 数据持久化和还原
   - 智能摘要生成

3. **ConversationAPI** (`app/api/v1/endpoints/conversations.py`)
   - RESTful API接口
   - 流式响应处理
   - 会话CRUD操作

#### 前端组件

1. **MultiRoundAnalysis** (`frontend/src/pages/analysis/MultiRoundAnalysis.tsx`)
   - 主要的多轮分析界面
   - 状态管理和事件处理
   - 实时UI更新

2. **ConversationSidebar**
   - 会话列表和切换
   - 历史会话管理

---

## 技术实现

### 后端核心实现

#### 1. 多轮分析器 (MultiRoundLLMAnalyzer)

```python
class MultiRoundLLMAnalyzer(LLMStreamAnalyzer):
    """多轮分析专用的LLM分析器"""
    
    async def generate_conversation_stream(
        self, 
        conversation_id: str,
        query: str, 
        round_number: int,
        project_id: str, 
        use_full_context: bool = True,
        context_depth: int = 5
    ) -> AsyncGenerator[str, None]:
        """生成多轮对话的流式分析响应"""
        
        # 1. 获取会话上下文
        if round_number > 1 and use_full_context:
            llm_context = await self.conversation_service.get_conversation_context_with_llm_summary(
                conversation_id, max_rounds=context_depth
            )
            
        # 2. 构建增强查询
        enhanced_query = f"{llm_context}\n\n## 当前用户问题\n{query}"
        
        # 3. 执行分析并保存状态
        async for event in self.generate_stream(enhanced_query, project_id):
            yield event
```

#### 2. 上下文感知机制

**相关性计算**：
```python
def _calculate_context_relevance(self, current_query: str, context: ConversationContextData) -> float:
    """计算当前查询与历史上下文的相关性"""
    relevance_score = 0.0
    
    # 查询相似性 (40%)
    query_similarity = self._calculate_query_similarity(current_query, context.previous_queries)
    relevance_score += query_similarity * 0.4
    
    # 实体重叠度 (30%)
    entity_overlap = self._calculate_entity_overlap(current_query, context)
    relevance_score += entity_overlap * 0.3
    
    # 主题一致性 (30%)
    topic_consistency = self._calculate_topic_consistency(current_query, context)
    relevance_score += topic_consistency * 0.3
    
    return relevance_score
```

**动态上下文策略**：
- **高相关性(≥0.7)**：详细历史上下文 + 具体数据点
- **中等相关性(0.4-0.7)**：核心洞察 + 关键发现
- **低相关性(<0.4)**：基础背景信息

#### 3. LLM智能总结

```python
async def generate_round_summary(self, analysis_id: str) -> Optional[str]:
    """使用LLM生成轮次总结"""
    analysis = self.db.query(Analysis).filter(Analysis.id == analysis_id).first()
    
    prompt = f"""
请为以下数据分析结果生成简洁的总结：

查询：{analysis.query}
结果：{analysis.result[:1000]}

要求：
1. 提取3-5个关键发现
2. 总结主要数据洞察
3. 控制在200字以内
4. 使用结构化格式
"""
    
    completion = await self.llm_client.chat.completions.create(
        model=settings.OPENAI_MODEL,
        messages=[{"role": "user", "content": prompt}],
        max_tokens=300,
        temperature=0.3
    )
    
    return completion.choices[0].message.content
```

### 前端核心实现

#### 1. 状态管理

```typescript
interface ConversationState {
  conversationId: string;
  rounds: AnalysisRound[];
  currentRound: number;
  isMultiRound: boolean;
  projectId: string;
  title: string;
}

interface AnalysisRound {
  roundId: string;
  query: string;
  analysisId: string;
  analysisSteps: any[];
  toolResults: any[];
  finalReport: string;
  status: 'completed' | 'running' | 'failed';
  timestamp: string;
  contextRelevance?: number;
  contextSummary?: string;
}
```

#### 2. 上下文相关性显示

```typescript
const ContextRelevanceIndicator: React.FC<{
  relevance: number;
  summary?: string;
}> = ({ relevance, summary }) => {
  let color = '#52c41a'; // 绿色 - 高相关性
  let text = '高度相关';
  let icon = '🔗';
  
  if (relevance < 0.3) {
    color = '#faad14'; // 橙色 - 低相关性
    text = '新话题';
    icon = '🆕';
  } else if (relevance < 0.7) {
    color = '#1890ff'; // 蓝色 - 中等相关性
    text = '部分相关';
    icon = '🔄';
  }
  
  return (
    <Tooltip title={`与历史对话的相关性: ${(relevance * 100).toFixed(1)}%`}>
      <Tag color={color}>
        {icon} {text}
      </Tag>
    </Tooltip>
  );
};
```

#### 3. 数据还原机制

```typescript
const loadConversation = async (convId: string) => {
  // 加载会话详情
  const conversationResponse = await conversationApi.getConversation(convId);
  
  // 加载完整分析状态
  const analysesResponse = await conversationApi.getConversationAnalyses(convId);
  
  // 重建轮次数据
  const rounds: AnalysisRound[] = analyses.map((analysis: any) => ({
    roundId: `round_${analysis.analysis_id}`,
    query: analysis.query,
    analysisSteps: analysis.analysis_steps || [],
    toolResults: analysis.tool_executions || [],
    finalReport: analysis.final_report || '',
    mcpResults: analysis.mcp_results || {},
    contextRelevance: analysis.context_relevance,
    contextSummary: analysis.llm_summary
  }));
  
  setConversationState({
    conversationId: convId,
    rounds: rounds,
    currentRound: rounds.length - 1
  });
};
```

---

## API接口

### 1. 创建会话

```http
POST /api/v1/conversations
Content-Type: application/json

{
  "project_id": "project_123",
  "initial_query": "分析ABC公司的基本信息"
}
```

### 2. 执行多轮分析 (流式)

```http
POST /api/v1/conversations/{conversation_id}/rounds/stream
Content-Type: application/json

{
  "query": "查看ABC公司的资金流转情况",
  "round_number": 2,
  "max_planning_rounds": 25,
  "use_full_context": true,
  "context_depth": 5
}
```

**流式响应事件**：
- `context_loaded`: 上下文加载完成
- `analysis_created`: 分析记录创建
- `step_started`: 工具执行开始
- `step_completed`: 工具执行完成
- `report_generated`: 分析报告生成
- `context_updated`: 上下文更新完成

### 3. 获取会话分析记录

```http
GET /api/v1/conversations/{conversation_id}/analyses?include_full_state=true
```

### 4. 获取会话上下文

```http
GET /api/v1/conversations/{conversation_id}/context?max_rounds=5
```

---

## 数据持久化

### 数据库表结构

#### 1. conversations 表
```sql
CREATE TABLE conversations (
    id VARCHAR(255) PRIMARY KEY,
    project_id VARCHAR(255) NOT NULL,
    title VARCHAR(500),
    context_summary TEXT,
    total_rounds INTEGER DEFAULT 0,
    total_tokens_used INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 2. analyses 表 (扩展字段)
```sql
ALTER TABLE analyses ADD COLUMN (
    conversation_id VARCHAR(255),
    round_number INTEGER,
    analysis_steps JSON,
    tool_results JSON,
    mcp_results JSON,
    status VARCHAR(20) DEFAULT 'running',
    execution_time FLOAT,
    context_relevance FLOAT,
    llm_summary TEXT
);
```

#### 3. conversation_contexts 表
```sql
CREATE TABLE conversation_contexts (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    conversation_id VARCHAR(255) NOT NULL,
    round_number INTEGER NOT NULL,
    context_type ENUM('query', 'result', 'insight', 'tool_usage') NOT NULL,
    context_data JSON NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_conversation_round (conversation_id, round_number)
);
```

### 数据保存流程

1. **分析开始**：创建Analysis记录，状态为'running'
2. **步骤执行**：实时保存analysis_steps和tool_results
3. **报告生成**：保存final_report，状态改为'completed'
4. **上下文提取**：异步生成LLM总结并保存上下文记录
5. **会话更新**：更新conversation的统计信息

---

## 上下文感知

### 上下文数据结构

```python
@dataclass
class ConversationContextData:
    previous_queries: List[str] = field(default_factory=list)
    previous_results: List[AnalysisResult] = field(default_factory=list)
    key_findings: List[str] = field(default_factory=list)
    data_sources_used: List[str] = field(default_factory=list)
    tools_used: List[str] = field(default_factory=list)
    user_preferences: Dict[str, Any] = field(default_factory=dict)
```

### 智能洞察提取

```python
def _extract_key_insights(self, result_text: str) -> List[str]:
    """从分析结果中提取关键洞察"""
    insights = []
    
    # 关键词匹配
    insight_indicators = [
        '发现', '结论', '建议', '洞察', '关键', '重要', 
        '显示', '表明', '总计', '平均', '最高', '最低', 
        '增长', '下降', '趋势', '异常'
    ]
    
    # 数值模式匹配
    number_patterns = [
        r'(\d+(?:\.\d+)?(?:万|千|百|元|个|家|次|%))',
        r'(增长|下降|上升|减少)\s*(\d+(?:\.\d+)?%)',
        r'(总计|合计|平均)\s*(\d+(?:\.\d+)?(?:万|千|百|元))'
    ]
    
    for paragraph in result_text.split('\n\n'):
        for line in paragraph.split('\n'):
            line = line.strip()
            
            # 长度和关键词过滤
            if (15 < len(line) < 150 and 
                any(keyword in line for keyword in insight_indicators)):
                
                # 清理格式
                clean_line = re.sub(r'^[#*\-\d\.\s]+', '', line).strip('：:')
                
                if clean_line and clean_line not in insights:
                    insights.append(clean_line)
    
    return insights[:8]
```

### 上下文构建策略

根据相关性分数动态调整上下文内容：

```python
def _build_context_aware_query(self, current_query: str, context: ConversationContextData, round_number: int) -> str:
    """构建上下文感知的查询"""
    
    if not context or round_number == 1:
        return current_query
    
    # 计算相关性
    relevance_score = self._calculate_context_relevance(current_query, context)
    
    if relevance_score < 0.3:
        # 低相关性：新话题
        return self._build_new_topic_prompt(current_query, context, round_number)
    elif relevance_score < 0.7:
        # 中等相关性：部分相关
        return self._build_moderate_context_prompt(current_query, context, round_number)
    else:
        # 高相关性：强相关
        return self._build_high_relevance_context_prompt(current_query, context, round_number)
```

---

## 部署配置

### 环境要求

- Python 3.8+
- Node.js 16+
- PostgreSQL 12+
- Redis (可选，用于缓存)

### 后端配置

```python
# settings.py
OPENAI_API_KEY = "your_openai_api_key"
OPENAI_MODEL = "gpt-4"
DATABASE_URL = "postgresql://user:pass@localhost/dbname"

# 多轮分析配置
MULTI_ROUND_CONFIG = {
    "max_context_rounds": 10,
    "context_cache_ttl": 3600,
    "llm_summary_enabled": True,
    "relevance_threshold": 0.3
}
```

### 前端配置

```typescript
// config.ts
export const API_CONFIG = {
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8000',
  timeout: 30000,
  multiRound: {
    maxRounds: 50,
    contextDepth: 5,
    autoSave: true
  }
};
```

### 数据库迁移

```bash
# 执行数据库迁移
alembic upgrade head

# 或手动执行SQL
psql -d your_database -f migrations/add_multi_round_fields.sql
```

---

## 故障排查

### 常见问题

#### 1. 上下文加载失败

**症状**：多轮分析时显示"上下文加载失败"

**排查步骤**：
```python
# 检查会话是否存在
conversation = db.query(Conversation).filter(Conversation.id == conversation_id).first()

# 检查分析记录
analyses = db.query(Analysis).filter(Analysis.conversation_id == conversation_id).all()

# 检查上下文记录
contexts = db.query(ConversationContext).filter(
    ConversationContext.conversation_id == conversation_id
).all()
```

**解决方案**：
- 确保conversation_contexts表存在且有数据
- 检查ConversationService的get_conversation_context方法
- 添加错误处理和降级策略

#### 2. 分析状态不同步

**症状**：前端显示的分析状态与实际不符

**排查步骤**：
```typescript
// 检查状态更新逻辑
console.log('Current conversation state:', conversationState);
console.log('Analysis steps:', analysisSteps);
console.log('MCP results:', mcpResults);
```

**解决方案**：
- 确保updateCurrentRoundStatus正确调用
- 检查事件处理的时序
- 添加状态同步机制

#### 3. 数据还原不完整

**症状**：切换会话后部分数据丢失

**排查步骤**：
```sql
-- 检查数据完整性
SELECT 
    a.id,
    a.analysis_steps IS NOT NULL as has_steps,
    a.mcp_results IS NOT NULL as has_mcp,
    a.final_report IS NOT NULL as has_report
FROM analyses a 
WHERE a.conversation_id = 'your_conversation_id';
```

**解决方案**：
- 确保save_analysis_state正确保存所有字段
- 检查JSON序列化/反序列化
- 添加数据验证

### 调试工具

#### 1. 上下文调试脚本

```python
# debug_context.py
def debug_conversation_context(conversation_id: str):
    """调试会话上下文"""
    service = ConversationService(db)
    
    # 获取原始上下文数据
    raw_contexts = service._get_raw_contexts(conversation_id)
    print(f"Raw contexts: {len(raw_contexts)}")
    
    # 获取结构化上下文
    context_data = service.get_conversation_context(conversation_id)
    print(f"Structured context: {context_data}")
    
    # 测试相关性计算
    analyzer = MultiRoundLLMAnalyzer(db)
    relevance = analyzer._calculate_context_relevance("测试查询", context_data)
    print(f"Relevance score: {relevance}")
```

#### 2. 前端调试工具

```typescript
// 在浏览器控制台中使用
window.debugMultiRound = {
  getConversationState: () => conversationState,
  getAnalysisSteps: () => analysisSteps,
  getMcpResults: () => mcpResults,
  testContextRelevance: (relevance: number) => {
    setContextRelevance(relevance);
  }
};
```

---

## 性能优化

### 1. 数据库优化

```sql
-- 添加索引
CREATE INDEX idx_conversations_project_updated ON conversations(project_id, updated_at DESC);
CREATE INDEX idx_analyses_conversation_round ON analyses(conversation_id, round_number);
CREATE INDEX idx_contexts_conversation_type ON conversation_contexts(conversation_id, context_type);

-- 分区表（大数据量时）
CREATE TABLE conversation_contexts_2024 PARTITION OF conversation_contexts
FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');
```

### 2. 缓存策略

```python
# 上下文缓存
@lru_cache(maxsize=100)
def get_conversation_context_cached(conversation_id: str, max_rounds: int = 5):
    """带缓存的上下文获取"""
    return conversation_service.get_conversation_context(conversation_id, max_rounds)

# Redis缓存
async def get_llm_summary_cached(analysis_id: str) -> Optional[str]:
    """带Redis缓存的LLM总结"""
    cache_key = f"llm_summary:{analysis_id}"
    cached = await redis.get(cache_key)
    
    if cached:
        return cached.decode()
    
    summary = await generate_round_summary(analysis_id)
    if summary:
        await redis.setex(cache_key, 3600, summary)
    
    return summary
```

### 3. 前端优化

```typescript
// 虚拟滚动（大量轮次时）
const VirtualizedRounds = React.memo(({ rounds }) => {
  return (
    <FixedSizeList
      height={600}
      itemCount={rounds.length}
      itemSize={200}
      itemData={rounds}
    >
      {RoundItem}
    </FixedSizeList>
  );
});

// 懒加载分析结果
const LazyAnalysisResult = React.lazy(() => import('./AnalysisResult'));

// 防抖的状态更新
const debouncedUpdateState = useMemo(
  () => debounce((newState) => setConversationState(newState), 300),
  []
);
```

### 4. 监控指标

```python
# 性能监控
@monitor_performance
async def generate_conversation_stream(...):
    start_time = time.time()
    
    # 分析逻辑
    
    metrics.record_duration('multi_round_analysis', time.time() - start_time)
    metrics.increment('multi_round_requests')
```

---

## 最佳实践

### 1. 开发建议

- **状态管理**：使用不可变数据结构，避免状态污染
- **错误处理**：每个异步操作都要有错误处理和降级策略
- **性能监控**：关键路径添加性能监控和日志
- **测试覆盖**：核心逻辑要有单元测试和集成测试

### 2. 用户体验

- **加载状态**：所有异步操作都要有加载指示
- **错误反馈**：错误信息要用户友好且可操作
- **响应式设计**：支持不同屏幕尺寸
- **键盘快捷键**：提供常用操作的快捷键

### 3. 运维建议

- **日志记录**：关键操作和错误要有详细日志
- **监控告警**：设置关键指标的监控告警
- **备份策略**：定期备份会话和分析数据
- **容量规划**：根据使用量规划存储和计算资源

---

## 更新日志

### v2.1.0 (2024-01-15)
- ✅ 新增上下文相关性评估
- ✅ 优化LLM智能总结
- ✅ 增强数据持久化机制
- ✅ 改进前端状态管理

### v2.0.0 (2024-01-01)
- ✅ 完整的多轮分析系统
- ✅ 智能上下文感知
- ✅ 流式响应支持
- ✅ 数据完整还原

### v1.0.0 (2023-12-01)
- ✅ 基础多轮对话功能
- ✅ 简单上下文管理
- ✅ 基本数据持久化

---

## 联系支持

如有问题或建议，请联系开发团队或提交Issue。

**文档版本**：v2.1.0  
**最后更新**：2024-01-15  
**维护者**：AI数据分析团队