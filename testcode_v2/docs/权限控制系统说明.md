# 项目权限控制系统说明

## 概述

本系统实现了基于两级用户层级关系的项目访问权限控制，支持超级用户和普通用户的不同访问权限。

## 权限规则

### 公共项目权限
- **公共项目（user_id=0）**：所有用户都可以查看和操作
- 适用于共享模板、示例项目等场景

### 超级用户权限
- 可以查看和操作自己创建的项目
- 可以查看和操作所有直接子账号创建的项目
- 可以查看和操作所有公共项目

### 普通用户权限
- 可以查看和操作自己创建的项目
- 可以查看和操作父级账号创建的项目
- 可以查看和操作所有公共项目
- 无法访问其他同级或子级用户的项目

## 数据库结构

### 用户表字段
- `id`: 用户唯一标识
- `is_superuser`: 是否为超级用户
- `pid`: 父级用户ID，0表示顶级用户

### 项目表字段
- `id`: 项目唯一标识
- `user_id`: 项目创建者ID（0表示公共项目）
- 其他项目信息字段

## 实现细节

### 权限检查函数

```python
def get_accessible_user_ids(db: Session, user_id: int) -> List[int]:
    """获取用户可以访问的用户ID列表（两级账号结构）"""
    # 始终包含自己和公共项目（user_id=0）
    # 超级用户：获取所有直接子账号
    # 普通用户：获取自己和父级账号
```

```python
def check_project_access_permission(db: Session, user_id: int, project_user_id: int) -> bool:
    """检查用户是否有访问指定项目的权限"""
    # 公共项目（user_id=0）所有人都可以访问
    # 其他项目基于get_accessible_user_ids的结果进行权限判断
```

### API接口权限控制

所有项目相关的接口都添加了权限控制：

1. **GET /projects/** - 获取项目列表
   - 基于权限过滤，只返回用户有权限访问的项目

2. **GET /projects/{project_id}** - 获取项目详情
   - 检查用户是否有权限访问该项目

3. **PUT /projects/{project_id}** - 更新项目
   - 检查用户是否有权限修改该项目

4. **DELETE /projects/{project_id}** - 删除项目
   - 检查用户是否有权限删除该项目

5. **项目相关子资源接口**
   - 所有子资源（表、分析记录等）都基于项目权限进行控制

## 使用示例

### 两级用户层级关系示例

```
管理员(超级用户, id=1, pid=0)
├── 员工A(普通用户, id=2, pid=1)
├── 员工B(普通用户, id=3, pid=1)
└── 员工C(普通用户, id=4, pid=1)
```

### 权限访问矩阵

| 用户 | 可访问的项目创建者 |
|------|-------------------|
| 管理员(id=1) | 0, 1, 2, 3, 4 (公共项目、自己和所有子账号) |
| 员工A(id=2) | 0, 1, 2 (公共项目、父级和自己) |
| 员工B(id=3) | 0, 1, 3 (公共项目、父级和自己) |
| 员工C(id=4) | 0, 1, 4 (公共项目、父级和自己) |

## 安全考虑

1. **认证验证**: 所有接口都需要有效的JWT令牌
2. **权限检查**: 每个操作都会检查用户权限
3. **数据隔离**: 用户只能看到有权限的数据
4. **错误处理**: 无权限访问返回403错误

## 扩展性

系统设计支持：
- 两级用户关系管理
- 灵活的权限规则扩展
- 新的资源类型权限控制
- 权限缓存优化（可后续添加）

## 注意事项

1. 权限检查在每次请求时都会执行数据库查询
2. 对于大量用户的系统，建议添加权限缓存机制
3. 用户层级关系变更时，需要考虑对现有项目权限的影响
4. 建议定期审计用户权限和项目访问日志
5. 系统设计为两级账号结构，简化了权限管理复杂度 