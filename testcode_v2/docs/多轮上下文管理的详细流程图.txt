graph TD
    A[多轮分析请求] --> B{round_number > 1?}
    B -->|否| C[首轮分析，无需上下文]
    B -->|是| D[开始上下文管理流程]
    
    D --> E[获取历史分析数据]
    E --> F["调用_get_historical_analysis_data"]
    F --> G["查询analyses表和tool_executions表"]
    
    G --> H{历史数据存在?}
    H -->|否| I[无历史上下文，继续分析]
    H -->|是| J[构建历史数据结构]
    
    J --> K["整理每轮分析的完整信息"]
    K --> L["包含查询、工具执行、结果等"]
    
    L --> M["调用_llm_extract_relevant_context"]
    M --> N["将历史数据转为JSON字符串"]
    N --> O["构建LLM上下文分析提示词"]
    
    O --> P["LLM分析当前查询与历史的相关性"]
    P --> Q{LLM判断结果}
    
    Q -->|无相关上下文| R["返回None，不使用历史信息"]
    Q -->|有相关上下文| S["生成结构化上下文摘要"]
    
    S --> T["【用户意图】部分"]
    S --> U["【分析路径】部分"]
    S --> V["【最终结论】部分"]
    
    T --> W[合并上下文摘要]
    U --> W
    V --> W
    
    W --> X["保存到context_data中"]
    X --> Y["传递给generate_stream方法"]
    
    Y --> Z["在意图分析中使用上下文"]
    Z --> AA["添加历史轮次分析摘要到提示词"]
    
    AA --> BB["LLM基于上下文进行意图分析"]
    BB --> CC["生成更精确的分析计划"]
    
    R --> DD[直接进行分析，无上下文增强]
    I --> DD
    C --> DD
    CC --> EE[继续执行分析流程]
    DD --> EE
    
    subgraph "历史数据结构"
        FF["round_number: 轮次编号"]
        GG["user_query: 用户查询"]
        HH["tool_executions: 工具执行记录"]
        II["results: 分析结果"]
        JJ["status: 分析状态"]
    end
    
    subgraph "LLM上下文分析"
        KK["相关性判断逻辑"]
        LL["摘要生成算法"]
        MM["结构化输出格式"]
    end
    
    subgraph "上下文构建器"
        NN["ContextRecoveryBuilder"]
        OO["IntentAnalysisContextBuilder"]
        PP["ContextBuilderManager"]
    end
    
    K --> FF
    K --> GG
    K --> HH
    K --> II
    K --> JJ
    
    P --> KK
    S --> LL
    T --> MM
    
    Y --> NN
    Z --> OO
    Y --> PP