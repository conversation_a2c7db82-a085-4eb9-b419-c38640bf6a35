graph TD
    A[用户发起查询] --> B{是否为新会话?}
    B -->|是| C[创建新会话]
    B -->|否| D[获取会话上下文]
    
    C --> E[round_number = 1]
    D --> F{round_number > 1?}
    F -->|是| G[获取历史分析数据]
    F -->|否| E
    
    G --> H[LLM智能上下文提取]
    H --> I{上下文相关?}
    I -->|是| J[生成上下文摘要]
    I -->|否| K[无相关上下文]
    
    J --> L[开始意图分析]
    K --> L
    E --> L
    
    L --> M[调用_analyze_user_intent]
    M --> N[构建意图分析提示词]
    N --> O[LLM生成意图分析结果]
    O --> P{需要澄清?}
    
    P -->|是| Q[生成澄清问题]
    P -->|否| R[生成执行步骤规划]
    
    Q --> S[发送intent_confirmation_request事件]
    R --> S
    
    S --> T[前端显示意图确认界面]
    T --> U[用户确认/调整意图]
    U --> V{用户操作}
    
    V -->|确认| W[提交意图确认]
    V -->|取消| X[取消分析]
    V -->|调整| Y[用户补充说明]
    
    Y --> W
    W --> Z[发送intent_confirmation_completed事件]
    Z --> AA[继续执行分析]
    
    AA --> BB[构建执行上下文]
    BB --> CC[上下文构建器管理器]
    CC --> DD[ContextRecoveryBuilder]
    CC --> EE[IntentAnalysisContextBuilder]
    
    DD --> FF[构建恢复上下文]
    EE --> GG[构建意图分析上下文]
    
    FF --> HH[合并所有上下文信息]
    GG --> HH
    
    HH --> II[开始流式分析执行]
    II --> JJ[工具选择与执行]
    JJ --> KK[StepInterceptor拦截事件]
    
    KK --> LL{执行步骤类型}
    LL -->|system| MM[系统步骤]
    LL -->|tool| NN[工具执行步骤]
    LL -->|planning| OO[规划步骤]
    LL -->|report| PP[报告生成步骤]
    LL -->|error| QQ[错误处理步骤]
    
    MM --> RR[保存步骤详情]
    NN --> SS[执行具体工具]
    OO --> TT[生成执行计划]
    PP --> UU[生成分析报告]
    QQ --> VV[记录错误信息]
    
    SS --> WW{工具执行成功?}
    WW -->|是| XX[保存工具执行结果]
    WW -->|否| VV
    
    XX --> RR
    TT --> RR
    UU --> RR
    VV --> RR
    
    RR --> YY[更新analysis_step_details表]
    YY --> ZZ{还有更多步骤?}
    
    ZZ -->|是| JJ
    ZZ -->|否| AAA[分析完成]
    
    AAA --> BBB[更新analyses表状态]
    BBB --> CCC[发送analysis_completed事件]
    CCC --> DDD[前端显示最终结果]
    
    X --> EEE[清理分析状态]
    EEE --> FFF[结束]
    DDD --> FFF
    
    subgraph "数据持久化层"
        GGG[(conversations表)]
        HHH[(analyses表)]
        III[(tool_executions表)]
        JJJ[(analysis_step_details表)]
    end
    
    subgraph "前端组件"
        KKK[MultiRoundAnalysis.tsx]
        LLL[ConversationState管理]
        MMM[意图确认界面]
        NNN[实时流式事件处理]
        OOO[分析步骤可视化]
    end
    
    subgraph "核心分析引擎"
        PPP[LLMStreamAnalyzer]
        QQQ[MultiRoundLLMAnalyzer]
        RRR[StepInterceptor]
        SSS[ContextBuilderManager]
    end
    
    YY --> HHH
    YY --> JJJ
    KK --> RRR
    CC --> SSS
    T --> MMM
    II --> PPP
    G --> QQQ