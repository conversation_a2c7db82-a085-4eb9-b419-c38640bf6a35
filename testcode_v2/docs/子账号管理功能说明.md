# 子账号管理功能说明

## 功能概述

子账号管理模块为超级用户提供了完整的子账号管理功能，包括创建、编辑、删除、启用/禁用子账号等操作。

## 权限说明

- **超级用户（is_superuser=true）**：可以访问子账号管理功能
- **普通用户**：无法访问子账号管理功能

## 功能特性

### 1. 子账号列表
- 分页显示当前用户的所有子账号
- 显示用户名、邮箱、状态、创建时间等信息
- 支持刷新数据

### 2. 创建子账号
- 必填字段：用户名、密码
- 可选字段：邮箱
- 默认状态：启用
- 自动设置父级ID为当前用户ID
- 子账号不能是超级用户

### 3. 编辑子账号
- 可修改用户名、邮箱、密码、状态
- 密码留空表示不修改
- 用户名和邮箱唯一性校验

### 4. 删除子账号
- 只能删除自己的子账号
- 不能删除自己
- 删除前有确认提示

### 5. 状态管理
- 点击状态标签可快速切换启用/禁用
- 禁用的用户无法登录系统

## 访问方式

### 前端入口
1. 以超级用户身份登录系统
2. 在左侧导航栏底部可以看到"用户管理"图标
3. 点击图标或"用户"标签进入子账号管理页面

### API接口

#### 获取子账号列表
```http
GET /api/v1/users/sub-accounts?skip=0&limit=10
Authorization: Bearer <token>
```

#### 创建子账号
```http
POST /api/v1/users/sub-accounts
Authorization: Bearer <token>
Content-Type: application/json

{
  "username": "test_user",
  "email": "<EMAIL>",
  "password": "password123",
  "is_active": true
}
```

#### 更新子账号
```http
PUT /api/v1/users/sub-accounts/{user_id}
Authorization: Bearer <token>
Content-Type: application/json

{
  "username": "new_username",
  "email": "<EMAIL>",
  "is_active": false
}
```

#### 删除子账号
```http
DELETE /api/v1/users/sub-accounts/{user_id}
Authorization: Bearer <token>
```

#### 切换子账号状态
```http
POST /api/v1/users/sub-accounts/{user_id}/toggle-status
Authorization: Bearer <token>
```

## 数据模型

### 用户表结构
```sql
users (
  id INTEGER PRIMARY KEY,
  username VARCHAR(50) UNIQUE NOT NULL,
  email VARCHAR(100) UNIQUE,
  hashed_password VARCHAR(255) NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  is_superuser BOOLEAN DEFAULT FALSE,
  pid INTEGER DEFAULT 0,  -- 父级用户ID
  created_at TIMESTAMP,
  updated_at TIMESTAMP
)
```

### 用户层级关系
```
超级用户 (id=1, pid=0, is_superuser=true)
├── 子账号A (id=2, pid=1, is_superuser=false)
├── 子账号B (id=3, pid=1, is_superuser=false)
└── 子账号C (id=4, pid=1, is_superuser=false)
```

## 权限控制

子账号管理功能基于现有的权限控制系统：

1. **接口级权限**：所有子账号管理接口都要求用户必须是超级用户
2. **数据级权限**：用户只能管理自己的直接子账号（pid = current_user.id）
3. **前端权限**：用户管理入口只对超级用户可见

## 安全考虑

1. **密码安全**：所有密码都经过哈希加密存储
2. **权限隔离**：子账号不能成为超级用户
3. **数据隔离**：用户只能访问自己的子账号
4. **操作限制**：不能删除自己，不能修改父级关系

## 使用场景

1. **企业管理**：管理员为不同部门创建子账号
2. **项目协作**：项目负责人为团队成员创建账号
3. **权限分级**：根据业务需要分配不同级别的访问权限

## 注意事项

1. 用户名必须唯一，建议使用有意义的命名规则
2. 邮箱如果提供，也必须唯一
3. 密码最少6个字符，建议使用强密码
4. 禁用的账号无法登录，但数据仍然保留
5. 删除账号会永久删除所有相关数据，操作不可逆

## 扩展功能

未来可以考虑的扩展功能：
1. 批量操作（批量创建、删除、状态切换）
2. 角色管理（为子账号分配不同角色）
3. 权限细分（更细粒度的功能权限控制）
4. 操作日志（记录用户管理操作历史）
5. 账号到期管理（设置账号有效期） 