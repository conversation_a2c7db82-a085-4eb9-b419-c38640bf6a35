# 项目智能自检功能说明

## 概述

项目智能自检功能是一个基于AI的智能化项目配置验证系统，通过与用户的交互式对话，确保AI Agent在开始数据分析之前已经充分理解用户的业务需求和数据结构。该功能集成在项目创建流程的最后阶段，为用户提供智能化的项目就绪性评估和配置优化。

## 功能特性

### 🧠 智能业务理解
- **自动项目分析**：基于项目信息、数据源类型、表结构和样本数据进行智能分析
- **业务域识别**：自动识别业务领域（电商、金融、教育等）
- **关键实体提取**：识别核心业务实体和指标
- **数据关系分析**：理解表间关系和业务逻辑

### 💬 交互式问题生成
- **智能问题生成**：基于分析结果生成个性化补充问题
- **优先级分类**：问题分为必答、重要、可选三个级别
- **多种问题类型**：支持文本、单选、多选、数值等问题类型
- **动态问题调整**：根据用户回答动态调整后续问题

### 🎯 场景预测与验证
- **常见场景预测**：基于业务理解预测用户可能的分析需求
- **Agent能力验证**：使用真实Agent测试场景理解能力
- **交互式确认**：用户可以确认、修正或拒绝预测场景
- **反馈收集**：收集用户对场景理解的具体反馈

### 📊 项目就绪状态评估
- **综合评估**：基于多维度信息评估项目配置完整性
- **就绪状态判断**：明确告知用户项目是否可以开始使用
- **缺失信息提示**：清晰指出需要补充的关键信息

## 技术架构

### 后端架构

#### 核心服务类
```python
class ProjectIntelligenceService:
    """基于Redis+内存降级的项目智能理解服务"""
    
    # 主要功能方法
    - start_intelligence_session()      # 启动智能理解会话
    - analyze_business_understanding()  # 分析项目就绪状态
    - generate_intelligent_questions()  # 生成智能问题
    - save_user_answers()              # 保存用户回答
    - predict_scenarios()              # 预测场景
    - save_scenario_confirmations()    # 保存场景确认
    - complete_intelligence_session()  # 完成会话
```

#### 存储机制
- **主存储**：Redis（支持多进程部署）
- **降级存储**：全局内存存储
- **持久化**：重要信息保存到数据库的DataSource.description字段
- **会话管理**：2小时自动过期，完成后主动清理

#### LLM集成
- **模型支持**：支持OpenAI、Google Gemini等多种LLM
- **Prompt工程**：专业的项目就绪评估和场景预测Prompt
- **结果解析**：智能JSON解析，支持Markdown代码块提取
- **超时控制**：120秒LLM调用超时保护

### 前端架构

#### 主要组件
```typescript
// 智能理解向导主组件
ProjectIntelligenceWizard
├── BusinessUnderstandingStep     // 业务理解展示
├── IntelligentQuestionStep      // 智能问题回答
├── ScenarioPredictionStep       // 场景预测验证
└── CompletionSummaryStep        // 完成总结
```

#### 状态管理
```typescript
interface IntelligenceSession {
  sessionId: string | null;
  status: 'idle' | 'analyzing' | 'questioning' | 'predicting' | 'completed';
  readinessEvaluation: any | null;
  questions: any[];
  userAnswers: Record<string, any>;
  scenarioPredictions: any[];
  loading: boolean;
  error: string | null;
}
```

## 功能流程

### 1. 启动智能理解
```mermaid
graph TD
    A[用户完成表描述] --> B[点击进入自检]
    B --> C[创建智能理解会话]
    C --> D[获取项目数据]
    D --> E[开始项目就绪评估]
```

### 2. 项目就绪评估
```mermaid
graph TD
    A[收集项目信息] --> B[构建评估Prompt]
    B --> C[LLM分析]
    C --> D[解析评估结果]
    D --> E{项目是否就绪?}
    E -->|是| F[跳过问题阶段]
    E -->|否| G[生成补充问题]
```

### 3. 智能问题交互
```mermaid
graph TD
    A[生成个性化问题] --> B[用户回答问题]
    B --> C[保存用户答案]
    C --> D[进入场景预测]
```

### 4. 场景预测验证
```mermaid
graph TD
    A[LLM预测场景] --> B[Agent验证场景]
    B --> C[展示预测结果]
    C --> D[用户确认场景]
    D --> E[收集反馈]
    E --> F[完成自检]
```

## API接口

### 会话管理
```typescript
// 启动智能理解会话
POST /api/v1/project-intelligence/start/{project_id}
Response: { session_id: string }

// 获取会话状态
GET /api/v1/project-intelligence/session/{session_id}
Response: { status, created_at, updated_at }
```

### 核心功能
```typescript
// 分析业务理解
POST /api/v1/project-intelligence/analyze/{session_id}
Response: ProjectReadinessEvaluation

// 生成智能问题
POST /api/v1/project-intelligence/questions/{session_id}
Response: { questions: Question[] }

// 保存用户回答
POST /api/v1/project-intelligence/answers/{session_id}
Body: { [questionId]: answer }

// 预测场景
POST /api/v1/project-intelligence/predict/{session_id}
Response: { scenarios: Scenario[] }

// 保存场景确认
POST /api/v1/project-intelligence/confirmations/{session_id}
Body: { [scenarioId]: { status, feedback } }

// 完成会话
POST /api/v1/project-intelligence/complete/{session_id}
Response: CompletionSummary
```

## 数据结构

### 项目就绪评估结果
```typescript
interface ProjectReadinessEvaluation {
  ready: "yes" | "no";
  reason_if_no: string;
  project_understanding: {
    business_domain: string;
    business_type: string;
    key_entities: string[];
    core_metrics: string[];
    data_relationships: string;
  };
  can_answer_examples: string[];
  boundaries: string[];
  missing_info: string[];
}
```

### 智能问题
```typescript
interface Question {
  id: string;
  question: string;
  priority: "critical" | "important" | "optional";
  type: "text" | "single_choice" | "multiple_choice" | "numeric";
  options?: string[];
}
```

### 场景预测
```typescript
interface Scenario {
  id: string;
  title: string;
  description: string;
  predicted_question: string;
  understanding_points: string[];
  solution_approach: string;
  confidence_score: number;
  agent_analysis?: {
    success: boolean;
    reasoning: string;
    planning_steps: string[];
    token_usage: object;
    analysis_time: number;
  };
}
```

## 用户体验优化

### 1. 渐进式加载
- **先跳转再加载**：点击下一步立即跳转到自检页面，然后显示加载动画
- **分阶段提示**：不同阶段显示不同的加载提示信息
- **超时保护**：前端180秒超时，后端120秒LLM超时

### 2. 智能交互
- **个性化问题**：根据项目特点生成针对性问题
- **优先级标识**：清晰的视觉标识区分问题重要性
- **实时验证**：表单实时验证，提供即时反馈

### 3. 错误处理
- **优雅降级**：Redis失败时自动降级到内存存储
- **错误恢复**：网络错误时提供重试机制
- **状态保持**：会话数据持久化，支持页面刷新恢复

## 配置说明

### 环境变量
```bash
# Redis配置（用于会话存储）
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# LLM配置
OPENAI_API_KEY=your-api-key
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-3.5-turbo
```

### 超时配置
```python
# 后端超时设置
LLM_TIMEOUT = 120  # LLM调用超时（秒）
SESSION_TTL = 7200  # 会话过期时间（秒）

# 前端超时设置
API_TIMEOUT = 180000  # API调用超时（毫秒）
```

## 性能优化

### 1. 缓存机制
- **项目数据缓存**：基于项目数据哈希的24小时缓存
- **LLM结果缓存**：相同输入直接返回缓存结果
- **会话数据缓存**：Redis存储支持快速读写

### 2. 并发支持
- **多进程部署**：支持4个worker进程
- **Redis共享**：多进程间通过Redis共享会话数据
- **连接池**：Redis连接池管理，最大100个连接

### 3. 资源优化
- **数据精简**：只取前3条样本数据，减少LLM处理负担
- **输出限制**：LLM输出限制2000 tokens，提高响应速度
- **主动清理**：完成会话后立即清理临时数据

## 监控与日志

### 日志级别
- **DEBUG**：详细的会话操作日志
- **INFO**：关键流程节点日志
- **WARNING**：Redis降级、超时等警告
- **ERROR**：业务异常和系统错误

### 关键指标
- **会话成功率**：成功完成的会话比例
- **平均响应时间**：各阶段的平均处理时间
- **Redis命中率**：缓存命中率统计
- **LLM调用统计**：调用次数、成功率、平均耗时

## 故障排查

### 常见问题

#### 1. 会话不存在或已过期
**原因**：多进程环境下内存存储不共享
**解决**：确保Redis正常运行，检查Redis连接配置

#### 2. LLM调用超时
**原因**：网络延迟或模型响应慢
**解决**：检查网络连接，考虑调整超时时间

#### 3. 场景预测失败
**原因**：Agent调用异常或项目数据不完整
**解决**：检查Agent服务状态，验证项目数据完整性

### 调试方法
```bash
# 查看Redis连接状态
redis-cli ping

# 检查会话数据
redis-cli keys "intelligence_session:*"

# 查看应用日志
tail -f logs/app/app.log | grep intelligence

# 检查活跃会话数量
curl -X GET "http://localhost:8301/api/v1/project-intelligence/session/{session_id}"
```

## 未来规划

### 短期优化
- [ ] 支持更多问题类型（日期、文件上传等）
- [ ] 增加场景预测的准确性评估
- [ ] 优化LLM Prompt，提高理解准确性
- [ ] 添加用户满意度评分机制

### 中期功能
- [ ] 支持多语言国际化
- [ ] 集成更多LLM模型选择
- [ ] 增加历史会话回顾功能
- [ ] 支持团队协作的项目配置

### 长期愿景
- [ ] 基于用户行为的个性化推荐
- [ ] 自动化的项目配置优化建议
- [ ] 跨项目的知识迁移学习
- [ ] 企业级的配置模板管理

---

*文档版本：v2.0*  
*最后更新：2025年1月*  
*维护者：AI平台开发团队* 