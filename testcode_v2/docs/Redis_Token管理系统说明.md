# Redis Token管理系统说明

## 概述

本系统实现了基于Redis的JWT Token管理，支持单点登录（挤下线）功能。当同一个账号在不同地方登录时，会自动将之前的登录会话踢下线，确保账号安全。

## 核心功能

### 1. 单点登录（SSO）
- 每个用户同时只能有一个有效的登录会话
- 新登录会自动撤销之前的Token
- 防止账号被多人同时使用

### 2. Token存储管理
- JWT Token存储在Redis中，支持过期自动清理
- 双重验证：JWT签名验证 + Redis存在性验证
- 高性能的Token查询和验证

### 3. 主动登出和用户状态验证
- 用户可以主动退出登录，撤销Token
- 实时检查用户禁用状态，被禁用用户立即失去访问权限
- 简化的Token管理，专注核心安全功能

## 技术架构

### Redis存储结构

```
1. 用户Token映射
   Key: user_token:{user_id}
   Value: {jwt_token}
   TTL: JWT过期时间

2. Token信息存储
   Key: token_info:{jwt_token}
   Value: {
     "user_id": 1,
     "user_info": {...},
     "created_at": "2024-01-01T00:00:00",
     "expires_at": "2024-01-01T01:00:00"
   }
   TTL: JWT过期时间
```

### 核心组件

#### 1. TokenService (app/services/token_service.py)
Token管理的核心服务类，提供以下功能：

- `store_token()`: 存储Token（实现单点登录）
- `get_user_token()`: 获取用户当前活跃Token
- `get_token_info()`: 获取Token详细信息
- `is_token_valid()`: 验证Token是否有效
- `revoke_token()`: 撤销指定Token
- `refresh_token_expiry()`: 刷新Token过期时间

#### 2. 认证中间件增强 (app/core/middleware/auth.py)
在原有JWT验证基础上增加Redis验证：

```python
# 三重验证流程
1. JWT格式和签名验证
2. Redis中Token存在性验证
3. 用户账户状态验证（是否被禁用）
```

#### 3. 登录接口改造 (app/api/v1/endpoints/auth.py)
- 登录成功后自动存储Token到Redis
- 实现单点登录逻辑
- 增加退出登录接口
- 简化管理功能，专注核心认证

## API接口

### 1. 用户登录
```http
POST /api/v1/auth/login
```
- 验证用户名密码
- 生成JWT Token
- 存储到Redis（挤下线逻辑）
- 返回Token

### 2. 用户退出
```http
POST /api/v1/auth/logout
Authorization: Bearer {token}
```
- 撤销当前Token
- 从Redis中删除

### 3. 获取用户信息
```http
GET /api/v1/auth/me
Authorization: Bearer {token}
```
- 验证Token有效性
- 返回用户信息



## 安全特性

### 1. 三重验证
- JWT签名验证确保Token未被篡改
- Redis存在性验证确保Token未被撤销
- 用户状态验证确保账户未被禁用

### 2. 单点登录保护
- 防止账号被多人同时使用
- 自动踢下线机制

### 3. 用户状态控制
- 被禁用用户立即失去所有访问权限
- 无需等待Token过期，实时生效

### 4. 过期自动清理
- Redis TTL机制自动清理过期Token
- 无需手动清理，节省存储空间

## 配置说明

### 环境变量
```bash
# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# JWT配置
JWT_SECRET_KEY=your-secret-key
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=60
```

### Redis键命名规范
- 用户Token: `user_token:{user_id}`
- Token信息: `token_info:{jwt_token}`

## 使用场景

### 1. 普通用户场景
```python
# 用户A在电脑上登录
POST /api/v1/auth/login
# 获得token_1

# 用户A在手机上登录
POST /api/v1/auth/login  
# 获得token_2，token_1自动失效

# 用户A使用token_1访问API
GET /api/v1/projects
# 返回401，提示"Token已失效，请重新登录"
```

### 2. 用户禁用场景
```python
# 用户A正常使用系统
GET /api/v1/projects
# 返回: 正常数据

# 管理员禁用用户A账户（在数据库中设置is_active=False）
# 用户A继续使用相同Token访问API
GET /api/v1/projects
# 返回: 401 "账户已被禁用"
```

## 错误处理

### 常见错误码
- `401 Unauthorized`: Token无效或已过期
- `403 Forbidden`: 权限不足
- `404 Not Found`: 用户不存在
- `500 Internal Server Error`: Redis连接失败等系统错误

### 错误消息
- "Token已失效，请重新登录"
- "账户已被禁用"
- "登录失败，请稍后重试"
- "用户不存在"

## 监控和维护

### 1. 性能监控
- 监控Redis连接数和响应时间
- 监控Token验证的成功率
- 监控活跃用户数量变化

### 2. 日志记录
系统会记录以下关键操作：
- Token存储成功/失败
- 用户被挤下线
- Token撤销操作
- 强制下线操作

### 3. 运维建议
- 定期监控Redis内存使用情况
- 设置合理的Token过期时间
- 定期检查异常登录行为

## 扩展功能

### 1. 可能的扩展
- 支持多设备登录（修改单点登录逻辑）
- Token刷新机制
- 登录地点记录
- 异常登录检测

### 2. 性能优化
- Redis集群部署
- Token验证缓存
- 批量操作优化

## 总结

本Redis Token管理系统通过双重验证和单点登录机制，显著提升了系统的安全性。同时保持了良好的性能和用户体验，为企业级应用提供了可靠的身份认证解决方案。 