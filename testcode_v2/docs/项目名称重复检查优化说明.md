# 项目名称重复检查优化说明

## 优化背景

### 原有问题
在原有的项目创建逻辑中，项目名称重复检查是**全局性的**，即系统会检查整个数据库中是否存在同名项目。这种设计存在以下问题：

1. **权限不一致**：用户可能因为看不到其他用户的项目而不知道为什么不能使用某个名称
2. **用户体验差**：用户无法创建与自己不可见项目同名的项目
3. **业务逻辑不合理**：如果用户看不到某个项目，理论上应该可以使用相同的名称

### 优化目标
将项目名称重复检查改为**基于用户可见范围**的检查，提升用户体验和业务逻辑的合理性。

## 优化方案

### 核心原理
利用现有的权限控制系统，通过 `get_accessible_user_ids()` 函数获取用户可以访问的用户ID列表，然后只在这个范围内检查项目名称是否重复。

### 权限范围说明
- **超级用户**：可以看到自己和所有子账号创建的项目
- **普通用户**：可以看到自己和父级账号创建的项目

## 修改内容

### 1. 项目管理接口 (`app/api/v1/endpoints/projects.py`)

#### 创建项目接口优化
```python
# 原有逻辑（全局检查）
existing_project = db.query(ProjectModel).filter(ProjectModel.name == project_in.name).first()

# 优化后逻辑（可见范围检查）
accessible_user_ids = get_accessible_user_ids(db, current_user.id)
existing_project = db.query(ProjectModel).filter(
    ProjectModel.name == project_in.name,
    ProjectModel.user_id.in_(accessible_user_ids)
).first()
```

#### 更新项目接口优化
```python
# 原有逻辑（全局检查）
existing_project = db.query(ProjectModel).filter(
    ProjectModel.name == project_in.name, 
    ProjectModel.id != project_id
).first()

# 优化后逻辑（可见范围检查）
accessible_user_ids = get_accessible_user_ids(db, current_user.id)
existing_project = db.query(ProjectModel).filter(
    ProjectModel.name == project_in.name, 
    ProjectModel.id != project_id,
    ProjectModel.user_id.in_(accessible_user_ids)
).first()
```

### 2. 项目向导接口 (`app/api/v1/endpoints/project_wizard.py`)

#### 创建项目接口优化
```python
# 原有逻辑（全局检查）
existing_project = db.query(Project).filter(Project.name == project_data.name).first()

# 优化后逻辑（可见范围检查）
accessible_user_ids = get_accessible_user_ids(db, current_user.id)
existing_project = db.query(Project).filter(
    Project.name == project_data.name,
    Project.user_id.in_(accessible_user_ids)
).first()
```

#### 依赖调整
为了支持权限检查，项目向导接口进行了以下调整：
- 引入 `get_current_user_with_permissions` 依赖
- 引入 `get_accessible_user_ids` 函数
- 将 `current_user` 参数类型从 `Dict[str, Any]` 改为 `User`

## 优化效果

### 1. 用户体验提升
- 用户只需要确保在自己可见的项目范围内名称不重复
- 减少了因不可见项目导致的命名限制
- 错误提示更加准确："项目名称在可见范围内已存在"

### 2. 业务逻辑更合理
- 符合权限隔离的设计原则
- 用户只需要关心自己能看到的项目
- 不同用户组之间的项目名称可以重复

### 3. 权限一致性
- 项目创建的权限检查与项目查看的权限检查保持一致
- 遵循"看得到才需要避免重复"的原则

## 影响范围

### 受影响的接口
1. `POST /api/v1/projects/` - 创建项目
2. `PUT /api/v1/projects/{project_id}` - 更新项目
3. `POST /api/v1/project-wizard/create_project` - 项目向导创建项目

### 不受影响的功能
- 项目列表查询（已经基于权限过滤）
- 项目详情查询（已经有权限检查）
- 项目删除（已经有权限检查）
- 其他项目相关功能

## 兼容性说明

### 向后兼容
- 对于超级用户，由于可以看到所有项目，行为基本不变
- 对于普通用户，可能会发现之前不能创建的项目名称现在可以创建了

### 数据安全
- 优化不涉及数据结构变更
- 不影响现有项目数据
- 权限控制依然严格

## 测试建议

### 功能测试场景
1. **超级用户创建项目**
   - 尝试创建与现有项目同名的项目（应该失败）
   - 尝试创建与子账号项目同名的项目（应该失败）

2. **普通用户创建项目**
   - 尝试创建与自己项目同名的项目（应该失败）
   - 尝试创建与父级项目同名的项目（应该失败）
   - 尝试创建与其他不可见用户项目同名的项目（应该成功）

3. **项目更新测试**
   - 更新项目名称为已存在的可见项目名称（应该失败）
   - 更新项目名称为不可见项目的名称（应该成功）

### 边界测试
- 用户权限变更后的行为验证
- 大量并发创建同名项目的处理
- 权限函数异常情况的处理

## 性能影响

### 查询复杂度
- 增加了一次权限查询：`get_accessible_user_ids()`
- 项目名称检查查询增加了 `user_id.in_()` 条件
- 总体性能影响微乎其微

### 优化建议
- 可以考虑对 `get_accessible_user_ids()` 结果进行缓存
- 在高并发场景下可以使用数据库索引优化查询

## 总结

这次优化将项目名称重复检查从全局检查改为基于用户可见范围的检查，提升了用户体验，使业务逻辑更加合理，同时保持了权限控制的一致性。优化是向后兼容的，不会影响现有功能，是一个低风险、高收益的改进。 