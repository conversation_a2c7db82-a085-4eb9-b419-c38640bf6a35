graph TD
    %% 用户交互层
    A[用户输入分析问题] --> B{是否为新会话?}

    %% 会话管理分支
    B -->|是| C[创建新会话]
    B -->|否| D[加载现有会话]

    C --> E[生成会话ID]
    E --> F[初始化会话状态]
    F --> G[设置第一轮分析]

    D --> H[获取会话历史]
    H --> I[重建轮次状态]
    I --> J[准备新轮次]

    %% 上下文管理
    G --> K{轮次数 > 1?}
    J --> K
    K -->|否| L[无需上下文]
    K -->|是| M[LLM上下文分析]

    M --> N[获取历史分析数据]
    N --> O[LLM判断相关性]
    O --> P{有相关上下文?}
    P -->|是| Q[生成上下文摘要]
    P -->|否| R[跳过上下文]

    %% 初始化阶段
    L --> S[创建执行上下文<br/>初始化analysis_insights_collection]
    Q --> S
    R --> S

    S --> T[发送start事件]
    T --> U[创建分析记录]
    U --> V[发送analysis_created事件]
    V --> W[加载工具列表]
    W --> X[发送tools_loaded事件]
    X --> Y[初始化SQL执行器]
    Y --> Z[发送sql_executor_initialized事件]

    %% 意图分析阶段
    Z --> AA[LLM意图分析]
    AA --> BB[发送intent_analyzed事件]
    BB --> CC[发送intent_confirmation_request事件]
    CC --> DD[等待用户意图确认]
    DD --> EE[发送intent_confirmation_completed事件]
    
    %% 主循环开始
    EE --> FF[进入主循环 while not context.is_completed]
    
    %% 打断检查
    FF --> GG[检查打断和取消状态]
    GG --> HH{打断状态检查}
    
    HH -->|cancel| II[处理取消]
    HH -->|wait| JJ[等待用户反馈]
    HH -->|adjust| KK[应用用户反馈]
    HH -->|continue| LL[继续分析]
    
    II --> MM[发送completed事件并退出]
    JJ --> NN{是否超时?}
    NN -->|是| OO[处理超时]
    NN -->|否| PP[发送waiting_feedback事件]
    PP --> QQ[睡眠2秒]
    QQ --> FF
    
    OO --> RR{超时处理结果}
    RR -->|cancel| MM
    RR -->|continue| SS[重置打断状态]
    
    KK --> TT[清理打断信号]
    TT --> SS
    SS --> FF
    
    %% 任务状态处理
    LL --> UU[更新上下文缓存]
    UU --> VV{context.task_state}
    
    %% 规划阶段
    VV -->|planning| WW[规划阶段打断检查]
    WW --> XX{是否被打断?}
    XX -->|是| FF
    XX -->|否| YY{达到最大规划轮数?}
    
    YY -->|是| ZZ[强制结束规划]
    ZZ --> AAA[生成最终报告]
    AAA --> BBB[发送report_generated事件]
    BBB --> CCC[设置task_state=completed]
    CCC --> FF
    
    YY -->|否| DDD[增加规划轮数]
    DDD --> EEE[发送planning_round事件]
    EEE --> FFF[生成分析计划]
    FFF --> GGG[调用LLM进行规划]
    GGG --> HHH{LLM规划结果}
    
    %% 工具执行分支
    HHH -->|action_type=tool| III[工具执行前打断检查]
    III --> JJJ{是否被打断?}
    JJJ -->|是| FF
    JJJ -->|否| KKK[发送plan_created事件]
    
    KKK --> LLL[验证工具存在]
    LLL --> MMM{工具是否存在?}
    MMM -->|否| FF
    MMM -->|是| NNN[发送planning_decision事件]

    NNN --> OOO[发送step_started事件]
    OOO --> PPP[执行工具步骤]
    PPP --> QQQ[发送step_completed事件]

    %% 专业数据分析阶段（新增）
    QQQ --> QQQA[专业数据分析前打断检查]
    QQQA --> QQQB{是否被打断?}
    QQQB -->|是| FF
    QQQB -->|否| QQQC[调用ResultAnalyzer进行专业分析]
    QQQC --> QQQD[计算统计摘要]
    QQQD --> QQQE[评估数据质量]
    QQQE --> QQQF[检测模式和异常]
    QQQF --> QQQG[分析相关性]
    QQQG --> QQQH[LLM提取业务洞察]
    QQQH --> QQQI[构建AnalysisResult对象]
    QQQI --> QQQJ[收集洞察到analysis_insights_collection]
    QQQJ --> QQQK[保存分析摘要到analysis_results_history]

    %% 智能图表生成
    QQQK --> RRR[图表生成前打断检查]
    RRR --> SSS{是否被打断?}
    SSS -->|是| FF
    SSS -->|否| TTT[调用智能图表生成Agent]
    TTT --> TTTU[LLM分析数据适合性]
    TTTU --> UUU{图表生成成功?}
    UUU -->|是| VVV[发送chart_generated事件]
    UUU -->|否| WWW[跳过图表生成]
    VVV --> VVVX[保存图表到数据库]
    VVVX --> XXX[记录执行结果]
    WWW --> XXX

    %% 智能评估
    XXX --> YYY[智能评估前打断检查]
    YYY --> ZZZ{是否被打断?}
    ZZZ -->|是| FF
    ZZZ -->|否| AAAA[调用结果评估器（传递分析洞察）]
    AAAA --> AAAAB[LLM评估完成度和质量]
    AAAAB --> BBBB[发送evaluation_completed事件]
    BBBB --> BBBBC[存储评估结果到上下文]
    BBBBC --> CCCC{评估建议是否继续?}
    
    CCCC -->|否| DDDD[设置task_state=completed]
    CCCC -->|是| EEEE[继续规划]
    DDDD --> FFFF[生成最终报告]
    FFFF --> GGGG[发送report_generated事件]
    GGGG --> FF
    EEEE --> FF
    
    %% 最终答案分支
    HHH -->|action_type=final_answer| HHHH[设置task_state=completed]
    HHHH --> IIII[发送plan_created事件]
    IIII --> JJJJ[发送report_generation_started事件]
    JJJJ --> JJJJK[获取收集的分析洞察]
    JJJJK --> KKKK[调用ReportGenerator生成智能报告]
    KKKK --> KKKKL[聚合分析洞察]
    KKKKL --> KKKKM[提取图表信息]
    KKKKM --> KKKKN[构建结构化洞察呈现]
    KKKKN --> KKKKO[生成最终报告内容]
    KKKKO --> LLLL[更新分析记录状态为completed]
    LLLL --> MMMM[发送report_generated事件]
    MMMM --> FF
    
    %% 循环结束条件
    VV -->|completed| NNNN[发送completed事件]
    VV -->|failed| OOOO[发送error事件]
    
    NNNN --> PPPP[清理上下文缓存]
    OOOO --> PPPP
    PPPP --> QQQQ[分析结束]
    
    %% 错误处理
    GGG -.-> RRRR[LLM调用失败]
    PPP -.-> RRRR
    QQQH -.-> RRRR[洞察提取失败]
    TTTU -.-> RRRR[图表分析失败]
    AAAAB -.-> RRRR[评估分析失败]
    KKKK -.-> RRRR[报告生成失败]

    RRRR --> SSSS[发送error事件]
    SSSS --> SSSST[回退到原有方法]
    SSSST --> TTTT[设置task_state=failed]
    TTTT --> FF
    
    %% 样式定义
    classDef userAction fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef systemProcess fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef llmProcess fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef eventProcess fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef errorProcess fill:#ffebee,stroke:#b71c1c,stroke-width:2px
    classDef loopProcess fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef interruptProcess fill:#fff9c4,stroke:#f57f17,stroke-width:2px
    classDef analysisProcess fill:#e3f2fd,stroke:#0d47a1,stroke-width:2px
    classDef chartProcess fill:#f1f8e9,stroke:#2e7d32,stroke-width:2px
    classDef reportProcess fill:#fce4ec,stroke:#880e4f,stroke-width:2px

    class A,DD,JJ,KK userAction
    class C,D,E,F,G,H,I,J,U,W,Y,S,UU,LLL,PPP,XXX,VVVX systemProcess
    class AA,GGG,QQQH,TTTU,AAAAB,KKKK,M,N,O,Q llmProcess
    class T,V,X,Z,BB,CC,EE,OOO,QQQ,VVV,BBBB,GGGG,MMMM,NNNN eventProcess
    class RRRR,SSSS,SSSST,TTTT,OOOO errorProcess
    class FF,VV,HH,NN,RR,XX,YY,JJJ,MMM,SSS,ZZZ,CCCC,QQQB loopProcess
    class GG,WW,III,RRR,YYY,QQQA,II,TT,SS interruptProcess
    class QQQC,QQQD,QQQE,QQQF,QQQG,QQQI,QQQJ,QQQK analysisProcess
    class TTTU,VVV,VVVX chartProcess
    class JJJJK,KKKKL,KKKKM,KKKKN,KKKKO,LLLL reportProcess

---

# 最新多轮分析流程详细说明

## 🔄 **核心流程概述**

本流程图展示了最新的多轮分析系统的完整执行流程，包含了专业数据分析、智能图表生成和增强报告生成等关键功能。

## 📊 **关键新增功能**

### **1. 专业数据分析阶段（QQQC-QQQK）**
- **QQQC**: 调用ResultAnalyzer进行专业分析
- **QQQD**: 计算统计摘要（总行数、列信息、数值列、分类列）
- **QQQE**: 评估数据质量（完整性、一致性、准确性）
- **QQQF**: 检测模式和异常（业务模式识别，移除技术性异常）
- **QQQG**: 分析相关性（智能过滤显而易见的技术关联）
- **QQQH**: LLM提取业务洞察（商业价值导向）
- **QQQI**: 构建AnalysisResult对象
- **QQQJ**: 收集洞察到analysis_insights_collection
- **QQQK**: 保存分析摘要到analysis_results_history

### **2. 智能图表生成增强（TTTU）**
- **TTTU**: LLM分析数据适合性（判断是否适合生成图表）
- **VVVX**: 保存图表到数据库（持久化存储）

### **3. 增强报告生成（JJJJK-KKKKO）**
- **JJJJK**: 获取收集的分析洞察（analysis_insights_collection）
- **KKKKL**: 聚合分析洞察（去重、排序、分类）
- **KKKKM**: 提取图表信息（从数据库和上下文）
- **KKKKN**: 构建结构化洞察呈现（专业格式化）
- **KKKKO**: 生成最终报告内容（包含洞察和图表）

## 🎯 **数据流转关系**

### **洞察收集链路**
```
工具执行结果 → 专业数据分析 → AnalysisResult对象 → analysis_insights_collection → 报告生成
```

### **图表生成链路**
```
工具执行结果 → 图表适合性分析 → 图表配置生成 → 数据库存储 → 报告引用
```

### **报告生成链路**
```
收集的洞察 + 图表信息 → 聚合处理 → 结构化呈现 → 最终报告
```

## 🔧 **错误处理机制**

- **QQQH**: 洞察提取失败 → 回退到基础分析
- **TTTU**: 图表分析失败 → 跳过图表生成
- **AAAAB**: 评估分析失败 → 使用默认评估
- **KKKK**: 报告生成失败 → 回退到原有方法

## 🎨 **颜色编码说明**

- **蓝色（userAction）**: 用户交互节点
- **紫色（systemProcess）**: 系统处理节点
- **橙色（llmProcess）**: LLM调用节点
- **绿色（eventProcess）**: 事件发送节点
- **红色（errorProcess）**: 错误处理节点
- **浅绿（loopProcess）**: 循环控制节点
- **黄色（interruptProcess）**: 打断处理节点
- **浅蓝（analysisProcess）**: 专业分析节点
- **深绿（chartProcess）**: 图表生成节点
- **粉色（reportProcess）**: 报告生成节点

## 📈 **性能优化点**

1. **并行处理**: 图表生成与评估可并行执行
2. **缓存机制**: 洞察结果缓存到上下文
3. **错误恢复**: 多层次的错误处理和回退机制
4. **资源管理**: 数据库连接和LLM调用的优化管理

## 🔍 **调试和监控**

- 每个关键节点都有详细的日志记录
- 洞察收集过程有调试输出
- 报告生成失败有完整的错误追踪
- Token使用情况的异步记录