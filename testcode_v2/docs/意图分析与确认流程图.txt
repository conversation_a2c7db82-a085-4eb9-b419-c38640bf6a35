graph TD
    A[用户输入查询] --> B[开始意图分析流程]
    B --> C[构建意图分析上下文]
    
    C --> D{是否有历史上下文?}
    D -->|是| E[添加历史轮次分析摘要]
    D -->|否| F[仅使用当前查询]
    
    E --> G[构建完整意图分析提示词]
    F --> G
    
    G --> H[调用LLM进行意图分析]
    H --> I[LLM返回JSON结构化结果]
    
    I --> J{JSON格式是否正确?}
    J -->|否| K[修复JSON格式错误]
    J -->|是| L[解析意图分析结果]
    
    K --> L
    L --> M[提取关键信息]
    
    M --> N["intent_description<br/>意图描述"]
    M --> O["execution_steps<br/>执行步骤规划"]
    M --> P["needs_clarification<br/>是否需要澄清"]
    M --> Q["clarification_questions<br/>澄清问题列表"]
    
    P --> R{需要澄清?}
    R -->|是| S[构建澄清问题界面]
    R -->|否| T[直接显示执行步骤]
    
    S --> U[显示澄清提示语]
    S --> V[显示选择题选项]
    S --> W[提供自定义输入框]
    
    U --> X[前端意图确认界面]
    V --> X
    W --> X
    T --> X
    
    X --> Y[用户交互选择]
    Y --> Z{用户操作类型}
    
    Z -->|直接确认| AA[无额外调整]
    Z -->|回答澄清问题| BB[收集澄清答案]
    Z -->|添加补充说明| CC[收集用户补充]
    Z -->|取消分析| DD[取消整个分析]
    
    BB --> EE[处理选择题答案]
    BB --> FF[处理自定义答案]
    CC --> GG[收集用户文本输入]
    
    EE --> HH[构建澄清响应文本]
    FF --> HH
    GG --> II[构建补充说明文本]
    
    HH --> JJ[合并所有用户调整]
    II --> JJ
    AA --> JJ
    
    JJ --> KK[生成最终intent_adjustment]
    KK --> LL[发送intent_confirmation_completed事件]
    
    LL --> MM[更新意图分析结果]
    MM --> NN{包含用户修正?}
    
    NN -->|是| OO["标记为已整合用户补充"]
    NN -->|否| PP["标记为用户已确认"]
    
    OO --> QQ[构建IntentAnalysisContext]
    PP --> QQ
    
    QQ --> RR[添加到执行上下文]
    RR --> SS[继续执行分析流程]
    
    DD --> TT[清理分析状态]
    TT --> UU[结束分析]
    
    subgraph "意图分析核心组件"
        VV["_analyze_user_intent方法"]
        WW["意图分析提示词模板"]
        XX["JSON格式修复器"]
        YY["IntentAnalysisContextBuilder"]
    end
    
    subgraph "前端确认界面组件"
        ZZ["意图描述展示"]
        AAA["执行步骤时间线"]
        BBB["澄清问题表单"]
        CCC["用户补充输入框"]
        DDD["确认/取消按钮"]
    end