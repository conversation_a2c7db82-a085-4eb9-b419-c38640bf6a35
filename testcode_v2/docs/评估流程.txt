graph TD
    A["🔧 工具执行完成"] --> B["📋 调用ResultEvaluator.evaluate_completion"]
    B --> C["🔍 数据合理性检查<br/>• 数值合理性<br/>• 逻辑一致性<br/>• 业务常识验证<br/>• 异常值识别"]
    B --> D["📊 数据完整性评估<br/>• 覆盖度分析<br/>• 样本充分性<br/>• 时间范围完整性<br/>• 关联数据完整性"]
    B --> E["🎯 查询策略验证<br/>• SQL语法正确性<br/>• 筛选条件合理性<br/>• 聚合逻辑验证<br/>• 性能考虑"]
    B --> F["🔧 问题诊断<br/>• 根因分析<br/>• 问题分类<br/>• 影响评估<br/>• 解决方案建议"]
    
    C --> G["💾 存储评估结果到ExecutionContext"]
    D --> G
    E --> G
    F --> G
    
    G --> H["📝 记录评估信息到分析日志"]
    H --> I{"🤔 评估建议是否继续?"}
    
    I -->|"❌ 否<br/>数据质量通过 且<br/>完整性分数 >= 0.8"| J["📄 生成最终报告<br/>• 智能评估触发结束<br/>• 包含评估信息的推理"]
    
    I -->|"✅ 是<br/>需要补充数据或<br/>修正查询策略"| K["🔄 继续下一轮规划"]
    
    K --> L["🏗️ EvaluationContextBuilder<br/>注入评估结果"]
    L --> M["📋 构建数据质量与策略评估报告<br/>• 数据质量问题<br/>• 查询策略问题<br/>• 数据异常分析<br/>• 问题诊断与建议"]
    M --> N["🎯 基于评估结果优化规划策略<br/>• 调整查询方法<br/>• 补充数据维度<br/>• 修正策略问题"]
    
    N --> O["⚙️ 执行优化后的工具"]
    O --> A
    
    P["⚠️ 评估异常"] --> Q["🛡️ 保守评估策略<br/>• should_continue: True<br/>• completion_confidence: 0.5<br/>• data_validation_passed: False"]
    Q --> K
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#fff3e0
    style D fill:#fff3e0
    style E fill:#fff3e0
    style F fill:#fff3e0
    style G fill:#e8f5e8
    style H fill:#e8f5e8
    style I fill:#fff9c4
    style J fill:#ffebee
    style K fill:#e3f2fd
    style L fill:#f1f8e9
    style M fill:#f1f8e9
    style N fill:#f1f8e9
    style O fill:#e1f5fe
    style P fill:#ffebee
    style Q fill:#ffebee