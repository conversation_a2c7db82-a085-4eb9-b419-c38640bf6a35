# 后台管理系统部署指南

## 概述

本文档描述如何部署新开发的后台管理系统。该系统与现有用户系统完全独立，提供用户管理、系统管理等功能。

## 系统架构

```
用户端路由: http://localhost:3000/           (原有功能保持不变)
管理端路由: http://localhost:3000/admin/     (新增管理员功能)
```

- 完全独立的认证系统
- 独立的数据表和API端点
- 前端路由自动区分用户端和管理端

## 部署步骤

### 2. 创建管理员账户

#### 2.1 运行初始化脚本
```bash
python create_admin.py
```

#### 2.2 验证管理员账户
```sql
SELECT * FROM administrators WHERE username = 'admin';
```

### 3. 后端部署

#### 3.1 确保依赖已安装
```bash
pip install -r requirements.txt
```

#### 3.2 重启后端服务
```bash
# 如果使用uvicorn
uvicorn main:app --reload --host 0.0.0.0 --port 8301

# 如果使用gunicorn
gunicorn main:app -w 4 -k uvicorn.workers.UnicornWorker --bind 0.0.0.0:8301
```

### 4. 前端部署

#### 4.1 确保依赖已安装
```bash
cd frontend
npm install
```

#### 4.2 重启前端服务
```bash
npm start
```

## 访问系统

### 用户端（原有功能）
- URL: `http://localhost:3000/`
- 登录: 使用原有用户账户

### 管理端（新增功能）
- URL: `http://localhost:3000/admin/login`
- 默认账户:
  - 用户名: `admin`
  - 密码: `2Olr3cV@#($EmR9`

## 功能验证

### 1. 管理员登录测试
1. 访问 `http://localhost:3000/admin/login`
2. 使用默认账户登录
3. 验证能否成功进入管理后台

### 2. 用户管理功能测试
1. 在管理后台点击"用户管理"
2. 尝试创建新用户
3. 尝试编辑用户信息
4. 验证用户层级关系功能

### 3. 权限隔离测试
1. 确认管理员token与用户token完全独立
2. 确认管理员无法访问用户端功能
3. 确认用户无法访问管理端功能

