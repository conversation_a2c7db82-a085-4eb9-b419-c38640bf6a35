# 基础镜像 - 包含所有重型依赖和安装步骤
FROM python:3.10.14-bullseye AS base

WORKDIR /app

# 使用清华镜像替换Debian源，加速系统依赖下载
RUN echo "deb https://mirrors.tuna.tsinghua.edu.cn/debian/ bullseye main contrib non-free" > /etc/apt/sources.list && \
    echo "deb https://mirrors.tuna.tsinghua.edu.cn/debian/ bullseye-updates main contrib non-free" >> /etc/apt/sources.list && \
    echo "deb https://mirrors.tuna.tsinghua.edu.cn/debian/ bullseye-backports main contrib non-free" >> /etc/apt/sources.list && \
    echo "deb https://mirrors.tuna.tsinghua.edu.cn/debian-security bullseye-security main contrib non-free" >> /etc/apt/sources.list

# 安装Oracle Instant Client依赖、Nginx和其他必要的工具
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    libaio1 \
    wget \
    unzip \
    ca-certificates \
    curl \
    nginx \
    supervisor \
    && rm -rf /var/lib/apt/lists/*

# 下载并安装Oracle Instant Client（版本*********.04）
RUN mkdir -p /opt/oracle && \
    cd /opt/oracle && \
    wget https://download.oracle.com/otn_software/linux/instantclient/2380000/instantclient-basic-linux.x64-*********.04.zip && \
    unzip instantclient-basic-linux.x64-*********.04.zip && \
    rm instantclient-basic-linux.x64-*********.04.zip && \
    ln -s /opt/oracle/instantclient_23_8 /opt/oracle/instantclient && \
    echo /opt/oracle/instantclient > /etc/ld.so.conf.d/oracle-instantclient.conf && \
    ldconfig

# 配置环境变量以使cx_Oracle能找到Oracle客户端
ENV ORACLE_HOME=/opt/oracle/instantclient
ENV LD_LIBRARY_PATH=$ORACLE_HOME
ENV PATH=$PATH:$ORACLE_HOME

# 预安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt

# 创建nginx目录和日志目录
RUN mkdir -p /var/www/html && \
    mkdir -p /var/log/supervisor

WORKDIR /app 