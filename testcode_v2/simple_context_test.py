#!/usr/bin/env python3
"""
简化的上下文测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.session import get_db
from app.services.conversation_service import ConversationService
from app.models.conversation import Conversation, ConversationContext, ContextType
import logging

logging.basicConfig(level=logging.INFO)
log = logging.getLogger(__name__)

def simple_context_test():
    """简化的上下文测试"""
    
    db = next(get_db())
    
    try:
        # 1. 直接创建会话记录
        log.info("创建测试会话...")
        conversation = Conversation(
            id="simple_test_conv",
            project_id="2f3e1718-7a2e-4c67-a573-eb7ae93a7ca9",
            title="简化测试会话",
            status="active"
        )
        
        db.add(conversation)
        db.commit()
        log.info("会话创建成功")
        
        # 2. 直接创建上下文记录
        log.info("创建上下文记录...")
        
        # 查询上下文
        query_context = ConversationContext(
            conversation_id="simple_test_conv",
            round_number=1,
            context_type="query",  # 使用字符串值
            context_data={"query": "查询ABC公司信息", "intent": "分析请求"}
        )
        
        # 结果上下文
        result_context = ConversationContext(
            conversation_id="simple_test_conv",
            round_number=1,
            context_type="result",  # 使用字符串值
            context_data={
                "query": "查询ABC公司信息",
                "summary": "ABC公司是一家有限责任公司...",
                "insights": ["公司注册资本较大", "经营状态正常"],
                "full_result": "完整的分析结果..."
            }
        )
        
        # 工具使用上下文
        tool_context = ConversationContext(
            conversation_id="simple_test_conv",
            round_number=1,
            context_type="tool_usage",  # 使用字符串值
            context_data={
                "tools": ["公司基本信息查询"],
                "data_sources": ["FINRISK_BASE_INFO"]
            }
        )
        
        db.add(query_context)
        db.add(result_context)
        db.add(tool_context)
        db.commit()
        log.info("上下文记录创建成功")
        
        # 3. 测试获取上下文
        log.info("测试获取上下文...")
        conversation_service = ConversationService(db)
        
        context_data = conversation_service.get_conversation_context("simple_test_conv")
        
        log.info(f"获取到的上下文:")
        log.info(f"  previous_queries: {context_data.previous_queries}")
        log.info(f"  previous_results: {len(context_data.previous_results)} 个")
        log.info(f"  key_findings: {context_data.key_findings}")
        log.info(f"  tools_used: {context_data.tools_used}")
        log.info(f"  data_sources_used: {context_data.data_sources_used}")
        
        if context_data.previous_results:
            result = context_data.previous_results[0]
            log.info(f"  第一个结果: 轮次{result.round_number}, 查询'{result.query}', 摘要'{result.summary}'")
        
        # 4. 清理测试数据
        log.info("清理测试数据...")
        db.query(ConversationContext).filter(
            ConversationContext.conversation_id == "simple_test_conv"
        ).delete()
        db.delete(conversation)
        db.commit()
        
        log.info("测试完成")
        
    except Exception as e:
        log.error(f"测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    simple_context_test() 