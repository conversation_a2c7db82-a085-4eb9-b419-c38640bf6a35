/*
 Navicat Premium Data Transfer

 Source Server         : 本地
 Source Server Type    : MySQL
 Source Server Version : 90300
 Source Host           : localhost:3306
 Source Schema         : ai_data_qa

 Target Server Type    : MySQL
 Target Server Version : 90300
 File Encoding         : 65001

 Date: 09/06/2025 14:07:45
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for analyses
-- ----------------------------
DROP TABLE IF EXISTS `analyses`;
CREATE TABLE `analyses` (
  `id` varchar(36) NOT NULL,
  `query` text NOT NULL COMMENT '用户查询',
  `intent_analysis` json DEFAULT NULL COMMENT '意图分析结果',
  `result` text COMMENT '分析报告',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `project_id` varchar(36) NOT NULL COMMENT '所属项目ID',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `conversation_id` varchar(50) DEFAULT NULL COMMENT '所属会话ID',
  `round_number` int DEFAULT '1' COMMENT '轮次编号',
  `context_tokens_used` int DEFAULT '0' COMMENT '上下文消耗的token数',
  `context_summary` text COMMENT '本轮次使用的上下文摘要',
  `analysis_steps` json DEFAULT NULL COMMENT '分析步骤详情',
  `tool_results` json DEFAULT NULL COMMENT '工具执行结果映射',
  `mcp_results` json DEFAULT NULL COMMENT 'MCP执行结果',
  `status` enum('running','completed','failed') DEFAULT 'running' COMMENT '分析状态',
  `execution_time` float DEFAULT NULL COMMENT '总执行时间(秒)',
  `context_relevance` float DEFAULT NULL COMMENT '上下文相关性评分',
  `llm_summary` text COMMENT 'LLM生成的本轮次总结',
  PRIMARY KEY (`id`),
  KEY `idx_analysis_project` (`project_id`),
  KEY `idx_analysis_created` (`created_at`),
  KEY `idx_conversation_id` (`conversation_id`),
  KEY `idx_round_number` (`round_number`),
  KEY `idx_status` (`status`),
  KEY `idx_context_relevance` (`context_relevance`),
  KEY `idx_execution_time` (`execution_time`),
  CONSTRAINT `analyses_ibfk_1` FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`) ON DELETE CASCADE,
  CONSTRAINT `analyses_ibfk_conversation` FOREIGN KEY (`conversation_id`) REFERENCES `conversations` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='分析记录表';

-- ----------------------------
-- Records of analyses
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for analysis_step_details
-- ----------------------------
DROP TABLE IF EXISTS `analysis_step_details`;
CREATE TABLE `analysis_step_details` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `analysis_id` varchar(36) NOT NULL COMMENT '所属分析ID',
  `step_id` varchar(100) NOT NULL COMMENT '步骤ID',
  `step_name` varchar(500) NOT NULL COMMENT '步骤名称',
  `step_type` enum('system','tool','planning','report','error','interaction') NOT NULL COMMENT '步骤类型',
  `status` enum('process','finish','error','wait') NOT NULL COMMENT '步骤状态',
  `step_order` int NOT NULL COMMENT '步骤顺序',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `execution_time` float DEFAULT NULL COMMENT '执行时间(毫秒)',
  `description` text COMMENT '步骤描述',
  `tool_name` varchar(200) DEFAULT NULL COMMENT '工具名称(如果是工具步骤)',
  `parameters` json DEFAULT NULL COMMENT '执行参数(如果是工具步骤)',
  `has_result` tinyint(1) DEFAULT '0' COMMENT '是否有执行结果',
  `result_key` varchar(100) DEFAULT NULL COMMENT '结果键值',
  `has_intent_data` tinyint(1) DEFAULT '0' COMMENT '是否有意图分析数据',
  `has_report` tinyint(1) DEFAULT '0' COMMENT '是否有报告数据',
  `has_chart_data` tinyint(1) DEFAULT '0' COMMENT '是否有图表数据',
  `plan_data` json DEFAULT NULL COMMENT '规划数据',
  `reasoning` text COMMENT '执行推理过程',
  `error_message` text COMMENT '错误信息',
  `interaction_type` varchar(50) DEFAULT NULL COMMENT '交互类型: intent_confirmation/clarification/chart_display',
  `interaction_data` json DEFAULT NULL COMMENT '交互相关数据',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_analysis_step_order` (`analysis_id`,`step_order`),
  KEY `idx_step_type` (`step_type`),
  KEY `idx_step_status` (`status`),
  KEY `idx_step_id` (`step_id`),
  KEY `idx_interaction_type` (`interaction_type`),
  CONSTRAINT `analysis_step_details_ibfk_1` FOREIGN KEY (`analysis_id`) REFERENCES `analyses` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=119 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='分析步骤详情表';

-- ----------------------------
-- Records of analysis_step_details
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for conversation_contexts
-- ----------------------------
DROP TABLE IF EXISTS `conversation_contexts`;
CREATE TABLE `conversation_contexts` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `conversation_id` varchar(50) NOT NULL,
  `round_number` int NOT NULL,
  `context_type` enum('query','result','insight','data_source','tool_usage') NOT NULL COMMENT '上下文类型',
  `context_data` json NOT NULL COMMENT '上下文数据',
  `relevance_score` float DEFAULT '1' COMMENT '上下文相关性评分',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_conversation_round` (`conversation_id`,`round_number`),
  KEY `idx_context_type` (`context_type`),
  KEY `idx_relevance_score` (`relevance_score`),
  CONSTRAINT `conversation_contexts_ibfk_1` FOREIGN KEY (`conversation_id`) REFERENCES `conversations` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=367 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='会话上下文表';

-- ----------------------------
-- Records of conversation_contexts
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for conversations
-- ----------------------------
DROP TABLE IF EXISTS `conversations`;
CREATE TABLE `conversations` (
  `id` varchar(50) NOT NULL,
  `project_id` varchar(50) NOT NULL,
  `title` varchar(500) DEFAULT NULL COMMENT '会话标题',
  `context_summary` text COMMENT '会话级别的上下文摘要',
  `total_rounds` int DEFAULT '0' COMMENT '总轮次数',
  `total_tokens_used` int DEFAULT '0' COMMENT '总消耗token数',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `status` enum('ACTIVE','ARCHIVED','PAUSED') DEFAULT 'ACTIVE',
  PRIMARY KEY (`id`),
  KEY `idx_project_id` (`project_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `conversations_ibfk_1` FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='多轮分析会话表';

-- ----------------------------
-- Records of conversations
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for llm_models
-- ----------------------------
DROP TABLE IF EXISTS `llm_models`;
CREATE TABLE `llm_models` (
  `id` varchar(36) NOT NULL,
  `name` varchar(100) NOT NULL COMMENT '模型名称',
  `description` text COMMENT '模型描述',
  `model_name` varchar(100) NOT NULL COMMENT '模型标识符',
  `api_key` varchar(500) NOT NULL COMMENT 'API密钥',
  `base_url` varchar(500) DEFAULT NULL COMMENT 'API基础URL',
  `config` json DEFAULT NULL COMMENT '额外配置参数',
  `is_active` tinyint(1) DEFAULT NULL COMMENT '是否启用',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `ix_llm_models_id` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for tool_executions
-- ----------------------------
DROP TABLE IF EXISTS `tool_executions`;
CREATE TABLE `tool_executions` (
  `id` varchar(36) NOT NULL,
  `parameters` json NOT NULL COMMENT '执行参数',
  `result` json DEFAULT NULL COMMENT '执行结果',
  `execution_time` float DEFAULT NULL COMMENT '执行时间(秒)',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `analysis_id` varchar(36) NOT NULL COMMENT '所属分析ID',
  `tool_id` varchar(36) NOT NULL COMMENT '工具ID',
  `step_id` varchar(50) DEFAULT NULL COMMENT '分析步骤ID',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` enum('running','success','failed') DEFAULT 'running' COMMENT '执行状态',
  `error_message` text COMMENT '错误信息',
  `step_order` int DEFAULT NULL COMMENT '步骤顺序',
  `reasoning` text COMMENT '执行推理过程',
  PRIMARY KEY (`id`),
  KEY `idx_execution_analysis` (`analysis_id`),
  KEY `idx_execution_tool` (`tool_id`),
  KEY `idx_status` (`status`),
  KEY `idx_step_order` (`step_order`),
  KEY `idx_step_id` (`step_id`),
  CONSTRAINT `tool_executions_ibfk_1` FOREIGN KEY (`analysis_id`) REFERENCES `analyses` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='工具执行记录表';

-- ----------------------------
-- Records of tool_executions
-- ----------------------------
BEGIN;
COMMIT;

SET FOREIGN_KEY_CHECKS = 1;
