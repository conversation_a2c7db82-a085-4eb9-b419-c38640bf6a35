-- 添加分析状态相关字段的数据库迁移脚本
-- 执行前请备份数据库

-- 1. 为 analyses 表添加新字段
ALTER TABLE analyses 
ADD COLUMN analysis_steps JSON COMMENT '分析步骤详情',
ADD COLUMN tool_results JSON COMMENT '工具执行结果映射',
ADD COLUMN mcp_results JSON COMMENT 'MCP执行结果',
ADD COLUMN status VARCHAR(20) DEFAULT 'running' COMMENT '分析状态: running/completed/failed',
ADD COLUMN execution_time FLOAT COMMENT '总执行时间(秒)',
ADD COLUMN context_relevance FLOAT COMMENT '上下文相关性评分',
ADD COLUMN llm_summary TEXT COMMENT 'LLM生成的本轮次总结';

-- 2. 为 tool_executions 表添加新字段
ALTER TABLE tool_executions
ADD COLUMN status VARCHAR(20) DEFAULT 'success' COMMENT '执行状态: success/failed/running',
ADD COLUMN error_message TEXT COMMENT '错误信息',
ADD COLUMN step_order INT COMMENT '步骤顺序',
ADD COLUMN reasoning TEXT COMMENT '执行推理过程';

-- 3. 创建索引以提高查询性能
CREATE INDEX idx_analyses_status ON analyses(status);
CREATE INDEX idx_analyses_conversation_round ON analyses(conversation_id, round_number);
CREATE INDEX idx_tool_executions_status ON tool_executions(status);
CREATE INDEX idx_tool_executions_step_order ON tool_executions(analysis_id, step_order);

-- 4. 更新现有数据的状态（可选）
UPDATE analyses SET status = 'completed' WHERE result IS NOT NULL AND result != '';
UPDATE analyses SET status = 'failed' WHERE result IS NULL OR result = '';

COMMIT; 