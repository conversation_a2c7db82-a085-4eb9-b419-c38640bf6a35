-- 权限控制系统重构迁移脚本
-- 执行日期: 2025-01-XX
-- 说明: 从两级用户层级系统升级为基于角色和企业的权限控制系统

USE ai_data_qa;

-- ================================
-- 第一步：创建新表结构
-- ================================

-- 企业组织表
CREATE TABLE `organizations` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '企业ID',
  `name` varchar(100) NOT NULL COMMENT '企业名称',
  `description` text COMMENT '企业描述',
  -- 限额配置（概念性，暂不实时更新）
  `token_limit` bigint DEFAULT 1000000 COMMENT 'Token消耗限额（月）',
  `user_limit` int DEFAULT 50 COMMENT '用户数量限额',
  `project_limit` int DEFAULT 20 COMMENT '项目数量限额',
  -- 状态管理
  `status` tinyint DEFAULT 1 COMMENT '状态：1=正常 0=禁用',
  `expire_at` datetime DEFAULT NULL COMMENT '过期时间',

  -- 联系信息
  `contact_email` varchar(100) COMMENT '联系邮箱',
  `contact_phone` varchar(20) COMMENT '联系电话',
  `address` text COMMENT '地址',

  -- 时间戳
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_org_name` (`name`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='企业组织表';

-- 系统角色表
CREATE TABLE `system_roles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '角色名称',
  `code` varchar(50) NOT NULL COMMENT '角色代码',
  `description` text COMMENT '角色描述',
  `level` int NOT NULL COMMENT '角色级别，数字越小权限越高',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_role_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统角色表';

-- 项目角色表
CREATE TABLE `project_roles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '项目角色名称',
  `code` varchar(50) NOT NULL COMMENT '项目角色代码',
  `description` text COMMENT '角色描述',
  `permissions` json NOT NULL COMMENT '权限列表',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_project_role_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目角色表';

-- 项目成员表
CREATE TABLE `project_members` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `project_id` varchar(36) NOT NULL COMMENT '项目ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `project_role_id` int(11) NOT NULL COMMENT '项目角色ID',
  `invited_by` int(11) DEFAULT NULL COMMENT '邀请人ID',
  `joined_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
  `status` enum('ACTIVE','INACTIVE') DEFAULT 'ACTIVE' COMMENT '成员状态',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_project_user` (`project_id`, `user_id`),
  KEY `idx_user_projects` (`user_id`),
  KEY `idx_project_role` (`project_role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目成员表';

-- ================================
-- 第二步：修改现有表结构
-- ================================

-- 修改用户表，添加角色和企业字段
ALTER TABLE `users` 
ADD COLUMN `role_id` int(11) DEFAULT 3 COMMENT '系统角色ID，默认为普通用户',
ADD COLUMN `org_id` int(11) NOT NULL COMMENT '所属企业ID（普通用户必须归属企业）',
ADD COLUMN `can_create_project` tinyint(1) DEFAULT 1 COMMENT '是否可以创建项目';

-- 修改项目表，添加所有者、可见性和企业字段
ALTER TABLE `projects`
ADD COLUMN `owner_id` int(11) NOT NULL DEFAULT 1 COMMENT '项目所有者ID',
ADD COLUMN `visibility` enum('PRIVATE','PUBLIC') DEFAULT 'PRIVATE' COMMENT '项目可见性',
ADD COLUMN `org_id` int(11) NOT NULL COMMENT '所属企业ID（继承自创建者）';

ALTER TABLE `conversations`
ADD COLUMN `user_id` int(11) NOT NULL DEFAULT 1 COMMENT '会话创建者ID',
ADD INDEX `idx_conversations_user_id` (`user_id`);

-- ================================
-- 第三步：插入基础数据
-- ================================

-- 插入系统角色
INSERT INTO system_roles (id, name, code, description, level) VALUES
(1, '超级管理员', 'SUPER_ADMIN', '系统运维管理，主要负责数据分析平台本身的运维', 1),
(2, '企业管理员', 'ORG_ADMIN', '管理企业内所有项目和用户的项目权限', 2),
(3, '普通用户', 'NORMAL_USER', '基础用户，可创建和管理自己的项目', 3);

-- 插入项目角色
INSERT INTO project_roles (id, name, code, description, permissions) VALUES
(1, '项目所有者', 'PROJECT_OWNER', '项目创建者，拥有项目完全控制权限', 
 '["view", "edit", "delete", "execute", "manage_datasource", "manage_tools", "manage_members"]'),
(2, '项目协作者', 'PROJECT_COLLABORATOR', '可进行分析操作、查看结果、管理工具', 
 '["view", "execute", "manage_tools"]'),
(3, '项目观察者', 'PROJECT_OBSERVER', '可查看项目内容、执行分析操作', 
 '["view", "execute"]');

-- 创建默认企业（用于数据迁移）
INSERT INTO organizations (id, name, code, description) VALUES
(1, '默认企业', 'DEFAULT_ORG', '数据迁移时创建的默认企业');

-- ================================
-- 第四步：数据迁移
-- ================================

-- 更新现有用户数据
UPDATE users SET 
    role_id = CASE 
        WHEN is_superuser = 1 THEN 1  -- 超级管理员
        ELSE 3  -- 普通用户
    END,
    org_id = 1  -- 暂时分配到默认企业
WHERE role_id IS NULL;

-- 更新现有项目数据
UPDATE projects SET 
    owner_id = user_id,
    visibility = 'PRIVATE',  -- 默认设为私有
    org_id = (SELECT org_id FROM users WHERE users.id = projects.user_id)
WHERE owner_id IS NULL;

-- 为每个项目创建所有者成员记录
INSERT INTO project_members (project_id, user_id, project_role_id, joined_at)
SELECT p.id, p.owner_id, 1, p.created_at
FROM projects p
WHERE NOT EXISTS (
    SELECT 1 FROM project_members pm 
    WHERE pm.project_id = p.id AND pm.user_id = p.owner_id
);

-- ================================
-- 第五步：添加索引优化
-- ================================

-- 为新字段添加索引
ALTER TABLE `users` ADD KEY `idx_role_id` (`role_id`);
ALTER TABLE `users` ADD KEY `idx_org_id` (`org_id`);
ALTER TABLE `projects` ADD KEY `idx_owner_id` (`owner_id`);
ALTER TABLE `projects` ADD KEY `idx_visibility` (`visibility`);
ALTER TABLE `projects` ADD KEY `idx_project_org_id` (`org_id`);

-- ================================
-- 第六步：数据验证
-- ================================

-- 验证用户数据迁移
SELECT 'User migration check' as check_type, 
       COUNT(*) as total_users,
       COUNT(CASE WHEN role_id IS NOT NULL THEN 1 END) as users_with_role,
       COUNT(CASE WHEN org_id IS NOT NULL THEN 1 END) as users_with_org
FROM users;

-- 验证项目数据迁移
SELECT 'Project migration check' as check_type,
       COUNT(*) as total_projects,
       COUNT(CASE WHEN owner_id IS NOT NULL THEN 1 END) as projects_with_owner,
       COUNT(CASE WHEN org_id IS NOT NULL THEN 1 END) as projects_with_org
FROM projects;

-- 验证项目成员数据
SELECT 'Project members check' as check_type,
       COUNT(*) as total_members,
       COUNT(CASE WHEN project_role_id = 1 THEN 1 END) as owners
FROM project_members;

-- ================================
-- 第七步：清理旧字段（可选，建议在确认无误后执行）
-- ================================

-- 注意：以下操作会删除旧的权限控制字段，请在确认新系统运行正常后执行
-- ALTER TABLE `users` DROP COLUMN `is_superuser`;
-- ALTER TABLE `users` DROP COLUMN `pid`;

COMMIT; 