-- ===========================================
-- 整合的RBAC系统迁移脚本
-- 整合final_rbac_design.sql、add_admin_system.sql、create_juyun_org_and_assign_users.sql
-- 执行日期: 待定
-- ===========================================

-- 1. 创建企业/组织表
CREATE TABLE `organizations` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '企业ID',
  `name` varchar(100) NOT NULL COMMENT '企业名称',
  `description` text COMMENT '企业描述',
  
  -- 限额配置（概念性，暂不实时更新）
  `token_limit` bigint DEFAULT 1000000 COMMENT 'Token消耗限额（月）',
  `user_limit` int DEFAULT 50 COMMENT '用户数量限额',
  `project_limit` int DEFAULT 20 COMMENT '项目数量限额',
  
  -- 状态管理
  `status` tinyint DEFAULT 1 COMMENT '状态：1=正常 2=禁用',
  `expire_at` datetime DEFAULT NULL COMMENT '过期时间',
  
  -- 联系信息
  `contact_email` varchar(100) COMMENT '联系邮箱',
  `contact_phone` varchar(20) COMMENT '联系电话',
  `address` text COMMENT '地址',
  
  -- 时间戳
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='企业组织表';

-- 2. 创建角色表（保留JSON权限存储）
CREATE TABLE `roles` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `name` varchar(50) NOT NULL COMMENT '角色名称',
  `description` text COMMENT '角色描述',
  `level` tinyint NOT NULL DEFAULT 0 COMMENT '角色层级：0=系统级 1=企业级 2=项目级',
  
  -- 权限配置（JSON格式，保留原有逻辑）
  `permissions` json COMMENT '权限配置JSON',
  
  -- 作用域限制
  `org_id` int DEFAULT NULL COMMENT '所属企业ID（NULL表示系统级角色）',
  
  -- 状态管理
  `is_active` tinyint DEFAULT 1 COMMENT '是否启用',
  `is_system` tinyint DEFAULT 0 COMMENT '是否系统内置角色（不可删除）',
  
  -- 时间戳
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  KEY `idx_org_role` (`org_id`, `level`),
  KEY `idx_level` (`level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表';

-- 3. 创建管理员表
CREATE TABLE `administrators` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '管理员ID',
  `username` varchar(50) NOT NULL COMMENT '管理员用户名',
  `email` varchar(100) DEFAULT NULL COMMENT '管理员邮箱',
  `hashed_password` varchar(100) NOT NULL COMMENT '密码哈希',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活',
  `last_login_at` datetime DEFAULT NULL COMMENT '最后登录时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_username` (`username`),
  UNIQUE KEY `idx_email` (`email`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';

-- 项目角色表
CREATE TABLE `project_roles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '项目角色名称',
  `code` varchar(50) NOT NULL COMMENT '项目角色代码',
  `description` text COMMENT '角色描述',
  `permissions` json NOT NULL COMMENT '权限列表',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_project_role_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目角色表';

-- 项目成员表
CREATE TABLE `project_members` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `project_id` varchar(36) NOT NULL COMMENT '项目ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `project_role_id` int(11) NOT NULL COMMENT '项目角色ID',
  `invited_by` int(11) DEFAULT NULL COMMENT '邀请人ID',
  `joined_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
  `status` enum('ACTIVE','INACTIVE') DEFAULT 'ACTIVE' COMMENT '成员状态',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_project_user` (`project_id`, `user_id`),
  KEY `idx_user_projects` (`user_id`),
  KEY `idx_project_role` (`project_role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目成员表';

-- 插入项目角色
INSERT INTO project_roles (id, name, code, description, permissions) VALUES
(1, '项目所有者', 'PROJECT_OWNER', '项目创建者，拥有项目完全控制权限', 
 '["view", "edit", "delete", "execute", "manage_datasource", "manage_tools", "manage_members"]'),
(2, '项目协作者', 'PROJECT_COLLABORATOR', '可进行分析操作、查看结果、管理工具', 
 '["view", "execute", "manage_tools"]'),
(3, '项目观察者', 'PROJECT_OBSERVER', '可查看项目内容、执行分析操作', 
 '["view", "execute"]');

-- 4. 修改用户表，添加RBAC字段
ALTER TABLE `users` 
ADD COLUMN `role_id` int(11) DEFAULT 3 COMMENT '系统角色ID，默认为普通用户',
ADD COLUMN `org_id` int(11) NOT NULL COMMENT '所属企业ID（普通用户必须归属企业）',
ADD COLUMN `can_create_project` tinyint(1) DEFAULT 1 COMMENT '是否可以创建项目',
ADD KEY `idx_role_id` (`role_id`),
ADD KEY `idx_org_id` (`org_id`);

-- 5. 修改项目表，添加企业ID字段
ALTER TABLE `projects`
ADD COLUMN `owner_id` int(11) NOT NULL DEFAULT 1 COMMENT '项目所有者ID',
ADD COLUMN `visibility` enum('PRIVATE','PUBLIC') DEFAULT 'PRIVATE' COMMENT '项目可见性',
ADD COLUMN `org_id` int(11) NOT NULL COMMENT '所属企业ID（继承自创建者）',
ADD KEY `idx_org_id` (`org_id`),
ADD KEY `idx_owner_id` (`owner_id`);

-- 6. 修改会话表，添加用户ID字段
ALTER TABLE `conversations`
ADD COLUMN `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '会话创建者ID',
ADD INDEX `idx_conversations_user_id` (`user_id`);


-- ===========================================
-- 数据迁移和初始化
-- ===========================================

-- 7. 插入系统内置角色
INSERT INTO `roles` (`name`, `description`, `level`, `permissions`, `org_id`, `is_system`) VALUES
-- 系统级角色（超级用户 - 独立登录系统）
('超级管理员', '系统超级管理员，管理所有企业和系统', 0, 
 '["system:*", "org:*", "user:*", "project:*", "analysis:*"]', NULL, 1),

-- 企业级角色（企业管理员 - 独立登录，管理企业内所有资源包括项目）
('企业管理员', '企业管理员，管理企业内所有用户、项目和分析任务', 1, 
 '["org:read", "org:update", "user:*", "project:*", "analysis:*"]', NULL, 1),

-- 用户级角色（简化为两种基础角色）
('普通用户', '普通用户，可以使用项目和执行分析', 2, 
 '["project:read", "analysis:read", "analysis:write"]', NULL, 1);

-- 8. 创建聚云科技企业
INSERT INTO `organizations` (
  `id`, `name`, `description`, `token_limit`, `user_limit`, `project_limit`,
  `status`, `expire_at`, `contact_email`, `contact_phone`, `address`,
  `created_at`, `updated_at`
) VALUES (
  1, '聚云科技', '', 100000000, 100000, 50000,
  1, NULL, '', '', '',
  NOW(), NOW()
);

-- 8. 插入默认管理员账户与超级管理员）
INSERT INTO `ai_data_qa`.`administrators` 
(`username`, `email`, `hashed_password`, `is_active`, `last_login_at`, `created_at`, `updated_at`) 
VALUES ('admin', '<EMAIL>', '$2b$12$oMKiEo3fqADzhDyUToBc8.V8E7ZxMCLXbIlCLjIYf/pUSdIfmey/G', 1, now(), now(), now());
INSERT INTO `ai_data_qa`.`users` ( `username`, `email`, `hashed_password`, `is_active`, `is_superuser`, `pid`, `created_at`, `updated_at`, `role_id`, `org_id`, `can_create_project`) 
VALUES ('admin', '<EMAIL>', '$2b$12$c0acifFEiF2gVlRN4J3ZVO491zMnwlH5/cdwTH4EMhN4Bc8PuaN9O', 1, 0, 0, '2025-06-25 03:24:44', '2025-06-25 04:03:54', 1, 0, 1);
-- 9. 更新所有用户归属到聚云科技
UPDATE users SET org_id = 1 WHERE org_id = 0 OR org_id IS NULL;

-- 10. 设置decisionadmin用户的角色为企聚云科技管理员（role_id=2）
UPDATE users SET role_id = 2 WHERE username = 'decisionadmin';

-- 11. 更新所有项目归属到聚云科技
UPDATE projects SET org_id = 1, owner_id = 1, visibility = 'PUBLIC' WHERE org_id = 0 OR org_id IS NULL;

-- 12. 设置org_id字段为NOT NULL，确保企业ID不能为空
ALTER TABLE users MODIFY COLUMN org_id INT NOT NULL;
ALTER TABLE projects MODIFY COLUMN org_id INT NOT NULL; 