-- 添加用户语言偏好字段
-- 迁移脚本：add_user_language_preference.sql

-- 为用户表添加语言偏好字段
ALTER TABLE users ADD COLUMN language_preference VARCHAR(10) DEFAULT 'zh-CN' COMMENT '用户语言偏好';

-- 创建索引以优化查询性能
CREATE INDEX idx_users_language_preference ON users(language_preference);

-- 更新现有用户的默认语言为中文
UPDATE users SET language_preference = 'zh-CN' WHERE language_preference IS NULL;

-- 验证添加是否成功
SELECT 
    COLUMN_NAME, 
    DATA_TYPE, 
    IS_NULLABLE, 
    COLUMN_DEFAULT, 
    COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'users' 
AND COLUMN_NAME = 'language_preference';