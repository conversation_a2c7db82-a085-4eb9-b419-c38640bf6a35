-- 多轮分析功能数据库迁移脚本
-- 添加会话上下文管理相关表结构

-- 1. 扩展会话表，添加上下文相关字段
ALTER TABLE conversations 
ADD COLUMN context_summary TEXT COMMENT '会话级别的上下文摘要',
ADD COLUMN total_rounds INT DEFAULT 0 COMMENT '总轮次数',
ADD COLUMN total_tokens_used INT DEFAULT 0 COMMENT '总消耗token数';

-- 更新会话状态枚举，添加暂停状态
ALTER TABLE conversations 
MODIFY COLUMN status ENUM('active', 'archived', 'paused') DEFAULT 'active' COMMENT '会话状态';

-- 2. 创建会话上下文表
CREATE TABLE conversation_contexts (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    conversation_id VARCHAR(50) NOT NULL COMMENT '会话ID',
    round_number INT NOT NULL COMMENT '轮次编号',
    context_type ENUM('query', 'result', 'insight', 'data_source', 'tool_usage') NOT NULL COMMENT '上下文类型',
    context_data JSON NOT NULL COMMENT '上下文数据',
    relevance_score FLOAT DEFAULT 1.0 COMMENT '上下文相关性评分',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    -- 外键约束
    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE,
    
    -- 索引
    INDEX idx_conversation_round (conversation_id, round_number),
    INDEX idx_context_type (context_type),
    INDEX idx_relevance_score (relevance_score),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会话上下文表';

-- 3. 扩展分析表，添加上下文相关字段（如果还没有的话）
-- 检查字段是否存在，如果不存在则添加
SET @sql = (
    SELECT IF(
        (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
         WHERE TABLE_SCHEMA = DATABASE() 
         AND TABLE_NAME = 'analyses' 
         AND COLUMN_NAME = 'context_summary') = 0,
        'ALTER TABLE analyses ADD COLUMN context_summary TEXT COMMENT "轮次使用的上下文摘要"',
        'SELECT "context_summary column already exists"'
    )
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 为现有会话初始化统计数据
UPDATE conversations c 
SET total_rounds = (
    SELECT COUNT(*) 
    FROM analyses a 
    WHERE a.conversation_id = c.id
)
WHERE c.total_rounds = 0;

-- 5. 创建索引优化查询性能
CREATE INDEX idx_analyses_conversation_round ON analyses(conversation_id, round_number);
CREATE INDEX idx_conversations_project_status ON conversations(project_id, status);
CREATE INDEX idx_conversations_updated_at ON conversations(updated_at DESC);

-- 6. 插入示例上下文数据（可选，用于测试）
-- INSERT INTO conversation_contexts (conversation_id, round_number, context_type, context_data, relevance_score)
-- VALUES 
-- ('test_conv_001', 1, 'query', '{"query": "测试查询", "intent": "分析请求"}', 1.0),
-- ('test_conv_001', 1, 'result', '{"summary": "测试结果摘要", "insights": ["测试洞察1", "测试洞察2"]}', 0.9);

-- 7. 创建视图，方便查询会话统计信息
CREATE OR REPLACE VIEW conversation_stats AS
SELECT 
    c.id,
    c.project_id,
    c.title,
    c.status,
    c.total_rounds,
    c.total_tokens_used,
    c.created_at,
    c.updated_at,
    COUNT(DISTINCT cc.id) as context_records_count,
    MAX(a.created_at) as last_analysis_at
FROM conversations c
LEFT JOIN conversation_contexts cc ON c.id = cc.conversation_id
LEFT JOIN analyses a ON c.id = a.conversation_id
GROUP BY c.id, c.project_id, c.title, c.status, c.total_rounds, c.total_tokens_used, c.created_at, c.updated_at;

-- 8. 创建存储过程，用于清理过期的上下文数据
DELIMITER //
CREATE PROCEDURE CleanupExpiredContexts(IN days_to_keep INT DEFAULT 30)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE conv_id VARCHAR(50);
    DECLARE context_count INT;
    
    -- 声明游标
    DECLARE conv_cursor CURSOR FOR 
        SELECT c.id 
        FROM conversations c 
        WHERE c.updated_at < DATE_SUB(NOW(), INTERVAL days_to_keep DAY)
        AND c.status = 'archived';
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    -- 开始事务
    START TRANSACTION;
    
    OPEN conv_cursor;
    
    read_loop: LOOP
        FETCH conv_cursor INTO conv_id;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- 删除过期会话的上下文数据
        DELETE FROM conversation_contexts 
        WHERE conversation_id = conv_id;
        
        -- 记录清理的上下文数量
        SELECT ROW_COUNT() INTO context_count;
        
    END LOOP;
    
    CLOSE conv_cursor;
    
    -- 提交事务
    COMMIT;
    
    SELECT CONCAT('清理完成，共清理 ', context_count, ' 条上下文记录') AS result;
END //
DELIMITER ;

-- 9. 创建触发器，自动更新会话统计信息
DELIMITER //
CREATE TRIGGER update_conversation_stats_after_analysis_insert
AFTER INSERT ON analyses
FOR EACH ROW
BEGIN
    UPDATE conversations 
    SET 
        total_rounds = (
            SELECT COUNT(*) 
            FROM analyses 
            WHERE conversation_id = NEW.conversation_id
        ),
        updated_at = NOW()
    WHERE id = NEW.conversation_id;
END //

CREATE TRIGGER update_conversation_stats_after_analysis_delete
AFTER DELETE ON analyses
FOR EACH ROW
BEGIN
    UPDATE conversations 
    SET 
        total_rounds = (
            SELECT COUNT(*) 
            FROM analyses 
            WHERE conversation_id = OLD.conversation_id
        ),
        updated_at = NOW()
    WHERE id = OLD.conversation_id;
END //
DELIMITER ;

-- 10. 添加注释说明
ALTER TABLE conversations COMMENT = '会话表 - 支持多轮分析对话';
ALTER TABLE conversation_contexts COMMENT = '会话上下文表 - 存储结构化的对话上下文信息';

-- 迁移完成提示
SELECT '多轮分析功能数据库迁移完成！' AS migration_status; 