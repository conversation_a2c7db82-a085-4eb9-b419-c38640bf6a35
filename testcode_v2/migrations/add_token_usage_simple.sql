-- 简化版Token用量跟踪MySQL迁移脚本

-- 1. 创建token_usages表
CREATE TABLE `token_usages` (
  `id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `org_id` int NOT NULL,
  `project_id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `analysis_id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `model_id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `model_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'unknown',
  `prompt_tokens` int NOT NULL DEFAULT '0' COMMENT '输入Token数量',
  `completion_tokens` int NOT NULL DEFAULT '0' COMMENT '输出Token数量',
  `total_tokens` int NOT NULL DEFAULT '0' COMMENT '总Token数量',
  PRIMARY KEY (`id`),
  KEY `idx_token_usages_organization_id` (`org_id`),
  KEY `idx_token_usages_user_id` (`user_id`),
  KEY `idx_token_usages_project_id` (`project_id`),
  KEY `idx_token_usages_analysis_id` (`analysis_id`),
  KEY `idx_token_usages_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Token使用记录表';
-- 2. 给organizations表添加usage字段（如果不存在）
-- 注意：如果字段已存在会报错，可以忽略或手动检查
ALTER TABLE `organizations` 
ADD COLUMN `total_token_usage` BIGINT NOT NULL DEFAULT 0 COMMENT 'Token总用量';

-- 完成 