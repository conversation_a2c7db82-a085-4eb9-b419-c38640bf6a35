/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
}

/* 语言切换器 */
.language-switch-container {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(8px);
  border-radius: 8px;
  padding: 8px;
  border: 1px solid rgba(59, 130, 246, 0.2);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.language-switch-container .ant-select {
  border: none !important;
  box-shadow: none !important;
}

.language-switch-container .ant-select .ant-select-selector {
  border: none !important;
  box-shadow: none !important;
  background: transparent !important;
}

/* 登录页面主容器 */
.login-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  position: relative;
  overflow: hidden;
}

/* 去除整体背景虚化 */
.login-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  z-index: 1;
}

/* 背景产品介绍卡片 */
.background-cards {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
  pointer-events: none; /* 防止卡片阻挡交互 */
}

.feature-card {
  position: absolute;
  width: 280px;
  padding: 24px;
  background: rgba(255, 255, 255, 0.85);
  border: 1px solid rgba(59, 130, 246, 0.15);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.08);
  transition: all 0.3s ease;
  animation: gentleFloat 12s ease-in-out infinite;
  pointer-events: auto; /* 恢复卡片本身的交互 */
}

.feature-card:hover {
  background: rgba(255, 255, 255, 0.95);
  border-color: rgba(59, 130, 246, 0.25);
  transform: translateY(-2px);
  box-shadow: 0 8px 28px rgba(59, 130, 246, 0.12);
}

.card-icon {
  font-size: 36px;
  color: #3b82f6;
  margin-bottom: 16px;
  display: block;
  transition: transform 0.3s ease;
}

.feature-card:hover .card-icon {
  transform: scale(1.1);
}

.feature-card h3 {
  color: #1e293b;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 12px;
  line-height: 1.4;
  transition: color 0.3s ease;
}

.feature-card:hover h3 {
  color: #3b82f6;
}

.feature-card p {
  color: #64748b;
  font-size: 15px;
  line-height: 1.5;
  margin: 0;
  transition: color 0.3s ease;
}

.feature-card:hover p {
  color: #475569;
}

/* 卡片位置布局 - 智能定位，避免遮挡中央区域 */
.card-1 {
  top: 5%;
  left: 2%;
  animation-delay: 0s;
}

.card-2 {
  top: 8%;
  right: 3%;
  animation-delay: 2s;
}

.card-3 {
  top: 35%;
  left: 1%;
  animation-delay: 4s;
}

.card-4 {
  top: 38%;
  right: 2%;
  animation-delay: 6s;
}

.card-5 {
  bottom: 15%;
  left: 3%;
  animation-delay: 8s;
}

.card-6 {
  bottom: 12%;
  right: 4%;
  animation-delay: 10s;
}

@keyframes gentleFloat {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
  }
  33% {
    transform: translateY(-3px) translateX(2px);
  }
  66% {
    transform: translateY(-1px) translateX(-1px);
  }
}

/* 中央登录卡片 - 增大突出主题 */
.central-login-card {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 640px;
  padding: 64px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(59, 130, 246, 0.25);
  border-radius: 28px;
  box-shadow: 
    0 24px 80px rgba(59, 130, 246, 0.15),
    0 12px 32px rgba(0, 0, 0, 0.06);
  text-align: center;
  transition: all 0.3s ease;
}

.central-login-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(59, 130, 246, 0.03) 0%, 
    rgba(255, 255, 255, 0.08) 50%, 
    rgba(29, 78, 216, 0.03) 100%);
  border-radius: 28px;
  z-index: -1;
}

/* 登录头部 - 增大主题元素 */
.login-header {
  margin-bottom: 56px;
}

.login-logo {
  width: 140px;
  height: 140px;
  object-fit: contain;
  margin: 0 auto 32px;
  display: block;
  filter: drop-shadow(0 12px 32px rgba(59, 130, 246, 0.2));
  transition: all 0.3s ease;
}

.login-logo:hover {
  transform: scale(1.05) translateY(-3px);
  filter: drop-shadow(0 16px 40px rgba(59, 130, 246, 0.25));
}

.login-title {
  color: #1e293b !important;
  font-size: 48px !important;
  font-weight: 800 !important;
  margin-bottom: 20px !important;
  letter-spacing: -0.02em;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.login-subtitle {
  color: #64748b !important;
  font-size: 20px !important;
  font-weight: 500 !important;
  line-height: 1.6;
}

/* 登录表单 */
.login-form {
  margin-bottom: 48px;
}

.login-form .ant-form-item {
  margin-bottom: 32px;
}

.login-input {
  height: 60px !important;
  border-radius: 18px !important;
  border: 1px solid rgba(59, 130, 246, 0.2) !important;
  font-size: 18px !important;
  transition: all 0.3s ease !important;
  background: rgba(255, 255, 255, 0.8) !important;
  backdrop-filter: blur(8px) !important;
}

.login-input:hover {
  border-color: rgba(59, 130, 246, 0.4) !important;
  background: rgba(255, 255, 255, 0.9) !important;
}

.login-input:focus,
.login-input.ant-input-focused {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1) !important;
  background: rgba(255, 255, 255, 0.95) !important;
}

.login-input .ant-input {
  border: none !important;
  box-shadow: none !important;
  padding-left: 20px !important;
  background: transparent !important;
}

.login-input .anticon {
  color: #64748b;
  margin-left: 24px;
  font-size: 20px;
}

.login-input:focus .anticon {
  color: #3b82f6;
}

.login-button {
  height: 60px !important;
  border-radius: 18px !important;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
  border: none !important;
  font-size: 18px !important;
  font-weight: 600 !important;
  box-shadow: 0 12px 32px rgba(59, 130, 246, 0.3) !important;
  transition: all 0.3s ease !important;
  position: relative;
  overflow: hidden;
}

.login-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.login-button:hover::before {
  left: 100%;
}

.login-button:hover {
  transform: translateY(-3px) !important;
  box-shadow: 0 16px 40px rgba(59, 130, 246, 0.4) !important;
}

.login-button:active {
  transform: translateY(0) !important;
}

/* 登录底部 */
.login-footer {
  padding-top: 32px;
  border-top: 1px solid rgba(59, 130, 246, 0.15);
}

.company-info {
  color: #94a3b8 !important;
  font-size: 16px !important;
  font-weight: 500 !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .feature-card {
    width: 260px;
    padding: 20px;
  }
  
  .card-icon {
    font-size: 32px;
  }
  
  .feature-card h3 {
    font-size: 16px;
  }
  
  .feature-card p {
    font-size: 14px;
  }
  
  /* 调整卡片位置，给中央区域更多空间 */
  .card-1 { top: 3%; left: 1%; }
  .card-2 { top: 6%; right: 1%; }
  .card-3 { top: 32%; left: 0.5%; }
  .card-4 { top: 35%; right: 0.5%; }
  .card-5 { bottom: 12%; left: 1%; }
  .card-6 { bottom: 9%; right: 1%; }
}

@media (max-width: 1024px) {
  .feature-card {
    width: 240px;
    padding: 18px;
  }
  
  .card-icon {
    font-size: 30px;
    margin-bottom: 12px;
  }
  
  .feature-card h3 {
    font-size: 15px;
    margin-bottom: 10px;
  }
  
  .feature-card p {
    font-size: 13px;
  }
  
  .central-login-card {
    max-width: 560px;
    padding: 56px;
  }
  
  .login-title {
    font-size: 40px !important;
  }
  
  /* 进一步调整卡片位置 */
  .card-3, .card-4 {
    display: none; /* 隐藏中间的卡片，避免遮挡 */
  }
}

@media (max-width: 768px) {
  .login-container {
    padding: 16px;
  }
  
  .feature-card {
    width: 200px;
    padding: 16px;
  }
  
  .card-icon {
    font-size: 28px;
    margin-bottom: 10px;
  }
  
  .feature-card h3 {
    font-size: 14px;
    margin-bottom: 8px;
  }
  
  .feature-card p {
    font-size: 12px;
    line-height: 1.4;
  }
  
  .central-login-card {
    max-width: 100%;
    padding: 48px 32px;
  }
  
  .login-logo {
    width: 120px;
    height: 120px;
  }
  
  .login-title {
    font-size: 36px !important;
  }
  
  .login-subtitle {
    font-size: 18px !important;
  }
  
  /* 只显示顶部和底部的卡片 */
  .card-3, .card-4 {
    display: none;
  }
  
  /* 调整剩余卡片位置 */
  .card-1 { top: 2%; left: 0.5%; }
  .card-2 { top: 4%; right: 0.5%; }
  .card-5 { bottom: 8%; left: 0.5%; }
  .card-6 { bottom: 6%; right: 0.5%; }
}

@media (max-width: 640px) {
  .feature-card {
    width: 180px;
    padding: 14px;
  }
  
  .card-icon {
    font-size: 24px;
  }
  
  .feature-card h3 {
    font-size: 13px;
  }
  
  .feature-card p {
    font-size: 11px;
  }
  
  /* 只保留角落的卡片 */
  .card-1 { top: 1%; left: 0%; }
  .card-2 { top: 2%; right: 0%; }
  .card-5 { bottom: 6%; left: 0%; }
  .card-6 { bottom: 4%; right: 0%; }
}

@media (max-width: 480px) {
  .feature-card {
    display: none; /* 小屏幕完全隐藏背景卡片 */
  }
  
  .central-login-card {
    padding: 40px 28px;
    margin: 20px;
  }
  
  .login-container::before {
    background: rgba(255, 255, 255, 0.05); /* 减少背景遮罩 */
  }
} 