import axios, { AxiosRequestConfig } from 'axios';
import { message } from 'antd';

// 企业基础信息
export interface OrganizationInfo {
  id: number;
  name: string;
}

// 角色基础信息
export interface RoleInfo {
  id: number;
  name: string;
  level: number;
}

// 管理员用户接口定义（包含企业和角色信息）
export interface AdminUser {
  id: number;
  username: string;
  email?: string;
  is_active: boolean;
  pid: number;
  is_superuser: boolean;
  role_id?: number;
  org_id?: number;
  organization?: OrganizationInfo;
  role?: RoleInfo;
  created_at: string;
  updated_at: string;
  parent_username?: string;  // 父级用户名
  children_count: number;    // 子账号数量
  // 前端树形结构辅助字段
  children?: AdminUser[];
  key?: string;
}

// 企业相关接口定义
export interface Organization {
  id: number;
  name: string;
  description?: string;
  admin_user_id?: number;
  token_limit: number;
  user_limit: number;
  project_limit: number;
  status: number;
  expire_at?: string;
  contact_email?: string;
  contact_phone?: string;
  address?: string;
  created_at: string;
  updated_at: string;
  is_active: boolean;
  is_expired: boolean;
}

export interface OrganizationStats {
  id: number;
  name: string;
  status: number;
  user_count: number;
  project_count: number;
  token_used: number;
  user_limit: number;
  project_limit: number;
  token_limit: number;
  user_usage_rate: number;
  project_usage_rate: number;
  token_usage_rate: number;
}

export interface OrganizationList {
  items: Organization[];
  total: number;
  page: number;
  size: number;
}

export interface CreateOrganizationData {
  name: string;
  description?: string;
  admin_user_id?: number;
  token_limit?: number;
  user_limit?: number;
  project_limit?: number;
  status?: number;
  expire_at?: string;
  contact_email?: string;
  contact_phone?: string;
  address?: string;
}

export interface UpdateOrganizationData {
  name?: string;
  description?: string;
  admin_user_id?: number;
  token_limit?: number;
  user_limit?: number;
  project_limit?: number;
  status?: number;
  expire_at?: string;
  contact_email?: string;
  contact_phone?: string;
  address?: string;
}

// 角色相关接口定义
export interface Role {
  id: number;
  name: string;
  description?: string;
  level: number;
  permissions?: string[];
  org_id?: number;
  is_active: boolean;
  is_system: boolean;
  created_at: string;
  updated_at: string;
  is_system_role: boolean;
  is_org_role: boolean;
  is_user_role: boolean;
}

export interface AdminLoginData {
  username: string;
  password: string;
}

export interface AdminTokenResponse {
  access_token: string;
  token_type: string;
  admin_info: AdminUser;
}

export interface CreateUserData {
  username: string;
  password: string;
  email?: string;
  pid?: number;
  is_active?: boolean;
  org_id?: number;
  role_id?: number;
}

export interface UpdateUserData {
  username?: string;
  email?: string;
  password?: string;
  pid?: number;
  is_active?: boolean;
  org_id?: number;
  role_id?: number;
}

// 创建管理员专用的axios实例
const adminApiInstance = axios.create({
  baseURL: '/admin-api',
  timeout: 60000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
adminApiInstance.interceptors.request.use(
  config => {
    // 获取管理员token
    const adminToken = localStorage.getItem('adminToken');
    if (adminToken) {
      config.headers['Authorization'] = `Bearer ${adminToken}`;
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 响应拦截器 
adminApiInstance.interceptors.response.use(
  response => {
    return response;
  },
  error => {
    console.log('API请求错误:', error);
    
    // 网络错误处理
    if (!error.response) {
      // 请求未发送成功或无响应
      if (error.code === 'NETWORK_ERROR') {
        message.error('网络连接失败，请检查网络设置');
      } else if (error.code === 'TIMEOUT') {
        message.error('请求超时，请稍后重试');
      } else {
        message.error('网络异常，请检查连接');
      }
      return Promise.reject(error);
    }
    
    const status = error.response.status;
    
    // 详细的HTTP状态码处理
    switch (status) {
      case 401:
        // 清除管理员本地token和信息
        localStorage.removeItem('adminToken');
        localStorage.removeItem('adminInfo');
        
        // 如果不是在管理员登录页面，则重定向到管理员登录页面
        if (!window.location.pathname.startsWith('/admin/login')) {
          message.error('管理员登录已过期，请重新登录');
          window.location.href = '/admin/login';
        }
        break;
        
      case 403:
        message.error('权限不足，无法执行此操作');
        break;
        
      case 404:
        message.error('请求的资源不存在');
        break;
        
      case 429:
        message.error('请求过于频繁，请稍后再试');
        break;
        
      case 500:
        message.error('服务器内部错误，请联系技术支持');
        break;
        
      case 502:
        message.error('网关错误，请稍后重试');
        break;
        
      case 503:
        message.error('服务暂时不可用，请稍后重试');
        break;
        
      case 504:
        message.error('网关超时，请稍后重试');
        break;
        
      default:
        // 其他错误，尝试显示后端返回的错误信息
        const errorData = error.response.data;
        if (errorData?.detail) {
          message.error(`操作失败: ${errorData.detail}`);
        } else if (errorData?.message) {
          message.error(`操作失败: ${errorData.message}`);
        } else {
          message.error(`请求失败 (错误代码: ${status})`);
        }
    }
    
    return Promise.reject(error);
  }
);

// 管理员认证相关API
export const adminAuthApi = {
  // 管理员登录
  login: (data: AdminLoginData) => adminApiInstance.post('/auth/login', data),
  
  // 获取当前管理员信息
  getCurrentAdmin: () => adminApiInstance.get('/auth/me'),
  
  // 检查是否已登录（基本检查）
  isLoggedIn: () => {
    const token = localStorage.getItem('adminToken');
    const adminInfo = localStorage.getItem('adminInfo');
    
    // 基本检查：token和管理员信息都存在
    if (!token || !adminInfo) {
      return false;
    }
    
    // 检查token格式是否正确（简单验证）
    try {
      const parts = token.split('.');
      if (parts.length !== 3) {
        return false;
      }
      
      // 检查管理员信息格式
      const admin = JSON.parse(adminInfo);
      if (!admin.username || !admin.id) {
        return false;
      }
      
      return true;
    } catch (e) {
      // JSON解析失败或其他错误
      localStorage.removeItem('adminToken');
      localStorage.removeItem('adminInfo');
      return false;
    }
  },
  
  // 退出登录
  logout: () => {
    localStorage.removeItem('adminToken');
    localStorage.removeItem('adminInfo');
    window.location.href = '/admin/login';
  }
};

// 用户管理相关API
export const adminUserApi = {
  // 获取用户列表
  getUsers: (params?: { 
    skip?: number; 
    limit?: number; 
    pid?: number;
    org_id?: number;
    username?: string;
    is_active?: boolean;
  }) => adminApiInstance.get('/users', { params }),
  
  // 获取用户总数
  getUsersCount: (params?: {
    org_id?: number;
    username?: string;
    is_active?: boolean;
  }) => adminApiInstance.get('/users-count', { params }),
  
  // 获取企业简化列表
  getOrganizationsSimple: () => adminApiInstance.get('/organizations-simple'),
  
  // 获取用户详情
  getUser: (userId: number) => adminApiInstance.get(`/users/${userId}`),
  
  // 创建用户
  createUser: (data: CreateUserData) => adminApiInstance.post('/users', data),
  
  // 更新用户
  updateUser: (userId: number, data: UpdateUserData) => 
    adminApiInstance.put(`/users/${userId}`, data),
  
  // 删除用户
  deleteUser: (userId: number) => adminApiInstance.delete(`/users/${userId}`),
  
  // 批量激活用户
  batchActivateUsers: (userIds: number[]) => 
    adminApiInstance.post('/users/batch-activate', userIds),
  
  // 批量禁用用户
  batchDeactivateUsers: (userIds: number[]) => 
    adminApiInstance.post('/users/batch-deactivate', userIds),
};

// 企业管理相关API
export const adminOrganizationApi = {
  // 获取企业列表
  getOrganizations: (params?: { 
    skip?: number; 
    limit?: number; 
    status?: number; 
    search?: string; 
  }) => adminApiInstance.get('/organizations', { params }),
  
  // 获取企业详情
  getOrganization: (orgId: number) => adminApiInstance.get(`/organizations/${orgId}`),
  
  // 创建企业
  createOrganization: (data: CreateOrganizationData) => 
    adminApiInstance.post('/organizations', data),
  
  // 更新企业
  updateOrganization: (orgId: number, data: UpdateOrganizationData) => 
    adminApiInstance.put(`/organizations/${orgId}`, data),
  
  // 删除企业
  deleteOrganization: (orgId: number) => adminApiInstance.delete(`/organizations/${orgId}`),
  
  // 获取企业统计
  getOrganizationStats: (orgId: number) => 
    adminApiInstance.get(`/organizations/${orgId}/stats`),
  
  // 获取所有企业统计
  getAllOrganizationStats: () => adminApiInstance.get('/organizations-stats'),
  
  // 获取企业Token用量详情
  getOrganizationTokenUsage: (orgId: number) => 
    adminApiInstance.get(`/organizations/${orgId}/token-usage`),
  
  // 检查企业各项限额
  checkOrganizationLimits: (orgId: number) => 
    adminApiInstance.get(`/organizations/${orgId}/check-limits`),
};

// 角色管理相关API
export const adminRoleApi = {
  // 获取角色列表
  getRoles: (params?: { 
    skip?: number; 
    limit?: number; 
    level?: number; 
    org_id?: number;
    is_active?: boolean;
    search?: string; 
  }) => adminApiInstance.get('/roles', { params }),
  
  // 获取角色详情
  getRole: (roleId: number) => adminApiInstance.get(`/roles/${roleId}`),
  
  // 创建角色
  createRole: (data: any) => adminApiInstance.post('/roles', data),
  
  // 更新角色
  updateRole: (roleId: number, data: any) => 
    adminApiInstance.put(`/roles/${roleId}`, data),
  
  // 删除角色
  deleteRole: (roleId: number) => adminApiInstance.delete(`/roles/${roleId}`),
  
  // 获取角色模板
  getRoleTemplates: () => adminApiInstance.get('/role-templates'),
  
  // 获取可用权限列表
  getAvailablePermissions: () => adminApiInstance.get('/available-permissions'),
  
  // 检查角色权限
  checkRolePermission: (roleId: number, permission: string) => 
    adminApiInstance.post(`/roles/${roleId}/check-permission`, null, { 
      params: { permission } 
    }),
};

// 统计分析相关API
export const adminStatisticsApi = {
  // 获取系统总览
  getSystemOverview: () => adminApiInstance.get('/statistics/system-overview'),
  
  // 获取企业排行榜
  getOrganizationRanking: (limit?: number) => 
    adminApiInstance.get('/statistics/organization-ranking', { params: { limit } }),
  
  // 获取用户增长趋势
  getUserGrowthTrend: (days?: number) => 
    adminApiInstance.get('/statistics/user-growth-trend', { params: { days } }),
  
  // 获取角色分布统计
  getRoleDistribution: () => adminApiInstance.get('/statistics/role-distribution'),
  
  // 获取企业健康状况
  getOrganizationHealth: () => adminApiInstance.get('/statistics/organization-health'),
  
  // 获取系统告警
  getSystemAlerts: () => adminApiInstance.get('/statistics/system-alerts'),
};

// 统一导出的API对象
export const adminApi = {
  // 认证相关
  ...adminAuthApi,
  
  // 用户管理
  ...adminUserApi,
  
  // 企业管理
  ...adminOrganizationApi,
  
  // 角色管理
  ...adminRoleApi,
  
  // 统计分析
  ...adminStatisticsApi,
};

export default adminApi; 