import React from 'react';
import { Card, Row, Col, Space } from 'antd';
import './AdminPage.css';

interface AdminPageProps {
  title: string;
  actionButtons?: React.ReactNode[];
  searchForm?: React.ReactNode;
  children: React.ReactNode;
}

const AdminPage: React.FC<AdminPageProps> = ({
  title,
  actionButtons,
  searchForm,
  children,
}) => {
  return (
    <div className="admin-page">
      <Card className="admin-page-main-card">
        <Row
          justify="space-between"
          align="middle"
          className="admin-page-header"
        >
          <Col>
            <h2 className="admin-page-title">{title}</h2>
          </Col>
          {actionButtons && (
            <Col>
              <Space>{actionButtons}</Space>
            </Col>
          )}
        </Row>

        {searchForm && (
          <div className="admin-page-search-area">
            {searchForm}
          </div>
        )}

        <div className="admin-page-content">
          {children}
        </div>
      </Card>
    </div>
  );
};

export default AdminPage;
