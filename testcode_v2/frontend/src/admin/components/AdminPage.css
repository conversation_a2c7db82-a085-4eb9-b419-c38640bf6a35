/* frontend/src/admin/components/AdminPage.css */

.admin-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.admin-page-search-card {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: none;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.admin-page-main-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: none;
}

.admin-page-header {
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 20px;
}

.admin-page-title {
  margin: 0;
  color: #262626;
  font-weight: 600;
  font-size: 20px;
}

.admin-page-content .ant-table {
  border-radius: 8px;
  overflow: hidden;
}

.admin-page-content .ant-table-thead > tr > th {
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  font-weight: 600;
  color: #262626;
} 