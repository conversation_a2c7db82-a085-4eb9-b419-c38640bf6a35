import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Switch,
  message,
  Popconfirm,
  Tag,
  Card,
  Row,
  Col,
  Tooltip,
  Badge,
  Select,
  List,
  Layout,
  Descriptions,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  UserOutlined,
  TeamOutlined,
  EyeOutlined,
  SearchOutlined,
  AppstoreOutlined,
  MailOutlined,
  LockOutlined,
  SafetyCertificateOutlined,
} from '@ant-design/icons';
import type { ColumnsType, TableProps } from 'antd/es/table';
import { adminUserApi, adminRoleApi, AdminUser, CreateUserData, UpdateUserData, Role } from '../services/adminApi';
import AdminPage from '../components/AdminPage'; // 导入AdminPage组件

interface OrganizationOption {
  id: number;
  name: string;
}

interface RoleOption {
  id: number;
  name: string;
}

const UserManagement: React.FC = () => {
  const [users, setUsers] = useState<AdminUser[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  
  // 组织搜索词
  const [orgSearchTerm, setOrgSearchTerm] = useState('');
  
  // 搜索条件状态
  const [searchFormInstance] = Form.useForm();
  const [organizations, setOrganizations] = useState<OrganizationOption[]>([]);
  const [roles, setRoles] = useState<RoleOption[]>([]);
  const [searchParams, setSearchParams] = useState<{
    org_id?: number;
    username?: string;
    is_active?: boolean;
  }>({});
  
  // 模态框状态
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingUser, setEditingUser] = useState<AdminUser | null>(null);
  const [form] = Form.useForm();

  // 子用户查看模态框状态
  const [isChildModalVisible, setIsChildModalVisible] = useState(false);
  const [childUsers, setChildUsers] = useState<AdminUser[]>([]);
  const [selectedParentUser, setSelectedParentUser] = useState<AdminUser | null>(null);

  // 获取用户列表
  const fetchUsers = async (page = 1, pageSize = 10, search = searchParams) => {
    setLoading(true);
    try {
      // 并行获取用户列表和用户总数
      const [usersResponse, countResponse] = await Promise.all([
        adminUserApi.getUsers({
          skip: (page - 1) * pageSize,
          limit: pageSize,
          ...search
        }),
        adminUserApi.getUsersCount(search)
      ]);
      
      if (usersResponse.data && countResponse.data) {
        // 确保数据是数组格式
        const userData = Array.isArray(usersResponse.data) ? usersResponse.data : [];
        setUsers(userData);
        setPagination((prev: any) => ({
          ...prev,
          current: page,
          pageSize,
          total: countResponse.data.count, // 使用正确的总数
        }));
      }
    } catch (error: any) {
      console.error('获取用户列表失败:', error);
      message.error('获取用户列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取企业列表
  const fetchOrganizations = async () => {
    try {
      console.log('开始获取企业列表...');
      const response = await adminUserApi.getOrganizationsSimple();
      console.log('企业列表响应:', response);
      if (response.data) {
        console.log('设置企业数据:', response.data);
        setOrganizations(response.data);
      }
    } catch (error: any) {
      console.error('获取企业列表失败:', error);
      message.error('获取企业列表失败');
    }
  };

  // 获取角色列表
  const fetchRoles = async () => {
    try {
      const response = await adminRoleApi.getRoles({ limit: 100 });
      if (response.data && response.data.items) {
        const roleOptions = response.data.items.map((role: Role) => ({
          id: role.id,
          name: role.name
        }));
        setRoles(roleOptions);
      }
    } catch (error: any) {
      console.error('获取角色列表失败:', error);
      message.error('获取角色列表失败');
    }
  };

  // 组件挂载时获取用户列表
  useEffect(() => {
    fetchUsers();
    fetchOrganizations();
    fetchRoles();
  }, []);

  // 点击左侧企业列表处理
  const handleSelectOrg = (orgId: number | null) => {
    const newSearchParams = {
      org_id: orgId === null ? undefined : orgId,
    };
    setSearchParams(newSearchParams);
    searchFormInstance.resetFields(['username', 'is_active']);
    fetchUsers(1, pagination.pageSize, newSearchParams);
  };

  // 搜索处理
  const handleSearch = (values: any) => {
    const newSearchParams = {
      ...searchParams,
      username: values.username?.trim() || undefined,
      is_active: values.is_active,
    };
    setSearchParams(newSearchParams);
    fetchUsers(1, pagination.pageSize, newSearchParams);
  };

  // 重置搜索
  const handleResetSearch = () => {
    searchFormInstance.resetFields();
    const newSearchParams = { org_id: searchParams.org_id };
    setSearchParams(newSearchParams);
    fetchUsers(1, pagination.pageSize, newSearchParams);
  };

  // 查看子用户
  const handleViewChildren = async (parentUser: AdminUser) => {
    try {
      // 直接从后端获取子用户列表
      const response = await adminUserApi.getUsers({ pid: parentUser.id });
      if (response.data) {
        // 确保数据是数组格式
        const childUserData = Array.isArray(response.data) ? response.data : [];
        setChildUsers(childUserData);
        setSelectedParentUser(parentUser);
        setIsChildModalVisible(true);
      }
    } catch (error: any) {
      console.error('获取子用户列表失败:', error);
      message.error('获取子用户列表失败');
    }
  };

  // 表格列定义
  const columns: ColumnsType<AdminUser> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 60,
    },
    {
      title: '用户类型',
      key: 'userType',
      width: 100,
      render: (_, record: AdminUser) => (
        <Space>
          {record.pid === 0 ? (
            <Tooltip title="主账号">
              <TeamOutlined style={{ color: '#1890ff' }} />
            </Tooltip>
          ) : (
            <Tooltip title="子账号">
              <UserOutlined style={{ color: '#52c41a' }} />
            </Tooltip>
          )}
        </Space>
      ),
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      width: 150,
      render: (username: string, record: AdminUser) => (
        <Space>
          <span style={{ fontWeight: record.pid === 0 ? 'bold' : 'normal' }}>
            {username}
          </span>
          {record.pid !== 0 && (
            <Tag color="orange">子账号</Tag>
          )}
        </Space>
      ),
    },
    {
      title: '企业名称',
      key: 'organization',
      width: 150,
      render: (_, record: AdminUser) => (
        record.organization ? (
          <Tag color="blue">{record.organization.name}</Tag>
        ) : (
          <span style={{ color: '#999' }}>未分配</span>
        )
      ),
    },
    {
      title: '角色',
      key: 'role',
      width: 120,
      render: (_, record: AdminUser) => (
        record.role ? (
          <Tag color="green">{record.role.name}</Tag>
        ) : (
          <span style={{ color: '#999' }}>未分配</span>
        )
      ),
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      width: 200,
      render: (email: string) => email || <span style={{ color: '#999' }}>未设置</span>,
    },
    {
      title: '父级用户',
      key: 'parentUser',
      width: 120,
      render: (_, record: AdminUser) => {
        if (record.pid === 0) {
          // 主账号：显示子账号数量和查看按钮
          return record.children_count > 0 ? (
            <Badge count={record.children_count} size="small">
              <Button 
                type="link" 
                size="small"
                icon={<EyeOutlined />}
                onClick={() => handleViewChildren(record)}
              >
                查看子账号
              </Button>
            </Badge>
          ) : (
            <span style={{ color: '#999' }}>无子账号</span>
          );
        } else {
          // 子账号：显示父级用户名
          return record.parent_username ? (
            <Tag color="cyan">{record.parent_username}</Tag>
          ) : (
            <span style={{ color: '#ff4d4f' }}>父级不存在</span>
          );
        }
      },
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      width: 100,
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '激活' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record: AdminUser) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定删除此用户吗？"
            description="删除后无法恢复"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 处理分页变化
  const handleTableChange: TableProps<AdminUser>['onChange'] = (paginationInfo) => {
    if (paginationInfo.current && paginationInfo.pageSize) {
      fetchUsers(paginationInfo.current, paginationInfo.pageSize);
    }
  };

  // 显示创建用户模态框
  const showCreateModal = () => {
    setEditingUser(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  // 显示编辑用户模态框
  const handleEdit = (user: AdminUser) => {
    setEditingUser(user);
    
    // 确保数据已加载
    const loadDataAndSetForm = async () => {
      // 确保企业和角色数据都已加载
      const promises = [];
      if (organizations.length === 0) {
        promises.push(fetchOrganizations());
      }
      if (roles.length === 0) {
        promises.push(fetchRoles());
      }
      
      if (promises.length > 0) {
        await Promise.all(promises);
      }
      
      // 设置表单值
      form.setFieldsValue({
        username: user.username,
        email: user.email,
        is_active: user.is_active,
        org_id: user.organization?.id, // 设置企业ID
        role_id: user.role_id, // 设置角色ID
      });
    };
    
    loadDataAndSetForm();
    setIsModalVisible(true);
  };

  // 删除用户
  const handleDelete = async (userId: number) => {
    try {
      await adminUserApi.deleteUser(userId);
      message.success('删除用户成功');
      fetchUsers(pagination.current, pagination.pageSize);
    } catch (error: any) {
      console.error('删除用户失败:', error);
      message.error('删除用户失败');
    }
  };

  // 提交表单（创建或更新用户）
  const handleSubmit = async (values: any) => {
    try {
      if (editingUser) {
        // 更新用户
        const updateData: UpdateUserData = {
          username: values.username,
          email: values.email,
          is_active: values.is_active,
          org_id: values.org_id, // 添加企业ID
          role_id: values.role_id, // 添加角色ID
        };
        
        if (values.password && values.password.trim()) {
          updateData.password = values.password;
        }
        
        await adminUserApi.updateUser(editingUser.id, updateData);
        message.success('更新用户成功');
      } else {
        // 创建用户（只能创建父级用户）
        const createData: CreateUserData = {
          username: values.username,
          password: values.password,
          email: values.email,
          pid: 0, // 固定为0，只能创建父级用户
          is_active: values.is_active ?? true,
          org_id: values.org_id,
          role_id: values.role_id, // 添加角色ID
        };
        
        await adminUserApi.createUser(createData);
        message.success('创建用户成功');
      }
      
      setIsModalVisible(false);
      form.resetFields();
      fetchUsers(pagination.current, pagination.pageSize);
    } catch (error: any) {
      console.error('操作失败:', error);
      if (error.response?.data?.detail) {
        message.error(error.response.data.detail);
      } else {
        message.error(editingUser ? '更新用户失败' : '创建用户失败');
      }
    }
  };

  // 子用户表格列定义
  const childColumns: ColumnsType<AdminUser> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      width: 150,
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      width: 200,
      render: (email: string) => email || <span style={{ color: '#999' }}>未设置</span>,
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      width: 100,
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '激活' : '禁用'}
        </Tag>
      ),
    },
  ];

  const searchFormNode = (
    <Form
      form={searchFormInstance}
      layout="inline"
      onFinish={handleSearch}
    >
      <Form.Item name="username" label="用户名">
        <Input
          placeholder="输入用户名"
          allowClear
          style={{ width: 180 }}
        />
      </Form.Item>
      
      <Form.Item name="is_active" label="状态">
        <Select
          placeholder="选择状态"
          allowClear
          style={{ width: 120 }}
          options={[
            { label: '激活', value: true },
            { label: '禁用', value: false },
          ]}
        />
      </Form.Item>
      
      <Form.Item>
        <Space>
          <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
            搜索
          </Button>
          <Button onClick={handleResetSearch}>
            重置
          </Button>
        </Space>
      </Form.Item>
    </Form>
  );

  const actionButtons = [
    <Button 
      key="refresh"
      icon={<ReloadOutlined />} 
      onClick={() => fetchUsers(pagination.current, pagination.pageSize)}
    >
      刷新
    </Button>,
    <Button 
      key="create"
      type="primary" 
      icon={<PlusOutlined />} 
      onClick={showCreateModal}
    >
      创建主账号
    </Button>
  ];

  return (
    <>
      <Layout style={{ background: '#fff' }}>
        <Layout.Sider width={240} style={{ background: '#fff', borderRight: '1px solid #f0f0f0', padding: '12px' }}>
          <h3 style={{ marginBottom: '12px', fontWeight: 600 }}>企业列表</h3>
          <Input.Search
            placeholder="搜索企业"
            onChange={e => setOrgSearchTerm(e.target.value)}
            style={{ marginBottom: 10 }}
            allowClear
          />
          <div style={{ height: 'calc(100vh - 220px)', overflowY: 'auto' }}>
            <List
              dataSource={[
                { id: null, name: '所有企业' },
                ...organizations.filter(org => org.name.toLowerCase().includes(orgSearchTerm.toLowerCase()))
              ]}
              renderItem={(item: any) => (
                <List.Item
                  onClick={() => handleSelectOrg(item.id)}
                  style={{
                    cursor: 'pointer',
                    padding: '8px 12px',
                    borderRadius: '4px',
                    borderLeft: searchParams.org_id === item.id || (searchParams.org_id === undefined && item.id === null)
                      ? '3px solid #1890ff'
                      : '3px solid transparent',
                    background: searchParams.org_id === item.id || (searchParams.org_id === undefined && item.id === null)
                      ? '#e6f7ff'
                      : 'transparent',
                    transition: 'all 0.2s',
                  }}
                  className="org-list-item"
                >
                  <Space>
                    {item.id === null ? <AppstoreOutlined /> : <TeamOutlined />}
                    <span style={{ fontWeight: item.id === null ? 'bold' : 'normal' }}>
                      {item.name}
                    </span>
                  </Space>
                </List.Item>
              )}
              size="small"
            />
          </div>
        </Layout.Sider>
        <Layout.Content style={{ padding: '0 8px' }}>
          <AdminPage
            title="用户管理"
            searchForm={searchFormNode}
            actionButtons={actionButtons}
          >
            <Table
              columns={columns}
              dataSource={users}
              rowKey="id"
              loading={loading}
              pagination={{
                ...pagination,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total: number) => `共 ${total} 条记录`,
                size: 'default',
              }}
              onChange={handleTableChange}
              scroll={{ x: 1200 }}
              rowClassName={(record: AdminUser) => record.pid === 0 ? 'parent-user-row' : 'child-user-row'}
              size="middle"
            />
          </AdminPage>
        </Layout.Content>
      </Layout>

      {/* 创建/编辑用户模态框 */}
      <Modal
        title={editingUser ? '编辑用户' : '创建主账号'}
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
        }}
        onOk={form.submit}
        width={600}
      >
        {editingUser && (
          <Descriptions
            title="用户基本信息"
            bordered
            size="small"
            column={2}
            style={{ marginBottom: 24 }}
          >
            <Descriptions.Item label="用户ID">{editingUser.id}</Descriptions.Item>
            {editingUser.pid !== 0 && editingUser.parent_username ? (
              <Descriptions.Item label="父级用户">{editingUser.parent_username}</Descriptions.Item>
            ) : (
              <Descriptions.Item label="用户类型">主账号</Descriptions.Item>
            )}
            <Descriptions.Item label="创建时间">{new Date(editingUser.created_at).toLocaleString()}</Descriptions.Item>
            <Descriptions.Item label="最后更新">{new Date((editingUser as any).updated_at).toLocaleString()}</Descriptions.Item>
          </Descriptions>
        )}
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={24}>
            <Col span={24}>
              <Form.Item
                name="username"
                label="用户名"
                rules={[
                  { required: true, message: '请输入用户名' },
                  { min: 3, message: '用户名长度至少3个字符' },
                  { max: 50, message: '用户名长度不能超过50个字符' }
                ]}
              >
                <Input placeholder="请输入用户名" size="large" prefix={<UserOutlined />} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="org_id"
                label={<Space><TeamOutlined />所属企业</Space>}
                rules={[
                  { required: true, message: '请选择所属企业' }
                ]}
              >
                <Select
                  placeholder="选择企业"
                  allowClear
                  showSearch
                  size="large"
                  style={{ width: '100%' }}
                  loading={organizations.length === 0}
                  options={organizations.map((org: OrganizationOption) => ({
                    value: org.id,
                    label: org.name
                  }))}
                  filterOption={(input: string, option: any) =>
                    option.label.toLowerCase().includes(input.toLowerCase())
                  }
                  notFoundContent={organizations.length === 0 ? "加载中..." : "暂无企业数据"}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="role_id"
                label={<Space><SafetyCertificateOutlined />用户角色</Space>}
                rules={[
                  { required: true, message: '请选择用户角色' }
                ]}
              >
                <Select
                  placeholder="选择角色"
                  allowClear
                  showSearch
                  size="large"
                  style={{ width: '100%' }}
                  loading={roles.length === 0}
                  options={roles.map((role: RoleOption) => ({
                    value: role.id,
                    label: role.name
                  }))}
                  filterOption={(input: string, option: any) =>
                    option.label.toLowerCase().includes(input.toLowerCase())
                  }
                  notFoundContent={roles.length === 0 ? "加载中..." : "暂无角色数据"}
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                name="email"
                label="邮箱"
                rules={[
                  { type: 'email', message: '请输入有效的邮箱地址' }
                ]}
              >
                <Input placeholder="请输入邮箱（可选）" size="large" prefix={<MailOutlined />} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="password"
                label={editingUser ? '新密码' : '密码'}
                rules={editingUser ? [] : [
                  { required: true, message: '请输入密码' },
                  { min: 6, message: '密码长度至少6个字符' }
                ]}
              >
                <Input.Password 
                  placeholder={editingUser ? '留空则不修改密码' : '请输入密码'} 
                  size="large"
                  prefix={<LockOutlined />}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="is_active"
                label="账户状态"
                valuePropName="checked"
                initialValue={true}
                style={{ paddingTop: '32px' }}
              >
                <Switch checkedChildren="激活" unCheckedChildren="禁用" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>

      {/* 查看子账号模态框 */}
      <Modal
        title={`${selectedParentUser?.username || ''} 的子账号列表`}
        open={isChildModalVisible}
        onCancel={() => {
          setIsChildModalVisible(false);
          setChildUsers([]);
          setSelectedParentUser(null);
        }}
        footer={[
          <Button key="close" onClick={() => {
            setIsChildModalVisible(false);
            setChildUsers([]);
            setSelectedParentUser(null);
          }}>
            关闭
          </Button>
        ]}
        width={800}
      >
        <div style={{ padding: '10px 0' }}>
          {childUsers.length > 0 ? (
            <Table
              columns={childColumns}
              dataSource={childUsers}
              rowKey="id"
              pagination={false}
              size="small"
              scroll={{ x: 600 }}
            />
          ) : (
            <div style={{ 
              textAlign: 'center', 
              padding: '40px 0', 
              color: '#999' 
            }}>
              暂无子账号数据
            </div>
          )}
        </div>
      </Modal>
    </>
  );
};

export default UserManagement;