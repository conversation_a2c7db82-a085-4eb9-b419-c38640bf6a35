import React, { useState, useEffect } from 'react';
import {
  Table, Button, Modal, Form, Input, InputNumber, Select, Space, 
  Card, Row, Col, Statistic, Tag, message, Popconfirm, Tooltip
} from 'antd';
import {
  PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined,
  BuildOutlined, UserOutlined, ProjectOutlined, AlertOutlined
} from '@ant-design/icons';
import { adminApi } from '../services/adminApi';
import AdminPage from '../components/AdminPage';

const { Option } = Select;
const { TextArea } = Input;

interface Organization {
  id: number;
  name: string;
  description?: string;
  token_limit: number;
  user_limit: number;
  project_limit: number;
  status: number;
  expire_at?: string;
  contact_email?: string;
  contact_phone?: string;
  address?: string;
  created_at: string;
  updated_at: string;
  is_active: boolean;
  is_expired: boolean;
}

interface OrganizationStats {
  id: number;
  name: string;
  status: number;
  user_count: number;
  project_count: number;
  token_used: number;
  user_limit: number;
  project_limit: number;
  token_limit: number;
  user_usage_rate: number;
  project_usage_rate: number;
  token_usage_rate: number;
}

const OrganizationManagement: React.FC = () => {
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [organizationStats, setOrganizationStats] = useState<OrganizationStats[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingOrg, setEditingOrg] = useState<Organization | null>(null);
  const [form] = Form.useForm();
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 获取企业列表
  const fetchOrganizations = async (page = 1, pageSize = 10) => {
    setLoading(true);
    try {
      const response = await adminApi.getOrganizations({
        skip: (page - 1) * pageSize,
        limit: pageSize,
      });
      setOrganizations(response.data.items);
      setPagination({
        current: page,
        pageSize,
        total: response.data.total,
      });
    } catch (error) {
      message.error('获取企业列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取企业统计
  const fetchOrganizationStats = async () => {
    try {
      const response = await adminApi.getAllOrganizationStats();
      setOrganizationStats(response.data);
    } catch (error) {
      message.error('获取企业统计失败');
    }
  };

  useEffect(() => {
    fetchOrganizations();
    fetchOrganizationStats();
  }, []);

  // 表格列定义
  const columns = [
    {
      title: '企业名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Organization) => (
        <Space>
          <BuildOutlined />
          <span>{text}</span>
          {record.status !== 1 && <Tag color="red">异常</Tag>}
        </Space>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: number, record: Organization) => {
        const statusMap = {
          1: { text: '正常', color: 'green' },
          2: { text: '停用', color: 'red' },
        };
        const statusInfo = statusMap[status as keyof typeof statusMap] || { text: '未知', color: 'gray' };
        const stats = organizationStats.find((s: OrganizationStats) => s.id === record.id);
        
        // 检查是否有超限情况
        const isTokenExceeded = stats && stats.token_usage_rate >= 1.0;
        const isUserExceeded = stats && stats.user_usage_rate >= 1.0;
        const isProjectExceeded = stats && stats.project_usage_rate >= 1.0;
        const hasExceeded = isTokenExceeded || isUserExceeded || isProjectExceeded;
        
        return (
          <Space>
            <Tag color={statusInfo.color}>{statusInfo.text}</Tag>
            {hasExceeded && (
              <Tooltip title={`超限警告: ${isTokenExceeded ? 'Token' : ''}${isUserExceeded ? ' 用户' : ''}${isProjectExceeded ? ' 项目' : ''}`}>
                <Tag color="red" icon={<AlertOutlined />}>超限</Tag>
              </Tooltip>
            )}
          </Space>
        );
      },
    },
    {
      title: '用户限额',
      dataIndex: 'user_limit',
      key: 'user_limit',
      render: (limit: number, record: Organization) => {
        const stats = organizationStats.find((s: OrganizationStats) => s.id === record.id);
        const used = stats?.user_count || 0;
        const rate = stats?.user_usage_rate || 0;
        const color = rate > 0.8 ? 'red' : rate > 0.6 ? 'orange' : 'green';
        return (
          <Tooltip title={`使用率: ${(rate * 100).toFixed(1)}%`}>
            <Tag color={color}>{used}/{limit}</Tag>
          </Tooltip>
        );
      },
    },
    {
      title: '项目限额',
      dataIndex: 'project_limit',
      key: 'project_limit',
      render: (limit: number, record: Organization) => {
        const stats = organizationStats.find((s: OrganizationStats) => s.id === record.id);
        const used = stats?.project_count || 0;
        const rate = stats?.project_usage_rate || 0;
        const color = rate > 0.8 ? 'red' : rate > 0.6 ? 'orange' : 'green';
        return (
          <Tooltip title={`使用率: ${(rate * 100).toFixed(1)}%`}>
            <Tag color={color}>{used}/{limit}</Tag>
          </Tooltip>
        );
      },
    },
    {
      title: 'Token限额',
      dataIndex: 'token_limit',
      key: 'token_limit',
      render: (limit: number, record: Organization) => {
        const stats = organizationStats.find((s: OrganizationStats) => s.id === record.id);
        const used = stats?.token_used || 0;
        const rate = stats?.token_usage_rate || 0;
        const color = rate > 0.9 ? 'red' : rate > 0.8 ? 'orange' : rate > 0.6 ? 'gold' : 'green';
        const formatNumber = (num: number) => {
          if (num >= 1000000) {
            return `${(num / 1000000).toFixed(1)}M`;
          } else if (num >= 1000) {
            return `${(num / 1000).toFixed(1)}K`;
          }
          return num.toString();
        };
        return (
          <Tooltip title={`使用率: ${(rate * 100).toFixed(1)}%`}>
            <Tag color={color}>{formatNumber(used)}/{formatNumber(limit)}</Tag>
          </Tooltip>
        );
      },
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text: string) => new Date(text).toLocaleDateString(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: Organization) => (
        <Space size="middle">
          <Tooltip title="查看详情">
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => handleViewOrganization(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="link"
              icon={<EditOutlined />}
              onClick={() => handleEditOrganization(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定删除这个企业吗？"
            onConfirm={() => handleDeleteOrganization(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="link"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 处理新建企业
  const handleCreateOrganization = () => {
    setEditingOrg(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 处理编辑企业
  const handleEditOrganization = (org: Organization) => {
    setEditingOrg(org);
    form.setFieldsValue({
      ...org,
      expire_at: org.expire_at ? new Date(org.expire_at).toISOString().split('T')[0] : undefined,
    });
    setModalVisible(true);
  };

  // 处理查看企业详情
  const handleViewOrganization = async (org: Organization) => {
    try {
      const [statsResponse, tokenResponse, limitsResponse] = await Promise.all([
        adminApi.getOrganizationStats(org.id),
        adminApi.getOrganizationTokenUsage(org.id),
        adminApi.checkOrganizationLimits(org.id)
      ]);
      
      const stats = statsResponse.data;
      const tokenInfo = tokenResponse.data;
      const limits = limitsResponse.data;
      
      const formatTokens = (num: number) => {
        if (num >= 1000000) {
          return `${(num / 1000000).toFixed(2)}M`;
        } else if (num >= 1000) {
          return `${(num / 1000).toFixed(1)}K`;
        }
        return num.toLocaleString();
      };
      
      Modal.info({
        title: `企业详情 - ${org.name}`,
        width: 700,
        content: (
          <div>
            <Row gutter={16}>
              <Col span={8}>
                <Statistic title="用户数量" value={stats.user_count} suffix={`/ ${stats.user_limit}`} />
              </Col>
              <Col span={8}>
                <Statistic title="项目数量" value={stats.project_count} suffix={`/ ${stats.project_limit}`} />
              </Col>
              <Col span={8}>
                <Statistic 
                  title="Token用量" 
                  value={formatTokens(stats.token_used)} 
                  suffix={`/ ${formatTokens(stats.token_limit)}`}
                  valueStyle={{ color: tokenInfo.is_exceeded ? '#ff4d4f' : '#1890ff' }}
                />
              </Col>
            </Row>
            <Row gutter={16} style={{ marginTop: 16 }}>
              <Col span={8}>
                <Statistic 
                  title="用户使用率" 
                  value={stats.user_usage_rate * 100} 
                  precision={1}
                  suffix="%" 
                  valueStyle={{ color: limits.user_limit_ok ? '#52c41a' : '#ff4d4f' }}
                />
              </Col>
              <Col span={8}>
                <Statistic 
                  title="项目使用率" 
                  value={stats.project_usage_rate * 100} 
                  precision={1}
                  suffix="%" 
                  valueStyle={{ color: limits.project_limit_ok ? '#52c41a' : '#ff4d4f' }}
                />
              </Col>
              <Col span={8}>
                <Statistic 
                  title="Token使用率" 
                  value={tokenInfo.usage_percentage} 
                  precision={1}
                  suffix="%" 
                  valueStyle={{ color: limits.token_limit_ok ? '#52c41a' : '#ff4d4f' }}
                />
              </Col>
            </Row>
            <Row gutter={16} style={{ marginTop: 16 }}>
              <Col span={24}>
                <div style={{ 
                  padding: '12px', 
                  backgroundColor: tokenInfo.is_exceeded ? '#fff2f0' : '#f6ffed',
                  border: `1px solid ${tokenInfo.is_exceeded ? '#ffccc7' : '#b7eb8f'}`,
                  borderRadius: '6px'
                }}>
                  <p style={{ margin: 0, fontWeight: 'bold', color: tokenInfo.is_exceeded ? '#ff4d4f' : '#52c41a' }}>
                    {tokenInfo.is_exceeded ? '⚠️ Token已超限' : '✅ Token使用正常'}
                  </p>
                  <p style={{ margin: '4px 0 0 0', fontSize: '12px', color: '#666' }}>
                    剩余Token: {formatTokens(tokenInfo.remaining)} | 
                    使用率: {tokenInfo.usage_percentage}% |
                    状态: {limits.all_limits_ok ? '所有限额正常' : '存在超限项目'}
                  </p>
                </div>
              </Col>
            </Row>
            <div style={{ marginTop: 16 }}>
              <p><strong>描述:</strong> {org.description || '无'}</p>
              <p><strong>联系邮箱:</strong> {org.contact_email || '无'}</p>
              <p><strong>联系电话:</strong> {org.contact_phone || '无'}</p>
              <p><strong>地址:</strong> {org.address || '无'}</p>
            </div>
          </div>
        ),
      });
    } catch (error) {
      message.error('获取企业详情失败');
    }
  };

  // 处理删除企业
  const handleDeleteOrganization = async (id: number) => {
    try {
      await adminApi.deleteOrganization(id);
      message.success('删除成功');
      fetchOrganizations(pagination.current, pagination.pageSize);
      fetchOrganizationStats();
    } catch (error) {
      message.error('删除失败');
    }
  };

  // 处理表单提交
  const handleFormSubmit = async (values: any) => {
    try {
      if (editingOrg) {
        await adminApi.updateOrganization(editingOrg.id, values);
        message.success('更新成功');
      } else {
        await adminApi.createOrganization(values);
        message.success('创建成功');
      }
      setModalVisible(false);
      fetchOrganizations(pagination.current, pagination.pageSize);
      fetchOrganizationStats();
    } catch (error) {
      message.error(editingOrg ? '更新失败' : '创建失败');
    }
  };

  // 计算总体统计
  const totalStats = organizationStats.reduce(
    (acc: any, stats: OrganizationStats) => ({
      totalUsers: acc.totalUsers + stats.user_count,
      totalProjects: acc.totalProjects + stats.project_count,
      totalTokens: acc.totalTokens + stats.token_used,
      totalOrgs: acc.totalOrgs + 1,
      activeOrgs: acc.activeOrgs + (stats.status === 1 ? 1 : 0),
    }),
    { totalUsers: 0, totalProjects: 0, totalTokens: 0, totalOrgs: 0, activeOrgs: 0 }
  );

  const actionButtons = [
    <Button
      key="create"
      type="primary"
      icon={<PlusOutlined />}
      onClick={handleCreateOrganization}
    >
      新建企业
    </Button>
  ];

  return (
    <AdminPage
      title="企业管理"
      actionButtons={actionButtons}
    >
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="企业总数"
              value={totalStats.totalOrgs}
              prefix={<BuildOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="活跃企业"
              value={totalStats.activeOrgs}
              prefix={<BuildOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总用户数"
              value={totalStats.totalUsers}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总项目数"
              value={totalStats.totalProjects}
              prefix={<ProjectOutlined />}
            />
          </Card>
        </Col>
      </Row>
      
      {/* Token使用统计 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={8}>
          <Card>
            <Statistic
              title="Token总用量"
              value={totalStats.totalTokens}
              prefix={<AlertOutlined />}
              formatter={(value) => {
                const num = Number(value);
                if (num >= 1000000) {
                  return `${(num / 1000000).toFixed(2)}M`;
                } else if (num >= 1000) {
                  return `${(num / 1000).toFixed(1)}K`;
                }
                return num.toLocaleString();
              }}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="超限企业"
              value={organizationStats.filter(s => s.token_usage_rate >= 1.0).length}
              prefix={<AlertOutlined />}
              valueStyle={{ color: organizationStats.filter(s => s.token_usage_rate >= 1.0).length > 0 ? '#ff4d4f' : '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="高用量企业"
              value={organizationStats.filter(s => s.token_usage_rate >= 0.8 && s.token_usage_rate < 1.0).length}
              prefix={<AlertOutlined />}
              valueStyle={{ color: organizationStats.filter(s => s.token_usage_rate >= 0.8).length > 0 ? '#fa8c16' : '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 企业列表 */}
      <Table
        columns={columns}
        dataSource={organizations}
        rowKey="id"
        loading={loading}
        pagination={{
          ...pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total: number, range: [number, number]) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
        }}
        onChange={(pag: any) => {
          fetchOrganizations(pag.current!, pag.pageSize!);
        }}
      />

      {/* 创建/编辑企业弹窗 */}
      <Modal
        title={editingOrg ? '编辑企业' : '新建企业'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleFormSubmit}
        >
          <Form.Item
            label="企业名称"
            name="name"
            rules={[{ required: true, message: '请输入企业名称' }]}
          >
            <Input placeholder="请输入企业名称" />
          </Form.Item>

          <Form.Item
            label="企业描述"
            name="description"
          >
            <TextArea rows={3} placeholder="请输入企业描述" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label="用户限额"
                name="user_limit"
                rules={[{ required: true, message: '请输入用户限额' }]}
              >
                <InputNumber min={1} placeholder="用户限额" style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="项目限额"
                name="project_limit"
                rules={[{ required: true, message: '请输入项目限额' }]}
              >
                <InputNumber min={1} placeholder="项目限额" style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="Token限额"
                name="token_limit"
                rules={[{ required: true, message: '请输入Token限额' }]}
              >
                <InputNumber min={0} placeholder="Token限额" style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="状态"
                name="status"
                rules={[{ required: true, message: '请选择状态' }]}
              >
                <Select placeholder="请选择状态">
                  <Option value={1}>正常</Option>
                  <Option value={2}>停用</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="过期时间"
                name="expire_at"
              >
                <Input type="date" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="联系邮箱"
                name="contact_email"
              >
                <Input placeholder="联系邮箱" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="联系电话"
                name="contact_phone"
              >
                <Input placeholder="联系电话" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="地址"
            name="address"
          >
            <TextArea rows={2} placeholder="请输入地址" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingOrg ? '更新' : '创建'}
              </Button>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </AdminPage>
  );
};

export default OrganizationManagement; 