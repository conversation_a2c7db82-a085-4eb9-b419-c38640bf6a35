import React, { useState, useEffect } from 'react';
import { Layout, Menu, Button, Avatar, Dropdown, Space, Typography } from 'antd';
import {
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  DashboardOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  BuildOutlined,
} from '@ant-design/icons';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import { adminAuthApi } from '../services/adminApi';

const { Header, Sider, Content } = Layout;
const { Title } = Typography;

const AdminDashboard: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [adminInfo, setAdminInfo] = useState<any>(null);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    // 获取管理员信息
    const storedAdminInfo = localStorage.getItem('adminInfo');
    if (storedAdminInfo) {
      try {
        setAdminInfo(JSON.parse(storedAdminInfo));
      } catch (e) {
        console.error('解析管理员信息失败:', e);
        // 信息损坏，清理并跳转登录
        localStorage.removeItem('adminToken');
        localStorage.removeItem('adminInfo');
        navigate('/admin/login');
      }
    } else {
      // 没有管理员信息，跳转登录
      navigate('/admin/login');
    }
  }, [navigate]);

  // 处理菜单点击
  const handleMenuClick = (key: string) => {
    switch (key) {
      case 'users':
        navigate('/admin/users');
        break;
      case 'organizations':
        navigate('/admin/organizations');
        break;
      default:
        break;
    }
  };

  // 处理登出
  const handleLogout = () => {
    adminAuthApi.logout();
  };

  // 用户下拉菜单
  const userMenu = (
    <Menu
      items={[
        {
          key: 'logout',
          icon: <LogoutOutlined />,
          label: '退出登录',
          onClick: handleLogout,
        },
      ]}
    />
  );

  // 侧边栏菜单项
  const sideMenuItems = [
    {
      key: 'users',
      icon: <UserOutlined />,
      label: '用户管理',
    },
    {
      key: 'organizations',
      icon: <BuildOutlined />,
      label: '企业管理',
    },
  ];

  // 获取当前选中的菜单键
  const getSelectedKeys = () => {
    const path = location.pathname;
    if (path.includes('/admin/users')) return ['users'];
    if (path.includes('/admin/organizations')) return ['organizations'];
    return ['users'];
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider trigger={null} collapsible collapsed={collapsed}>
        <div style={{ 
          height: 64, 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          borderBottom: '1px solid #f0f0f0'
        }}>
          <Title level={4} style={{ margin: 0, color: 'white' }}>
            {collapsed ? '管理' : '管理后台'}
          </Title>
        </div>
        
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={getSelectedKeys()}
          items={sideMenuItems}
          onClick={({ key }) => handleMenuClick(key)}
        />
      </Sider>
      
      <Layout>
        <Header style={{ 
          padding: '0 16px', 
          background: '#fff', 
          display: 'flex', 
          alignItems: 'center',
          justifyContent: 'space-between',
          borderBottom: '1px solid #f0f0f0'
        }}>
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => setCollapsed(!collapsed)}
            style={{ fontSize: '16px', width: 64, height: 64 }}
          />
          
          <Space>
            <span>欢迎, {adminInfo?.username || '管理员'}</span>
            <Dropdown overlay={userMenu} placement="bottomRight">
              <Avatar icon={<UserOutlined />} style={{ cursor: 'pointer' }} />
            </Dropdown>
          </Space>
        </Header>
        
        <Content style={{ 
          margin: 0,
          minHeight: 280,
          background: '#f5f5f5'
        }}>
          <Outlet />
        </Content>
      </Layout>
    </Layout>
  );
};

export default AdminDashboard; 