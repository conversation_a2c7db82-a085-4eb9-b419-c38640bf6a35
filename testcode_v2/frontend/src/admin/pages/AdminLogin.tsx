import * as React from 'react';
import { useState } from 'react';
import { Form, Input, Button, Card, message, Typography } from 'antd';
import { UserOutlined, LockOutlined, SettingOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { adminAuthApi, AdminLoginData } from '../services/adminApi';

const { Title } = Typography;

const AdminLogin: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const { t } = useTranslation();

  const onFinish = async (values: AdminLoginData) => {
    setLoading(true);
    
    // 先清理旧的认证信息
    localStorage.removeItem('adminToken');
    localStorage.removeItem('adminInfo');
    
    try {
      const response = await adminAuthApi.login(values);
      
      // 检查响应状态和数据结构
      if (response.status === 200 && response.data?.access_token && response.data?.admin_info) {
        // 保存管理员token和信息到localStorage
        localStorage.setItem('adminToken', response.data.access_token);
        localStorage.setItem('adminInfo', JSON.stringify(response.data.admin_info));
        
        message.success(t('common:admin.login.loginSuccess'));
        
        // 跳转到管理员主页
        navigate('/admin/users');
      } else {
        // 响应格式不正确
        message.error(t('common:admin.login.errors.invalidResponse'));
      }
    } catch (error: any) {
      console.error('Login failed:', error);
      
      // 确保清理认证信息
      localStorage.removeItem('adminToken');
      localStorage.removeItem('adminInfo');
      
      // 详细的错误分类处理
      if (!error.response) {
        // 网络错误：无响应
        message.error(t('common:admin.login.errors.networkError'));
      } else {
        const status = error.response.status;
        const errorData = error.response.data;
        
        switch (status) {
          case 401:
            message.error(t('common:admin.login.errors.loginFailed'));
            break;
          case 403:
            message.error('Account disabled, please contact administrator');
            break;
          case 404:
            message.error('Login endpoint not found, please contact technical support');
            break;
          case 429:
            message.error('Too many login attempts, please try again later');
            break;
          case 500:
          case 502:
          case 503:
          case 504:
            message.error(t('common:admin.login.errors.serverError'));
            break;
          default:
            // 使用后端返回的具体错误信息
            if (errorData?.detail) {
              message.error(`${t('common:admin.login.errors.loginFailed')}: ${errorData.detail}`);
            } else if (errorData?.message) {
              message.error(`${t('common:admin.login.errors.loginFailed')}: ${errorData.message}`);
            } else {
              message.error(t('common:admin.login.errors.unknownError'));
            }
        }
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ 
      minHeight: '100vh', 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    }}>
      <Card 
        style={{ 
          width: 400,
          boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
          borderRadius: '16px'
        }}
      >
        <div style={{ textAlign: 'center', marginBottom: 32 }}>
          <SettingOutlined style={{ fontSize: '48px', color: '#1890ff', marginBottom: 16 }} />
          <Title level={2} style={{ margin: 0 }}>{t('common:admin.login.title')}</Title>
          <p style={{ color: '#666', marginTop: 8 }}>{t('common:admin.login.subtitle')}</p>
        </div>

        <Form
          name="admin-login"
          size="large"
          onFinish={onFinish}
          autoComplete="off"
        >
          <Form.Item
            name="username"
            rules={[
              { required: true, message: t('common:admin.login.validation.usernameRequired') },
              { min: 3, message: t('common:admin.login.validation.usernameMinLength') }
            ]}
          >
            <Input 
              prefix={<UserOutlined />} 
              placeholder={t('common:admin.login.username')} 
              autoComplete="username"
            />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[
              { required: true, message: t('common:admin.login.validation.passwordRequired') },
              { min: 6, message: t('common:admin.login.validation.passwordMinLength') }
            ]}
          >
            <Input.Password 
              prefix={<LockOutlined />} 
              placeholder={t('common:admin.login.password')} 
              autoComplete="current-password"
            />
          </Form.Item>

          <Form.Item>
            <Button 
              type="primary" 
              htmlType="submit" 
              loading={loading}
              style={{ 
                width: '100%',
                height: '48px',
                borderRadius: '8px',
                fontSize: '16px',
                fontWeight: 500
              }}
            >
              {loading ? t('common:admin.login.loggingIn') : t('common:admin.login.loginButton')}
            </Button>
          </Form.Item>
        </Form>

        <div style={{ textAlign: 'center', marginTop: 16, color: '#999', fontSize: '12px' }}>
          <p>{t('common:admin.login.footer')}</p>
        </div>
      </Card>
    </div>
  );
};

export default AdminLogin; 