import { useState, useCallback } from 'react';
import { projectIntelligenceApi } from '../services/api';

interface IntelligenceSession {
  sessionId: string | null;
  status: 'idle' | 'analyzing' | 'questioning' | 'predicting' | 'completed';
  readinessEvaluation: any | null;
  questions: any[];
  userAnswers: Record<string, any>;
  scenarioPredictions: any[];
  loading: boolean;
  error: string | null;
}

export const useProjectIntelligence = () => {
  const [session, setSession] = useState<IntelligenceSession>({
    sessionId: null,
    status: 'idle',
    readinessEvaluation: null,
    questions: [],
    userAnswers: {},
    scenarioPredictions: [],
    loading: false,
    error: null
  });

  const startIntelligenceCheck = useCallback(async (projectId: string) => {
    setSession(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      // 启动会话
      const response = await projectIntelligenceApi.startSession(projectId);
      const sessionId = response.data.session_id;
      
      setSession(prev => ({
        ...prev,
        sessionId: sessionId,
        status: 'analyzing'
      }));
      
      // 自动开始项目就绪状态评估
      const evaluationResponse = await projectIntelligenceApi.analyzeUnderstanding(sessionId);
      const evaluation = evaluationResponse.data;
      
      setSession(prev => ({
        ...prev,
        readinessEvaluation: evaluation,
        status: 'questioning'
        // 保持loading=true，直到问题生成完成
      }));
      
      // 生成智能问题
      const questionsResponse = await projectIntelligenceApi.generateQuestions(sessionId);
      const questions = questionsResponse.data.questions;
      
      setSession(prev => ({
        ...prev,
        questions: questions,
        loading: false  // 所有数据加载完成后才设置loading=false
      }));
      
    } catch (error: any) {
      console.error('智能理解启动失败:', error);
      setSession(prev => ({
        ...prev,
        error: error.response?.data?.detail || error.message || '启动智能理解失败',
        loading: false
      }));
    }
  }, []);

  const submitAnswers = useCallback(async (answers: Record<string, any>) => {
    if (!session.sessionId) return;
    
    setSession(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      await projectIntelligenceApi.saveAnswers(session.sessionId, answers);
      
      setSession(prev => ({
        ...prev,
        userAnswers: answers,
        status: 'predicting'
      }));
      
      // 生成场景预测
      const predictionsResponse = await projectIntelligenceApi.predictScenarios(session.sessionId);
      const predictions = predictionsResponse.data.scenarios;
      
      setSession(prev => ({
        ...prev,
        scenarioPredictions: predictions,
        loading: false
      }));
      
    } catch (error: any) {
      console.error('提交回答失败:', error);
      setSession(prev => ({
        ...prev,
        error: error.response?.data?.detail || error.message || '提交回答失败',
        loading: false
      }));
    }
  }, [session.sessionId]);

  const completeIntelligence = useCallback(async (scenarioConfirmations?: Record<string, any>) => {
    if (!session.sessionId) return null;
    
    setSession(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      // 如果有场景确认信息，先保存
      if (scenarioConfirmations && Object.keys(scenarioConfirmations).length > 0) {
        await projectIntelligenceApi.saveScenarioConfirmations(session.sessionId, scenarioConfirmations);
      }
      
      const response = await projectIntelligenceApi.completeSession(session.sessionId);
      const summary = response.data;
      
      setSession(prev => ({
        ...prev,
        status: 'completed',
        loading: false
      }));
      
      return summary;
      
    } catch (error: any) {
      console.error('完成智能理解失败:', error);
      setSession(prev => ({
        ...prev,
        error: error.response?.data?.detail || error.message || '完成智能理解失败',
        loading: false
      }));
      return null;
    }
  }, [session.sessionId]);

  const resetSession = useCallback(() => {
    setSession({
      sessionId: null,
      status: 'idle',
      readinessEvaluation: null,
      questions: [],
      userAnswers: {},
      scenarioPredictions: [],
      loading: false,
      error: null
    });
  }, []);

  const clearError = useCallback(() => {
    setSession(prev => ({ ...prev, error: null }));
  }, []);

  return {
    session,
    startIntelligenceCheck,
    submitAnswers,
    completeIntelligence,
    resetSession,
    clearError
  };
}; 