import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import api from '../services/api';

export const useLanguageInit = () => {
  const { i18n } = useTranslation();

  useEffect(() => {
    const initLanguage = async () => {
      try {
        // 1. 首先尝试从localStorage获取语言设置
        const localLanguage = localStorage.getItem('userLanguage');
        
        // 2. 如果用户已登录，尝试从服务器获取语言偏好
        const token = localStorage.getItem('token');
        if (token) {
          try {
            const response = await api.get('/users/language-preference', {
              headers: {
                'Authorization': `Bearer ${token}`,
              },
            });
            
            const serverLanguage = response.data?.data?.language;
            
            // 3. 优先使用服务器上的语言设置
            if (serverLanguage && serverLanguage !== localLanguage) {
              localStorage.setItem('userLanguage', serverLanguage);
              await i18n.changeLanguage(serverLanguage);
              return;
            }
          } catch (error) {
            console.warn('Failed to fetch user language preference:', error);
            // 如果服务器请求失败，继续使用本地设置
          }
        }

        // 4. 使用本地存储的语言或默认语言
        const targetLanguage = localLanguage || 'zh-CN';
        if (i18n.language !== targetLanguage) {
          await i18n.changeLanguage(targetLanguage);
        }
      } catch (error) {
        console.error('Failed to initialize language:', error);
        // 出错时使用默认中文
        await i18n.changeLanguage('zh-CN');
      }
    };

    initLanguage();
  }, [i18n]);
};

export default useLanguageInit;