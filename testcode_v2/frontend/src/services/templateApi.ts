import api from './api';

// 模板类型枚举
export enum TemplateType {
  WORKFLOW = 'workflow',     // 分析流程模板
  SPECIFICATION = 'specification', // 分析规范模板
  PROMPT = 'prompt'          // 提示词模板
}

// 分析步骤配置
export interface AnalysisStep {
  id: string;
  name: string;
  description: string;
  required: boolean;
  order: number;
  prompt_template?: string;
  expected_output?: string;
}

// 规范配置
export interface SpecificationConfig {
  data_requirements?: string[];  // 数据要求
  output_format?: string;        // 输出格式
  quality_criteria?: string[];   // 质量标准
  validation_rules?: string[];   // 验证规则
}

export interface Template {
  id: string;
  name: string;
  description?: string;
  content: string;
  category?: string;            // 模板分类（销售、财务、用户等）
  tags?: string[];              // 标签
  template_type: string;        // 模板类型：query, workflow, specification
  complexity_level?: 'basic' | 'intermediate' | 'advanced'; // 复杂度
  applicable_data_types?: string[];     // 适用数据类型
  is_public: boolean;
  is_system: boolean;
  usage_count: number;
  created_at: string;
  created_by?: number;
  creator_name?: string;
  can_edit: boolean;
  can_delete: boolean;

  // 结构化配置（可选，后续阶段实现）
  workflow_steps?: AnalysisStep[];      // 流程步骤
  specification_config?: SpecificationConfig; // 规范配置
}

export interface TemplateCreateRequest {
  name: string;
  description?: string;
  content: string;
  category?: string;
  tags?: string[];
  template_type?: string;
  complexity_level?: string;
  applicable_data_types?: string[];
  is_public?: boolean;
}

export interface TemplateUpdateRequest {
  name?: string;
  description?: string;
  content?: string;
  is_public?: boolean;
}

export interface TemplateListParams {
  projectId: string;
  search?: string;
}

export class TemplateApi {
  /**
   * 获取模板列表
   */
  static async getTemplates(params: TemplateListParams): Promise<Template[]> {
    // 转换参数名以匹配后端API
    const apiParams = {
      project_id: params.projectId,
      search: params.search
    };

    const response = await api.get('/templates/templates', { params: apiParams });
    return response.data;
  }

  /**
   * 创建模板
   */
  static async createTemplate(
    projectId: string,
    data: TemplateCreateRequest
  ): Promise<Template> {
    const response = await api.post('/templates/templates', data, {
      params: { project_id: projectId }
    });
    return response.data;
  }

  /**
   * 更新模板
   */
  static async updateTemplate(
    templateId: string,
    data: TemplateUpdateRequest
  ): Promise<Template> {
    const response = await api.put(`/templates/templates/${templateId}`, data);
    return response.data;
  }

  /**
   * 删除模板
   */
  static async deleteTemplate(templateId: string): Promise<void> {
    await api.delete(`/templates/templates/${templateId}`);
  }

  /**
   * 使用模板（记录使用次数）
   */
  static async recordTemplateUsage(templateId: string): Promise<{ usageCount: number }> {
    const response = await api.post(`/templates/templates/${templateId}/use`);
    return response.data;
  }

  // 移除了模板分类功能
}

export default TemplateApi;
