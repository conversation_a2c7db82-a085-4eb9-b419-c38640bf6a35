import axios, { AxiosRequestConfig } from 'axios';
import { message } from 'antd';

// 存储正在进行的请求
const pendingRequests = new Map();

// 401错误处理状态标记
let isHandling401 = false;

// 全局401错误处理函数
const handle401Error = () => {
  if (isHandling401) {
    return;
  }
  
  isHandling401 = true;
  
  // 清除本地token
  localStorage.removeItem('token');
  localStorage.removeItem('userInfo');
  
  // 如果不是在登录页面，则重定向到登录页面
  if (window.location.pathname !== '/login') {
    message.error('登录已过期，请重新登录');
    setTimeout(() => {
      window.location.href = '/login';
    }, 1500);
  }
};

// 创建标识请求的key
const generateRequestKey = (config: AxiosRequestConfig): string => {
  const { url, method, params, data } = config;
  return [url, method, JSON.stringify(params), JSON.stringify(data)].join('&');
};

// 取消重复请求
const cancelPendingRequests = (config: AxiosRequestConfig): void => {
  const requestKey = generateRequestKey(config);
  if (pendingRequests.has(requestKey)) {
    // 取消上一个相同的请求
    const controller = pendingRequests.get(requestKey);
    controller.abort();
    pendingRequests.delete(requestKey);
  }
};

// 创建axios实例
const api = axios.create({
  baseURL: 'http://localhost:8301/api/v1',
  timeout: 60000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 获取token
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    
    // 支持AbortController
    const controller = new AbortController();
    config.signal = controller.signal;
    
    // 取消之前的重复请求
    cancelPendingRequests(config);
    
    // 存储当前请求
    pendingRequests.set(generateRequestKey(config), controller);
    
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  response => {
    // 请求完成后，从pendingRequests中移除
    pendingRequests.delete(generateRequestKey(response.config));
    return response;
  },
  error => {
    // 请求被取消不需要提示错误
    if (axios.isCancel(error)) {
      console.log('请求被取消:', error.message);
      return new Promise(() => {});
    }
    
    // 请求失败后，从pendingRequests中移除
    if (error.config) {
      pendingRequests.delete(generateRequestKey(error.config));
    }
    
    // 处理未授权错误，跳转到登录页面
    if (error.response && error.response.status === 401) {    
      // 如果在登录页面，让错误正常传播到业务代码处理
      if (window.location.pathname === '/login') {
        return Promise.reject(error);
      }
      
      // 在其他页面，调用统一的401错误处理函数
      handle401Error();
      
      // 返回一个永远不会resolve的Promise，阻止业务代码继续处理401错误
      // 这样可以确保401错误不会传播到业务代码中，避免业务层重复处理
      return new Promise(() => {});
    }
    
    return Promise.reject(error);
  }
);

// 认证相关API
const authApi = {
  // 用户登录
  login: (data: { username: string; password: string }) => api.post('/auth/login', data),
  
  // 获取当前用户信息
  getCurrentUser: () => api.get('/auth/me'),
  
  // 检查是否已登录
  isLoggedIn: () => {
    return !!localStorage.getItem('token');
  },
  
  // 重置401处理状态（用于登录成功后重置）
  reset401Status: () => {
    isHandling401 = false;
  },
  
  // 手动触发401错误处理（用于业务代码中需要时调用）
  handle401: () => {
    handle401Error();
  },
  
  // 退出登录
  logout: () => {
    localStorage.removeItem('token');
    localStorage.removeItem('userInfo');
    window.location.href = '/login';
  }
};

// 项目相关API
const projectApi = {
  // 获取项目列表
  getProjects: (params?: any) => api.get('/projects/', { params }),
  
  // 获取所有项目（不分页，用于下拉列表）
  getAllProjects: () => api.get('/projects/', { params: { skip: 0, limit: 100 } }),
  
  // 获取项目详情
  getProject: (id: string) => api.get(`/projects/${id}`),
  
  // 创建项目
  createProject: (data: any) => api.post('/projects/', data),
  
  // 更新项目
  updateProject: (id: string, data: any) => api.put(`/projects/${id}`, data),
  
  // 删除项目
  deleteProject: (id: string) => api.delete(`/projects/${id}`),
  
  // 获取项目关联的表
  getProjectTables: (id: string) => api.get(`/projects/${id}/tables`),
  
  // 获取项目选定的表（用于表描述步骤）
  getSelectedTables: (id: string) => api.get(`/projects/${id}/selected-tables`),
  
  // 更新表描述
  updateTableDescription: (projectId: string, tableId: string, description: string) => 
    api.put(`/projects/${projectId}/selected-tables/${tableId}`, { table_description: description })
};

// 数据源相关API
const dataSourceApi = {
  // 获取数据源列表
  getDataSources: (params?: any) => api.get('/data-sources/', { params }),
  
  // 获取数据源详情
  getDataSource: (id: string) => api.get(`/data-sources/${id}`),
  
  // 创建数据源
  createDataSource: (data: any) => api.post('/data-sources/', data),
  
  // 更新数据源
  updateDataSource: (id: string, data: any) => api.put(`/data-sources/${id}`, data),
  
  // 删除数据源
  deleteDataSource: (id: string) => api.delete(`/data-sources/${id}`),
  
  // 测试数据源连接
  testConnection: (id: string) => api.post(`/data-sources/${id}/test-connection`),
};

// 工具相关API
const toolApi = {
  // 获取工具列表
  getTools: (params?: any) => api.get('/tools/', { params }),
  
  // 获取工具详情
  getTool: (id: string) => api.get(`/tools/${id}`),
  
  // 创建工具
  createTool: (data: any) => api.post('/tools/', data),
  
  // 更新工具
  updateTool: (id: string, data: any) => api.put(`/tools/${id}`, data),
  
  // 删除工具
  deleteTool: (id: string) => api.delete(`/tools/${id}`),
  
  // 测试工具
  testTool: (id: string, data: any) => api.post(`/tools/${id}/test`, data),
};

// 项目成员相关API
const projectMemberApi = {
  // 获取项目成员列表
  getProjectMembers: (projectId: string) => api.get(`/projects/${projectId}/members`),
  
  // 邀请项目成员
  inviteMember: (projectId: string, data: any) => api.post(`/projects/${projectId}/members`, data),
  
  // 更新项目成员角色
  updateMember: (projectId: string, userId: number, data: any) => 
    api.put(`/projects/${projectId}/members/${userId}`, data),
  
  // 移除项目成员
  removeMember: (projectId: string, userId: number) => 
    api.delete(`/projects/${projectId}/members/${userId}`),
  
  // 获取可邀请的用户列表
  getInvitableUsers: (projectId: string) => api.get(`/projects/${projectId}/invitable-users`),
};

// 企业组织相关API
const organizationApi = {
  // 获取企业列表
  getOrganizations: (params?: any) => api.get('/organizations/', { params }),
  
  // 获取企业详情
  getOrganization: (id: number) => api.get(`/organizations/${id}`),
  
  // 创建企业
  createOrganization: (data: any) => api.post('/organizations/', data),
  
  // 更新企业
  updateOrganization: (id: number, data: any) => api.put(`/organizations/${id}`, data),
  
  // 获取企业用户列表
  getOrganizationUsers: (orgId: number) => api.get(`/organizations/${orgId}/users`),
};

// 分析相关API
const analysisApi = {
  // 执行分析
  executeAnalysis: (data: any) => {
    // 确保data存在且是对象
    const requestData = { ...data };
    
    // 如果有工具类型字段，确保大写
    if (requestData.tools && Array.isArray(requestData.tools)) {
      requestData.tools = requestData.tools.map((tool: any) => {
        if (tool.tool_type && typeof tool.tool_type === 'string') {
          tool.tool_type = tool.tool_type.toUpperCase();
        }
        return tool;
      });
    }
    
    return api.post('/analysis/', requestData);
  },
  
  // 执行流式分析（使用SSE）
  executeAnalysisStream: (data: any, callbacks: { 
    onStart?: () => void,
    onEvent?: (event: string, data: any) => void,
    onError?: (error: any) => void,
    onComplete?: (analysisId: string) => void
  }) => {
    // 确保data存在且是对象
    const requestData = { ...data };
    
    // 如果有工具类型字段，确保大写
    if (requestData.tools && Array.isArray(requestData.tools)) {
      requestData.tools = requestData.tools.map((tool: any) => {
        if (tool.tool_type && typeof tool.tool_type === 'string') {
          tool.tool_type = tool.tool_type.toUpperCase();
        }
        return tool;
      });
    }
    
    // 使用流式分析端点
    const url = `${api.defaults.baseURL}/analysis/stream`;
    
    // 创建URLSearchParams对象，用于POST请求体
    const params = new URLSearchParams();
    params.append('project_id', requestData.project_id);
    params.append('query', requestData.query);
    
    // 创建EventSource连接流式端点
    const eventSource = new EventSource(`${url}?${params.toString()}`);
    
    // 注册一般消息处理器
    eventSource.onmessage = (event) => {
      try {
        const parsedData = JSON.parse(event.data);
        const eventType = parsedData.event;
        const eventData = parsedData.data;
        
        // 调用事件回调
        if (callbacks.onEvent) {
          callbacks.onEvent(eventType, eventData);
        }
        
        // 对特定事件进行处理
        if (eventType === 'completed' && callbacks.onComplete) {
          callbacks.onComplete(eventData.id);
          eventSource.close();
        }
      } catch (error) {
        console.error('解析SSE事件数据失败:', error);
      }
    };
    
    // 处理错误
    eventSource.onerror = (error) => {
      if (callbacks.onError) {
        callbacks.onError(error);
      }
      eventSource.close();
    };
    
    // 连接打开时的回调
    eventSource.onopen = () => {
      if (callbacks.onStart) {
        callbacks.onStart();
      }
    };
    
    // 返回事件源，以便调用者可以控制关闭
    return eventSource;
  },
  
  // 获取分析记录列表
  getAnalyses: (params?: any) => api.get('/analysis/', { params }),
  
  // 获取分析记录详情
  getAnalysis: (id: string) => api.get(`/analysis/${id}`),
  
  // 删除分析记录
  deleteAnalysis: (id: string) => api.delete(`/analysis/${id}`),
  
  // 获取工具执行记录
  getToolExecutions: (analysisId: string) => api.get(`/analysis/${analysisId}/executions`),
};

// LLM分析相关API
const llmAnalysisApi = {
  // 执行流式LLM分析
  executeStreamAnalysis: (data: any, callbacks: { 
    onStart?: () => void,
    onEvent?: (event: string, data: any) => void,
    onError?: (error: any) => void,
    onComplete?: (analysisId: string) => void
  }) => {
    const { project_id, query, continue_analysis_id, max_planning_rounds } = data;
    
    // 创建URL参数
    const params = new URLSearchParams();
    if (project_id) params.append('project_id', project_id);
    if (query) params.append('query', query);
    if (continue_analysis_id) params.append('continue_analysis_id', continue_analysis_id);
    if (max_planning_rounds) params.append('max_planning_rounds', max_planning_rounds.toString());
    
    const url = `${api.defaults.baseURL}/analysis/llm-stream`;
    
    // 创建新的EventSource
    const eventSource = new EventSource(`${url}?${params.toString()}`);
    let analysisId: string | null = null;
    
    // 注册各种事件处理函数
    eventSource.onopen = () => {
      if (callbacks?.onStart) callbacks.onStart();
    };
    
    eventSource.onerror = (error) => {
      console.error('LLM分析流式连接出错:', error);
      eventSource.close();
      if (callbacks?.onError) callbacks.onError(error);
    };
    
    eventSource.onmessage = (event) => {
      try {
        const eventData = JSON.parse(event.data);
        
        // 处理不同类型的事件
        if (eventData.event) {
          if (eventData.event === 'completed') {
            // **特殊处理：如果是超时取消的completed事件，也需要触发onEvent**
            if (eventData.data?.cancelled && eventData.data?.reason === 'interrupt_timeout') {
              console.log('🔔 检测到超时取消的completed事件，同时触发onEvent和onComplete');
              if (callbacks?.onEvent) callbacks.onEvent(eventData.event, eventData.data);
            }

            analysisId = eventData.data?.id || analysisId;
            eventSource.close();
            if (callbacks?.onComplete) callbacks.onComplete(analysisId || '');
          } else {
            if (callbacks?.onEvent) callbacks.onEvent(eventData.event, eventData.data);
          }
        }
      } catch (error) {
        console.error('解析事件数据出错:', error, event.data);
      }
    };
    
    // 返回EventSource对象以便于管理
    return eventSource;
  },
  
  // 获取LLM分析历史记录
  getLlmAnalysisHistory: (params?: any) => api.get('/analysis/llm/history', { params }),
  
  // 获取LLM分析详情
  getLlmAnalysisDetail: (id: string) => api.get(`/analysis/llm/${id}`),
  
  // 取消LLM分析
  cancelLlmAnalysis: (analysisId: string) => api.post(`/analysis/llm/${analysisId}/cancel`),
};

// LLM模型管理相关API
const llmModelApi = {
  // 获取模型列表
  getModels: (params?: any) => api.get('/llm-models/', { params }),

  // 获取模型详情
  getModel: (id: string) => api.get(`/llm-models/${id}`),

  // 创建模型
  createModel: (data: any) => api.post('/llm-models/', data),

  // 更新模型
  updateModel: (id: string, data: any) => api.put(`/llm-models/${id}`, data),

  // 删除模型
  deleteModel: (id: string) => api.delete(`/llm-models/${id}`),

  // 使用配置测试模型（无需保存）
  testModelConfig: (configData: any) => api.post('/llm-models/test-config', configData),
};

// 项目向导相关API
const projectWizardApi = {
  // 创建项目(步骤1)
  createProject: (data: any) => api.post('/project-wizard/create_project', data),
  
  // 测试数据源连接(步骤2)
  testConnection: (data: any) => {
    console.log('测试连接参数:', JSON.stringify(data, null, 2));
    // 确保data包含必要参数
    if (!data.type) {
      console.error('错误: 缺少type参数');
      data.type = 'mysql'; // 使用默认值
    }
    if (!data.config) {
      console.error('错误: 缺少config参数');
      data.config = {};
    }
    
    // 确保端口是数字类型
    if (data.config && data.config.port && typeof data.config.port === 'string') {
      data.config.port = parseInt(data.config.port, 10);
    }
    
    // 深拷贝data对象，避免引用问题
    const processedData = JSON.parse(JSON.stringify(data));
    console.log('处理后的请求参数:', JSON.stringify(processedData, null, 2));
    
    // 直接构造请求对象，确保格式正确
    return api.post('/project-wizard/test_connection', processedData);
  },
  
  // 添加数据源(步骤2)
  addDataSource: (data: any) => api.post('/project-wizard/add_datasource', data),
  
  // 获取数据源中的表列表(步骤3)
  getDataSourceTables: (datasourceId: string) => api.get(`/project-wizard/datasource_tables/${datasourceId}`),
  
  // 保存选定的表(步骤3)
  saveTables: (datasourceId: string, data: any) => api.post(`/project-wizard/save_tables/${datasourceId}`, data),
  
  // 保存选定表并进入描述步骤(步骤3) - 增加超时时间到3分钟
  saveSelectedTables: (projectId: string, selectedTables: string[]) => 
    api.post(`/project-wizard/save_selected_tables/${projectId}`, { tables: selectedTables }, { timeout: 180000 }),
  
  // 获取项目选定的表（用于表描述步骤）
  getSelectedTables: (projectId: string) => api.get(`/projects/${projectId}/selected-tables`),
  
  // 更新表描述(步骤4)
  updateTableDescription: (projectId: string, tableId: string, description: string) =>
    api.put(`/projects/${projectId}/selected-tables/${tableId}`, { table_description: description })
};

// 会话管理API
const conversationApi = {
  // 创建会话
  createConversation: (data: { project_id: string; initial_query: string }) =>
    api.post('/conversations', data),
  
  // 获取会话详情
  getConversation: (conversationId: string) =>
    api.get(`/conversations/${conversationId}`),
  
  // 获取会话分析列表
  getConversationAnalyses: (conversationId: string) =>
    api.get(`/conversations/${conversationId}/analyses`),
  
  // 在会话中创建新分析
  createAnalysisInConversation: (conversationId: string, data: any) =>
    api.post(`/conversations/${conversationId}/analyses`, data),
  
  // 获取项目的所有会话（支持分页）
  getProjectConversations: (projectId: string, params?: { page?: number; page_size?: number }) => {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.page_size) searchParams.append('page_size', params.page_size.toString());
    const queryString = searchParams.toString();
    return api.get(`/conversations/projects/${projectId}/conversations${queryString ? '?' + queryString : ''}`);
  },
  
  // 更新会话标题
  updateConversation: (conversationId: string, data: { title?: string; status?: string }) =>
    api.put(`/conversations/${conversationId}`, data),
  
  // 删除会话
  deleteConversation: (conversationId: string) =>
    api.delete(`/conversations/${conversationId}`),
  
  // 新增：获取会话上下文
  getConversationContext: (conversationId: string, params?: { 
    max_rounds?: number; 
    context_types?: string[] 
  }) => {
    const searchParams = new URLSearchParams();
    if (params?.max_rounds) searchParams.append('max_rounds', params.max_rounds.toString());
    if (params?.context_types) {
      params.context_types.forEach(type => searchParams.append('context_types', type));
    }
    const queryString = searchParams.toString();
    return api.get(`/conversations/${conversationId}/context${queryString ? '?' + queryString : ''}`);
  },

  // 新增：取消意图确认
  cancelIntentConfirmation: (conversationId: string, analysisId: string) =>
    api.post(`/conversations/${conversationId}/intent-confirmation/cancel?analysis_id=${analysisId}`)
};

// 多轮分析API
const multiRoundAnalysisApi = {
  // 在会话中执行新轮次分析
  executeRoundAnalysis: (
    conversationId: string,
    roundData: {
      query: string;
      round_number: number;
      max_planning_rounds?: number;
      use_full_context?: boolean;
      context_depth?: number;
      continue_analysis_id?: string;
    },
    callbacks: {
      onStart?: () => void;
      onEvent?: (eventType: string, eventData: any) => void;
      onError?: (error: any) => void;
      onComplete?: (analysisId: string) => void;
    }
  ) => {
    const url = `${api.defaults.baseURL}/conversations/${conversationId}/rounds/stream`;
    
    // 获取token
    const token = localStorage.getItem('token');
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Accept': 'text/event-stream',
      'Cache-Control': 'no-cache',
    };
    
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }
    
    // 使用fetch + ReadableStream进行流式通信
    const fetchPromise = fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(roundData)
    }).then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('无法获取响应流');
      }
      
      const decoder = new TextDecoder();
      let buffer = '';
      let cancelled = false;
      
      const processStream = async () => {
        try {
          if (callbacks?.onStart) callbacks.onStart();
          
          while (!cancelled) {
            const { done, value } = await reader.read();
            
            if (done) break;
            
            buffer += decoder.decode(value, { stream: true });
            
            // 处理完整的事件
            const lines = buffer.split('\n');
            buffer = lines.pop() || ''; // 保留不完整的行
            
            for (const line of lines) {
              if (line.startsWith('data: ')) {
                try {
                  const eventData = JSON.parse(line.slice(6));
                  
                  if (eventData.event === 'completed') {
                    // **特殊处理：如果是超时取消的completed事件，也需要触发onEvent**
                    if (eventData.data?.cancelled && eventData.data?.reason === 'interrupt_timeout') {
                      console.log('🔔 检测到超时取消的completed事件，同时触发onEvent和onComplete');
                      if (callbacks?.onEvent) {
                        callbacks.onEvent(eventData.event, eventData.data);
                      }
                    }

                    if (callbacks?.onComplete) {
                      callbacks.onComplete(eventData.data?.id || '');
                    }
                    return;
                  } else if (eventData.event === 'error') {
                    if (callbacks?.onError) {
                      callbacks.onError(eventData.data);
                    }
                    return;
                  } else {
                    if (callbacks?.onEvent) {
                      callbacks.onEvent(eventData.event, eventData.data);
                    }
                  }
                } catch (error) {
                  console.error('解析事件数据出错:', error, line);
                }
              }
            }
          }
        } catch (error) {
          if (!cancelled) {
            console.error('处理流数据出错:', error);
            if (callbacks?.onError) callbacks.onError(error);
          }
        }
      };
      
      processStream();
      
      // 返回一个可以取消的对象
      return {
        cancel: () => {
          cancelled = true;
          reader.cancel();
        }
      };
    });
    
    return fetchPromise;
  },
  
  // 获取轮次的详细上下文
  getRoundContext: (conversationId: string, roundNumber: number) =>
    api.get(`/conversations/${conversationId}/rounds/${roundNumber}/context`)
};

// 用户相关API（子账号管理）
const userApi = {
  // 获取子账号列表
  getSubAccounts: (params?: { skip?: number; limit?: number }) => 
    api.get('/users/', { params }),
  
  // 创建子账号
  createSubAccount: (data: any) => api.post('/users/', data),
  
  // 更新子账号
  updateSubAccount: (userId: number, data: any) => 
    api.put(`/users/${userId}`, data),
  
  // 删除子账号
  deleteSubAccount: (userId: number) => api.delete(`/users/${userId}`),
  
  // 切换子账号状态
  toggleSubAccountStatus: (userId: number) => 
    api.put(`/users/${userId}/toggle-status`),
  
  // 获取子账号详情
  getSubAccount: (userId: number) => api.get(`/users/${userId}`),
};

// 扩展现有的llmAnalysisApi
const llmAnalysisApiExtended = {
  ...llmAnalysisApi,
  
  // 在会话中执行流式分析（兼容现有接口）
  executeStreamAnalysisInConversation: (
    conversationId: string,
    analysisData: any,
    callbacks: any
  ) => {
    // 添加会话ID到分析数据中
    const dataWithConversation = {
      ...analysisData,
      conversation_id: conversationId
    };
    return llmAnalysisApi.executeStreamAnalysis(dataWithConversation, callbacks);
  },

  // 打断分析
  interruptAnalysis: (analysisId: string) => 
    api.post(`/analysis/llm/${analysisId}/interrupt`),

  // 提交用户反馈
  submitUserFeedback: (analysisId: string, feedbackData: any) =>
    api.post(`/analysis/llm/${analysisId}/feedback`, feedbackData),

  // 取消打断，继续分析
  cancelInterruptContinueAnalysis: (analysisId: string) =>
    api.post(`/analysis/llm/${analysisId}/cancel-interrupt`)
};

// 将所有API方法挂载到api实例上
Object.assign(api, {
  ...projectApi,
  ...dataSourceApi,
  ...toolApi,
  ...analysisApi,
  ...projectWizardApi,
  ...conversationApi,
  ...multiRoundAnalysisApi,
  ...llmAnalysisApiExtended,
  ...llmModelApi,
  ...authApi,
  ...userApi
});

// 导出全局401错误处理器，供业务代码使用
export const globalHandle401 = handle401Error;

// 项目智能理解相关API
const projectIntelligenceApi = {
  // 启动智能理解会话
  startSession: (projectId: string) => 
    api.post(`/project-intelligence/start/${projectId}`),
  
  // 分析业务理解（增加超时时间到3分钟）
  analyzeUnderstanding: (sessionId: string) => 
    api.post(`/project-intelligence/analyze/${sessionId}`, {}, { timeout: 180000 }),
  
  // 生成智能问题
  generateQuestions: (sessionId: string) => 
    api.post(`/project-intelligence/questions/${sessionId}`),
  
  // 保存用户回答
  saveAnswers: (sessionId: string, answers: any) => 
    api.post(`/project-intelligence/answers/${sessionId}`, answers),
  
  // 预测场景
  predictScenarios: (sessionId: string) => 
    api.post(`/project-intelligence/predict/${sessionId}`),
  
  // 保存场景确认
  saveScenarioConfirmations: (sessionId: string, confirmations: any) => 
    api.post(`/project-intelligence/confirmations/${sessionId}`, confirmations),
  
  // 完成会话
  completeSession: (sessionId: string) => 
    api.post(`/project-intelligence/complete/${sessionId}`),
  
  // 获取会话状态
  getSessionStatus: (sessionId: string) => 
    api.get(`/project-intelligence/session/${sessionId}`)
};

export { projectApi, dataSourceApi, toolApi, analysisApi, projectWizardApi, conversationApi, multiRoundAnalysisApi, llmAnalysisApiExtended, llmModelApi, authApi, userApi, projectMemberApi, organizationApi, projectIntelligenceApi };
export default api; 