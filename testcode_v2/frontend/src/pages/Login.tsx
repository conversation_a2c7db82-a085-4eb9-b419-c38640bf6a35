import React, { useState } from 'react';
import { Form, Input, Button, message, Typography } from 'antd';
import { UserOutlined, LockOutlined, <PERSON>boltOutlined, MessageOutlined, BarChartOutlined, RobotOutlined, DatabaseOutlined, LineChartOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { authApi } from '../services/api';
import LanguageSwitch from '../components/LanguageSwitch';
import '../styles/login.css';

const { Title, Text } = Typography;

const Login: React.FC = () => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const onFinish = async (values: any) => {
    setLoading(true);
    try {
      // 第一步：登录获取token
      const loginResponse = await authApi.login(values);
      if (loginResponse && loginResponse.data && loginResponse.data.data) {
        // 保存token
        localStorage.setItem('token', loginResponse.data.data.access_token);
        
        // 第二步：获取用户信息
        try {
          const userResponse = await authApi.getCurrentUser();
          if (userResponse && userResponse.data && userResponse.data.data) {
            // 保存用户信息
            localStorage.setItem('userInfo', JSON.stringify(userResponse.data.data));
          }
        } catch (userError) {
          console.error('Failed to get user info:', userError);
          // 即使获取用户信息失败，也不影响登录流程
        }
        
        message.success(t('auth:login.loginSuccess'));
        
        // 派发登录成功事件，通知App组件重新加载项目列表
        window.dispatchEvent(new CustomEvent('loginSuccess'));
        
        // 导航到主页面（会自动重定向到分析页面）
        navigate('/');
      } else {
        message.error(t('auth:login.loginFailed'));
      }
    } catch (error: any) {
      console.error('Login error:', error);
      
      // 提取后端返回的具体错误信息
      let errorMessage = t('auth:login.loginFailed');
      
      if (error.response?.data?.detail) {
        // 后端返回的具体错误信息
        errorMessage = error.response.data.detail;
      } else if (error.response?.status === 401) {
        // 401错误但没有具体信息
        errorMessage = t('auth:login.invalidCredentials');
      } else if (error.response?.status === 403) {
        // 403错误
        errorMessage = t('auth:login.accountLocked');
      } else if (error.message) {
        // 网络错误等其他错误
        errorMessage = error.message;
      }
      
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const containerStyle = {
    backgroundImage: `url(${process.env.PUBLIC_URL}/image.png)`,
    backgroundSize: 'cover',
    backgroundPosition: 'center center',
    backgroundRepeat: 'no-repeat'
  };

  return (
    <div className="login-container" style={containerStyle}>
      {/* 背景产品介绍卡片 */}
      <div className="background-cards">
        <div className="feature-card card-1">
          <ThunderboltOutlined className="card-icon" />
          <h3>{t('auth:login.features.aiAnalysis.title')}</h3>
          <p>{t('auth:login.features.aiAnalysis.description')}</p>
        </div>
        
        <div className="feature-card card-2">
          <MessageOutlined className="card-icon" />
          <h3>{t('auth:login.features.conversational.title')}</h3>
          <p>{t('auth:login.features.conversational.description')}</p>
        </div>
        
        <div className="feature-card card-3">
          <BarChartOutlined className="card-icon" />
          <h3>{t('auth:login.features.visualization.title')}</h3>
          <p>{t('auth:login.features.visualization.description')}</p>
        </div>
        
        <div className="feature-card card-4">
          <RobotOutlined className="card-icon" />
          <h3>{t('auth:login.features.smartRecommendation.title')}</h3>
          <p>{t('auth:login.features.smartRecommendation.description')}</p>
        </div>
        
        <div className="feature-card card-5">
          <DatabaseOutlined className="card-icon" />
          <h3>{t('auth:login.features.multiSource.title')}</h3>
          <p>{t('auth:login.features.multiSource.description')}</p>
        </div>
        
        <div className="feature-card card-6">
          <LineChartOutlined className="card-icon" />
          <h3>{t('auth:login.features.realTimeMonitoring.title')}</h3>
          <p>{t('auth:login.features.realTimeMonitoring.description')}</p>
        </div>
      </div>

      {/* 语言切换器 - 右上角 */}
      <div className="language-switch-container">
        <LanguageSwitch size="small" />
      </div>

      {/* 中央登录卡片 */}
      <div className="central-login-card">
        <div className="login-header">
          <img src="/home-logo.0ceca025.png" alt="DecisionAI" className="login-logo" />
          <Title level={1} className="login-title">DecisionAI 2.0</Title>
          <Text className="login-subtitle">{t('auth:login.subtitle')}</Text>
        </div>

        <Form
          name="login"
          onFinish={onFinish}
          autoComplete="off"
          size="large"
          className="login-form"
        >
          <Form.Item
            name="username"
            rules={[{ required: true, message: t('auth:login.username') }]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder={t('auth:login.usernamePlaceholder')}
              className="login-input"
            />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[{ required: true, message: t('auth:login.password') }]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder={t('auth:login.passwordPlaceholder')}
              className="login-input"
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              className="login-button"
              block
            >
              {t('auth:login.loginButton')}
            </Button>
          </Form.Item>
        </Form>

        <div className="login-footer">
          <Text className="company-info">{t('common:login.companyInfo')}</Text>
        </div>
      </div>
    </div>
  );
};

export default Login; 