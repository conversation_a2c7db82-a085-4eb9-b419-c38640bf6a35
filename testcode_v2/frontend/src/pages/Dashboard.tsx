import React, { useEffect, useState, useCallback } from 'react';
import { Row, Col, Card, Statistic, List, Typography, Alert, Spin } from 'antd';
import { Link } from 'react-router-dom';
import { ProjectOutlined, DatabaseOutlined, ToolOutlined, SearchOutlined, MessageOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { toolApi, analysisApi, conversationApi } from '../services/api';

const { Title } = Typography;

const Dashboard: React.FC = () => {
  const { t } = useTranslation();
  // 统计数据状态
  const [stats, setStats] = useState({
    toolCount: 0,
    analysisCount: 0,
    conversationCount: 0 // 新增：会话数量统计
  });
  // 最近会话列表状态（原来是最近分析记录）
  const [recentConversations, setRecentConversations] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // 使用useCallback包装fetchDashboardData，避免重复创建函数
  const fetchDashboardData = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      // 在真实环境中，这里应该调用API获取统计数据
      const projectId = localStorage.getItem('selectedProjectId');
      if (!projectId) {
        setError(t('dashboard:messages.selectProjectFirst'));
        return;
      }
      
      // 并行获取各种统计数据
      const [toolsResponse, analysesResponse, conversationsResponse] = await Promise.all([
        toolApi.getTools({ project_id: projectId }),
        analysisApi.getAnalyses({ limit: 5, project_id: projectId }),
        conversationApi.getProjectConversations(projectId, { page: 1, page_size: 5 })
      ]);
      
      console.log('工具响应:', toolsResponse);
      console.log('分析响应:', analysesResponse);
      console.log('会话响应:', conversationsResponse);
      
      // 适应分页数据结构
      const toolData = toolsResponse.data?.data?.items ? toolsResponse.data.data : { items: [], page_info: { total: 0 } };
      const analysisData = analysesResponse.data?.data?.items ? analysesResponse.data.data : { items: [], page_info: { total: 0 } };
      
      // 会话数据结构适配：接口返回的是 data.conversations 和 data.pagination
      // 需求：支持任意项目的会话数据解析，不仅限于示例中的特定响应格式
      const conversationData = conversationsResponse.data?.data ? {
        items: conversationsResponse.data.data.conversations || [],
        page_info: { 
          total: conversationsResponse.data.data.pagination?.total_count || 0 
        }
      } : { items: [], page_info: { total: 0 } };
      
      console.log('解析后的会话数据:', conversationData);
      
      setStats({
        toolCount: toolData.page_info?.total || 0,
        analysisCount: analysisData.page_info?.total || 0,
        conversationCount: conversationData.page_info?.total || 0,
      });
      
      setRecentConversations(conversationData.items || []);
    } catch (error) {
      console.error('Fetch dashboard data failed', error);
      setError(t('dashboard:messages.fetchDataFailed'));
    } finally {
      setLoading(false);
    }
  }, []);

  // 使用空依赖数组，确保useEffect只执行一次
  useEffect(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  if (error) {
    return <Alert message={t('dashboard:messages.error')} description={error} type="error" showIcon />;
  }

  return (
    <div>
      <Title level={2}>{t('dashboard:title')}</Title>
      
      <Row gutter={16} className="dashboard-cards">
        <Col span={6}>
          <Link to="/tools">
            <Card hoverable className="dashboard-card">
              <Statistic
                title={t('dashboard:statistics.toolCount')}
                value={stats.toolCount}
                prefix={<ToolOutlined />}
                loading={loading}
              />
            </Card>
          </Link>
        </Col>
        {/* <Col span={6}>
          <Link to="/analysis/history">
            <Card hoverable className="dashboard-card">
              <Statistic
                title="分析次数"
                value={stats.analysisCount}
                prefix={<SearchOutlined />}
                loading={loading}
              />
            </Card>
          </Link>
        </Col> */}
        <Col span={6}>
          <Link to="/analysis/multi-round">
            <Card hoverable className="dashboard-card">
              <Statistic
                title={t('dashboard:statistics.conversationCount')}
                value={stats.conversationCount}
                prefix={<MessageOutlined />}
                loading={loading}
              />
            </Card>
          </Link>
        </Col>
      </Row>
      
      <Card 
        title={t('dashboard:recentConversations.title')} 
        loading={loading}
      >
        <List
          dataSource={recentConversations}
          renderItem={(item) => (
            <List.Item
              actions={[
                <Link to={`/analysis/multi-round/${item.id}`} key="view">{t('dashboard:recentConversations.enterConversation')}</Link>
              ]}
            >
              <List.Item.Meta
                avatar={<MessageOutlined style={{ fontSize: '16px', color: '#1890ff' }} />}
                title={<Link to={`/analysis/multi-round/${item.id}`}>{item.title || t('dashboard:recentConversations.untitled')}</Link>}
                description={
                  <div>
                    <div>{t('dashboard:recentConversations.rounds')}: {item.rounds_count || 0}</div>
                    <div>{t('dashboard:recentConversations.time')}: {item.last_analysis_at ? new Date(item.last_analysis_at).toLocaleString() : (item.updated_at ? new Date(item.updated_at).toLocaleString() : '-')}</div>
                    {item.last_query && <div style={{ color: '#666', fontSize: '12px', marginTop: '4px' }}>{t('dashboard:recentConversations.lastQuestion')}: {item.last_query.length > 50 ? item.last_query.substring(0, 50) + '...' : item.last_query}</div>}
                  </div>
                }
              />
            </List.Item>
          )}
          locale={{ emptyText: t('dashboard:recentConversations.noRecords') }}
        />
      </Card>
    </div>
  );
};

export default Dashboard; 