import React, { useEffect, useState } from 'react';
import { Typo<PERSON>, Tabs, Card, Alert, Spin, Empty } from 'antd';
import { useSearchParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import DataSourceListComponent from '../../components/DataSourceList';
import { projectApi } from '../../services/api';

const { Title } = Typography;
const { TabPane } = Tabs;

const DataSourceList: React.FC = () => {
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const [projects, setProjects] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [activeKey, setActiveKey] = useState<string>("");

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await projectApi.getAllProjects();
        console.log('Project list response:', response);
        
        if (response.data.data.items) {
          console.log('Project list response:', response.data.data.items);
          // 直接使用response.data，它应当是数组
          const projectsData = Array.isArray(response.data.data.items) ? response.data.data.items : [];
          setProjects(projectsData);
          
          // 设置默认激活的项目
          const projectId = searchParams.get('projectId') || (projectsData.length > 0 ? projectsData[0].id : "");
          setActiveKey(projectId);
        } else {
          setError(t('common:messages.interfaceResponseError'));
        }
      } catch (error) {
        console.error('Failed to fetch projects', error);
        setError(t('common:messages.getProjectFailed'));
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, [searchParams]);

  // Tab切换处理
  const handleTabChange = (key: string) => {
    setActiveKey(key);
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin tip={t('common:messages.loading')} size="large" />
      </div>
    );
  }

  if (error) {
    return <Alert message={t('common:messages.error')} description={error} type="error" showIcon />;
  }

  return (
    <div>
      <Title level={2}>{t('common:dataSource.title')}</Title>
      
      {projects.length === 0 ? (
        <Empty 
          description={t('common:messages.noProjectsCreateFirst')} 
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      ) : (
        <Tabs 
          activeKey={activeKey} 
          onChange={handleTabChange}
          type="card"
          tabPosition="top"
        >
          {projects.map((project) => (
            <TabPane tab={project.name} key={project.id}>
              <DataSourceListComponent projectId={project.id} />
            </TabPane>
          ))}
        </Tabs>
      )}
    </div>
  );
};

export default DataSourceList; 