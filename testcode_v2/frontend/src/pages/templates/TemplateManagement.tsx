import React, { useState, useEffect } from 'react';
import {
  Card, Table, Button, Modal, Form, Input, message, Space,
  Popconfirm, Tag, Typography, Tooltip, Row, Col, Checkbox, Alert
} from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { Template, TemplateApi, TemplateCreateRequest, TemplateUpdateRequest } from '../../services/templateApi';

const { TextArea } = Input;
const { Text } = Typography;

const TemplateManagement: React.FC = () => {
  const { t } = useTranslation();
  const [projectId, setProjectId] = useState<string>('');
  const [templates, setTemplates] = useState<Template[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<Template | null>(null);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewTemplate, setPreviewTemplate] = useState<Template | null>(null);
  const [form] = Form.useForm();

  // 获取当前选中的项目ID
  useEffect(() => {
    const selectedProjectId = localStorage.getItem('selectedProjectId');
    if (selectedProjectId) {
      setProjectId(selectedProjectId);
    }
  }, []);

  // 获取模板列表
  const fetchTemplates = async () => {
    if (!projectId) return;
    
    setLoading(true);
    try {
      const data = await TemplateApi.getTemplates({ projectId });
      setTemplates(data);
    } catch (error) {
      message.error(t('analysis:templateManagement.messages.fetchFailed'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTemplates();
  }, [projectId]);

  // 创建/更新模板
  const handleSubmit = async (values: any) => {
    try {
      // 转换字段名格式：驼峰转下划线
      const apiData = {
        name: values.name,
        description: values.description,
        content: values.content,
        is_public: values.isPublic || false
      };

      if (editingTemplate) {
        // 更新模板
        await TemplateApi.updateTemplate(editingTemplate.id, apiData);
        message.success(t('analysis:templateManagement.messages.templateUpdated'));
      } else {
        // 创建模板
        await TemplateApi.createTemplate(projectId!, apiData);
        message.success(t('analysis:templateManagement.messages.templateCreated'));
      }

      setModalVisible(false);
      setEditingTemplate(null);
      form.resetFields();
      fetchTemplates();
    } catch (error) {
      message.error(editingTemplate ? t('analysis:templateManagement.messages.updateFailed') : t('analysis:templateManagement.messages.createFailed'));
    }
  };

  // 删除模板
  const handleDelete = async (templateId: string) => {
    try {
      await TemplateApi.deleteTemplate(templateId);
      message.success(t('analysis:templateManagement.messages.templateDeleted'));
      fetchTemplates();
    } catch (error) {
      message.error(t('analysis:templateManagement.messages.deleteFailed'));
    }
  };

  // 打开编辑模态框
  const handleEdit = (template: Template) => {
    setEditingTemplate(template);
    form.setFieldsValue({
      name: template.name,
      description: template.description,
      content: template.content,
      isPublic: template.is_public
    });
    setModalVisible(true);
  };

  // 预览模板
  const handlePreview = (template: Template) => {
    setPreviewTemplate(template);
    setPreviewVisible(true);
  };

  // 表格列定义
  const columns = [
    {
      title: t('analysis:templateManagement.templateName'),
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Template) => (
        <Space>
          <Text strong>{text}</Text>
          {record.is_system && <Tag color="blue">{t('analysis:templateManagement.systemTemplate')}</Tag>}
          {record.is_public && <Tag color="green">{t('analysis:templateManagement.publicTag')}</Tag>}
        </Space>
      ),
    },
    // 移除了分类列
    {
      title: t('analysis:templateManagement.templateDescription'),
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      render: (description: string) => (
        <Tooltip title={description}>
          <Text ellipsis style={{ maxWidth: 200 }}>
            {description || '-'}
          </Text>
        </Tooltip>
      ),
    },
    {
      title: t('analysis:templateManagement.usageCount'),
      dataIndex: 'usage_count',
      key: 'usage_count',
      width: 150,
      sorter: (a: Template, b: Template) => a.usage_count - b.usage_count,
    },
    {
      title: t('analysis:templateManagement.creator'),
      dataIndex: 'creator_name',
      key: 'creator_name',
      render: (creatorName: string) => creatorName || '-',
    },
    {
      title: t('analysis:templateManagement.actions'),
      key: 'actions',
      width: 150,
      render: (_: any, record: Template) => (
        <Space>
          <Tooltip title={t('analysis:templateManagement.preview')}>
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handlePreview(record)}
            />
          </Tooltip>
          {record.can_edit && (
            <Tooltip title={t('analysis:templateManagement.edit')}>
              <Button
                type="text"
                icon={<EditOutlined />}
                onClick={() => handleEdit(record)}
              />
            </Tooltip>
          )}
          {record.can_delete && (
            <Tooltip title={t('analysis:templateManagement.delete')}>
              <Popconfirm
                title={t('analysis:templateManagement.messages.deleteConfirm')}
                onConfirm={() => handleDelete(record.id)}
                okText={t('analysis:templateManagement.confirm')}
                cancelText={t('analysis:templateManagement.cancel')}
              >
                <Button
                  type="text"
                  danger
                  icon={<DeleteOutlined />}
                />
              </Popconfirm>
            </Tooltip>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: 24 }}>
      {!projectId ? (
        <Alert
          message={t('analysis:templateManagement.noProject.title')}
          description={t('analysis:templateManagement.noProject.description')}
          type="warning"
          showIcon
        />
      ) : (
        <Card
          title={t('analysis:templateManagement.title')}
          extra={
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setEditingTemplate(null);
                form.resetFields();
                setModalVisible(true);
              }}
            >
              {t('analysis:templateManagement.createTemplate')}
            </Button>
          }
        >
        <Table
          columns={columns}
          dataSource={templates}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => t('analysis:templateManagement.pagination.total', { total }),
          }}
        />
      </Card>
      )}

      {/* 创建/编辑模板模态框 */}
      <Modal
        title={editingTemplate ? t('analysis:templateManagement.editTemplate') : t('analysis:templateManagement.createTemplate')}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingTemplate(null);
          form.resetFields();
        }}
        onOk={() => form.submit()}
        width={800}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label={t('analysis:templateManagement.templateName')}
                rules={[{ required: true, message: t('analysis:templateManagement.validation.nameRequired') }]}
              >
                <Input placeholder={t('analysis:templateManagement.placeholders.templateName')} />
              </Form.Item>
            </Col>
            <Col span={12}>
              {/* 移除了分类选择 */}
            </Col>
          </Row>
          
          <Form.Item
            name="description"
            label={t('analysis:templateManagement.templateDescription')}
          >
            <Input.TextArea
              placeholder={t('analysis:templateManagement.placeholders.templateDescription')}
              rows={2}
            />
          </Form.Item>

          <Form.Item
            name="content"
            label={t('analysis:templateManagement.templateContent')}
            rules={[{ required: true, message: t('analysis:templateManagement.validation.contentRequired') }]}
          >
            <TextArea
              placeholder={t('analysis:templateManagement.placeholders.templateContent')}
              rows={10}
            />
          </Form.Item>

          <Form.Item
            name="isPublic"
            label={t('analysis:templateManagement.isPublic')}
            valuePropName="checked"
          >
            <Checkbox>{t('analysis:templateManagement.publicTemplate')}</Checkbox>
          </Form.Item>
        </Form>
      </Modal>

      {/* 预览模板模态框 */}
      <Modal
        title={`${t('analysis:templateManagement.previewTemplate')}：${previewTemplate?.name}`}
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        footer={[
          <Button key="close" onClick={() => setPreviewVisible(false)}>
            {t('analysis:templateManagement.close')}
          </Button>
        ]}
        width={800}
      >
        {previewTemplate && (
          <div>
            <div style={{ marginBottom: 16 }}>
              <Text strong>{t('analysis:templateManagement.templateDescription')}：</Text>
              <Text>{previewTemplate.description || t('analysis:templateManagement.noDescription')}</Text>
            </div>
            <div>
              <Text strong>{t('analysis:templateManagement.templateContent')}：</Text>
              <div style={{
                background: '#f6f8fa',
                padding: 12,
                borderRadius: 6,
                marginTop: 8,
                whiteSpace: 'pre-wrap',
                fontFamily: 'monospace'
              }}>
                {previewTemplate.content}
              </div>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default TemplateManagement;
