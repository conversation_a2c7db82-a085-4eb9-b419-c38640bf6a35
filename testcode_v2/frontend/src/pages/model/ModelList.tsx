import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  But<PERSON>,
  Space,
  message,
  Popconfirm,
  Card,
  Row,
  Col,
  Typography,
  Tooltip,
  Modal,
  Spin,
  Switch,
  Input
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  PlayCircleOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { llmModelApi } from '../../services/api';

const { Title, Text } = Typography;

interface LLMModel {
  id: string;
  name: string;
  description?: string;
  model_name: string;
  base_url?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}



interface TestResult {
  success: boolean;
  response?: string;
  error?: string;
  latency?: number;
}

const ModelList: React.FC = () => {
  const [models, setModels] = useState<LLMModel[]>([]);
  const [loading, setLoading] = useState(false);
  const [testModalVisible, setTestModalVisible] = useState(false);
  const [testResult, setTestResult] = useState<TestResult | null>(null);
  const [testingModel, setTestingModel] = useState<LLMModel | null>(null);
  const [testing, setTesting] = useState(false);
  const navigate = useNavigate();

  // 获取模型列表
  const fetchModels = async () => {
    try {
      setLoading(true);
      const response = await llmModelApi.getModels();
      if (response.data?.code === 200) {
        setModels(response.data.data || []);
      }
    } catch (error) {
      console.error('获取模型列表失败:', error);
      message.error('获取模型列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchModels();
  }, []);

  // 删除模型
  const handleDelete = async (id: string) => {
    try {
      const response = await llmModelApi.deleteModel(id);
      if (response.data?.code === 200) {
        message.success('删除成功');
        fetchModels();
      }
    } catch (error) {
      console.error('删除模型失败:', error);
      message.error('删除模型失败');
    }
  };

  // 切换模型启用状态
  const handleToggleActive = async (id: string, checked: boolean) => {
    try {
      const response = await llmModelApi.updateModel(id, {
        is_active: checked
      });
      if (response.data?.code === 200) {
        message.success(checked ? '模型已启用' : '模型已禁用');
        fetchModels(); // 刷新列表，确保显示最新状态
      }
    } catch (error) {
      console.error('切换模型状态失败:', error);
      message.error('切换模型状态失败');
    }
  };



  // 测试模型 - 弹出API key输入框
  const handleTest = (model: LLMModel) => {
    Modal.confirm({
      title: `测试模型: ${model.name}`,
      content: (
        <div>
          <p style={{ marginBottom: '12px' }}>请输入API密钥来测试模型连接：</p>
          <Input.Password 
            id="test-api-key-input"
            placeholder="请输入API密钥"
            style={{ marginBottom: '8px' }}
          />
          <p style={{ fontSize: '12px', color: '#666', margin: 0 }}>
            出于安全考虑，API密钥不会被保存
          </p>
        </div>
      ),
      okText: '开始测试',
      cancelText: '取消',
      width: 500,
      onOk: async () => {
        const apiKeyInput = document.getElementById('test-api-key-input') as HTMLInputElement;
        const apiKey = apiKeyInput?.value;
        
        if (!apiKey) {
          message.error('请输入API密钥');
          return false; // 阻止关闭弹窗
        }
        
        await performTest(model, apiKey);
      }
    });
  };

  // 执行测试
  const performTest = async (model: LLMModel, apiKey: string) => {
    setTestingModel(model);
    setTestResult(null);
    setTestModalVisible(true);
    setTesting(true);

    try {
      // 构建配置数据进行测试
      const configData = {
        name: model.name,
        model_name: model.model_name,
        api_key: apiKey,
        base_url: model.base_url || 'https://api.openai.com/v1'
      };

      const response = await llmModelApi.testModelConfig(configData);

      if (response.data?.code === 200) {
        setTestResult(response.data.data);
      } else {
        setTestResult({
          success: false,
          error: '测试请求失败'
        });
      }
    } catch (error: any) {
      console.error('测试模型失败:', error);
      setTestResult({
        success: false,
        error: error.message || '测试过程中发生错误'
      });
    } finally {
      setTesting(false);
    }
  };

  // 关闭测试结果弹窗
  const handleCloseTestModal = () => {
    setTestModalVisible(false);
    setTestResult(null);
    setTestingModel(null);
    setTesting(false);
  };

  const columns = [
    {
      title: '模型名称',
      dataIndex: 'model_name',
      key: 'model_name',
      render: (text: string) => (
        <div style={{ fontWeight: 500, fontSize: '14px', fontFamily: 'monospace' }}>
          {text}
        </div>
      ),
    },
    {
      title: 'API地址',
      dataIndex: 'base_url',
      key: 'base_url',
      ellipsis: true,
      width: 250,
      render: (url: string) => (
        <span style={{ fontSize: '13px', color: '#666' }}>
          {url || 'https://api.openai.com/v1'}
        </span>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      render: (text: string) => (
        <span style={{ fontSize: '13px', color: '#666' }}>
          {text || '-'}
        </span>
      ),
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      width: 80,
      align: 'center' as const,
      render: (isActive: boolean, record: LLMModel) => (
        <Switch
          checked={isActive}
          onChange={(checked) => handleToggleActive(record.id, checked)}
          checkedChildren="启用"
          unCheckedChildren="禁用"
          size="small"
        />
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      align: 'center' as const,
      render: (_: any, record: LLMModel) => (
        <Space size="small">
          <Tooltip title="测试模型">
            <Button
              type="link"
              size="small"
              icon={<PlayCircleOutlined />}
              onClick={() => handleTest(record)}
            />
          </Tooltip>

          <Tooltip title="编辑">
            <Button
              type="link"
              size="small"
              icon={<EditOutlined />}
              onClick={() => navigate(`/models/${record.id}/edit`)}
            />
          </Tooltip>

          <Popconfirm
            title="确定要删除这个模型吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="link"
                size="small"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Row justify="space-between" align="middle" style={{ marginBottom: '24px' }}>
        <Col>
          <Title level={2}>LLM模型管理</Title>
        </Col>
        <Col>
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => navigate('/models/create')}
          >
            添加模型
          </Button>
        </Col>
      </Row>

      <Card>
        <Table
          columns={columns}
          dataSource={models}
          rowKey="id"
          loading={loading}
          size="middle"
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
            pageSize: 10,
            showLessItems: true,
          }}
        />
      </Card>

      {/* 测试结果弹窗 */}
      <Modal
        title={`测试结果 - ${testingModel?.name}`}
        open={testModalVisible}
        onCancel={handleCloseTestModal}
        footer={[
          <Button key="close" onClick={handleCloseTestModal}>
            关闭
          </Button>
        ]}
        width={600}
      >
        {testing ? (
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <Spin size="large" />
            <div style={{ marginTop: '16px' }}>
              <Text>正在测试模型连接和响应...</Text>
            </div>
          </div>
        ) : testResult ? (
          <div>
            {testResult.success ? (
              <div style={{ textAlign: 'center', padding: '20px' }}>
                <div style={{ fontSize: '48px', marginBottom: '16px' }}>✅</div>
                <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#52c41a', marginBottom: '8px' }}>
                  调用成功
                </div>
                <div style={{ color: '#666', marginBottom: '16px' }}>
                  响应时间: {testResult.latency?.toFixed(3)}秒
                </div>
                <div style={{
                  background: '#f6ffed',
                  border: '1px solid #b7eb8f',
                  padding: '12px',
                  borderRadius: '6px',
                  textAlign: 'left',
                  fontFamily: 'monospace'
                }}>
                  {testResult.response}
                </div>
              </div>
            ) : (
              <div style={{ textAlign: 'center', padding: '20px' }}>
                <div style={{ fontSize: '48px', marginBottom: '16px' }}>❌</div>
                <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#ff4d4f', marginBottom: '16px' }}>
                  调用失败
                </div>
                <div style={{
                  background: '#fff2f0',
                  border: '1px solid #ffccc7',
                  padding: '12px',
                  borderRadius: '6px',
                  textAlign: 'left',
                  color: '#cf1322',
                  fontFamily: 'monospace',
                  fontSize: '12px'
                }}>
                  {testResult.error}
                </div>
              </div>
            )}
          </div>
        ) : null}
      </Modal>
    </div>
  );
};

export default ModelList;
