import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Button,
  Card,
  Switch,
  message,
  Row,
  Col,
  Typography,
  Space,
  Divider,
  Modal
} from 'antd';
import { useNavigate, useParams } from 'react-router-dom';
import { ArrowLeftOutlined, SaveOutlined, PlayCircleOutlined } from '@ant-design/icons';
import { llmModelApi } from '../../services/api';


const { Title } = Typography;
const { TextArea } = Input;

interface ModelFormData {
  model_name: string;
  description?: string;
  api_key: string;
  base_url?: string;
  is_active: boolean;
}

const ModelEdit: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const [testPassed, setTestPassed] = useState(false);
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEdit = !!id;

  // 获取模型详情（编辑模式）
  const fetchModelDetail = async () => {
    if (!id) return;
    
    try {
      setLoading(true);
      const response = await llmModelApi.getModel(id);
      if (response.data?.code === 200) {
        const model = response.data.data;
        form.setFieldsValue({
          model_name: model.model_name,
          description: model.description,
          api_key: '', // API key不从服务器返回，需要用户重新输入
          base_url: model.base_url,
          is_active: model.is_active,
        });

        // 编辑模式下需要重新测试，因为API key需要重新输入
        setTestPassed(false);
      }
    } catch (error) {
      console.error('获取模型详情失败:', error);
      message.error('获取模型详情失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isEdit) {
      fetchModelDetail();
    }
  }, [id, isEdit]);

  // 监听表单值变化，重置测试状态
  const handleFormValuesChange = () => {
    if (testPassed) {
      setTestPassed(false);
    }
  };

  // 提交表单
  const handleSubmit = async (values: ModelFormData) => {
    try {
      setLoading(true);

      // 自动生成name字段，使用model_name作为显示名称
      const submitData = {
        ...values,
        name: values.model_name // 使用model_name作为name
      };

      let response;
      if (isEdit && id) {
        response = await llmModelApi.updateModel(id, submitData);
      } else {
        response = await llmModelApi.createModel(submitData);
      }

      if (response.data?.code === 200) {
        message.success(isEdit ? '更新成功' : '创建成功');
        navigate('/models');
      }
    } catch (error) {
      console.error('保存模型失败:', error);
      message.error('保存模型失败');
    } finally {
      setLoading(false);
    }
  };

  // 测试模型
  const handleTest = async () => {
    try {
      // 验证表单
      await form.validateFields();
      const formValues = form.getFieldsValue();
      
      // 显示加载状态
      setLoading(true);
      message.loading('正在测试模型...', 0);
      
      // 构建配置测试数据
      const configData = {
        name: formValues.model_name, // 使用model_name作为name
        model_name: formValues.model_name,
        api_key: formValues.api_key,
        base_url: formValues.base_url || 'https://api.openai.com/v1'
      };
       
      const response = await llmModelApi.testModelConfig(configData);
      
      message.destroy();
      
      if (response.data?.code === 200) {
        const result = response.data.data;
        if (result.success) {
          // 测试成功，设置测试通过状态
          setTestPassed(true);
          Modal.info({
            title: '测试成功',
            content: (
              <div>
                <p style={{ color: '#52c41a', fontWeight: 'bold', marginBottom: '12px' }}>
                  ✅ 模型测试通过，现在可以保存配置
                </p>
                <p><strong>响应时间:</strong> {result.latency?.toFixed(3)}秒</p>
                <p><strong>模型响应:</strong></p>
                <div style={{
                  background: '#f6ffed',
                  border: '1px solid #b7eb8f',
                  padding: '12px',
                  borderRadius: '6px',
                  fontFamily: 'monospace',
                  whiteSpace: 'pre-wrap',
                  maxHeight: '200px',
                  overflow: 'auto'
                }}>
                  {result.response}
                </div>
              </div>
            ),
            width: 600,
          });
        } else {
          Modal.error({
            title: '测试失败',
            content: (
              <div style={{
                background: '#fff2f0',
                border: '1px solid #ffccc7',
                padding: '12px',
                borderRadius: '6px',
                color: '#cf1322',
                fontFamily: 'monospace',
                whiteSpace: 'pre-wrap'
              }}>
                {result.error || '未知错误'}
              </div>
            ),
            width: 600,
          });
        }
      }
    } catch (error: any) {
      message.destroy();
      console.error('测试模型失败:', error);
      Modal.error({
        title: '测试失败',
        content: error.message || '网络请求失败',
        width: 600,
      });
    } finally {
      setLoading(false);
    }
  };



  return (
    <div style={{ padding: '24px' }}>
      <Row justify="space-between" align="middle" style={{ marginBottom: '24px' }}>
        <Col>
          <Space>
            <Button 
              icon={<ArrowLeftOutlined />} 
              onClick={() => navigate('/models')}
            >
              返回
            </Button>
            <Title level={2} style={{ margin: 0 }}>
              {isEdit ? '编辑模型' : '添加模型'}
            </Title>
          </Space>
        </Col>
        <Col>
          <Space>
            <Button 
              icon={<PlayCircleOutlined />}
              onClick={handleTest}
              type={testPassed ? 'default' : 'primary'}
            >
              测试模型
            </Button>
            <Button 
              type="primary" 
              icon={<SaveOutlined />}
              loading={loading}
              disabled={!testPassed}
              onClick={() => form.submit()}
              title={!testPassed ? '请先测试模型配置' : ''}
            >
              保存
            </Button>
          </Space>
        </Col>
      </Row>

      <Card>
        {/* 状态提示 */}
        {!testPassed && (
          <div style={{ 
            background: '#fff7e6', 
            border: '1px solid #ffd591', 
            padding: '12px', 
            borderRadius: '6px', 
            marginBottom: '24px',
            color: '#d46b08'
          }}>
            ⚠️ 请先测试模型配置，确保连接正常后再保存
          </div>
        )}
        
        {testPassed && (
          <div style={{ 
            background: '#f6ffed', 
            border: '1px solid #b7eb8f', 
            padding: '12px', 
            borderRadius: '6px', 
            marginBottom: '24px',
            color: '#389e0d'
          }}>
            ✅ 模型配置测试通过，可以保存
          </div>
        )}

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          onValuesChange={handleFormValuesChange}
          initialValues={{
            is_active: true,
            base_url: 'https://api.openai.com/v1',
          }}
        >
          <Form.Item
            label="模型名称"
            name="model_name"
            rules={[{ required: true, message: '请输入模型名称' }]}
            extra="API调用时使用的模型标识符，如：gpt-3.5-turbo, google/gemini-2.5-flash"
          >
            <Input placeholder="例如：gpt-3.5-turbo" />
          </Form.Item>

          <Form.Item
            label="API基础URL"
            name="base_url"
          >
            <Input placeholder="https://api.openai.com/v1" />
          </Form.Item>

          <Form.Item
            label="API密钥"
            name="api_key"
            rules={[{ required: true, message: '请输入API密钥' }]}
            extra={isEdit ? "出于安全考虑，需要重新输入API密钥" : ""}
          >
            <Input.Password 
              placeholder={isEdit ? "请重新输入API密钥" : "请输入API密钥"} 
            />
          </Form.Item>

          <Form.Item
            label="描述"
            name="description"
          >
            <TextArea 
              rows={3} 
              placeholder="请输入模型描述（可选）" 
            />
          </Form.Item>

          <Divider />

          <Form.Item
            label="启用状态"
            name="is_active"
            valuePropName="checked"
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>
        </Form>
      </Card>


    </div>
  );
};

export default ModelEdit;
