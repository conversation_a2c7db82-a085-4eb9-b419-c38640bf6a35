import React, { useState, useEffect } from 'react';
import { Table, Input, Button, message, List, Typography, Card, Spin } from 'antd';
import { SaveOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { projectWizardApi } from '../../services/api';

const { TextArea } = Input;
const { Title, Text, Paragraph } = Typography;

interface TableInfo {
  id: string;
  table_name: string;
  table_description: string;
  table_schema: Record<string, any>;
  data_source_id: string;
}

interface TableDescriptionStepProps {
  projectId: string;
  selectedTables?: string[]; // 新增：从上一步传递的选定表格列表
  onNext: () => void;
  onPrev: () => void;
}

const TableDescriptionStep: React.FC<TableDescriptionStepProps> = ({
  projectId,
  selectedTables,
  onNext,
  onPrev,
}) => {
  const { t } = useTranslation();
  const [tables, setTables] = useState<TableInfo[]>([]);
  const [currentTable, setCurrentTable] = useState<TableInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [allDescribed, setAllDescribed] = useState(true);
  const [processingTables, setProcessingTables] = useState(false); // 新增：处理表格保存状态

  useEffect(() => {
    // 如果有传递的selectedTables，先保存它们，再获取表信息
    if (selectedTables && selectedTables.length > 0) {
      saveSelectedTablesAndFetch();
    } else {
      fetchSelectedTables();
    }
  }, [projectId, selectedTables]);

  useEffect(() => {
    if (tables.length > 0 && !currentTable) {
      setCurrentTable(tables[0]);
    }
    
    // 检查是否所有表都有描述
    const anyEmpty = tables.some(table => !table.table_description);
    setAllDescribed(!anyEmpty);
  }, [tables]);

  const saveSelectedTablesAndFetch = async () => {
    if (!selectedTables || selectedTables.length === 0) {
      fetchSelectedTables();
      return;
    }

    try {
      setLoading(true);
      setProcessingTables(true);
      
      // 先保存选定的表格
      const saveResponse = await projectWizardApi.saveSelectedTables(projectId, selectedTables);
      
      if (saveResponse.data.success) {
        message.success(t('project:wizard.tableDescription.saveSelectedTablesSuccess'));
        // 然后获取表格详细信息
        await fetchSelectedTables();
      } else {
        message.error(t('project:wizard.tableDescription.saveFailed') + ': ' + saveResponse.data.message);
        setLoading(false);
      }
      setProcessingTables(false);
    } catch (error: any) {
      console.error('Failed to save tables:', error);
      message.error(t('project:wizard.tableDescription.saveTablesFailed') + ': ' + (error.response?.data?.message || error.message));
      setLoading(false);
      setProcessingTables(false);
    }
  };

  const fetchSelectedTables = async () => {
    try {
      if (!processingTables) {
        setLoading(true);
      }
      const response = await projectWizardApi.getSelectedTables(projectId);
      setTables(response.data.data);
      setLoading(false);
    } catch (error) {
      console.error('Failed to fetch selected tables:', error);
      message.error(t('project:wizard.tableDescription.fetchSelectedTablesFailed'));
      setLoading(false);
    }
  };

  const handleDescriptionChange = (description: string) => {
    if (currentTable) {
      setCurrentTable({
        ...currentTable,
        table_description: description,
      });
    }
  };

  const saveDescription = async () => {
    if (!currentTable) return;

    try {
      setSaving(true);
      const response = await projectWizardApi.updateTableDescription(
        projectId, 
        currentTable.id, 
        currentTable.table_description
      );
      
      // 更新本地表列表
      setTables(tables.map(table => 
        table.id === currentTable.id 
          ? { ...table, table_description: currentTable.table_description } 
          : table
      ));
      
      message.success(t('project:wizard.tableDescription.descriptionSaved', { tableName: currentTable.table_name }));
      setSaving(false);
      
      // 检查是否所有表都有描述
      const updatedTables = tables.map(table => 
        table.id === currentTable.id 
          ? { ...table, table_description: currentTable.table_description } 
          : table
      );
      const anyEmpty = updatedTables.some(table => !table.table_description);
      setAllDescribed(!anyEmpty);
    } catch (error) {
      console.error('Failed to save table description:', error);
      message.error(t('project:wizard.tableDescription.saveDescriptionFailed'));
      setSaving(false);
    }
  };

  const selectTable = (table: TableInfo) => {
    setCurrentTable(table);
  };

  const renderSchemaFields = () => {
    if (!currentTable || !currentTable.table_schema) return null;

    const columns = [
      {
        title: t('project:wizard.tableDescription.fieldName'),
        dataIndex: 'field',
        key: 'field',
      },
      {
        title: t('project:wizard.tableDescription.dataType'),
        dataIndex: 'type',
        key: 'type',
      },
      {
        title: t('project:wizard.tableDescription.nullable'),
        dataIndex: 'nullable',
        key: 'nullable',
        render: (text: string) => text === 'Y' ? t('common:form.yes') : t('common:form.no')
      }
    ];

    const data = Object.entries(currentTable.table_schema).map(([field, details], index) => ({
      key: index,
      field: field,
      type: (details as any).data_type,
      nullable: (details as any).nullable
    }));

    return (
      <Table 
        columns={columns} 
        dataSource={data} 
        size="small" 
        pagination={false}
        scroll={{ y: 200 }}
      />
    );
  };

  if (loading) {
    const tipText = processingTables 
      ? t('project:wizard.tableDescription.savingAndLoading')
      : t('project:wizard.tableDescription.loadingTableInfo');
    return (
      <div style={{ textAlign: 'center', padding: '60px 20px' }}>
        <Spin size="large" tip={tipText} />
        <div style={{ marginTop: 16, color: '#666' }}>
          {processingTables && t('project:wizard.tableDescription.processWaitMessage')}
        </div>
      </div>
    );
  }

  return (
    <Card title={t('project:wizard.tableDescription.title')} bordered={false}>
      <Paragraph>
        {t('project:wizard.tableDescription.description')}
      </Paragraph>
      
      <div style={{ display: 'flex', marginTop: '20px', height: '60vh' }}>
        {/* 左侧表格列表 */}
        <div style={{ width: '25%', marginRight: '20px', overflowY: 'auto' }}>
          <List
            itemLayout="horizontal"
            dataSource={tables}
            renderItem={table => (
              <List.Item 
                onClick={() => selectTable(table)}
                style={{ 
                  cursor: 'pointer', 
                  backgroundColor: currentTable?.id === table.id ? '#f0f5ff' : 'white',
                  padding: '10px',
                  borderRadius: '4px',
                  marginBottom: '8px'
                }}
              >
                <List.Item.Meta
                  title={table.table_name}
                  description={
                    <div>
                      {table.table_description ? 
                        <Text type="success">{t('project:wizard.tableDescription.hasDescription')}</Text> : 
                        <Text type="secondary">{t('project:wizard.tableDescription.noDescription')}</Text>
                      }
                    </div>
                  }
                />
              </List.Item>
            )}
          />
        </div>
        
        {/* 右侧描述编辑区 */}
        <div style={{ width: '75%' }}>
          {currentTable ? (
            <Card title={t('project:wizard.tableDescription.tableTitle', { tableName: currentTable.table_name })}>
              <div style={{ marginBottom: '16px' }}>
                <Title level={5}>{t('project:wizard.tableDescription.schemaOverview')}</Title>
                {renderSchemaFields()}
              </div>
              
              <div style={{ marginBottom: '16px' }}>
                <Title level={5}>{t('project:wizard.tableDescription.tableDescriptionLabel')}</Title>
                <TextArea
                  rows={6}
                  value={currentTable.table_description || ''}
                  onChange={(e) => handleDescriptionChange(e.target.value)}
                  placeholder={t('project:wizard.tableDescription.descriptionPlaceholder')}
                />
              </div>
              
              <Button 
                type="primary" 
                icon={<SaveOutlined />} 
                onClick={saveDescription}
                loading={saving}
              >
                {t('project:wizard.tableDescription.saveDescription')}
              </Button>
            </Card>
          ) : (
            <div>{t('project:wizard.tableDescription.selectTableFromLeft')}</div>
          )}
        </div>
      </div>
      
      <div style={{ marginTop: '20px', display: 'flex', justifyContent: 'space-between' }}>
        <Button onClick={onPrev}>{t('common:buttons.previous')}</Button>
        <Button 
          type="primary" 
          onClick={onNext}
        >
          {t('project:wizard.tableDescription.enterSelfCheck')}
        </Button>
      </div>
    </Card>
  );
};

export default TableDescriptionStep; 