import React from 'react';
import { Typography, But<PERSON>, Breadcrumb } from 'antd';
import { Link, useNavigate } from 'react-router-dom';
import { PlusOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import ProjectListComponent from '../../components/ProjectList';

const { Title } = Typography;

const ProjectList: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  
  return (
    <div>
      <Breadcrumb 
        style={{ marginBottom: '16px' }}
        items={[
          {
            title: <Link to="/">{t('common:navigation.dashboard')}</Link>
          },
          {
            title: t('common:navigation.projects')
          }
        ]}
      />
      
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
        <Title level={2} style={{ margin: 0 }}>{t('common:navigation.projects')}</Title>
        <Button 
          type="primary" 
          icon={<PlusOutlined />}
          onClick={() => navigate('/project-wizard')}
        >
          {t('project:list.createProject')}
        </Button>
      </div>
      
      <ProjectListComponent />
    </div>
  );
};

export default ProjectList; 