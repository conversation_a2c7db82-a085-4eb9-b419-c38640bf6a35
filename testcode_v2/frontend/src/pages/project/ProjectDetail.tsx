import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  Card, Typography, Descriptions, Button, Skeleton, message, 
  Tabs, Divider, Modal, Form, Input, Space, Table, Tag, List,
  Select, Row, Col, Checkbox
} from 'antd';
import { 
  EditOutlined, DeleteOutlined, LeftOutlined,
  DatabaseOutlined, TableOutlined, BarChartOutlined,
  SearchOutlined, ClockCircleOutlined, CheckCircleOutlined,
  FileTextOutlined, TeamOutlined, ToolOutlined, PlusOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { projectApi, dataSourceApi, projectWizardApi, authApi, projectMemberApi } from '../../services/api';
import TableDescriptionModal from '../../components/TableDescriptionModal';
import ProjectMemberManagement from '../../components/ProjectMemberManagement';

const { Title, Paragraph } = Typography;
const { TabPane } = Tabs;
const { TextArea } = Input;
const { Option } = Select;

// 数据源类型选项
const getDataSourceTypes = (t: any) => [
  { value: 'oracle', label: t('common:dataSource.types.oracle') },
  { value: 'mysql', label: t('common:dataSource.types.mysql') },
  { value: 'postgresql', label: t('common:dataSource.types.postgresql') },
  { value: 'mssql', label: t('common:dataSource.types.mssql') },
  { value: 'http_api', label: t('common:dataSource.types.httpApi') }
];

const ProjectDetail: React.FC = () => {
  const { t } = useTranslation();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState<boolean>(true);
  const [project, setProject] = useState<any>(null);
  const [selectedTables, setSelectedTables] = useState<any[]>([]);
  const [editModalVisible, setEditModalVisible] = useState<boolean>(false);
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [userProjectRole, setUserProjectRole] = useState<string | null>(null);
  const [form] = Form.useForm();
  
  // 添加编辑数据源和表的状态
  const [dataSourceEditModalVisible, setDataSourceEditModalVisible] = useState<boolean>(false);
  const [editingDataSource, setEditingDataSource] = useState<any>(null);
  const [dataSourceForm] = Form.useForm();
  const [connectionSuccess, setConnectionSuccess] = useState<boolean>(false);
  const [connectionTesting, setConnectionTesting] = useState<boolean>(false);
  const [selectedDataSourceType, setSelectedDataSourceType] = useState<string>('mysql');

  // 添加当前标签页状态
  const [activeTabKey, setActiveTabKey] = useState<string>('basic');

  // 报告提示词管理状态
  const [reportPromptModalVisible, setReportPromptModalVisible] = useState<boolean>(false);
  const [reportPromptForm] = Form.useForm();
  const [reportPromptSaving, setReportPromptSaving] = useState<boolean>(false);
  
  // 表编辑相关状态
  const [tableEditModalVisible, setTableEditModalVisible] = useState<boolean>(false);
  const [availableTables, setAvailableTables] = useState<string[]>([]);
  const [checkedTables, setCheckedTables] = useState<string[]>([]);
  const [savingTables, setSavingTables] = useState<boolean>(false);
  
  // 表描述编辑相关状态
  const [tableDescriptionModalVisible, setTableDescriptionModalVisible] = useState<boolean>(false);
  
  // 获取项目详情
  const fetchProjectDetail = async () => {
    if (!id) return;
    
    setLoading(true);
    try {
      const response = await projectApi.getProject(id);
      setProject(response.data.data);
      
      // 设置表单初始值
      form.setFieldsValue({
        name: response.data.data.name,
        description: response.data.data.description || '',
        visibility: response.data.data.visibility || 'PRIVATE'
      });
      
      // 获取项目关联的表（如果需要）
      fetchProjectTables();
    } catch (error) {
      console.error('Fetch project detail failed:', error);
      message.error(t('projectDetail:messages.fetchProjectFailed'));
    } finally {
      setLoading(false);
    }
  };
  
  // 获取项目关联的表
  const fetchProjectTables = async () => {
    if (!id) return;
    
    try {
      const response = await projectApi.getProjectTables(id);
      setSelectedTables(response.data.data || []);
    } catch (error) {
      console.error('Fetch project tables failed:', error);
      message.error(t('projectDetail:messages.fetchTablesFailed'));
    }
  };
  
  // 编辑项目信息
  const handleEditProject = async () => {
    try {
      const values = await form.validateFields();
      await projectApi.updateProject(id!, values);
      message.success(t('projectDetail:messages.projectUpdateSuccess'));
      setEditModalVisible(false);
      fetchProjectDetail();
    } catch (error) {
      console.error('Update project failed:', error);
      message.error(t('projectDetail:messages.projectUpdateFailed'));
    }
  };
  
  // 删除项目
  const handleDeleteProject = () => {
    Modal.confirm({
      title: t('projectDetail:modals.confirmDelete'),
      content: t('projectDetail:modals.deleteMessage'),
      okText: t('common:buttons.confirm'),
      cancelText: t('common:buttons.cancel'),
      onOk: async () => {
        try {
          await projectApi.deleteProject(id!);
          message.success(t('projectDetail:messages.projectDeleteSuccess'));
          navigate('/projects');
        } catch (error) {
          console.error('Delete project failed:', error);
          message.error(t('projectDetail:messages.projectDeleteFailed'));
        }
      }
    });
  };
  
  // 获取当前用户信息
  const fetchCurrentUser = async () => {
    try {
      const response = await authApi.getCurrentUser();
      // 从标准响应格式中提取实际的用户数据
      setCurrentUser(response.data.data);
    } catch (error) {
      console.error('Get user info failed:', error);
    }
  };

  // 获取用户在项目中的角色
  const fetchUserProjectRole = async () => {
    if (!id || !currentUser) {
      console.log('fetchUserProjectRole: Missing required parameters', { id, currentUser });
      return;
    }
    
    try {
      console.log('fetchUserProjectRole: Starting to get project member info', { projectId: id, userId: currentUser.id });
      const response = await projectMemberApi.getProjectMembers(id);
      console.log('fetchUserProjectRole: API response', response);
      
      // 处理标准响应格式
      const apiData = response.data?.data || response.data;
      const members = apiData?.members || [];
      console.log('fetchUserProjectRole: Member list', members);
      
      // 查找当前用户在项目中的角色
      const userMember = members.find((member: any) => member.user_id === currentUser.id);
      console.log('fetchUserProjectRole: Found user member info', userMember);
      
      if (userMember) {
        setUserProjectRole(userMember.role_name);
        console.log('fetchUserProjectRole: Set user project role', userMember.role_name);
      } else {
        setUserProjectRole(null);
        console.log('fetchUserProjectRole: User is not a project member');
      }
    } catch (error) {
      console.error('Fetch user project role failed:', error);
      setUserProjectRole(null);
    }
  };

  // 检查用户是否有项目编辑权限
  const canEditProject = () => {
    if (!currentUser || !project) {
      return false;
    }
    
    // 优先使用后端返回的权限信息
    if (project.permissions && typeof project.permissions.can_edit === 'boolean') {
      return project.permissions.can_edit;
    }
    
    // 兜底逻辑：超级管理员、企业管理员（同企业）、项目所有者
    return (
      currentUser.role_id === 1 || // 超级管理员
      (currentUser.role_id === 2 && currentUser.org_id === project.org_id) || // 企业管理员
      project.owner_id === currentUser.id || // 项目所有者
      currentUser.is_superuser // 兼容旧字段
    );
  };

  // 检查用户是否有项目删除权限
  const canDeleteProject = () => {
    if (!currentUser || !project) {
      return false;
    }
    
    // 优先使用后端返回的权限信息
    if (project.permissions && typeof project.permissions.can_delete === 'boolean') {
      return project.permissions.can_delete;
    }
    
    // 兜底逻辑：超级管理员、企业管理员（同企业）、项目所有者
    return (
      currentUser.role_id === 1 || // 超级管理员
      (currentUser.role_id === 2 && currentUser.org_id === project.org_id) || // 企业管理员
      project.owner_id === currentUser.id || // 项目所有者
      currentUser.is_superuser // 兼容旧字段
    );
  };

  // 检查用户是否有成员管理权限
  const canManageMembers = () => {
    if (!currentUser || !project) {
      return false;
    }
    
    // 优先使用后端返回的权限信息
    if (project.permissions && typeof project.permissions.can_manage_members === 'boolean') {
      return project.permissions.can_manage_members;
    }
    
    // 兜底逻辑：超级管理员、企业管理员（同企业）、项目所有者
    return (
      currentUser.role_id === 1 || // 超级管理员
      (currentUser.role_id === 2 && currentUser.org_id === project.org_id) || // 企业管理员
      project.owner_id === currentUser.id || // 项目所有者
      currentUser.is_superuser // 兼容旧字段
    );
  };

  // 检查用户是否有工具管理权限
  const canManageTools = () => {
    console.log('=== Tool management permission check debug info ===');
    console.log('currentUser:', currentUser);
    console.log('project:', project);
    console.log('userProjectRole:', userProjectRole);
    
    if (!currentUser || !project) {
      console.log('User or project info missing');
      return false;
    }
    
    // 超级管理员总是可以管理工具
    if (currentUser.role_id === 1) {
      console.log('Super admin tool management permission granted');
      return true;
    }
    
    // 企业管理员可以管理本企业项目的工具
    if (currentUser.role_id === 2 && currentUser.org_id === project.org_id) {
      console.log('Enterprise admin tool management permission granted');
      return true;
    }
    
    // 项目所有者可以管理工具
    if (project.owner_id === currentUser.id) {
      console.log('Project owner tool management permission granted');
      return true;
    }
    
    // 兼容旧的权限字段
    if (currentUser.is_superuser) {
      console.log('Legacy super user tool management permission granted');
      return true;
    }
    
    // 基于项目角色的权限检查
    if (userProjectRole) {
      console.log('Check project role:', userProjectRole);
      // Project collaborator can manage tools
      if (userProjectRole === t('project:roles.collaborator')) {
        console.log('Project collaborator tool management permission granted');
        return true;
      }
      // Project observer cannot manage tools
      if (userProjectRole === t('project:roles.observer')) {
        console.log('Project observer has no tool management permission');
        return false;
      }
    }
    
    // 对于普通用户，如果是公开项目且在同一企业，暂时允许管理工具
    // 这是兜底逻辑，主要用于处理还没有获取到项目角色信息的情况
    if (currentUser.role_id === 3) {
      console.log('Regular user permission check:', project.visibility, currentUser.org_id, project.org_id);
      if (project.visibility === 'PUBLIC' && currentUser.org_id === project.org_id) {
        console.log('Public project tool management permission granted');
        return true;
      }
    }
    
    if (project.permissions && typeof project.permissions.can_manage_tools === 'boolean') {
      return project.permissions.can_manage_tools;
    }
    
    console.log('Tool management permission check failed');
    return false;
  };

  // 打开报告提示词编辑弹窗
  const handleEditReportPrompt = () => {
    reportPromptForm.setFieldsValue({
      report_prompt: project?.report_prompt || ''
    });
    setReportPromptModalVisible(true);
  };

  // 保存报告提示词
  const handleSaveReportPrompt = async () => {
    try {
      setReportPromptSaving(true);
      const values = await reportPromptForm.validateFields();

      await projectApi.updateProject(id!, {
        report_prompt: values.report_prompt
      });

      message.success(t('projectDetail:messages.reportPromptUpdateSuccess'));
      setReportPromptModalVisible(false);

      // 保持在报告设置标签页
      setActiveTabKey('report-settings');

      // 刷新项目信息
      await fetchProjectDetail();
    } catch (error: any) {
      console.error('Update report prompt failed:', error);
      message.error(error.response?.data?.message || t('projectDetail:messages.reportPromptUpdateFailed'));
    } finally {
      setReportPromptSaving(false);
    }
  };

  // 重置为默认提示词
  const handleResetReportPrompt = () => {
    const defaultPrompt = t('projectDetail:defaultReportPrompt');

    reportPromptForm.setFieldsValue({
      report_prompt: defaultPrompt
    });
  };

  // 初始化时获取项目详情和用户信息
  useEffect(() => {
    fetchProjectDetail();
    fetchCurrentUser();
  }, [id]);

  // 当用户信息获取完成后，获取用户在项目中的角色
  useEffect(() => {
    if (currentUser && id) {
      fetchUserProjectRole();
    }
  }, [currentUser, id]);
  
  // 打开编辑数据源模态框
  const openEditDataSourceModal = (dataSource: any) => {
    setEditingDataSource(dataSource);
    setConnectionSuccess(!!dataSource?.id); 

    if (dataSource?.id) {
      dataSourceForm.setFieldsValue({
        name: dataSource.name,
        description: dataSource.description,
        type: dataSource.type,
        config: dataSource.config,
      });
      setSelectedDataSourceType(dataSource.type);
    } else {
      dataSourceForm.resetFields();
      dataSourceForm.setFieldsValue({ type: 'mysql' });
      setSelectedDataSourceType('mysql');
    }
    
    setDataSourceEditModalVisible(true);
  };
  
  // 测试数据源连接
  const testConnection = async () => {
    try {
      const formValues = await dataSourceForm.validateFields();
      setConnectionTesting(true);
      
      // 使用项目向导API测试连接
      const response = await projectWizardApi.testConnection({
        type: formValues.type,
        config: formValues.config
      });
      
      if (response.data.success) {
        setConnectionSuccess(true);
        message.success(t('projectDetail:messages.connectionSuccess'));
        
        // 如果是编辑现有数据源，保存可用的表
        if (response.data.tables && response.data.tables.length > 0) {
          setAvailableTables(response.data.tables);
        }
      } else {
        setConnectionSuccess(false);
        message.error(t('projectDetail:messages.connectionFailed') + ': ' + response.data.message);
      }
    } catch (error) {
      console.error('Test connection failed', error);
      message.error(t('projectDetail:messages.connectionFailed'));
      setConnectionSuccess(false);
    } finally {
      setConnectionTesting(false);
    }
  };
  
  // 更新或创建数据源
  const handleUpdateDataSource = async () => {
    try {
      if (!connectionSuccess) {
        message.warning(t('projectDetail:messages.testConnectionFirst'));
        return;
      }
      
      const values = await dataSourceForm.validateFields();
      
      const payload = {
        ...values,
        project_id: id,
      };

      if (editingDataSource?.id) {
        // 更新现有数据源
        await dataSourceApi.updateDataSource(editingDataSource.id, {
          name: values.name,
          description: values.description,
          type: values.type,
          config: values.config,
        });
        message.success(t('projectDetail:messages.dataSourceUpdateSuccess'));
      } else {
        // 创建新数据源并关联到项目
        await projectWizardApi.addDataSource(payload);
        message.success(t('projectDetail:messages.dataSourceAddSuccess'));
      }
      
      setDataSourceEditModalVisible(false);
      fetchProjectDetail(); // 刷新项目详情
      
    } catch (error: any) {
      console.error('Operation data source failed:', error);
      if (error.response && error.response.data && error.response.data.message) {
        message.error(t('common:message.error.operationFailed') + ': ' + error.response.data.message);
      } else {
        message.error(t('common:message.error.operationFailed'));
      }
    }
  };
  
  // 渲染数据源配置表单
  const renderDataSourceConfigFields = () => {
    switch (selectedDataSourceType) {
      case 'oracle':
        return (
          <>
            <Form.Item
              name={['config', 'host']}
              label={t('common:labels.hostAddress')}
              rules={[{ required: true, message: t('project:wizard.validation.hostRequired') }]}
            >
              <Input placeholder={t('project:wizard.placeholders.hostExample')} />
            </Form.Item>
            <Form.Item
              name={['config', 'port']}
              label={t('common:labels.port')}
              rules={[{ required: true, message: t('project:wizard.validation.portRequired') }]}
              initialValue={1521}
            >
              <Input 
                type="number" 
                placeholder={t('project:wizard.placeholders.oraclePort')} 
                onChange={(e) => {
                  const portValue = e.target.value ? parseInt(e.target.value, 10) : 0;
                  dataSourceForm.setFieldValue(['config', 'port'], portValue);
                }}
              />
            </Form.Item>
            <Form.Item
              name={['config', 'service_name']}
              label={t('common:labels.serviceName')}
              rules={[{ required: true, message: t('project:wizard.validation.serviceNameRequired') }]}
            >
              <Input placeholder={t('project:wizard.placeholders.serviceNameExample')} />
            </Form.Item>
            <Form.Item
              name={['config', 'username']}
              label={t('common:labels.username')}
              rules={[{ required: true, message: t('project:wizard.validation.usernameRequired') }]}
            >
              <Input placeholder={t('project:wizard.placeholders.oracleUsername')} />
            </Form.Item>
            <Form.Item
              name={['config', 'password']}
              label={t('common:labels.password')}
              rules={[{ required: true, message: t('project:wizard.validation.passwordRequired') }]}
            >
              <Input.Password placeholder={t('common:labels.inputPassword')} />
            </Form.Item>
          </>
        );
        
      case 'mysql':
      case 'postgresql':
      case 'mssql':
        return (
          <>
            <Form.Item
              name={['config', 'host']}
              label={t('common:labels.hostAddress')}
              rules={[{ required: true, message: t('project:wizard.validation.hostRequired') }]}
            >
              <Input placeholder={t('project:wizard.placeholders.hostExample')} />
            </Form.Item>
            <Form.Item
              name={['config', 'port']}
              label={t('common:labels.port')}
              rules={[{ required: true, message: t('project:wizard.validation.portRequired') }]}
            >
              <Input 
                type="number" 
                placeholder={t('project:wizard.placeholders.mysqlPort')} 
                onChange={(e) => {
                  const portValue = e.target.value ? parseInt(e.target.value, 10) : 0;
                  dataSourceForm.setFieldValue(['config', 'port'], portValue);
                }}
              />
            </Form.Item>
            <Form.Item
              name={['config', 'database']}
              label={t('common:labels.databaseName')}
              rules={[{ required: true, message: t('project:wizard.validation.databaseRequired') }]}
            >
              <Input placeholder={t('project:wizard.placeholders.databaseExample')} />
            </Form.Item>
            <Form.Item
              name={['config', 'username']}
              label={t('common:labels.username')}
              rules={[{ required: true, message: t('project:wizard.validation.usernameRequired') }]}
            >
              <Input placeholder={t('project:wizard.placeholders.mysqlUsername')} />
            </Form.Item>
            <Form.Item
              name={['config', 'password']}
              label={t('common:labels.password')}
              rules={[{ required: true, message: t('project:wizard.validation.passwordRequired') }]}
            >
              <Input.Password placeholder={t('common:labels.inputPassword')} />
            </Form.Item>
          </>
        );
        
      case 'http_api':
        return (
          <>
            <Form.Item
              name={['config', 'base_url']}
              label={t('project:wizard.labels.baseUrl')}
              rules={[{ required: true, message: t('project:wizard.validation.baseUrlRequired') }]}
            >
              <Input placeholder={t('project:wizard.placeholders.baseUrlExample')} />
            </Form.Item>
            <Form.Item
              name={['config', 'headers']}
              label={t('common:labels.requestHeaders')}
              tooltip={t('project:wizard.tooltips.requestHeaders')}
            >
              <TextArea
                placeholder={t('project:wizard.placeholders.headersExample')}
                rows={4}
              />
            </Form.Item>
          </>
        );
        
      default:
        return null;
    }
  };
  
  // 打开表编辑模态框
  const openTableEditModal = async (dataSourceId: string) => {
    try {
      // 获取该数据源的可用表
      const response = await projectWizardApi.getDataSourceTables(dataSourceId);
      if (response.data) {
        setAvailableTables(response.data);
        
        // 设置已选中的表
        const checkedTableNames = selectedTables
          .filter(table => table.data_source_id === dataSourceId)
          .map(table => table.table_name);
          
        setCheckedTables(checkedTableNames);
        setEditingDataSource({ id: dataSourceId });
        setTableEditModalVisible(true);
      }
    } catch (error) {
      console.error('Fetch table list failed', error);
      message.error(t('projectDetail:messages.fetchTablesFailed'));
    }
  };
  
  // 保存选定的表
  const handleSaveTables = async () => {
    if (!editingDataSource?.id) {
      message.error(t('projectForm:validation.dataSourceIdRequired'));
      return;
    }
    
    try {
      setSavingTables(true);
      
      const response = await projectWizardApi.saveTables(editingDataSource.id, {
        tables: checkedTables
      });
      
      if (response.data.success) {
        message.success(t('projectDetail:messages.tablesSaveSuccess'));
        setTableEditModalVisible(false);
        fetchProjectTables(); // 刷新表列表
      } else {
        message.error(t('projectDetail:messages.saveTableSelectionFailed') + ': ' + response.data.message);
      }
    } catch (error) {
      console.error('Save tables failed', error);
      message.error(t('projectDetail:messages.saveTableSelectionFailed'));
    } finally {
      setSavingTables(false);
    }
  };
  
  // 打开表描述编辑弹框
  const openTableDescriptionModal = () => {
    setTableDescriptionModalVisible(true);
  };
  
  // 关闭表描述编辑弹框
  const closeTableDescriptionModal = () => {
    setTableDescriptionModalVisible(false);
  };
  
  // 表描述更新后的回调
  const handleTableDescriptionUpdate = () => {
    fetchProjectTables(); // 刷新项目表列表
  };
  
  // 渲染数据源信息
  const renderDataSources = () => {
    if (!project?.data_sources || project.data_sources.length === 0) {
      return (
        <div>
          <span style={{ marginRight: 16 }}>{t('projectDetail:labels.noDataSourceConfigured')}</span>
          {canEditProject() && (
            <Button 
              icon={<PlusOutlined />}
              onClick={() => openEditDataSourceModal({})}
            >
              {t('projectDetail:buttons.addDataSource')}
            </Button>
          )}
        </div>
      );
    }
    
    return (
      <List
        itemLayout="vertical"
        dataSource={project.data_sources}
        renderItem={(dataSource: any) => (
          <List.Item
            actions={[
              <Button 
                key="edit" 
                type="primary"
                icon={<EditOutlined />}
                onClick={() => openEditDataSourceModal(dataSource)}
                disabled={!canEditProject()}
              >
                {t('projectDetail:buttons.editDataSource')}
              </Button>
            ]}
          >
            <Card title={dataSource.name} size="small">
              <Descriptions bordered size="small" column={1}>
                <Descriptions.Item label={t('common:labels.dataSourceType')}>
                  {(() => {
                    switch(dataSource.type.toLowerCase()) {
                      case 'mysql': return t('common:dataSource.types.mysql');
                      case 'postgresql': return t('common:dataSource.types.postgresql');
                      case 'oracle': return t('common:dataSource.types.oracle');
                      case 'mssql': return t('common:dataSource.types.mssql');
                      case 'http_api': return 'HTTP API';
                      default: return dataSource.type;
                    }
                  })()}
                </Descriptions.Item>
                <Descriptions.Item label={t('common:labels.description')}>{dataSource.description || '-'}</Descriptions.Item>
                <Descriptions.Item label={t('projectDetail:labels.connectionConfig')}>
                  <pre style={{ whiteSpace: 'pre-wrap' }}>
                    {JSON.stringify(dataSource.config, null, 2)
                      .replace(/"password": ".*?"/, '"password": "********"')}
                  </pre>
                </Descriptions.Item>
                <Descriptions.Item label={t('common:time.createdAt')}>
                  {new Date(dataSource.created_at).toLocaleString()}
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </List.Item>
        )}
      />
    );
  };
  
  // 渲染选定的表信息
  const renderSelectedTables = () => {
    if (!project?.data_sources || project.data_sources.length === 0) {
      return <p>{t('projectDetail:labels.noDataSourceConfigured')}</p>;
    }
    
    if (!selectedTables.length) {
      return (
        <div>
          <p>{t('projectDetail:labels.noTablesSelected')}</p>
          {project.data_sources.map((dataSource: any) => (
            <Button 
              key={dataSource.id}
              icon={<TableOutlined />}
              onClick={() => openTableEditModal(dataSource.id)}
              style={{ marginTop: 16 }}
            >
              {t('projectDetail:buttons.selectTablesFor', { name: dataSource.name })}
            </Button>
          ))}
        </div>
      );
    }
    
    // 按数据源分组显示表
    const tablesByDataSource: {[key: string]: any[]} = {};
    
    // 分组表
    selectedTables.forEach(table => {
      if (!tablesByDataSource[table.data_source_id]) {
        tablesByDataSource[table.data_source_id] = [];
      }
      tablesByDataSource[table.data_source_id].push(table);
    });
    
    return (
      <div>
        {project.data_sources.map((dataSource: any) => (
          <div key={dataSource.id} style={{ marginBottom: 24 }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
              <h3 style={{ margin: 0 }}>{dataSource.name}</h3>
              <Space>
                <Button 
                  icon={<TableOutlined />}
                  onClick={() => openTableEditModal(dataSource.id)}
                >
                  {t('projectDetail:buttons.editTables')}
                </Button>
                <Button 
                  icon={<FileTextOutlined />}
                  onClick={() => openTableDescriptionModal()}
                >
                  {t('projectDetail:buttons.editTableDescriptions')}
                </Button>
              </Space>
            </div>
            
            {tablesByDataSource[dataSource.id] && tablesByDataSource[dataSource.id].length > 0 ? (
              <ul>
                {tablesByDataSource[dataSource.id].map((table: any) => (
                  <li key={table.id}>
                    <b>{table.table_name}</b>
                    {table.table_description && <p>{table.table_description}</p>}
                  </li>
                ))}
              </ul>
            ) : (
              <p>{t('projectDetail:labels.noTablesSelectedForDataSource')}</p>
            )}
          </div>
        ))}
      </div>
    );
  };

  // 渲染报告提示词管理
  const renderReportPromptManagement = () => {
    return (
      <div>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
          <Title level={4}>{t('projectDetail:labels.reportPrompt')}</Title>
          {canEditProject() && (
            <Button
              type="primary"
              icon={<EditOutlined />}
              onClick={handleEditReportPrompt}
            >
              {t('projectDetail:buttons.editPrompt')}
            </Button>
          )}
        </div>

        <Card>
          <Paragraph>
            <strong>{t('projectDetail:labels.currentPrompt')}:</strong>
          </Paragraph>
          <div style={{
            background: '#f5f5f5',
            padding: '12px',
            borderRadius: '6px',
            whiteSpace: 'pre-wrap',
            maxHeight: '300px',
            overflow: 'auto'
          }}>
            {project?.report_prompt || t('projectDetail:labels.usingDefaultPrompt')}
          </div>

          <Paragraph style={{ marginTop: 16, color: '#666' }}>
            <strong>{t('projectDetail:labels.description')}:</strong> {t('projectDetail:labels.reportPromptDescription')}
          </Paragraph>
        </Card>
      </div>
    );
  };
  
  if (loading) {
    return (
      <Card>
        <Skeleton active />
      </Card>
    );
  }
  
  return (
    <div>
      <Button 
        icon={<LeftOutlined />} 
        onClick={() => navigate('/projects')}
        style={{ marginBottom: 16 }}
      >
        {t('projectDetail:buttons.backToList')}
      </Button>
      
      <Card>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
          <Title level={2}>{project?.name}</Title>
          <Space>
            {canEditProject() && (
              <Button
                type="primary" 
                icon={<EditOutlined />}
                onClick={() => setEditModalVisible(true)}
              >
                {t('projectDetail:buttons.editProject')}
              </Button>
            )}
            {canDeleteProject() && (
              <Button
                danger
                icon={<DeleteOutlined />}
                onClick={handleDeleteProject}
              >
                {t('projectDetail:buttons.deleteProject')}
              </Button>
            )}
          </Space>
        </div>
        
        <Tabs defaultActiveKey="overview">
          <TabPane tab={<span><FileTextOutlined />{t('projectDetail:tabs.overview')}</span>} key="overview">
            <Paragraph>{project?.description || t('projectDetail:noDescription')}</Paragraph>
            
            <Divider />
            
            <Tabs 
              activeKey={activeTabKey}
              onChange={setActiveTabKey}
            >
              <TabPane tab={<span><DatabaseOutlined />{t('projectDetail:tabs.basicInfo')}</span>} key="basic">
                <Descriptions bordered size="small" column={1}>
                  <Descriptions.Item label={t('projectDetail:labels.projectId')}>{project?.id}</Descriptions.Item>
                  <Descriptions.Item label={t('projectDetail:labels.projectOwner')}>{project?.owner_username || `${t('projectDetail:labels.userId')}: ${project?.owner_id}`}</Descriptions.Item>
                  <Descriptions.Item label={t('projectDetail:labels.projectVisibility')}>
                    <Tag color={project?.visibility === 'PUBLIC' ? 'green' : 'orange'}>
                      {project?.visibility === 'PUBLIC' ? t('project:visibility.publicProject') : t('project:visibility.privateProject')}
                    </Tag>
                    <span style={{ marginLeft: 8, color: '#666', fontSize: '12px' }}>
                      {project?.visibility === 'PUBLIC' ? t('projectDetail:visibility.allMembersVisible') : t('projectDetail:visibility.invitedMembersOnly')}
                    </span>
                  </Descriptions.Item>
                  <Descriptions.Item label={t('common:time.createdAt')}>{new Date(project?.created_at).toLocaleString()}</Descriptions.Item>
                  <Descriptions.Item label={t('common:time.updatedAt')}>{new Date(project?.updated_at).toLocaleString()}</Descriptions.Item>
                </Descriptions>
              </TabPane>
              
              <TabPane tab={<span><DatabaseOutlined />{t('projectDetail:tabs.dataSource')}</span>} key="datasource">
                {renderDataSources()}
              </TabPane>
              
              <TabPane tab={<span><TableOutlined />{t('projectDetail:tabs.dataTables')}</span>} key="tables">
                {renderSelectedTables()}
              </TabPane>
              
              {canManageMembers() && (
                <TabPane tab={<span><TeamOutlined />{t('projectDetail:tabs.memberManagement')}</span>} key="members">
                  <ProjectMemberManagement
                    projectId={id!}
                    currentUser={currentUser}
                    project={project}
                    onMemberChange={() => {
                      // 成员变化时可以刷新项目信息
                      fetchProjectDetail();
                    }}
                  />
                </TabPane>
              )}

              <TabPane tab={<span><SettingOutlined />{t('projectDetail:tabs.reportSettings')}</span>} key="report-settings">
                {renderReportPromptManagement()}
              </TabPane>
            </Tabs>
          </TabPane>
        </Tabs>
      </Card>
      
      {/* 编辑项目信息的弹窗 */}
      <Modal
        title={t('projectDetail:modals.editProject')}
        open={editModalVisible}
        onOk={handleEditProject}
        onCancel={() => setEditModalVisible(false)}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label={t('projectDetail:labels.projectName')}
            rules={[{ required: true, message: t('projectDetail:validation.projectNameRequired') }]}
          >
            <Input placeholder={t('projectDetail:placeholders.enterProjectName')} />
          </Form.Item>
          <Form.Item
            name="description"
            label={t('projectDetail:labels.projectDescription')}
          >
            <TextArea 
              placeholder={t('projectDetail:placeholders.enterProjectDescription')} 
              rows={4} 
            />
          </Form.Item>
          <Form.Item
            name="visibility"
            label={t('projectDetail:labels.projectVisibility')}
            rules={[{ required: true, message: t('projectDetail:validation.projectVisibilityRequired') }]}
          >
            <Select placeholder={t('projectDetail:placeholders.selectProjectVisibility')}>
              <Option value="PRIVATE">{t('project:visibility.privateProject')}</Option>
              <Option value="PUBLIC">{t('project:visibility.publicProject')}</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
      
      {/* 编辑数据源模态框 */}
      <Modal
        title={editingDataSource?.id ? t('projectDetail:modals.editDataSource') : t('projectDetail:modals.addDataSource')}
        open={dataSourceEditModalVisible}
        onCancel={() => setDataSourceEditModalVisible(false)}
        footer={[
          <Button key="back" onClick={() => setDataSourceEditModalVisible(false)}>
            {t('common:buttons.cancel')}
          </Button>,
          <Button 
            key="test" 
            onClick={testConnection}
            loading={connectionTesting}
          >
            {t('projectDetail:buttons.testConnection')}
          </Button>,
          <Button 
            key="submit" 
            type="primary" 
            onClick={handleUpdateDataSource}
            disabled={!connectionSuccess}
          >
            {t('projectDetail:buttons.save')}
          </Button>
        ]}
        width={700}
      >
        <Form
          form={dataSourceForm}
          layout="vertical"
          onValuesChange={(changedValues) => {
            if (changedValues.type) {
              setSelectedDataSourceType(changedValues.type);
              setConnectionSuccess(false);
            }
            
            // 如果修改了连接配置，需要重新测试连接
            if (changedValues.config) {
              setConnectionSuccess(false);
            }
          }}
        >
          <Form.Item
            name="name"
            label={t('projectDetail:labels.dataSourceName')}
            rules={[{ required: true, message: t('projectDetail:validation.pleaseEnterDataSourceName') }]}
          >
            <Input placeholder={t('projectDetail:placeholders.enterDataSourceNameHere')} />
          </Form.Item>
          <Form.Item
            name="description"
            label={t('projectDetail:labels.dataSourceDescription')}
          >
            <TextArea 
              placeholder={t('projectDetail:placeholders.enterDataSourceDescriptionOptional')} 
              rows={2} 
            />
          </Form.Item>
          <Form.Item
            name="type"
            label={t('projectDetail:labels.dataSourceType')}
            rules={[{ required: true, message: t('projectDetail:validation.pleaseSelectDataSourceType') }]}
          >
            <Select 
              placeholder={t('projectDetail:placeholders.selectDataSourceTypeHere')}
              disabled={!!editingDataSource?.id}
            >
              {getDataSourceTypes(t).map((type: any) => (
                <Option key={type.value} value={type.value}>{type.label}</Option>
              ))}
            </Select>
          </Form.Item>
          
          <Divider orientation="left">{t('projectDetail:dividers.connectionConfig')}</Divider>
          
          {renderDataSourceConfigFields()}
          
          {connectionSuccess && (
            <div style={{ marginTop: 16, color: 'green' }}>
              <CheckCircleOutlined /> {t('projectDetail:labels.connectionSuccess')}
            </div>
          )}
        </Form>
      </Modal>
      
      {/* 编辑表模态框 */}
      <Modal
        title={t('projectDetail:modals.selectTables')}
        open={tableEditModalVisible}
        onCancel={() => setTableEditModalVisible(false)}
        footer={[
          <Button key="back" onClick={() => setTableEditModalVisible(false)}>
            {t('projectDetail:buttons.cancel')}
          </Button>,
          <Button 
            key="submit" 
            type="primary" 
            onClick={handleSaveTables}
            loading={savingTables}
            disabled={checkedTables.length === 0}
          >
            {t('projectDetail:buttons.save')}
          </Button>
        ]}
        width={600}
      >
        <div style={{ marginBottom: 16 }}>
          {t('projectDetail:labels.tablesSelected', { count: checkedTables.length })}
        </div>
        
        <Checkbox.Group
          style={{ width: '100%' }}
          value={checkedTables}
          onChange={(values) => setCheckedTables(values as string[])}
        >
          <Row>
            {availableTables.map(table => (
              <Col span={12} key={table}>
                <Checkbox value={table}>{table}</Checkbox>
              </Col>
            ))}
          </Row>
        </Checkbox.Group>
      </Modal>
      
      {/* 表描述编辑弹框 */}
      <TableDescriptionModal
        visible={tableDescriptionModalVisible}
        onClose={closeTableDescriptionModal}
        projectId={id!}
        onUpdate={handleTableDescriptionUpdate}
      />

      {/* 报告提示词编辑弹框 */}
      <Modal
        title={t('projectDetail:modals.editReportPrompt')}
        open={reportPromptModalVisible}
        onOk={handleSaveReportPrompt}
        onCancel={() => setReportPromptModalVisible(false)}
        width={800}
        confirmLoading={reportPromptSaving}
        footer={[
          <Button key="reset" onClick={handleResetReportPrompt}>
            {t('projectDetail:buttons.resetToDefault')}
          </Button>,
          <Button key="cancel" onClick={() => setReportPromptModalVisible(false)}>
            {t('projectDetail:buttons.cancel')}
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={reportPromptSaving}
            onClick={handleSaveReportPrompt}
          >
            {t('projectDetail:buttons.save')}
          </Button>
        ]}
      >
        <Form
          form={reportPromptForm}
          layout="vertical"
        >
          <Form.Item
            label={t('projectDetail:labels.reportPrompt')}
            name="report_prompt"
            rules={[
              { required: false, message: t('projectDetail:validation.pleaseEnterReportPrompt') }
            ]}
            extra={
              <div style={{ marginTop: 8 }}>
                {t('projectDetail:prompts.customPromptDescription')}
              </div>
            }
          >
            <TextArea
              rows={12}
              placeholder={t('projectDetail:placeholders.enterCustomReportPrompt')}
              showCount
              maxLength={5000}
              style={{ marginBottom: 16 }}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ProjectDetail; 