import React, { useState, useEffect } from 'react';
import { 
  Card, Form, Button, Table, message, Space, Typography, Spin, Alert
} from 'antd';
import { useTranslation } from 'react-i18next';
import { 
  TableOutlined, LoadingOutlined, ExclamationCircleOutlined, ReloadOutlined
} from '@ant-design/icons';
import { projectWizardApi } from '../../services/api';

const { Paragraph } = Typography;

interface TableSelectionStepProps {
  dataSourceId: string | null;
  selectedTables: string[];
  cachedTableList?: string[];  // 新增：缓存的表格列表
  onTableSelectionChange: (tables: string[]) => void;
  onNext: () => void;
  onPrev?: () => void;  // 新增：返回上一步回调
  onTableListUpdate?: (tables: string[]) => void;  // 新增：表格列表更新回调
}

const TableSelectionStep: React.FC<TableSelectionStepProps> = ({
  dataSourceId,
  selectedTables,
  cachedTableList = [],
  onTableSelectionChange,
  onNext,
  onPrev,
  onTableListUpdate
}) => {
  const { t } = useTranslation();
  const [tableList, setTableList] = useState<string[]>(cachedTableList);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isFromCache, setIsFromCache] = useState(false);
  const [form] = Form.useForm();

  // 组件挂载时的逻辑优化
  useEffect(() => {
    if (dataSourceId) {
      // 如果有缓存的表格列表，优先使用缓存
      if (cachedTableList && cachedTableList.length > 0) {
        setTableList(cachedTableList);
        setIsFromCache(true);
        setError(null);
        console.log('Using cached table list:', cachedTableList);
      } else {
        // 没有缓存时才请求API
        fetchTableList();
      }
    }
  }, [dataSourceId, cachedTableList]);

  // 获取数据源表列表
  const fetchTableList = async (forceRefresh = false) => {
    if (!dataSourceId) {
      setError(t('project:wizard.tableSelection.dataSourceIdMissing'));
      return;
    }

    try {
      setLoading(true);
      setError(null);
      setIsFromCache(false);
      
      const response = await projectWizardApi.getDataSourceTables(dataSourceId);
      
      if (response.data && Array.isArray(response.data)) {
        setTableList(response.data);
        // 通知父组件更新缓存
        if (onTableListUpdate) {
          onTableListUpdate(response.data);
        }
        
        if (response.data.length === 0) {
          message.warning(t('project:wizard.tableSelection.noTablesAvailable'));
        } else if (forceRefresh) {
          message.success(t('project:wizard.tableSelection.refreshSuccess', { count: response.data.length }));
        }
      } else {
        throw new Error(t('project:wizard.tableSelection.invalidTableListFormat'));
      }
    } catch (error: any) {
      console.error('Failed to fetch table list', error);
      let errorMessage = t('project:wizard.tableSelection.fetchTablesFailed');
      
      if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
        errorMessage = t('project:wizard.tableSelection.requestTimeout');
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      setError(errorMessage);
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // 处理表格选择变化
  const handleTableSelectionChange = (selectedRowKeys: React.Key[]) => {
    onTableSelectionChange(selectedRowKeys as string[]);
  };

  // 处理下一步
  const handleNext = () => {
    if (selectedTables.length === 0) {
      message.warning(t('project:wizard.tableSelection.selectAtLeastOneTable'));
      return;
    }
    onNext();
  };

  // 手动刷新表格列表
  const handleRefresh = () => {
    fetchTableList(true);
  };

  // 渲染加载状态
  if (loading) {
    return (
      <Card title={t('project:wizard.tableSelection.title')} variant="outlined">
        <div style={{ 
          textAlign: 'center', 
          padding: '60px 20px',
          minHeight: '300px',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center'
        }}>
          <Spin 
            size="large" 
            indicator={<LoadingOutlined style={{ fontSize: 48, color: '#1890ff' }} spin />}
          />
          <div style={{ marginTop: 24, fontSize: 16, color: '#666' }}>
            {t('project:wizard.tableSelection.readingTables')}
          </div>
          <div style={{ marginTop: 8, fontSize: 14, color: '#999' }}>
            {t('project:wizard.tableSelection.pleaseWait')}
          </div>
        </div>
      </Card>
    );
  }

  // 渲染错误状态
  if (error) {
    return (
      <Card title={t('project:wizard.tableSelection.title')} variant="outlined">
        <Alert
          message={t('project:wizard.tableSelection.fetchTablesFailed')}
          description={error}
          type="error"
          showIcon
          icon={<ExclamationCircleOutlined />}
          action={
            <Button size="small" onClick={handleRefresh}>
              {t('common:buttons.retry')}
            </Button>
          }
          style={{ marginBottom: 24 }}
        />
        
        <div style={{ textAlign: 'center', padding: '40px 20px' }}>
          <div style={{ color: '#999', marginBottom: 16 }}>
            {t('project:wizard.tableSelection.unableToFetchTables')}
          </div>
          <Button type="primary" onClick={handleRefresh}>
            {t('project:wizard.tableSelection.refetch')}
          </Button>
        </div>
      </Card>
    );
  }

  // 渲染正常状态
  return (
    <Card title="选择分析表" variant="outlined">
      <Form form={form} layout="vertical">
        <Paragraph>
          {t('project:wizard.tableSelection.description')}
        </Paragraph>
        
        <div style={{ marginBottom: 16 }}>
          <Space>
            <TableOutlined />
            <span style={{ marginRight: 8 }}>
              {t('project:wizard.tableSelection.tableCount', { total: tableList.length, selected: selectedTables.length })}
            </span>
            {isFromCache && (
              <span style={{ color: '#999', fontSize: '12px' }}>
                ({t('project:wizard.tableSelection.usingCachedData')})
              </span>
            )}
            {tableList.length > 0 && (
              <>
                <Button 
                  type="link" 
                  size="small"
                  onClick={() => {
                    if (selectedTables.length === tableList.length) {
                      onTableSelectionChange([]);
                    } else {
                      onTableSelectionChange(tableList);
                    }
                  }}
                >
                  {selectedTables.length === tableList.length ? t('project:wizard.tableSelection.deselectAll') : t('project:wizard.tableSelection.selectAll')}
                </Button>
                <Button 
                  type="link" 
                  size="small"
                  icon={<ReloadOutlined />}
                  onClick={handleRefresh}
                  title={t('project:wizard.tableSelection.refreshTableList')}
                >
                  {t('common:buttons.refresh')}
                </Button>
              </>
            )}
          </Space>
        </div>
        
        {tableList.length > 0 ? (
          <Table
            rowSelection={{
              type: 'checkbox',
              selectedRowKeys: selectedTables,
              onChange: handleTableSelectionChange,
              getCheckboxProps: (record) => ({
                name: record.tableName,
              }),
            }}
            dataSource={tableList.map(table => ({ 
              key: table, 
              tableName: table 
            }))}
            columns={[
              {
                title: t('project:wizard.tableSelection.tableName'),
                dataIndex: 'tableName',
                key: 'tableName',
                render: (tableName: string) => (
                  <Space>
                    <TableOutlined style={{ color: '#1890ff' }} />
                    <span>{tableName}</span>
                  </Space>
                )
              }
            ]}
            pagination={{ 
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => 
                t('project:wizard.tableSelection.paginationInfo', { start: range[0], end: range[1], total })
            }}
            size="middle"
          />
        ) : (
          <div style={{ 
            textAlign: 'center', 
            padding: '40px 20px',
            border: '1px dashed #d9d9d9',
            borderRadius: '6px',
            backgroundColor: '#fafafa'
          }}>
            <TableOutlined style={{ fontSize: 48, color: '#d9d9d9', marginBottom: 16 }} />
            <div style={{ color: '#999', marginBottom: 16 }}>
              {t('project:wizard.tableSelection.noTablesAvailable')}
            </div>
            <Button type="primary" onClick={handleRefresh}>
              {t('project:wizard.tableSelection.refreshList')}
            </Button>
          </div>
        )}
        
        <div style={{ marginTop: 24, display: 'flex', justifyContent: 'space-between' }}>
          {onPrev && (
            <Button onClick={onPrev}>
              {t('project:wizard.tableSelection.backToDataSource')}
            </Button>
          )}
          <Button 
            type="primary"
            onClick={handleNext}
            disabled={selectedTables.length === 0}
          >
            {t('project:wizard.tableSelection.nextStep', { 
              status: selectedTables.length > 0 
                ? t('project:wizard.tableSelection.selectedCount', { count: selectedTables.length })
                : t('project:wizard.tableSelection.noTableSelected')
            })}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default TableSelectionStep; 