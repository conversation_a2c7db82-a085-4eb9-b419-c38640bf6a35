import React, { useState, useEffect } from 'react';
import { 
  Steps, Form, Input, Button, Card, Select, message, 
  Row, Col, Divider, Table, Transfer, notification,
  Typography, Space, Checkbox, Radio, Modal, Tag
} from 'antd';
import { 
  ProjectOutlined, DatabaseOutlined, TableOutlined,
  CheckCircleOutlined, LoadingOutlined, ExclamationCircleOutlined,
  FileTextOutlined, UserAddOutlined, DeleteOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { projectWizardApi, projectMemberApi } from '../../services/api';
import TableDescriptionStep from './TableDescriptionStep';
import TableSelectionStep from './TableSelectionStep';
import ProjectIntelligenceWizard from '../../components/ProjectIntelligence/ProjectIntelligenceWizard';

const { Step } = Steps;
const { TextArea } = Input;
const { Option } = Select;
const { Title, Paragraph } = Typography;

const ProjectWizard = () => {
  const { t } = useTranslation();

// 数据源类型选项
const dataSourceTypes = [
    { value: 'oracle', label: t('project:wizard.dataSource.oracle') },
    { value: 'mysql', label: t('project:wizard.dataSource.mysql') },
    { value: 'postgresql', label: t('project:wizard.dataSource.postgresql') },
    { value: 'mssql', label: t('project:wizard.dataSource.mssql') },
  // { value: 'http_api', label: 'HTTP API' }
];
  const [currentStep, setCurrentStep] = useState(0);
  const [form] = Form.useForm();
  const [dataSourceForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [projectId, setProjectId] = useState<string | null>(null);
  const [dataSourceId, setDataSourceId] = useState<string | null>(null);
  const [selectedDataSourceType, setSelectedDataSourceType] = useState<string>('mysql');
  const [connectionSuccess, setConnectionSuccess] = useState(false);
  const [selectedTables, setSelectedTables] = useState<string[]>([]);
  const [savingTables, setSavingTables] = useState(false);
  const [cachedTableList, setCachedTableList] = useState<string[]>([]);
  const [connectionTesting, setConnectionTesting] = useState(false);
  const [members, setMembers] = useState<any[]>([]);
  const [invitableUsers, setInvitableUsers] = useState<any[]>([]);
  const [inviteModalVisible, setInviteModalVisible] = useState(false);
  const [memberForm] = Form.useForm();
  
  // 新增：状态管理
  const [isProjectCreated, setIsProjectCreated] = useState(false);
  const [isDataSourceCreated, setIsDataSourceCreated] = useState(false);
  const [originalProjectData, setOriginalProjectData] = useState<any>(null);
  const [originalDataSourceConfig, setOriginalDataSourceConfig] = useState<any>(null);
  
  const navigate = useNavigate();
  
  // 检测项目信息是否发生变更
  const hasProjectDataChanged = () => {
    if (!originalProjectData || !isProjectCreated) return false;
    const currentData = form.getFieldsValue();
    return (
      currentData.name !== originalProjectData.name ||
      currentData.description !== originalProjectData.description ||
      currentData.visibility !== originalProjectData.visibility
    );
  };
  
  // 检测数据源配置是否发生变更
  const hasDataSourceConfigChanged = () => {
    if (!originalDataSourceConfig || !isDataSourceCreated) return false;
    const currentConfig = dataSourceForm.getFieldsValue();
    
    // 检查基本信息
    if (currentConfig.name !== originalDataSourceConfig.name ||
        currentConfig.description !== originalDataSourceConfig.description ||
        currentConfig.type !== originalDataSourceConfig.type) {
      return true;
    }
    
    // 检查连接配置（深度比较）
    const currentConnConfig = currentConfig.config || {};
    const originalConnConfig = originalDataSourceConfig.config || {};
    
    const configKeys = ['host', 'port', 'database', 'username', 'password', 'service_name', 'base_url'];
    for (const key of configKeys) {
      if (currentConnConfig[key] !== originalConnConfig[key]) {
        return true;
      }
    }
    
    return false;
  };
  
  // 清除表格缓存（当数据源配置变更时）
  const clearTableCache = () => {
    setCachedTableList([]);
    setSelectedTables([]);
    console.log('数据源配置已变更，清除表格缓存');
  };
  
  // 组件挂载时设置表单初始值
  useEffect(() => {
    if (dataSourceForm) {
      // 强制设置默认值
      dataSourceForm.setFieldsValue({
        name: '',
        description: '',
        type: 'mysql', // 确保类型字段被设置
        config: {
          host: 'localhost',
          port: 3306,
          database: '',  // 确保数据库名被明确设置为空字符串
          username: '',
          password: ''
        }
      });
      
      // 检查表单中的实际值，确认初始化成功
      setTimeout(() => {
        const initialConfig = dataSourceForm.getFieldValue('config');
        console.log('初始化后的配置:', initialConfig);
        if (initialConfig) {
          console.log('初始化后的数据库名:', initialConfig.database, '类型:', typeof initialConfig.database);
        }
      }, 100);
      
      // 确保类型状态与表单一致
      setSelectedDataSourceType('mysql');
    }
  }, [dataSourceForm]);
  
  // 根据数据源类型渲染不同的配置表单
  const renderDataSourceConfigFields = () => {
    switch (selectedDataSourceType) {
      case 'oracle':
        return (
          <>
            <Form.Item
              name={['config', 'host']}
              label={t('projectDetail:labels.hostAddress')}
              rules={[{ required: true, message: t('projectDetail:validation.hostRequired') }]}
            >
              <Input placeholder={t('projectDetail:placeholders.enterHostAddress')} />
            </Form.Item>
            <Form.Item
              name={['config', 'port']}
              label={t('projectDetail:labels.port')}
              rules={[{ required: true, message: t('projectDetail:validation.portRequired') }]}
              initialValue={1521}
            >
              <Input 
                type="number" 
                placeholder={t('projectDetail:placeholders.enterPort')} 
                onChange={(e) => {
                  // 确保端口是数字类型
                  const portValue = e.target.value ? parseInt(e.target.value, 10) : 0;
                  dataSourceForm.setFieldValue(['config', 'port'], portValue);
                }}
              />
            </Form.Item>
            <Form.Item
              name={['config', 'service_name']}
              label={t('projectDetail:labels.serviceName')}
              rules={[{ required: true, message: t('projectDetail:validation.serviceNameRequired') }]}
            >
              <Input placeholder={t('projectDetail:placeholders.enterServiceName')} />
            </Form.Item>
            <Form.Item
              name={['config', 'username']}
              label={t('projectDetail:labels.username')}
              rules={[{ required: true, message: t('projectDetail:validation.usernameRequired') }]}
            >
              <Input placeholder={t('projectDetail:placeholders.enterUsername')} />
            </Form.Item>
            <Form.Item
              name={['config', 'password']}
              label={t('projectDetail:labels.password')}
              rules={[{ required: true, message: t('projectDetail:validation.passwordRequired') }]}
            >
              <Input.Password placeholder={t('projectDetail:placeholders.enterPassword')} />
            </Form.Item>
          </>
        );
        
      case 'mysql':
      case 'postgresql':
      case 'mssql':
        return (
          <>
            <Form.Item
              name={['config', 'host']}
              label={t('projectDetail:labels.hostAddress')}
              rules={[{ required: true, message: t('projectDetail:validation.hostRequired') }]}
            >
              <Input placeholder={t('projectDetail:placeholders.enterHostAddress')} />
            </Form.Item>
            <Form.Item
              name={['config', 'port']}
              label={t('projectDetail:labels.port')}
              rules={[{ required: true, message: t('projectDetail:validation.portRequired') }]}
              initialValue={3306}
            >
              <Input 
                type="number" 
                placeholder={t('project:wizard.placeholders.mysqlPort')} 
                onChange={(e) => {
                  // 确保端口是数字类型
                  const portValue = e.target.value ? parseInt(e.target.value, 10) : 0;
                  dataSourceForm.setFieldValue(['config', 'port'], portValue);
                }}
              />
            </Form.Item>
            <Form.Item
              name={['config', 'database']}
              label={t('projectDetail:labels.databaseName')}
              rules={[{ required: true, message: t('projectDetail:validation.databaseRequired') }]}
            >
              <Input 
                placeholder={t('project:wizard.placeholders.databaseExample')} 
                onChange={(e) => {
                  // 确保数据库名不为空且进行直接赋值
                  const dbValue = e.target.value || '';
                  console.log('数据库名onChange:', dbValue, '值长度:', dbValue.length);
                  
                  // 强制直接设置字段值，不使用数组路径
                  const currentConfig = dataSourceForm.getFieldValue('config') || {};
                  currentConfig.database = dbValue;
                  dataSourceForm.setFieldsValue({ config: currentConfig });
                  
                  // 直接打印整个config对象和database字段
                  console.log('设置后的config对象:', dataSourceForm.getFieldValue('config'));
                  console.log('设置后的database:', dataSourceForm.getFieldValue(['config', 'database']));
                }}
              />
            </Form.Item>
            <Form.Item
              name={['config', 'username']}
              label={t('projectDetail:labels.username')}
              rules={[{ required: true, message: t('projectDetail:validation.usernameRequired') }]}
            >
              <Input 
                placeholder={t('project:wizard.placeholders.mysqlUsername')} 
                onChange={(e) => {
                  // 确保用户名不为空
                  const usernameValue = e.target.value || '';
                  console.log('用户名onChange:', usernameValue, '值长度:', usernameValue.length);
                  
                  // 强制更新表单字段值
                  dataSourceForm.setFieldValue(['config', 'username'], usernameValue);
                  
                  // 检查表单中的实际值，确认设置成功
                  setTimeout(() => {
                    const currentValue = dataSourceForm.getFieldValue(['config', 'username']);
                    console.log('设置后的用户名:', currentValue, '类型:', typeof currentValue);
                  }, 100);
                }}
              />
            </Form.Item>
            <Form.Item
              name={['config', 'password']}
              label={t('projectDetail:labels.password')}
              rules={[{ required: true, message: t('projectDetail:validation.passwordRequired') }]}
            >
              <Input.Password placeholder={t('projectDetail:placeholders.enterPassword')} />
            </Form.Item>
          </>
        );
        
      case 'http_api':
        return (
          <>
            <Form.Item
              name={['config', 'base_url']}
              label={t('projectDetail:labels.baseUrl')}
              rules={[{ required: true, message: t('projectDetail:validation.baseUrlRequired') }]}
            >
              <Input placeholder={t('projectDetail:placeholders.enterBaseUrl')} />
            </Form.Item>
            <Form.Item
              name={['config', 'headers']}
              label={t('projectDetail:labels.requestHeaders')}
              tooltip={t('projectDetail:placeholders.enterRequestHeaders')}
            >
              <TextArea
                placeholder={t('projectDetail:placeholders.requestHeadersExample')}
                rows={4}
              />
            </Form.Item>
          </>
        );
        
      default:
        return null;
    }
  };
  
  // 第一步：创建或更新项目
  const handleCreateProject = async () => {
    try {
      await form.validateFields();
      setLoading(true);
      
      const values = form.getFieldsValue();
      
      // 如果项目已创建且数据有变更，需要更新项目
      if (isProjectCreated && hasProjectDataChanged()) {
        console.log('检测到项目信息变更，需要更新项目');
        // 这里需要调用更新项目的API（暂时先提示用户）
        message.warning(t('project:wizard.messages.projectChangeNotSupported'));
        setLoading(false);
        return;
      }
      
      // 如果项目已创建且数据未变更，直接进入下一步
      if (isProjectCreated && !hasProjectDataChanged()) {
        console.log('项目已创建且无变更，直接进入下一步');
        setCurrentStep(1);
        setLoading(false);
        return;
      }
      
      // 创建新项目
      const response = await projectWizardApi.createProject(values);
      
      if (response.data.success) {
        message.success(t('project:wizard.messages.projectCreated'));
        setProjectId(response.data.project_id);
        setIsProjectCreated(true);
        setOriginalProjectData({ ...values });
        
        // 如果是私有项目，获取可邀请用户列表
        if (values.visibility === 'PRIVATE') {
          await fetchInvitableUsers(response.data.project_id);
        }
        
        setCurrentStep(1);
      } else {
        // 处理错误
        if (response.data.message && response.data.message.includes('项目名称已存在')) {
          message.error(t('project:wizard.messages.projectNameExists'));
          // 自动聚焦到项目名称输入框
          form.getFieldInstance('name')?.focus();
        } else {
          message.error(t('project:wizard.messages.projectCreateFailed') + '：' + response.data.message);
        }
      }
    } catch (error) {
      console.error('创建项目失败', error);
      message.error(t('project:wizard.messages.projectCreateFailed'));
    } finally {
      setLoading(false);
    }
  };
  
  // 获取可邀请用户列表
  const fetchInvitableUsers = async (pid: string) => {
    try {
      const response = await projectMemberApi.getInvitableUsers(pid);
      setInvitableUsers(response.data || []);
    } catch (error) {
      console.error('获取可邀请用户失败:', error);
    }
  };
  
  // 添加项目成员
  const handleAddMember = async (values: any) => {
    if (!projectId) return;
    
    try {
      await projectMemberApi.inviteMember(projectId, {
        user_id: values.user_id,
        project_role_id: values.project_role_id || 3 // 默认为观察者
      });
      
      message.success(t('project:wizard.memberManagement.addSuccess'));
      setInviteModalVisible(false);
      memberForm.resetFields();
      
      // 更新成员列表
      const newMember = invitableUsers.find(user => user.id === values.user_id);
      if (newMember) {
        setMembers(prev => [...prev, {
          ...newMember,
          project_role_id: values.project_role_id || 3,
          role_name: values.project_role_id === 2 ? '协作者' : '观察者'
        }]);
        
        // 从可邀请列表中移除
        setInvitableUsers(prev => prev.filter(user => user.id !== values.user_id));
      }
    } catch (error: any) {
      message.error(error.response?.data?.detail || t('project:wizard.memberManagement.addFailed'));
    }
  };
  
  // 移除项目成员
  const handleRemoveMember = (userId: number) => {
    const removedMember = members.find(member => member.id === userId);
    if (removedMember) {
      setMembers(prev => prev.filter(member => member.id !== userId));
      setInvitableUsers(prev => [...prev, removedMember]);
      message.success(t('project:wizard.memberManagement.removeSuccess'));
    }
  };
  
  // 测试数据源连接
  const testConnection = async () => {
    try {
      // 从DOM直接获取输入值，而不是依赖表单状态
      const inputs = document.querySelectorAll('input');
      let host = '';
      let port = 3306;
      let dbName = '';
      let username = '';
      let password = '';
      let serviceName = '';
      
      // 遍历输入框获取值
      inputs.forEach((input) => {
        const placeholder = input.getAttribute('placeholder');
        if (placeholder === t('projectDetail:placeholders.enterHostAddress')) {
          host = (input as HTMLInputElement).value.trim() || 'localhost';
        } else if (placeholder === t('projectDetail:placeholders.enterPort')) {
          // 特殊处理Oracle端口
          const portVal = (input as HTMLInputElement).value;
          port = portVal ? parseInt(portVal, 10) : 1521;
        } else if (placeholder && placeholder.includes('3306')) {
          // 处理MySQL/PostgreSQL/MSSQL端口
          const portVal = (input as HTMLInputElement).value;
          port = portVal ? parseInt(portVal, 10) : 3306;
        } else if (placeholder === t('project:wizard.placeholders.databaseExample')) {
          dbName = (input as HTMLInputElement).value.trim();
        } else if (placeholder === t('projectDetail:placeholders.enterServiceName')) {
          serviceName = (input as HTMLInputElement).value.trim();
        } else if (placeholder === t('projectDetail:placeholders.enterUsername')) {
          username = (input as HTMLInputElement).value.trim();
        } else if (placeholder === t('project:wizard.placeholders.mysqlUsername')) {
          username = (input as HTMLInputElement).value.trim();
        } else if (input.type === 'password' || placeholder === t('projectDetail:placeholders.enterPassword')) {
          password = (input as HTMLInputElement).value;
        }
      });
      
      console.log('从DOM获取的值:', { 
        host, 
        port, 
        dbName, 
        serviceName,
        username, 
        password: '******',
        type: selectedDataSourceType 
      });
      
      // 构建请求数据
      let requestData;
      
      // 根据数据源类型构建不同的请求数据
      if (selectedDataSourceType === 'oracle') {
        requestData = {
          type: 'oracle',
          config: {
            host: host || 'localhost',
            port: port || 1521,
            service_name: serviceName,
            username: username,
            password: password
          }
        };
        
        // 验证Oracle必填字段
        if (!host) {
          message.warning(t('projectDetail:validation.hostRequired'));
          return;
        }
        
        if (!port) {
          message.warning(t('projectDetail:validation.portRequired'));
          return;
        }
        
        if (!serviceName) {
          message.warning(t('projectDetail:validation.serviceNameRequired'));
          return;
        }
        
        if (!username) {
          message.warning(t('projectDetail:validation.usernameRequired'));
          return;
        }
        
        if (!password) {
          message.warning(t('projectDetail:validation.passwordRequired'));
          return;
        }
      } else if (selectedDataSourceType === 'http_api') {
        requestData = {
          type: 'http_api',
          config: {
            base_url: host || '',
            headers: '{}'
          }
        };
        
        if (!host) {
          message.warning(t('projectDetail:validation.baseUrlRequired'));
          return;
        }
      } else {
        // MySQL/PostgreSQL/MSSQL
        requestData = {
          type: selectedDataSourceType,
          config: {
            host: host || 'localhost',
            port: port,
            database: dbName,
            username: username,
            password: password
          }
        };
        
        // 验证必填字段
        if (!host) {
          message.warning(t('projectDetail:validation.hostRequired'));
          return;
        }
        
        if (!port) {
          message.warning(t('projectDetail:validation.portRequired'));
          return;
        }
        
        if (!dbName) {
          message.warning(t('projectDetail:validation.databaseRequired'));
          return;
        }
        
        if (!username) {
          message.warning(t('projectDetail:validation.usernameRequired'));
          return;
        }
        
        if (!password) {
          message.warning(t('projectDetail:validation.passwordRequired'));
          return;
        }
      }
      
      setConnectionTesting(true);
      
      console.log('发送测试连接请求:', requestData);
      
      const response = await projectWizardApi.testConnection(requestData);
      
      if (response.data.success) {
        setConnectionSuccess(true);
        message.success(t('projectDetail:messages.connectionSuccess'));
        // 表格列表将在下一步自动获取，这里不需要设置
      } else {
        setConnectionSuccess(false);
        message.error(t('projectDetail:messages.connectionFailed') + ': ' + response.data.message);
      }
    } catch (error) {
      console.error('测试连接失败', error);
      message.error(t('projectDetail:messages.connectionFailed'));
      setConnectionSuccess(false);
    } finally {
      setConnectionTesting(false);
    }
  };
  
  // 第二步：添加或更新数据源
  const handleAddDataSource = async () => {
    if (!connectionSuccess) {
      message.warning(t('project:wizard.messages.testConnectionFirst'));
      return;
    }
    
    try {
      // 确保表单中有type字段
      if (!dataSourceForm.getFieldValue('type')) {
        dataSourceForm.setFieldValue('type', selectedDataSourceType);
      }
      
      // 获取表单中的所有值
      const formValues = await dataSourceForm.validateFields();
      
      // 如果数据源已创建，直接进入下一步（简化逻辑）
      if (isDataSourceCreated) {
        console.log('数据源已创建，直接进入下一步');
        setCurrentStep(2);
        return;
      }
      console.log('添加数据源表单值:', formValues);
      
      // 获取DOM直接的值，确保数据正确
      const inputs = document.querySelectorAll('input');
      let host = '';
      let port = 3306;
      let dbName = '';
      let username = '';
      let password = '';
      let serviceName = '';
      
      // 遍历输入框获取值
      inputs.forEach((input) => {
        const placeholder = input.getAttribute('placeholder');
        if (placeholder === t('projectDetail:placeholders.enterHostAddress')) {
          host = (input as HTMLInputElement).value.trim() || 'localhost';
        } else if (placeholder === t('projectDetail:placeholders.enterPort')) {
          // 特殊处理Oracle端口
          const portVal = (input as HTMLInputElement).value;
          port = portVal ? parseInt(portVal, 10) : 1521;
        } else if (placeholder && placeholder.includes('3306')) {
          // 处理MySQL/PostgreSQL/MSSQL端口
          const portVal = (input as HTMLInputElement).value;
          port = portVal ? parseInt(portVal, 10) : 3306;
        } else if (placeholder === t('project:wizard.placeholders.databaseExample')) {
          dbName = (input as HTMLInputElement).value.trim();
        } else if (placeholder === t('projectDetail:placeholders.enterServiceName')) {
          serviceName = (input as HTMLInputElement).value.trim();
        } else if (placeholder === t('projectDetail:placeholders.enterUsername')) {
          username = (input as HTMLInputElement).value.trim();
        } else if (placeholder === t('project:wizard.placeholders.mysqlUsername')) {
          username = (input as HTMLInputElement).value.trim();
        } else if (input.type === 'password' || placeholder === t('projectDetail:placeholders.enterPassword')) {
          password = (input as HTMLInputElement).value;
        }
      });
      
      console.log('从DOM获取的值:', { 
        host, 
        port, 
        dbName, 
        serviceName,
        username, 
        password: '******', 
        type: selectedDataSourceType 
      });
      
      // 构建请求数据
      let requestData;
      
      // 根据数据源类型构建不同的请求数据
      if (selectedDataSourceType === 'oracle') {
        requestData = {
          name: formValues.name || `数据源-${new Date().toLocaleString()}`,
          description: formValues.description || '',
          type: 'oracle',
          config: {
            host: host || 'localhost',
            port: port || 1521,
            service_name: serviceName,
            username: username,
            password: password
          },
          project_id: projectId
        };
        
        // 验证Oracle必填字段
        if (!host) {
          message.error(t('projectDetail:validation.hostRequired'));
          return;
        }
        
        if (!port) {
          message.error(t('projectDetail:validation.portRequired'));
          return;
        }
        
        if (!serviceName) {
          message.error(t('projectDetail:validation.serviceNameRequired'));
          return;
        }
        
        if (!username) {
          message.error(t('projectDetail:validation.usernameRequired'));
          return;
        }
        
        if (!password) {
          message.error(t('projectDetail:validation.passwordRequired'));
          return;
        }
      } else if (selectedDataSourceType === 'http_api') {
        requestData = {
          name: formValues.name || `数据源-${new Date().toLocaleString()}`,
          description: formValues.description || '',
          type: 'http_api',
          config: {
            base_url: host || '',
            headers: '{}'
          },
          project_id: projectId
        };
        
        if (!host) {
          message.error(t('projectDetail:validation.baseUrlRequired'));
          return;
        }
      } else {
        // MySQL/PostgreSQL/MSSQL
        requestData = {
          name: formValues.name || `数据源-${new Date().toLocaleString()}`,
          description: formValues.description || '',
          type: selectedDataSourceType,
          config: {
            host: host || 'localhost',
            port: port,
            database: dbName,
            username: username,
            password: password
          },
          project_id: projectId
        };
        
        // 验证必填字段
        if (!host) {
          message.error(t('projectDetail:validation.hostRequired'));
          return;
        }
        
        if (!port) {
          message.error(t('projectDetail:validation.portRequired'));
          return;
        }
        
        if (!dbName) {
          message.error(t('projectDetail:validation.databaseRequired'));
          return;
        }
        
        if (!username) {
          message.error(t('projectDetail:validation.usernameRequired'));
          return;
        }
        
        if (!password) {
          message.error(t('projectDetail:validation.passwordRequired'));
          return;
        }
      }
      
      setLoading(true);
      
      console.log('添加数据源最终请求参数:', requestData);
      
      const response = await projectWizardApi.addDataSource(requestData);
      
      if (response.data.success) {
        message.success(t('projectDetail:messages.dataSourceAddSuccess'));
        setDataSourceId(response.data.datasource_id);
        setIsDataSourceCreated(true);
        
        // 保存原始数据源配置用于变更检测
        setOriginalDataSourceConfig({
          name: formValues.name,
          description: formValues.description,
          type: selectedDataSourceType,
          config: requestData.config
        });
        
        // 优化用户体验：不在这里获取表格列表，直接跳转到下一步
        // 表格列表获取将在下一页进行，并显示加载状态
        setCurrentStep(2);
      } else {
        message.error(t('projectDetail:messages.dataSourceAddFailed') + ': ' + response.data.message);
      }
    } catch (error) {
      console.error('添加数据源失败', error);
      message.error(t('projectDetail:messages.dataSourceAddFailed'));
    } finally {
      setLoading(false);
    }
  };
  
  // 表格相关逻辑已移至 TableSelectionStep 组件
  
  // 渲染步骤内容
  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <Card title={t('project:wizard.step1')} variant="outlined">
            <Form
              form={form}
              layout="vertical"
              initialValues={{ name: '', description: '', visibility: 'PRIVATE' }}
            >
              <Form.Item
                name="name"
                label={t('project:wizard.form.projectName')}
                rules={[{ required: true, message: t('project:wizard.form.projectNameRequired') }]}
              >
                <Input placeholder={t('project:wizard.form.projectNamePlaceholder')} />
              </Form.Item>
              <Form.Item
                name="description"
                label={t('project:wizard.form.projectDescription')}
              >
                <TextArea 
                  placeholder={t('project:wizard.form.projectDescriptionPlaceholder')} 
                  rows={4} 
                />
              </Form.Item>
              <Form.Item
                name="visibility"
                label={t('project:wizard.form.projectVisibility')}
                rules={[{ required: true, message: t('project:wizard.form.projectVisibilityRequired') }]}
              >
                <Radio.Group>
                  <Radio value="PRIVATE">{t('project:wizard.form.visibilityPrivate')}</Radio>
                  <Radio value="PUBLIC">{t('project:wizard.form.visibilityPublic')}</Radio>
                </Radio.Group>
              </Form.Item>
              
              {/* 如果选择私有项目且项目已创建，显示成员管理 */}
              {form.getFieldValue('visibility') === 'PRIVATE' && projectId && (
                <Card title={t('project:wizard.memberManagement.title')} size="small" style={{ marginBottom: 16 }}>
                  <div style={{ marginBottom: 16 }}>
                    <Button
                      type="primary"
                      icon={<UserAddOutlined />}
                      onClick={() => setInviteModalVisible(true)}
                      disabled={invitableUsers.length === 0}
                    >
                      {t('project:wizard.memberManagement.addMember')}
                    </Button>
                    {invitableUsers.length === 0 && (
                      <span style={{ marginLeft: 8, color: '#999' }}>
                        {t('project:wizard.memberManagement.noInvitableUsers')}
                      </span>
                    )}
                  </div>
                  
                  {members.length > 0 && (
                    <Table
                      size="small"
                      dataSource={members}
                      rowKey="id"
                      pagination={false}
                      columns={[
                        {
                          title: t('project:wizard.memberManagement.username'),
                          dataIndex: 'username',
                          key: 'username'
                        },
                        {
                          title: t('common:labels.email'),
                          dataIndex: 'email',
                          key: 'email'
                        },
                        {
                          title: t('common:labels.role'),
                          dataIndex: 'role_name',
                          key: 'role_name',
                          render: (roleName: string) => (
                            <Tag color={roleName === t('project:wizard.memberManagement.collaborator') ? 'blue' : 'green'}>
                              {roleName}
                            </Tag>
                          )
                        },
                        {
                          title: t('common:labels.actions'),
                          key: 'action',
                          render: (_: any, record: any) => (
                            <Button
                              type="link"
                              danger
                              size="small"
                              icon={<DeleteOutlined />}
                              onClick={() => handleRemoveMember(record.id)}
                            >
                              {t('common:buttons.remove')}
                            </Button>
                          )
                        }
                      ]}
                    />
                  )}
                </Card>
              )}
              
              <Form.Item>
                <Button 
                  type="primary" 
                  onClick={handleCreateProject}
                  loading={loading}
                >
                  {t('project:wizard.next')}
                </Button>
              </Form.Item>
            </Form>
          </Card>
        );
        
      case 1:
        return (
          <Card title={t('projectDetail:buttons.addDataSource')} variant="outlined">
            <Form
              form={dataSourceForm}
              layout="vertical"
              initialValues={{ 
                name: '', 
                description: '', 
                type: 'mysql',
                config: {
                  host: 'localhost',
                  port: 3306,
                  database: '',  // 显式设置为空字符串，而不是undefined
                  username: '',  // 显式设置为空字符串，而不是undefined
                  password: ''   // 显式设置为空字符串，而不是undefined
                }
              }}
              onValuesChange={(changedValues) => {
                if (changedValues.type) {
                  setSelectedDataSourceType(changedValues.type);
                  setConnectionSuccess(false);
                  
                  // 如果数据源已创建，检测到类型变更时清除缓存
                  if (isDataSourceCreated) {
                    clearTableCache();
                  }
                  
                  // 获取当前用户名值，避免切换类型时丢失
                  const currentUsername = dataSourceForm.getFieldValue(['config', 'username']) || '';
                  console.log('切换数据源类型时保存当前用户名:', currentUsername);
                  
                  // 根据选择的数据源类型设置默认配置
                  if (changedValues.type === 'mysql') {
                    dataSourceForm.setFieldsValue({
                      config: {
                        host: 'localhost',
                        port: 3306,
                        database: '',
                        username: currentUsername || '',  // 保留当前用户名
                        password: ''
                      }
                    });
                  } else if (changedValues.type === 'postgresql') {
                    dataSourceForm.setFieldsValue({
                      config: {
                        host: 'localhost',
                        port: 5432,
                        database: '',
                        username: currentUsername || '',  // 保留当前用户名
                        password: ''
                      }
                    });
                  } else if (changedValues.type === 'oracle') {
                    dataSourceForm.setFieldsValue({
                      config: {
                        host: 'localhost',
                        port: 1521,
                        service_name: '',
                        username: '',
                        password: ''
                      }
                    });
                  } else if (changedValues.type === 'mssql') {
                    dataSourceForm.setFieldsValue({
                      config: {
                        host: 'localhost',
                        port: 1433,
                        database: '',
                        username: '',
                        password: ''
                      }
                    });
                  } else if (changedValues.type === 'http_api') {
                    dataSourceForm.setFieldsValue({
                      config: {
                        base_url: '',
                        headers: '{}'
                      }
                    });
                  }
                }
                
                // 添加对config对象变更的特殊处理
                if (changedValues.config) {
                  console.log('config对象变更:', changedValues.config);
                  
                  // 任何配置变更都重置连接状态（无论数据源是否已创建）
                  setConnectionSuccess(false);
                  
                  // 如果数据源已创建，清除缓存
                  if (isDataSourceCreated) {
                    clearTableCache();
                    console.log('检测到数据源配置变更，已清除缓存');
                  }
                  
                  // 确保database和username字段有值
                  if (changedValues.config.database !== undefined) {
                    console.log('数据库名变更为:', changedValues.config.database);
                  }
                  
                  if (changedValues.config.username !== undefined) {
                    console.log('用户名变更为:', changedValues.config.username);
                  }
                }
                
                // 检测基本信息变更
                if (changedValues.name || changedValues.description) {
                  if (isDataSourceCreated) {
                    console.log('检测到数据源基本信息变更');
                  }
                }
              }}
            >
              <Form.Item
                name="name"
                label={t('projectDetail:labels.dataSourceName')}
                rules={[{ required: true, message: t('projectDetail:validation.dataSourceNameRequired') }]}
              >
                <Input placeholder={t('projectDetail:placeholders.enterDataSourceName')} />
              </Form.Item>
              <Form.Item
                name="description"
                label={t('projectDetail:labels.dataSourceDescription')}
              >
                <TextArea 
                  placeholder={t('projectDetail:placeholders.enterDataSourceDescription')} 
                  rows={2} 
                />
              </Form.Item>
              <Form.Item
                name="type"
                label={t('projectDetail:labels.dataSourceType')}
                rules={[{ required: true, message: t('projectDetail:validation.dataSourceTypeRequired') }]}
                initialValue={selectedDataSourceType}
              >
                <Select 
                  placeholder={t('projectDetail:placeholders.selectDataSourceType')}
                  onChange={(value) => {
                    setSelectedDataSourceType(value);
                    setConnectionSuccess(false);
                    
                    // 获取当前用户名值，避免切换类型时丢失
                    const currentUsername = dataSourceForm.getFieldValue(['config', 'username']) || '';
                    console.log('切换数据源类型时保存当前用户名:', currentUsername);
                    
                    // 根据选择的数据源类型设置默认配置
                    if (value === 'mysql') {
                      dataSourceForm.setFieldsValue({
                        config: {
                          host: 'localhost',
                          port: 3306,
                          database: '',
                          username: currentUsername || '',  // 保留当前用户名
                          password: ''
                        }
                      });
                    } else if (value === 'postgresql') {
                      dataSourceForm.setFieldsValue({
                        config: {
                          host: 'localhost',
                          port: 5432,
                          database: '',
                          username: currentUsername || '',  // 保留当前用户名
                          password: ''
                        }
                      });
                    } else if (value === 'oracle') {
                      dataSourceForm.setFieldsValue({
                        config: {
                          host: 'localhost',
                          port: 1521,
                          service_name: '',
                          username: '',
                          password: ''
                        }
                      });
                    } else if (value === 'mssql') {
                      dataSourceForm.setFieldsValue({
                        config: {
                          host: 'localhost',
                          port: 1433,
                          database: '',
                          username: '',
                          password: ''
                        }
                      });
                    } else if (value === 'http_api') {
                      dataSourceForm.setFieldsValue({
                        config: {
                          base_url: '',
                          headers: '{}'
                        }
                      });
                    }
                  }}
                >
                  {dataSourceTypes.map(type => (
                    <Option key={type.value} value={type.value}>{type.label}</Option>
                  ))}
                </Select>
              </Form.Item>
              
              <Divider orientation="left">{t('projectDetail:labels.connectionConfig')}</Divider>
              
              {renderDataSourceConfigFields()}
              
              <Row>
                <Col span={24} style={{ textAlign: 'left', marginBottom: 16 }}>
                  <Space>
                    <Button 
                      onClick={testConnection}
                      loading={connectionTesting}
                    >
                      {t('projectDetail:buttons.testConnection')}
                    </Button>
                    <Button 
                      onClick={() => {
                        // 重置表单为当前数据源类型的默认值
                        let defaultConfig = {};
                        
                        if (selectedDataSourceType === 'mysql') {
                          defaultConfig = {
                            host: 'localhost',
                            port: 3306,
                            database: '',
                            username: '',
                            password: ''
                          };
                        } else if (selectedDataSourceType === 'postgresql') {
                          defaultConfig = {
                            host: 'localhost',
                            port: 5432,
                            database: '',
                            username: '',
                            password: ''
                          };
                        } else if (selectedDataSourceType === 'oracle') {
                          defaultConfig = {
                            host: 'localhost',
                            port: 1521,
                            service_name: '',
                            username: '',
                            password: ''
                          };
                        } else if (selectedDataSourceType === 'mssql') {
                          defaultConfig = {
                            host: 'localhost',
                            port: 1433,
                            database: '',
                            username: '',
                            password: ''
                          };
                        } else if (selectedDataSourceType === 'http_api') {
                          defaultConfig = {
                            base_url: '',
                            headers: '{}'
                          };
                        }
                        
                        dataSourceForm.setFieldsValue({
                          config: defaultConfig
                        });
                        setConnectionSuccess(false);
                      }}
                    >
                      {t('common:buttons.reset')}
                    </Button>
                    {connectionSuccess && (
                      <span style={{ color: 'green' }}>
                        <CheckCircleOutlined /> {t('projectDetail:messages.connectionSuccess')}
                      </span>
                    )}
                  </Space>
                </Col>
              </Row>
              
              <Form.Item>
                <Space>
                  <Button onClick={() => setCurrentStep(0)}>
                    {t('common:buttons.previous')}
                  </Button>
                  <Button 
                    type="primary" 
                    onClick={handleAddDataSource}
                    loading={loading}
                    disabled={!connectionSuccess}
                  >
                    {t('common:buttons.next')}
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Card>
        );
        
      case 2:
        return (
          <TableSelectionStep 
            dataSourceId={dataSourceId}
            selectedTables={selectedTables}
            cachedTableList={cachedTableList}
            onTableSelectionChange={setSelectedTables}
            onTableListUpdate={setCachedTableList}
            onNext={() => setCurrentStep(3)}
          />
        );
        
      case 3:
        // 表描述步骤
        return (
          <TableDescriptionStep 
            projectId={projectId as string} 
            selectedTables={selectedTables} // 传递选定的表格列表
            onNext={() => {
              // 进入智能理解步骤
              setCurrentStep(4);
            }}
            onPrev={() => setCurrentStep(2)}
          />
        );

      case 4:
        // 智能理解步骤
        return (
          <ProjectIntelligenceWizard
            projectId={projectId as string}
            onComplete={() => {
              message.success(t('project:wizard.messages.projectCompleted'));
              
              // 重新加载项目列表
              try {
                // 发送事件通知App组件重新加载项目列表
                const reloadEvent = new CustomEvent('reloadProjects', {
                  detail: { projectId: projectId }
                });
                window.dispatchEvent(reloadEvent);
              } catch (error) {
                console.error('发送重新加载事件失败:', error);
              }
              
              navigate('/');
            }}
            onCancel={() => {
              // 跳过智能理解，直接完成项目创建
              message.success(t('project:wizard.messages.projectCompleted'));
              
              // 重新加载项目列表
              try {
                const reloadEvent = new CustomEvent('reloadProjects', {
                  detail: { projectId: projectId }
                });
                window.dispatchEvent(reloadEvent);
              } catch (error) {
                console.error('发送重新加载事件失败:', error);
              }
              
              navigate('/');
            }}
          />
        );
        
      default:
        return null;
    }
  };
  
  // 在数据库名检查后，继续添加其他字段的验证
  // 非空检查函数
  const isEmpty = (value: any) => {
    // 打印值的具体情况，便于调试
    console.log(`检查字段是否为空:`, value, `类型:`, typeof value, `值:`, String(value));
    
    // 处理undefined和null
    if (value === undefined || value === null) {
      return true;
    }
    
    // 处理空字符串
    if (typeof value === 'string') {
      return value.trim() === '';
    }
    
    // 处理数字0
    if (typeof value === 'number' && value === 0) {
      return true;
    }
    
    // 如果以上都不满足，则认为不为空
    return false;
  };
  
  return (
    <div className="project-wizard" style={{ 
      maxWidth: '1200px', 
      margin: '0 auto',
      minHeight: 'calc(100vh - 48px)',
      paddingBottom: '40px'
    }}>
      <Title level={2}>{t('project:wizard.title')}</Title>
      
      <Steps current={currentStep} style={{ marginBottom: 30 }}>
        <Step title={t('project:wizard.step1')} icon={<ProjectOutlined />} />
        <Step title={t('project:wizard.step2')} icon={<DatabaseOutlined />} />
        <Step title={t('project:wizard.step3')} icon={<TableOutlined />} />
        <Step title={t('project:wizard.step4')} icon={<FileTextOutlined />} />
        <Step title={t('project:wizard.step5')} icon={<CheckCircleOutlined />} />
      </Steps>
      
      {renderStepContent()}
      
      {/* 邀请成员模态框 */}
      <Modal
        title={t('project:wizard.memberManagement.addMember')}
        open={inviteModalVisible}
        onCancel={() => {
          setInviteModalVisible(false);
          memberForm.resetFields();
        }}
        footer={null}
      >
        <Form
          form={memberForm}
          layout="vertical"
          onFinish={handleAddMember}
        >
          <Form.Item
            name="user_id"
            label={t('project:wizard.memberManagement.selectUser')}
            rules={[{ required: true, message: t('project:wizard.memberManagement.selectUserRequired') }]}
          >
            <Select
              placeholder={t('project:wizard.memberManagement.selectUserPlaceholder')}
              showSearch
              optionFilterProp="children"
            >
              {invitableUsers.map(user => (
                <Select.Option key={user.id} value={user.id}>
                  {user.username} ({user.email})
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="project_role_id"
            label={t('project:wizard.memberManagement.projectRole')}
            rules={[{ required: true, message: t('project:wizard.memberManagement.projectRoleRequired') }]}
            initialValue={3}
          >
            <Select placeholder={t('project:wizard.memberManagement.selectRole')}>
              <Select.Option value={2}>{t('project:wizard.memberManagement.collaboratorDescription')}</Select.Option>
              <Select.Option value={3}>{t('project:wizard.memberManagement.observerDescription')}</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {t('common:buttons.add')}
              </Button>
              <Button onClick={() => {
                setInviteModalVisible(false);
                memberForm.resetFields();
              }}>
                {t('common:buttons.cancel')}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ProjectWizard; 