import React, { useEffect, useState } from 'react';
import { Form, Input, Button, Card, Select, Alert, Spin, Typography, Breadcrumb, Space, message, Table } from 'antd';
import { LeftOutlined, SaveOutlined, PlusOutlined, MinusCircleOutlined } from '@ant-design/icons';
import { useNavigate, useParams, Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { toolApi } from '../../services/api';
import { ToolType } from '../../types';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

interface ToolParameter {
  name: string;
  type: string;
  description?: string;
  required: boolean;
  default?: any;
}

const ToolEdit: React.FC = () => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEditing = Boolean(id);

  const [loading, setLoading] = useState<boolean>(false);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedProjectId, setSelectedProjectId] = useState<string>('');

  // 获取全局选择的项目ID
  useEffect(() => {
    // 从localStorage获取当前选中的项目ID
    const projectId = localStorage.getItem('selectedProjectId');
    
    if (projectId) {
      setSelectedProjectId(projectId);
    } else {
      setError(t('common:messages.selectProjectFirst'));
    }
  }, []);

  // 如果是编辑模式，加载工具数据
  useEffect(() => {
    if (isEditing && id) {
      const fetchToolData = async () => {
        setLoading(true);
        try {
          const response = await toolApi.getTool(id);
          
          let toolData = null;
          if (response?.data?.data) {
            toolData = response.data.data;
          } else {
            toolData = response.data;
          }
          
          if (toolData) {
            // 预处理参数，确保符合表单要求的格式
            let parameters = toolData.parameters;
            if (typeof parameters === 'string') {
              try {
                parameters = JSON.parse(parameters);
              } catch (e) {
                parameters = [];
              }
            }
            
            // 确保parameters是数组
            if (!Array.isArray(parameters)) {
              parameters = [];
            }
            
            form.setFieldsValue({
              ...toolData,
              parameters: parameters
            });
          } else {
            setError(t('tool:edit.fetchDataError'));
          }
        } catch (error) {
          console.error('获取工具详情失败:', error);
          setError(t('tool:edit.fetchDetailError'));
        } finally {
          setLoading(false);
        }
      };

      fetchToolData();
    }
  }, [id, isEditing, form]);

  // 表单提交处理
  const handleSubmit = async (values: any) => {
    setSubmitting(true);
    setError(null);
    
    try {
      // 确保parameters是一个有效的数组
      if (!Array.isArray(values.parameters)) {
        values.parameters = [];
      }
      
      // 处理每个参数，确保格式正确
      values.parameters = values.parameters.map((param: any) => ({
        name: param.name || '',
        type: param.type || 'string',
        description: param.description || '',
        required: typeof param.required === 'boolean' ? param.required : true,
        default: param.default
      }));
      
      if (isEditing) {
        // 编辑模式
        await toolApi.updateTool(id!, values);
        message.success(t('tool:edit.updateSuccess'));
      } else {
        // 创建模式 - 使用全局项目ID
        if (!selectedProjectId) {
          throw new Error(t('common:messages.selectProjectFirst'));
        }
        
        // 添加项目ID
        values.project_id = selectedProjectId;
        
        await toolApi.createTool(values);
        message.success(t('tool:edit.createSuccess'));
      }
      navigate('/tools');
    } catch (error: any) {
      console.error(isEditing ? '更新工具失败:' : '创建工具失败:', error);
      let errorMessage = isEditing ? t('tool:edit.updateError') : t('tool:edit.createError');
      
      if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error?.message) {
        errorMessage = error.message;
      }
      
      setError(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin tip={t('common:messages.loading')} size="large" />
      </div>
    );
  }

  return (
    <div>
      <Breadcrumb 
        style={{ marginBottom: '16px' }}
        items={[
          {
            title: <Link to="/">{t('common:navigation.dashboard')}</Link>
          },
          {
            title: <Link to="/tools">{t('common:navigation.tools')}</Link>
          },
          {
            title: isEditing ? t('tool:edit.editTool') : t('tool:edit.createTool')
          }
        ]}
      />
      
      <Card>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
          <Title level={2} style={{ margin: 0 }}>{isEditing ? t('tool:edit.editTool') : t('tool:edit.createTool')}</Title>
          <Button 
            icon={<LeftOutlined />} 
            onClick={() => navigate('/tools')}
          >
            {t('tool:edit.backToList')}
          </Button>
        </div>
        
        {error && (
          <Alert 
            message={t('common:messages.error')} 
            description={error} 
            type="error" 
            showIcon 
            style={{ marginBottom: '24px' }} 
          />
        )}
        
        {!selectedProjectId && !isEditing ? (
          <Alert
            message={t('tool:edit.selectProjectFirst')}
            description={t('tool:edit.selectProjectDescription')}
            type="warning"
            showIcon
          />
        ) : (
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            initialValues={{
              tool_type: ToolType.SQL,
              parameters: [{ name: '', type: 'string', description: '', required: true }],
            }}
          >
            {/* 工具名称 */}
            <Form.Item
              name="name"
              label={t('tool:form.name')}
              rules={[{ required: true, message: t('tool:form.nameRequired') }]}
            >
              <Input placeholder={t('tool:form.namePlaceholder')} />
            </Form.Item>
            
            {/* 工具描述 */}
            <Form.Item
              name="description"
              label={t('tool:form.description')}
            >
              <TextArea 
                placeholder={t('tool:form.descriptionPlaceholder')} 
                autoSize={{ minRows: 3, maxRows: 6 }}
              />
            </Form.Item>
            
            {/* 工具类型 */}
            <Form.Item
              name="tool_type"
              label={t('tool:form.type')}
              rules={[{ required: true, message: t('tool:form.typeRequired') }]}
            >
              <Select>
                <Option value={ToolType.SQL}>{t('tool:type.sql')}</Option>
                <Option value={ToolType.API}>{t('tool:type.api')}</Option>
                <Option value={ToolType.PYTHON}>{t('tool:type.python')}</Option>
                <Option value={ToolType.GRAPH}>{t('tool:type.graph')}</Option>
                <Option value={ToolType.VECTOR}>{t('tool:type.vector')}</Option>
              </Select>
            </Form.Item>
            
            {/* 工具模板 */}
            <Form.Item
              name="template"
              label={t('tool:form.template')}
              rules={[{ required: true, message: t('tool:form.templateRequired') }]}
            >
              <TextArea 
                placeholder={t('tool:form.templatePlaceholder')} 
                autoSize={{ minRows: 6, maxRows: 12 }}
              />
            </Form.Item>
            
            {/* 参数列表 */}
            <Form.List name="parameters">
              {(fields, { add, remove }) => (
                <>
                  <div style={{ marginBottom: '16px' }}>
                    <Title level={5}>{t('tool:form.parameters')}</Title>
                  </div>
                  
                  <Table
                    dataSource={fields.map(field => ({
                      ...field,
                      key: field.key
                    }))}
                    pagination={false}
                    size="small"
                    bordered
                    columns={[
                      {
                        title: t('tool:form.paramName'),
                        dataIndex: 'name',
                        key: 'name',
                        width: '15%',
                        render: (_: any, field: any) => (
                          <Form.Item
                            {...field}
                            name={[field.name, 'name']}
                            rules={[{ required: true, message: t('tool:form.paramNameRequired') }]}
                            noStyle
                          >
                            <Input placeholder={t('tool:form.paramNamePlaceholder')} />
                          </Form.Item>
                        ),
                      },
                      {
                        title: t('tool:form.paramType'),
                        dataIndex: 'type',
                        key: 'type',
                        width: '15%',
                        render: (_: any, field: any) => (
                          <Form.Item
                            {...field}
                            name={[field.name, 'type']}
                            rules={[{ required: true, message: t('tool:form.paramTypeRequired') }]}
                            noStyle
                          >
                            <Select style={{ width: '100%' }}>
                              <Option value="string">{t('tool:paramType.string')}</Option>
                              <Option value="number">{t('tool:paramType.number')}</Option>
                              <Option value="boolean">{t('tool:paramType.boolean')}</Option>
                              <Option value="array">{t('tool:paramType.array')}</Option>
                              <Option value="object">{t('tool:paramType.object')}</Option>
                            </Select>
                          </Form.Item>
                        ),
                      },
                      {
                        title: t('tool:form.paramDescription'),
                        dataIndex: 'description',
                        key: 'description',
                        width: '25%',
                        render: (_: any, field: any) => (
                          <Form.Item
                            {...field}
                            name={[field.name, 'description']}
                            noStyle
                          >
                            <Input placeholder={t('tool:form.paramDescriptionPlaceholder')} />
                          </Form.Item>
                        ),
                      },
                      {
                        title: t('tool:form.paramRequired'),
                        dataIndex: 'required',
                        key: 'required',
                        width: '10%',
                        render: (_: any, field: any) => (
                          <Form.Item
                            {...field}
                            name={[field.name, 'required']}
                            valuePropName="checked"
                            noStyle
                          >
                            <Select style={{ width: '100%' }}>
                              <Option value={true}>{t('common:form.yes')}</Option>
                              <Option value={false}>{t('common:form.no')}</Option>
                            </Select>
                          </Form.Item>
                        ),
                      },
                      {
                        title: t('tool:form.paramDefault'),
                        dataIndex: 'default',
                        key: 'default',
                        width: '20%',
                        render: (_: any, field: any) => (
                          <Form.Item
                            {...field}
                            name={[field.name, 'default']}
                            noStyle
                          >
                            <Input placeholder={t('tool:form.paramDefaultPlaceholder')} />
                          </Form.Item>
                        ),
                      },
                      {
                        title: t('common:table.actions'),
                        key: 'action',
                        width: '10%',
                        render: (_: any, field: any) => (
                          <Button
                            type="link"
                            danger
                            icon={<MinusCircleOutlined />}
                            onClick={() => remove(field.name)}
                          >
                            {t('common:button.delete')}
                          </Button>
                        ),
                      },
                    ]}
                  />
                  
                  <Form.Item style={{ marginTop: '16px' }}>
                    <Button 
                      type="dashed" 
                      onClick={() => add()} 
                      block 
                      icon={<PlusOutlined />}
                    >
                      {t('tool:form.addParameter')}
                    </Button>
                  </Form.Item>
                </>
              )}
            </Form.List>
            
            <Form.Item>
              <Space>
                <Button 
                  type="primary" 
                  htmlType="submit" 
                  icon={<SaveOutlined />} 
                  loading={submitting}
                >
                  {isEditing ? t('tool:edit.saveChanges') : t('tool:edit.createTool')}
                </Button>
                <Button onClick={() => navigate('/tools')}>
                  {t('common:button.cancel')}
                </Button>
              </Space>
            </Form.Item>
          </Form>
        )}
      </Card>
    </div>
  );
};

export default ToolEdit; 