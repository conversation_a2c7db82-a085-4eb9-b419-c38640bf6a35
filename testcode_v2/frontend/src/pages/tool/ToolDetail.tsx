import React, { useEffect, useState } from 'react';
import { Card, Typography, Button, Descriptions, Tabs, Divider, Space, Tag, Alert, Spin, Breadcrumb, Form, message, Popconfirm } from 'antd';
import { LeftOutlined, EditOutlined, DeleteOutlined, ExperimentOutlined } from '@ant-design/icons';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { toolApi, dataSourceApi } from '../../services/api';
import { Tool, ToolType, DataSource } from '../../types';
import ToolTestPanel from '../../components/ToolTestPanel';
import { getToolTypeDisplay, getToolTypeColor } from '../../utils/toolUtils';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

interface ToolParameter {
  name: string;
  type: string;
  description?: string;
  required?: boolean;
  default?: any;
}

const ToolDetail: React.FC = () => {
  const { t } = useTranslation();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  
  const [tool, setTool] = useState<Tool | null>(null);
  const [dataSource, setDataSource] = useState<DataSource | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('basic');
  
  // 获取工具详情
  useEffect(() => {
    const fetchToolData = async () => {
      if (!id) return;
      
      setLoading(true);
      try {
        const response = await toolApi.getTool(id);
        
        let toolData = null;
        if (response?.data?.data) {
          toolData = response.data.data;
        } else {
          toolData = response.data;
        }
        
        if (toolData) {
          setTool(toolData);
          
          // 获取关联的数据源信息
          if (toolData.data_source_id) {
            try {
              const dsResponse = await dataSourceApi.getDataSource(toolData.data_source_id);
              let dsData = null;
              if (dsResponse?.data?.data) {
                dsData = dsResponse.data.data;
              } else {
                dsData = dsResponse.data;
              }
              
              if (dsData) {
                setDataSource(dsData);
              }
            } catch (e) {
              console.error('Failed to fetch data source info:', e);
            }
          }
        } else {
          setError(t('tool:detail.fetchError'));
        }
      } catch (error) {
        console.error('Failed to fetch tool details:', error);
        setError(t('tool:detail.fetchDetailErrorRetry'));
      } finally {
        setLoading(false);
      }
    };

    fetchToolData();
  }, [id]);
  
  // 删除工具
  const handleDelete = async () => {
    if (!id) return;
    
    try {
      await toolApi.deleteTool(id);
      message.success(t('tool:detail.deleteSuccess'));
      navigate('/tools');
    } catch (error) {
      console.error('Failed to delete tool:', error);
      message.error(t('tool:detail.deleteErrorRetry'));
    }
  };
  
  // 确保参数是数组类型
  const getToolParameters = (): ToolParameter[] => {
    if (!tool) return [];
    
    let parameters = tool.parameters;
    console.log('原始参数:', parameters);
    
    // 如果参数是字符串，尝试解析
    if (typeof parameters === 'string') {
      try {
        parameters = JSON.parse(parameters);
        console.log('解析后的参数:', parameters);
      } catch (e) {
        console.error('解析参数失败:', e);
        return [];
      }
    }
    
    // 确保是数组
    if (!Array.isArray(parameters)) {
      console.warn('参数不是数组类型:', parameters);
      // 如果是对象，尝试提取values属性或转换为数组
      if (parameters && typeof parameters === 'object') {
        if ('values' in parameters && Array.isArray(parameters.values)) {
          console.log('使用values属性:', parameters.values);
          return parameters.values;
        } else {
          // 尝试将对象转换为数组
          try {
            const paramArray = Object.entries(parameters).map(([name, details]) => {
              if (typeof details === 'object' && details !== null) {
                return { name, ...details };
              } else {
                return { name, type: 'string', description: name };
              }
            });
            console.log('将对象转换为数组:', paramArray);
            return paramArray;
          } catch (e) {
            console.error('转换参数对象为数组失败:', e);
            return [];
          }
        }
      }
      return [];
    }
    
    return parameters;
  };
  
  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin tip={t('tool:detail.loading')} size="large" />
      </div>
    );
  }
  
  if (error || !tool) {
    return <Alert message={t('tool:detail.error')} description={error || t('tool:detail.fetchError')} type="error" showIcon />;
  }
  
  return (
    <div>
      <Breadcrumb 
        style={{ marginBottom: '16px' }}
        items={[
          {
            title: <Link to="/">{t('tool:detail.breadcrumb.home')}</Link>
          },
          {
            title: <Link to="/tools">{t('tool:detail.breadcrumb.toolManagement')}</Link>
          },
          {
            title: t('tool:detail.breadcrumb.toolDetail')
          }
        ]}
      />
      
      <Card>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
          <div>
            <Title level={2} style={{ margin: 0 }}>{tool.name}</Title>
            <Text type="secondary">{tool.description || t('tool:detail.noDescription')}</Text>
          </div>
          <Space>
            <Button 
              icon={<LeftOutlined />} 
              onClick={() => navigate('/tools')}
            >
              {t('tool:detail.backToList')}
            </Button>
            <Button 
              type="primary" 
              icon={<EditOutlined />}
              onClick={() => navigate(`/tools/${id}/edit`)}
            >
              {t('tool:detail.edit')}
            </Button>
            <Popconfirm
              title={t('tool:detail.confirmDelete')}
              description={t('tool:detail.confirmDeleteDescription')}
              onConfirm={handleDelete}
              okText={t('tool:detail.confirm')}
              cancelText={t('tool:detail.cancel')}
            >
              <Button 
                danger
                icon={<DeleteOutlined />}
              >
                {t('tool:detail.delete')}
              </Button>
            </Popconfirm>
          </Space>
        </div>
        
        <Tabs 
          activeKey={activeTab} 
          onChange={setActiveTab}
        >
          <TabPane tab={t('tool:detail.basicInfo')} key="basic">
            <Descriptions bordered column={1}>
              <Descriptions.Item label={t('tool:detail.toolId')}>{tool.id}</Descriptions.Item>
              <Descriptions.Item label={t('tool:detail.toolName')}>{tool.name}</Descriptions.Item>
              <Descriptions.Item label={t('tool:detail.toolDescription')}>{tool.description || t('tool:detail.none')}</Descriptions.Item>
              <Descriptions.Item label={t('tool:detail.toolType')}>
                <Tag color={getToolTypeColor(tool.tool_type)}>{getToolTypeDisplay(tool.tool_type)}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label={t('tool:detail.dataSource')}>
                {dataSource ? (
                  <Link to={`/datasources/${dataSource.id}`}>{dataSource.name}</Link>
                ) : (
                  `ID: ${tool.data_source_id}`
                )}
              </Descriptions.Item>
              <Descriptions.Item label={t('tool:detail.createdTime')}>{tool.created_at ? new Date(tool.created_at).toLocaleString() : '-'}</Descriptions.Item>
              <Descriptions.Item label={t('tool:detail.updatedTime')}>{tool.updated_at ? new Date(tool.updated_at).toLocaleString() : '-'}</Descriptions.Item>
            </Descriptions>
            
            <Divider orientation="left">{t('tool:detail.template')}</Divider>
            <Card size="small">
              <pre style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-all' }}>
                {tool.template}
              </pre>
            </Card>
            
            <Divider orientation="left">{t('tool:detail.parameters')}</Divider>
            {Array.isArray(getToolParameters()) && getToolParameters().length > 0 ? (
              <Descriptions bordered column={1}>
                {getToolParameters().map((param, index) => (
                  <Descriptions.Item key={index} label={`${t('tool:detail.parameter')} ${index + 1}: ${param.name || t('tool:detail.unnamed')}`}>
                    <p><strong>{t('tool:detail.type')}</strong> {param.type || t('tool:detail.unspecified')}</p>
                    <p><strong>{t('tool:detail.description')}</strong> {param.description || t('tool:detail.none')}</p>
                    <p><strong>{t('tool:detail.required')}</strong> {param.required ? t('tool:detail.yes') : t('tool:detail.no')}</p>
                    {param.default !== undefined && (
                      <p><strong>{t('tool:detail.defaultValue')}</strong> {String(param.default)}</p>
                    )}
                  </Descriptions.Item>
                ))}
              </Descriptions>
            ) : (
              <Alert message={t('tool:detail.noParameters')} type="info" showIcon />
            )}
          </TabPane>
          
          <TabPane tab={t('tool:detail.testTool')} key="test" style={{ minHeight: '400px' }}>
            <div style={{ marginBottom: '20px' }}>
              <Alert 
                message={t('tool:test.title')} 
                description={t('tool:test.description')} 
                type="info" 
                showIcon 
                icon={<ExperimentOutlined />}
              />
            </div>
            <ToolTestPanel 
              toolId={tool.id} 
              toolParameters={getToolParameters()}
              toolType={tool.tool_type} 
            />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default ToolDetail; 