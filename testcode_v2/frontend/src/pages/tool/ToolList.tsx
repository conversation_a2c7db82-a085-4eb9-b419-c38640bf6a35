import React, { useEffect, useState, useRef } from 'react';
import { Typography, Table, Alert, Spin, Empty, Breadcrumb, Button, Tag, Space, Modal, message } from 'antd';
import { Link, useNavigate } from 'react-router-dom';
import { PlusOutlined, EditOutlined, DeleteOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { toolApi } from '../../services/api';
import { getToolTypeDisplay, getToolTypeColor } from '../../utils/toolUtils';

const { Title } = Typography;
const { confirm } = Modal;

const ToolList: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [tools, setTools] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedProjectId, setSelectedProjectId] = useState<string>("");
  
  // 使用useRef防止重复调用
  const fetchingRef = useRef(false);

  useEffect(() => {
    // 从localStorage获取当前选中的项目ID
    const projectId = localStorage.getItem('selectedProjectId');
    
    if (projectId) {
      setSelectedProjectId(projectId);
      fetchTools(projectId);
    } else {
      setError(t('common:messages.selectProjectFirst'));
      setLoading(false);
    }
    
    // 清理函数
    return () => {
      fetchingRef.current = false;
    };
  }, []); 
  
  const fetchingToolsRef = useRef(false);
  
  const fetchTools = async (projectId: string) => {
    // 防止重复请求
    if (fetchingToolsRef.current) return;
    fetchingToolsRef.current = true;
    
    try {
      setLoading(true);
      // 使用project_id参数
      const response = await toolApi.getTools({ project_id: projectId });
      console.log('工具响应:', response);
      
      if (response && response.data) {
        // 处理API响应的复杂结构
        if (response.data.data && response.data.data.items) {
          // 标准分页数据结构: { data: { items: [...], page_info: {...} } }
          setTools(response.data.data.items);
        } else if (response.data.items && Array.isArray(response.data.items)) {
          // 简化的分页数据结构: { items: [...], page_info: {...} }
          setTools(response.data.items);
        } else if (Array.isArray(response.data)) {
          // 直接是数组: [...]
          setTools(response.data);
        } else {
          console.error("工具列表响应格式错误:", response.data);
          setTools([]);
        }
      } else {
        console.error("工具列表响应格式错误");
        setTools([]);
      }
    } catch (error) {
      console.error("获取工具列表失败", error);
      setTools([]);
    } finally {
      setLoading(false);
      fetchingToolsRef.current = false;
    }
  };

  // 处理删除工具
  const handleDeleteTool = (id: string, name: string) => {
    confirm({
      title: t('common:messages.deleteConfirm'),
      icon: <ExclamationCircleOutlined />,
      content: t('common:messages.deleteToolConfirm', { name }),
      okText: t('common:buttons.confirm'),
      okType: 'danger',
      cancelText: t('common:buttons.cancel'),
      onOk: async () => {
        try {
          await toolApi.deleteTool(id);
          message.success(t('common:messages.deleteSuccess'));
          // 刷新列表
          fetchTools(selectedProjectId);
        } catch (error) {
          console.error('删除工具失败:', error);
          message.error(t('common:messages.deleteError'));
        }
      }
    });
  };

  // 表格列定义
  const columns = [
    {
      title: t('common:labels.name'),
      width: 200,
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: any) => (
        <Link to={`/tools/${record.id}`}>{text}</Link>
      )
    },
    {
      title: t('common:labels.description'),
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      render: (text: string) => text || t('common:labels.none')
    },
    {
      title: t('common:labels.type'),
      width: 100,
      dataIndex: 'tool_type',
      key: 'tool_type',
      render: (text: string) => (
        <Tag color={getToolTypeColor(text)}>{getToolTypeDisplay(text)}</Tag>
      )
    },
    {
      title: t('common:labels.createdTime'),
      width: 200,
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text: string) => text ? new Date(text).toLocaleString() : '-'
    },
    {
      title: t('common:labels.actions'),
      key: 'action',
      render: (_: any, record: any) => (
        <Space size="middle">
          <Button 
            type="link" 
            onClick={() => navigate(`/tools/${record.id}`)}
          >
            {t('common:buttons.view')}
          </Button>
          <Button 
            type="link" 
            icon={<EditOutlined />} 
            onClick={() => navigate(`/tools/${record.id}/edit`)}
          >
            {t('common:buttons.edit')}
          </Button>
          <Button 
            type="link" 
            danger 
            icon={<DeleteOutlined />} 
            onClick={() => handleDeleteTool(record.id, record.name)}
          >
            {t('common:buttons.delete')}
          </Button>
        </Space>
      ),
    },
  ];

  if (loading && !tools.length) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin tip={t('common:messages.loading')} size="large" />
      </div>
    );
  }

  if (error) {
    return <Alert message={t('common:messages.error')} description={error} type="error" showIcon />;
  }

  return (
    <div>
      <Breadcrumb 
        style={{ marginBottom: '16px' }}
        items={[
          {
            title: <Link to="/">{t('common:navigation.dashboard')}</Link>
          },
          {
            title: t('common:navigation.tools')
          }
        ]}
      />
      
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
        <Title level={2} style={{ margin: 0 }}>{t('common:navigation.tools')}</Title>
        <Button 
          type="primary" 
          icon={<PlusOutlined />}
          onClick={() => navigate('/tools/create')}
          disabled={!selectedProjectId}
        >
          {t('common:buttons.create')}
        </Button>
      </div>
      
      {!selectedProjectId ? (
        <Empty 
          description={t('common:messages.selectProjectFirst')} 
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      ) : (
        <Table 
          columns={columns}
          dataSource={tools.map(tool => ({ ...tool, key: tool.id }))}
          rowKey="id"
          pagination={{ pageSize: 10 }}
          locale={{ emptyText: <Empty description={t('common:messages.noData')} /> }}
        />
      )}
    </div>
  );
};

export default ToolList; 