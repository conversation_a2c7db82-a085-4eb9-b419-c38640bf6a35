import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  Switch,
  message,
  Popconfirm,
  Space,
  Card,
  Tag,
  Typography,
  Tooltip
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UserOutlined,
  MailOutlined,
  LockOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone,
  ReloadOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { userApi } from '../../services/api';
import { User, CreateUserRequest, UpdateUserRequest } from '../../types';

const { Title } = Typography;

interface PageInfo {
  page: number;
  page_size: number;
  total: number;
  total_page: number;
}

interface UserFormData extends CreateUserRequest {}

const UserList: React.FC = () => {
  const { t } = useTranslation();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [pageInfo, setPageInfo] = useState<PageInfo>({
    page: 1,
    page_size: 10,
    total: 0,
    total_page: 0
  });
  
  // 模态框相关状态
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [modalType, setModalType] = useState<'create' | 'edit'>('create');
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [form] = Form.useForm();

  // 获取用户列表
  const fetchUsers = async (page = 1, pageSize = 10) => {
    setLoading(true);
    try {
      const response = await userApi.getSubAccounts({
        skip: (page - 1) * pageSize,
        limit: pageSize
      });
      
      if (response.data.code === 0) {
        setUsers(response.data.data.items);
        setPageInfo(response.data.data.page_info);
      } else {
        message.error(response.data.message || t('admin:users.fetchFailed'));
      }
    } catch (error: any) {
      console.error('获取用户列表失败:', error);
      message.error(t('admin:users.fetchFailed'));
    } finally {
      setLoading(false);
    }
  };

  // 页面加载时获取数据
  useEffect(() => {
    fetchUsers();
  }, []);

  // 打开创建用户模态框
  const handleCreate = () => {
    setModalType('create');
    setEditingUser(null);
    form.resetFields();
    form.setFieldsValue({ 
      is_active: true,  // 默认启用
      can_create_project: true  // 默认允许创建项目
    }); 
    setIsModalVisible(true);
  };

  // 打开编辑用户模态框
  const handleEdit = (user: User) => {
    setModalType('edit');
    setEditingUser(user);
    form.setFieldsValue({
      username: user.username,
      email: user.email,
      is_active: user.is_active,
      can_create_project: user.can_create_project,
      password: '' // 编辑时密码为空，表示不修改
    });
    setIsModalVisible(true);
  };

  // 提交表单
  const handleSubmit = async (values: UserFormData) => {
    try {
      if (modalType === 'create') {
        const response = await userApi.createSubAccount(values);
        if (response.data.code === 0) {
          message.success(t('admin:users.createSuccess'));
          fetchUsers(pageInfo.page, pageInfo.page_size);
        } else {
          message.error(response.data.message || t('admin:users.createFailed'));
        }
      } else if (editingUser) {
        // 编辑时，如果密码为空则不传递密码字段
        const updateData: any = {
          username: values.username,
          email: values.email,
          is_active: values.is_active,
          can_create_project: values.can_create_project
        };
        
        if (values.password && values.password.trim()) {
          updateData.password = values.password;
        }
        
        const response = await userApi.updateSubAccount(editingUser.id, updateData);
        if (response.data.code === 0) {
          message.success(t('admin:users.updateSuccess'));
          fetchUsers(pageInfo.page, pageInfo.page_size);
        } else {
          message.error(response.data.message || t('admin:users.updateFailed'));
        }
      }
      
      setIsModalVisible(false);
      form.resetFields();
    } catch (error: any) {
      console.error('操作失败:', error);
      message.error(t('admin:users.operationFailed'));
    }
  };

  // 删除用户
  const handleDelete = async (userId: number) => {
    try {
      const response = await userApi.deleteSubAccount(userId);
      if (response.data.code === 0) {
        message.success(t('admin:users.deleteSuccess'));
        fetchUsers(pageInfo.page, pageInfo.page_size);
      } else {
        message.error(response.data.message || t('admin:users.deleteFailed'));
      }
    } catch (error: any) {
      console.error('删除失败:', error);
      message.error(t('admin:users.deleteFailed'));
    }
  };

  // 切换用户状态
  const handleToggleStatus = async (userId: number) => {
    try {
      const response = await userApi.toggleSubAccountStatus(userId);
      if (response.data.code === 0) {
        message.success(response.data.message);
        fetchUsers(pageInfo.page, pageInfo.page_size);
      } else {
        message.error(response.data.message || t('admin:users.toggleStatusFailed'));
      }
    } catch (error: any) {
      console.error('状态切换失败:', error);
      message.error(t('admin:users.toggleStatusFailed'));
    }
  };

  // 表格列定义
  const columns = [
    {
      title: t('admin:users.username'),
      dataIndex: 'username',
      key: 'username',
      render: (text: string) => (
        <Space>
          <UserOutlined />
          {text}
        </Space>
      )
    },
    {
      title: t('admin:users.email'),
      dataIndex: 'email',
      key: 'email',
      render: (text: string) => text ? (
        <Space>
          <MailOutlined />
          {text}
        </Space>
      ) : '-'
    },
    {
      title: t('admin:users.systemRole'),
      dataIndex: 'role_id',
      key: 'role_id',
      render: (roleId: number) => {
        const roleMap: { [key: number]: { name: string; color: string } } = {
          1: { name: t('admin:users.superAdmin'), color: 'red' },
          2: { name: t('admin:users.enterpriseAdmin'), color: 'blue' },
          3: { name: t('admin:users.regularUser'), color: 'green' }
        };
        const role = roleMap[roleId] || { name: t('admin:users.unknownRole'), color: 'default' };
        return <Tag color={role.color}>{role.name}</Tag>;
      }
    },
    {
      title: t('admin:users.status'),
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive: boolean, record: User) => (
        <Tooltip title={t('admin:users.toggleStatusTooltip', { action: isActive ? t('admin:users.disableUser') : t('admin:users.enableUser') })}>
          <Tag 
            color={isActive ? 'green' : 'red'}
            style={{ cursor: 'pointer' }}
            onClick={() => handleToggleStatus(record.id)}
          >
            {isActive ? t('admin:users.enabled') : t('admin:users.disabled')}
          </Tag>
        </Tooltip>
      )
    },
    {
      title: t('admin:users.projectPermission'),
      dataIndex: 'can_create_project',
      key: 'can_create_project',
      render: (canCreate: boolean) => (
        <Tag color={canCreate ? 'green' : 'orange'}>
          {canCreate ? t('admin:users.canCreate') : t('admin:users.cannotCreate')}
        </Tag>
      )
    },
    {
      title: t('admin:users.createdAt'),
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text: string) => new Date(text).toLocaleString()
    },
    {
      title: t('common:labels.actions'),
      key: 'action',
      render: (_: any, record: User) => (
        <Space>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            {t('common:buttons.edit')}
          </Button>
          <Popconfirm
            title={t('admin:users.deleteConfirmTitle')}
            description={t('admin:users.deleteConfirmDescription')}
            onConfirm={() => handleDelete(record.id)}
            okText={t('common:buttons.confirm')}
            cancelText={t('common:buttons.cancel')}
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              {t('common:buttons.delete')}
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={3} style={{ margin: 0 }}>{t('admin:users.subAccountManagement')}</Title>
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => fetchUsers(pageInfo.page, pageInfo.page_size)}
            >
              {t('admin:users.refresh')}
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreate}
            >
              {t('admin:users.createSubAccount')}
            </Button>
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={users}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pageInfo.page,
            pageSize: pageInfo.page_size,
            total: pageInfo.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => t('admin:users.paginationTotal', { start: range[0], end: range[1], total }),
            onChange: (page, pageSize) => {
              fetchUsers(page, pageSize);
            }
          }}
        />
      </Card>

      {/* 创建/编辑用户模态框 */}
      <Modal
        title={modalType === 'create' ? t('admin:users.createSubAccount') : t('admin:users.editSubAccount')}
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
        }}
        footer={null}
        width={500}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            label={t('admin:users.username')}
            name="username"
            rules={[
              { required: true, message: t('admin:users.usernameRequired') },
              { min: 3, message: t('admin:users.usernameMinLength') },
              { max: 20, message: t('admin:users.usernameMaxLength') },
              { pattern: /^[a-zA-Z0-9_]+$/, message: t('admin:users.usernamePattern') }
            ]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder={t('admin:users.usernameRequired')}
            />
          </Form.Item>

          <Form.Item
            label={t('admin:users.email')}
            name="email"
            rules={[
              { type: 'email', message: t('admin:users.emailInvalid') }
            ]}
          >
            <Input
              prefix={<MailOutlined />}
              placeholder={t('admin:users.email')}
            />
          </Form.Item>

          <Form.Item
            label={modalType === 'create' ? t('auth:login.password') : t('admin:users.newPassword')}
            name="password"
            rules={modalType === 'create' ? [
              { required: true, message: t('admin:users.passwordRequired') },
              { min: 6, message: t('admin:users.passwordMinLength') }
            ] : [
              { min: 6, message: t('admin:users.passwordMinLength') }
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder={modalType === 'create' ? t('admin:users.passwordRequired') : t('admin:users.passwordPlaceholder')}
              iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
            />
          </Form.Item>

          <Form.Item
            label={t('admin:users.accountStatus')}
            name="is_active"
            valuePropName="checked"
          >
            <Switch
              checkedChildren={t('admin:users.enable')}
              unCheckedChildren={t('admin:users.disable')}
            />
          </Form.Item>

          <Form.Item
            label={t('admin:users.projectPermission')}
            name="can_create_project"
            valuePropName="checked"
            tooltip={t('admin:users.projectPermissionTooltip')}
          >
            <Switch
              checkedChildren={t('admin:users.allow')}
              unCheckedChildren={t('admin:users.forbid')}
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button
                onClick={() => {
                  setIsModalVisible(false);
                  form.resetFields();
                }}
              >
                {t('common:buttons.cancel')}
              </Button>
              <Button type="primary" htmlType="submit">
                {modalType === 'create' ? t('common:buttons.create') : t('common:buttons.save')}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default UserList; 