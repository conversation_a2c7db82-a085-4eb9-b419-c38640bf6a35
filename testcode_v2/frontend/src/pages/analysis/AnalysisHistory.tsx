import React, { useState, useEffect } from 'react';
import { Card, Table, Button, Input, Space, Typography, Popconfirm, message, Alert } from 'antd';
import { SearchOutlined, DeleteOutlined, EyeOutlined, ReloadOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { analysisApi } from '../../services/api';
import { formatDateTime } from '../../utils/dateUtils';

const { Title } = Typography;

const AnalysisHistory: React.FC = () => {
  const navigate = useNavigate();
  
  const [loading, setLoading] = useState(false);
  const [analyses, setAnalyses] = useState<any[]>([]);
  const [totalRecords, setTotalRecords] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [selectedProjectId, setSelectedProjectId] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState('');

  // 获取全局选择的项目ID
  useEffect(() => {
    // 从localStorage获取当前选中的项目ID
    const projectId = localStorage.getItem('selectedProjectId');
    
    if (projectId) {
      setSelectedProjectId(projectId);
      // 加载该项目的分析记录
      fetchAnalyses(currentPage, pageSize, projectId);
    }
  }, [currentPage, pageSize]);

  // 获取分析记录
  const fetchAnalyses = async (page = 1, size = 10, projectId: string | null = null) => {
    if (!projectId) {
      return;
    }
    
    setLoading(true);
    try {
      const params: any = {
        skip: (page - 1) * size,
        limit: size
      };
      
      if (projectId) {
        params.project_id = projectId;
      }
      
      const response = await analysisApi.getAnalyses(params);
      if (response.data.code === 0) {
        const data = response.data.data;
        setAnalyses(data.items || []);
        setTotalRecords(data.page_info?.total || 0);
      } else {
        message.error('获取分析记录失败: ' + response.data.message);
      }
    } catch (error: any) {
      message.error('获取分析记录出错: ' + (error.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  // 处理页面变化
  const handlePageChange = (page: number, pageSize?: number) => {
    setCurrentPage(page);
    if (pageSize) setPageSize(pageSize);
  };

  // 处理搜索按钮点击
  const handleSearch = () => {
    // 在真实项目中，这里应该通过API进行搜索
    // 由于当前API不支持查询参数搜索，我们在前端过滤
    if (searchQuery) {
      const filteredAnalyses = analyses.filter(analysis => 
        analysis.query.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setAnalyses(filteredAnalyses);
    } else {
      fetchAnalyses(currentPage, pageSize, selectedProjectId);
    }
  };

  // 查看分析详情
  const viewAnalysis = (id: string) => {
    navigate(`/analysis/${id}`);
  };

  // 删除分析记录
  const deleteAnalysis = async (id: string) => {
    try {
      const response = await analysisApi.deleteAnalysis(id);
      if (response.data.code === 0) {
        message.success('分析记录删除成功');
        fetchAnalyses(currentPage, pageSize, selectedProjectId);
      } else {
        message.error('删除失败: ' + response.data.message);
      }
    } catch (error: any) {
      message.error('删除出错: ' + (error.message || '未知错误'));
    }
  };

  const columns = [
    {
      title: '分析内容',
      dataIndex: 'query',
      key: 'query',
      ellipsis: true,
      render: (text: string) => <span title={text}>{text}</span>
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      render: (text: string) => formatDateTime(text)
    },
    {
      title: '操作',
      key: 'action',
      width: 250,
      render: (_: any, record: any) => (
        <Space size="middle">
          <Button 
            type="link" 
            icon={<EyeOutlined />} 
            onClick={() => viewAnalysis(record.id)}
          >
            查看
          </Button>
          <Popconfirm
            title="确定要删除此分析记录吗？"
            onConfirm={() => deleteAnalysis(record.id)}
            okText="是"
            cancelText="否"
          >
            <Button 
              type="link" 
              danger 
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="analysis-history-container">
      <Card 
        title="分析历史记录"
        extra={
          <Button
            type="primary"
            icon={<SearchOutlined />}
            onClick={() => navigate('/analysis')}
          >
            新建分析
          </Button>
        }
      >
        {!selectedProjectId ? (
          <Alert
            message="请先选择项目"
            description="请在顶部导航栏选择一个项目，然后再查看分析历史记录"
            type="warning"
            showIcon
          />
        ) : (
          <>
            <div className="filter-section" style={{ marginBottom: 16 }}>
              <Space wrap>
                <Input
                  placeholder="搜索分析内容"
                  value={searchQuery}
                  onChange={e => setSearchQuery(e.target.value)}
                  style={{ width: 200 }}
                  onPressEnter={handleSearch}
                />
                <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                  搜索
                </Button>
                <Button 
                  icon={<ReloadOutlined />} 
                  onClick={() => {
                    setSearchQuery('');
                    fetchAnalyses(currentPage, pageSize, selectedProjectId);
                  }}
                >
                  重置
                </Button>
              </Space>
            </div>

            <Table
              columns={columns}
              dataSource={analyses}
              rowKey="id"
              loading={loading}
              pagination={{
                current: currentPage,
                pageSize: pageSize,
                total: totalRecords,
                onChange: handlePageChange,
                showSizeChanger: true,
                showTotal: total => `共 ${total} 条记录`,
              }}
            />
          </>
        )}
      </Card>
    </div>
  );
};

export default AnalysisHistory; 