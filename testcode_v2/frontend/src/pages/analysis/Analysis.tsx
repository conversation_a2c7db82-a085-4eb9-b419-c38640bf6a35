import React, { useState, useEffect, useRef, Fragment } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import {
  Button, Input, Select, Card, Row, Col, Typography, Space, Tag, Steps, Spin, Alert, Divider, 
  List, Table, Collapse, message, Empty, Upload, Radio, Checkbox, Skeleton, Switch, Tooltip, Badge,
  Modal, Form, Tabs, Descriptions, Timeline
} from 'antd';
import { SearchOutlined, HistoryOutlined, QuestionCircleOutlined, SendOutlined, CheckCircleOutlined, CloseCircleOutlined, LoadingOutlined, FileTextOutlined, AimOutlined, CodeOutlined, EyeOutlined, BulbOutlined, CheckOutlined } from '@ant-design/icons';
import ReactMarkdown from 'react-markdown';
import { useTranslation } from 'react-i18next';
import { projectApi, llmAnalysisApiExtended } from '../../services/api';
import { normalizeToolType } from '../../utils/toolUtils';
import ToolResultRenderer from '../../components/ToolResults';
import { JsonView } from 'react-json-view-lite';
import 'react-json-view-lite/dist/index.css';
import ReactECharts from 'echarts-for-react';
// @ts-ignore
import remarkGfm from 'remark-gfm';  // 添加 GFM 支持
// @ts-ignore
import rehypeRaw from 'rehype-raw';  // 添加 HTML 支持
import './AnalysisDetail.css';  // 引入共享的样式文件

const { Paragraph, Text } = Typography;
const { TextArea } = Input;

// ExecutionStatusTag 组件定义
const ExecutionStatusTag = ({ success }: { success?: boolean }) => {
  const { t } = useTranslation();
  if (success === undefined) return null;
  
  return (
    <Tag color={success ? 'success' : 'error'}>
      {success ? t('common:status.completed') : t('common:status.failed')}
    </Tag>
  );
};

const Analysis: React.FC = () => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const eventSourceRef = useRef<EventSource | null>(null);
  const timelineRef = useRef<HTMLDivElement>(null);
  
  const [loading, setLoading] = useState<boolean>(false);
  const [selectedProjectId, setSelectedProjectId] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [tools, setTools] = useState<any[]>([]);
  
  // 流式分析相关状态
  const [streaming, setStreaming] = useState<boolean>(false);
  const [currentStep, setCurrentStep] = useState<string>('');
  const [analysisSteps, setAnalysisSteps] = useState<any[]>([]);
  const [intentAnalysis, setIntentAnalysis] = useState<any>(null);
  const [toolResults, setToolResults] = useState<any[]>([]);
  const [finalReport, setFinalReport] = useState<string>('');
  const [clarificationRequest, setClarificationRequest] = useState<any>(null);
  const [showClarificationDialog, setShowClarificationDialog] = useState(false);
  const [clarificationAnswers, setClarificationAnswers] = useState<Record<string, any>>({});
  const [clarificationHistory, setClarificationHistory] = useState<any[]>([]); // 存储澄清历史
  const [analysisId, setAnalysisId] = useState<string>('');
  const [streamComplete, setStreamComplete] = useState<boolean>(false);
  
  // 新增：LLM分析相关状态
  const [mcpResults, setMcpResults] = useState<any>({});

  // 图表弹窗状态
  const [chartModalVisible, setChartModalVisible] = useState(false);
  const [chartData, setChartData] = useState<any>(null);
  const [chartDataList, setChartDataList] = useState<any[]>([]); // 保存所有生成的图表数据

  // 添加意图确认相关的状态
  const [intentConfirmation, setIntentConfirmation] = useState<any>(null);
  const [intentAdjustment, setIntentAdjustment] = useState<string>('');

  // 添加函数字符串转换功能
  const reviveEchartsFunctions = (obj: any): any => {
    if (Array.isArray(obj)) {
      return obj.map(reviveEchartsFunctions);
    } else if (obj && typeof obj === 'object') {
      // 检查是否是特殊的函数对象格式 {"__function__": "函数体"}
      if (obj.__function__ && typeof obj.__function__ === 'string') {
        try {
          // eslint-disable-next-line no-new-func
          return eval('(' + obj.__function__ + ')');
        } catch (error) {
          console.warn('Failed to convert __function__ object:', error);
          return obj; // 保持原始对象，避免破坏整个配置
        }
      }

      const result = { ...obj };
      for (const key in result) {
        if (
          typeof result[key] === 'string' &&
          result[key].trim().startsWith('function')
        ) {
          try {
            // eslint-disable-next-line no-new-func
            result[key] = eval('(' + result[key] + ')');
          } catch (error) {
            console.warn(`Failed to convert function string for key ${key}:`, error);
            // 保持原始字符串，避免破坏整个配置
          }
        } else if (typeof result[key] === 'object') {
          result[key] = reviveEchartsFunctions(result[key]);
        }
      }
      return result;
    }
    return obj;
  };

  // 渲染步骤名称（支持翻译键和历史数据兼容）
  const renderStepName = (stepName: string) => {
    // 处理翻译键格式
    const parseTranslationKey = (name: string): string => {
      // 处理带参数的翻译键格式：steps.toolsLoaded|{"count":5}
      if (name.includes('|')) {
        const [key, paramStr] = name.split('|');
        try {
          const params = JSON.parse(paramStr);
          return t(`analysis:${key}`, params) as string;
        } catch (e) {
          return t(`analysis:${key}`, { defaultValue: name }) as string;
        }
      }
      
      // 处理简单翻译键格式：steps.start
      if (name.includes('.') && !name.includes(' ') && !name.includes('*')) {
        return t(`analysis:${name}`, { defaultValue: name }) as string;
      }
      
      // 兼容历史数据：硬编码中文映射
      const chineseToKeyMap: Record<string, string> = {
        '开始分析': 'steps.start',
        '分析开始': 'steps.start',
        '创建分析记录': 'steps.analysisCreated',
        '分析用户意图': 'steps.intentAnalyzed',
        '正在分析用户意图': 'steps.intentAnalysisStarted',
        '初始化SQL执行器': 'steps.sqlExecutorInitialized',
        '正在生成分析报告': 'steps.generatingReport',
        '生成分析报告': 'steps.reportGenerated',
        '分析完成': 'steps.completed',
        '分析出错': 'steps.analysisError',
        '智能图表生成': 'tools.intelligentChartGeneration',
        '等待用户确认意图': 'intentConfirmation.waitingForConfirmation',
        '意图确认完成': 'intentConfirmation.completed',
        '应用用户反馈': 'interrupt.feedbackApplied'
      };
      
      // 处理"执行工具: XXX"格式
      if (name.startsWith('执行工具: ')) {
        const toolName = name.replace('执行工具: ', '');
        return t('analysis:steps.executeTool', { toolName, defaultValue: name }) as string;
      }
      
      // 处理"已加载 X 个分析工具"格式
      const toolsLoadedMatch = name.match(/已加载 (\d+) 个分析工具/);
      if (toolsLoadedMatch) {
        const count = parseInt(toolsLoadedMatch[1]);
        return t('analysis:steps.toolsLoaded', { count, defaultValue: name }) as string;
      }
      
      // 处理"智能评估: XXX"格式
      if (name.startsWith('智能评估: ')) {
        const reasoning = name.replace('智能评估: ', '');
        return t('analysis:steps.intelligentEvaluation', { reasoning, defaultValue: name }) as string;
      }
      
      // 尝试从映射表中查找
      if (chineseToKeyMap[name]) {
        return t(`analysis:${chineseToKeyMap[name]}`, { defaultValue: name }) as string;
      }
      
      return name;
    };
    
    return parseTranslationKey(stepName);
  };
  const [showIntentConfirmation, setShowIntentConfirmation] = useState(false);

  // 自动滚动到底部
  useEffect(() => {
    if (timelineRef.current && streaming) {
      // 使用setTimeout确保DOM已更新
      setTimeout(() => {
        // 查找报告卡片
        // 查找包含"分析报告"的卡片标题
        const cardTitles = document.querySelectorAll('.ant-card .ant-card-head-title');
        let reportCard: HTMLElement | null = null;
        
        for (let i = 0; i < cardTitles.length; i++) {
          if (cardTitles[i].textContent?.includes('分析报告')) {
            const closestCard = cardTitles[i].closest('.ant-card');
            if (closestCard) {
              reportCard = closestCard as HTMLElement;
            break;
            }
          }
        }
        
        if (reportCard && finalReport) {
          // 如果找到报告元素并且有报告内容，滚动到报告
          reportCard.scrollIntoView({ behavior: 'smooth', block: 'start' });
        } else if (timelineRef.current) {
          // 否则滚动到时间线底部
          const lastItem = timelineRef.current.querySelector('.ant-timeline-item:last-child');
          if (lastItem) {
            lastItem.scrollIntoView({ behavior: 'smooth', block: 'center' });
          } else {
            timelineRef.current.scrollIntoView({ behavior: 'smooth', block: 'end' });
          }
        }
      }, 100);
    }
  }, [analysisSteps, toolResults, finalReport, streaming]);

  // 获取全局选择的项目ID
  useEffect(() => {
    // 从localStorage获取当前选中的项目ID
    const projectId = localStorage.getItem('selectedProjectId');
    
    if (projectId) {
      setSelectedProjectId(projectId);
      form.setFieldsValue({ query: '' }); // 只设置查询字段，项目ID会在提交时从state获取
    } else {
      setError("请先在顶部选择一个项目");
    }
    
    // 组件卸载时关闭EventSource连接
    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
    };
  }, [form]);

  // 获取项目列表
  const fetchProjects = async () => {
    try {
      const response = await projectApi.getAllProjects();
      
      if (response.data.code === 0) {
        setTools(response.data.data.items || []);
      } else {
        console.error('获取项目列表失败:', response.data.message);
      }
    } catch (error) {
      console.error('获取项目列表出错:', error);
    }
  };

  // 查看历史记录
  const viewHistory = () => {
    navigate('/analysis/history');
  };
  
  // 处理流式分析请求
  const handleAnalysis = async () => {
    try {
      const formValues = await form.validateFields();
      
      // 确保有选择的项目ID
      if (!selectedProjectId) {
        message.error(t('common:messages.selectProjectFirst'));
        return;
      }
      
      // 清空之前的分析结果
      setStreaming(true);
      setError(null);
      setAnalysisSteps([]);
      setCurrentStep('');
      setIntentAnalysis(null);
      setToolResults([]);
      setFinalReport('');
      setClarificationRequest(null);
      setShowClarificationDialog(false);
      setClarificationAnswers({});
      setClarificationHistory([]); // 清空澄清历史
      setAnalysisId('');
      setStreamComplete(false);
      setMcpResults({}); // 清空MCP结果
      
      // 准备分析请求数据
      const analysisData: any = {
        project_id: selectedProjectId,
        query: formValues.query
      };
      
      // 如果有工具数据，确保工具类型正确
      if (tools.length > 0) {
        analysisData.tools = tools.map(tool => ({
          ...tool,
          tool_type: normalizeToolType(tool.tool_type)
        }));
      }
      
      // 使用LLM分析API
      eventSourceRef.current = llmAnalysisApiExtended.executeStreamAnalysis(
        analysisData,
        {
          onStart: () => {
            // 初始化LLM分析状态
            setAnalysisSteps([{
              id: 'start',
              name: '分析开始',
              status: 'process',
              time: new Date().toISOString()
            }]);
          },
          onEvent: (eventType, eventData) => {
            handleLlmStreamEvent(eventType, eventData);
          },
          onError: (error: any) => {
            setError('LLM流式分析出错: ' + error);
            // 不再设置 setStreaming(false)，保持分析内容显示
            // setStreaming(false);
          },
          onComplete: (id: string) => {
            setAnalysisId(id);
            setStreaming(false);
            setStreamComplete(true);
            message.success('分析完成');
          }
        }
      );
    } catch (error: any) {
      console.error('分析执行出错:', error);
      message.error(t('analysis:messages.executionFailed') + ': ' + (error.message || t('common:messages.unknownError')));
      // 不再设置 setStreaming(false)，保持分析内容显示
      // setStreaming(false);
    }
  };
  
  // 取消流式分析
  const cancelAnalysis = async () => {
    if (!streaming) return;
    
    try {
      // 1. 立即关闭EventSource连接
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
        eventSourceRef.current = null;
        console.log('EventSource连接已关闭');
      }
      
      // 2. 如果有analysisId且使用LLM模式，通知后端取消
      if (analysisId) {
        try {
          const response = await llmAnalysisApiExtended.cancelLlmAnalysis(analysisId);
          
          if (response.data.code === 0) {
            console.log('后端取消信号发送成功');
      message.info('分析已取消');
          } else {
            console.warn('后端取消信号发送失败:', response.data.message);
            message.warning('取消请求已发送，但后端响应异常');
          }
        } catch (apiError) {
          console.warn('调用取消API失败:', apiError);
          message.warning('取消请求发送失败，但前端已停止分析');
        }
      } else {
        // 非LLM模式或没有analysisId时，直接显示取消消息
        message.info('分析已取消');
      }
      
      // 3. 立即设置前端状态为停止
      setStreaming(false);
      
    } catch (error: any) {
      console.error('取消分析时出错:', error);
      message.error(t('analysis:errors.cancelAnalysisFailed', { defaultValue: '取消分析失败' }));
      
      // 即使出错，也要停止前端的分析状态
      setStreaming(false);
    }
  };
  
  // 处理流式事件
  const handleStreamEvent = (eventType: string, eventData: any) => {
    switch (eventType) {
      case 'start':
        setCurrentStep('start');
        setAnalysisSteps([{
          id: 'start',
          name: t('analysis:steps.start'),
          status: 'process',
          time: new Date().toISOString()
        }]);
        break;
        
      case 'analysis_created':
        setAnalysisId(eventData.id);
        setCurrentStep('analysis_created');
        setAnalysisSteps(prev => {
          // 更新所有之前步骤为完成状态
          const updated = prev.map(s => ({...s, status: 'finish'}));
          
          return [
            ...updated,
            {
              id: 'analysis_created',
              name: t('analysis:steps.analysisCreated'),
              status: 'process',
              time: new Date().toISOString()
            }
          ];
        });
        break;
        
      case 'tools_loaded':
        setCurrentStep('tools_loaded');
        setAnalysisSteps(prev => [
          ...prev,
            {
              id: 'tools_loaded',
            name: t('analysis:steps.toolsLoaded', { count: eventData.tool_count }),
            status: 'finish',
              time: new Date().toISOString()
            }
        ]);
        break;
        
      case 'sql_executor_initialized':
        console.log('SQL执行器初始化事件数据:', eventData);
        setCurrentStep('sql_executor_initialized');
        setAnalysisSteps(prev => {
          // 更新所有之前步骤为完成状态
          const updated = prev.map(s => ({...s, status: 'finish'}));
          
          // 从事件数据中提取schema_loaded状态
          // 处理不同格式的事件数据
          const schemaLoaded = eventData && (
            // 直接在eventData中
            eventData.schema_loaded === true || 
            // 在eventData.data中
            (eventData.data && eventData.data.schema_loaded === true) ||
            // 判断字符串值
            eventData.schema_loaded === 'true' ||
            (eventData.data && eventData.data.schema_loaded === 'true')
          );
          
          return [
            ...updated,
            {
              id: 'sql_executor_initialized',
              name: t('analysis:steps.sqlExecutorInitialized'),
              status: 'process',
              description: schemaLoaded ? t('analysis:steps.schemaLoaded') : t('analysis:steps.schemaLoadFailed'),
              time: new Date().toISOString()
            }
          ];
        });
        break;
        
      case 'intent_analysis_started':
        setCurrentStep('intent_analysis_started');
        setAnalysisSteps(prev => {
          // 更新所有之前步骤为完成状态
          const updated = prev.map(s => ({...s, status: 'finish'}));
          
          return [
            ...updated,
            {
              id: 'intent_analysis_started',
              name: t('analysis:steps.intentAnalysisStarted'),
              status: 'process',
              time: new Date().toISOString()
            }
          ];
        });
        break;
        
      case 'intent_analyzed':
        // 解析意图分析数据
        const intentData = eventData.intent_analysis || eventData;
        const intentTime = eventData.execution_time;
        
        setIntentAnalysis(intentData);
        setCurrentStep('intent_analyzed');
        setAnalysisSteps(prev => {
          // 明确地将之前所有步骤标记为完成，特别关注intent_analysis_started
          const updatedSteps = prev.map(s => {
            // 特别确保intent_analysis_started步骤被标记为完成
            if (s.id === 'intent_analysis_started') {
              return {...s, status: 'finish'};
            }
            return {...s, status: 'finish'};
          });
          
          // 添加新步骤
          return [
            ...updatedSteps,
            {
              id: 'intent_analyzed',
              name: t('analysis:steps.intentAnalyzed'),
              status: 'process',
              time: new Date().toISOString(),
              hasIntentData: true,  // 标记此步骤有意图分析数据
              executionTime: intentTime // 添加执行时间
            }
          ];
        });
        break;
        
      case 'step_started':
        // 处理步骤开始
        // 使用后端返回的 step_id
        const startedStepId = eventData.step_id;
        setCurrentStep(startedStepId);
        setAnalysisSteps(prev => {
          // 更新所有之前的步骤为完成状态
          const updated = prev.map(s => ({...s, status: 'finish'}));
          
          // 避免重复添加相同步骤
          if (updated.some(s => s.id === startedStepId)) {
            return updated.map(s => 
              s.id === startedStepId ? {...s, status: 'process'} : s
            );
          }
          
          return [
            ...updated,
            {
              id: startedStepId,
              name: t('analysis:steps.executeTool', { toolName: eventData.tool_name }),
              status: 'process',
              tool_name: eventData.tool_name,
              parameters: eventData.parameters,
              time: new Date().toISOString()
            }
          ];
        });
        break;
        
      case 'step_completed':
        // 处理步骤完成
        // 使用后端返回的 step_id
        const completedStepId = eventData.step_id;
        
        // 检查是否是澄清请求 - 支持多种格式
        const isClarificationResult = 
          eventData.result?.action_type === 'clarification_needed' ||
          eventData.result?.action_type === 'clarification' ||
          eventData.result?.display_format === 'clarification' ||
          eventData.tool_name === '智能交互';  // 更新为智能交互
        
        if (isClarificationResult) {
          // 处理澄清请求
          console.log('标准模式检测到澄清请求，事件数据:', eventData);
          
          const questions = eventData.result?.data?.questions || eventData.result?.questions || [];
          const context = eventData.result?.data?.context || eventData.result?.context || {};
          const message = eventData.result?.data?.message || eventData.result?.message || "需要您提供更多信息以继续分析";
          
          console.log('标准模式提取的澄清信息:', { questions, context, message });
          
          setClarificationRequest({
            stepId: completedStepId,
            questions: questions,
            context: context,
            message: message
          });
          
          // 暂停分析，等待用户输入
          setAnalysisSteps(prev => {
            return prev.map(step => {
              if (step.id === completedStepId) {
                return {
                  ...step,
                  status: 'wait',
                  description: t('analysis:steps.waitingForInput')
                };
              }
              return step;
            });
          });
          
          // 显示澄清对话框
          setShowClarificationDialog(true);
          console.log('标准模式澄清对话框应该已显示');
          break;
        }
        
        // 更新工具结果
        setMcpResults((prev: Record<string, any>) => ({
          ...prev,
          [completedStepId]: eventData.result
        }));
        
        // 更新步骤状态
        setAnalysisSteps(prev => {
          return prev.map(step => {
            if (step.id === completedStepId) {
              // 存储执行结果中的execution_time
              const executionTime = eventData.result?.execution_time || 
                (eventData.result?.result?.execution_time) || null;
              
              return {
                ...step, 
                status: 'finish',
                hasResult: true,
                resultKey: completedStepId,
                executionTime: executionTime // 保存执行时间
              };
            }
            return step;
          });
        });
        
        // 不再清除当前步骤，而是设置为"等待LLM规划"步骤
        setCurrentStep('waiting_for_planning');
        setAnalysisSteps(prev => {
          // 如果已经有waiting_for_planning步骤，则不添加
          if (prev.some(s => s.id === 'waiting_for_planning')) {
            return prev.map(s => 
              s.id === 'waiting_for_planning' ? {...s, status: 'process'} : s
            );
          }
          
          return [
            ...prev,
            {
              id: 'waiting_for_planning',
              name: t('analysis:steps.waitingForPlanning'),
              status: 'process',
              time: new Date().toISOString()
            }
          ];
        });
        break;
        
      case 'report_generation_started':
        setCurrentStep('generating_report');
        setAnalysisSteps(prev => {
          // 更新所有之前步骤为完成状态，并过滤掉waiting_for_planning步骤
          const updated = prev
            .filter(s => s.id !== 'waiting_for_planning') // 移除waiting_for_planning步骤
            .map(s => ({...s, status: 'finish'}));
          
          return [
            ...updated,
            {
              id: 'generating_report',
              name: t('analysis:steps.generatingReport'),
              status: 'process',
              time: new Date().toISOString()
            }
          ];
        });
        break;
        
      case 'report_generated':
        // 设置最终响应
        const reportData = eventData.report || '未能生成报告';
        const reportTime = eventData.execution_time;
        
        setFinalReport(reportData);
        setCurrentStep('report_generated');
        
        setAnalysisSteps(prev => {
          // 更新所有之前步骤为完成状态，特别是将generating_report标记为完成
          const updated = prev.map(s => {
            // 特别处理generating_report步骤，确保将其标记为完成
            if (s.id === 'generating_report') {
              return { ...s, status: 'finish' };
            }
            return { ...s, status: 'finish' };
          });
          
          // 如果已经有report_generated步骤，不要再添加
          if (updated.some(s => s.id === 'report_generated')) {
            return updated;
          }
          
          return [
            ...updated,
            {
              id: 'report_generated',
              name: t('analysis:steps.reportGenerated'),
              status: 'finish',
              time: new Date().toISOString(),
              hasReport: true,
              executionTime: reportTime // 保存执行时间
            }
          ];
        });
        break;
        
      case 'final_response':
        // 设置最终响应 (兼容旧的事件类型)
        setFinalReport(eventData.response || eventData.markdown_response || '未能生成报告');
        setCurrentStep('report_generated');
        
        setAnalysisSteps(prev => {
          // 更新所有之前步骤为完成状态，特别是将generating_report标记为完成
          const updated = prev.map(s => {
            // 特别处理generating_report步骤，确保将其标记为完成
            if (s.id === 'generating_report') {
              return { ...s, status: 'finish' };
            }
            return { ...s, status: 'finish' };
          });
          
          // 如果已经有report_generated步骤，不要再添加
          if (updated.some(s => s.id === 'report_generated')) {
            return updated;
          }
          
          return [
            ...updated,
            {
              id: 'report_generated',
              name: t('analysis:steps.reportGenerated'),
              status: 'finish',
              time: new Date().toISOString(),
              hasReport: true
            }
          ];
        });
        break;
        
      case 'completed':
        setCurrentStep('completed');
        setAnalysisSteps(prev => {
          // 所有步骤都标记为完成
          const updated = prev.map(s => ({...s, status: 'finish'}));
          
          // 避免重复添加完成步骤
          if (updated.some(s => s.id === 'completed')) {
            return updated;
          }
          
          return [
            ...updated,
            {
              id: 'completed',
              name: t('analysis:steps.completed'),
              status: 'finish',
              time: new Date().toISOString()
            }
          ];
        });
        setStreamComplete(true);
        break;
        
      case 'error':
        setError(eventData.message || '未知错误');
        setStreaming(false);
        break;
        
      case 'analysis_continued':
        // 处理继续分析事件
        console.log('检测到分析继续事件:', eventData);
        console.log('当前分析步骤:', analysisSteps);
        
        // 保持原有的分析步骤，只更新continue_analysis步骤状态
        setAnalysisSteps(prev => {
          return prev.map(step => {
            if (step.id === 'continue_analysis') {
              return {
                ...step, 
                status: 'finish',
                description: eventData.message || t('analysis:steps.continueAnalysis')
              };
            }
            return step;
          });
        });
        
        // 不要重置currentStep，而是继续当前的分析流程
        // setCurrentStep('analysis_continued');
        
        console.log('分析继续处理完成，保持原有步骤历史');
        break;
        
      case 'plan_created':
        setCurrentStep('plan_created');
        
        // 检查是否是澄清工具调用
        if (eventData.action_type === 'tool' && eventData.tool_name === '智能交互' ||   // 更新为智能交互
            eventData.action_type === 'clarification') {
          // 处理澄清请求
          console.log('LLM模式在plan_created中检测到澄清请求，事件数据:', eventData);
          const questions = eventData.parameters?.questions || eventData.questions || [];
          const context = eventData.parameters?.context || eventData.context || {};
          const message = eventData.parameters?.message || eventData.message || eventData.reasoning || "需要您提供更多信息以继续分析";
          
          setClarificationRequest({
            stepId: 'clarification_request',
            questions: questions,
            context: context,
            message: message
          });
          
          // 添加澄清步骤到分析步骤中
          setAnalysisSteps(prev => {
            const updated = prev
              .filter(s => s.id !== 'waiting_for_planning')
              .map(s => ({...s, status: 'finish'}));
            
            return [
              ...updated,
              {
                id: 'clarification_request',
                name: t('analysis:steps.waitingForClarification'),
                status: 'wait',
                description: t('analysis:steps.clarificationDescription'),
                time: new Date().toISOString()
              }
            ];
          });
          
          // 显示澄清对话框
          setShowClarificationDialog(true);
          break;
        }
        
        setAnalysisSteps(prev => {
          // 更新所有之前步骤为完成状态，并过滤掉waiting_for_planning步骤
          const updated = prev
            .filter(s => s.id !== 'waiting_for_planning') // 移除waiting_for_planning步骤
            .map(s => ({...s, status: 'finish'}));
          
          const actionType = eventData.action_type;
          const toolName = eventData.tool_name || '';
          const reasoning = eventData.reasoning || '';
          
          // 如果是最终答案，创建两个步骤：plan_created和generating_report
          if (actionType === 'final_answer') {
            return [
              ...updated,
              {
                id: 'plan_created',
                name: reasoning,
                status: 'finish', // 立即标记为完成
                time: new Date().toISOString(),
                planData: eventData
              },
              {
                id: 'generating_report',
                name: t('analysis:steps.generatingReport'),
                status: 'process', // 标记为进行中
                time: new Date().toISOString()
              }
            ];
          }
          
          // 如果是其他类型的计划，只创建一个步骤
          return [
            ...updated,
            {
              id: 'plan_created',
              name: `${reasoning}`,
              status: 'process',
              time: new Date().toISOString(),
              planData: eventData
            }
          ];
        });
        
        // 如果决定生成最终答案，将当前步骤设置为generating_report
        if (eventData.action_type === 'final_answer') {
          setCurrentStep('generating_report');
        }
        break;
        
      case 'step_started':
        // 处理步骤开始
        // 使用后端返回的 step_id
        const stepId = eventData.step_id;
        setCurrentStep(stepId);
        setAnalysisSteps(prev => {
          // 更新所有之前的步骤为完成状态
          const updated = prev.map(s => ({...s, status: 'finish'}));
          
          // 避免重复添加相同步骤
          if (updated.some(s => s.id === stepId)) {
            return updated.map(s => 
              s.id === stepId ? {...s, status: 'process'} : s
            );
          }
          
          return [
            ...updated,
            {
              id: stepId,
              name: `执行工具: ${eventData.tool_name}`,
              status: 'process',
              tool_name: eventData.tool_name,
              parameters: eventData.parameters,
              time: new Date().toISOString()
            }
          ];
        });
        break;
    }
  };

  // 处理LLM流式事件
  const handleLlmStreamEvent = (eventType: string, eventData: any) => {
    console.log('LLM流事件:', eventType, eventData);
    
    // 检查是否是继续分析模式
    const isContinueAnalysis = analysisSteps.some(step => step.id === 'continue_analysis');
    
    switch (eventType) {
      case 'start':
        // 检查是否是继续分析（通过分析步骤中是否有continue_analysis步骤来判断）
        if (!isContinueAnalysis) {
          // 只有在新分析时才重置步骤
        setCurrentStep('start');
        setAnalysisSteps([{
          id: 'start',
          name: t('analysis:steps.start'),
          status: 'process',
          time: new Date().toISOString()
        }]);
        } else {
          console.log('检测到继续分析，跳过start事件的步骤重置');
        }
        break;

      case 'analysis_created':
        // 检查是否是继续分析
        if (!isContinueAnalysis) {
          // 只有在新分析时才处理analysis_created事件
        setAnalysisId(eventData.id);
        setCurrentStep('analysis_created');
        setAnalysisSteps(prev => {
          // 更新所有之前步骤为完成状态
          const updated = prev.map(s => ({...s, status: 'finish'}));
          
          return [
            ...updated,
            {
              id: 'analysis_created',
              name: t('analysis:steps.analysisCreated'),
              status: 'process',
              time: new Date().toISOString()
            }
          ];
        });
        } else {
          console.log('检测到继续分析，跳过analysis_created事件处理');
          // 在继续分析时，确保analysisId正确设置
          if (eventData.id && eventData.id !== analysisId) {
            setAnalysisId(eventData.id);
          }
        }
        break;
        
      case 'analysis_continued':
        // 新增：专门处理继续分析事件
        console.log('处理analysis_continued事件:', eventData);
        
        // 确保分析ID正确
        if (eventData.id && eventData.id !== analysisId) {
          setAnalysisId(eventData.id);
        }
        
        // 更新continue_analysis步骤状态
        setAnalysisSteps(prev => prev.map(step => {
          if (step.id === 'continue_analysis') {
            return {
              ...step,
              status: 'finish',
              description: `已恢复分析上下文 - ${eventData.message || '继续执行分析'}`
            };
          }
          return step;
        }));
        
        // 添加上下文恢复步骤
        setAnalysisSteps(prev => [
          ...prev,
          {
            id: 'context_restored',
            name: '上下文已恢复',
            status: 'finish',
            time: new Date().toISOString(),
            description: `原查询: ${eventData.original_query?.substring(0, 100)}${eventData.original_query?.length > 100 ? '...' : ''}`
          }
        ]);
        
        setCurrentStep('context_restored');
        break;
        
      case 'tools_loaded':
        // 在继续分析模式下，避免重复添加tools_loaded步骤
        const hasToolsLoaded = analysisSteps.some(step => step.id === 'tools_loaded');
        if (!hasToolsLoaded) {
        setCurrentStep('tools_loaded');
          setAnalysisSteps(prev => [
            ...prev,
            {
              id: 'tools_loaded',
              name: `已加载 ${eventData.tool_count} 个分析工具`,
              status: 'finish',
              time: new Date().toISOString()
            }
          ]);
        } else {
          console.log('工具已加载，跳过重复的tools_loaded事件');
        }
        break;
        
      case 'planning_started':
      case 'planning': // 兼容两种事件名
        setCurrentStep('planning');
        setAnalysisSteps(prev => {
          // 更新所有之前步骤为完成状态，并过滤掉waiting_for_planning步骤
          const updated = prev
            .filter(s => s.id !== 'waiting_for_planning') // 移除waiting_for_planning步骤
            .map(s => ({...s, status: 'finish'}));
          
          // 检查是否已经有planning步骤
          const hasPlanningStep = updated.some(s => s.id === 'planning');
          if (hasPlanningStep) {
            // 如果已有planning步骤，只更新状态
            return updated.map(s => s.id === 'planning' ? {...s, status: 'process'} : s);
          }
          
          return [
            ...updated,
            {
              id: 'planning',
              name: isContinueAnalysis ? '继续规划分析流程' : '规划分析流程',
              status: 'process',
              time: new Date().toISOString()
            }
          ];
        });
        break;
        
      case 'schema_loaded':
        // 在继续分析模式下，避免重复添加schema_loaded步骤
        const hasSchemaLoaded = analysisSteps.some(step => step.id === 'schema_loaded');
        if (!hasSchemaLoaded) {
        setCurrentStep('schema_loaded');
        setAnalysisSteps(prev => {
          // 更新所有之前步骤为完成状态
          const updated = prev.map(s => ({...s, status: 'finish'}));
          
          return [
            ...updated,
            {
              id: 'schema_loaded',
              name: '加载数据库结构',
              status: 'process',
              time: new Date().toISOString()
            }
          ];
        });
        } else {
          console.log('数据库结构已加载，跳过重复的schema_loaded事件');
        }
        break;
        
      // 新增: 处理规划决策事件，填充空白步骤  
      case 'planning_decision':
        // 避免重复添加planning_decision步骤
        const hasPlanningDecision = analysisSteps.some(step => step.id === 'planning_decision');
        if (!hasPlanningDecision) {
          setCurrentStep('planning_decision');
          setAnalysisSteps(prev => {
            // 更新所有之前步骤为完成状态
            const updated = prev.map(s => ({...s, status: 'finish'}));
            
            return [
              ...updated,
              {
                id: 'planning_decision',
                name: eventData.message || '分析决策',
                status: 'finish',
                time: new Date().toISOString(),
                description: eventData.reasoning || `选择${eventData.tool_name}工具执行分析`
              }
            ];
          });
        } else {
          console.log('已存在规划决策步骤，跳过重复的planning_decision事件');
        }
        break;
        
      case 'intent_analysis_started':
        setCurrentStep('intent_analysis_started');
           setAnalysisSteps(prev => {
          // 更新所有之前步骤为完成状态
          const updated = prev.map(s => ({...s, status: 'finish'}));
             
             return [
               ...updated,
               {
              id: 'intent_analysis_started',
              name: '正在分析用户意图',
              status: 'process',
                 time: new Date().toISOString()
               }
             ];
           });
           break;
        
      // 新增：处理意图确认请求
      case 'intent_confirmation_request':
        console.log('收到意图确认请求:', eventData);
        setIntentConfirmation(eventData);
        setShowIntentConfirmation(true);
        setCurrentStep('intent_confirmation');
        setAnalysisSteps(prev => {
          // 更新所有之前步骤为完成状态
          const updated = prev.map(s => ({...s, status: 'finish'}));
          
          return [
            ...updated,
            {
              id: 'intent_confirmation',
              name: '等待用户确认意图',
              status: 'process',
              time: new Date().toISOString(),
              description: '请确认分析意图和计划，或提供调整建议'
            }
          ];
        });
        break;
        
      // 新增：处理意图确认完成
      case 'intent_confirmation_completed':
        console.log('意图确认完成:', eventData);
        setShowIntentConfirmation(false);
        setIntentConfirmation(null);
        setIntentAdjustment('');
        setCurrentStep('intent_confirmed');
        setAnalysisSteps(prev => {
          // 更新意图确认步骤为完成状态
          const updated = prev.map(s => 
            s.id === 'intent_confirmation' 
              ? {...s, status: 'finish', name: '意图确认完成'}
              : s
          );
          
          return updated;
        });
        break;
        
      case 'intent_analyzed':
        // 解析意图分析数据
        const intentData = eventData.intent_analysis || eventData;
        const intentTime = eventData.execution_time;
        
        setIntentAnalysis(intentData);
        setCurrentStep('intent_analyzed');
        setAnalysisSteps(prev => {
          // 明确地将之前所有步骤标记为完成，特别关注intent_analysis_started
          const updatedSteps = prev.map(s => {
            // 特别确保intent_analysis_started步骤被标记为完成
            if (s.id === 'intent_analysis_started') {
              return {...s, status: 'finish'};
            }
            return {...s, status: 'finish'};
          });
           
          // 添加新步骤
           return [
            ...updatedSteps,
             {
              id: 'intent_analyzed',
              name: '分析用户意图',
               status: 'process',
               time: new Date().toISOString(),
              hasIntentData: true,  // 标记此步骤有意图分析数据
              executionTime: intentTime // 添加执行时间
             }
           ];
         });
         break;
         
       case 'step_started':
         // 处理步骤开始
         // 使用后端返回的 step_id
         const stepId = eventData.step_id;
         setCurrentStep(stepId);
         setAnalysisSteps(prev => {
           // 更新所有之前的步骤为完成状态
           const updated = prev.map(s => ({...s, status: 'finish'}));
           
           // 避免重复添加相同步骤
           if (updated.some(s => s.id === stepId)) {
             return updated.map(s => 
               s.id === stepId ? {...s, status: 'process'} : s
             );
           }
           
           // 根据工具名称设置不同的步骤名称
           let stepName = t('analysis:steps.executeTool', { toolName: eventData.tool_name });
           if (eventData.tool_name === '意图分析') {
             stepName = t('analysis:steps.intentAnalyzed');
           } else if (eventData.tool_name === '意图确认') {
             stepName = '处理意图确认';
           }
           
           return [
             ...updated,
             {
               id: stepId,
               name: stepName,
               status: 'process',
               tool_name: eventData.tool_name,
               parameters: eventData.parameters,
               time: new Date().toISOString()
             }
           ];
         });
         break;
         
       case 'step_completed':
         // 处理步骤完成
         // 使用后端返回的 step_id
         const completedStepId = eventData.step_id;
         
         // 检查是否是澄清请求 - 支持多种格式
         const isClarificationResult = 
           eventData.result?.action_type === 'clarification_needed' ||
           eventData.result?.action_type === 'clarification' ||
           eventData.result?.display_format === 'clarification' ||
           eventData.tool_name === '信息澄清';
         
         if (isClarificationResult) {
           // 处理澄清请求
          console.log('标准模式检测到澄清请求，事件数据:', eventData);
           
           const questions = eventData.result?.data?.questions || eventData.result?.questions || [];
           const context = eventData.result?.data?.context || eventData.result?.context || {};
           const message = eventData.result?.data?.message || eventData.result?.message || "需要您提供更多信息以继续分析";
           
          console.log('标准模式提取的澄清信息:', { questions, context, message });
           
           setClarificationRequest({
             stepId: completedStepId,
             questions: questions,
             context: context,
             message: message
           });
           
           // 暂停分析，等待用户输入
           setAnalysisSteps(prev => {
             return prev.map(step => {
               if (step.id === completedStepId) {
                 return {
                   ...step,
                   status: 'wait',
                   description: '等待用户提供更多信息...'
                 };
               }
               return step;
             });
           });
           
           // 显示澄清对话框
           setShowClarificationDialog(true);
          console.log('标准模式澄清对话框应该已显示');
           break;
         }
         
         // 处理特殊步骤：意图分析和意图确认
         if (eventData.tool_name === '意图分析') {
           // 保存意图分析结果
           setIntentAnalysis(eventData.result);
           console.log('保存意图分析结果:', eventData.result);
           // 意图分析完成后，等待意图确认请求事件
         } else if (eventData.tool_name === '意图确认') {
           // 意图确认完成，更新相关状态
           console.log('意图确认步骤完成:', eventData.result);
         }
         
         // 更新工具结果
         setMcpResults((prev: Record<string, any>) => ({
           ...prev,
           [completedStepId]: eventData.result
         }));
         
         // 更新步骤状态
         setAnalysisSteps(prev => {
           return prev.map(step => {
             if (step.id === completedStepId) {
               // 存储执行结果中的execution_time
               const executionTime = eventData.result?.execution_time || 
                 (eventData.result?.result?.execution_time) || null;
               
               return {
                 ...step, 
                 status: 'finish',
                 hasResult: true,
                 resultKey: completedStepId,
                 executionTime: executionTime, // 保存执行时间
                 hasIntentData: eventData.tool_name === '意图分析' // 标记意图分析步骤
               };
             }
             return step;
           });
         });
         
         // 不再清除当前步骤，而是设置为"等待LLM规划"步骤
         setCurrentStep('waiting_for_planning');
         setAnalysisSteps(prev => {
           // 如果已经有waiting_for_planning步骤，则不添加
           if (prev.some(s => s.id === 'waiting_for_planning')) {
             return prev.map(s => 
               s.id === 'waiting_for_planning' ? {...s, status: 'process'} : s
             );
           }
           
           return [
             ...prev,
             {
               id: 'waiting_for_planning',
               name: '正在分析结果并规划下一步...',
               status: 'process',
               time: new Date().toISOString()
             }
           ];
         });
         break;
         
       case 'report_generation_started':
         setCurrentStep('generating_report');
         setAnalysisSteps(prev => {
           // 更新所有之前步骤为完成状态，并过滤掉waiting_for_planning步骤
           const updated = prev
             .filter(s => s.id !== 'waiting_for_planning') // 移除waiting_for_planning步骤
             .map(s => ({...s, status: 'finish'}));
           
           return [
             ...updated,
             {
               id: 'generating_report',
               name: '正在生成分析报告',
               status: 'process',
               time: new Date().toISOString()
             }
           ];
         });
         break;
         
       case 'report_generated':
         // 设置最终响应
         const reportData = eventData.report || '未能生成报告';
         const reportTime = eventData.execution_time;
         
         setFinalReport(reportData);
         setCurrentStep('report_generated');
         
         setAnalysisSteps(prev => {
           // 更新所有之前步骤为完成状态，特别是将generating_report标记为完成
           const updated = prev.map(s => {
             // 特别处理generating_report步骤，确保将其标记为完成
             if (s.id === 'generating_report') {
               return { ...s, status: 'finish' };
             }
             return { ...s, status: 'finish' };
           });
           
           // 如果已经有report_generated步骤，不要再添加
           if (updated.some(s => s.id === 'report_generated')) {
             return updated;
           }
           
           return [
             ...updated,
             {
               id: 'report_generated',
               name: '生成分析报告',
               status: 'finish',
               time: new Date().toISOString(),
               hasReport: true,
               executionTime: reportTime // 保存执行时间
             }
           ];
         });
         break;
         
       case 'clarification_waiting':
         // 处理澄清工具执行后的等待状态
         console.log('接收到澄清等待事件:', eventData);
         setCurrentStep('clarification_waiting');
         
         // 更新分析步骤，显示等待状态
         setAnalysisSteps(prev => {
           return prev.map(step => {
             // 如果存在waiting_for_planning步骤，将其移除
             if (step.id === 'waiting_for_planning') {
               return null;
             }
             return step;
           }).filter(Boolean).concat([
             {
               id: 'clarification_waiting',
               name: '等待用户提供澄清信息',
               status: 'wait',
               description: eventData.message || '分析已暂停，等待用户提供澄清信息',
               time: new Date().toISOString()
             }
           ]);
         });
         
         // 显示暂停状态提示
         message.info('分析已暂停，请在弹出的对话框中提供必要信息');
         break;
         
       case 'chart_generated':
         console.log('收到图表生成事件:', eventData);
         
         try {
           const chartData = eventData.chart_data;
           if (chartData && chartData.data) {
             // 创建图表数据对象，支持单图表和多图表
             const chartWithId = {
               ...chartData,
               chartId: chartData.chartId || `chart_${eventData.step_id}_${Date.now()}`,
               stepId: eventData.step_id,
               generatedAt: eventData.generatedAt || new Date().toISOString()
             };
             
             // 保存到图表历史列表
             setChartDataList(prev => [...prev, chartWithId]);
             
             // 自动弹出图表模态框
             setChartData(chartWithId);
             setChartModalVisible(true);
             
             console.log('图表数据已保存:', chartWithId);
             message.success(
               (chartData.data?.chart_type === 'multi_charts' || chartData.data?.display_format === 'multi_chart' || (chartData.data?.total_charts && chartData.data?.total_charts > 1))
                 ? `已生成 ${chartData.data?.charts?.length || chartData.data?.total_charts || 0} 个图表`
                 : '图表已生成'
             );
           } else {
             console.warn('图表数据为空或格式不正确:', eventData);
             message.warning('图表数据格式异常');
           }
         } catch (error) {
           console.error('处理图表生成事件失败:', error);
           message.error('处理图表数据失败');
         }
         break;
         
       case 'completed':
         // 检查是否是暂停状态而不是真正完成
         const isPaused = eventData.status === 'waiting_for_clarification' || 
                         eventData.status === 'waiting_for_intent_confirmation' ||
                         eventData.cancelled;
         
         if (isPaused) {
           // 暂停状态：不设置为真正完成，保持等待状态
           console.log('分析暂停等待用户输入:', eventData);
           
           // 根据暂停类型设置相应的步骤状态
           if (eventData.status === 'waiting_for_intent_confirmation') {
             setAnalysisSteps(prev => {
               return prev.map(s => 
                 s.id === 'intent_confirmation' 
                   ? {...s, status: 'wait', description: '等待用户确认意图和计划'}
                   : s
               );
             });
           } else if (eventData.status === 'waiting_for_clarification') {
             // 澄清等待状态已在其他地方处理
           }
           
           // 不设置streamComplete为true，保持流处于活跃状态
           break;
         }
         
         // 真正的完成状态
         setCurrentStep('completed');
         setAnalysisSteps(prev => {
           // 所有步骤都标记为完成
           const updated = prev.map(s => ({...s, status: 'finish'}));
           
           // 避免重复添加完成步骤
           if (updated.some(s => s.id === 'completed')) {
             return updated;
           }
           
           return [
             ...updated,
             {
               id: 'completed',
               name: eventData.message || '分析完成',
               status: 'finish',
               time: new Date().toISOString()
             }
           ];
         });
         setStreamComplete(true);
         break;
         
       case 'error':
         setError(eventData.message);
         setStreaming(false);
         break;
        
      case 'plan_created':
        setCurrentStep('plan_created');
        
        // 检查是否是澄清工具调用
        if (eventData.action_type === 'tool' && eventData.tool_name === '智能交互' ||   // 更新为智能交互
            eventData.action_type === 'clarification') {
          // 处理澄清请求
          console.log('LLM模式在plan_created中检测到澄清请求，事件数据:', eventData);
          const questions = eventData.parameters?.questions || eventData.questions || [];
          const context = eventData.parameters?.context || eventData.context || {};
          const message = eventData.parameters?.message || eventData.message || eventData.reasoning || "需要您提供更多信息以继续分析";
          
          setClarificationRequest({
            stepId: 'clarification_request',
            questions: questions,
            context: context,
            message: message
          });
          
          // 添加澄清步骤到分析步骤中
          setAnalysisSteps(prev => {
            const updated = prev
              .filter(s => s.id !== 'waiting_for_planning')
              .map(s => ({...s, status: 'finish'}));
            
            return [
              ...updated,
              {
                id: 'clarification_request',
                name: '等待用户提供更多信息',
                status: 'wait',
                description: '请在弹出的对话框中补充必要信息',
                time: new Date().toISOString()
              }
            ];
          });
          
          // 显示澄清对话框
          setShowClarificationDialog(true);
          break;
        }
        
        setAnalysisSteps(prev => {
          // 更新所有之前步骤为完成状态，并过滤掉waiting_for_planning步骤
          const updated = prev
            .filter(s => s.id !== 'waiting_for_planning') // 移除waiting_for_planning步骤
            .map(s => ({...s, status: 'finish'}));
          
          const actionType = eventData.action_type;
          const toolName = eventData.tool_name || '';
          const reasoning = eventData.reasoning || '';
          
          // 如果是最终答案，创建两个步骤：plan_created和generating_report
          if (actionType === 'final_answer') {
            return [
              ...updated,
              {
                id: 'plan_created',
                name: reasoning,
                status: 'finish', // 立即标记为完成
                time: new Date().toISOString(),
                planData: eventData
              },
              {
                id: 'generating_report',
                name: '正在生成分析报告',
                status: 'process', // 标记为进行中
                time: new Date().toISOString()
              }
            ];
          }
          
          // 如果是其他类型的计划，只创建一个步骤
          return [
            ...updated,
            {
              id: 'plan_created',
              name: `${reasoning}`,
              status: 'process',
              time: new Date().toISOString(),
              planData: eventData
            }
          ];
        });
        
        // 如果决定生成最终答案，将当前步骤设置为generating_report
        if (eventData.action_type === 'final_answer') {
          setCurrentStep('generating_report');
        }
        break;
     }
   };

    // 渲染意图分析内容
    const renderIntentAnalysis = () => {
    if (!intentAnalysis) return null;

    // 查找意图分析步骤以获取耗时信息
    const intentStep = analysisSteps.find(step => step.id === 'intent_analyzed');
    const executionTime = intentStep?.executionTime;

    return (
      <Card
        bordered={false}
        style={{ background: '#f9f9f9', marginTop: 16, marginLeft: 24 }}
        size="small"
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span>
              <AimOutlined style={{ marginRight: 8, color: '#1890ff' }} />
              意图分析结果
            </span>
            {executionTime && <Text type="secondary">{t('analysis:tools.executionTime', { time: typeof executionTime === 'number' ? executionTime.toFixed(3) : parseFloat(String(executionTime)).toFixed(3) })}</Text>}
          </div>
        }
      >
        {/* 意图详细描述 - 新的核心字段 */}
        {intentAnalysis.intent_description && (
          <div style={{ marginBottom: 16 }}>
            <div style={{
              background: '#f6ffed', 
              border: '1px solid #b7eb8f',
              borderRadius: 6,
              padding: '12px 16px',
              marginBottom: 12
                }}>
              <Text strong style={{ color: '#52c41a' }}>
                <FileTextOutlined style={{ marginRight: 6 }} />
                分析指导
              </Text>
              <div style={{ marginTop: 8 }}>
                <Text style={{ fontSize: '14px', lineHeight: '1.6', whiteSpace: 'pre-wrap' }}>
                  {intentAnalysis.intent_description}
                </Text>
                </div>
            </div>
          </div>
        )}

        {/* 兼容旧字段显示 - 如果存在的话 */}
        {intentAnalysis.summary && (
          <div style={{ marginBottom: 16 }}>
                        <div style={{
              background: '#e6f7ff', 
              border: '1px solid #91d5ff',
              borderRadius: 6,
              padding: '12px 16px',
              marginBottom: 12
            }}>
              <Text strong style={{ color: '#1890ff' }}>
                <BulbOutlined style={{ marginRight: 6 }} />
                分析目标
                              </Text>
              <div style={{ marginTop: 8 }}>
                <Text style={{ fontSize: '14px', lineHeight: '1.6' }}>{intentAnalysis.summary}</Text>
                            </div>
            </div>
                        </div>
                      )}

        {/* 分析类型 - 兼容旧字段 */}
        {intentAnalysis.analysis_type && (
          <div style={{ marginBottom: 16 }}>
            <Text strong style={{ marginRight: 8 }}>分析类型:</Text>
            <Tag color="blue">{intentAnalysis.analysis_type}</Tag>
            {intentAnalysis.context_aware && (
              <Tag color="green" style={{ marginLeft: 8 }}>已结合上下文</Tag>
            )}
          </div>
        )}
      </Card>
    );
  };

  // 渲染工具执行结果
  const renderToolResult = (result: any) => {
    if (!result) return null;
    
    // 简化的工具结果渲染，不再依赖旧的步骤信息
    
    // 基本卡片属性，适用于所有工具结果类型
    const cardProps = {
      bordered: false,
      style: { 
        marginTop: 16, 
        marginLeft: 24
      },
      size: 'small' as any,
      title: (
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Space>
            <span>{result.tool_name || t('analysis:tools.executionResult')}</span>
          </Space>
          <div>
            {result.execution_time && (
              <Tag color="green">{t('analysis:tools.executionTime', { time: result.execution_time.toFixed(3) })}</Tag>
            )}
          </div>
        </div>
      )
    };
    
    // 处理SQL错误情况
    if (result.result?.error || (typeof result.result?.data === 'string' && result.result.data.includes('ORA-'))) {
      return (
        <Card {...cardProps}>
          <div style={{ marginBottom: 8 }}>
            <Text type="danger" strong>错误:</Text>
            <div style={{ 
              whiteSpace: 'pre-wrap', 
              fontFamily: 'monospace', 
              background: '#fff2f0', 
              padding: 8, 
              borderRadius: 4,
              border: '1px solid #ffccc7',
              marginTop: 8
            }}>
              {result.result?.error || result.result?.data}
            </div>
          </div>
        </Card>
      );
    }

    // 处理资金流转分析 (Graph类型)
    if (result.tool_name?.includes("资金流转分析") || result.result?.display_format === "graph") {
      return (
        <Card {...cardProps}>
          {renderFundTransferResult(result)}
        </Card>
      );
    }
    
    // 处理公司基本信息查询
    if (result.tool_name?.includes("公司基本信息查询") && result.result?.data) {
      return (
        <Card {...cardProps}>
          {renderCompanyInfoResult(result)}
        </Card>
      );
    }
    
    // 处理图表生成结果
    if (result.tool_name?.includes("智能图表生成") || result.result?.display_format === "chart" || result.result?.data?.chart_type === "echarts") {
      return (
        <Card {...cardProps}>
          {renderChartResult(result)}
        </Card>
      );
    }
    
    // 首先检查是否为SQL查询结果
    if (isSQLQueryResult(result)) {
      // 检查是否有结果数据
      if (result.result?.results && Array.isArray(result.result.results)) {
        return (
          <Card {...cardProps}>
            <div>
              <Space direction="vertical" style={{ width: '100%' }}>
                {result.result.sql && (
                  <div style={{ marginBottom: 8 }}>
                    <Text strong>执行的SQL:</Text>
                    <div style={{ 
                      whiteSpace: 'pre-wrap', 
                      fontFamily: 'monospace', 
                      background: '#f0f5ff', 
                      padding: 8, 
                      borderRadius: 4,
                      border: '1px solid #d6e4ff',
                      marginTop: 4
                    }}>
                      {result.result.sql}
                    </div>
                  </div>
                )}
                
                {result.result.parameters && Object.keys(result.result.parameters).length > 0 && (
                  <div style={{ marginBottom: 8 }}>
                    <Text strong>参数:</Text>
                    <div style={{ 
                      whiteSpace: 'pre-wrap', 
                      fontFamily: 'monospace', 
                      background: '#f9f0ff', 
                      padding: 8, 
                      borderRadius: 4,
                      border: '1px solid #efdbff',
                      marginTop: 4
                    }}>
                      {JSON.stringify(result.result.parameters, null, 2)}
                    </div>
                  </div>
                )}
                
                <Text type="secondary">
                  执行时间: {result.result.execution_time?.toFixed(3) || '未知'} ms | 
                  行数: {result.result.row_count || result.result.results.length}
                </Text>
                
                <div style={{ marginTop: 8 }}>
                  {renderSQLResultsTable(result.result.results, result.result.columns)}
                </div>
              </Space>
            </div>
          </Card>
        );
      }
      
      return (
        <Card {...cardProps}>
          <div>
            <Text type="secondary">执行时间: {typeof result.execution_time === 'number' ? result.execution_time.toFixed(3) : (result.result?.execution_time ? parseFloat(String(result.result.execution_time)).toFixed(3) : '未知')} ms</Text>
            <div style={{ marginTop: 8 }}>
              {renderSQLResultTable(result.result?.data || result.result)}
            </div>
          </div>
        </Card>
      );
    }
    
    return (
      <Card {...cardProps}>
        {result.result?.data && (
          <>
            {isTableData(result.result.data) ? (
              <div>
                <Text type="secondary">执行时间: {typeof result.execution_time === 'number' ? result.execution_time.toFixed(3) : (result.result?.execution_time ? parseFloat(String(result.result.execution_time)).toFixed(3) : '未知')} ms</Text>
                <div style={{ marginTop: 8 }}>
                  {renderDataTable(result.result.data)}
                </div>
              </div>
            ) : (
              <ToolResultRenderer
                toolName={result.tool_name || ""}
                toolType={result.result.display_format || "json"}
                data={result.result.data}
                error={result.result.error}
                executionTime={typeof result.execution_time === 'number' ? result.execution_time.toFixed(3) : (result.result?.execution_time ? parseFloat(String(result.result.execution_time)).toFixed(3) : '未知')}
              />
            )}
          </>
        )}
      </Card>
    );
  };

  // 处理资金流转分析结果
  const renderFundTransferResult = (result: any): React.ReactNode => {
    try {
      const resultData = result.result.data;
      
      if (!resultData || !resultData.data || !Array.isArray(resultData.data) || resultData.data.length === 0) {
        return <Empty description="资金流转数据为空" />;
      }
      
      // 获取列名和数据
      const columns = resultData.columns || [];
      const tableData = resultData.data;
      
      // 计算最大交易金额，用于边的宽度缩放
      let maxAmount = 0;
      tableData.forEach((row: any[]) => {
        const amount = row[6]; // 总金额列索引
        if (typeof amount === 'number' && amount > maxAmount) {
          maxAmount = amount;
        }
      });
      
      // 提取所有公司名称，构建节点
      const companySet = new Set<string>();
      tableData.forEach((row: any[]) => {
        companySet.add(row[1]); // 源公司
        companySet.add(row[3]); // 目标公司
      });
      
      const nodes = Array.from(companySet).map((company, index) => ({
        id: company,
        name: company,
        symbolSize: 30,
        itemStyle: {
          color: '#5470c6'
        },
        label: {
          show: true,
          position: 'right',
          formatter: '{b}'
        }
      }));
      
      // 构建边
      const links = tableData.map((row: any[]) => {
        // 获取风险等级
        const riskLevel = row[7]; // 风险等级列索引
        let lineColor = '#91cc75'; // 默认绿色，低风险
        
        if (riskLevel === '高风险') {
          lineColor = '#ee6666'; // 红色
        } else if (riskLevel === '中风险') {
          lineColor = '#fac858'; // 黄色
        }
        
        // 计算边的宽度，基于交易金额比例
        const amount = row[6]; // 总金额列索引
        const lineWidth = 1 + (amount / maxAmount) * 5; // 宽度范围：1-6
        
        return {
          source: row[1], // 源公司
          target: row[3], // 目标公司
          value: row[6], // 总金额
          lineStyle: {
            width: lineWidth,
            color: lineColor,
            curveness: 0.3 // 添加一点曲率，避免重叠
          },
          label: {
            show: true,
            formatter: `{c} 元`
          },
          tooltip: {
            formatter: ({ data }: any) => {
              const sourceCompany = data.source;
              const targetCompany = data.target;
              const amount = data.value;
              
              // 查找对应的行以获取更多详情
              const rowData = tableData.find((r: any[]) => 
                r[1] === sourceCompany && r[3] === targetCompany
              );
              
              if (rowData) {
                const sourceAccount = rowData[2];
                const targetAccount = rowData[4];
                const txCount = rowData[5];
                const riskLevel = rowData[7];
                
                return `
                  <div style="font-weight:bold;margin-bottom:5px;">
                    ${sourceCompany} → ${targetCompany}
                  </div>
                  <div>源账号: ${sourceAccount}</div>
                  <div>目标账号: ${targetAccount}</div>
                  <div>交易次数: ${txCount}</div>
                  <div>总金额: ¥${amount}</div>
                  <div>风险等级: ${riskLevel}</div>
                `;
              }
              
              return `${sourceCompany} → ${targetCompany}: ¥${amount}`;
            }
          }
        };
      });
      
      // 构建图表配置
      const graphOption = {
        title: {
          text: '资金流转关系图',
          subtext: `共 ${nodes.length} 家公司，${links.length} 条资金流向`,
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          confine: true
        },
        legend: {
          data: ['公司', '资金流向'],
          orient: 'vertical',
          right: 10,
          top: 'center'
        },
        series: [
          {
            name: '资金流转',
            type: 'graph',
            layout: 'force',
            data: nodes,
            links: links,
            roam: true,
            draggable: true,
            edgeSymbol: ['none', 'arrow'],  // 添加箭头指向配置，起点无箭头，终点有箭头
            edgeSymbolSize: [0, 10],        // 箭头大小配置
            label: {
              position: 'right'
            },
            force: {
              repulsion: 100,
              gravity: 0.1,
              edgeLength: 150
            },
            lineStyle: {
              opacity: 0.8,
              color: '#91cc75',
              width: 2,
              type: 'solid',
              curveness: 0.2  // 增加曲率以便更好地显示箭头
            },
            emphasis: {
              focus: 'adjacency',
              lineStyle: {
                width: 5
              }
            }
          }
        ]
      };
      
      return (
        <div>
          <Tabs defaultActiveKey="graph">
            <Tabs.TabPane tab="关系图" key="graph">
              <div style={{ height: 500, marginBottom: 16 }}>
                <ReactECharts
                  option={reviveEchartsFunctions(graphOption)}
                  style={{ height: '100%', width: '100%' }} 
                  opts={{ renderer: 'canvas' }}
                  // 添加简单的右键菜单，例如放大、缩小等
                  onEvents={{
                    contextmenu: (params: any) => {
                      // 防止默认右键菜单
                      params.event.event.preventDefault();
                    }
                  }}
                />
              </div>
              <div>
                <Alert 
                  message="使用提示" 
                  description={
                    <ul style={{ paddingLeft: 20, marginBottom: 0 }}>
                      <li>鼠标滚轮：缩放图表</li>
                      <li>拖动：平移图表</li>
                      <li>拖动节点：调整节点位置</li>
                      <li>悬停：查看详细信息</li>
                    </ul>
                  }
                  type="info" 
                  showIcon 
                />
              </div>
            </Tabs.TabPane>
            <Tabs.TabPane tab="数据表格" key="table">
              <div>
                <Table 
                  columns={resultData.columns.map((col: string, index: number) => ({
                    title: col,
                    dataIndex: index.toString(),
                    key: index.toString(),
                    ellipsis: { showTitle: false },
                    render: (text: any) => {
                      if (col === '风险等级') {
                        let color = 'green';
                        if (text === '高风险') color = 'red';
                        else if (text === '中风险') color = 'orange';
                        
                        return <Tag color={color}>{text}</Tag>;
                      }
                      
                      return (
                        <Typography.Paragraph 
                          ellipsis={{ rows: 3, expandable: true, symbol: '更多' }}
                          style={{ marginBottom: 0 }}
                        >
                          {text === null || text === undefined ? '' : String(text)}
                        </Typography.Paragraph>
                      );
                    }
                  }))} 
                  dataSource={resultData.data.map((row: any[], rowIndex: number) => {
                    const rowData: any = { key: rowIndex };
                    row.forEach((cell, cellIndex) => {
                      rowData[cellIndex.toString()] = cell;
                    });
                    return rowData;
                  })}
                  size="small"
                  pagination={{ 
                    pageSize: 10, 
                    showSizeChanger: true, 
                    showTotal: (total: number) => `共 ${total} 条记录`,
                    pageSizeOptions: ['10', '20', '50', '100']
                  }}
                  bordered
                />
              </div>
            </Tabs.TabPane>
          </Tabs>
        </div>
      );
    } catch (error) {
      console.error('渲染资金流转分析时出错：', error);
      // 出错时，回退到普通表格显示
      return (
        <>
          <Alert
            message="渲染资金流转图表时出错"
            description={String(error)}
            type="error"
            showIcon
            style={{ marginBottom: 16 }}
          />
          <div>
            {result.result?.data && renderDataTable(result.result.data)}
          </div>
        </>
      );
    }
  };

  // 处理公司基本信息查询结果
  const renderCompanyInfoResult = (result: any): React.ReactNode => {
    try {
      const resultData = result.result.data;
      // 将 execution_time 转为数字
      const execTimeRaw = result.result.execution_time;
      const execTime = execTimeRaw != null ? (typeof execTimeRaw === 'string' ? parseFloat(execTimeRaw) : execTimeRaw) : null;
      console.log('公司基本信息查询结果：', resultData);
      
      // 检测是否有JSON_DOC字段的嵌套JSON格式
      if (resultData?.data && Array.isArray(resultData.data) && resultData.data.length > 0) {
        const firstRow = resultData.data[0];
        if (Array.isArray(firstRow) && firstRow.length > 0) {
          const possibleJson = firstRow[0];
          
          if (typeof possibleJson === 'string' && (possibleJson.startsWith('[') || possibleJson.startsWith('{'))) {
            try {
              // 尝试解析JSON字符串
              const parsedData = JSON.parse(possibleJson);
              console.log('成功解析JSON_DOC：', parsedData);
              
              // 处理解析出的公司数据
              if (Array.isArray(parsedData)) {
                return (
                  <>
                    <div style={{ margin: '8px 0', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span style={{ fontWeight: 'bold' }}>公司基本信息</span>
                      <Text type="secondary">执行时间: {execTime != null ? execTime.toFixed(3) : '未知'} ms</Text>
                    </div>
                    {parsedData.map((company, index) => {
                      // 提取相关文件数据
                      const relatedFiles = company["相关文件"] || [];
                      // 创建一个不包含相关文件的公司信息副本，用于基本信息展示
                      const basicCompanyInfo = { ...company };
                      delete basicCompanyInfo["相关文件"];
                      
                      return (
                      <div key={index} style={{ marginBottom: 16 }}>
                        <Descriptions 
                          title={company["公司名称"] || "公司详情"} 
                          bordered 
                          column={{ xxl: 4, xl: 3, lg: 3, md: 2, sm: 2, xs: 1 }}
                          size="small"
                        >
                            {Object.entries(basicCompanyInfo).map(([key, value]) => (
                            <Descriptions.Item 
                              key={key} 
                              label={<strong>{key}</strong>}
                              labelStyle={{ fontWeight: 'bold' }}
                            >
                              {value !== null && value !== undefined ? String(value) : '-'}
                            </Descriptions.Item>
                          ))}
                        </Descriptions>
                          
                          {/* 相关文件展示区域 */}
                          {relatedFiles && Array.isArray(relatedFiles) && relatedFiles.length > 0 && (
                            <div style={{ marginTop: 16 }}>
                              <Divider orientation="left" style={{ margin: '16px 0 12px 0' }}>
                                <Text strong>相关文件 ({relatedFiles.length})</Text>
                              </Divider>
                              <div style={{ 
                                background: '#fafafa', 
                                padding: 12, 
                                borderRadius: 6,
                                border: '1px solid #f0f0f0'
                              }}>
                                <List
                                  size="small"
                                  dataSource={relatedFiles}
                                  renderItem={(file: any, fileIndex: number) => (
                                    <List.Item
                                      key={fileIndex}
                                      style={{ 
                                        padding: '8px 12px',
                                        background: '#fff',
                                        marginBottom: 8,
                                        borderRadius: 4,
                                        border: '1px solid #e8e8e8'
                                      }}
                                      actions={[
                                        <Button
                                          key="download"
                                          type="link"
                                          size="small"
                                          icon={<FileTextOutlined />}
                                          onClick={() => {
                                            if (file.文件链接) {
                                              window.open(file.文件链接, '_blank');
                                            }
                                          }}
                                          disabled={!file.文件链接}
                                        >
                                          下载
                                        </Button>,
                                        <Button
                                          key="preview"
                                          type="link"
                                          size="small"
                                          onClick={() => {
                                            if (file.文件链接) {
                                              // 创建预览模态框
                                              const fileName = file.文件名 || '未知文件';
                                              const fileUrl = file.文件链接;
                                              
                                              // 判断文件类型
                                              const fileExtension = fileName.split('.').pop()?.toLowerCase();
                                              const isImage = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(fileExtension || '');
                                              const isPdf = fileExtension === 'pdf';
                                              const isOfficeDoc = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'].includes(fileExtension || '');
                                              
                                              let previewContent;
                                              var officeViewerUrl = '';
                                              if (isImage) {
                                                previewContent = (
                                                  <div style={{ textAlign: 'center' }}>
                                                    <img 
                                                      src={fileUrl} 
                                                      alt={fileName}
                                                      style={{ 
                                                        maxWidth: '100%', 
                                                        maxHeight: '70vh',
                                                        objectFit: 'contain'
                                                      }}
                                                      onError={(e) => {
                                                        (e.target as HTMLImageElement).style.display = 'none';
                                                        const errorDiv = document.createElement('div');
                                                        errorDiv.innerHTML = '图片加载失败';
                                                        errorDiv.style.color = '#999';
                                                        errorDiv.style.textAlign = 'center';
                                                        errorDiv.style.padding = '20px';
                                                        (e.target as HTMLImageElement).parentNode?.appendChild(errorDiv);
                                                      }}
                                                    />
                                                  </div>
                                                );
                                              } else if (isPdf) {
                                                previewContent = (
                                                  <div style={{ height: '70vh' }}>
                                                    <iframe
                                                      src={`${fileUrl}#toolbar=1&navpanes=1&scrollbar=1`}
                                                      style={{ 
                                                        width: '100%', 
                                                        height: '100%',
                                                        border: 'none'
                                                      }}
                                                      title={fileName}
                                                    />
                                                  </div>
                                                );
                                              } else if (isOfficeDoc) {
                                                // Office文档使用微软在线预览服务
                                                officeViewerUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(fileUrl)}`;
                                                previewContent = (
                                                  <div style={{ height: '70vh' }}>
                                                    <iframe
                                                      src={officeViewerUrl}
                                                      style={{ 
                                                        width: '100%', 
                                                        height: '100%',
                                                        border: 'none'
                                                      }}
                                                      title={fileName}
                                                    />
                                                  </div>
                                                );
                                              } else {
                                                previewContent = (
                                                  <div style={{ 
                                                    textAlign: 'center', 
                                                    padding: '40px',
                                                    color: '#666'
                                                  }}>
                                                    <FileTextOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
                                                    <div>此文件类型不支持在线预览</div>
                                                    <div style={{ marginTop: '8px', fontSize: '12px' }}>
                                                      文件类型: {fileExtension?.toUpperCase() || '未知'}
                                                    </div>
                                                    <Button 
                                                      type="primary" 
                                                      style={{ marginTop: '16px' }}
                                                      onClick={() => window.open(fileUrl, '_blank')}
                                                    >
                                                      下载文件
                                                    </Button>
                                                  </div>
                                                );
                                              }
                                              
                                              Modal.info({
                                                title: (
                                                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                                    <span>文件预览: {fileName}</span>
                                                    <Button 
                                                      type="link" 
                                                      size="small"
                                                      onClick={() => window.open(officeViewerUrl || fileUrl, '_blank')}
                                                    >
                                                      在新窗口打开
                                                    </Button>
                                                  </div>
                                                ),
                                                content: previewContent,
                                                width: '80vw',
                                                style: { 
                                                  top: '8vh'
                                                },
                                                okText: '关闭',
                                                icon: null
                                              });
                                            }
                                          }}
                                          disabled={!file.文件链接}
                                        >
                                          预览
                                        </Button>
                                      ]}
                                    >
                                      <List.Item.Meta
                                        avatar={
                                          <div style={{ 
                                            width: 32, 
                                            height: 32, 
                                            background: '#1890ff', 
                                            borderRadius: 4,
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            color: '#fff',
                                            fontSize: '12px',
                                            fontWeight: 'bold'
                                          }}>
                                            {file.文件名 ? file.文件名.split('.').pop()?.toUpperCase().slice(0, 3) || 'DOC' : 'DOC'}
                                          </div>
                                        }
                                        title={
                                          <div style={{ display: 'flex', alignItems: 'center' }}>
                                            <Text strong style={{ marginRight: 8 }}>
                                              {file.文件名 || '未知文件名'}
                                            </Text>
                                            {file.文件链接 && (
                                              <Tag color="green">可访问</Tag>
                                            )}
                                          </div>
                                        }
                                        description={
                                          <div style={{ fontSize: '12px', color: '#666' }}>
                                            {file.文件链接 ? (
                                              <div>
                                                <Text copyable={{ text: file.文件链接 }} style={{ fontSize: '11px' }}>
                                                  {file.文件链接.length > 60 ? 
                                                    `${file.文件链接.substring(0, 60)}...` : 
                                                    file.文件链接
                                                  }
                                                </Text>
                                              </div>
                                            ) : (
                                              <Text type="secondary">无可用链接</Text>
                                            )}
                                          </div>
                                        }
                                      />
                                    </List.Item>
                                  )}
                                />
                              </div>
                            </div>
                          )}
                          
                        {index < parsedData.length - 1 && <Divider />}
                      </div>
                      );
                    })}
                    <div style={{ textAlign: 'right', color: '#999', fontSize: '12px' }}>
                      共 {parsedData.length} 条记录
                    </div>
                  </>
                );
              }
            } catch (e) {
              console.error('解析JSON_DOC失败：', e);
            }
          }
        }
      }
      
      // 如果不符合特殊格式或解析失败，回退到标准表格显示
      return (
        <>
          <Text type="secondary">执行时间: {execTime != null ? execTime.toFixed(3) : '未知'} ms</Text>
          <div style={{ marginTop: 8 }}>
            {renderDataTable(resultData)}
          </div>
        </>
      );
    } catch (error) {
      console.error('渲染公司基本信息时出错：', error);
      // 出错时，回退到普通JSON显示
      return (
        <>
          <Alert
            message="渲染公司信息时出错"
            description={String(error)}
            type="error"
            showIcon
            style={{ marginBottom: 16 }}
          />
          <div>
            <pre style={{ whiteSpace: 'pre-wrap' }}>
              {JSON.stringify(result.result.data, null, 2)}
            </pre>
          </div>
        </>
      );
    }
  };

  // 使用Ant Design Table渲染标准表格数据
  const renderDataTable = (data: any): React.ReactNode => {
    if (!data || !data.columns || !data.data) return null;
    
    const columns = data.columns.map((col: string, index: number) => ({
      title: col,
      dataIndex: index.toString(),
      key: index.toString(),
      ellipsis: { showTitle: false },
      render: (text: any) => (
        <Typography.Paragraph 
          ellipsis={{ rows: 3, expandable: true, symbol: '更多' }}
          style={{ marginBottom: 0 }}
        >
          {text === null || text === undefined ? '' : text}
        </Typography.Paragraph>
      )
    }));
    
    const tableData = data.data.map((row: any[], rowIndex: number) => {
      const rowData: any = { key: rowIndex };
      row.forEach((cell, cellIndex) => {
        rowData[cellIndex.toString()] = cell;
      });
      return rowData;
    });
    
    // 根据数据量自动配置分页
    const paginationConfig = tableData.length > 10 ? {
      pageSize: 10,
      showSizeChanger: true,
      showTotal: (total: number) => `共 ${total} 条记录`,
      pageSizeOptions: ['10', '20', '50', '100'],
    } : false;
    
    return (
      <>
        <Table 
          columns={columns} 
          dataSource={tableData} 
          size="small" 
          pagination={paginationConfig}
          bordered
        />
        {!paginationConfig && (
          <div style={{ marginTop: 8, textAlign: 'right' }}>
            <Text type="secondary">共 {data.count || tableData.length} 条记录</Text>
          </div>
        )}
      </>
    );
  };
  
  // 渲染SQL查询结果为表格
  const renderSQLResultTable = (data: any): React.ReactNode => {
    try {
      console.log('渲染SQL结果：', data);
      
      // 支持原始结果对象中含有 results 和 columns 字段
      if (data && typeof data === 'object' && Array.isArray(data.results)) {
        // 构建表头
        const cols = (data.columns || (data.results.length > 0 ? Object.keys(data.results[0]) : [])).map((col: string) => ({
          title: col,
          dataIndex: col,
          key: col,
          ellipsis: { showTitle: false },
          render: (text: any) => (
            <Typography.Paragraph ellipsis={{ rows: 3, expandable: true, symbol: '更多' }} style={{ marginBottom: 0 }}>
              {text === null || text === undefined ? '' : String(text)}
            </Typography.Paragraph>
          )
        }));
        // 构建数据
        const tableData = data.results.map((row: any, index: number) => ({ ...row, key: index }));
        
        // 根据数据量自动配置分页
        const paginationConfig = tableData.length > 10 ? {
          pageSize: 10,
          showSizeChanger: true,
          showTotal: (total: number) => `共 ${total} 条记录`,
          pageSizeOptions: ['10', '20', '50', '100'],
        } : false;
        
        return (
          <>
            <Table 
              columns={cols} 
              dataSource={tableData} 
              size="small" 
              pagination={paginationConfig} 
              bordered 
            />
            {!paginationConfig && (
              <div style={{ marginTop: 8, textAlign: 'right' }}>
                <Text type="secondary">共 {tableData.length} 条记录</Text>
              </div>
            )}
          </>
        );
      }
       // 如果数据是对象而不是字符串，直接处理为表格
       if (data && typeof data === 'object' && !Array.isArray(data)) {
         // 如果有rows属性，可能是标准SQL结果格式
         if (data.rows && Array.isArray(data.rows)) {
           console.log('检测到标准SQL结果格式 (rows)');
           const columns = data.columns || Object.keys(data.rows[0] || {}).map(k => ({
             title: k,
             dataIndex: k,
             key: k
           }));
          
           // 根据数据量自动配置分页
           const rowsData = data.rows.map((row: any, index: number) => ({
             ...row,
             key: index
           }));
           
           const paginationConfig = rowsData.length > 10 ? {
             pageSize: 10,
             showSizeChanger: true,
             showTotal: (total: number) => `共 ${total} 条记录`,
             pageSizeOptions: ['10', '20', '50', '100'],
           } : false;
           
           return (
             <>
               <Table 
                 columns={columns.map((col: any) => ({
                   title: typeof col === 'string' ? col : col.title || col.name,
                   dataIndex: typeof col === 'string' ? col : col.dataIndex || col.name,
                   key: typeof col === 'string' ? col : col.key || col.name,
                   ellipsis: { showTitle: false },
                   render: (text: any) => (
                     <Typography.Paragraph 
                       ellipsis={{ rows: 3, expandable: true, symbol: '更多' }}
                       style={{ marginBottom: 0 }}
                     >
                       {text === null || text === undefined ? '' : String(text)}
                     </Typography.Paragraph>
                   )
                 }))} 
                 dataSource={rowsData}
                 size="small"
                 pagination={paginationConfig}
                 bordered
               />
               {!paginationConfig && (
                 <div style={{ marginTop: 8, textAlign: 'right' }}>
                   <Text type="secondary">共 {rowsData.length} 条记录</Text>
                 </div>
               )}
             </>
           );
         }
         
         // 如果有data属性，也可能是标准结果格式
         if (data.data && Array.isArray(data.data)) {
           console.log('检测到标准SQL结果格式 (data)');
           return renderDataTable(data);
         }
         
         // 如果是普通对象但不是特定格式，尝试展示为JSON
         return (
           <div style={{ 
             whiteSpace: 'pre-wrap', 
             fontFamily: 'monospace', 
             background: '#f5f5f5', 
             padding: 8, 
             borderRadius: 4, 
             maxHeight: '300px', 
             overflow: 'auto'
           }}>
             {JSON.stringify(data, null, 2)}
           </div>
         );
       }
       
       // 如果数据是数组，可能是记录列表
       if (Array.isArray(data)) {
         console.log('检测到数组数据，尝试渲染为表格');
         if (data.length > 0) {
           // 提取列名（使用第一行的键）
           const firstRow = data[0];
           const columns = Object.keys(firstRow).map(col => ({
             title: col,
             dataIndex: col,
             key: col,
             ellipsis: { showTitle: false },
             render: (text: any) => (
               <Typography.Paragraph 
                 ellipsis={{ rows: 3, expandable: true, symbol: '更多' }}
                 style={{ marginBottom: 0 }}
               >
                 {text === null || text === undefined ? '' : String(text)}
               </Typography.Paragraph>
             )
           }));
           
           // 为每行添加key
           const tableData = data.map((row: any, index: number) => ({
             ...row,
             key: index
           }));
           
           // 根据数据量自动配置分页
           const paginationConfig = tableData.length > 10 ? {
             pageSize: 10,
             showSizeChanger: true,
             showTotal: (total: number) => `共 ${total} 条记录`,
             pageSizeOptions: ['10', '20', '50', '100'],
           } : false;
           
           return (
             <>
               <Table 
                 columns={columns} 
                 dataSource={tableData}
                 size="small"
                 pagination={paginationConfig}
                 bordered
               />
               {!paginationConfig && (
                 <div style={{ marginTop: 8, textAlign: 'right' }}>
                   <Text type="secondary">共 {data.length} 条记录</Text>
                 </div>
               )}
             </>
           );
         } else {
           return <Empty description="查询结果为空" />;
         }
       }
       
       // 如果数据是字符串，尝试解析
       if (typeof data === 'string') {
         // 尝试解析为JSON
         try {
           if (data.trim().startsWith('[') || data.trim().startsWith('{')) {
             const parsedData = JSON.parse(data);
             console.log('成功将字符串解析为JSON：', parsedData);
             
             // 递归调用以处理解析后的结果
             return renderSQLResultTable(parsedData);
           }
         } catch (e) {
           console.log('JSON解析失败，尝试其他方式', e);
         }
         
         // 如果是表格形式的文本（例如CSV格式）
         const lines = data.split('\n').filter(line => line.trim());
         if (lines.length > 1) {
           const separator = lines[0].includes(',') ? ',' : 
                           lines[0].includes('\t') ? '\t' : 
                           lines[0].includes('|') ? '|' : null;
           
           if (separator) {
             console.log('检测到分隔符文本数据');
             // 解析列和数据
             const columns = lines[0].split(separator).map((col, index) => ({
               title: col.trim(),
               dataIndex: index.toString(),
               key: index.toString(),
               ellipsis: { showTitle: false },
               render: (text: any) => (
                 <Typography.Paragraph 
                   ellipsis={{ rows: 3, expandable: true, symbol: '更多' }}
                   style={{ marginBottom: 0 }}
                 >
                   {text === null || text === undefined ? '' : text}
                 </Typography.Paragraph>
               )
             }));
             
             const tableData = lines.slice(1).map((line, rowIndex) => {
               const cells = line.split(separator);
               const rowData: any = { key: rowIndex };
               cells.forEach((cell, cellIndex) => {
                 rowData[cellIndex.toString()] = cell.trim();
               });
               return rowData;
             });
             
             return (
               <>
                 <Table 
                   columns={columns} 
                   dataSource={tableData}
                   size="small"
                   pagination={false}
                   bordered
                 />
                 <div style={{ marginTop: 8, textAlign: 'right' }}>
                   <Text type="secondary">共 {tableData.length} 条记录</Text>
                 </div>
               </>
             );
           }
         }
         
         // 如果没有特定格式，显示原始内容
         return (
           <div style={{ 
             whiteSpace: 'pre-wrap', 
             fontFamily: 'monospace', 
             background: '#f5f5f5', 
             padding: 8, 
             borderRadius: 4 
           }}>
             {data}
           </div>
         );
       }
       
       // 对于null或undefined，显示提示
       if (data === null || data === undefined) {
         return <Empty description="查询结果为空" />;
       }
       
       // 其他类型，显示JSON字符串
       return (
         <div style={{ 
           whiteSpace: 'pre-wrap', 
           fontFamily: 'monospace', 
           background: '#f5f5f5', 
           padding: 8, 
           borderRadius: 4 
         }}>
           {JSON.stringify(data, null, 2)}
         </div>
       );
     } catch (error) {
       console.error('渲染SQL结果表格时出错：', error);
       // 出错时，显示错误信息和原始数据
       return (
         <>
           <Alert 
             message="渲染SQL结果时发生错误" 
             description={String(error)}
             type="error" 
             showIcon 
             style={{ marginBottom: 8 }}
           />
           <div style={{ 
             whiteSpace: 'pre-wrap', 
             fontFamily: 'monospace', 
             background: '#f5f5f5', 
             padding: 8, 
             borderRadius: 4 
           }}>
             {typeof data === 'string' ? data : JSON.stringify(data, null, 2)}
           </div>
         </>
       );
     }
   };

  // 判断数据是否符合表格格式
  const isTableData = (data: any): boolean => {
    if (!data) return false;
    
    try {
      // 检查是否具有表格所需的所有字段
      return (
        data.columns && Array.isArray(data.columns) && data.columns.length > 0 &&
        data.data && Array.isArray(data.data) &&
        typeof data.count !== 'undefined'
      );
    } catch (error) {
      console.error('判断表格数据时出错：', error);
      return false;
    }
  };

  // 判断是否为SQL查询结果
  const isSQLQueryResult = (result: any): boolean => {
    try {
      // 检查是否含有SQL专用的results和columns字段
      if (result.result?.results && Array.isArray(result.result.results) && 
          (result.result?.columns || result.result?.results.length > 0)) {
        console.log('SQL检测：检测到results字段');
        return true;
      }
      
      // 检查工具名称是否包含SQL或查询关键词
      const toolName = (result.tool_name || "").toLowerCase();
      if (toolName.includes("sql") || toolName.includes("查询") || toolName.includes("query")) {
        console.log('SQL检测：工具名称匹配');
        return true;
      }
      
      // 检查是否有SQL字段
      if (result.result?.sql && typeof result.result.sql === 'string') {
        console.log('SQL检测：发现SQL语句字段');
        return true;
      }
      
      // 检查数据显示格式
      const displayFormat = (result.result?.display_format || "").toLowerCase();
      if (displayFormat === "sql" || displayFormat === "db" || displayFormat === "database" || displayFormat === "table") {
        console.log('SQL检测：显示格式匹配');
        return true;
      }
      
      // 检查数据内容特征
      const data = result.result?.data;
      if (data) {
        if (typeof data === 'string' && (
          data.includes('SELECT') && data.includes('FROM') ||
          data.includes('select') && data.includes('from')
        )) {
          console.log('SQL检测：SQL语句匹配');
          return true;
        }
        
        // 检查是否为典型的SQL错误
        if (typeof data === 'string' && (
          data.includes('ORA-') || 
          data.includes('SQL error') || 
          data.includes('SQL Error') ||
          data.includes('SQL执行')
        )) {
          console.log('SQL检测：SQL错误匹配');
          return true;
        }
        
        // 检查是否为典型的SQL结果结构
        if (typeof data === 'object') {
          if (Array.isArray(data) && data.length > 0 && typeof data[0] === 'object') {
            console.log('SQL检测：数据是记录数组');
            return true;
          }
          
          if (data.rows && Array.isArray(data.rows)) {
            console.log('SQL检测：数据有rows属性');
            return true;
          }
          
          if (data.columns && Array.isArray(data.columns)) {
            console.log('SQL检测：数据有columns属性');
            return true;
          }
          
          if (data.results && Array.isArray(data.results)) {
            console.log('SQL检测：数据有results属性');
            return true;
          }
        }
      }
      
      // 检查工具执行结果的类型
      if (result.result?.type === 'sql' || result.result?.type === 'database' || result.result?.type === 'table') {
        console.log('SQL检测：结果类型匹配');
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('判断SQL查询结果时出错：', error);
      return false;
    }
  };

  // 渲染分析报告
  const renderFinalReport = () => {
    if (!finalReport) return null;
    
    // 查找报告生成步骤以获取耗时信息
    const reportStep = analysisSteps.find(step => step.id === 'report_generated');
    const reportExecutionTime = toolResults.find(result => result.step_id === 'report_generation')?.execution_time;
    
    return (
      <Card 
        bordered={false} 
        style={{ marginTop: 16, marginLeft: 24 }}
        size="small"
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span>{t('analysis:results.analysisReport')}</span>
            {reportExecutionTime && <Text type="secondary">{t('analysis:tools.executionTime', { time: typeof reportExecutionTime === 'number' ? reportExecutionTime.toFixed(3) : parseFloat(String(reportExecutionTime)).toFixed(3) })}</Text>}
          </div>
        }
        className="report-card"
      >
        <ReactMarkdown
          children={finalReport}
          remarkPlugins={[remarkGfm]}
          rehypePlugins={[rehypeRaw]}
          components={{
            // 自定义表格渲染
            table: ({...props}) => (
              <div style={{ overflowX: 'auto', marginBottom: '16px' }}>
                <table className="markdown-table" {...props} />
              </div>
            ),
            // 自定义表格头部渲染
            thead: ({...props}) => (
              <thead className="markdown-thead" {...props} />
            ),
            // 自定义表格行渲染
            tr: ({...props}) => (
              <tr className="markdown-tr" {...props} />
            ),
            // 自定义表格单元格渲染
            td: ({...props}) => (
              <td className="markdown-td" {...props} />
            ),
            // 自定义表格头部单元格渲染
            th: ({...props}) => (
              <th className="markdown-th" {...props} />
            )
          }}
        />
        
        {streamComplete && (
          <div style={{ marginTop: 16, textAlign: 'right' }}>
            <Space>
              <Button 
                type="primary"
                onClick={() => navigate(`/analysis/${analysisId}`)}
                size="small"
              >
                查看详情
              </Button>
              <Button 
                onClick={() => {
                  setStreaming(false);
                  setStreamComplete(false);
                  setAnalysisSteps([]);
                  setIntentAnalysis(null);
                  setToolResults([]);
                  setFinalReport('');
                  setClarificationHistory([]); // 清空澄清历史
                  setClarificationRequest(null);
                  setClarificationAnswers({});
                  // form.resetFields();
                }}
                size="small"
              >
                重新分析
              </Button>
            </Space>
          </div>
        )}
      </Card>
    );
  };

  // 获取特定步骤的工具执行结果
  const getStepResult = (stepId: string) => {
    return toolResults.find(result => result.step_id === stepId);
  };

  // 判断步骤是否为当前正在执行的步骤
  const isCurrentStep = (stepId: string) => {
    // 检查步骤是否已经完成
    const step = analysisSteps.find(s => s.id === stepId);
    if (step && step.status === 'finish') {
      return false;
    }
    
    // 特殊处理生成报告步骤 - 如果report_generated已存在，则generating_report不再是当前步骤
    if (stepId === 'generating_report') {
      return currentStep === 'generating_report' && !analysisSteps.some(s => s.id === 'report_generated');
    }
    
    // 特殊处理正在分析用户意图步骤 - 只有当currentStep是intent_analysis_started时才显示loading
    if (stepId === 'intent_analysis_started') {
      return currentStep === 'intent_analysis_started';
    }
    
    // 特殊处理等待LLM规划步骤
    if (stepId === 'waiting_for_planning') {
      return currentStep === 'waiting_for_planning';
    }
    
    return currentStep === stepId && streaming;
  };

  // 判断步骤是否是将来的步骤(还未开始)
  const isFutureStep = (index: number) => {
    // 如果currentStep为空，说明没有正在执行的步骤
    // 此时所有已完成的步骤都应该显示完成状态，而不是future状态
    if (!currentStep) {
      const lastFinishedStep = analysisSteps.findIndex(s => s.status !== 'finish');
      // 如果所有步骤都已完成，或者当前步骤索引小于等于最后一个完成的步骤
      // 则不应该显示为future状态
      return lastFinishedStep === -1 ? false : index > lastFinishedStep;
    }
    
    const currentIndex = analysisSteps.findIndex(s => s.id === currentStep);
    return index > currentIndex && streaming;
  };

  // 专门处理results和columns格式的SQL结果
  const renderSQLResultsTable = (results: any[], columns: string[]): React.ReactNode => {
    if (!results || !Array.isArray(results) || results.length === 0) {
      return <Empty description="查询结果为空" />;
    }
    
    const tableColumns = columns?.map((col: string) => ({
      title: col,
      dataIndex: col,
      key: col,
      ellipsis: { showTitle: false },
      render: (text: any) => (
        <Typography.Paragraph 
          ellipsis={{ rows: 3, expandable: true, symbol: '更多' }}
          style={{ marginBottom: 0 }}
        >
          {text === null || text === undefined ? '' : String(text)}
        </Typography.Paragraph>
      )
    })) || Object.keys(results[0]).map(key => ({
      title: key,
      dataIndex: key,
      key: key,
      ellipsis: { showTitle: false },
      render: (text: any) => (
        <Typography.Paragraph 
          ellipsis={{ rows: 3, expandable: true, symbol: '更多' }}
          style={{ marginBottom: 0 }}
        >
          {text === null || text === undefined ? '' : String(text)}
        </Typography.Paragraph>
      )
    }));
    
    const tableData = results.map((row: any, index: number) => ({
      ...row,
      key: index
    }));
    
    // 根据数据量自动配置分页
    const paginationConfig = tableData.length > 10 ? {
      pageSize: 10,
      showSizeChanger: true,
      showTotal: (total: number) => `共 ${total} 条记录`,
      pageSizeOptions: ['10', '20', '50', '100'],
    } : false;
    
    return (
      <>
        <Table 
          columns={tableColumns} 
          dataSource={tableData}
          size="small"
          pagination={paginationConfig}
          bordered
        />
        {!paginationConfig && (
          <div style={{ marginTop: 8, textAlign: 'right' }}>
            <Text type="secondary">共 {tableData.length} 条记录</Text>
          </div>
        )}
      </>
    );
  };

  // 渲染澄清请求内容
  const renderClarificationContent = (clarificationData: any) => {
    if (!clarificationData) return null;
    
    return (
      <Card 
        bordered={false} 
        style={{ marginTop: 16, marginLeft: 24, background: '#f9f9f9' }}
        size="small"
        title="澄清请求详情"
      >
        {/* 显示澄清原因 */}
        {clarificationData.context && (
          <div style={{ marginBottom: 12 }}>
            <Text strong>澄清原因：</Text>
            <div style={{ 
              marginTop: 4, 
              padding: '8px 12px', 
              background: '#e6f7ff', 
              borderRadius: 4, 
              borderLeft: '3px solid #1890ff' 
            }}>
              {typeof clarificationData.context === 'string' 
                ? clarificationData.context 
                : clarificationData.context.reason || clarificationData.context.message || '需要更多信息'}
            </div>
          </div>
        )}
        
        {/* 显示问题列表 */}
        {clarificationData.questions && clarificationData.questions.length > 0 && (
          <div>
            <Text strong>需要澄清的问题：</Text>
            <List
              size="small"
              style={{ marginTop: 8 }}
              dataSource={clarificationData.questions}
              renderItem={(question: any, index: number) => (
                <List.Item>
                  <div style={{ width: '100%' }}>
                    <div style={{ fontWeight: 500, marginBottom: 4 }}>
                      {index + 1}. {question.question}
                      {question.required && <span style={{ color: 'red', marginLeft: 4 }}>*</span>}
                    </div>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      字段类型：{question.field_type || 'text'}
                      {question.options && question.options.length > 0 && (
                        <span>，选项：{question.options.join(', ')}</span>
                      )}
                    </div>
                    {question.description && (
                      <div style={{ 
                        fontSize: '12px', 
                        color: '#666', 
                        marginTop: 4, 
                        padding: '4px 8px', 
                        background: '#f5f5f5', 
                        borderRadius: 4 
                      }}>
                        💡 {question.description}
                      </div>
                    )}
                  </div>
                </List.Item>
              )}
            />
          </div>
        )}
      </Card>
    );
  };

  // 渲染用户澄清回答
  const renderClarificationAnswers = (answers: Record<string, any>, questions: any[]) => {
    if (!answers || Object.keys(answers).length === 0) return null;
    
    return (
      <Card 
        bordered={false} 
        style={{ marginTop: 16, marginLeft: 24, background: '#f6ffed' }}
        size="small"
      >
        <Descriptions size="small" column={1}>
          {Object.entries(answers).map(([fieldName, value]) => {
            const question = questions?.find((q: any) => q.field_name === fieldName);
            const questionText = question?.question || fieldName;
            
            return (
              <Descriptions.Item 
                key={fieldName}
                label={<Text strong>{questionText}</Text>}
              >
                <Text>{value}</Text>
              </Descriptions.Item>
            );
          })}
        </Descriptions>
      </Card>
    );
  };

  // 渲染LLM分析结果
  const renderLlmResult = (step: any) => {
    if (!step || !step.resultKey || !mcpResults[step.resultKey]) return null;
    
    const result = mcpResults[step.resultKey];
    
    // 渲染工具参数信息 - 简化版本，不显示详细参数
    const renderLlmParameters = () => {
      return null; // 不显示参数详情，保持界面简洁
    };
    
    // 根据操作类型生成卡片标题
    let cardTitle = t('analysis:tools.executionResult');
    if (step.executionRecord) {
      if (step.executionRecord.operation_type === 'resource') {
        const resourcePath = step.executionRecord.details?.resource_path || t('analysis:tools.unknownResource');
        cardTitle = t('analysis:tools.resourceResult', { resourcePath });
      } else if (step.executionRecord.operation_type === 'tool') {
        const toolName = step.executionRecord.details?.tool_name || t('analysis:tools.unknownTool');
        cardTitle = t('analysis:tools.toolExecutionResult', { toolName });
      }
    }
    // 获取执行时间
    const timestamp = step.executionRecord?.timestamp ? new Date(step.executionRecord.timestamp) : null;
    const hasExecutionTime = result.execution_time || (typeof result === 'object' && result.result?.execution_time);
    const executionTime = hasExecutionTime ? 
      (result.execution_time || result.result?.execution_time) : 
      null;
    
    // 优先显示执行时间，其次显示时间戳
    const formattedTime = executionTime ?
      t('analysis:tools.executionTime', { time: typeof executionTime === 'number' ? executionTime.toFixed(3) : parseFloat(String(executionTime)).toFixed(3) }) :
      (timestamp ?
        `${timestamp.toLocaleDateString()} ${timestamp.toLocaleTimeString()}` :
        '未知时间');
    
    // 渲染SQL查询结果
    // 基于步骤名或结果中返回的SQL判断
    if (step.tool_name === '自动SQL查询' || result.sql) {
      // 获取SQL语句
      const sql = step.parameters?.sql || result.sql || '';
      return (
        <Card
          bordered={false}
          style={{ marginTop: 16, marginLeft: 24 }}
          size="small"
          title={
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <span>{cardTitle}</span>
              <Text type="secondary">{formattedTime}</Text>
            </div>
          }
        >
          {renderLlmParameters()}
          {sql && (
            <div style={{ marginBottom: 12 }}>
              <Text strong>执行的SQL:</Text>
              <div style={{
                whiteSpace: 'pre-wrap',
                fontFamily: 'monospace',
                background: '#f0f5ff',
                padding: 8,
                borderRadius: 4,
                border: '1px solid #d6e4ff',
                marginTop: 4
              }}>
                {sql}
              </div>
            </div>
          )}
          <div style={{ marginTop: 8 }}>
            {renderSQLResultTable(result)}
          </div>
        </Card>
      );
    }
    
    // 渲染资源结果（通常是schema信息）
    if (step.executionRecord?.operation_type === 'resource') {
      return (
        <Card 
          bordered={false} 
          style={{ marginTop: 16, marginLeft: 24 }}
          size="small"
          title={
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <span>{cardTitle}</span>
              <Text type="secondary">{formattedTime}</Text>
            </div>
          }
        >
          {renderLlmParameters()}
          <div style={{ 
            whiteSpace: 'pre-wrap', 
            fontFamily: 'monospace', 
            background: '#f8f8f8', 
            padding: 8, 
            borderRadius: 4,
            border: '1px solid #f0f0f0',
            maxHeight: '300px',
            overflow: 'auto'
          }}>
            <JsonView 
              data={result} 
              shouldExpandNode={() => true} 
            />
          </div>
        </Card>
      );
    }
    
    // 新增：特殊工具渲染优先级，使用 step.tool_name
    const toolName = step.tool_name || step.executionRecord?.details?.tool_name;
    
    // 意图分析工具专用渲染
    if (toolName === '意图分析' || step.hasIntentData) {
      return (
        <Card 
          bordered={false} 
          style={{ marginTop: 16, marginLeft: 24 }} 
          size="small"
          title={
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <span>用户意图分析结果</span>
              <Text type="secondary">{formattedTime}</Text>
            </div>
          }
        >
          {renderLlmParameters()}
          {renderIntentAnalysis && !showIntentConfirmation && renderIntentAnalysis()}
        </Card>
      );
    }
    
    // 意图确认工具专用渲染
    if (toolName === '意图确认') {
      const confirmationResult = result || {};
      return (
        <Card
          bordered={false}
          style={{
            marginTop: 16,
            marginLeft: 24,
            background: '#fff',
            border: '1px solid #f0f0f0',
            borderRadius: 8,
            boxShadow: '0 2px 8px rgba(0,0,0,0.06)'
          }}
          size="small"
          title={
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <span>
                <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} />
                意图确认完成
              </span>
              <Text type="secondary">{formattedTime}</Text>
            </div>
          }
        >
          {renderLlmParameters()}

          {/* 使用简单格式显示问答详情 */}
          <div style={{ fontSize: '13px', lineHeight: '1.8' }}>
            {/* 显示问答详情 */}
            {confirmationResult.intent_analysis?.clarification_questions &&
             confirmationResult.clarification_answers &&
             Object.keys(confirmationResult.clarification_answers).length > 0 && (
              <div>
                {confirmationResult.intent_analysis.clarification_questions.map((question: any, index: number) => {
                  const answer = confirmationResult.clarification_answers[question.id];
                  if (!answer) return null;

                  return (
                    <div key={question.id || index} style={{ marginBottom: 12 }}>
                      <div style={{ marginBottom: 4 }}>
                        <Text strong>问题 {index + 1}：</Text>
                        <Text style={{ marginLeft: 8 }}>{question.question}</Text>
                      </div>
                      <div>
                        <Text strong>回答：</Text>
                        <Text style={{ marginLeft: 8 }}>{answer}</Text>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}

            {/* 显示用户补充说明（如果没有澄清问题） */}
            {confirmationResult.user_adjustment &&
             !confirmationResult.clarification_answers && (
              <div style={{ marginBottom: 12 }}>
                <Text strong>用户回答：</Text>
                <Text style={{ marginLeft: 8, whiteSpace: 'pre-line' }}>
                  {confirmationResult.user_adjustment}
                </Text>
              </div>
            )}
          </div>
        </Card>
      );
    }
    
    // 智能图表生成工具专用渲染
    if (toolName?.includes('智能图表生成') || toolName?.includes('图表生成') || 
        result?.display_format === "chart" || result?.data?.chart_type === "echarts") {
      return (
        <Card 
          bordered={false} 
          style={{ marginTop: 16, marginLeft: 24 }} 
          size="small"
          title={
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <span>{cardTitle}</span>
              <Text type="secondary">{formattedTime}</Text>
            </div>
          }
        >
          {renderLlmParameters()}
          {renderChartResult({ result })}
        </Card>
      );
    }
    
    // 资金流转分析图表
    if (toolName?.includes('资金流转')) {
      return (
        <Card bordered={false} style={{ marginTop: 16, marginLeft: 24 }} size="small"
          title={
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <span>{cardTitle}</span><Text type="secondary">{formattedTime}</Text>
            </div>
          }
        >
          {renderLlmParameters()}
          {renderFundTransferResult({ result })}
        </Card>
      );
    }
    // 公司基本信息查询
    if (toolName?.includes('公司基本信息')) {
      return (
        <Card bordered={false} style={{ marginTop: 16, marginLeft: 24 }} size="small"
          title={
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <span>{cardTitle}</span><Text type="secondary">{formattedTime}</Text>
            </div>
          }
        >
          {renderLlmParameters()}
          {renderCompanyInfoResult({ result })}
        </Card>
      );
    }
    // 通用表格数据
    if (result.data && isTableData(result.data)) {
      return (
        <Card bordered={false} style={{ marginTop: 16, marginLeft: 24 }} size="small"
          title={
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <span>{cardTitle}</span><Text type="secondary">{formattedTime}</Text>
            </div>
          }
        >
          {renderLlmParameters()}
          {renderDataTable(result.data)}
        </Card>
      );
    }
    
    // 默认渲染为JSON查看器
    return (
      <Card 
        bordered={false} 
        style={{ marginTop: 16, marginLeft: 24 }}
        size="small"
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span>{cardTitle}</span>
            <Text type="secondary">{formattedTime}</Text>
          </div>
        }
      >
        {renderLlmParameters()}
        <div style={{ 
          whiteSpace: 'pre-wrap', 
          fontFamily: 'monospace', 
          background: '#f8f8f8', 
          padding: 8, 
          borderRadius: 4,
          border: '1px solid #f0f0f0',
          maxHeight: '300px',
          overflow: 'auto'
        }}>
          {typeof result === 'string' ? (
            result
          ) : (
            <JsonView 
              data={result} 
              shouldExpandNode={() => true} 
            />
          )}
        </div>
      </Card>
    );
  };

  const handleClarificationSubmit = async () => {
    if (!clarificationRequest) return;
    
    // 收集用户的答案
    const answers: Record<string, any> = {};
    clarificationRequest.questions.forEach((q: any) => {
      const value = clarificationAnswers[q.field_name];
      if (value !== undefined && value !== '') {
        answers[q.field_name] = value;
      }
    });
    
    // 检查是否所有必填字段都已填写
    const missingFields = clarificationRequest.questions.filter((q: any) => 
      q.required && (!answers[q.field_name] || answers[q.field_name] === '')
    );
    
    if (missingFields.length > 0) {
      message.error(`请填写必填字段: ${missingFields.map((f: any) => f.question).join(', ')}`);
      return;
    }
    
    // 保存智能交互历史
    const clarificationRecord = {
      timestamp: new Date().toISOString(),
      request: clarificationRequest,
      answers: answers,
      questionsText: clarificationRequest.questions.map((q: any) => q.question),
      answersText: Object.entries(answers).map(([key, value]) => {
        const question = clarificationRequest.questions.find((q: any) => q.field_name === key);
        return `${question?.question || key}: ${value}`;
      })
    };
    
    setClarificationHistory(prev => [...prev, clarificationRecord]);
    
    // 关闭对话框
    setShowClarificationDialog(false);
    
    // 更新步骤状态，添加智能交互历史数据
    setAnalysisSteps((prev: any[]) => {
      return prev.map(step => {
        if (step.id === 'clarification_request') {
          return {
            ...step,
            status: 'finish',
            description: '已收到用户补充信息，继续分析...',
            clarificationData: {
              request: clarificationRequest,
              answers: answers
            }
          };
        }
        return step;
      });
    });
    
    // 添加用户回答步骤到时间线
    setAnalysisSteps((prev: any[]) => [
      ...prev,
      {
        id: 'clarification_answered',
        name: '用户提供补充信息',
        status: 'finish',
        time: new Date().toISOString(),
        clarificationAnswers: answers,
        clarificationQuestions: clarificationRequest.questions
      }
    ]);
    
    // 清空当前的智能交互请求和答案
    setClarificationRequest(null);
    setClarificationAnswers({});
    
    // 构建更加智能的交互回复内容
    const originalQuery = form.getFieldValue('query') || '';
    const clarificationText = constructClarificationResponse(clarificationRequest, answers, originalQuery);
    
    console.log('智能交互回复内容:', clarificationText);
    console.log('当前分析ID:', analysisId);
    
    try {
      // 关闭当前的EventSource连接
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
        eventSourceRef.current = null;
      }
      
      // 添加继续分析步骤 - 但不重置整个分析步骤列表
      setAnalysisSteps((prev: any[]) => [
        ...prev,
        {
          id: 'continue_analysis',
          name: '基于补充信息继续分析',
          status: 'process',
          time: new Date().toISOString(),
          description: '正在整合用户补充信息并继续分析...'
        }
      ]);
      
      // 重新设置streaming状态为true
      setStreaming(true);
      
      // 重新启动分析，使用增强的查询
      // 继续分析，传递智能交互回复和分析ID
      const analysisData: any = {
        project_id: selectedProjectId,
        query: clarificationText, // 使用构建的智能交互回复
        continue_analysis_id: analysisId, // 传递当前分析ID用于继续
        max_planning_rounds: 25 // 可以根据需要调整
      };
      
      // 设置当前步骤为继续分析
      setCurrentStep('continue_analysis');
      
      // 继续LLM分析 - 重要：不要重置streaming状态，保持为true
      eventSourceRef.current = llmAnalysisApiExtended.executeStreamAnalysis(
        analysisData,
        {
          onStart: () => {
            console.log('继续分析开始，保持当前会话状态');
            // 不要重置分析步骤，只更新当前步骤状态
          },
          onEvent: (eventType: string, eventData: any) => {
            // 处理继续分析的事件 - 特殊处理以避免重置会话
            console.log('继续分析事件:', eventType, eventData);
            
            // 对于某些事件，我们需要特殊处理以避免重置会话状态
            if (eventType === 'start' || eventType === 'analysis_created') {
              // 跳过这些事件，因为我们是在继续现有分析
              console.log(`跳过继续分析中的${eventType}事件`);
              return;
            }
            
            // 对于analysis_continued事件，更新步骤状态
            if (eventType === 'analysis_continued') {
              setAnalysisSteps((prev: any[]) => prev.map(step => {
                if (step.id === 'continue_analysis') {
                  return {
                    ...step,
                    status: 'finish',
                    description: `已恢复分析上下文，整合用户补充信息后继续分析`
                  };
                }
                return step;
              }));
              return;
            }
            
            // 其他事件正常处理
            handleLlmStreamEvent(eventType, eventData);
          },
          onError: (error: any) => {
            setError('重新启动分析时出错: ' + error);
            // 不再设置 setStreaming(false)，保持分析内容显示
            // setStreaming(false);
          },
          onComplete: (id: string) => {
            // 保持当前的分析ID不变，因为我们是在继续同一个分析
            console.log('继续分析完成，分析ID保持不变:', analysisId);
            setStreaming(false);
            setStreamComplete(true);
          }
        }
      );
    } catch (error: any) {
      console.error('继续分析时出错:', error);
      message.error('继续分析失败: ' + (error.message || '未知错误'));
      setStreaming(false);
    }
  };

  // 构建智能的澄清回复内容
  // 处理意图确认提交
  const handleIntentConfirmationSubmit = async () => {
    if (!intentConfirmation || !analysisId) return;
    
    try {
      console.log('提交意图确认:', { intentAdjustment, analysisId });
      
      // 如果用户没有提供调整意见，使用默认确认信息
      const finalAdjustment = intentAdjustment.trim() || "";
      
             // 继续分析，传入用户的意图调整
       const eventSource = llmAnalysisApiExtended.executeStreamAnalysis(
         {
           project_id: selectedProjectId,
           query: finalAdjustment,
           continue_analysis_id: analysisId,
           max_planning_rounds: 25
         },
        {
          onStart: () => {
            console.log('意图确认后继续分析开始');
          },
          onEvent: handleLlmStreamEvent,
          onError: (error: any) => {
            console.error('意图确认后继续分析出错:', error);
            message.error('继续分析失败，请重试');
          },
          onComplete: (analysisId: any) => {
            console.log('意图确认后分析完成:', analysisId);
          }
        }
      );
      
             // 保存EventSource以便取消
       eventSourceRef.current = eventSource;
      
    } catch (error) {
      console.error('提交意图确认失败:', error);
      message.error('提交失败，请重试');
    }
  };

  const constructClarificationResponse = (clarificationRequest: any, answers: Record<string, any>, originalQuery: string): string => {
    // 如果只有一个问题且是简单的澄清，直接返回答案
    if (clarificationRequest.questions.length === 1) {
      const question = clarificationRequest.questions[0];
      const answer = answers[question.field_name];
      return answer || question.question;
    }
    
    // 多个问题时，构建结构化的回复
    const answerParts: string[] = [];
    
    clarificationRequest.questions.forEach((question: any) => {
      const answer = answers[question.field_name];
      if (answer) {
        // 更自然的表达方式
        if (question.question.includes('什么') || question.question.includes('哪个') || question.question.includes('哪些')) {
          answerParts.push(answer);
        } else {
          answerParts.push(`${question.question.replace(/[？?：:]+$/, '')}: ${answer}`);
        }
      }
    });
    
    return answerParts.join('，');
  };

  // 处理图表生成结果
  const renderChartResult = (result: any): React.ReactNode => {
    try {
      const resultData = result.result.data || result.result;
      
      // 检查是否为多图表结构
      const isMultiChart = resultData && (resultData.chart_type === 'multi_charts' || resultData.display_format === 'multi_chart' || (resultData.total_charts && resultData.total_charts > 1)) && resultData.charts;
      const totalCharts = isMultiChart ? (resultData.charts.length || resultData.total_charts || 0) : 1;
      
      // 检查是否有有效的图表配置
      let hasValidChart = false;
      if (isMultiChart) {
        hasValidChart = resultData.charts.some((chart: any) => {
          const config = chart.config || chart.chart_config; // 支持新旧格式
          const chartType = chart.chart_type || 'echarts';

          // 检查ECharts类型图表
          if (chartType === 'echarts' || chartType === 'line' || chartType === 'bar' || chartType === 'pie' || !chartType || chartType === 'unknown') {
            return config && config.series && Array.isArray(config.series) && config.series.length > 0;
          }

          // 检查Table类型图表
          if (chartType === 'table') {
            return config && config.columns && Array.isArray(config.columns) && config.columns.length > 0 &&
                   config.dataSource && Array.isArray(config.dataSource) && config.dataSource.length > 0;
          }

          return false;
        });
      } else {
        const chartType = resultData.chart_type || 'echarts';

        // 检查ECharts类型图表
        if (chartType === 'echarts' || chartType === 'line' || chartType === 'bar' || chartType === 'pie' || !chartType || chartType === 'unknown') {
          hasValidChart = resultData && resultData.chart_config &&
            resultData.chart_config.series && Array.isArray(resultData.chart_config.series) &&
            resultData.chart_config.series.length > 0;
        }

        // 检查Table类型图表
        if (chartType === 'table') {
          hasValidChart = resultData && resultData.chart_config &&
            resultData.chart_config.columns && Array.isArray(resultData.chart_config.columns) && resultData.chart_config.columns.length > 0 &&
            resultData.chart_config.dataSource && Array.isArray(resultData.chart_config.dataSource) && resultData.chart_config.dataSource.length > 0;
        }
      }

      if (!hasValidChart) {
        return (
          <div style={{ padding: '16px' }}>
            <Alert 
              message="图表配置无效或为空" 
              description="未能生成有效的图表配置"
              type="warning" 
              showIcon 
              style={{ marginBottom: 16 }}
            />
            <div>
              <Text strong>原始数据：</Text>
              <pre style={{ 
                background: '#fff7e6', 
                padding: 12, 
                borderRadius: 6,
                border: '1px solid #ffd591',
                fontSize: '12px',
                maxHeight: '200px',
                overflow: 'auto',
                marginTop: 8
              }}>
                {JSON.stringify(resultData, null, 2)}
              </pre>
            </div>
          </div>
        );
      }

      return (
        <div style={{ padding: '16px' }}>
          <Card 
            size="small" 
            style={{ 
              background: isMultiChart 
                ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' 
                : 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
              border: 'none',
              borderRadius: 12,
              color: 'white'
            }}
            bodyStyle={{ padding: '20px' }}
          >
            <Row justify="space-between" align="middle" gutter={[16, 8]}>
              <Col xs={24} md={16}>
                <Space direction="vertical" size="small" style={{ width: '100%' }}>
                  <div>
                    <Text strong style={{ fontSize: '16px', color: 'white' }}>
                      {isMultiChart ? '📊 智能多图表分析' : '📈 智能图表分析'}
                    </Text>
                  </div>
                  <div>
                    <Text style={{ fontSize: '14px', color: 'rgba(255,255,255,0.9)' }}>
                      {isMultiChart 
                        ? `共生成 ${totalCharts} 个图表` 
                        : '单个图表已生成'
                      }
                    </Text>
                  </div>
                  {resultData.user_query && (
                    <div>
                      <Text style={{ fontSize: '13px', color: 'rgba(255,255,255,0.8)' }}>
                        <strong>查询：</strong>{resultData.user_query}
                      </Text>
                    </div>
                  )}
                  {resultData.generated_reason && (
                    <div>
                      <Text style={{ fontSize: '12px', color: 'rgba(255,255,255,0.7)' }}>
                        <strong>生成原因：</strong>{resultData.generated_reason}
                      </Text>
                    </div>
                  )}
                </Space>
              </Col>
              <Col xs={24} md={8} style={{ textAlign: 'right' }}>
                <Button 
                  type="primary" 
                  ghost
                  icon={<EyeOutlined />}
                  size="large"
                  style={{ 
                    borderRadius: 8,
                    fontWeight: 500,
                    borderColor: 'white',
                    color: 'white',
                    minWidth: '120px'
                  }}
                  onClick={() => {
                    // 创建图表数据用于弹框查看
                    const chartWithId = {
                      ...result.result,
                      data: resultData,
                      chartId: `chart_view_${Date.now()}`,
                      stepId: result.step_id || 'unknown',
                      generatedAt: new Date().toISOString()
                    };
                    setChartData(chartWithId);
                    setChartModalVisible(true);
                  }}
                >
                  {isMultiChart ? '查看所有图表' : '查看图表'}
                </Button>
              </Col>
            </Row>
          </Card>

          {/* 执行时间信息 */}
          <div style={{ textAlign: 'center', padding: '12px 0' }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {isMultiChart ? `共生成 ${totalCharts} 个图表` : '图表生成完成'} | 
              执行时间: {typeof result.execution_time === 'number' ? 
                result.execution_time.toFixed(3) : 
                (result.result?.execution_time ? 
                  parseFloat(String(result.result.execution_time)).toFixed(3) : 
                  '未知'
                )
              } ms
            </Text>
          </div>
        </div>
      );
    } catch (error) {
      console.error('渲染图表结果失败:', error);
      return (
        <div style={{ padding: '16px' }}>
          <Alert 
            message={`渲染图表时出错: ${String(error)}`}
            type="error" 
            showIcon 
            style={{ marginBottom: 16 }}
          />
          <div>
            <Text strong>错误详情：</Text>
            <pre style={{ 
              background: '#fff2f0', 
              padding: 12, 
              borderRadius: 6,
              border: '1px solid #ffccc7',
              fontSize: '12px',
              maxHeight: '200px',
              overflow: 'auto',
              marginTop: 8
            }}>
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        </div>
      );
    }
  };

  // 新增多图表渲染函数
  const renderMultiChartResult = (result: any, resultData: any): React.ReactNode => {
    const charts = resultData.charts || [];
    const totalCharts = charts.length;
    const userQuery = resultData.user_query || '';
    const generatedReason = resultData.generated_reason || '';

    if (totalCharts === 0) {
      return (
        <div style={{ padding: '16px' }}>
          <Alert 
            message="未生成任何图表" 
            description="数据不适合可视化或配置有误"
            type="warning" 
            showIcon 
          />
        </div>
      );
    }

    return (
      <div style={{ padding: '16px' }}>
        <Space direction="vertical" style={{ width: '100%' }} size="large">
          {/* 多图表总体信息头部 */}
          <Card 
            size="small" 
            style={{ 
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              border: 'none',
              borderRadius: 12,
              color: 'white'
            }}
            bodyStyle={{ padding: '20px' }}
          >
            <Row justify="space-between" align="middle" gutter={[16, 8]}>
              <Col xs={24} md={18}>
                <Space direction="vertical" size="small" style={{ width: '100%' }}>
                  <div>
                    <Text strong style={{ fontSize: '16px', color: 'white' }}>
                      📊 智能图表分析 - 共生成 {totalCharts} 个图表
                    </Text>
                  </div>
                  {userQuery && (
                    <div>
                      <Text style={{ fontSize: '14px', color: 'rgba(255,255,255,0.9)' }}>
                        <strong>查询：</strong>{userQuery}
                      </Text>
                    </div>
                  )}
                  {generatedReason && (
                    <div>
                      <Text style={{ fontSize: '13px', color: 'rgba(255,255,255,0.8)' }}>
                        <strong>生成原因：</strong>{generatedReason}
                      </Text>
                    </div>
                  )}
                </Space>
              </Col>
              <Col xs={24} md={6} style={{ textAlign: 'right' }}>
                <Button 
                  type="primary" 
                  ghost
                  icon={<EyeOutlined />}
                  size="middle"
                  style={{ 
                    borderRadius: 8,
                    fontWeight: 500,
                    borderColor: 'white',
                    color: 'white'
                  }}
                  onClick={() => {
                    // 创建多图表数据用于大图查看
                    const multiChartData = {
                      ...result.result,
                      data: resultData,
                      chartId: `multi_chart_${Date.now()}`,
                      stepId: result.step_id || 'unknown',
                      generatedAt: new Date().toISOString()
                    };
                    setChartData(multiChartData);
                    setChartModalVisible(true);
                  }}
                >
                  查看所有图表
                </Button>
              </Col>
            </Row>
          </Card>

          {/* 逐一显示每个图表 */}
          {charts.map((chart: any, index: number) => {
            const chartConfig = chart.chart_config;
            const chartType = chart.chart_type || 'unknown';
            const chartTitle = chart.title || `图表 ${index + 1}`;
            const chartDescription = chart.description || '';

            // 验证单个图表配置
            if (!chartConfig || !chartConfig.series || !Array.isArray(chartConfig.series) || chartConfig.series.length === 0) {
              return (
                <Card 
                  key={`chart-error-${index}`}
                  title={
                    <Space>
                      <Tag color="red">图表 {index + 1}</Tag>
                      <Text strong>{chartTitle}</Text>
                    </Space>
                  }
                  style={{ borderRadius: 8 }}
                >
                  <Alert 
                    message={`图表 ${index + 1} 配置无效：缺少series数据`}
                    type="warning" 
                    showIcon 
                  />
                </Card>
              );
            }

            return (
              <Card 
                key={`chart-${index}`}
                title={
                  <Space>
                    <Tag color="blue">图表 {index + 1}</Tag>
                    <Text strong style={{ fontSize: '16px' }}>{chartTitle}</Text>
                    <Tag color="green" style={{ fontSize: '11px' }}>{chartType}</Tag>
                  </Space>
                }
                extra={
                  <Button 
                    type="text" 
                    icon={<EyeOutlined />}
                    size="small"
                    onClick={() => {
                      // 为单个图表创建查看数据
                      const singleChartData = {
                        success: true,
                        data: {
                          chart_config: chartConfig,
                          chart_type: chartType,
                          description: chartDescription,
                          generated_at: new Date().toISOString()
                        },
                        display_format: "chart",
                        chartId: `single_chart_${index}_${Date.now()}`,
                        stepId: result.step_id || 'unknown',
                        generatedAt: new Date().toISOString()
                      };
                      setChartData(singleChartData);
                      setChartModalVisible(true);
                    }}
                  >
                    单独查看
                  </Button>
                }
                style={{ 
                  borderRadius: 12,
                  boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                  marginBottom: 16
                }}
                bodyStyle={{ padding: '20px' }}
              >
                {/* 图表描述 */}
                {chartDescription && (
                  <div style={{ marginBottom: '16px' }}>
                    <Text style={{ fontSize: '14px', color: '#666' }}>
                      {chartDescription}
                    </Text>
                  </div>
                )}

                {/* 图表显示区域 */}
                <div style={{ 
                  background: '#fafafa',
                  borderRadius: 8,
                  padding: '16px',
                  minHeight: '400px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <ReactECharts
                    option={{
                      ...reviveEchartsFunctions(chartConfig),
                      // 通用的响应式和优化配置
                      grid: {
                        ...chartConfig?.grid,
                        left: '10%',
                        right: '10%',
                        top: '20%',  // 增加顶部空间，为标题和图例留出位置
                        bottom: '20%', // 增加底部空间，为工具栏留出位置
                        containLabel: true
                      },
                      // 确保图例位置合理
                      legend: {
                        ...chartConfig?.legend,
                        top: chartConfig?.legend?.top || '12%', // 图例位置稍微下移
                        type: 'scroll', // 当图例过多时支持滚动
                        orient: 'horizontal'
                      },
                      // 工具提示优化
                      tooltip: {
                        ...chartConfig?.tooltip,
                        trigger: chartConfig?.tooltip?.trigger || 'item',
                        confine: true, // 将tooltip限制在图表区域内
                        backgroundColor: 'rgba(0,0,0,0.8)',
                        borderColor: 'rgba(0,0,0,0.8)',
                        textStyle: {
                          color: '#fff',
                          fontSize: 12
                        }
                      },
                      // 标题优化
                      title: {
                        ...chartConfig?.title,
                        left: 'center',
                        top: '2%', // 标题位置靠上
                        textStyle: {
                          fontSize: 16,
                          fontWeight: 'bold',
                          ...chartConfig?.title?.textStyle
                        }
                      },
                      // 工具栏配置 - 确保在底部不遮挡标题
                      toolbox: {
                        ...chartConfig?.toolbox,
                        show: chartConfig?.toolbox?.show !== false, // 默认显示工具栏
                        orient: 'horizontal',
                        left: 'center',
                        bottom: '2%', // 工具栏位置在底部
                        feature: {
                          saveAsImage: { 
                            show: true,
                            title: '保存为图片'
                          },
                          magicType: {
                            show: true,
                            title: {
                              line: '切换为折线图',
                              bar: '切换为柱状图'
                            },
                            type: ['line', 'bar']
                          },
                          restore: { 
                            show: true,
                            title: '还原'
                          },
                          ...chartConfig?.toolbox?.feature
                        }
                      }
                    }}
                    style={{ 
                      height: '500px', 
                      width: '100%',
                      minHeight: '400px'
                    }}
                    opts={{ 
                      renderer: 'canvas'
                    }}
                    notMerge={true}
                    lazyUpdate={true}
                    onChartReady={(chart: any) => {
                      // 图表加载完成后的优化
                      const resizeChart = () => {
                        if (chart && !chart.isDisposed()) {
                          chart.resize();
                        }
                      };
                      // 监听窗口大小变化
                      window.addEventListener('resize', resizeChart);
                      // 延迟初始化大小调整
                      setTimeout(resizeChart, 100);
                      
                      // 清理函数
                      return () => {
                        window.removeEventListener('resize', resizeChart);
                      };
                    }}
                  />
                </div>

                {/* 图表配置展示（可折叠） */}
                <div style={{ marginTop: '16px' }}>
                  <Collapse 
                    ghost 
                    size="small"
                    style={{ background: 'transparent' }}
                  >
                    <Collapse.Panel 
                      header={
                        <Text style={{ fontSize: '12px', fontWeight: 500 }}>
                          <CodeOutlined style={{ marginRight: 6 }} />
                          查看图表 {index + 1} 配置
                        </Text>
                      } 
                      key={`config-${index}`}
                    >
                      <div style={{ 
                        background: '#f5f5f5', 
                        padding: 12, 
                        borderRadius: 6,
                        border: '1px solid #d9d9d9'
                      }}>
                        <pre style={{ 
                          margin: 0, 
                          fontSize: '10px',
                          maxHeight: '250px',
                          overflow: 'auto',
                          whiteSpace: 'pre-wrap',
                          lineHeight: 1.3
                        }}>
                          {JSON.stringify(chartConfig, null, 2)}
                        </pre>
                      </div>
                    </Collapse.Panel>
                  </Collapse>
                </div>
              </Card>
            );
          })}

          {/* 执行时间信息 */}
          <div style={{ textAlign: 'center', padding: '8px 0' }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              共生成 {totalCharts} 个图表 | 执行时间: {typeof result.execution_time === 'number' ? 
                result.execution_time.toFixed(3) : 
                (result.result?.execution_time ? 
                  parseFloat(String(result.result.execution_time)).toFixed(3) : 
                  '未知'
                )
              } ms
            </Text>
          </div>
        </Space>
      </div>
    );
  };

  // 在renderLlmResult作用域内确保extractSupplement可用
  const extractSupplement = (userAdjustment: string): string | null => {
    if (!userAdjustment) return null;
    // 支持"补充需求："或"补充说明："，兼容全角/半角冒号
    const match = userAdjustment.match(/(?:补充[需求说明][：:])([\s\S]*)$/);
    if (match && match[1]) {
      // 去掉前后空行
      return match[1].replace(/^\s+|\s+$/g, '');
    }
    return null;
  };

  return (
    <div className="analysis-container">
      <Card className="analysis-card" title={
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <span>{t('analysis:title')}</span>
          <Space>
          <Button 
            type="link" 
            icon={<HistoryOutlined />} 
            onClick={viewHistory}
          >
              {t('analysis:history.title')}
          </Button>
          </Space>
        </div>
      }>
        {!selectedProjectId ? (
          <Alert
            message={t('analysis:errors.noProject')}
            description={t('analysis:errors.noProjectDescription')}
            type="warning"
            showIcon
          />
        ) : (
          <Form
            form={form}
            layout="vertical"
            onFinish={handleAnalysis}
          >
            <Form.Item
              name="query"
              label={t('analysis:chat.placeholder')}
              rules={[{ required: true, message: t('analysis:errors.queryRequired') }]}
            >
              <TextArea 
                placeholder={t('analysis:chat.placeholder')} 
                autoSize={{ minRows: 3, maxRows: 6 }}
                disabled={streaming}
              />
            </Form.Item>
            
            <Form.Item>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                {/* <Radio.Group
                  value={useLlmMode}
                  onChange={(e) => setUseLlmMode(e.target.value)}
                  disabled={streaming}
                  buttonStyle="solid"
                  size="small"
                >
                  <Radio.Button value={true}>LLM分析模式</Radio.Button>
                  <Radio.Button value={false}>标准分析模式</Radio.Button>
                </Radio.Group> */}
                
                {!streaming ? (
                  <Button 
                    type="primary" 
                    icon={<SendOutlined />} 
                    htmlType="submit" 
                    loading={loading}
                  >
                    {t('analysis:chat.send')}
                  </Button>
                ) : (
                  <Button 
                    type="default" 
                    danger
                    onClick={cancelAnalysis}
                  >
                    {t('common:buttons.cancel')}
                  </Button>
                )}
              </div>
            </Form.Item>
          </Form>
        )}

        {(streaming || streamComplete) && (
          <div className="streaming-container" style={{ marginTop: 20 }}>
            <Divider orientation="left">{t('analysis:steps.analysisProgress')}</Divider>
            
            {streaming && (
              <Alert 
                message={t('analysis:steps.llmAnalysisRunning')} 
                type="info"
                showIcon
                style={{ marginBottom: 16 }}
              />
            )}
            
            <div ref={timelineRef} style={{ marginBottom: 20 }}>
              <Timeline>
                {analysisSteps.map((step, index) => {
                  // 显示LLM步骤
                  return (
                    <Timeline.Item 
                      key={step.id}
                      dot={
                        isCurrentStep(step.id) ? (
                          <LoadingOutlined style={{ fontSize: '16px' }} spin />
                        ) : step.status === 'finish' ? (
                          step.id === 'report_generated' ? (
                            <FileTextOutlined style={{ color: '#52c41a' }} />
                          ) : step.id === 'planning' ? (
                            <AimOutlined style={{ color: '#1890ff' }} />
                          ) : step.id.includes('_resource') ? (
                            <span style={{ color: '#722ed1' }}>R</span>
                          ) : step.id.includes('_tool') ? (
                            <CodeOutlined style={{ color: '#fa8c16' }} />
                          ) : step.id === 'completed' ? (
                            <CheckCircleOutlined style={{ color: '#52c41a', fontSize: '16px' }} />
                          ) : step.id === 'clarification_request' ? (
                            <QuestionCircleOutlined style={{ color: '#fa8c16' }} />
                          ) : step.id === 'clarification_answered' ? (
                            <CheckCircleOutlined style={{ color: '#52c41a' }} />
                          ) : (
                            <CheckCircleOutlined style={{ color: '#52c41a' }} />
                          )
                        ) : step.status === 'wait' ? (
                          <QuestionCircleOutlined style={{ color: '#fa8c16', fontSize: '16px' }} />
                        ) : step.status === 'error' ? (
                          <CloseCircleOutlined style={{ color: '#f5222d', fontSize: '16px' }} />
                        ) : step.status === 'process' ? (
                          <LoadingOutlined style={{ fontSize: '16px' }} spin />
                        ) : (
                          <CloseCircleOutlined style={{ color: '#f5222d' }} />
                        )
                      }
                    >
                      <div>
                        <Space>
                          <Text strong={isCurrentStep(step.id) || step.status === 'wait'}>
                            {renderStepName(step.name)}
                            {isCurrentStep(step.id) && <Text type="secondary"> ({t('analysis:steps.executing')})</Text>}
                            {step.status === 'wait' && <Text style={{ color: '#fa8c16' }}> ({t('analysis:steps.waitingForUserInput')})</Text>}
                          </Text>
                          {step.description && (
                            <Text type="secondary" style={step.status === 'wait' ? { color: '#fa8c16' } : {}}>
                              ({step.description})
                            </Text>
                          )}
                        </Space>
                      </div>
                      </Timeline.Item>
                    );
                })}
              </Timeline>
            </div>

            {error && (
              <Alert 
                message={t('analysis:steps.analysisError')} 
                description={error} 
                type="error" 
                showIcon 
                style={{ marginTop: 16 }}
              />
            )}
          </div>
        )}
      </Card>
      
      {/* 智能交互信息对话框 */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <QuestionCircleOutlined style={{ color: '#1890ff', marginRight: 8 }} />
            智能交互 - 需要更多信息
          </div>
        }
        open={showClarificationDialog}
        onOk={handleClarificationSubmit}
        onCancel={() => {
          setShowClarificationDialog(false);
          setClarificationAnswers({});
          
          // 更新步骤状态为已取消
          setAnalysisSteps((prev: any[]) => {
            return prev.map(step => {
              if (step.id === 'clarification_request') {
                return {
                  ...step,
                  status: 'error',
                  description: '用户取消了智能交互'
                };
              }
              return step;
            });
          });
          
          setStreaming(false);
        }}
        width={800}
        okText="提交继续分析"
        cancelText="取消"
        destroyOnClose={true}
      >
        {clarificationRequest && (
          <div>
            {/* 显示交互原因 */}
                <Alert 
              message={clarificationRequest?.message || "需要更多信息以继续分析"} 
                  type="info" 
                  showIcon 
              style={{ marginBottom: 16 }}
            />
            
            <Form layout="vertical">
              {clarificationRequest.questions.map((question: any, index: number) => (
                <Form.Item
                  key={question.id || `question_${index}`}
                  label={
                    <span>
                      {question.question}
                      {question.required && <span style={{ color: 'red', marginLeft: 4 }}>*</span>}
                    </span>
                  }
                  required={question.required}
                  style={{ marginBottom: 16 }}
                >
                  {question.field_type === 'select' && question.options ? (
                    <Select
                      placeholder={question.placeholder || "请选择或输入"}
                      value={clarificationAnswers[question.field_name] ? [clarificationAnswers[question.field_name]] : []}
                      onChange={(value) => {
                        const finalValue = Array.isArray(value) ? (value.length > 0 ? value[value.length - 1] : '') : value;
                        setClarificationAnswers(prev => ({
                          ...prev,
                          [question.field_name]: finalValue
                        }));
                      }}
                      style={{ width: '100%' }}
                      showSearch
                      allowClear
                      mode="tags"
                      maxTagCount={1}
                      notFoundContent={null}
                      defaultActiveFirstOption={false}
                      filterOption={false}
                    >
                      {question.options.map((option: any, optionIndex: number) => {
                        const optionValue = typeof option === 'object' ? (option.value || option.label) : option;
                        const optionLabel = typeof option === 'object' ? (option.label || option.value) : option;
                        
                        return (
                          <Select.Option key={`${question.field_name}_option_${optionIndex}`} value={optionValue}>
                            {optionLabel}
                          </Select.Option>
                        );
                      })}
                    </Select>
                  ) : question.field_type === 'number' ? (
                    <Input
                      type="number"
                      placeholder={question.placeholder || "请输入数字"}
                      value={clarificationAnswers[question.field_name]}
                      onChange={(e) => {
                        setClarificationAnswers(prev => ({
                          ...prev,
                          [question.field_name]: e.target.value
                        }));
                      }}
                    />
                  ) : question.field_type === 'date' ? (
                    <Input
                      type="date"
                      placeholder={question.placeholder || "请选择日期"}
                      value={clarificationAnswers[question.field_name]}
                      onChange={(e) => {
                        setClarificationAnswers(prev => ({
                          ...prev,
                          [question.field_name]: e.target.value
                        }));
                      }}
                    />
                  ) : question.field_type === 'textarea' ? (
                    <TextArea
                      placeholder={question.placeholder || "请输入详细信息"}
                      value={clarificationAnswers[question.field_name]}
                      onChange={(e) => {
                        setClarificationAnswers(prev => ({
                          ...prev,
                          [question.field_name]: e.target.value
                        }));
                      }}
                      autoSize={{ minRows: 2, maxRows: 4 }}
                    />
                  ) : (
                    <Input
                      placeholder={question.placeholder || "请输入信息"}
                      value={clarificationAnswers[question.field_name]}
                      onChange={(e) => {
                        setClarificationAnswers(prev => ({
                          ...prev,
                          [question.field_name]: e.target.value
                        }));
                      }}
                    />
                  )}
                  
                  {question.description && (
                    <div style={{ color: '#666', fontSize: '12px', marginTop: 4 }}>
                      💡 {question.description}
                    </div>
                  )}
                </Form.Item>
              ))}
            </Form>
          </div>
        )}
      </Modal>
      
      {/* 图表展示弹窗 */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <EyeOutlined style={{ marginRight: 8, color: '#1890ff' }} />
            <span>
              {chartData?.data?.chart_type === 'multi_charts' 
                ? `图表展示 - 共 ${chartData?.data?.charts?.length || 0} 个图表` 
                : '图表展示'
                            }
            </span>
    </div>
        }
        width="95%"
        style={{ top: 20 }}
        open={chartModalVisible}
        onCancel={() => setChartModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setChartModalVisible(false)}>
            关闭
          </Button>,
          <Button 
            key="download" 
            type="primary" 
            onClick={() => {
              // 实现图表下载功能
              message.info('图表下载功能开发中');
            }}
          >
            下载图表
          </Button>
        ]}
        bodyStyle={{ padding: '24px' }}
      >
        {chartData && (
          <div style={{ minHeight: '500px' }}>
            {/* 检查是否为多图表结构 */}
            {(chartData.data?.chart_type === 'multi_charts' || chartData.data?.display_format === 'multi_chart' || (chartData.data?.total_charts && chartData.data?.total_charts > 1)) && chartData.data?.charts ? (
              <Space direction="vertical" style={{ width: '100%' }} size="large">
                {/* 多图表总体信息 */}
                <Card 
                  size="small"
                  style={{ 
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', 
                    border: 'none',
                    borderRadius: 12,
                    color: 'white'
                  }}
                  bodyStyle={{ padding: '20px' }}
                >
                  <Row gutter={[24, 16]}>
                    <Col xs={24} md={12}>
                      <div>
                        <Text strong style={{ fontSize: '18px', color: 'white', display: 'block', marginBottom: 8 }}>
                          📊 多图表分析
                        </Text>
                        <Text style={{ fontSize: '14px', color: 'rgba(255,255,255,0.9)' }}>
                          共生成 {chartData.data.charts.length} 个图表
                        </Text>
                      </div>
                    </Col>
                    <Col xs={24} md={12}>
                      <div style={{ textAlign: 'right' }}>
                        {chartData.data.user_query && (
                          <div>
                            <Text style={{ fontSize: '13px', color: 'rgba(255,255,255,0.8)', display: 'block' }}>
                              查询：{chartData.data.user_query}
                            </Text>
                          </div>
                        )}
                        {chartData.data.generated_reason && (
                          <div>
                            <Text style={{ fontSize: '12px', color: 'rgba(255,255,255,0.7)' }}>
                              {chartData.data.generated_reason}
                            </Text>
                          </div>
                        )}
                      </div>
                    </Col>
                  </Row>
                </Card>

                {/* 多图表展示 */}
                <div style={{ display: 'grid', gap: '24px' }}>
                  {chartData.data.charts.map((chart: any, index: number) => {
                    const chartConfig = chart.config || chart.chart_config; // 支持新旧格式
                    const chartType = chart.chart_type || 'echarts';
                    const chartTitle = chart.title || `图表 ${index + 1}`;
                    const chartDescription = chart.description || '';

                    return (
                      <Card 
                        key={`modal-chart-${index}`}
                        title={
                          <Space>
                            <Tag color="blue">图表 {index + 1}</Tag>
                            <Text strong style={{ fontSize: '16px' }}>{chartTitle}</Text>
                            <Tag color="green" style={{ fontSize: '11px' }}>{chartType}</Tag>
                          </Space>
                        }
                        style={{ 
                          borderRadius: 12,
                          boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                          border: '1px solid #f0f0f0'
                        }}
                        bodyStyle={{ padding: '20px' }}
                      >
                        {/* 图表描述 */}
                        {chartDescription && (
                          <div style={{ marginBottom: '16px', padding: '12px', background: '#f8f9fa', borderRadius: 8 }}>
                            <Text style={{ fontSize: '14px', color: '#666' }}>
                              {chartDescription}
                            </Text>
                          </div>
                        )}

                        {/* 图表显示区域 */}
                        <div style={{
                          background: 'white',
                          borderRadius: 8,
                          padding: '16px',
                          minHeight: '500px',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          border: '1px solid #f0f0f0'
                        }}>
                          {chartType === 'table' && chartConfig && chartConfig.columns ? (
                            // 表格渲染
                            <div style={{ width: '100%' }}>
                              <Table
                                {...chartConfig}
                                scroll={{ x: 'max-content', y: 400 }}
                                size="small"
                                bordered
                                style={{ width: '100%' }}
                              />
                            </div>
                          ) : chartConfig && chartConfig.series ? (
                            (() => {
                              // 先转换函数，然后应用其他配置
                              const revivedConfig = reviveEchartsFunctions(chartConfig);
                              return (
                                <ReactECharts
                                  option={{
                                    ...revivedConfig,
                                    // 通用的响应式和优化配置
                                    grid: {
                                      ...revivedConfig?.grid,
                                      left: '10%',
                                      right: '10%',
                                      top: '20%',  // 增加顶部空间，为标题和图例留出位置
                                      bottom: '20%', // 增加底部空间，为工具栏留出位置
                                      containLabel: true
                                    },
                                    // 确保图例位置合理
                                    legend: {
                                      ...revivedConfig?.legend,
                                      top: revivedConfig?.legend?.top || '12%', // 图例位置稍微下移
                                      type: 'scroll', // 当图例过多时支持滚动
                                      orient: 'horizontal'
                                    },
                                    // 保持原有的tooltip配置（包括已转换的函数）
                                    tooltip: {
                                      confine: true, // 将tooltip限制在图表区域内
                                      backgroundColor: 'rgba(0,0,0,0.8)',
                                      borderColor: 'rgba(0,0,0,0.8)',
                                      textStyle: {
                                        color: '#fff',
                                        fontSize: 12
                                      },
                                      ...revivedConfig?.tooltip, // 保持原有配置，包括已转换的函数
                                    },
                                // 标题优化
                                title: {
                                  ...chartConfig?.title,
                                  left: 'center',
                                  top: '2%', // 标题位置靠上
                                  textStyle: {
                                    fontSize: 16,
                                    fontWeight: 'bold',
                                    ...chartConfig?.title?.textStyle
                                  }
                                },
                                // 工具栏配置 - 确保在底部不遮挡标题
                                toolbox: {
                                  ...chartConfig?.toolbox,
                                  show: chartConfig?.toolbox?.show !== false, // 默认显示工具栏
                                  orient: 'horizontal',
                                  left: 'center',
                                  bottom: '2%', // 工具栏位置在底部
                                  feature: {
                                    saveAsImage: { 
                                      show: true,
                                      title: '保存为图片'
                                    },

                                    magicType: {
                                      show: true,
                                      title: {
                                        line: '切换为折线图',
                                        bar: '切换为柱状图'
                                      },
                                      type: ['line', 'bar']
                                    },
                                    restore: { 
                                      show: true,
                                      title: '还原'
                                    },
                                    ...chartConfig?.toolbox?.feature
                                  }
                                }
                              }}
                              style={{ 
                                height: '500px', 
                                width: '100%',
                                minHeight: '400px'
                              }}
                              opts={{ 
                                renderer: 'canvas'
                              }}
                              notMerge={true}
                              lazyUpdate={true}
                              onChartReady={(chart: any) => {
                                // 图表加载完成后的优化
                                const resizeChart = () => {
                                  if (chart && !chart.isDisposed()) {
                                    chart.resize();
                                  }
                                };
                                // 监听窗口大小变化
                                window.addEventListener('resize', resizeChart);
                                // 延迟初始化大小调整
                                setTimeout(resizeChart, 100);
                                
                                // 清理函数
                                return () => {
                                  window.removeEventListener('resize', resizeChart);
                                };
                              }}
                            />
                              );
                            })()
                          ) : (
                            <Alert 
                              message={`图表 ${index + 1} 配置无效`}
                              description="缺少有效的图表配置数据"
                              type="warning" 
                              showIcon 
                            />
                          )}
                        </div>

                        {/* 配置详情（可折叠） */}
                        <div style={{ marginTop: '16px' }}>
                          <Collapse 
                            ghost 
                            size="small"
                            style={{ background: 'transparent' }}
                          >
                            <Collapse.Panel 
                              header={
                                <Space>
                                  <CodeOutlined style={{ color: '#1890ff' }} />
                                  <Text style={{ fontSize: '13px', fontWeight: 500 }}>
                                    查看图表 {index + 1} 配置
                                  </Text>
                                </Space>
                              } 
                              key={`modal-config-${index}`}
                            >
                              <div style={{ 
                                background: '#f5f5f5', 
                                padding: 12, 
                                borderRadius: 6,
                                border: '1px solid #d9d9d9'
                              }}>
                                <pre style={{ 
                                  margin: 0, 
                                  fontSize: '11px',
                                  maxHeight: '300px',
                                  overflow: 'auto',
                                  whiteSpace: 'pre-wrap',
                                  lineHeight: 1.3
                                }}>
                                  {JSON.stringify(chartConfig, null, 2)}
                                </pre>
                              </div>
                            </Collapse.Panel>
                          </Collapse>
                        </div>
                      </Card>
                    );
                  })}
                </div>
              </Space>
            ) : chartData.data?.table_config ? (
              // MoE表格展示
              <Space direction="vertical" style={{ width: '100%' }} size="large">
                {/* 表格基本信息 */}
                <Card
                  size="small"
                  style={{
                    background: 'linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%)',
                    border: '1px solid #e8eaec',
                    borderRadius: 8
                  }}
                >
                  <Row gutter={[24, 16]}>
                    <Col xs={24} md={8}>
                      <div style={{ textAlign: 'center' }}>
                        <Text strong style={{ fontSize: '14px', display: 'block', marginBottom: 8 }}>
                          展示类型
                        </Text>
                        <Tag
                          color="green"
                          style={{
                            fontSize: '13px',
                            padding: '4px 12px',
                            borderRadius: 6
                          }}
                        >
                          数据表格
                        </Tag>
                      </div>
                    </Col>
                    <Col xs={24} md={8}>
                      <div style={{ textAlign: 'center' }}>
                        <Text strong style={{ fontSize: '14px', display: 'block', marginBottom: 8 }}>
                          数据量
                        </Text>
                        <Text style={{ fontSize: '13px' }}>
                          {chartData.data.table_config.dataSource?.length || 0} 条记录
                        </Text>
                      </div>
                    </Col>
                    <Col xs={24} md={8}>
                      <div style={{ textAlign: 'center' }}>
                        <Text strong style={{ fontSize: '14px', display: 'block', marginBottom: 8 }}>
                          列数
                        </Text>
                        <Text style={{ fontSize: '13px' }}>
                          {chartData.data.table_config.columns?.length || 0} 列
                        </Text>
                      </div>
                    </Col>
                  </Row>
                </Card>

                {/* Ant Design表格 */}
                <Card
                  title={chartData.data.title || '数据表格'}
                  style={{
                    borderRadius: 8,
                    boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                    border: '1px solid #f0f0f0'
                  }}
                  bodyStyle={{ padding: '20px' }}
                >
                  <Table
                    {...chartData.data.table_config}
                    scroll={{ x: 'max-content', y: 500 }}
                    size="small"
                  />
                </Card>

                {/* 配置详情（可折叠） */}
                <Card
                  size="small"
                  style={{
                    background: '#fafafa',
                    border: '1px solid #f0f0f0',
                    borderRadius: 8
                  }}
                >
                  <Collapse
                    ghost
                    size="small"
                    style={{ background: 'transparent' }}
                  >
                    <Collapse.Panel
                      header={
                        <Space>
                          <CodeOutlined style={{ color: '#1890ff' }} />
                          <Text style={{ fontSize: '14px', fontWeight: 500 }}>
                            查看表格配置详情
                          </Text>
                        </Space>
                      }
                      key="config"
                    >
                      <div style={{
                        background: 'white',
                        padding: 16,
                        borderRadius: 6,
                        border: '1px solid #e8e8e8',
                        boxShadow: 'inset 0 1px 3px rgba(0,0,0,0.1)'
                      }}>
                        <pre style={{
                          margin: 0,
                          fontSize: '12px',
                          maxHeight: '400px',
                          overflow: 'auto',
                          whiteSpace: 'pre-wrap',
                          lineHeight: 1.5,
                          color: '#333'
                        }}>
                          {JSON.stringify(chartData.data.table_config, null, 2)}
                        </pre>
                      </div>
                    </Collapse.Panel>
                  </Collapse>
                </Card>
              </Space>
            ) : (chartData.data?.chart_config || (chartData.data?.total_charts === 1 && chartData.data?.charts?.[0]?.config)) ? (
              // 单图表展示（原有逻辑保持不变）
              <Space direction="vertical" style={{ width: '100%' }} size="large">
                {/* 图表基本信息 */}
                <Card 
                  size="small"
                  style={{ 
                    background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)', 
                    border: '1px solid #e8eaec',
                    borderRadius: 8
                  }}
                >
                  <Row gutter={[24, 16]}>
                    <Col xs={24} md={8}>
                      <div style={{ textAlign: 'center' }}>
                        <Text strong style={{ fontSize: '14px', display: 'block', marginBottom: 8 }}>
                          图表类型
                        </Text>
                        <Tag 
                          color="blue" 
                          style={{ 
                            fontSize: '13px', 
                            padding: '4px 12px',
                            borderRadius: 6
                          }}
                        >
                          {chartData.data.chart_type || 'echarts'}
                        </Tag>
                      </div>
                    </Col>
                    {chartData.data.data_source_info && (
                      <Col xs={24} md={8}>
                        <div style={{ textAlign: 'center' }}>
                          <Text strong style={{ fontSize: '14px', display: 'block', marginBottom: 8 }}>
                            数据源信息
                          </Text>
                          <Space direction="vertical" size="small">
                            <Text style={{ fontSize: '13px' }}>{chartData.data.data_source_info.type}</Text>
                            <Text type="secondary" style={{ fontSize: '12px' }}>
                              记录数: {chartData.data.data_source_info.records_count}
                            </Text>
                          </Space>
                        </div>
                      </Col>
                    )}
                    {chartData.data.generated_at && (
                      <Col xs={24} md={8}>
                        <div style={{ textAlign: 'center' }}>
                          <Text strong style={{ fontSize: '14px', display: 'block', marginBottom: 8 }}>
                            生成时间
                          </Text>
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            {new Date(chartData.data.generated_at).toLocaleString()}
                          </Text>
                        </div>
                      </Col>
                    )}
                  </Row>
                </Card>

                {/* ECharts图表 */}
                <Card 
                  style={{ 
                    borderRadius: 8,
                    boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                    border: '1px solid #f0f0f0'
                  }}
                  bodyStyle={{ padding: '20px' }}
                >
                  <div style={{ 
                    background: 'white',
                    borderRadius: 6,
                    minHeight: '500px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    position: 'relative'
                  }}>
                    <ReactECharts
                      option={{
                        ...reviveEchartsFunctions(chartData.data.chart_config || chartData.data.charts?.[0]?.config),
                        // 通用的响应式和优化配置
                        grid: {
                          ...(chartData.data.chart_config || chartData.data.charts?.[0]?.config)?.grid,
                          left: '10%',
                          right: '10%',
                          top: '20%',  // 增加顶部空间，为标题和图例留出位置
                          bottom: '20%', // 增加底部空间，为工具栏留出位置
                          containLabel: true
                        },
                        // 确保图例位置合理
                        legend: {
                          ...(chartData.data.chart_config || chartData.data.charts?.[0]?.config)?.legend,
                          top: (chartData.data.chart_config || chartData.data.charts?.[0]?.config)?.legend?.top || '12%', // 图例位置稍微下移
                          type: 'scroll', // 当图例过多时支持滚动
                          orient: 'horizontal'
                        },
                        // 工具提示优化
                        tooltip: {
                          ...(chartData.data.chart_config || chartData.data.charts?.[0]?.config)?.tooltip,
                          trigger: (chartData.data.chart_config || chartData.data.charts?.[0]?.config)?.tooltip?.trigger || 'item',
                          confine: true, // 将tooltip限制在图表区域内
                          backgroundColor: 'rgba(0,0,0,0.8)',
                          borderColor: 'rgba(0,0,0,0.8)',
                          textStyle: {
                            color: '#fff',
                            fontSize: 12
                          }
                        },
                        // 标题优化
                        title: {
                          ...(chartData.data.chart_config || chartData.data.charts?.[0]?.config)?.title,
                          left: 'center',
                          top: '2%', // 标题位置靠上
                          textStyle: {
                            fontSize: 16,
                            fontWeight: 'bold',
                            ...(chartData.data.chart_config || chartData.data.charts?.[0]?.config)?.title?.textStyle
                          }
                        },
                        // 工具栏配置 - 确保在底部不遮挡标题
                        toolbox: {
                          ...(chartData.data.chart_config || chartData.data.charts?.[0]?.config)?.toolbox,
                          show: (chartData.data.chart_config || chartData.data.charts?.[0]?.config)?.toolbox?.show !== false, // 默认显示工具栏
                          orient: 'horizontal',
                          left: 'center',
                          bottom: '2%', // 工具栏位置在底部
                          feature: {
                            saveAsImage: {
                              show: true,
                              title: '保存为图片'
                            },

                            magicType: {
                              show: true,
                              title: {
                                line: '切换为折线图',
                                bar: '切换为柱状图'
                              },
                              type: ['line', 'bar']
                            },
                            restore: {
                              show: true,
                              title: '还原'
                            },
                            ...(chartData.data.chart_config || chartData.data.charts?.[0]?.config)?.toolbox?.feature
                          }
                        }
                      }}
                      style={{ 
                        height: '500px', 
                        width: '100%',
                        minHeight: '400px'
                      }}
                      opts={{ 
                        renderer: 'canvas'
                      }}
                      notMerge={true}
                      lazyUpdate={true}
                      onChartReady={(chart: any) => {
                        // 图表加载完成后的优化
                        const resizeChart = () => {
                          if (chart && !chart.isDisposed()) {
                            chart.resize();
                          }
                        };
                        // 监听窗口大小变化
                        window.addEventListener('resize', resizeChart);
                        // 延迟初始化大小调整
                        setTimeout(resizeChart, 100);
                        
                        // 清理函数
                        return () => {
                          window.removeEventListener('resize', resizeChart);
                        };
                      }}
                    />
                  </div>
                </Card>

                {/* 配置详情（可折叠） */}
                <Card 
                  size="small"
                  style={{ 
                    background: '#fafafa', 
                    border: '1px solid #f0f0f0',
                    borderRadius: 8
                  }}
                >
                  <Collapse 
                    ghost 
                    size="small"
                    style={{ background: 'transparent' }}
                  >
                    <Collapse.Panel 
                      header={
                        <Space>
                          <CodeOutlined style={{ color: '#1890ff' }} />
                          <Text style={{ fontSize: '14px', fontWeight: 500 }}>
                            查看图表配置详情
                          </Text>
                        </Space>
                      } 
                      key="config"
                    >
                      <div style={{ 
                        background: 'white', 
                        padding: 16, 
                        borderRadius: 6,
                        border: '1px solid #e8e8e8',
                        boxShadow: 'inset 0 1px 3px rgba(0,0,0,0.1)'
                      }}>
                        <pre style={{ 
                          margin: 0, 
                          fontSize: '12px',
                          maxHeight: '400px',
                          overflow: 'auto',
                          whiteSpace: 'pre-wrap',
                          lineHeight: 1.5,
                          color: '#333'
                        }}>
                          {JSON.stringify(chartData.data.chart_config || chartData.data.charts?.[0]?.config, null, 2)}
                        </pre>
                      </div>
                    </Collapse.Panel>
                  </Collapse>
                </Card>
              </Space>
            ) : (
              <div style={{ 
                textAlign: 'center', 
                padding: 60,
                background: '#fafafa',
                borderRadius: 8,
                border: '2px dashed #d9d9d9'
              }}>
                <Alert 
                  message="图表配置数据无效" 
                  type="warning" 
                  showIcon 
                  style={{ marginBottom: 24 }}
                />
                <div>
                  <Text strong style={{ fontSize: '14px', marginBottom: 16, display: 'block' }}>
                    原始数据：
                  </Text>
                  <pre style={{ 
                    background: 'white', 
                    padding: 16, 
                    borderRadius: 6,
                    border: '1px solid #ffd591',
                    fontSize: '12px',
                    textAlign: 'left',
                    maxHeight: '300px',
                    overflow: 'auto'
                  }}>
                    {JSON.stringify(chartData, null, 2)}
                  </pre>
                </div>
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* 意图确认模态框 */}
      <Modal
        title={
          <Space>
            <BulbOutlined style={{ color: '#faad14' }} />
            <span>确认分析意图</span>
          </Space>
        }
        visible={showIntentConfirmation}
        onCancel={() => {
          setShowIntentConfirmation(false);
          setIntentAdjustment('');
        }}
        width={800}
        footer={[
          <Button 
            key="cancel" 
            onClick={() => {
              setShowIntentConfirmation(false);
              setIntentAdjustment('');
            }}
          >
            取消
          </Button>,
          <Button 
            key="confirm" 
            type="primary"
            onClick={handleIntentConfirmationSubmit}
            icon={<CheckOutlined />}
          >
            提交并继续
          </Button>
        ]}
      >
                {intentConfirmation && (
          <div style={{ maxHeight: '500px', overflow: 'auto' }}>
            {/* 用户查询 */}
            <div style={{ marginBottom: 16 }}>
              <Text strong>查询：</Text>
              <div style={{ 
                background: '#f6f8fa', 
                padding: 8, 
                borderRadius: 4, 
                marginTop: 4,
                border: '1px solid #e1e8ed'
              }}>
                <Text style={{ fontSize: '13px' }}>{intentConfirmation.user_query}</Text>
              </div>
            </div>

            {/* 详细意图描述 - 主要字段 */}
            {intentConfirmation.intent_analysis?.intent_description && (
              <div style={{ marginBottom: 16 }}>
                <Text strong>AI分析指导：</Text>
                <div style={{ 
                  background: '#f0f5ff', 
                  padding: 12, 
                  borderRadius: 6, 
                  marginTop: 8,
                  border: '1px solid #d6e4ff'
                }}>
                  <Text style={{ fontSize: '13px', color: '#666', lineHeight: '1.6', whiteSpace: 'pre-wrap' }}>
                    {intentConfirmation.intent_analysis.intent_description}
                  </Text>
                </div>
              </div>
            )}

            {/* 兼容旧字段 - 意图理解 */}
            {intentConfirmation.intent_analysis?.summary && (
              <div style={{ marginBottom: 16 }}>
                <Text strong>AI理解的分析目标：</Text>
                <div style={{ 
                  background: '#f6f8fa', 
                  padding: 12, 
                  borderRadius: 6, 
                  marginTop: 8,
                  border: '1px solid #e1e8ed'
                }}>
                  <Text style={{ fontSize: '14px', color: '#333' }}>
                    {intentConfirmation.intent_analysis.summary}
                  </Text>
                </div>
              </div>
            )}

            {/* 分析类型 */}
            {intentConfirmation.intent_analysis?.analysis_type && (
              <div style={{ marginBottom: 16 }}>
                <Text strong>分析类型：</Text>
                <div style={{ marginTop: 8 }}>
                  <Tag color="blue" style={{ fontSize: '12px' }}>
                    {intentConfirmation.intent_analysis.analysis_type}
                      </Tag>
                  {intentConfirmation.intent_analysis.context_aware && (
                    <Tag color="green" style={{ fontSize: '12px', marginLeft: 8 }}>
                      已结合上下文
                    </Tag>
                  )}
                </div>
              </div>
            )}



            {/* 用户补充 */}
            <div>
              <Text strong>补充需求（可选）：</Text>
              <div style={{ marginTop: 8 }}>
                <Input.TextArea
                  value={intentAdjustment}
                  onChange={(e) => setIntentAdjustment(e.target.value)}
                  placeholder="如有补充或修改建议，请输入..."
                  rows={3}
                  showCount
                  maxLength={300}
                />
              </div>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default Analysis;