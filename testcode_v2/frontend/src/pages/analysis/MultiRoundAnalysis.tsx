import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useNavigate, usePara<PERSON>, <PERSON> } from 'react-router-dom';
import {
  Button, Input, Select, Card, Row, Col, Typography, Space, Tag, Steps, Spin, Alert, Divider,
  List, Table, Collapse, message, Empty, Upload, Radio, Checkbox, Skeleton, Switch, Tooltip, Badge,
  Modal, Form, Tabs, Descriptions, Timeline, Statistic, Progress
} from 'antd';
import {
  QuestionCircleOutlined, SendOutlined, CheckCircleOutlined,
  CloseCircleOutlined, LoadingOutlined, CodeOutlined,
  MessageOutlined, PlusOutlined, EyeOutlined,
  PlayCircleOutlined,
  ExclamationCircleOutlined,
  BulbOutlined,
  FileTextOutlined,
  RocketOutlined,
  StarOutlined,
  UserOutlined
} from '@ant-design/icons';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import ReactECharts from 'echarts-for-react';
import { JsonView } from 'react-json-view-lite';
import 'react-json-view-lite/dist/index.css';
import { useTranslation } from 'react-i18next';
import { projectApi, llmAnalysisApiExtended, conversationApi, multiRoundAnalysisApi } from '../../services/api';
import { normalizeToolType } from '../../utils/toolUtils';
import TemplatePanel from '../../components/TemplatePanel';
import TemplateParameterPanel from '../../components/TemplateParameterPanel';
import { Template, TemplateApi } from '../../services/templateApi';
import { personalTemplateStorage, PersonalTemplateUsage } from '../../utils/personalTemplateStorage';
import DynamicReportRenderer from '../../components/report/DynamicReportRenderer';
import './MultiRoundAnalysis.css';

import './AnalysisDetail.css';

const { Paragraph, Text } = Typography;
const { TextArea } = Input;
const { Panel } = Collapse;

// 会话状态接口
interface ConversationState {
  conversationId: string;
  rounds: AnalysisRound[];
  currentRound: number;
  isMultiRound: boolean;
  projectId: string;
  title: string;
}

interface AnalysisRound {
  roundId: string;
  query: string;
  analysisId: string;
  analysisSteps: any[];
  toolResults: any[];
  finalReport: string;
  status: 'completed' | 'running' | 'failed';
  timestamp: string;
  intentAnalysis?: any;
  mcpResults?: Record<string, any>;
  contextSummary?: string;
  chartDataList?: any[];

}

// 定义Conversation接口
interface Conversation {
  id: string;
  title: string;
  project_id: string;
  created_at: string;
  updated_at: string;
  status: 'active' | 'archived';
  rounds_count?: number;
  last_query?: string;
  last_analysis_at?: string;
}



// 内联打断信息组件 - 嵌入到时间线中
const InlineInterruptItem: React.FC<{
  interrupt: any;
}> = ({ interrupt }) => {
  const { t } = useTranslation();
  
  // 状态颜色映射
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return '#faad14';
      case 'adjusted': return '#52c41a';
      case 'timeout': return '#ff4d4f';
      default: return '#d9d9d9';
    }
  };
  
  // 状态文本映射
  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return t('analysis:status.pending');
      case 'adjusted': return t('analysis:status.adjusted');
      case 'timeout': return t('analysis:status.timeout');
      default: return status;
    }
  };


  // 状态图标映射
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return '⏳';
      case 'adjusted': return '✅';
      case 'timeout': return '⏰';
      default: return '🛑';
    }
  };

  return (
    <div style={{
      background: '#fafafa',
      border: `1px solid ${getStatusColor(interrupt.status)}`,
      borderLeft: `4px solid ${getStatusColor(interrupt.status)}`,
      borderRadius: '8px',
      padding: '12px 16px',
      margin: '8px 0',
      fontSize: '14px'
    }}>
      {/* 简洁的标题行 */}
      <div style={{ 
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: interrupt.user_feedback ? 8 : 0
      }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <span style={{ marginRight: 8, fontSize: '16px' }}>
            {getStatusIcon(interrupt.status)}
          </span>
          <span style={{ 
            fontWeight: 500, 
            color: '#262626'
          }}>
            {t('analysis:interrupt.title')}
          </span>
        </div>
        <Tag 
          color={getStatusColor(interrupt.status)} 
          style={{ 
            margin: 0,
            fontSize: '12px',
            borderRadius: '4px'
          }}
        >
          {getStatusText(interrupt.status)}
        </Tag>
      </div>

      {/* 用户反馈内容 */}
      {interrupt.user_feedback && (
        <div style={{ 
          background: '#f0f0f0', 
          padding: '8px 12px', 
          borderRadius: '6px',
          fontSize: '13px',
          lineHeight: '1.5',
          color: '#555',
          fontStyle: 'italic'
        }}>
          "{interrupt.user_feedback}"
        </div>
      )}

      {/* 时间信息 */}
      <div style={{ 
        fontSize: '11px', 
        color: '#999',
        marginTop: 8,
        textAlign: 'right'
      }}>
        {new Date(interrupt.interrupt_timestamp).toLocaleString('zh-CN', {
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        })}
      </div>
    </div>
  );
};

// 继续分析面板组件
const ContinueAnalysisPanel: React.FC<{
  onContinue: () => void;
  onNewConversation: () => void;
  disabled: boolean;
}> = ({ onContinue, onNewConversation, disabled }) => {
  const { t } = useTranslation();
  return (
    <Card 
      size="small" 
      style={{ 
        marginTop: 16, 
        background: 'linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%)',
        border: '1px solid #b7eb8f',
        borderRadius: '8px'
      }}
    >
      <div style={{ textAlign: 'center', padding: '12px' }}>
        <Text style={{ fontSize: '16px', fontWeight: 500 }}>
          {t('project:intelligence.analysisComplete')}
        </Text>
        <div style={{ marginTop: 16 }}>
          <Space size="large">
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
              onClick={onContinue}
              disabled={disabled}
              size="large"
            >
              {t('common:buttons.continueQuestion')}
            </Button>
            <Button 
              icon={<MessageOutlined />}
              onClick={onNewConversation}
              size="large"
            >
              {t('common:buttons.startNewConversation')}
            </Button>
          </Space>
        </div>
      </div>
    </Card>
  );
};



const MultiRoundAnalysis: React.FC = () => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const { conversationId } = useParams<{ conversationId?: string }>();
  const eventSourceRef = useRef<EventSource | { cancel: () => void } | null>(null);
  const timelineRef = useRef<HTMLDivElement>(null);
  const analysisContainerRef = useRef<HTMLDivElement>(null); // 新增：分析区域容器的ref
  
  // 🔧 新增：连接标识符和防抖保护
  const [currentConnectionId, setCurrentConnectionId] = useState<string | null>(null);
  const [isConversationSwitching, setIsConversationSwitching] = useState(false);
  const connectionIdRef = useRef<string | null>(null);
  
  // @fixed_implementation_start
  // 实现标识: 错误信息格式化处理
  // 功能描述: 统一处理错误对象的格式化，避免显示[object Object]，确保用户能看到具体的错误信息
  // 修复内容: 创建formatErrorMessage函数，处理字符串、对象、未知类型的错误信息
  // @fixed_implementation_end
  
  // 错误信息格式化函数
  const formatErrorMessage = (error: any): string => {
    if (typeof error === 'string') {
      return error;
    }
    
    if (error && typeof error === 'object') {
      // 尝试获取常见的错误属性
      if (error.message) {
        return error.message;
      }
      if (error.error) {
        return typeof error.error === 'string' ? error.error : JSON.stringify(error.error);
      }
      if (error.msg) {
        return error.msg;
      }
      if (error.detail) {
        return error.detail;
      }
      
      // 如果是HTTP响应错误
      if (error.response?.data?.message) {
        return error.response.data.message;
      }
      if (error.response?.data?.error) {
        return error.response.data.error;
      }
      
      // 最后尝试JSON序列化
      try {
        return JSON.stringify(error);
      } catch {
        return t('analysis:errors.unknownError', { defaultValue: '错误对象无法序列化' });
      }
    }
    
    return t('analysis:errors.unknownError', { defaultValue: '未知错误' });
  };
  
  // 状态文本映射
  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return t('analysis:status.pending');
      case 'adjusted': return t('analysis:status.adjusted');
      case 'timeout': return t('analysis:status.timeout');
      default: return status;
    }
  };
  
  // 基础状态
  const [loading, setLoading] = useState<boolean>(false);
  const [selectedProjectId, setSelectedProjectId] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [tools, setTools] = useState<any[]>([]);
  const [projects, setProjects] = useState<any[]>([]);
  const [userInfo, setUserInfo] = useState<any>(null);
  
  // 会话状态
  const [conversationState, setConversationState] = useState<ConversationState | null>(null);
  const [showContinueInput, setShowContinueInput] = useState(false);
  
  // 流式分析相关状态
  const [streaming, setStreaming] = useState<boolean>(false);
  const [currentStep, setCurrentStep] = useState<string>('');
  const [analysisSteps, setAnalysisSteps] = useState<any[]>([]);
  const [intentAnalysis, setIntentAnalysis] = useState<any>(null);
  const [toolResults, setToolResults] = useState<any[]>([]);
  const [finalReport, setFinalReport] = useState<string>('');

  const [analysisId, setAnalysisId] = useState<string>('');
  const [streamComplete, setStreamComplete] = useState<boolean>(false);
  const [mcpResults, setMcpResults] = useState<Record<string, any>>({});
  const [continueQuery, setContinueQuery] = useState('');
  
  // 打断功能相关状态
  const [isInterrupting, setIsInterrupting] = useState<boolean>(false);

  const [feedbackModalVisible, setFeedbackModalVisible] = useState<boolean>(false);
  const [currentInterruptId, setCurrentInterruptId] = useState<string | null>(null);
  const [waitingForFeedback, setWaitingForFeedback] = useState<boolean>(false);
  const [interruptFeedback, setInterruptFeedback] = useState<{
    adjustmentType: string;
    feedback: string;
    resetPlanning: boolean;
  }>({
    adjustmentType: 'strategy',
    feedback: '',
    resetPlanning: false
  });
  

  

  const [contextSummary, setContextSummary] = useState<string>('');

  // 新增：图表相关状态
  const [chartModalVisible, setChartModalVisible] = useState(false);
  const [chartData, setChartData] = useState<any>(null);
  const [chartDataList, setChartDataList] = useState<any[]>([]); // 保存所有生成的图表数据

  // 新增：意图确认相关状态
  const [intentConfirmation, setIntentConfirmation] = useState<any>(null);
  const [showIntentConfirmation, setShowIntentConfirmation] = useState(false);
  const [intentAdjustment, setIntentAdjustment] = useState<string>('');

  // 新增：澄清问题相关状态
  const [clarificationAnswers, setClarificationAnswers] = useState<Record<string, string>>({});

  // 新增：时间轴loading状态
  const [timelineLoading, setTimelineLoading] = useState<boolean>(false);
  const timelineLoadingTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 新增：模板相关状态
  const [showTemplatePanel, setShowTemplatePanel] = useState(false);
  const [templates, setTemplates] = useState<Template[]>([]);
  const [hotTemplates, setHotTemplates] = useState<Template[]>([]);
  const [personalTemplates, setPersonalTemplates] = useState<PersonalTemplateUsage[]>([]);
  const [templatesLoading, setTemplatesLoading] = useState(false);
  const [showParameterPanel, setShowParameterPanel] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [queryValue, setQueryValue] = useState<string>('');


  // 新增：基于分析ID的打断信息存储（事件驱动）
  const [interruptInfoByAnalysisId, setInterruptInfoByAnalysisId] = useState<Record<string, any>>({});

  // 获取指定分析ID的打断信息
  const getInterruptInfoForAnalysis = (analysisId: string) => {
    return interruptInfoByAnalysisId[analysisId] || null;
  };

  // 更新指定分析ID的打断信息
  const updateInterruptInfoForAnalysis = (analysisId: string, interruptInfo: any) => {
    setInterruptInfoByAnalysisId(prev => ({
      ...prev,
      [analysisId]: interruptInfo
    }));
  };

  // 清除指定分析ID的打断信息
  const clearInterruptInfoForAnalysis = (analysisId: string) => {
    setInterruptInfoByAnalysisId(prev => {
      const newState = { ...prev };
      delete newState[analysisId];
      return newState;
    });
  };

  // 刷新个人模板列表
  const refreshPersonalTemplates = async () => {
    if (!selectedProjectId || !userInfo) return;

    try {
      const userId = userInfo?.id?.toString() || userInfo?.username || 'anonymous';
      const latestTemplates = await TemplateApi.getTemplates({
        projectId: selectedProjectId
      });
      const updatedPersonalTemplates = personalTemplateStorage.getPersonalTemplatesFilteredByDatabase(
        userId,
        selectedProjectId,
        latestTemplates
      );
      setPersonalTemplates(updatedPersonalTemplates);
    } catch (error) {
      console.error('刷新个人模板列表失败:', error);
    }
  };

  // 处理模板选择
  const handleTemplateSelect = async (template: Template, withParameters: boolean = false) => {
    if (withParameters) {
      // 打开参数化面板
      setSelectedTemplate(template);
      setShowParameterPanel(true);
    } else {
      // 记录模板使用（本地存储）
      const userId = userInfo?.id?.toString() || userInfo?.username || 'anonymous';
      personalTemplateStorage.recordTemplateUsage(
        userId,
        selectedProjectId!,
        template.id,
        template.name,
        template.content,
        template.description,
        template.category
      );

      // 记录模板使用（数据库统计）
      try {
        await TemplateApi.recordTemplateUsage(template.id);
      } catch (error) {
        console.error('记录数据库使用次数失败:', error);
      }

      // 直接应用模板
      setQueryValue(template.content);
      form.setFieldsValue({
        query: template.content
      });
      setShowTemplatePanel(false);
      message.success(t('analysis:templates.templateApplied', { name: template.name }));

      // 刷新个人模板列表
      await refreshPersonalTemplates();

      // 聚焦到输入框，方便用户编辑
      setTimeout(() => {
        const textarea = document.querySelector('textarea[placeholder*="请输入您的分析问题"]') as HTMLTextAreaElement;
        if (textarea) {
          textarea.focus();
          // 将光标移到文本末尾
          textarea.setSelectionRange(textarea.value.length, textarea.value.length);
        }
      }, 100);
    }
  };

  // 处理参数化模板应用
  const handleParameterTemplateApply = (content: string) => {
    setQueryValue(content);
    form.setFieldsValue({
      query: content
    });
    setShowParameterPanel(false);
    setSelectedTemplate(null);
    setShowTemplatePanel(false);
    message.success(t('analysis:templates.templateApplied', { name: t('analysis:templates.useTemplate') }));
  };

  // 取消参数化模板
  const handleParameterTemplateCancel = () => {
    setShowParameterPanel(false);
    setSelectedTemplate(null);
  };



  // 获取热门模板
  const fetchHotTemplates = async () => {
    if (!selectedProjectId) return;

    try {
      setTemplatesLoading(true);
      const response = await TemplateApi.getTemplates({
        projectId: selectedProjectId
      });

      // 按使用次数排序，取前6个作为热门模板
      const sortedTemplates = response.sort((a: Template, b: Template) => b.usage_count - a.usage_count);
      const topTemplates = sortedTemplates.slice(0, 6);

      setHotTemplates(topTemplates);
      setTemplates(response);

      // 获取个人常用模板（基于数据库模板过滤）
      const userId = userInfo?.id?.toString() || userInfo?.username || 'anonymous';
      const personalTemplates = personalTemplateStorage.getPersonalTemplatesFilteredByDatabase(
        userId,
        selectedProjectId,
        response
      );

      setPersonalTemplates(personalTemplates);
    } catch (error) {
      console.error('获取热门模板失败:', error);
      // 如果获取失败，使用默认模板
      setHotTemplates([]);
    } finally {
      setTemplatesLoading(false);
    }
  };



  // 生成会话ID
  const generateConversationId = () => {
    return `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  };

  // 生成轮次ID
  const generateRoundId = () => {
    return `round_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  };

  // 获取项目列表
  const fetchProjects = async () => {
    try {
      const response = await projectApi.getProjects();
      setProjects(response.data || []);
    } catch (error) {
      console.error('获取项目列表失败:', error);
      message.error(t('analysis:errors.getProjectListFailed'));
    }
  };

  // 重置新建会话状态的函数 - 🔧增强连接管理
  const resetNewConversationState = () => {
    console.log('🔄 重置新建会话状态');
    
    // 🔧 设置切换状态保护
    setIsConversationSwitching(true);
    
    // 🔧 重置连接标识符
    const oldConnectionId = connectionIdRef.current;
    connectionIdRef.current = null;
    setCurrentConnectionId(null);
    
    // 关闭任何正在进行的EventSource连接
    if (eventSourceRef.current) {
      console.log(`🔗 关闭EventSource连接 (connectionId: ${oldConnectionId})`);
      if ('close' in eventSourceRef.current) {
        eventSourceRef.current.close();
      } else if ('cancel' in eventSourceRef.current) {
        eventSourceRef.current.cancel();
      }
      eventSourceRef.current = null;
    }
    
    // 🔧 清理时间轴loading定时器和状态
    if (timelineLoadingTimerRef.current) {
      clearTimeout(timelineLoadingTimerRef.current);
      timelineLoadingTimerRef.current = null;
    }
    setTimelineLoading(false);
    
    // 重置所有状态
    setConversationState(null);
    setStreaming(false);
    setError('');
    setAnalysisSteps([]);
    setCurrentStep('');
    setIntentAnalysis(null);
    setToolResults([]);
    setFinalReport('');
    setAnalysisId('');
    setStreamComplete(false);
    setMcpResults({});
    setShowContinueInput(false);
    setContextSummary('');
    setIntentConfirmation(null);
    setShowIntentConfirmation(false);
    setIntentAdjustment('');
    
    // 🔧 重置打断相关状态
    setWaitingForFeedback(false);
    setIsInterrupting(false);
    setFeedbackModalVisible(false);
    setCurrentInterruptId(null);
    setInterruptFeedback({
      adjustmentType: 'strategy',
      feedback: '',
      resetPlanning: false
    });
    
    form.resetFields();

    // 🔧 修复：清空输入框内容
    setQueryValue('');

    // 清除所有打断信息
    setInterruptInfoByAnalysisId({});
    
    // 🔧 重置打断相关状态
    setWaitingForFeedback(false);
    setIsInterrupting(false);
    setFeedbackModalVisible(false);
    setCurrentInterruptId(null);
    setInterruptFeedback({
      adjustmentType: 'strategy',
      feedback: '',
      resetPlanning: false
    });
    
    // 🔧 延迟释放切换状态保护，确保状态完全重置
    setTimeout(() => {
      setIsConversationSwitching(false);
      console.log('✅ 会话切换状态保护已释放');
    }, 100);
  };

  // 初始化
  useEffect(() => {
    // 从localStorage获取当前选中的项目ID
    const projectId = localStorage.getItem('selectedProjectId');
    
    if (projectId) {
      setSelectedProjectId(projectId);
    } else {
      setError("请先在顶部选择一个项目");
    }
    
    // 获取用户信息
    const storedUserInfo = localStorage.getItem('userInfo');
    if (storedUserInfo) {
      try {
        const parsedUserInfo = JSON.parse(storedUserInfo);
        setUserInfo(parsedUserInfo);
      } catch (e) {
        console.error('解析用户信息失败:', e);
      }
    }
    
    fetchProjects();
    
    // 组件卸载时关闭EventSource连接
    return () => {
      if (eventSourceRef.current) {
        if ('close' in eventSourceRef.current) {
          eventSourceRef.current.close();
        } else if ('cancel' in eventSourceRef.current) {
          eventSourceRef.current.cancel();
        }
      }
      
      // 清理时间轴loading定时器
      if (timelineLoadingTimerRef.current) {
        clearTimeout(timelineLoadingTimerRef.current);
      }
    };
  }, []); // 只在组件挂载时执行一次

  // 🔧 修复：URL变化监听，确保会话状态同步
  useEffect(() => {
    console.log('URL变化检测，当前conversationId:', conversationId);
    
    if (conversationId) {
      // 如果URL中有conversationId，加载对应的会话
      // 只有当conversationId与当前状态不同时才加载，避免重复加载
      if (!conversationState || conversationState.conversationId !== conversationId) {
        console.log('检测到conversationId变化，加载会话:', conversationId);
        loadConversation(conversationId);
      }
    } else {
      // 如果没有conversationId，立即重置所有状态（新建会话）
      console.log('没有conversationId，立即重置状态为新建会话');
      resetNewConversationState();
    }
  }, [conversationId]); // 只依赖conversationId

  // 获取热门模板
  useEffect(() => {
    if (selectedProjectId) {
      fetchHotTemplates();
    }
  }, [selectedProjectId]);

  // 🔧 新增：监听重置新建会话事件
  useEffect(() => {
    const handleResetNewConversation = (event: any) => {
      console.log('🔄 接收到重置新建会话事件:', event.detail);
      resetNewConversationState();
    };

    window.addEventListener('resetNewConversation', handleResetNewConversation);
    
    return () => {
      window.removeEventListener('resetNewConversation', handleResetNewConversation);
    };
  }, []);

  // 🔧 新增：连接状态清理的useEffect hook
  useEffect(() => {
    return () => {
      // 组件卸载时清理连接
      const currentConnectionId = connectionIdRef.current;
      if (eventSourceRef.current) {
        console.log(`🧹 组件卸载时清理连接 (connectionId: ${currentConnectionId})`);
        if ('close' in eventSourceRef.current) {
          eventSourceRef.current.close();
        } else if ('cancel' in eventSourceRef.current) {
          eventSourceRef.current.cancel();
        }
        eventSourceRef.current = null;
      }
      
      // 清理连接标识符
      connectionIdRef.current = null;
      
      // 清理定时器
      if (timelineLoadingTimerRef.current) {
        clearTimeout(timelineLoadingTimerRef.current);
      }
    };
  }, []);

  // 🔧 新增：会话ID变化时的连接清理
  useEffect(() => {
    return () => {
      // 会话ID变化时，确保旧连接被清理
      if (eventSourceRef.current && connectionIdRef.current) {
        console.log(`🧹 会话变化时清理旧连接 (connectionId: ${connectionIdRef.current})`);
        if ('close' in eventSourceRef.current) {
          eventSourceRef.current.close();
        } else if ('cancel' in eventSourceRef.current) {
          eventSourceRef.current.cancel();
        }
        eventSourceRef.current = null;
        connectionIdRef.current = null;
        setCurrentConnectionId(null);
      }
    };
  }, [conversationId]);

  // 滚动到分析区域底部
  const scrollToBottom = () => {
    setTimeout(() => {
      if (analysisContainerRef.current) {
        analysisContainerRef.current.scrollTo({
          top: analysisContainerRef.current.scrollHeight,
          behavior: 'smooth'
        });
      }
    }, 300);
  };

  // 滚动到时间线底部
  useEffect(() => {
    if (streaming || streamComplete) {
      scrollToBottom();
    }
  }, [analysisSteps, streaming, streamComplete, finalReport]);

  // 管理时间轴loading状态
  useEffect(() => {
    console.log('🔄 Streaming状态变化:', { 
      streaming, 
      timelineLoading, 
      conversationState: conversationState ? {
        currentRound: conversationState.currentRound,
        roundsLength: conversationState.rounds.length
      } : null 
    });
    
    // 清除之前的定时器
    if (timelineLoadingTimerRef.current) {
      clearTimeout(timelineLoadingTimerRef.current);
    }
    
    if (streaming) {
      // 开始streaming时，设置定时器在一定时间后显示loading效果
      console.log('⏰ 设置时间轴loading定时器 (1.5秒)');
      timelineLoadingTimerRef.current = setTimeout(() => {
        console.log('✅ 定时器触发，显示时间轴loading');
        setTimelineLoading(true);
      }, 1500); // 1.5秒后显示loading
    } else {
      // 停止streaming时，立即隐藏loading
      console.log('❌ 隐藏时间轴loading');
      setTimelineLoading(false);
    }
    
    // 清理函数
    return () => {
      if (timelineLoadingTimerRef.current) {
        clearTimeout(timelineLoadingTimerRef.current);
      }
    };
  }, [streaming, conversationState]);



  // 创建新会话
  const createNewConversation = (projectId: string) => {
    const newConversationId = generateConversationId();
    const newConversation: ConversationState = {
      conversationId: newConversationId,
      rounds: [],
      currentRound: -1,
      isMultiRound: true,
      projectId: projectId,
      title: '新的分析会话'
    };
    setConversationState(newConversation);
  };

  // 创建新会话并立即开始分析
  const createNewConversationAndStartAnalysis = async (projectId: string, query: string) => {
    try {
      // 先调用后端API创建会话
      const conversationResponse = await conversationApi.createConversation({
        project_id: projectId,
        initial_query: query
      });
      
      const createdConversation = conversationResponse.data;
      const newConversationId = createdConversation.id;
      
      console.log('✅ 后端创建会话成功，会话ID:', newConversationId);
      
      // 🔧 立即触发会话创建事件，通知App.tsx刷新对话列表并设置选中状态
      window.dispatchEvent(new CustomEvent('conversationCreated', {
        detail: { conversationId: newConversationId }
      }));
      
      // 🔧 更新URL，确保会话ID在地址栏中显示
      window.history.replaceState(null, '', `/analysis/multi-round/${newConversationId}`);
      
      const newRound: AnalysisRound = {
        roundId: generateRoundId(),
        query: query,
        analysisId: '',
        analysisSteps: [],
        toolResults: [],
        finalReport: '',
        status: 'running',
        timestamp: new Date().toISOString(),
        mcpResults: {}
      };
      
      const newConversation: ConversationState = {
        conversationId: newConversationId,
        rounds: [newRound],
        currentRound: 0,
        isMultiRound: true,
        projectId: projectId,
        title: createdConversation.title || (query.length > 30 ? `${query.substring(0, 30)}...` : query)
      };
      
      // 设置会话状态
      setConversationState(newConversation);
      

      
      // 重置当前分析状态
      console.log('🚀 第一轮分析开始：设置streaming=true');
      setStreaming(true);
      setError('');
      setAnalysisSteps([]);
      setCurrentStep('');
      setIntentAnalysis(null);
      setToolResults([]);
      setFinalReport('');

      setAnalysisId('');
      setStreamComplete(false);
      setMcpResults({});
      setShowContinueInput(false);
      setContextSummary('');
      setIntentConfirmation(null);
      setShowIntentConfirmation(false);
      setIntentAdjustment('');
      
      // 🔧 重置打断相关状态
      setWaitingForFeedback(false);
      setIsInterrupting(false);
      setFeedbackModalVisible(false);
      setCurrentInterruptId(null);
      setInterruptFeedback({
        adjustmentType: 'strategy',
        feedback: '',
        resetPlanning: false
      });
      
      // 🔧 触发分析开始事件，通知App.tsx刷新对话列表
      window.dispatchEvent(new CustomEvent('analysisStarted', {
        detail: { conversationId: newConversationId, query: query }
      }));
      
      // 🔧 生成唯一连接ID
      const newConnectionId = `conn_${newConversationId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      connectionIdRef.current = newConnectionId;
      setCurrentConnectionId(newConnectionId);
      console.log(`🔗 创建新连接 (connectionId: ${newConnectionId})`);
      
      // 使用新的多轮分析API
      const roundAnalysisResult = await multiRoundAnalysisApi.executeRoundAnalysis(
        newConversationId,
        {
          query: query,
          round_number: 1,
          max_planning_rounds: 25,
          use_full_context: false, // 第一轮不需要上下文
          context_depth: 0
        },
        {
          onStart: () => {
            console.log('📋 onStart回调：设置第一个分析步骤');
            setAnalysisSteps([{
              id: 'start',
              name: t('analysis:steps.roundAnalysisStart', { round: 1, defaultValue: '第 1 轮分析开始' }),
              status: 'process',
              time: new Date().toISOString()
            }]);
          },
          onEvent: (eventType: string, eventData: any) => {
            handleLlmStreamEvent(eventType, eventData, newConnectionId);
          },
          onError: (error: any) => {
            const errorMessage = formatErrorMessage(error);
            setError(t('analysis:errors.multiRoundAnalysisError', { error: errorMessage, defaultValue: '多轮分析出错: ' + errorMessage }));
            updateCurrentRoundStatus('failed');
          },
          onComplete: (id: string) => {
            console.log('🏁 onComplete回调：分析完成，设置streaming=false');
            setAnalysisId(id);
            setStreaming(false);
            setStreamComplete(true);
            
            // 使用setTimeout确保状态更新完成后再保存
            setTimeout(() => {
              updateCurrentRoundStatus('completed');
            }, 100);
            
            message.success(t('analysis:steps.completed', { defaultValue: '分析完成' }));
          }
        }
      );
      
      // 保存EventSource引用以便取消
      eventSourceRef.current = roundAnalysisResult;
      
      // 清空表单
      form.resetFields();
      
    } catch (error: any) {
      console.error('创建会话并开始分析失败:', error);
      message.error(t('analysis:errors.startAnalysisFailed', { error: formatErrorMessage(error), defaultValue: '启动分析失败: ' + formatErrorMessage(error) }));
      setStreaming(false);
    }
  };

  // 加载现有会话
  const loadConversation = async (convId: string) => {
    try {
      setLoading(true);
      
      // 🔧 加载会话时清理时间轴loading状态
      if (timelineLoadingTimerRef.current) {
        clearTimeout(timelineLoadingTimerRef.current);
        timelineLoadingTimerRef.current = null;
      }
      setTimelineLoading(false);
      setStreaming(false);
      
      // 🔧 重置打断相关状态
      setWaitingForFeedback(false);
      setIsInterrupting(false);
      setFeedbackModalVisible(false);
      setCurrentInterruptId(null);
      setInterruptFeedback({
        adjustmentType: 'strategy',
        feedback: '',
        resetPlanning: false
      });
      
      // 1. 加载会话详情
      const conversationResponse = await conversationApi.getConversation(convId);
      const conversation = conversationResponse.data?.data || conversationResponse.data;
      
      // 2. 加载完整分析状态
      const analysesResponse = await conversationApi.getConversationAnalyses(convId);
      const analyses = analysesResponse.data?.data || analysesResponse.data || [];
      
      console.log('加载的分析数据:', analyses);
      console.log('原始响应结构:', analysesResponse.data);
      
      // 检查数据格式
      if (!Array.isArray(analyses)) {
        console.error('分析数据不是数组格式:', analyses);
        throw new Error(t('analysis:errors.analysisDataFormatError', { defaultValue: '分析数据格式错误，期望数组格式' }));
      }
      
      // 3. 重建轮次数据
      const rounds: AnalysisRound[] = analyses.map((analysis: any) => {
        console.log('处理分析记录:', analysis);
        
        // 优先使用详细步骤记录，如果没有则使用analysis_steps
        let rebuiltSteps: any[] = [];
        let rebuiltMcpResults: Record<string, any> = {};
        let rebuiltIntentAnalysis: any = null;
        let rebuiltChartDataList: any[] = [];

        
        // 从step_details重建步骤（优先使用）
        if (analysis.step_details && Array.isArray(analysis.step_details)) {
          console.log('使用step_details重建步骤:', analysis.step_details);
          
          // 计算规划轮次 - 用于回显显示
          const calculatePlanningRound = (stepId: string, stepName: string): number => {
            // 从step_id中提取轮次信息，格式如：tool_planning_12, tool_planning_18
            const planningMatch = stepId.match(/tool_planning_(\d+)/);
            if (planningMatch) {
              const stepNumber = parseInt(planningMatch[1]);
              // 每轮分析的规划步骤从1开始计算
              // 根据step_number计算轮次：12->1, 18->2, 24->3 等
              // 一般每轮规划间隔约6个步骤号
              return Math.floor((stepNumber - 12) / 6) + 1;
            }
            
            // 备用：从步骤名称中提取轮次信息（支持中文和英文）
            const nameMatch = stepName.match(/第(\d+)轮规划/);
            if (nameMatch) {
              return parseInt(nameMatch[1]);
            }
            
            // 英文格式：Round X Planning
            const nameMatchEn = stepName.match(/Round (\d+) Planning/i);
            if (nameMatchEn) {
              return parseInt(nameMatchEn[1]);
            }
            
            return 1; // 默认第1轮
          };
          
          rebuiltSteps = analysis.step_details.map((step: any) => {
            const stepData: any = {
              id: step.step_id || step.id,
              name: step.step_name || step.name,
              status: step.status,
              time: step.start_time || step.time,
              description: step.description,
              tool_name: step.tool_name,
              parameters: step.parameters,
              hasResult: step.has_result || step.hasResult,
              resultKey: step.result_key || step.resultKey,
              hasIntentData: step.has_intent_data || step.hasIntentData,
              hasReport: step.has_report || step.hasReport,
              hasChartData: step.has_chart_data || step.hasChartData,
              planData: step.plan_data || step.planData,
              reasoning: step.reasoning,
              executionTime: step.execution_time || step.executionTime,
              errorMessage: step.error_message || step.errorMessage,
              interactionType: step.interaction_type || step.interactionType,
              interactionData: step.interaction_data || step.interactionData,
            };

            // 格式化步骤名称，添加粗体标记
            if (stepData.name) {
              if (stepData.name.startsWith('智能评估: ')) {
                stepData.name = stepData.name.replace('智能评估: ', '**智能评估:** ');
              } else if (stepData.name.startsWith('执行工具: ')) {
                stepData.name = stepData.name.replace('执行工具: ', '**执行工具:** ');
              }
            }
            
            // 处理tool_planning类型的步骤，计算并添加规划轮次信息
            if (stepData.id && stepData.id.startsWith('tool_planning_')) {
              const planningRound = calculatePlanningRound(stepData.id, stepData.name);
              
              // 检查步骤名称是否已经包含规划轮次信息（支持多语言）
              const hasRoundInfo = stepData.name.includes('第') && stepData.name.includes('轮规划') || 
                                   stepData.name.toLowerCase().includes('round') && stepData.name.toLowerCase().includes('planning');
              
              // 如果步骤名称中没有轮次信息，则添加轮次显示（使用多语言）
              if (!hasRoundInfo) {
                stepData.name = t('analysis:steps.planningRound', { 
                  round: planningRound, 
                  reasoning: stepData.name, 
                  defaultValue: `**第${planningRound}轮规划:** ${stepData.name}` 
                });
              }
              
              // 添加planData用于保持一致性
              if (!stepData.planData) {
                stepData.planData = {
                  planning_rounds: planningRound,
                  reasoning: stepData.reasoning || stepData.name
                };
              }
            }
            
            // 🔧 修复：处理洞察步骤类型（支持step_type或step_id判断）
            if (step.step_type === 'insight' || stepData.id?.startsWith('insight_')) {
              stepData.hasInsights = true;

              // 🔧 修复：设置洞察步骤的正确名称
              if (!stepData.name || stepData.name === step.step_name) {
                const toolName = step.tool_name || '分析工具';
                stepData.name = t('analysis:steps.insightDiscovered', {
                  toolName: toolName,
                  defaultValue: `${toolName} - 发现洞察`
                });
              }

              // 从parameters中恢复洞察数据
              if (step.parameters) {
                stepData.insightData = {
                  insights: step.parameters.insights || [],
                  patterns: step.parameters.patterns || [],
                  anomalies: step.parameters.anomalies || [],
                  correlations: step.parameters.correlations || [],
                  statistical_summary: step.parameters.statistical_summary || null,
                  data_quality: step.parameters.data_quality || null
                };

                // 🔧 修复：构建洞察摘要作为描述（多语言支持）
                const insightSummary = [];
                if (stepData.insightData.insights && stepData.insightData.insights.length > 0) {
                  insightSummary.push(t('analysis:insights.summary.foundInsights', {
                    count: stepData.insightData.insights.length,
                    defaultValue: `发现 ${stepData.insightData.insights.length} 个关键洞察`
                  }));
                }
                if (stepData.insightData.patterns && stepData.insightData.patterns.length > 0) {
                  insightSummary.push(t('analysis:insights.summary.dataPatterns', {
                    count: stepData.insightData.patterns.length,
                    defaultValue: `${stepData.insightData.patterns.length} 个数据模式`
                  }));
                }
                if (stepData.insightData.anomalies && stepData.insightData.anomalies.length > 0) {
                  insightSummary.push(t('analysis:insights.summary.anomalies', {
                    count: stepData.insightData.anomalies.length,
                    defaultValue: `${stepData.insightData.anomalies.length} 个异常`
                  }));
                }

                stepData.description = insightSummary.length > 0
                  ? insightSummary.join(t('analysis:insights.summary.separator', { defaultValue: '，' }))
                  : t('analysis:insights.summary.defaultDescription', { defaultValue: '发现数据洞察' });

                // 同时将洞察数据添加到mcpResults中
                if (step.result_key || step.step_id) {
                  const resultKey = step.result_key || step.step_id;
                  rebuiltMcpResults[resultKey] = stepData.insightData;
                }
              }
            }

            // 处理特殊步骤类型 - 支持interaction和tool类型
            if (step.step_type === 'interaction') {
              switch (step.interaction_type) {
                case 'intent_confirmation':
                  if (step.interaction_data?.intent_analysis) {
                    rebuiltIntentAnalysis = step.interaction_data.intent_analysis;
                  }
                  
                  // 为意图确认步骤添加结果数据，确保能正确渲染
                  if (step.step_name?.includes('意图确认') || step.step_name?.includes('Intent Confirmation') || step.tool_name === '意图确认') {
                    stepData.hasResult = true;
                    stepData.resultKey = step.step_id;
                    stepData.tool_name = '意图确认';
                    
                    // 将意图确认数据添加到mcpResults中，使用固定的key以保持一致性
                    const confirmationData = {
                      ...step.interaction_data,
                      intent_analysis: step.interaction_data?.intent_analysis || rebuiltIntentAnalysis,
                      confirmation_completed: true,
                      // 确保包含所有必要字段
                      original_query: step.interaction_data?.original_query || '',
                      user_adjustment: step.interaction_data?.user_adjustment || '',
                      has_user_modification: step.interaction_data?.has_user_modification || false,
                      clarification_answers: step.interaction_data?.clarification_answers || {}
                    };
                    
                    // 同时使用步骤ID和固定key保存，确保兼容性
                    rebuiltMcpResults[step.step_id] = confirmationData;
                    rebuiltMcpResults['intent_confirmation_request'] = confirmationData;
                  }
                  break;
                case 'chart_display':
                  if (step.interaction_data?.chart_data) {
                    // 添加到图表数据列表
                    rebuiltChartDataList.push({
                      ...step.interaction_data.chart_data,
                      chartId: step.step_id || `chart_${Date.now()}`,
                      stepId: step.step_id,
                      generatedAt: step.start_time || new Date().toISOString()
                    });
                    
                    // @fixed_implementation_start
                    // 实现标识: 历史图表数据还原修复
                    // 功能描述: 确保图表数据在历史记录还原时正确映射到mcpResults中
                    // 修复内容: 将interactionData.chart_data映射到mcpResults[step_id]，确保renderStepResult能找到数据
                    // 状态: 已确认 (CONFIRMED_IMPLEMENTATION)
                    // @fixed_implementation_end
                    
                    // 同时添加到mcpResults中，确保renderStepResult能找到数据
                    if (step.step_id) {
                      // 检查数据是否在chart_data.data中（MoE生成的数据结构）
                      const chartData = step.interaction_data.chart_data.data || step.interaction_data.chart_data;
                      rebuiltMcpResults[step.step_id] = chartData;
                    }
                  }
                  break;
              }
            }
            
            // 检查是否是打断反馈步骤
            if (step.tool_name === '用户反馈' || 
                step.step_name === '应用用户反馈' || 
                step.step_id?.startsWith('feedback_applied_')) {
              console.log('🔍 识别到打断反馈步骤:', step);
              
              // 标记为打断步骤
              stepData.isInterrupt = true;
              stepData.interruptData = {
                interrupt_id: step.parameters?.interrupt_id || step.step_id,
                status: 'adjusted',
                user_feedback: step.parameters?.feedback || step.description || '',
                adjustment_type: step.parameters?.adjustment_type || 'strategy',
                adjustment_summary: step.parameters?.adjustment_summary || step.description || '',
                interrupt_timestamp: step.start_time || step.time || new Date().toISOString()
              };
              
              console.log('✅ 打断反馈步骤已标记:', stepData);
            }
            
            return stepData;
          });
        } else if (analysis.analysis_steps && Array.isArray(analysis.analysis_steps)) {
          // 备用：使用analysis_steps
          console.log('使用analysis_steps重建步骤:', analysis.analysis_steps);
          
          // 计算规划轮次 - 用于回显显示
          const calculatePlanningRound = (stepId: string, stepName: string): number => {
            // 从step_id中提取轮次信息，格式如：tool_planning_12, tool_planning_18
            const planningMatch = stepId.match(/tool_planning_(\d+)/);
            if (planningMatch) {
              const stepNumber = parseInt(planningMatch[1]);
              // 每轮分析的规划步骤从1开始计算
              // 根据step_number计算轮次：12->1, 18->2, 24->3 等
              // 一般每轮规划间隔约6个步骤号
              return Math.floor((stepNumber - 12) / 6) + 1;
            }
            
            // 备用：从步骤名称中提取轮次信息（支持中文和英文）
            const nameMatch = stepName.match(/第(\d+)轮规划/);
            if (nameMatch) {
              return parseInt(nameMatch[1]);
            }
            
            // 英文格式：Round X Planning
            const nameMatchEn = stepName.match(/Round (\d+) Planning/i);
            if (nameMatchEn) {
              return parseInt(nameMatchEn[1]);
            }
            
            return 1; // 默认第1轮
          };
          
          // 转换analysis_steps格式以保持一致性
          rebuiltSteps = analysis.analysis_steps.map((step: any) => {
            const stepData: any = {
              id: step.id,
              name: step.name,
              status: step.status,
              time: step.time,
              description: step.description,
              tool_name: step.tool_name,
              parameters: step.parameters,
              hasResult: step.hasResult,
              resultKey: step.resultKey,
              hasIntentData: step.hasIntentData,
              hasReport: step.hasReport,
              hasChartData: step.hasChartData,
              planData: step.planData,
              reasoning: step.reasoning,
              executionTime: step.executionTime,
              errorMessage: step.errorMessage,
              interactionType: step.interactionType,
              interactionData: step.interactionData,
            };
            
            // 处理tool_planning类型的步骤，计算并添加规划轮次信息
            if (stepData.id && stepData.id.startsWith('tool_planning_')) {
              const planningRound = calculatePlanningRound(stepData.id, stepData.name);
              
              // 检查步骤名称是否已经包含规划轮次信息（支持多语言）
              const hasRoundInfo = stepData.name.includes('第') && stepData.name.includes('轮规划') || 
                                   stepData.name.toLowerCase().includes('round') && stepData.name.toLowerCase().includes('planning');
              
              // 如果步骤名称中没有轮次信息，则添加轮次显示（使用多语言）
              if (!hasRoundInfo) {
                stepData.name = t('analysis:steps.planningRound', { 
                  round: planningRound, 
                  reasoning: stepData.name, 
                  defaultValue: `**第${planningRound}轮规划:** ${stepData.name}` 
                });
              }
              
              // 添加planData用于保持一致性
              if (!stepData.planData) {
                stepData.planData = {
                  planning_rounds: planningRound,
                  reasoning: stepData.reasoning || stepData.name
                };
              }
            }
            
            // 检查是否是打断反馈步骤
            if (step.tool_name === '用户反馈' || 
                step.name === '应用用户反馈' || 
                step.id?.startsWith('feedback_applied_')) {
              console.log('🔍 识别到打断反馈步骤 (analysis_steps):', step);
              
              // 标记为打断步骤
              stepData.isInterrupt = true;
              stepData.interruptData = {
                interrupt_id: step.parameters?.interrupt_id || step.id,
                status: 'adjusted',
                user_feedback: step.parameters?.feedback || step.description || '',
                adjustment_type: step.parameters?.adjustment_type || 'strategy',
                adjustment_summary: step.parameters?.adjustment_summary || step.description || '',
                interrupt_timestamp: step.time || new Date().toISOString()
              };
              
              console.log('✅ 打断反馈步骤已标记 (analysis_steps):', stepData);
            }
            
            console.log('转换步骤数据:', {
              stepId: step.id,
              toolName: step.tool_name,
              hasParameters: !!step.parameters,
              parametersKeys: step.parameters ? Object.keys(step.parameters) : [],
              hasQuestions: !!(step.parameters?.questions),
              questionsCount: step.parameters?.questions?.length || 0
            });
            
            return stepData;
          });
          
          // 从analysis_steps中提取意图分析和交互数据
          analysis.analysis_steps.forEach((step: any) => {
            // 提取意图确认数据
            if (step.interactionType === 'intent_confirmation' && step.interactionData?.intent_analysis) {
              rebuiltIntentAnalysis = step.interactionData.intent_analysis;
              console.log('从analysis_steps提取意图分析:', rebuiltIntentAnalysis);
            }
            

            
            // 提取图表数据
            if (step.interactionType === 'chart_display' && step.interactionData?.chart_data) {
              rebuiltChartDataList.push({
                ...step.interactionData.chart_data,
                chartId: step.id || `chart_${Date.now()}`,
                stepId: step.id,
                generatedAt: step.time || new Date().toISOString()
              });
              
              // 同时添加到mcpResults中，确保renderStepResult能找到数据
              if (step.id) {
                // 检查数据是否在chart_data.data中（MoE生成的数据结构）
                const chartData = step.interactionData.chart_data.data || step.interactionData.chart_data;
                rebuiltMcpResults[step.id] = chartData;
              }
            }
          });
        }
        
        // 从tool_executions重建MCP结果
        if (analysis.tool_executions && Array.isArray(analysis.tool_executions)) {
          analysis.tool_executions.forEach((execution: any) => {
            if (execution.step_id && execution.result) {
              rebuiltMcpResults[execution.step_id] = execution.result;
            }
          });
        }
        
        // 从mcp_results补充结果（如果有）
        if (analysis.mcp_results) {
          if (Array.isArray(analysis.mcp_results)) {
            analysis.mcp_results.forEach((result: any, index: number) => {
              const key = `mcp_result_${index}`;
              rebuiltMcpResults[key] = result;
            });
          } else if (typeof analysis.mcp_results === 'object') {
            Object.assign(rebuiltMcpResults, analysis.mcp_results);
          }
        }
        
        // 从intent_analysis字段获取意图分析结果
        if (analysis.intent_analysis && !rebuiltIntentAnalysis) {
          rebuiltIntentAnalysis = analysis.intent_analysis;
        }
        
        console.log('重建的步骤数据:', {
          steps: rebuiltSteps.length,
          mcpResults: Object.keys(rebuiltMcpResults).length,
          intentAnalysis: !!rebuiltIntentAnalysis,
          chartDataList: rebuiltChartDataList.length,

        });
        
        console.log('🔍 处理分析数据（不再处理静态打断信息）:', {
          analysisId: analysis.analysis_id || analysis.id
        });
        
        return {
          roundId: `round_${analysis.analysis_id || analysis.id}`,
          query: analysis.query,
          analysisId: analysis.analysis_id || analysis.id,
          analysisSteps: rebuiltSteps,
          toolResults: analysis.tool_executions || [],
          finalReport: analysis.final_report || analysis.result || '',
          status: analysis.status === 'completed' ? 'completed' : 
                  analysis.status === 'failed' ? 'failed' : 'completed',
          timestamp: analysis.created_at,
          intentAnalysis: rebuiltIntentAnalysis,
          mcpResults: rebuiltMcpResults,
          contextSummary: analysis.context_summary,
          chartDataList: rebuiltChartDataList
          // 不再从静态数据获取打断信息

        };
      });
      
      console.log('重建的轮次数据:', rounds);
      
      // 4. 设置会话状态
      setConversationState({
        conversationId: convId,
        rounds: rounds,
        currentRound: rounds.length - 1,
        isMultiRound: true,
        projectId: conversation.project_id,
        title: conversation.title
      });
      
      // 5. 如果有轮次数据，设置当前轮次的状态
      if (rounds.length > 0) {
        const lastRound = rounds[rounds.length - 1];
        
        // 设置分析步骤
        setAnalysisSteps(lastRound.analysisSteps || []);
        
        // 设置MCP结果
        setMcpResults(lastRound.mcpResults || {});
        
        // 设置最终报告
        setFinalReport(lastRound.finalReport || '');
        
        // 设置意图分析
        if (lastRound.intentAnalysis) {
          setIntentAnalysis(lastRound.intentAnalysis);
        }
        
        // 设置图表数据列表
        if (lastRound.chartDataList && lastRound.chartDataList.length > 0) {
          setChartDataList(lastRound.chartDataList);
        }
        
        // 不再从静态数据设置打断信息，完全依赖事件驱动
        console.log('🔍 跳过静态打断信息设置，使用事件驱动方式');
        
        // 设置分析ID
        setAnalysisId(lastRound.analysisId);
        
                  // 设置上下文摘要
          if (lastRound.contextSummary !== undefined) {
            setContextSummary(lastRound.contextSummary);
          }
        
        console.log('已还原最后一轮的状态:', {
          analysisSteps: lastRound.analysisSteps?.length || 0,
          mcpResults: Object.keys(lastRound.mcpResults || {}).length,
          finalReport: !!lastRound.finalReport,
          intentAnalysis: !!lastRound.intentAnalysis,
          chartDataList: lastRound.chartDataList?.length || 0,

        });
      }
      
      message.success(t('analysis:multiRound.conversationLoadSuccess', { defaultValue: '会话加载成功' }));
      
    } catch (error: any) {
      console.error('加载会话失败:', error);
      console.error('错误详情:', {
        message: error.message,
        response: error.response?.data,
        stack: error.stack
      });
      
      let errorMessage = '加载会话失败';
      if (error.message) {
        errorMessage += ': ' + error.message;
      } else if (error.response?.data?.message) {
        errorMessage += ': ' + error.response.data.message;
      } else {
        errorMessage += ': 未知错误';
      }
      
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

    // 开始新轮次分析
  const startNewRoundInConversation = async () => {
    if (!conversationState) return;
    
    console.log('🔄 开始新轮次分析，当前会话ID:', conversationState.conversationId);
    
    try {
      const values = await form.validateFields();
      const query = values.query;
      
      if (!query?.trim()) {
        message.warning(t('analysis:errors.queryRequired', { defaultValue: '请输入分析问题' }));
        return;
      }
      
      // 🔧 修复：在开始新轮次之前，先保存当前轮次的完整状态
      let updatedConversationState = conversationState;
      if (conversationState.rounds.length > 0 && conversationState.currentRound >= 0) {
        const currentRoundIndex = conversationState.currentRound;
        const currentRound = conversationState.rounds[currentRoundIndex];
        
        // 如果当前轮次有全局状态数据，保存到轮次中
        if (analysisSteps.length > 0 || Object.keys(mcpResults).length > 0 || finalReport) {
          console.log('💾 保存当前轮次状态到会话中:', {
            roundIndex: currentRoundIndex,
            analysisStepsLength: analysisSteps.length,
            mcpResultsKeys: Object.keys(mcpResults),
            finalReportLength: finalReport.length
          });
          
          const updatedCurrentRound = {
            ...currentRound,
            analysisSteps: [...analysisSteps],
            toolResults: [...toolResults],
            finalReport: finalReport,
            analysisId: analysisId,
            intentAnalysis: intentAnalysis,
            mcpResults: { ...mcpResults },
            contextSummary: contextSummary,
            // 不再保存静态打断信息，完全依赖事件驱动
            status: streamComplete ? 'completed' as const : (streaming ? 'running' as const : 'completed' as const)
          };
          
          const updatedRounds = [...conversationState.rounds];
          updatedRounds[currentRoundIndex] = updatedCurrentRound;
          
          updatedConversationState = {
            ...conversationState,
            rounds: updatedRounds
          };
          
          // 立即更新会话状态，确保数据被保存
          setConversationState(updatedConversationState);
          
          console.log('✅ 当前轮次状态已保存');
        }
      }
      
      const newRoundNumber = updatedConversationState.rounds.length + 1;
      
      const newRound: AnalysisRound = {
        roundId: generateRoundId(),
        query: query,
        analysisId: '',
        analysisSteps: [],
        toolResults: [],
        finalReport: '',
        status: 'running',
        timestamp: new Date().toISOString(),
        mcpResults: {},
        contextSummary: contextSummary,
        chartDataList: [],

      };
      
      // 更新会话状态 - 添加新轮次并更新currentRound
      const finalUpdatedConversation = {
        ...updatedConversationState,
        rounds: [...updatedConversationState.rounds, newRound],
        currentRound: updatedConversationState.rounds.length
      };
      setConversationState(finalUpdatedConversation);
      

      
      // 🔧 触发分析开始事件，通知App.tsx刷新对话列表
      window.dispatchEvent(new CustomEvent('analysisStarted', {
        detail: { conversationId: conversationState.conversationId, query: query, roundNumber: newRoundNumber }
      }));
      
      // 重置当前分析状态（现在可以安全重置，因为之前的数据已保存）
      setStreaming(true);
      setError('');
      setAnalysisSteps([]);
      setCurrentStep('');
      setIntentAnalysis(null);
      setToolResults([]);
      setFinalReport('');

      setAnalysisId('');
      setStreamComplete(false);
      setMcpResults({});
      setShowContinueInput(false);
      setContextSummary('');
      setIntentConfirmation(null);
      setShowIntentConfirmation(false);
      setIntentAdjustment('');
      
      // 🔧 重置打断相关状态
      setWaitingForFeedback(false);
      setIsInterrupting(false);
      setFeedbackModalVisible(false);
      setCurrentInterruptId(null);
      setInterruptFeedback({
        adjustmentType: 'strategy',
        feedback: '',
        resetPlanning: false
      });
      
      // 🔧 生成唯一连接ID
      const roundConnectionId = `conn_${conversationState.conversationId}_r${newRoundNumber}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      connectionIdRef.current = roundConnectionId;
      setCurrentConnectionId(roundConnectionId);
      console.log(`🔗 创建新轮次连接 (connectionId: ${roundConnectionId})`);
      
      // 使用新的多轮分析API
      const roundAnalysisResult = await multiRoundAnalysisApi.executeRoundAnalysis(
        conversationState.conversationId,
        {
          query: query,
          round_number: newRoundNumber,
          max_planning_rounds: 25,
          use_full_context: true,
          context_depth: 5
        },
        {
          onStart: () => {
            setAnalysisSteps([{
              id: 'start',
              name: t('analysis:steps.roundAnalysisStart', { round: newRoundNumber, defaultValue: `第 ${newRoundNumber} 轮分析开始` }),
              status: 'process',
              time: new Date().toISOString()
            }]);
          },
          onEvent: (eventType: string, eventData: any) => {
            // 处理新的事件类型
            if (eventType === 'context_loaded') {
              // 处理上下文摘要
              const summary = eventData.context_summary || '';
              
              setContextSummary(summary);
              
              setAnalysisSteps(prev => [...prev.map(s => ({...s, status: 'finish'})), {
                id: 'context_loaded',
                name: t('analysis:steps.contextLoadedWithSummary', { summary, defaultValue: `已加载对话上下文：${summary}` }),
                status: 'finish',
                time: new Date().toISOString()
              }]);
            } else if (eventType === 'context_updated') {
              // 上下文更新完成
              console.log('会话上下文已更新');
            } else {
              handleLlmStreamEvent(eventType, eventData, roundConnectionId);
            }
          },
          onError: (error: any) => {
            const errorMessage = formatErrorMessage(error);
            setError(t('analysis:errors.multiRoundAnalysisError', { error: errorMessage, defaultValue: '多轮分析出错: ' + errorMessage }));
            updateCurrentRoundStatus('failed');
          },
          onComplete: (id: string) => {
            setAnalysisId(id);
            setStreaming(false);
            setStreamComplete(true);
            
            // 立即更新轮次状态，确保数据能正确显示
            updateCurrentRoundStatus('completed');
            
            message.success(t('analysis:steps.completed', { defaultValue: '分析完成' }));
          }
        }
      );
      
      // 保存EventSource引用以便取消
      eventSourceRef.current = roundAnalysisResult;
      
      // 清空表单
      form.resetFields();
      
    } catch (error: any) {
      console.error('分析执行出错:', error);
      message.error(t('analysis:errors.analysisExecutionFailed', { error: formatErrorMessage(error), defaultValue: '分析执行失败: ' + formatErrorMessage(error) }));
      updateCurrentRoundStatus('failed');
    }
  };



  // 更新当前轮次状态
  const updateCurrentRoundStatus = (status: 'completed' | 'running' | 'failed') => {
    console.log('🔄 更新当前轮次状态:', {
      status,
      analysisStepsLength: analysisSteps.length,
      finalReportLength: finalReport.length,
      mcpResultsKeys: Object.keys(mcpResults),
      analysisId
    });
    
    setConversationState(prevConversationState => {
      if (!prevConversationState) {
        console.warn('⚠️ 没有会话状态，无法更新轮次状态');
        return prevConversationState;
      }
      
      const updatedRounds = [...prevConversationState.rounds];
      if (updatedRounds[prevConversationState.currentRound]) {
        const currentRound = updatedRounds[prevConversationState.currentRound];
        const updatedRound = {
          ...currentRound,
          status,
          analysisSteps: [...analysisSteps],
          toolResults: [...toolResults],
          finalReport,
          analysisId,
          intentAnalysis,
          mcpResults: { ...mcpResults },
          // 保存上下文摘要信息
          contextSummary
        };
        
        updatedRounds[prevConversationState.currentRound] = updatedRound;
        
        console.log('✅ 轮次状态已更新:', {
          roundIndex: prevConversationState.currentRound,
          oldStepsLength: currentRound.analysisSteps?.length || 0,
          newStepsLength: updatedRound.analysisSteps.length,
          oldFinalReportLength: currentRound.finalReport?.length || 0,
          newFinalReportLength: updatedRound.finalReport.length,
          status: updatedRound.status
        });
      } else {
        console.warn('⚠️ 当前轮次不存在，无法更新状态');
      }
      
      return {
        ...prevConversationState,
        rounds: updatedRounds
      };
    });
  };



  // 继续分析
  const handleContinueAnalysis = () => {
    setShowContinueInput(true);
    // 滚动到输入框
    setTimeout(() => {
      const inputElement = document.querySelector('.continue-input textarea');
      if (inputElement) {
        inputElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        (inputElement as HTMLTextAreaElement).focus();
      }
    }, 100);
  };

  // 开始新对话
  const handleNewConversation = () => {
    console.log('handleNewConversation 被调用');
    if (!selectedProjectId) {
      message.warning(t('analysis:errors.noProject', { defaultValue: '请先选择项目' }));
      return;
    }
    
    // 关闭任何正在进行的EventSource连接
    if (eventSourceRef.current) {
      if ('close' in eventSourceRef.current) {
        eventSourceRef.current.close();
      } else if ('cancel' in eventSourceRef.current) {
        eventSourceRef.current.cancel();
      }
      eventSourceRef.current = null;
    }
    
    // 重置所有状态
    setConversationState(null);
    setStreaming(false);
    setError('');
    setAnalysisSteps([]);
    setCurrentStep('');
    setIntentAnalysis(null);
    setToolResults([]);
    setFinalReport('');

    setAnalysisId('');
    setStreamComplete(false);
    setMcpResults({});
    setShowContinueInput(false);
    setContextSummary('');
    setIntentConfirmation(null);
    setShowIntentConfirmation(false);
    setIntentAdjustment('');
    form.resetFields();
    
    console.log('准备导航到新的多轮分析页面');
    // 导航到新的多轮分析页面（不带conversationId）
    // 使用replace确保强制导航
    navigate('/analysis/multi-round', { replace: true });
    console.log('导航完成');
    
    message.success(t('analysis:multiRound.newConversationCreated', { defaultValue: '已创建新会话，请开始您的第一个问题' }));
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>, formInstance: any) => {
    // Shift + Enter: 换行（默认行为）
    if (e.key === 'Enter' && e.shiftKey) {
      // 允许默认的换行行为
      return;
    }

    // Enter: 提交表单
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault(); // 阻止默认的换行行为

      // 检查是否正在分析中
      if (streaming || loading) {
        return;
      }

      // 触发表单提交
      formInstance.submit();
    }
  };

  // 处理分析提交
  const handleAnalysis = async () => {
    if (!conversationState) {
      // 如果没有会话状态，创建新会话并立即开始分析
      const projectId = localStorage.getItem('selectedProjectId');
      if (!projectId) {
        message.error(t('analysis:errors.noProjectDescription', { defaultValue: '请先在顶部选择项目' }));
        return;
      }
      
      // 先验证表单
      let formValues;
      try {
        formValues = await form.validateFields();
      } catch (error) {
        return; // 表单验证失败，直接返回
      }
      
      // 创建新会话并立即开始分析
      await createNewConversationAndStartAnalysis(projectId, formValues.query);
    } else {
      // 确保有选择的项目ID
      if (!selectedProjectId) {
        message.error(t('analysis:errors.noProjectDescription', { defaultValue: '请先在顶部选择项目' }));
        return;
      }
      
      await startNewRoundInConversation();
    }
  };

  // 取消分析
  const cancelAnalysis = async () => {
    if (!streaming) return;
    
    try {
      if (eventSourceRef.current) {
        if ('close' in eventSourceRef.current) {
          eventSourceRef.current.close();
        } else if ('cancel' in eventSourceRef.current) {
          eventSourceRef.current.cancel();
        }
        eventSourceRef.current = null;
      }
      
      if (analysisId) {
        try {
          const response = await llmAnalysisApiExtended.cancelLlmAnalysis(analysisId);
          if (response.data.code === 0) {
            message.info(t('analysis:errors.analysisCancelled'));
          }
        } catch (apiError) {
          console.warn('调用取消API失败:', apiError);
        }
      }
      
      setStreaming(false);
      updateCurrentRoundStatus('failed');
      
      // 更新分析步骤状态，将正在执行的步骤标记为失败
      setAnalysisSteps((prev: any[]) => 
        prev.map(step => ({
          ...step,
          status: step.status === 'process' ? 'error' : step.status,
          name: step.status === 'process' ? `${step.name} (已取消)` : step.name
        }))
      );
      
      // 清除当前步骤状态
      setCurrentStep('cancelled');
      
    } catch (error: any) {
      console.error('取消分析时出错:', error);
      message.error(t('analysis:errors.cancelAnalysisFailed', { defaultValue: '取消分析失败' }));
      setStreaming(false);
    }
  };



  // 提交用户反馈（优化后：只提交反馈，打断信号已发送）
  const submitUserFeedback = async () => {
    if (!analysisId || !currentInterruptId) {
      message.warning(t('analysis:errors.missingInterruptInfo'));
      return;
    }

    if (!interruptFeedback.feedback.trim()) {
      message.warning(t('analysis:errors.pleaseFillFeedback'));
      return;
    }

    try {
      setLoading(true);
      
      // 提交反馈
      const feedbackData = {
        interrupt_id: currentInterruptId,
        adjustment_type: 'strategy', // 固定为策略调整
        feedback: interruptFeedback.feedback,
        reset_planning: false // 固定为不重新规划
      };
      
      const response = await llmAnalysisApiExtended.submitUserFeedback(analysisId, feedbackData);
      
      if (response.data.code === 0) {
        message.success(t('analysis:errors.feedbackSubmitted'));
        setFeedbackModalVisible(false);
        setWaitingForFeedback(false);
        setCurrentInterruptId(null);
        // 重置反馈表单
        setInterruptFeedback({
          adjustmentType: 'strategy',
          feedback: '',
          resetPlanning: false
        });
        
        // 打断信息将通过SSE事件自动更新，无需手动刷新
      } else {
        message.error(response.data.message || t('analysis:errors.submitFeedbackFailed'));
      }
    } catch (error) {
      console.error('提交反馈失败:', error);
      message.error(t('analysis:errors.submitFeedbackFailedRetry'));
    } finally {
      setLoading(false);
    }
  };

  // 直接打断分析并显示反馈弹窗（优化后：点击即打断）
  const showInterruptModal = async () => {
    if (!streaming) {
      message.warning(t('analysis:errors.noAnalysisInProgress'));
      return;
    }
    
    if (!analysisId) {
      message.warning(t('analysis:errors.noAnalysisRunning'));
      return;
    }

    try {
      setIsInterrupting(true);
      
      // 立即发送打断信号
      const response = await llmAnalysisApiExtended.interruptAnalysis(analysisId);
      
      if (response.data.code === 0) {
        message.success(t('analysis:errors.analysisInterrupted'));
        setCurrentInterruptId(response.data.data.interrupt_id);
        setWaitingForFeedback(true);
        setFeedbackModalVisible(true);
      } else {
        message.error(response.data.message || t('analysis:errors.interruptAnalysisFailed', { defaultValue: '打断分析失败' }));
      }
    } catch (error) {
      console.error('打断分析失败:', error);
      message.error(t('analysis:errors.interruptAnalysisFailedRetry'));
    } finally {
      setIsInterrupting(false);
    }
  };

  // 处理LLM流式事件 (复用原有逻辑) - 🔧增强连接验证
  const handleLlmStreamEvent = (eventType: string, eventData: any, connectionId?: string) => {
    // 🔧 连接验证：确保事件来自当前有效的连接
    if (connectionId && connectionIdRef.current && connectionId !== connectionIdRef.current) {
      console.log(`🚫 事件被忽略：连接ID不匹配 (收到: ${connectionId}, 当前: ${connectionIdRef.current})`);
      return;
    }
    
    // 🔧 状态验证：如果正在切换会话，忽略所有事件
    if (isConversationSwitching) {
      console.log(`🚫 事件被忽略：正在切换会话中`);
      return;
    }
    
    // 每次收到事件时，重置loading定时器
    console.log(`📨 收到事件: ${eventType}, streaming: ${streaming}, timelineLoading: ${timelineLoading}, connectionId: ${connectionId}`);
    
    // 清除之前的定时器
    if (timelineLoadingTimerRef.current) {
      clearTimeout(timelineLoadingTimerRef.current);
    }
    
    // 如果正在streaming，设置定时器在一定时间后显示loading效果
    if (streaming) {
      // 暂时隐藏loading，然后重新设置定时器
      setTimelineLoading(false);
      console.log('⏰ 重新设置时间轴loading定时器 (1.5秒)');
      timelineLoadingTimerRef.current = setTimeout(() => {
        console.log('✅ 事件间隔后显示时间轴loading');
        setTimelineLoading(true);
      }, 1500); // 1.5秒后显示loading
    }
    
    switch (eventType) {
      case 'start':
        // 检查是否是继续分析，如果是则不重置步骤
        if (analysisSteps.length > 0 && analysisId) {
          console.log('继续分析中，跳过start事件的步骤重置');
          return;
        }
        setCurrentStep('start');
        setAnalysisSteps([{
          id: 'start',
          name: t('analysis:steps.start'),
          status: 'process',
          time: new Date().toISOString()
        }]);
        break;
        
      case 'analysis_created':
        // 检查是否是继续分析，如果是则不重复添加创建分析记录步骤
        if (analysisSteps.some(step => step.id === 'analysis_created') && analysisId === eventData.id) {
          console.log('继续分析中，跳过重复的analysis_created事件');
          return;
        }
        setAnalysisId(eventData.id);
        setCurrentStep('analysis_created');
        setAnalysisSteps((prev: any[]) => [
          ...prev.map(s => ({...s, status: 'finish'})),
          {
            id: 'analysis_created',
            name: t('analysis:steps.analysisCreated'),
            status: 'process',
            time: new Date().toISOString()
          }
        ]);
        break;
        
      case 'analysis_continued':
        // 处理继续分析事件 - 不显示给用户，这是内部技术细节
        console.log('处理analysis_continued事件（内部处理，不显示给用户）:', eventData);
        setCurrentStep('analysis_continued');
        // 只更新现有步骤状态为完成，不添加新的"恢复分析上下文"步骤
        setAnalysisSteps((prev: any[]) => prev.map(s => ({...s, status: 'finish'})));
        break;
        
      case 'tools_loaded':
        // 检查是否是继续分析，如果是则不重复添加工具加载步骤
        if (analysisSteps.some(step => step.id === 'tools_loaded')) {
          console.log('继续分析中，跳过重复的tools_loaded事件');
          return;
        }
        setCurrentStep('tools_loaded');
        setAnalysisSteps((prev: any[]) => [
          ...prev.map(s => ({...s, status: 'finish'})),
          {
            id: 'tools_loaded',
            name: t('analysis:steps.toolsLoaded', { count: eventData.tool_count, defaultValue: `已加载 ${eventData.tool_count} 个分析工具` }),
            status: 'finish',
            time: new Date().toISOString()
          }
        ]);
        break;
        
      case 'schema_loaded':
        // 检查是否是继续分析，如果是则不重复添加Schema加载步骤
        if (analysisSteps.some(step => step.id === 'schema_loaded')) {
          console.log('继续分析中，跳过重复的schema_loaded事件');
          return;
        }
        setCurrentStep('schema_loaded');
        setAnalysisSteps((prev: any[]) => [
          ...prev.map(s => ({...s, status: 'finish'})),
          {
            id: 'schema_loaded',
            name: t('analysis:steps.schemaLoaded', { defaultValue: 'Schema加载完成' }),
            status: 'finish',
            time: new Date().toISOString()
          }
        ]);
        break;
        
      case 'intent_analyzed':
        const intentData = eventData.intent_analysis || eventData;
        setIntentAnalysis(intentData);
        setCurrentStep('intent_analyzed');
        setAnalysisSteps((prev: any[]) => [
          ...prev.map(s => ({...s, status: 'finish'})),
          {
            id: 'intent_analyzed',
            name: t('analysis:steps.intentAnalyzed', { defaultValue: '分析用户意图' }),
            status: 'process',
            time: new Date().toISOString(),
            hasIntentData: true
          }
        ]);
        break;

      // 新增：处理意图确认请求
      case 'intent_confirmation_request':
        console.log('收到意图确认请求:', eventData);
        console.log('意图分析数据:', eventData.intent_analysis);
        console.log('execution_steps:', eventData.intent_analysis?.execution_steps);
        setIntentConfirmation(eventData);
        setShowIntentConfirmation(true);
        setCurrentStep('intent_confirmation');
        setAnalysisSteps(prev => {
          // 更新所有之前步骤为完成状态
          const updated = prev.map(s => ({...s, status: 'finish'}));
          
          return [
            ...updated,
            {
              id: 'intent_confirmation',
                              name: t('analysis:intentConfirmation.waitingForConfirmation'),
              status: 'process',
              time: new Date().toISOString(),
              description: t('analysis:intentConfirmation.description', { defaultValue: '请确认分析意图和计划，或提供调整建议' })
            }
          ];
        });
        break;
        
      // 新增：处理意图确认完成
      case 'intent_confirmation_completed':
        console.log('意图确认完成:', eventData);
        setShowIntentConfirmation(false);
        setIntentConfirmation(null);
        setIntentAdjustment('');
        setCurrentStep('intent_confirmed');

        // 保存意图确认完成的结果数据，使用统一的简单格式
        setMcpResults((prev: Record<string, any>) => ({
          ...prev,
          'intent_confirmation_request': {  // 使用intent_confirmation_request作为key，与步骤ID保持一致
            ...eventData,
            // 保存完整的意图分析数据供前端渲染使用
            intent_analysis: {
              ...intentConfirmation?.intent_analysis,
              ...eventData.intent_analysis,
              // 确保execution_steps被保存
              execution_steps: intentConfirmation?.intent_analysis?.execution_steps || eventData.intent_analysis?.execution_steps
            },
            // 保存澄清问题的答案
            clarification_answers: eventData.clarification_answers || {},
            confirmation_completed: true,
            // 额外保存原始的意图分析数据用于后续引用
            original_intent_analysis: intentConfirmation?.intent_analysis,
            user_adjustment: eventData.user_adjustment || '',
            enhanced_query: eventData.enhanced_query || ''
          }
        }));

        setAnalysisSteps(prev => {
          // 更新意图确认步骤为完成状态，并添加结果数据
          const updated = prev.map(s =>
            s.id === 'intent_confirmation'
              ? {
                  ...s,
                  status: 'finish',
                  name: eventData.user_adjustment ? t('analysis:intentConfirmation.completedAdjusted') : t('analysis:intentConfirmation.completed'),
                  hasResult: true,
                  resultKey: 'intent_confirmation_request',  // 使用intent_confirmation_request作为resultKey
                  tool_name: t('analysis:tools.intentConfirmation', { defaultValue: '意图确认' }),
                  // 添加交互数据，确保与回调渲染格式一致
                  interactionType: 'intent_confirmation',
                  interactionData: {
                    original_query: eventData.original_query || '',
                    user_adjustment: eventData.user_adjustment || '',
                    has_user_modification: !!eventData.user_adjustment,
                    confirmation_completed: true,
                    intent_analysis: {
                      ...intentConfirmation?.intent_analysis,
                      ...eventData.intent_analysis
                    },
                    clarification_answers: eventData.clarification_answers || {}
                  }
                }
              : s
          );

          return updated;
        });
        break;
        
      // 新增：处理用户意图确认事件
      case 'user_intent_confirmation':
        console.log('收到用户意图确认事件:', eventData);

        // 保存意图确认结果到 mcpResults
        setMcpResults((prev: Record<string, any>) => ({
          ...prev,
          'intent_confirmation_request': eventData  // 使用固定的key
        }));

        // 更新意图确认步骤状态，添加交互数据确保统一格式
        setAnalysisSteps(prev => {
          return prev.map(s =>
            s.id === 'intent_confirmation_request' || s.tool_name === '意图确认'
              ? {
                  ...s,
                  status: 'finish',
                  name: eventData.has_user_modification ? t('analysis:intentConfirmation.completedAdjusted') : t('analysis:intentConfirmation.completed'),
                  hasResult: true,
                  resultKey: 'intent_confirmation_request',
                  tool_name: t('analysis:tools.intentConfirmation', { defaultValue: '意图确认' }),
                  // 添加交互数据，确保与回调渲染格式一致
                  interactionType: 'intent_confirmation',
                  interactionData: {
                    original_query: eventData.original_query || '',
                    user_adjustment: eventData.user_adjustment || '',
                    has_user_modification: eventData.has_user_modification || false,
                    confirmation_completed: true,
                    intent_analysis: eventData.intent_analysis || {},
                    clarification_answers: eventData.clarification_answers || {}
                  }
                }
              : s
          );
        });
        break;
        
      case 'step_started':
        // 处理步骤开始 - 这是关键的缺失事件！
        const stepId = eventData.step_id;
        
        // @fixed_implementation_start
        // 实现标识: 避免重复意图确认工具步骤
        // 功能描述: 跳过继续分析时的重复意图确认工具步骤
        // 修复内容: 检查是否是意图确认工具且已存在意图确认步骤，如果是则跳过
        // 状态: 已确认 (CONFIRMED_IMPLEMENTATION)
        // @fixed_implementation_end
        
        // 跳过继续分析时的重复意图确认工具步骤
        if (eventData.tool_name === '意图确认' && stepId.includes('intent_confirmation_')) {
          const hasIntentConfirmationStep = analysisSteps.some(s => 
            s.id === 'intent_confirmation' || s.tool_name === '意图确认'
          );
          if (hasIntentConfirmationStep) {
            console.log('跳过重复的意图确认工具步骤:', stepId);
            return;
          }
        }
        
        setCurrentStep(stepId);
        setAnalysisSteps((prev: any[]) => {
          const updated = prev.map(s => ({...s, status: 'finish'}));
          
          // 检查是否已存在相同步骤ID和工具名的步骤
          const existingStep = updated.find(s => s.id === stepId && s.tool_name === eventData.tool_name);
          if (existingStep) {
            console.log(`步骤 ${stepId} (${eventData.tool_name}) 已存在，更新状态为process`);
            return updated.map(s => 
              s.id === stepId && s.tool_name === eventData.tool_name 
                ? {...s, status: 'process'} 
                : s
            );
          }
          
          // 为了避免步骤ID冲突，在继续分析时为步骤ID添加时间戳后缀
          const uniqueStepId = analysisSteps.some(s => s.id === stepId) 
            ? `${stepId}_${Date.now()}` 
            : stepId;
          
          return [
            ...updated,
            {
              id: uniqueStepId,
              name: t('analysis:tools.executeTool', { defaultValue: '**执行工具:**' }) + ` ${eventData.tool_name}`,
              status: 'process',
              tool_name: eventData.tool_name,
              parameters: eventData.parameters,
              time: new Date().toISOString(),
              // 标记意图分析工具
              hasIntentData: eventData.tool_name === '意图分析',
              // 保存原始步骤ID用于后续匹配
              originalStepId: stepId
            }
          ];
        });
        break;
        
      case 'step_completed':
        const completedStepId = eventData.step_id;
        console.log('step_completed 事件:', {
          stepId: completedStepId,
          result: eventData.result,
          eventData: eventData
        });
        
        // @fixed_implementation_start
        // 实现标识: 避免重复处理意图确认工具
        // 功能描述: 跳过意图确认工具的step_completed事件，避免与融合后的意图确认步骤重复处理
        // 修复内容: 意图确认工具已通过user_intent_confirmation事件更新现有步骤，无需重复处理
        // 状态: 已确认 (CONFIRMED_IMPLEMENTATION)
        // @fixed_implementation_end
        
        // 跳过意图确认工具的step_completed事件，避免重复处理
        if (eventData.tool_name === '意图确认') {
          console.log('跳过意图确认工具的step_completed事件，已通过融合的意图确认步骤处理');
          break;
        }
        
        // 更新步骤状态 - 需要处理步骤ID可能不匹配的情况
        setAnalysisSteps((prev: any[]) => {
          return prev.map(step => {
            // 匹配原始步骤ID或当前步骤ID
            if (step.id === completedStepId || step.originalStepId === completedStepId) {
              return {
                ...step,
                status: 'finish',
                hasResult: true,
                resultKey: completedStepId,
                result: eventData.result,
                reasoning: eventData.reasoning
              };
            }
            return step;
          });
        });
        
        // 保存工具结果
        setToolResults((prev: any[]) => [
          ...prev,
          {
            stepId: completedStepId,
            toolName: eventData.tool_name,
            result: eventData.result,
            reasoning: eventData.reasoning,
            timestamp: new Date().toISOString()
          }
        ]);
        

        

        
        setMcpResults((prev: Record<string, any>) => {
          const newResults = {
            ...prev,
            [completedStepId]: eventData.result
          };
          console.log('更新 mcpResults:', newResults);
          return newResults;
        });
        
        setAnalysisSteps((prev: any[]) => {
          const updated = prev.map(step => {
            if (step.id === completedStepId) {
              const updatedStep = {
                ...step, 
                status: 'finish',
                hasResult: true,
                resultKey: completedStepId,
                // 检查是否是意图分析工具
                hasIntentData: eventData.tool_name === '意图分析' || step.hasIntentData
              };
              console.log('更新步骤:', updatedStep);
              return updatedStep;
            }
            return step;
          });
          console.log('更新后的 analysisSteps:', updated);
          return updated;
        });
        break;
        
      case 'plan_created':
        setCurrentStep('plan_created');
        

        
        setAnalysisSteps((prev: any[]) => {
          const updated = prev
            .filter(s => s.id !== 'waiting_for_planning')
            .map(s => ({...s, status: 'finish'}));
          
          const actionType = eventData.action_type;
          const reasoning = eventData.reasoning || '';
          const planningRounds = eventData.planning_rounds || 1;
          
          if (actionType === 'final_answer') {
            return [
              ...updated,
              {
                id: 'plan_created',
                name: `${reasoning}`,
                status: 'finish',
                time: new Date().toISOString(),
                planData: eventData
              },
              {
                id: 'generating_report',
                name: t('analysis:steps.generatingReport', { defaultValue: '正在生成分析报告' }),
                status: 'process',
                time: new Date().toISOString()
              }
            ];
          }
          
          return [
            ...updated,
            {
              id: 'plan_created',
              name: t('analysis:steps.planningRound', { round: planningRounds, reasoning, defaultValue: `**第${planningRounds}轮规划:** ${reasoning}` }),
              status: 'process',
              time: new Date().toISOString(),
              planData: eventData
            }
          ];
        });
        
        if (eventData.action_type === 'final_answer') {
          setCurrentStep('generating_report');
        }
        break;
        
      case 'chart_generated':
        console.log('收到图表生成事件:', eventData);
        
        try {
          // 添加调试信息，检查数据结构
          console.log('调试信息：');
          console.log('数据结构检查:');
          console.log('eventData?.result?.result?.result?.data:', eventData?.result?.result?.result?.data ? '✓ 存在' : '✗ 不存在');
          console.log('eventData?.result?.result?.data:', eventData?.result?.result?.data ? '✓ 存在' : '✗ 不存在');
          console.log('eventData?.result?.result?.result:', eventData?.result?.result?.result ? '✓ 存在' : '✗ 不存在');
          console.log('eventData?.result?.result:', eventData?.result?.result ? '✓ 存在' : '✗ 不存在');
          console.log('eventData?.result?.data:', eventData?.result?.data ? '✓ 存在' : '✗ 不存在');
          console.log('eventData?.chart_data:', eventData?.chart_data ? '✓ 存在' : '✗ 不存在');
          console.log('eventData?.data:', eventData?.data ? '✓ 存在' : '✗ 不存在');
          console.log('eventData?.charts:', eventData?.charts ? '✓ 存在' : '✗ 不存在');

          // 尝试多种可能的数据路径
          let chartData = null;
          let resultData = null;

          // 优先尝试标准路径
          if (eventData.chart_data && eventData.chart_data.data) {
            chartData = eventData.chart_data;
            resultData = eventData.chart_data.data;
            console.log('使用标准路径: eventData.chart_data');
          }
          // 尝试嵌套路径
          else if (eventData?.result?.result?.result) {
            resultData = eventData.result.result.result;
            chartData = { data: resultData };
            console.log('使用嵌套路径: eventData.result.result.result');
          }
          // 尝试其他可能的路径
          else if (eventData?.result?.result) {
            resultData = eventData.result.result;
            chartData = { data: resultData };
            console.log('使用路径: eventData.result.result');
          }
          else if (eventData?.result) {
            resultData = eventData.result;
            chartData = { data: resultData };
            console.log('使用路径: eventData.result');
          }

          console.log('resultData:', resultData ? '✓ 存在' : '✗ 不存在');

          // 检查是否为多图表
          const isMultiChart = resultData && (
            resultData.chart_type === 'multi_charts' ||
            resultData.display_format === 'multi_chart' ||
            (resultData.total_charts && resultData.total_charts > 1)
          ) && resultData.charts;

          console.log('isMultiChart:', isMultiChart ? '✓ 是' : '✗ 否');

          if (chartData && resultData) {
            // 创建图表数据对象，支持单图表和多图表
            const chartWithId = {
              ...chartData,
              chartId: chartData.chartId || `chart_${eventData.step_id}_${Date.now()}`,
              stepId: eventData.step_id,
              generatedAt: eventData.generatedAt || new Date().toISOString()
            };
            
            // 保存到图表历史列表
            setChartDataList(prev => [...prev, chartWithId]);

            // 检查是否为表格数据，表格数据不自动弹框（因为已经在页面中展示了）
            const isTableData = (
              resultData?.render_type === 'table' ||
              resultData?.display_format === 'table' ||
              resultData?.table_config ||
              (resultData?.content && resultData?.content?.columns && resultData?.content?.dataSource)
            );

            // 检查是否为多图表数据 - 支持新的MoE多图表格式
            // 注意：这里的isMultiChart已经在上面计算过了，使用resultData

            if (!isTableData) {
              // 只有非表格数据才自动弹出图表模态框
              setChartData(chartWithId);
              setChartModalVisible(true);
            } else {
              console.log('表格数据不自动弹框，已在页面中展示');
            }
            
            console.log('图表数据已保存:', chartWithId);
            message.success(
              isMultiChart
                ? t('analysis:multiChartsGenerated', { count: chartData.data?.charts?.length || 0 })
                : t('analysis:chartGenerated')
            );
          } else {
            console.warn('图表数据为空或格式不正确:', eventData);
            message.warning(t('analysis:errors.chartDataFormatAbnormal'));
          }
        } catch (error) {
          console.error('处理图表生成事件失败:', error);
          message.error(t('analysis:errors.processChartDataFailed'));
        }
        break;

      case 'multi_chart_generation_completed':
        console.log('收到多图表生成完成事件:', eventData);

        try {
          const chartData = eventData.chart_data;
          if (chartData && chartData.data) {
            // 创建多图表数据对象
            const multiChartWithId = {
              ...chartData,
              chartId: chartData.chartId || `multi_chart_${eventData.step_id}_${Date.now()}`,
              stepId: eventData.step_id,
              generatedAt: eventData.generatedAt || new Date().toISOString()
            };

            // 保存到图表历史列表
            setChartDataList(prev => [...prev, multiChartWithId]);

            // 自动弹出多图表模态框
            setChartData(multiChartWithId);
            setChartModalVisible(true);

            console.log('多图表数据已保存:', multiChartWithId);
            message.success(
              t('analysis:multiChartsGenerated', {
                count: chartData.data?.charts?.length || chartData.data?.total_charts || 0
              })
            );
          } else {
            console.warn('多图表数据为空或格式不正确:', eventData);
            message.warning(t('analysis:errors.chartDataFormatAbnormal'));
          }
        } catch (error) {
          console.error('处理多图表生成事件失败:', error);
          message.error(t('analysis:errors.processChartDataFailed'));
        }
        break;

      // 🚀 新增：图表生成进度事件处理（仅MoE Agent）
      case 'chart_generation_started':
        console.log('📊 图表生成开始:', eventData);
        
        // 只显示MoE Agent的进度
        if (eventData.generation_method === 'moe_agent' || !eventData.generation_method) {
          // 更新步骤状态，显示正在生成的图表总数
          setAnalysisSteps((prev: any[]) => {
            return prev.map(step => {
              if (step.id === eventData.step_id || step.originalStepId === eventData.step_id) {
                return {
                  ...step,
                  status: 'process',
                  progressInfo: {
                    type: 'chart_generation',
                    total: eventData.total_charts,
                    current: 0,
                    message: `开始并行生成 ${eventData.total_charts} 个图表...`
                  }
                };
              }
              return step;
            });
          });
          
          message.info(`开始生成 ${eventData.total_charts} 个图表，请稍候...`);
        }
        break;

      case 'chart_generation_progress':
        console.log('📈 图表生成进度:', eventData);
        
        // 只显示MoE Agent的进度
        if (eventData.generation_method === 'moe_agent' || !eventData.generation_method) {
          // 更新步骤进度信息
          setAnalysisSteps((prev: any[]) => {
            return prev.map(step => {
              if (step.id === eventData.step_id || step.originalStepId === eventData.step_id) {
                return {
                  ...step,
                  status: 'process',
                  progressInfo: {
                    type: 'chart_generation',
                    total: eventData.total,
                    current: eventData.current,
                    message: `正在生成第 ${eventData.current}/${eventData.total} 个图表: ${eventData.chart_id}`
                  }
                };
              }
              return step;
            });
          });
        }
        break;

      case 'chart_generation_success':
        console.log('✅ 单个图表生成成功:', eventData);
        
        // 只显示MoE Agent的进度
        if (eventData.generation_method === 'moe_agent' || !eventData.generation_method) {
          // 更新步骤进度信息
          setAnalysisSteps((prev: any[]) => {
            return prev.map(step => {
              if (step.id === eventData.step_id || step.originalStepId === eventData.step_id) {
                return {
                  ...step,
                  status: 'process',
                  progressInfo: {
                    type: 'chart_generation',
                    total: eventData.total,
                    current: eventData.current,
                    message: `第 ${eventData.current}/${eventData.total} 个图表生成完成: ${eventData.chart_title}`
                  }
                };
              }
              return step;
            });
          });
        }
        break;

      case 'chart_generation_error':
        console.log('❌ 单个图表生成失败:', eventData);
        
        // 只显示MoE Agent的进度
        if (eventData.generation_method === 'moe_agent' || !eventData.generation_method) {
          // 更新步骤进度信息，显示错误
          setAnalysisSteps((prev: any[]) => {
            return prev.map(step => {
              if (step.id === eventData.step_id || step.originalStepId === eventData.step_id) {
                return {
                  ...step,
                  status: 'process',
                  progressInfo: {
                    type: 'chart_generation',
                    total: eventData.total,
                    current: eventData.current,
                    message: `第 ${eventData.current}/${eventData.total} 个图表生成失败: ${eventData.error}`,
                    hasError: true
                  }
                };
              }
              return step;
            });
          });
          
          message.warning(`图表 ${eventData.chart_id} 生成失败: ${eventData.error}`);
        }
        break;

      case 'chart_generation_completed':
        console.log('🎉 图表生成全部完成:', eventData);
        
        // 只显示MoE Agent的进度
        if (eventData.generation_method === 'moe_agent' || !eventData.generation_method) {
          // 更新步骤状态为完成
          setAnalysisSteps((prev: any[]) => {
            return prev.map(step => {
              if (step.id === eventData.step_id || step.originalStepId === eventData.step_id) {
                return {
                  ...step,
                  status: 'process', // 保持process状态，等待最终的multi_chart_generation_completed事件
                  progressInfo: {
                    type: 'chart_generation',
                    total: eventData.total_charts,
                    current: eventData.successful_count,
                    message: `图表生成完成: 成功 ${eventData.successful_count}/${eventData.total_charts} 个`,
                    completed: true
                  }
                };
              }
              return step;
            });
          });
          
          if (eventData.failed_count > 0) {
            message.warning(`图表生成完成: 成功 ${eventData.successful_count} 个，失败 ${eventData.failed_count} 个`);
          } else {
            message.success(`所有 ${eventData.successful_count} 个图表生成成功！`);
          }
        }
        break;

      case 'chart_generation_failed':
        console.log('💥 图表生成全部失败:', eventData);
        
        // 只显示MoE Agent的进度
        if (eventData.generation_method === 'moe_agent' || !eventData.generation_method) {
          // 更新步骤状态为失败
          setAnalysisSteps((prev: any[]) => {
            return prev.map(step => {
              if (step.id === eventData.step_id || step.originalStepId === eventData.step_id) {
                return {
                  ...step,
                  status: 'error',
                  progressInfo: {
                    type: 'chart_generation',
                    message: '所有图表生成都失败了',
                    hasError: true
                  }
                };
              }
              return step;
            });
          });
          
          message.error('所有图表生成都失败了');
        }
        break;

      case 'report_generated':
        const reportData = eventData.report || '未能生成报告';
        setFinalReport(reportData);
        setCurrentStep('report_generated');
        
        setAnalysisSteps((prev: any[]) => {
          const updatedSteps = [
            ...prev.map(s => ({...s, status: 'finish'})),
            {
              id: 'report_generated',
              name: t('analysis:steps.reportGenerated', { defaultValue: '生成分析报告' }),
              status: 'finish',
              time: new Date().toISOString(),
              hasReport: true
            }
          ];
          
          return updatedSteps;
        });
        
        // 报告生成后滚动到底部
        scrollToBottom();
        break;

      case 'insight_discovered':
        console.log('🔍 发现分析洞察:', eventData);
        setCurrentStep('insight_discovered');

        // 🔧 修复：构建洞察摘要（多语言支持）
        const insightSummary = [];
        if (eventData.insights && eventData.insights.length > 0) {
          insightSummary.push(t('analysis:insights.summary.foundInsights', {
            count: eventData.insights.length,
            defaultValue: `发现 ${eventData.insights.length} 个关键洞察`
          }));
        }
        if (eventData.patterns && eventData.patterns.length > 0) {
          insightSummary.push(t('analysis:insights.summary.dataPatterns', {
            count: eventData.patterns.length,
            defaultValue: `${eventData.patterns.length} 个数据模式`
          }));
        }
        if (eventData.anomalies && eventData.anomalies.length > 0) {
          insightSummary.push(t('analysis:insights.summary.anomalies', {
            count: eventData.anomalies.length,
            defaultValue: `${eventData.anomalies.length} 个异常`
          }));
        }

        const summaryText = insightSummary.length > 0
          ? insightSummary.join(t('analysis:insights.summary.separator', { defaultValue: '，' }))
          : t('analysis:insights.summary.defaultDescription', { defaultValue: '发现数据洞察' });

        const insightStepId = `insight_${eventData.step_id || Date.now()}`;

        setAnalysisSteps((prev: any[]) => [
          ...prev.map(s => ({...s, status: 'finish'})),
          {
            id: insightStepId,
            name: t('analysis:steps.insightDiscovered', {
              toolName: eventData.tool_name || '分析工具',
              defaultValue: `${eventData.tool_name || '分析工具'} - 发现洞察`
            }),
            status: 'finish',
            time: new Date().toISOString(),
            description: summaryText,
            hasInsights: true,
            hasResult: true,
            resultKey: insightStepId,
            insightData: {
              insights: eventData.insights || [],
              patterns: eventData.patterns || [],
              anomalies: eventData.anomalies || [],
              correlations: eventData.correlations || [],
              statistical_summary: eventData.statistical_summary || null,
              data_quality: eventData.data_quality || null
            }
          }
        ]);

        // 🔧 修复：将洞察数据保存到mcpResults中，确保会话还原时能正确显示
        // 注意：洞察数据只在mcpResults中保存，不在renderStepResult中重复渲染
        setMcpResults((prev: Record<string, any>) => ({
          ...prev,
          [insightStepId]: {
            insights: eventData.insights || [],
            patterns: eventData.patterns || [],
            anomalies: eventData.anomalies || [],
            correlations: eventData.correlations || [],
            statistical_summary: eventData.statistical_summary || null,
            data_quality: eventData.data_quality || null,
            timestamp: eventData.timestamp || new Date().toISOString(),
            // 标记这是洞察数据，用于调试
            _type: 'insight_data'
          }
        }));

        // 显示洞察通知
        if (eventData.insights && eventData.insights.length > 0) {
          const firstInsight = eventData.insights[0];
          message.success(
            t('analysis:insightDiscovered', {
              title: firstInsight.title || '数据洞察',
              defaultValue: `发现洞察: ${firstInsight.title || '数据洞察'}`
            })
          );
        }
        break;

      // 打断相关事件处理
      case 'waiting_feedback':
        console.log('🔔 等待用户反馈:', eventData);
        setWaitingForFeedback(true);
        message.info(t('analysis:errors.waitingForFeedbackWithTime', { minutes: eventData.remaining_minutes?.toFixed(1) }));
        // 注意：waiting_feedback事件只设置UI状态，不创建打断记录
        // 打断记录完全由服务端事件（feedback_applied/interrupt_timeout）创建
        break;

      case 'feedback_applied':
        console.log('🔔 用户反馈已应用:', eventData);
        setWaitingForFeedback(false);
        message.success(t('analysis:errors.feedbackApplied'));
        
        // 直接添加到分析步骤中展示
        if (eventData.interrupt_id) {
          console.log('🔍 收到feedback_applied事件，直接添加到时间线:', eventData);
          
          setAnalysisSteps((prev: any[]) => [
            ...prev,
            {
              id: `interrupt_${eventData.interrupt_id}`,
              name: t('analysis:interrupt.feedbackApplied', { defaultValue: '用户反馈已应用' }),
              status: 'finish',
              time: new Date().toISOString(),
              isInterrupt: true,
              interruptData: {
                interrupt_id: eventData.interrupt_id,
                status: 'adjusted',
                user_feedback: eventData.feedback || '',
                adjustment_type: eventData.adjustment_type || '',
                adjustment_summary: eventData.adjustment_summary || '',
                interrupt_timestamp: eventData.interrupt_timestamp || new Date().toISOString()
              }
            }
          ]);
          
          console.log('✅ 打断反馈已直接添加到时间线');
        }
        break;

      case 'interrupt_timeout':
        console.log('🔔 打断超时:', eventData);
        setWaitingForFeedback(false);
        message.warning(t('analysis:errors.feedbackTimeout'));
        
        // 直接添加到分析步骤中展示
        if (eventData.interrupt_id) {
          console.log('🔍 收到interrupt_timeout事件，直接添加到时间线:', eventData);
          
          setAnalysisSteps((prev: any[]) => [
            ...prev,
            {
              id: `interrupt_timeout_${eventData.interrupt_id}`,
              name: t('analysis:interrupt.feedbackTimeout', { defaultValue: '用户反馈超时' }),
              status: 'finish',
              time: new Date().toISOString(),
              isInterrupt: true,
              interruptData: {
                interrupt_id: eventData.interrupt_id,
                status: 'timeout',
                user_feedback: '',
                adjustment_summary: t('analysis:interrupt.feedbackTimeout', { defaultValue: '用户反馈超时' }),
                interrupt_timestamp: eventData.interrupt_timestamp || new Date().toISOString()
              }
            }
          ]);
          
          console.log('✅ 打断超时已直接添加到时间线');
        }
        break;

      case 'interrupt_cancelled':
        console.log('🔔 用户取消反馈:', eventData);
        setWaitingForFeedback(false);
        
        // 静默处理，不显示消息，不添加到时间轴
        // 关闭反馈弹窗
        setFeedbackModalVisible(false);
        setCurrentInterruptId(null);
        setIsInterrupting(false);
        break;
        
      case 'completed':
        setCurrentStep('completed');
        setAnalysisSteps((prev: any[]) => {
          const updatedSteps = [
            ...prev.map(s => ({...s, status: 'finish'})),
            {
              id: 'completed',
              name: t('analysis:steps.completed', { defaultValue: '分析完成' }),
              status: 'finish',
              time: new Date().toISOString()
            }
          ];
          
          return updatedSteps;
        });
        setStreamComplete(true);

        if (eventData.cancelled && eventData.reason === 'interrupt_timeout') {
          console.log('✅ 条件匹配：分析因打断超时被取消，关闭打断弹窗');
          setWaitingForFeedback(false);
          setFeedbackModalVisible(false);
          setCurrentInterruptId(null);
          setIsInterrupting(false);
          // 重置反馈表单
          setInterruptFeedback({
            adjustmentType: 'strategy',
            feedback: '',
            resetPlanning: false
          });
          // 显示超时取消消息
          message.warning(eventData.message || t('analysis:errors.analysisTimeoutCancelled', {
            defaultValue: '分析因打断超时而取消'
          }));
          console.log('✅ 打断弹窗状态已重置');
        } else if (eventData.cancelled) {
          // 其他取消原因也关闭打断弹窗
          console.log('✅ 条件匹配：分析被取消（其他原因），关闭打断弹窗');
          setWaitingForFeedback(false);
          setFeedbackModalVisible(false);
          setCurrentInterruptId(null);
          setIsInterrupting(false);
          setInterruptFeedback({
            adjustmentType: 'strategy',
            feedback: '',
            resetPlanning: false
          });
          console.log('✅ 打断弹窗状态已重置（其他取消原因）');
        } else {
          console.log('❌ 条件不匹配，不关闭打断弹窗');
        }

        // 分析完成后滚动到底部
        scrollToBottom();
        break;
        

        
      case 'error':
        // 根据错误类型和严重程度进行分级处理
        const errorMessage = eventData.message || t('analysis:errors.unknownError', { defaultValue: '未知错误' });
        const errorType = eventData.error_type || 'unknown';
        const severity = eventData.severity || 'medium';
        const errorStepId = eventData.step_id;
        const isFatal = eventData.fatal === true || severity === 'fatal';
        
        console.warn('收到错误事件:', { errorType, severity, errorMessage, errorStepId, isFatal });
        
        if (isFatal) {
          // 致命错误：停止整个分析流程
          setError(errorMessage);
          setStreaming(false);
          updateCurrentRoundStatus('failed');
          
          message.error(t('analysis:errors.analysisFailed', { error: errorMessage, defaultValue: `分析失败: ${errorMessage}` }));
        } else {
          // 非致命错误：记录错误但继续分析
          console.warn('步骤执行出错但分析继续:', errorMessage);
          
          // 更新对应步骤的状态为错误
          if (errorStepId) {
            setAnalysisSteps((prev: any[]) => 
              prev.map(step => 
                step.id === errorStepId 
                  ? { ...step, status: 'error', errorMessage, name: `${step.name} (${t('analysis:errors.error', { defaultValue: '出错' })})` }
                  : step
              )
            );
          }
          
          // 记录在timeline中，不弹框显示
          console.log(`步骤执行警告: ${errorMessage}`);
        }
        break;
        
      case 'step_error':
        console.log('步骤执行出错:', eventData);
        const stepErrorMessage = eventData.error_message || eventData.message || t('analysis:errors.stepExecutionFailed', { defaultValue: '步骤执行失败' });
        const stepErrorId = eventData.step_id || `error_${Date.now()}`;
        const canContinue = eventData.can_continue !== false; // 默认可以继续，除非明确设置为false
        
        // 构建错误结果，格式与正常工具执行结果一致（不包含敏感信息）
        const errorResult = {
          success: false,
          error: stepErrorMessage,
          error_type: eventData.error_type || 'Exception',
          severity: eventData.severity || 'medium',
          can_continue: canContinue,
          data: null,
          execution_time: null
        };
        
        setCurrentStep('step_error');
        setAnalysisSteps((prev: any[]) => {
          return [
            ...prev.map(s => ({...s, status: 'finish'})),
            {
              id: stepErrorId,
              name: eventData.step_name || t('analysis:errors.stepExecutionError', { defaultValue: '执行步骤出错' }),
              status: 'error',
              time: new Date().toISOString(),
              description: stepErrorMessage,
              errorMessage: stepErrorMessage,
              canContinue: canContinue,
              tool_name: eventData.tool_name,
              hasResult: true,  // 标记有结果（错误结果）
              resultKey: stepErrorId,  // 使用step_id作为结果键
              // 保存原始工具执行参数（如SQL语句）
              parameters: eventData.parameters || {}
            }
          ];
        });
        
        // 将错误结果保存到mcpResults中，就像正常的工具执行结果一样
        setMcpResults((prev: Record<string, any>) => ({
          ...prev,
          [stepErrorId]: errorResult
        }));
        
        if (canContinue) {
          // 步骤级错误，记录在timeline中但继续分析（不弹框）
          console.log(`步骤执行失败，但分析将继续: ${stepErrorMessage}`);
        } else {
          // 不可恢复的步骤错误，停止分析
          setError(t('analysis:errors.stepExecutionFailed', { defaultValue: '步骤执行失败' }) + `: ${stepErrorMessage}`);
          setStreaming(false);
          updateCurrentRoundStatus('failed');
          message.error(t('analysis:errors.analysisStopped', { error: stepErrorMessage, defaultValue: `分析停止: ${stepErrorMessage}` }));
        }
        break;
        
      case 'evaluation_completed':
        // 处理评估完成事件
        console.log('收到评估完成事件:', eventData);
        
        // 使用后端提供的独立评估step_id，无需前端处理顺序
        const evaluationStepId = eventData.step_id;
        
        // 添加评估步骤到时间线
        setAnalysisSteps((prev: any[]) => {
          // 检查是否已存在相同的评估步骤
          const existingEvaluation = prev.find(s => s.id === evaluationStepId);
          if (existingEvaluation) {
            return prev;
          }
          
          // 后端已经通过独立的evaluation_step_id确保正确顺序，前端只需正常添加
          return [
            ...prev,
            {
              id: evaluationStepId,
              name: t('analysis:steps.intelligentEvaluation', { reasoning: eventData.reasoning, defaultValue: `**智能评估:** ${eventData.reasoning}` }),
              status: 'finish',
              time: eventData.timestamp || new Date().toISOString(),
              tool_name: t('analysis:tools.intelligentEvaluation', { defaultValue: '智能评估' }),
              hasResult: false // 不需要复杂结果渲染
            }
          ];
        });
        break;
    }
  };

  // 辅助渲染函数 - 从Analysis.tsx复制
  // 处理资金流转分析结果
  const renderFundTransferResult = (result: any): React.ReactNode => {
    try {
      const resultData = result.result.data;
      
      if (!resultData || !resultData.data || !Array.isArray(resultData.data) || resultData.data.length === 0) {
        return <Empty description="资金流转数据为空" />;
      }
      
      // 获取列名和数据
      const columns = resultData.columns || [];
      const tableData = resultData.data;
      
      // 计算最大交易金额，用于边的宽度缩放
      let maxAmount = 0;
      tableData.forEach((row: any[]) => {
        const amount = row[6]; // 总金额列索引
        if (typeof amount === 'number' && amount > maxAmount) {
          maxAmount = amount;
        }
      });
      
      // 提取所有公司名称，构建节点
      const companySet = new Set<string>();
      tableData.forEach((row: any[]) => {
        companySet.add(row[1]); // 源公司
        companySet.add(row[3]); // 目标公司
      });
      
      const nodes = Array.from(companySet).map((company, index) => ({
        id: company,
        name: company,
        symbolSize: 30,
        itemStyle: {
          color: '#5470c6'
        },
        label: {
          show: true,
          position: 'right',
          formatter: '{b}'
        }
      }));
      
      // 构建边
      const links = tableData.map((row: any[]) => {
        // 获取风险等级
        const riskLevel = row[7]; // 风险等级列索引
        let lineColor = '#91cc75'; // 默认绿色，低风险
        
        if (riskLevel === '高风险') {
          lineColor = '#ee6666'; // 红色
        } else if (riskLevel === '中风险') {
          lineColor = '#fac858'; // 黄色
        }
        
        // 计算边的宽度，基于交易金额比例
        const amount = row[6]; // 总金额列索引
        const lineWidth = 1 + (amount / maxAmount) * 5; // 宽度范围：1-6
        
        return {
          source: row[1], // 源公司
          target: row[3], // 目标公司
          value: row[6], // 总金额
          lineStyle: {
            width: lineWidth,
            color: lineColor,
            curveness: 0.3 // 添加一点曲率，避免重叠
          },
          label: {
            show: true,
            formatter: `{c} 元`
          }
        };
      });
      
      // 构建图表配置
      const graphOption = {
        title: {
          text: '资金流转关系图',
          subtext: `共 ${nodes.length} 家公司，${links.length} 条资金流向`,
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          confine: true
        },
        legend: {
          data: ['公司', '资金流向'],
          orient: 'vertical',
          right: 10,
          top: 'center'
        },
        series: [
          {
            name: '资金流转',
            type: 'graph',
            layout: 'force',
            data: nodes,
            links: links,
            roam: true,
            draggable: true,
            edgeSymbol: ['none', 'arrow'],
            edgeSymbolSize: [0, 10],
            label: {
              position: 'right'
            },
            force: {
              repulsion: 100,
              gravity: 0.1,
              edgeLength: 150
            },
            lineStyle: {
              opacity: 0.8,
              color: '#91cc75',
              width: 2,
              type: 'solid',
              curveness: 0.2
            },
            emphasis: {
              focus: 'adjacency',
              lineStyle: {
                width: 5
              }
            }
          }
        ]
      };
    
    return (
        <div>
          <Tabs defaultActiveKey="graph">
            <Tabs.TabPane tab="关系图" key="graph">
              <div style={{ height: 500, marginBottom: 16 }}>
                <ReactECharts 
                  option={reviveEchartsFunctions(graphOption)} 
                  style={{ height: '100%', width: '100%' }} 
                  opts={{ renderer: 'canvas' }}
                />
              </div>
            </Tabs.TabPane>
            <Tabs.TabPane tab="数据表格" key="table">
              <div>
                <Table 
                  columns={resultData.columns.map((col: string, index: number) => ({
                    title: col,
                    dataIndex: index.toString(),
                    key: index.toString(),
                    ellipsis: { showTitle: false },
                    render: (text: any) => {
                      if (col === '风险等级') {
                        let color = 'green';
                        if (text === '高风险') color = 'red';
                        else if (text === '中风险') color = 'orange';
                        
                        return <Tag color={color}>{text}</Tag>;
                      }
                      
                      return (
                        <Typography.Paragraph 
                          ellipsis={{ rows: 3, expandable: true, symbol: t('analysis:errors.expandMore') }}
                          style={{ marginBottom: 0 }}
                        >
                          {text === null || text === undefined ? '' : String(text)}
                        </Typography.Paragraph>
                      );
                    }
                  }))} 
                  dataSource={resultData.data.map((row: any[], rowIndex: number) => {
                    const rowData: any = { key: rowIndex };
                    row.forEach((cell, cellIndex) => {
                      rowData[cellIndex.toString()] = cell;
                    });
                    return rowData;
                  })}
                  size="small"
                  pagination={{ 
                    pageSize: 10, 
                    showSizeChanger: true, 
                    showTotal: (total: number) => t('analysis:tools.totalRecords', { total }),
                    pageSizeOptions: ['10', '20', '50', '100']
                  }}
                  bordered
                />
              </div>
            </Tabs.TabPane>
          </Tabs>
        </div>
      );
    } catch (error) {
      console.error('渲染资金流转分析时出错：', error);
      return (
        <>
          <Alert
            message="渲染资金流转图表时出错"
            description={String(error)}
            type="error"
            showIcon
            style={{ marginBottom: 16 }}
          />
          <div>
            {result.result?.data && renderDataTable(result.result.data)}
          </div>
        </>
      );
    }
  };

  // 处理公司基本信息查询结果
  const renderCompanyInfoResult = (result: any): React.ReactNode => {
    try {
      const resultData = result.result.data;
      const execTimeRaw = result.result.execution_time;
      const execTime = execTimeRaw != null ? (typeof execTimeRaw === 'string' ? parseFloat(execTimeRaw) : execTimeRaw) : null;
      
      // 检测是否有JSON_DOC字段的嵌套JSON格式
      if (resultData?.data && Array.isArray(resultData.data) && resultData.data.length > 0) {
        const firstRow = resultData.data[0];
        if (Array.isArray(firstRow) && firstRow.length > 0) {
          const possibleJson = firstRow[0];
          
          if (typeof possibleJson === 'string' && (possibleJson.startsWith('[') || possibleJson.startsWith('{'))) {
            try {
              const parsedData = JSON.parse(possibleJson);
              
              if (Array.isArray(parsedData)) {
                return (
                  <>
                    <div style={{ margin: '8px 0', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span style={{ fontWeight: 'bold' }}>公司基本信息</span>
                      <Text type="secondary">执行时间: {execTime != null ? execTime.toFixed(3) : '未知'} ms</Text>
                    </div>
                    {parsedData.map((company, index) => {
                      const relatedFiles = company["相关文件"] || [];
                      const basicCompanyInfo = { ...company };
                      delete basicCompanyInfo["相关文件"];
                      
                      return (
                      <div key={index} style={{ marginBottom: 16 }}>
                        <Descriptions 
                          title={company["公司名称"] || "公司详情"} 
                          bordered 
                          column={{ xxl: 4, xl: 3, lg: 3, md: 2, sm: 2, xs: 1 }}
                          size="small"
                        >
                            {Object.entries(basicCompanyInfo).map(([key, value]) => (
                            <Descriptions.Item 
                              key={key} 
                              label={<strong>{key}</strong>}
                              labelStyle={{ fontWeight: 'bold' }}
                            >
                              {value !== null && value !== undefined ? String(value) : '-'}
                            </Descriptions.Item>
                          ))}
                        </Descriptions>
                        {index < parsedData.length - 1 && <Divider />}
                      </div>
                      );
                    })}
                    <div style={{ textAlign: 'right', color: '#999', fontSize: '12px' }}>
                      {t('analysis:tools.recordCount', { count: parsedData.length })}
                    </div>
                  </>
                );
              }
            } catch (e) {
              console.error('解析JSON_DOC失败：', e);
            }
          }
        }
      }
      
      return (
        <>
          <Text type="secondary">执行时间: {execTime != null ? execTime.toFixed(3) : '未知'} ms</Text>
          <div style={{ marginTop: 8 }}>
            {renderDataTable(resultData)}
          </div>
        </>
      );
    } catch (error) {
      console.error('渲染公司基本信息时出错：', error);
      return (
        <>
          <Alert
            message="渲染公司信息时出错"
            description={String(error)}
            type="error"
            showIcon
            style={{ marginBottom: 16 }}
          />
          <div>
            <pre style={{ whiteSpace: 'pre-wrap' }}>
              {JSON.stringify(result.result.data, null, 2)}
            </pre>
          </div>
        </>
      );
    }
  };

  // 使用Ant Design Table渲染标准表格数据
  const renderDataTable = (data: any): React.ReactNode => {
    if (!data || !data.columns || !data.data) return null;
    
    const columns = data.columns.map((col: string, index: number) => ({
      title: col,
      dataIndex: index.toString(),
      key: index.toString(),
      ellipsis: { showTitle: false },
      render: (text: any) => (
        <Typography.Paragraph 
          ellipsis={{ rows: 3, expandable: true, symbol: '更多' }}
          style={{ marginBottom: 0 }}
        >
          {text === null || text === undefined ? '' : text}
        </Typography.Paragraph>
      )
    }));
    
    const tableData = data.data.map((row: any[], rowIndex: number) => {
      const rowData: any = { key: rowIndex };
      row.forEach((cell, cellIndex) => {
        rowData[cellIndex.toString()] = cell;
      });
      return rowData;
    });
    
    const paginationConfig = tableData.length > 10 ? {
      pageSize: 10,
      showSizeChanger: true,
      showTotal: (total: number) => t('analysis:tools.totalRecords', { total }),
      pageSizeOptions: ['10', '20', '50', '100'],
    } : false;
    
    return (
      <>
        <Table 
          columns={columns} 
          dataSource={tableData} 
          size="small" 
          pagination={paginationConfig}
          bordered
        />
        {!paginationConfig && (
          <div style={{ marginTop: 8, textAlign: 'right' }}>
            <Text type="secondary">{t('analysis:tools.recordCount', { count: data.count || tableData.length })}</Text>
          </div>
        )}
      </>
    );
  };

  // 渲染SQL查询结果为表格
  const renderSQLResultTable = (data: any): React.ReactNode => {
    try {
              // 检查是否为错误结果
        if (data && (data.success === false || data.error)) {
          const errorMessage = data.error || t('analysis:errors.queryExecutionFailed');
          const errorType = data.error_type || 'DatabaseError';

        return (
          <>
            <Alert
              message={t('analysis:errors.sqlExecutionFailed')}
              description={
                <div>
                  <div>{errorMessage}</div>
                  {errorType !== 'DatabaseError' && (
                    <div style={{ marginTop: 4, fontSize: '12px', color: '#666' }}>
                      {t('analysis:errors.errorType', { type: errorType })}
                    </div>
                  )}
                </div>
              }
              type="error"
              showIcon
              style={{ marginBottom: 12 }}
            />
            

          </>
        );
      }
      
      if (data && typeof data === 'object' && Array.isArray(data.results)) {
        const cols = (data.columns || (data.results.length > 0 ? Object.keys(data.results[0]) : [])).map((col: string) => ({
          title: col,
          dataIndex: col,
          key: col,
          ellipsis: { showTitle: false },
          render: (text: any) => (
            <Typography.Paragraph ellipsis={{ rows: 3, expandable: true, symbol: t('analysis:errors.expandMore') }} style={{ marginBottom: 0 }}>
              {text === null || text === undefined ? '' : String(text)}
            </Typography.Paragraph>
          )
        }));
        const tableData = data.results.map((row: any, index: number) => ({ ...row, key: index }));
        
        const paginationConfig = tableData.length > 10 ? {
          pageSize: 10,
          showSizeChanger: true,
          showTotal: (total: number) => t('analysis:tools.totalRecords', { total }),
          pageSizeOptions: ['10', '20', '50', '100'],
        } : false;
        
        return (
          <>
            <Table 
              columns={cols} 
              dataSource={tableData} 
              size="small" 
              pagination={paginationConfig} 
              bordered 
            />
            {!paginationConfig && (
              <div style={{ marginTop: 8, textAlign: 'right' }}>
                <Text type="secondary">{t('analysis:tools.recordCount', { count: tableData.length })}</Text>
              </div>
            )}
          </>
        );
      }
       
       if (data && typeof data === 'object' && !Array.isArray(data)) {
         if (data.data && Array.isArray(data.data)) {
           return renderDataTable(data);
         }
         
         return (
           <div style={{ 
             whiteSpace: 'pre-wrap', 
             fontFamily: 'monospace', 
             background: '#f5f5f5', 
             padding: 8, 
             borderRadius: 4, 
             maxHeight: '300px', 
             overflow: 'auto'
           }}>
             {JSON.stringify(data, null, 2)}
           </div>
         );
       }
       
       if (Array.isArray(data)) {
         if (data.length > 0) {
           const firstRow = data[0];
           const columns = Object.keys(firstRow).map(col => ({
             title: col,
             dataIndex: col,
             key: col,
             ellipsis: { showTitle: false },
             render: (text: any) => (
               <Typography.Paragraph 
                 ellipsis={{ rows: 3, expandable: true, symbol: t('analysis:errors.expandMore') }}
                 style={{ marginBottom: 0 }}
               >
                 {text === null || text === undefined ? '' : String(text)}
               </Typography.Paragraph>
             )
           }));
           
           const tableData = data.map((row: any, index: number) => ({
             ...row,
             key: index
           }));
           
           const paginationConfig = tableData.length > 10 ? {
             pageSize: 10,
             showSizeChanger: true,
             showTotal: (total: number) => t('analysis:tools.totalRecords', { total }),
             pageSizeOptions: ['10', '20', '50', '100'],
           } : false;
           
           return (
             <>
               <Table 
                 columns={columns} 
                 dataSource={tableData}
                 size="small"
                 pagination={paginationConfig}
                 bordered
               />
               {!paginationConfig && (
                 <div style={{ marginTop: 8, textAlign: 'right' }}>
                   <Text type="secondary">{t('analysis:tools.recordCount', { count: data.length })}</Text>
                 </div>
               )}
             </>
           );
         } else {
           return <Empty description="查询结果为空" />;
         }
       }
       
       if (typeof data === 'string') {
         try {
           if (data.trim().startsWith('[') || data.trim().startsWith('{')) {
             const parsedData = JSON.parse(data);
             return renderSQLResultTable(parsedData);
           }
         } catch (e) {
           console.log(t('analysis:errors.jsonParseFailed'), e);
         }
         
         return (
           <div style={{ 
             whiteSpace: 'pre-wrap', 
             fontFamily: 'monospace', 
             background: '#f5f5f5', 
             padding: 8, 
             borderRadius: 4 
           }}>
             {data}
           </div>
         );
       }
       
       if (data === null || data === undefined) {
         return <Empty description={t('analysis:errors.queryResultEmpty')} />;
       }
       
       return (
         <div style={{ 
           whiteSpace: 'pre-wrap', 
           fontFamily: 'monospace', 
           background: '#f5f5f5', 
           padding: 8, 
           borderRadius: 4 
         }}>
           {JSON.stringify(data, null, 2)}
         </div>
       );
     } catch (error) {
       console.error('渲染SQL结果表格时出错：', error);
       return (
         <>
           <Alert
             message={t('analysis:errors.renderSQLResultError')}
             description={String(error)}
             type="error"
             showIcon
             style={{ marginBottom: 8 }}
           />
           <div style={{ 
             whiteSpace: 'pre-wrap', 
             fontFamily: 'monospace', 
             background: '#f5f5f5', 
             padding: 8, 
             borderRadius: 4 
           }}>
             {typeof data === 'string' ? data : JSON.stringify(data, null, 2)}
           </div>
         </>
       );
     }
   };

  // 判断数据是否符合表格格式
  const isTableData = (data: any): boolean => {
    if (!data) return false;
    
    try {
      return (
        data.columns && Array.isArray(data.columns) && data.columns.length > 0 &&
        data.data && Array.isArray(data.data) &&
        typeof data.count !== 'undefined'
      );
    } catch (error) {
      console.error('判断表格数据时出错：', error);
      return false;
    }
  };

  // 处理图表生成结果
  const renderChartResult = (result: any): React.ReactNode => {
    try {
      // @fixed_implementation_start
      // 实现标识: 图表数据结构适配修复
      // 功能描述: 修复图表数据结构检查逻辑，支持多种数据源格式
      // 修复内容: 智能检测图表数据位置，支持result.result、result.data、result等多种格式
      // 状态: 已确认 (CONFIRMED_IMPLEMENTATION)
      // @fixed_implementation_end
      
      // 智能检测图表数据位置
      let resultData: any = null;
      
      // 尝试多种数据结构路径
      if (result?.result?.result?.data) {
        resultData = result.result.result.data;
        console.log('使用 result.result.result.data 路径:', resultData);
      } else if (result?.result?.data) {
        resultData = result.result.data;
        console.log('使用 result.result.data 路径:', resultData);
      } else if (result?.result?.result) {
        resultData = result.result.result;
        console.log('使用 result.result.result 路径:', resultData);
      } else if (result?.result) {
        resultData = result.result;
        console.log('使用 result.result 路径:', resultData);
      } else if (result?.chart_data?.data) {
        resultData = result.chart_data.data;
        console.log('使用 result.chart_data.data 路径 (interactionData):', resultData);
      } else if (result?.data) {
        resultData = result.data;
        console.log('使用 result.data 路径:', resultData);
      } else if (result?.charts) {
        resultData = result;
        console.log('使用 result 直接路径:', resultData);
      } else {
        resultData = result;
        console.log('使用默认 result 路径:', resultData);
      }

      // 🔧 检查是否为单个表格数据（只有单个表格才在timeline中直接显示）
      // 注意：多图表检测逻辑在后面，这里先做简单检测
      const hasMultipleCharts = (
        (resultData && resultData.chart_type === 'multi_charts' && resultData.charts) ||
        (resultData && resultData.display_format === 'multi_chart' && resultData.charts) ||
        (resultData && resultData.total_charts > 1 && resultData.charts) ||
        // 🔥 修复：检查原始result中的display_format（用户数据结构的情况）
        (result?.result?.display_format === 'multi_chart' && resultData.charts) ||
        (result?.display_format === 'multi_chart' && resultData.charts) ||
        (result?.data?.display_format === 'multi_chart' && resultData.charts) ||
        // 🔥 新增：检查chart_data路径（二次打开时的interactionData结构）
        (result?.chart_data?.display_format === 'multi_chart' && resultData.charts)
      );

      // 🔥 修复：区分单表格和多图表中的表格
      // 单表格：直接在timeline渲染，不显示card
      const isSingleTableData = (
        !hasMultipleCharts && (
          resultData?.render_type === 'table' ||
          resultData?.display_format === 'table' ||
          resultData?.table_config ||
          (resultData?.content && resultData?.content?.columns && resultData?.content?.dataSource) ||
          (resultData?.chart_type === 'table' && resultData?.config?.columns)
        )
      );
      
      // 多图表中的表格：显示card和按钮
      const isMultiChartTableData = (
        hasMultipleCharts && resultData.charts && resultData.charts.length === 1 && 
        resultData.charts[0].chart_type === 'table' && resultData.charts[0].config?.columns
      );

      // 处理单表格：直接在timeline渲染
      if (isSingleTableData) {
        console.log('检测到单表格数据，直接在timeline渲染:', resultData);

        // 提取表格数据
        let tableColumns = [];
        let tableDataSource = [];
        let tableTitle = '数据表格';

        // 🔥 检查单图表的table类型
        if (resultData?.chart_type === 'table' && resultData?.config) {
          tableColumns = resultData.config.columns || [];
          tableDataSource = resultData.config.dataSource || [];
          tableTitle = resultData.title || resultData.config.title || '数据表格';
          console.log('使用单图表table数据:', {
            columns: tableColumns.length,
            dataSource: tableDataSource.length,
            title: tableTitle
          });
        }
        // 优先检查table_config字段（MoE生成的表格）
        else if (resultData?.table_config) {
          tableColumns = resultData.table_config.columns || [];
          tableDataSource = resultData.table_config.dataSource || [];

          // 处理title字段（可能是函数字符串）
          let configTitle = resultData.table_config.title;
          if (typeof configTitle === 'string' && configTitle.includes('() =>')) {
            try {
              // 提取函数返回值
              const titleMatch = configTitle.match(/\(\)\s*=>\s*['"`]([^'"`]+)['"`]/);
              if (titleMatch) {
                configTitle = titleMatch[1];
              } else {
                configTitle = eval(`(${configTitle})()`);
              }
            } catch (error) {
              console.warn('解析title函数失败:', error);
              configTitle = '数据表格';
            }
          }

          tableTitle = resultData.title || configTitle || '数据表格';
          console.log('使用table_config数据:', {
            columns: tableColumns.length,
            dataSource: tableDataSource.length,
            title: tableTitle
          });
        } else if (resultData?.content) {
          tableColumns = resultData.content.columns || [];
          tableDataSource = resultData.content.dataSource || [];
          tableTitle = resultData.title || resultData.content.title || '数据表格';
        } else if (resultData?.columns && resultData?.dataSource) {
          tableColumns = resultData.columns;
          tableDataSource = resultData.dataSource;
          tableTitle = resultData.title || '数据表格';
        }

        // 转换为 Antd Table 格式 - 正确处理功能性配置
        const antdColumns = tableColumns.map((col: any, index: number) => {
          let columnConfig: any = {};

          if (typeof col === 'string') {
            columnConfig = {
              title: col,
              dataIndex: col,
              key: col
            };
          } else {
            const dataIndex = col.dataIndex || col.key || `column_${index}`;
            columnConfig = {
              title: col.title || col.dataIndex || `column_${index}`,
              dataIndex: dataIndex,
              key: col.key || col.dataIndex || `column_${index}`,
              ...col // 保留其他原有配置
            };

            // 处理排序功能 - 支持多种格式
            if (columnConfig.sorter) {
              if (columnConfig.sorter === true) {
                // 当sorter为true时，根据数据类型自动生成排序函数
                columnConfig.sorter = (a: any, b: any) => {
                  const aVal = a[columnConfig.dataIndex];
                  const bVal = b[columnConfig.dataIndex];
                  
                  // 处理null/undefined值
                  if (aVal == null && bVal == null) return 0;
                  if (aVal == null) return -1;
                  if (bVal == null) return 1;
                  
                  // 尝试数值比较
                  const aNum = Number(aVal);
                  const bNum = Number(bVal);
                  if (!isNaN(aNum) && !isNaN(bNum)) {
                    return aNum - bNum;
                  }
                  
                  // 字符串比较
                  return String(aVal).localeCompare(String(bVal));
                };
                console.log('自动生成排序函数:', columnConfig.dataIndex);
              } else if (typeof columnConfig.sorter === 'object' && columnConfig.sorter.compare) {
                if (typeof columnConfig.sorter.compare === 'string') {
                  try {
                    // 转换字符串格式的比较函数
                    const compareStr = columnConfig.sorter.compare;
                    if (compareStr.includes('localeCompare')) {
                      // 字符串比较
                      columnConfig.sorter = (a: any, b: any) => {
                        const aVal = a[columnConfig.dataIndex] || '';
                        const bVal = b[columnConfig.dataIndex] || '';
                        return aVal.toString().localeCompare(bVal.toString());
                      };
                    } else if (compareStr.includes(' - ')) {
                      // 数值比较
                      columnConfig.sorter = (a: any, b: any) => {
                        const aVal = a[columnConfig.dataIndex] || 0;
                        const bVal = b[columnConfig.dataIndex] || 0;
                        return aVal - bVal;
                      };
                    } else {
                      // 尝试eval转换
                      columnConfig.sorter = eval(`(a, b) => ${compareStr}`);
                    }
                    console.log('sorter函数转换成功');
                  } catch (error) {
                    console.warn('转换sorter函数失败:', error);
                    // 转换失败时使用默认排序
                    columnConfig.sorter = (a: any, b: any) => {
                      const aVal = a[columnConfig.dataIndex];
                      const bVal = b[columnConfig.dataIndex];
                      if (aVal == null && bVal == null) return 0;
                      if (aVal == null) return -1;
                      if (bVal == null) return 1;
                      return String(aVal).localeCompare(String(bVal));
                    };
                  }
                }
              }
            }
          }

          // 🔧 修复：转换字符串格式的函数为真正的 JavaScript 函数
          if (columnConfig.onFilter && typeof columnConfig.onFilter === 'string') {
            try {
              console.log('转换 onFilter 函数:', columnConfig.onFilter);
              // 提取函数体，更安全的转换方式
              const functionBody = columnConfig.onFilter.replace(/^\(.*?\)\s*=>\s*/, '');
              columnConfig.onFilter = new Function('value', 'record', `return ${functionBody}`);
              console.log('onFilter 函数转换成功');
            } catch (error) {
              console.warn(`转换 onFilter 函数失败:`, error);
              delete columnConfig.onFilter;
            }
          }

          // 🔧 新增：处理字符串格式的 render 函数
          if (columnConfig.render && typeof columnConfig.render === 'string') {
            try {
              console.log('转换 render 函数:', columnConfig.render);

              // 处理不同格式的函数字符串
              let renderFunction;

              // 检查是否包含valueFormatter（可能是错误的属性名）
              if (columnConfig.render.includes('valueFormatter')) {
                console.warn('检测到valueFormatter，可能是错误的属性名，忽略此render函数');
                throw new Error('Invalid render function contains valueFormatter');
              }

              if (columnConfig.render.includes('=>')) {
                // 箭头函数格式：value => ... 或 (value) => ...
                const simpleArrowMatch = columnConfig.render.match(/^(\w+)\s*=>\s*(.+)$/);
                const complexArrowMatch = columnConfig.render.match(/^\(([^)]*)\)\s*=>\s*(.+)$/);

                if (simpleArrowMatch) {
                  // 简单箭头函数：value => expression
                  const param = simpleArrowMatch[1];
                  const body = simpleArrowMatch[2];
                  
                  // 安全检查函数体
                  if (body.includes('valueFormatter')) {
                    console.warn('函数体包含valueFormatter，跳过转换');
                    throw new Error('Function body contains valueFormatter');
                  }
                  
                  renderFunction = new Function(param, `return ${body}`);
                  console.log('render 函数转换成功（简单箭头函数）');
                } else if (complexArrowMatch) {
                  // 复杂箭头函数：(value, record, index) => expression
                  const params = complexArrowMatch[1];
                  const body = complexArrowMatch[2];
                  
                  // 安全检查函数体
                  if (body.includes('valueFormatter')) {
                    console.warn('函数体包含valueFormatter，跳过转换');
                    throw new Error('Function body contains valueFormatter');
                  }
                  
                  renderFunction = new Function(...params.split(',').map((p: string) => p.trim()), `return ${body}`);
                  console.log('render 函数转换成功（复杂箭头函数）');
                } else {
                  // 尝试直接eval
                  if (columnConfig.render.includes('valueFormatter')) {
                    console.warn('函数包含valueFormatter，跳过转换');
                    throw new Error('Function contains valueFormatter');
                  }
                  renderFunction = eval(`(${columnConfig.render})`);
                  console.log('render 函数转换成功（eval）');
                }
              } else {
                // 可能是函数体或其他格式
                if (columnConfig.render.includes('valueFormatter')) {
                  console.warn('函数包含valueFormatter，跳过转换');
                  throw new Error('Function contains valueFormatter');
                }
                renderFunction = eval(`(${columnConfig.render})`);
                console.log('render 函数转换成功（直接eval）');
              }

              columnConfig.render = renderFunction;

            } catch (error) {
              console.warn(`转换 render 函数失败:`, error, '原始函数:', columnConfig.render);
              // 如果转换失败，使用默认的render函数
              columnConfig.render = (text: any) => (
                <Typography.Paragraph
                  ellipsis={{ rows: 3, expandable: true, symbol: '更多' }}
                  style={{ marginBottom: 0 }}
                >
                  {text === null || text === undefined ? '' : String(text)}
                </Typography.Paragraph>
              );
            }
          }

          // 🎨 新增：处理 anomaly_detection 配置
          if (columnConfig.anomaly_detection && columnConfig.anomaly_detection.enabled) {
            const anomalyConfig = columnConfig.anomaly_detection;
            const rules = anomalyConfig.rules || [];
            const highlightStyle = anomalyConfig.highlight_style || {};

            console.log('检测到异常检测配置:', anomalyConfig);

            // 创建异常检测render函数
            columnConfig.render = (value: any, record: any, index: number) => {
              // 检测异常类型
              let anomalyType = null;
              let displayValue = value;

              // 数值转换（处理字符串格式的数值）
              const numericValue = typeof value === 'string' ? parseFloat(value) : value;
              const isNumeric = !isNaN(numericValue) && isFinite(numericValue);

              // 1. 缺失值检测
              if (rules.includes('null_check') && (value === null || value === undefined || value === '')) {
                anomalyType = 'missing';
                displayValue = 'N/A';
              }
              // 2. 负值检测
              else if (rules.includes('negative_check') && isNumeric && numericValue < 0) {
                anomalyType = 'critical';
              }
              // 3. 零值检测
              else if (rules.includes('zero_check') && isNumeric && numericValue === 0) {
                anomalyType = 'warning';
              }
              // 4. 离群值检测（简单实现：可以根据需要扩展）
              else if (rules.includes('outlier_check') && isNumeric) {
                // 这里可以实现更复杂的离群值检测逻辑
                // 暂时跳过，因为需要整个数据集的统计信息
              }

              // 应用样式和图标
              if (anomalyType && highlightStyle[anomalyType]) {
                const style = highlightStyle[anomalyType];
                const icon = style.icon || '';

                return (
                  <span
                    style={{
                      backgroundColor: style.backgroundColor,
                      color: style.color,
                      padding: '2px 6px',
                      borderRadius: '4px',
                      fontSize: '12px',
                      fontWeight: 'bold'
                    }}
                    title={`异常检测: ${anomalyType}`}
                  >
                    {icon && <span style={{ marginRight: '4px' }}>{icon}</span>}
                    {displayValue}
                  </span>
                );
              }

              // 正常值：检查是否需要正向突出显示
              if (isNumeric && numericValue > 0 && highlightStyle.positive) {
                const style = highlightStyle.positive;
                const icon = style.icon || '';

                return (
                  <span
                    style={{
                      backgroundColor: style.backgroundColor,
                      color: style.color,
                      padding: '2px 6px',
                      borderRadius: '4px',
                      fontSize: '12px',
                      fontWeight: 'bold'
                    }}
                    title="正常值"
                  >
                    {icon && <span style={{ marginRight: '4px' }}>{icon}</span>}
                    {value}
                  </span>
                );
              }

              // 默认显示
              return value;
            };

            // 移除anomaly_detection配置，避免传递给Antd Table
            delete columnConfig.anomaly_detection;
          }

          return columnConfig;
        });

        // 确保数据源中每行都有key字段
        const antdDataSource = tableDataSource.map((row: any, index: number) => ({
          key: row.key || index,
          ...row
        }));

        // 分页配置 - 优先使用table_config中的pagination配置
        let paginationConfig: any = false;
        
        if (resultData?.table_config?.pagination) {
          // 使用table_config中的pagination配置
          paginationConfig = { ...resultData.table_config.pagination };
          
          // 处理pagination中的函数字符串，特别是showTotal
          if (paginationConfig.showTotal && typeof paginationConfig.showTotal === 'string') {
            try {
              console.log('转换 pagination.showTotal 函数:', paginationConfig.showTotal);
              
              // 处理箭头函数格式：total => ... 或 (total) => ...
              const simpleArrowMatch = paginationConfig.showTotal.match(/^(\w+)\s*=>\s*(.+)$/);
              const complexArrowMatch = paginationConfig.showTotal.match(/^\(([^)]*)\)\s*=>\s*(.+)$/);
              
              if (simpleArrowMatch) {
                // 简单箭头函数：total => expression
                const param = simpleArrowMatch[1];
                const body = simpleArrowMatch[2];
                paginationConfig.showTotal = new Function(param, `return ${body}`);
                console.log('pagination.showTotal 函数转换成功（简单箭头函数）');
              } else if (complexArrowMatch) {
                // 复杂箭头函数：(total, range) => expression
                const params = complexArrowMatch[1];
                const body = complexArrowMatch[2];
                paginationConfig.showTotal = new Function(...params.split(',').map((p: string) => p.trim()), `return ${body}`);
                console.log('pagination.showTotal 函数转换成功（复杂箭头函数）');
              } else {
                // 尝试直接eval
                paginationConfig.showTotal = eval(`(${paginationConfig.showTotal})`);
                console.log('pagination.showTotal 函数转换成功（eval）');
              }
            } catch (error) {
              console.warn('转换 pagination.showTotal 函数失败:', error);
              // 使用默认的showTotal函数
              paginationConfig.showTotal = (total: number, range: [number, number]) =>
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`;
            }
          }
        } else if (antdDataSource.length > 10) {
          // 回退到默认分页配置
          paginationConfig = {
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total: number, range: [number, number]) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
            pageSizeOptions: ['10', '20', '50']
          };
        }

        return (
          <div style={{ margin: '20px 0' }}>
            <Card
              title={tableTitle}
              style={{
                boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                border: 'none',
                borderRadius: 12
              }}
              headStyle={{
                background: 'transparent',
                border: 'none',
                color: '#2c3e50',
                fontWeight: 'bold'
              }}
              bodyStyle={{ padding: '20px', background: 'white', borderRadius: '0 0 12px 12px' }}
            >
              <Table
                columns={antdColumns}
                dataSource={antdDataSource}
                size={resultData?.table_config?.size || "small"}
                pagination={paginationConfig}
                bordered={resultData?.table_config?.bordered !== false}
                scroll={resultData?.table_config?.scroll || { x: true }}
                onChange={(pagination, filters, sorter, extra) => {
                  console.log('表格状态变化:', {
                    pagination,
                    filters,
                    extra
                  });
                }}
                rowKey="key"
              />
            </Card>
          </div>
        );
      }

      // 处理多图表中的表格：显示card和按钮
      if (isMultiChartTableData) {
        console.log('检测到多图表中的表格数据，显示card:', resultData);
        
        const tableChart = resultData.charts[0];
        const tableTitle = tableChart.title || tableChart.config?.title || '数据表格';
        
        return (
          <div style={{ padding: '16px' }}>
            <Card 
              size="small" 
              style={{ 
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                border: 'none',
                borderRadius: 12,
                color: 'white'
              }}
              bodyStyle={{ padding: '20px' }}
            >
              <Row justify="space-between" align="middle" gutter={[16, 8]}>
                <Col xs={24} md={16}>
                  <Space direction="vertical" size="small" style={{ width: '100%' }}>
                    <div>
                      <Text strong style={{ fontSize: '16px', color: 'white' }}>
                        📊 {t('analysis:tools.chartAnalysis')}
                      </Text>
                    </div>
                    <div>
                      <Text style={{ fontSize: '14px', color: 'rgba(255,255,255,0.9)' }}>
                        {tableTitle}
                      </Text>
                    </div>
                  </Space>
                </Col>
                <Col xs={24} md={8} style={{ textAlign: 'right' }}>
                  <Button 
                    type="primary" 
                    ghost
                    icon={<EyeOutlined />}
                    size="large"
                    style={{ 
                      borderRadius: 8,
                      fontWeight: 500,
                      borderColor: 'white',
                      color: 'white',
                      minWidth: '120px'
                    }}
                    onClick={() => {
                      // 创建图表数据用于弹框查看
                      // 🔥 修复：确保display_format能够正确传递，支持多种数据结构
                      const baseData = result.result || {};
                      
                      // 检查并提取display_format从不同可能的位置
                      let displayFormat = baseData.display_format;
                      if (!displayFormat) {
                        // 检查chart_data层级（二次打开的interactionData结构）
                        displayFormat = result.chart_data?.display_format;
                      }
                      if (!displayFormat) {
                        // 检查其他可能的位置
                        displayFormat = result.display_format || result.data?.display_format;
                      }
                      
                      const chartWithId = {
                        ...baseData,
                        data: resultData,
                        display_format: displayFormat, // 🔥 确保display_format被正确设置
                        chartId: `chart_view_${Date.now()}`,
                        stepId: result.step_id || 'unknown',
                        generatedAt: new Date().toISOString()
                      };
                      
                      console.log('构建的chartWithId数据:', {
                        hasDisplayFormat: !!chartWithId.display_format,
                        displayFormat: chartWithId.display_format,
                        hasDataCharts: !!chartWithId.data?.charts,
                        chartsLength: chartWithId.data?.charts?.length
                      });
                      
                      setChartData(chartWithId);
                      setChartModalVisible(true);
                    }}
                  >
                    {t('analysis:tools.viewChart')}
                  </Button>
                </Col>
              </Row>
            </Card>

            {/* 执行时间信息 */}
            <div style={{ textAlign: 'center', padding: '12px 0' }}>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {t('analysis:tools.chartGenerationComplete')}
              </Text>
            </div>
          </div>
        );
      }

      // 检查是否为多图表结构 - 支持新的MoE多图表格式
      const isMultiChart = (
        (resultData && resultData.chart_type === 'multi_charts' && resultData.charts) ||
        (resultData && resultData.display_format === 'multi_chart' && resultData.charts) ||
        (resultData && resultData.total_charts > 1 && resultData.charts)
      );
      const totalCharts = isMultiChart ? resultData.charts.length : 1;
      
      // 检查是否有有效的图表配置 - 支持新的MoE多图表格式
      let hasValidChart = false;
      if (isMultiChart) {
        hasValidChart = resultData.charts.some((chart: any) => {
          // 支持新的MoE格式：chart.config 或 chart.chart_config
          const chartConfig = chart.config || chart.chart_config;
          return chartConfig && (
            (chartConfig.series && Array.isArray(chartConfig.series) && chartConfig.series.length > 0) ||
            (chart.chart_type === 'table' && chartConfig.columns) ||
            (chart.chart_type === 'echarts' && chartConfig.dataset)
          );
        });
      } else {
        // 支持新格式的单图表验证
        const chartConfig = resultData?.chart_config || resultData?.charts?.[0]?.config;
        const chartType = resultData?.chart_type || resultData?.charts?.[0]?.chart_type || 'echarts';

        hasValidChart = chartConfig && (
          // ECharts类型图表验证
          (chartConfig.series && Array.isArray(chartConfig.series) && chartConfig.series.length > 0) ||
          // Table类型图表验证
          (chartType === 'table' && chartConfig.columns && Array.isArray(chartConfig.columns) && chartConfig.columns.length > 0) ||
          // Dataset格式图表验证
          (chartConfig.dataset && chartConfig.dataset.source)
        );
      }

      if (!hasValidChart) {
        return (
          <div style={{ padding: '16px' }}>
            <Alert 
              message={t('analysis:tools.chartConfigInvalidOrEmpty')}
              description="未能生成有效的图表配置"
              type="warning" 
              showIcon 
              style={{ marginBottom: 16 }}
            />
            <div>
              <Text strong>调试信息：</Text>
              <div style={{ marginTop: 8, fontSize: '12px' }}>
                <div>数据结构检查:</div>
                <ul style={{ margin: '4px 0', paddingLeft: '20px' }}>
                  <li>result?.result?.result?.data: {result?.result?.result?.data ? '✓ 存在' : '✗ 不存在'}</li>
                  <li>result?.result?.data: {result?.result?.data ? '✓ 存在' : '✗ 不存在'}</li>
                  <li>result?.result?.result: {result?.result?.result ? '✓ 存在' : '✗ 不存在'}</li>
                  <li>result?.result: {result?.result ? '✓ 存在' : '✗ 不存在'}</li>
                  <li>result?.data: {result?.data ? '✓ 存在' : '✗ 不存在'}</li>
                  <li>result?.charts: {result?.charts ? '✓ 存在' : '✗ 不存在'}</li>
                  <li>resultData: {resultData ? '✓ 存在' : '✗ 不存在'}</li>
                  <li>isMultiChart: {isMultiChart ? '✓ 是' : '✗ 否'}</li>
                </ul>
              </div>
              <Text strong>{t('analysis:tools.rawData')}</Text>
              <pre style={{ 
                background: '#fff7e6', 
                padding: 12, 
                borderRadius: 6,
                border: '1px solid #ffd591',
                fontSize: '12px',
                maxHeight: '200px',
                overflow: 'auto',
                marginTop: 8
              }}>
                {JSON.stringify(result, null, 2)}
              </pre>
              <Text strong>解析后的resultData：</Text>
              <pre style={{ 
                background: '#f0f5ff', 
                padding: 12, 
                borderRadius: 6,
                border: '1px solid #adc6ff',
                fontSize: '12px',
                maxHeight: '200px',
                overflow: 'auto',
                marginTop: 8
              }}>
                {JSON.stringify(resultData, null, 2)}
              </pre>
            </div>
          </div>
        );
      }

      return (
        <div style={{ padding: '16px' }}>
          <Card 
            size="small" 
            style={{ 
              background: isMultiChart 
                ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' 
                : 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
              border: 'none',
              borderRadius: 12,
              color: 'white'
            }}
            bodyStyle={{ padding: '20px' }}
          >
            <Row justify="space-between" align="middle" gutter={[16, 8]}>
              <Col xs={24} md={16}>
                <Space direction="vertical" size="small" style={{ width: '100%' }}>
                  <div>
                    <Text strong style={{ fontSize: '16px', color: 'white' }}>
                      {isMultiChart ? `📊 ${t('analysis:tools.multiChartAnalysis')}` : `📈 ${t('analysis:tools.chartAnalysis')}`}
                    </Text>
                  </div>
                  <div>
                    <Text style={{ fontSize: '14px', color: 'rgba(255,255,255,0.9)' }}>
                      {isMultiChart 
                        ? t('analysis:tools.chartsGenerated', { count: totalCharts })
                        : t('analysis:tools.singleChartGenerated')
                      }
                    </Text>
                  </div>
                  {resultData.user_query && (
                    <div>
                      <Text style={{ fontSize: '13px', color: 'rgba(255,255,255,0.8)' }}>
                        <strong>查询：</strong>{resultData.user_query}
                      </Text>
                    </div>
                  )}
                  {resultData.generated_reason && (
                    <div>
                      <Text style={{ fontSize: '12px', color: 'rgba(255,255,255,0.7)' }}>
                        <strong>{t('analysis:tools.generationReason')}</strong>{resultData.generated_reason}
                      </Text>
                    </div>
                  )}
                </Space>
              </Col>
              <Col xs={24} md={8} style={{ textAlign: 'right' }}>
                <Button 
                  type="primary" 
                  ghost
                  icon={<EyeOutlined />}
                  size="large"
                  style={{ 
                    borderRadius: 8,
                    fontWeight: 500,
                    borderColor: 'white',
                    color: 'white',
                    minWidth: '120px'
                  }}
                  onClick={() => {
                    // 创建图表数据用于弹框查看
                    // 🔥 修复：确保display_format能够正确传递，支持多种数据结构
                    const baseData = result.result || {};
                    
                    // 检查并提取display_format从不同可能的位置
                    let displayFormat = baseData.display_format;
                    if (!displayFormat) {
                      // 检查chart_data层级（二次打开的interactionData结构）
                      displayFormat = result.chart_data?.display_format;
                    }
                    if (!displayFormat) {
                      // 检查其他可能的位置
                      displayFormat = result.display_format || result.data?.display_format;
                    }
                    
                    const chartWithId = {
                      ...baseData,
                      data: resultData,
                      display_format: displayFormat, // 🔥 确保display_format被正确设置
                      chartId: `chart_view_${Date.now()}`,
                      stepId: result.step_id || 'unknown',
                      generatedAt: new Date().toISOString()
                    };
                    
                    console.log('构建的chartWithId数据:', {
                      hasDisplayFormat: !!chartWithId.display_format,
                      displayFormat: chartWithId.display_format,
                      hasDataCharts: !!chartWithId.data?.charts,
                      chartsLength: chartWithId.data?.charts?.length
                    });
                    
                    setChartData(chartWithId);
                    setChartModalVisible(true);
                  }}
                >
                  {isMultiChart ? t('analysis:tools.viewAllCharts') : t('analysis:tools.viewChart')}
                </Button>
              </Col>
            </Row>
          </Card>

          {/* 执行时间信息 */}
          <div style={{ textAlign: 'center', padding: '12px 0' }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {isMultiChart ? t('analysis:tools.chartsGenerated', { count: totalCharts }) : t('analysis:tools.chartGenerationComplete')}
            </Text>
          </div>
        </div>
      );
    } catch (error) {
      console.error('渲染图表结果失败:', error);
      return (
        <div style={{ padding: '16px' }}>
          <Alert 
            message={`渲染图表时出错: ${String(error)}`}
            type="error" 
            showIcon 
            style={{ marginBottom: 16 }}
          />
          <div>
            <Text strong>{t('analysis:errors.errorDetails')}</Text>
            <pre style={{ 
              background: '#fff2f0', 
              padding: 12, 
              borderRadius: 6,
              border: '1px solid #ffccc7',
              fontSize: '12px',
              maxHeight: '200px',
              overflow: 'auto',
              marginTop: 8
            }}>
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        </div>
      );
    }
  };

  // 解析用户回答文本为结构化数据
  const parseUserAnswers = (userAdjustment: string, clarificationQuestions: any[]): Record<string, string> => {
    if (!userAdjustment || !clarificationQuestions) return {};
    
    const answers: Record<string, string> = {};
    
    // 按行分割用户回答
    const lines = userAdjustment.split('\n').filter(line => line.trim());
    
    let currentQuestionIndex = -1;
    for (const line of lines) {
      // 检查是否是问题行（包含问号）
      if (line.includes('？') || line.includes('?')) {
        // 找到对应的问题
        const questionText = line.replace(/^.*?：/, '').trim();
        currentQuestionIndex = clarificationQuestions.findIndex(q => 
          q.question && questionText.includes(q.question.replace('？', '').replace('?', ''))
        );
      }
      // 检查是否是答案行
      else if (line.includes('答案：') && currentQuestionIndex >= 0) {
        const answer = line.replace(/^.*?答案：/, '').trim();
        const questionId = clarificationQuestions[currentQuestionIndex]?.id;
        if (questionId && answer) {
          answers[questionId] = answer;
        }
      }
    }
    
    return answers;
  };

  // 渲染意图分析内容
  const renderIntentAnalysis = (intentData?: any, clarificationData?: any) => {
    const intentToRender = intentData || intentAnalysis;
    if (!intentToRender) return null;

    // 只展示intent_description字段，移除所有兼容性代码
    if (!intentToRender.intent_description) {
      return (
        <div style={{ padding: 8 }}>
          <Text type="secondary">{t('analysis:errors.noIntentAnalysisData')}</Text>
        </div>
      );
    }

    // 渲染澄清问题和答案
    const renderClarificationSection = () => {
      const needsClarification = intentToRender.needs_clarification;
      const clarificationQuestions = intentToRender.clarification_questions;
      const clarificationPrompt = intentToRender.clarification_prompt;
      
      // 如果没有澄清需求，不渲染
      if (!needsClarification || !clarificationQuestions || !Array.isArray(clarificationQuestions) || clarificationQuestions.length === 0) {
        return null;
      }

      return (
        <div style={{ marginTop: 12 }}>
          <Text strong style={{ color: '#595959', fontSize: '13px', marginBottom: 6, display: 'block' }}>
            澄清问题与回答:
          </Text>
          
          {/* 澄清提示 */}
          {clarificationPrompt && (
            <div style={{ 
              background: 'linear-gradient(135deg, #fef7ff 0%, #f3e8ff 100%)', 
              border: '1px solid #e9d5ff',
              borderRadius: 8,
              padding: '12px 16px',
              marginBottom: 16,
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
            }}>
              <Text style={{ fontSize: '13px', color: '#7c3aed', lineHeight: '1.6', fontWeight: '400' }}>
                {clarificationPrompt}
              </Text>
            </div>
          )}
          
          {/* 澄清问题列表 */}
          <div style={{ paddingLeft: 12 }}>
            {clarificationQuestions.map((question: any, index: number) => {
              const userAnswer = clarificationData && clarificationData[question.id];
              
              return (
                <div 
                  key={question.id || index} 
                  style={{
                    background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
                    border: '1px solid #e2e8f0',
                    borderRadius: 12,
                    padding: '16px 20px',
                    marginBottom: 16,
                    position: 'relative',
                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.04)',
                    transition: 'all 0.2s ease'
                  }}
                >
                  {/* 问题编号 */}
                  <div style={{
                    position: 'absolute',
                    top: -8,
                    left: 12,
                    background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)',
                    color: 'white',
                    borderRadius: '50%',
                    width: 26,
                    height: 26,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '12px',
                    fontWeight: '600',
                    boxShadow: '0 4px 12px rgba(99, 102, 241, 0.25)',
                    border: '2px solid white'
                  }}>
                    Q{index + 1}
                  </div>

                  {/* 问题内容 */}
                  <div style={{ marginTop: 8 }}>
                    <div style={{ marginBottom: 8 }}>
                      <Text strong style={{ color: '#6366f1', fontSize: '13px' }}>
                        问题：
                      </Text>
                      <Text style={{ fontSize: '13px', lineHeight: '1.5', marginLeft: 4, color: '#334155' }}>
                        {question.question}
                      </Text>
                    </div>

                    {/* 可选项 - 优化为紧凑的选择按钮组 */}
                    {question.options && Array.isArray(question.options) && question.options.length > 0 && (
                      <div style={{ marginBottom: 12 }}>
                        <Text strong style={{ color: '#64748b', fontSize: '12px', marginBottom: 8, display: 'block' }}>
                          可选项：
                        </Text>
                        <div style={{ 
                          display: 'grid', 
                          gridTemplateColumns: 'repeat(auto-fit, minmax(min(120px, 100%), 1fr))',
                          gap: '6px',
                          marginTop: 6
                        }}>
                          {question.options.map((option: string, optIndex: number) => {
                            const isSelected = userAnswer === option;
                            return (
                              <div
                              key={optIndex}
                                onClick={() => {
                                  setClarificationAnswers(prev => ({
                                    ...prev,
                                    [question.id]: option
                                  }));
                                }}
                              style={{ 
                                  padding: '8px 12px',
                                  borderRadius: 8,
                                  border: isSelected ? '2px solid #2563eb' : '1px solid #e2e8f0',
                                  background: isSelected 
                                    ? 'linear-gradient(135deg, #2563eb 0%, #3b82f6 100%)' 
                                    : 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
                                  color: isSelected ? '#ffffff' : '#475569',
                                  fontSize: '12px',
                                  fontWeight: isSelected ? '500' : '400',
                                  textAlign: 'center',
                                  cursor: 'pointer',
                                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                                  userSelect: 'none',
                                  minHeight: '36px',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  lineHeight: '1.2',
                                  boxShadow: isSelected 
                                    ? '0 4px 12px rgba(37, 99, 235, 0.15)' 
                                    : '0 1px 3px rgba(0, 0, 0, 0.05)',
                                  position: 'relative',
                                  overflow: 'hidden'
                                }}
                                onMouseEnter={(e) => {
                                  if (!isSelected) {
                                    e.currentTarget.style.background = 'linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%)';
                                    e.currentTarget.style.borderColor = '#cbd5e1';
                                    e.currentTarget.style.transform = 'translateY(-1px)';
                                    e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)';
                                  }
                                }}
                                onMouseLeave={(e) => {
                                  if (!isSelected) {
                                    e.currentTarget.style.background = 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)';
                                    e.currentTarget.style.borderColor = '#e2e8f0';
                                    e.currentTarget.style.transform = 'translateY(0)';
                                    e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.05)';
                                  }
                              }}
                            >
                              {option}
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    )}

                    {/* 用户答案 */}
                    {userAnswer && (
                      <div style={{
                        background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',
                        border: '1px solid #e2e8f0',
                        borderRadius: 8,
                        padding: '8px 12px',
                        marginTop: 10,
                        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
                      }}>
                        <Text strong style={{ color: '#6366f1', fontSize: '12px' }}>
                          已选择：
                        </Text>
                        <Text style={{ 
                          fontSize: '12px', 
                          lineHeight: '1.4', 
                          marginLeft: 4,
                          color: '#334155',
                          fontWeight: '500'
                        }}>
                          {userAnswer}
                        </Text>
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      );
    };

    // 渲染执行步骤
    const renderExecutionSteps = () => {
      const steps = intentToRender.execution_steps;
      if (!steps || !Array.isArray(steps) || steps.length === 0) {
        return null;
      }

      return (
        <div style={{ marginTop: 12 }}>
          <Text strong style={{ color: '#595959', fontSize: '13px', marginBottom: 6, display: 'block' }}>
            执行步骤规划:
          </Text>
          <div style={{ paddingLeft: 12 }}>
            {steps.map((step, index) => (
              <div 
                key={index} 
                style={{
                  background: '#f0f8ff',
                  border: '1px solid #d6e4ff',
                  borderRadius: 6,
                  padding: '12px 16px',
                  marginBottom: 12,
                  position: 'relative'
                }}
              >
                {/* 步骤编号 */}
                <div style={{
                  position: 'absolute',
                  top: -8,
                  left: 12,
                  background: '#1890ff',
                  color: 'white',
                  borderRadius: '50%',
                  width: 24,
                  height: 24,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '12px',
                  fontWeight: 'bold'
                }}>
                  {step.step_number || index + 1}
                </div>

                {/* 步骤内容 */}
                <div style={{ marginTop: 8 }}>
                  {/* 步骤意图 */}
                  <div style={{ marginBottom: 8 }}>
                    <Text strong style={{ color: '#1890ff', fontSize: '13px' }}>
                      目标：
                    </Text>
                    <Text style={{ fontSize: '13px', lineHeight: '1.5', marginLeft: 4 }}>
                      {step.step_intent}
                    </Text>
                  </div>

                  {/* 推荐工具 */}
                  <div style={{ marginBottom: 8 }}>
                    <Text strong style={{ color: '#52c41a', fontSize: '13px' }}>
                      工具：
                    </Text>
                    <Tag 
                      color="blue" 
                      style={{ 
                        marginLeft: 4,
                        fontSize: '12px',
                        borderRadius: 12
                      }}
                    >
                      {step.recommended_tool}
                    </Tag>
                  </div>

                  {/* 工具选择理由 */}
                  <div style={{ marginBottom: 8 }}>
                    <Text strong style={{ color: '#fa8c16', fontSize: '13px' }}>
                      理由：
                    </Text>
                    <Text style={{ 
                      fontSize: '12px', 
                      lineHeight: '1.4', 
                      marginLeft: 4,
                      color: '#666'
                    }}>
                      {step.tool_usage_reason}
                    </Text>
                  </div>

                  {/* 预期结果 */}
                  <div>
                    <Text strong style={{ color: '#722ed1', fontSize: '13px' }}>
                      预期：
                    </Text>
                    <Text style={{ 
                      fontSize: '12px', 
                      lineHeight: '1.4', 
                      marginLeft: 4,
                      color: '#666'
                    }}>
                      {step.expected_outcome}
                    </Text>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      );
    };

    return (
      <div style={{
        background: '#fafafa', 
        border: '1px solid #f0f0f0',
        borderRadius: 4,
        padding: '12px 16px'
      }}>
        {/* 意图描述 */}
        <div style={{ marginBottom: 12 }}>
          <Text strong style={{ color: '#595959', fontSize: '13px', display: 'block', marginBottom: 6 }}>
            意图描述:
          </Text>
          <div style={{ 
            background: '#fff', 
            padding: '10px 12px',
            borderRadius: 4,
            border: '1px solid #e8e8e8'
          }}>
            <Text style={{ fontSize: '13px', lineHeight: '1.5', color: '#333' }}>
              {intentToRender.intent_description}
            </Text>
          </div>
        </div>

        {/* 澄清问题和答案 */}
        {renderClarificationSection()}

        {/* 执行步骤 */}
        {renderExecutionSteps()}
      </div>
    );
  };



  // 渲染步骤结果 (完整版本)
  const renderStepResult = (step: any, stepMcpResults?: Record<string, any>) => {
    console.log('🚀 renderStepResult 被调用:', step.id, step.tool_name);
    
    // 使用传入的mcpResults或默认的mcpResults
    const resultsToUse = stepMcpResults || mcpResults;
    
    console.log('renderStepResult 调用:', {
      step: step,
      hasResultKey: !!step?.resultKey,
      resultKey: step?.resultKey,
      mcpResultsKeys: Object.keys(resultsToUse),
      mcpResultForStep: resultsToUse[step?.resultKey],
      hasResult: !!step?.hasResult,
      stepHasResult: step?.hasResult,
      stepName: step?.name,
      stepToolName: step?.tool_name,
      stepParameters: step?.parameters,
      hasParameters: !!step?.parameters,
      hasQuestions: !!(step?.parameters?.questions)
    });
    
    // @fixed_implementation_start
    // 实现标识: 数据检查逻辑修复
    // 功能描述: 修复数据检查逻辑，支持interactionData作为备用数据源
    // 修复内容: 当mcp_results为空时，检查step是否有interactionData，避免图表等结果无法渲染
    // 状态: 已确认 (CONFIRMED_IMPLEMENTATION)
    // @fixed_implementation_end
    
    // 检查是否有可用的数据源
    const hasMcpResult = step?.resultKey && resultsToUse[step.resultKey];
    const hasInteractionData = step?.interactionData && Object.keys(step.interactionData).length > 0;
    const hasSpecialData = step?.hasChartData || step?.hasIntentData || step?.hasReport;
    
    if (!step || (!hasMcpResult && !hasInteractionData && !hasSpecialData)) {
      console.log('renderStepResult 返回null，原因:', {
        noStep: !step,
        noResultKey: !step?.resultKey,
        noMcpResult: !hasMcpResult,
        noInteractionData: !hasInteractionData,
        noSpecialData: !hasSpecialData
      });
      return null;
    }

    // 获取结果数据 - 优先使用mcp结果，其次使用interactionData
    const result = hasMcpResult ? resultsToUse[step.resultKey] : (step.interactionData || {});
    

    
    // 错误状态步骤现在通过mcpResults正常展示，不需要特殊处理
    
    // 渲染工具参数信息
    const renderLlmParameters = () => {
      // 获取参数信息（优先使用step.parameters，其次尝试从executionRecord中获取）
      const parameters = step.parameters || 
                        (step.executionRecord?.details?.parameters) || 
                        (step.executionRecord?.operation_type === 'tool' && step.executionRecord?.details?.tool_arguments);
      
      if (!parameters || Object.keys(parameters).length === 0) return null;
      if(step.tool_name === '自动SQL查询') return null;
      if(step.tool_name === '意图确认') return null; // 意图确认工具不显示参数
      if(step.tool_name === t('analysis:tools.intelligentChartGeneration') || step.tool_name === '智能图表生成') return null; // 智能图表生成工具不显示参数
      return (
        <div style={{ marginBottom: 12, borderBottom: '1px dashed #e8e8e8', paddingBottom: 8 }}>
          <div style={{ marginBottom: 4 }}>
            <Text strong>{t('analysis:tools.executionParameters')}</Text>
          </div>
          <div style={{ 
            background: '#fafafa', 
            padding: 8, 
            borderRadius: 4,
            border: '1px solid #f0f0f0',
            marginTop: 4,
            maxHeight: '200px',
            overflow: 'auto'
          }}>
            <pre style={{ margin: 0, whiteSpace: 'pre-wrap', fontSize: '12px' }}>
              {JSON.stringify(parameters, null, 2)}
            </pre>
          </div>
        </div>
      );
    };
    
    // 根据操作类型生成卡片标题
    let cardTitle = t('analysis:tools.executionResult');
    if (step.executionRecord) {
      if (step.executionRecord.operation_type === 'resource') {
        const resourcePath = step.executionRecord.details?.resource_path || t('analysis:tools.unknownResource');
        cardTitle = t('analysis:tools.resourceResult', { resourcePath });
      } else if (step.executionRecord.operation_type === 'tool') {
        const toolName = step.executionRecord.details?.tool_name || t('analysis:tools.unknownTool');
        cardTitle = t('analysis:tools.toolExecutionResult', { toolName });
      }
    }
    // 获取执行时间
    const timestamp = step.executionRecord?.timestamp ? new Date(step.executionRecord.timestamp) : null;
    const hasExecutionTime = result.execution_time || (typeof result === 'object' && result.result?.execution_time);
    const executionTime = hasExecutionTime ? 
      (result.execution_time || result.result?.execution_time) : 
      null;
    
    // 优先显示执行时间，其次显示时间戳
    const formattedTime = executionTime ?
      t('analysis:tools.executionTime', { time: typeof executionTime === 'number' ? executionTime.toFixed(3) : parseFloat(String(executionTime)).toFixed(3) }) :
      (timestamp ?
        `${timestamp.toLocaleDateString()} ${timestamp.toLocaleTimeString()}` :
        '');
    
    // 渲染SQL查询结果
    // 基于步骤名或结果中返回的SQL判断
    if (step.tool_name === '自动SQL查询' || result.sql) {
      // 获取SQL语句
      const sql = step.parameters?.sql || result.sql || '';
      return (
        <Card
          bordered={false}
          style={{ marginTop: 16, marginLeft: 24 }}
          size="small"
          title={
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <span>{cardTitle}</span>
              <Text type="secondary">{formattedTime}</Text>
            </div>
          }
        >
          {renderLlmParameters()}
          {sql && (
            <div style={{ marginBottom: 12 }}>
              <Text strong>{t('analysis:tools.executedSQL')}</Text>
              <div style={{
                whiteSpace: 'pre-wrap',
                fontFamily: 'monospace',
                background: '#f0f5ff',
                padding: 8,
                borderRadius: 4,
                border: '1px solid #d6e4ff',
                marginTop: 4
              }}>
                {sql}
              </div>
            </div>
          )}
          <div style={{ marginTop: 8 }}>
            {renderSQLResultTable(result)}
          </div>
        </Card>
      );
    }
    
    // 渲染资源结果（通常是schema信息）
    if (step.executionRecord?.operation_type === 'resource') {
      return (
        <Card 
          bordered={false} 
          style={{ marginTop: 16, marginLeft: 24 }}
          size="small"
          title={
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <span>{cardTitle}</span>
              <Text type="secondary">{formattedTime}</Text>
            </div>
          }
        >
          {renderLlmParameters()}
          <div style={{ 
            whiteSpace: 'pre-wrap', 
            fontFamily: 'monospace', 
            background: '#f8f8f8', 
            padding: 8, 
            borderRadius: 4,
            border: '1px solid #f0f0f0',
            maxHeight: '300px',
            overflow: 'auto'
          }}>
            <JsonView 
              data={result} 
              shouldExpandNode={() => true} 
            />
          </div>
        </Card>
      );
    }
    
    // 新增：特殊工具渲染优先级，使用 step.tool_name
    const toolName = step.tool_name || step.executionRecord?.details?.tool_name;
    
    // 意图分析工具专用渲染
    if (toolName === '意图分析' || step.hasIntentData) {
      // 🔧 修复：检查是否存在意图确认步骤，如果存在则不显示意图分析结果卡片
      // 避免与意图确认卡片重复显示相同内容
      const hasIntentConfirmationStep = analysisSteps.some(s => 
        s.tool_name === '意图确认' || 
        s.id === 'intent_confirmation' ||
        s.id === 'intent_confirmation_request'
      );
      
      // 如果存在意图确认步骤，则不显示意图分析结果卡片
      if (hasIntentConfirmationStep) {
        return null;
      }
      
      return (
        <Card 
          bordered={false} 
          style={{ marginTop: 16, marginLeft: 24 }} 
          size="small"
          title={
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <span>{t('analysis:errors.userIntentAnalysisResult')}</span>
              <Text type="secondary">{formattedTime}</Text>
            </div>
          }
        >
          {renderLlmParameters()}
          {/* 只在没有显示意图确认对话框时显示意图分析结果 */}
          {!showIntentConfirmation && renderIntentAnalysis(result)}
        </Card>
      );
    }
    
    // 🔧 修复：完全移除意图确认工具的卡片渲染，避免重复显示
    // 意图确认信息将在其他地方显示
    if (toolName === '意图确认' || toolName === 'Intent Confirmation' ||
        (step.interactionType === 'intent_confirmation') ||
        (step.id && (step.id.includes('intent_confirmation') || step.id === 'intent_confirmation_request'))) {
      return null;
    }

    // 🔧 修复：洞察步骤不在renderStepResult中渲染，避免重复显示
    // 洞察内容已经在步骤列表中显示，这里直接返回null
    if (step.hasInsights || step.id?.startsWith('insight_')) {
      return null;
    }
    

    

    
    // @fixed_implementation_start
    // 实现标识: 智能图表生成工具渲染修复
    // 功能描述: 修复图表生成工具的识别和数据渲染问题
    // 修复内容: 支持多种图表工具识别方式，正确处理图表数据结构
    // 状态: 已确认 (CONFIRMED_IMPLEMENTATION)
    // @fixed_implementation_end
    
    // 智能图表生成工具 - 支持多种识别方式
    const isChartTool = toolName?.includes(t('analysis:tools.intelligentChartGeneration')) ||
                       toolName?.includes('智能图表生成') ||
                       toolName?.includes('图表生成') ||
                       step.hasChartData ||
                       step.interactionType === 'chart_display' ||
                       result?.display_format === "chart" || 
                       result?.data?.chart_type === "echarts";
    
    if (isChartTool) {
      // 构建图表数据 - 支持多种数据源
      let chartResult = result;
      
      // 如果数据在interactionData中，重构为前端期望的格式
      if (step.interactionData?.chart_data) {
        // 检查数据是否在chart_data.data中（MoE生成的数据结构）
        const chartData = step.interactionData.chart_data.data || step.interactionData.chart_data;
        chartResult = {
          result: chartData,
          execution_time: step.executionTime || result?.execution_time || step.interactionData.chart_data?.execution_time
        };
        console.log('使用interactionData构建图表结果:', chartResult);
        console.log('原始interactionData结构:', step.interactionData);
      } else if (!result?.result && result?.chart_data) {
        // 如果result直接包含chart_data
        chartResult = {
          result: result,
          execution_time: step.executionTime || result?.execution_time
        };
      }
      
      return (
        <Card bordered={false} style={{ marginTop: 16, marginLeft: 24 }} size="small"
          
        >
          {renderLlmParameters()}
          {renderChartResult({ result: chartResult })}
        </Card>
      );
    }
    
    // 资金流转分析图表
    if (toolName?.includes('资金流转')) {
      return (
        <Card bordered={false} style={{ marginTop: 16, marginLeft: 24 }} size="small"
          title={
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <span>{cardTitle}</span><Text type="secondary">{formattedTime}</Text>
            </div>
          }
        >
          {renderLlmParameters()}
          {renderFundTransferResult({ result })}
        </Card>
      );
    }
    // 公司基本信息查询
    if (toolName?.includes('公司基本信息')) {
      return (
        <Card bordered={false} style={{ marginTop: 16, marginLeft: 24 }} size="small"
          title={
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <span>{cardTitle}</span><Text type="secondary">{formattedTime}</Text>
            </div>
          }
        >
          {renderLlmParameters()}
          {renderCompanyInfoResult({ result })}
        </Card>
      );
    }
    // 通用表格数据
    if (result.data && isTableData(result.data)) {
      return (
        <Card bordered={false} style={{ marginTop: 16, marginLeft: 24 }} size="small"
          title={
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <span>{cardTitle}</span><Text type="secondary">{formattedTime}</Text>
            </div>
          }
        >
          {renderLlmParameters()}
          {renderDataTable(result.data)}
        </Card>
      );
    }
    
    // 检查是否为错误结果
    const isErrorResult = result && (result.success === false || result.error);
    
    if (isErrorResult) {
      // 错误结果的特殊渲染
      const errorMessage = result.error || '执行失败';
      const errorType = result.error_type || 'Exception';
      const canContinue = result.can_continue !== false;
      
      return (
        <Card 
          bordered={false} 
          style={{ marginTop: 16, marginLeft: 24 }}
          size="small"
          title={
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <span style={{ color: '#ff4d4f' }}>
                <ExclamationCircleOutlined style={{ marginRight: 6 }} />
                {cardTitle}
              </span>
              <Text type="secondary">{formattedTime}</Text>
            </div>
          }
        >
          {renderLlmParameters()}
          
          <Alert
            message={canContinue ? "步骤执行警告" : "步骤执行失败"}
            description={
              <div>
                <div>{errorMessage}</div>
                {errorType !== 'Exception' && (
                  <div style={{ marginTop: 4, fontSize: '12px', color: '#666' }}>
                    异常类型: {errorType}
                  </div>
                )}
              </div>
            }
            type={canContinue ? "warning" : "error"}
            showIcon
            style={{ marginBottom: 12 }}
          />
          
          
          
          {canContinue && (
            <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
              ℹ️ 分析将继续进行其他步骤
            </div>
          )}
        </Card>
      );
    }
    
    // 默认渲染为JSON查看器
    return (
      <Card 
        bordered={false} 
        style={{ marginTop: 16, marginLeft: 24 }}
        size="small"
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span>{cardTitle}</span>
            <Text type="secondary">{formattedTime}</Text>
          </div>
        }
      >
        {renderLlmParameters()}
        <div style={{ 
          whiteSpace: 'pre-wrap', 
          fontFamily: 'monospace', 
          background: '#f8f8f8', 
          padding: 8, 
          borderRadius: 4,
          border: '1px solid #f0f0f0',
          maxHeight: '300px',
          overflow: 'auto'
        }}>
          {typeof result === 'string' ? (
            result
          ) : (
            <JsonView 
              data={result} 
              shouldExpandNode={() => true} 
            />
          )}
        </div>
      </Card>
    );
  };

  // 渲染步骤名称（支持Markdown格式）
  const renderStepName = (stepName: string) => {
    // 新增：处理翻译键格式
    const parseTranslationKey = (name: string): string => {
      // 处理带参数的翻译键格式：steps.toolsLoaded|{"count":5}
      if (name.includes('|')) {
        const [key, paramStr] = name.split('|');
        try {
          const params = JSON.parse(paramStr);
          return t(`analysis:${key}`, params) as string;
        } catch (e) {
          // 如果参数解析失败，尝试不带参数的翻译
          return t(`analysis:${key}`, { defaultValue: name }) as string;
        }
      }
      
      // 处理简单翻译键格式：steps.start
      if (name.includes('.') && !name.includes(' ') && !name.includes('*')) {
        return t(`analysis:${name}`, { defaultValue: name }) as string;
      }
      
      // 兼容历史数据：如果是硬编码中文，尝试映射到翻译键
      const chineseToKeyMap: Record<string, string> = {
        '开始分析': 'steps.start',
        '分析开始': 'steps.start',
        '创建分析记录': 'steps.analysisCreated',
        '分析用户意图': 'steps.intentAnalyzed',
        '正在分析用户意图': 'steps.intentAnalysisStarted',
        '初始化SQL执行器': 'steps.sqlExecutorInitialized',
        '正在生成分析报告': 'steps.generatingReport',
        '生成分析报告': 'steps.reportGenerated',
        '分析完成': 'steps.completed',
        '分析出错': 'steps.analysisError',
        [t('analysis:tools.intelligentChartGeneration')]: 'tools.intelligentChartGeneration',
        '智能图表生成': 'tools.intelligentChartGeneration',
        '等待用户确认意图': 'intentConfirmation.waitingForConfirmation',
        '意图确认完成': 'intentConfirmation.completed',
        '意图确认完成 (已调整)': 'intentConfirmation.completedAdjusted',
        '应用用户反馈': 'interrupt.feedbackApplied',
        // 新增：英文字符串映射，修复英文环境下意图确认步骤显示问题
        'Intent Confirmation Completed': 'intentConfirmation.completed',
        'Intent Confirmation Completed (Adjusted)': 'intentConfirmation.completedAdjusted',
        'Waiting for user intent confirmation': 'intentConfirmation.waitingForConfirmation',
        'User Feedback Applied': 'interrupt.feedbackApplied'
      };
      
      // 处理"执行工具: XXX"格式
      if (name.startsWith('执行工具: ')) {
        const toolName = name.replace('执行工具: ', '');
        return t('analysis:steps.executeTool', { toolName, defaultValue: name }) as string;
      }
      
      // 处理"已加载 X 个分析工具"格式
      const toolsLoadedMatch = name.match(/已加载 (\d+) 个分析工具/);
      if (toolsLoadedMatch) {
        const count = parseInt(toolsLoadedMatch[1]);
        return t('analysis:steps.toolsLoaded', { count, defaultValue: name }) as string;
      }
      
      // 处理"智能评估: XXX"格式
      if (name.startsWith('智能评估: ')) {
        const reasoning = name.replace('智能评估: ', '');
        return t('analysis:steps.intelligentEvaluation', { reasoning, defaultValue: name }) as string;
      }
      
      // 处理"第X轮规划: XXX"格式（中文）
      const planningMatch = name.match(/第(\d+)轮规划: (.+)/);
      if (planningMatch) {
        const round = parseInt(planningMatch[1]);
        const reasoning = planningMatch[2];
        return t('analysis:steps.planningRound', { round, reasoning, defaultValue: name }) as string;
      }
      
      // 处理"Round X Planning: XXX"格式（英文）
      const planningMatchEn = name.match(/Round (\d+) Planning: (.+)/i);
      if (planningMatchEn) {
        const round = parseInt(planningMatchEn[1]);
        const reasoning = planningMatchEn[2];
        return t('analysis:steps.planningRound', { round, reasoning, defaultValue: name }) as string;
      }
      
      // 尝试从映射表中查找
      if (chineseToKeyMap[name]) {
        return t(`analysis:${chineseToKeyMap[name]}`, { defaultValue: name }) as string;
      }
      
      // 如果都没匹配到，返回原始名称
      return name;
    };
    
    const translatedName = parseTranslationKey(stepName);
    
    // 需求：支持任意Markdown格式的步骤名，不仅限于特定格式
    return (
      <ReactMarkdown
        children={translatedName}
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeRaw]}
        components={{
          // 内联元素样式
          p: ({children}) => <span>{children}</span>, // 将段落转为内联元素
          strong: ({children}) => <strong style={{ fontWeight: 600 }}>{children}</strong>,
          em: ({children}) => <em style={{ fontStyle: 'italic' }}>{children}</em>,
          code: ({children}) => (
            <code style={{
              backgroundColor: '#f6f8fa',
              padding: '2px 4px',
              borderRadius: '3px',
              fontSize: '0.9em',
              color: '#d73a49'
            }}>{children}</code>
          ),
          // 禁用块级元素，确保步骤名保持内联
          h1: ({children}) => <strong>{children}</strong>,
          h2: ({children}) => <strong>{children}</strong>,
          h3: ({children}) => <strong>{children}</strong>,
          h4: ({children}) => <strong>{children}</strong>,
          h5: ({children}) => <strong>{children}</strong>,
          h6: ({children}) => <strong>{children}</strong>,
          ul: ({children}) => <span>{children}</span>,
          ol: ({children}) => <span>{children}</span>,
          li: ({children}) => <span>{children}</span>,
          blockquote: ({children}) => <span>{children}</span>
        }}
      />
    );
  };

  // 渲染最终报告
  const renderFinalReport = (reportContent?: any) => {
    // 使用传入的报告内容或默认的finalReport
    const reportToRender = reportContent || finalReport;
    
    if (!reportToRender) return null;
    
    // 查找报告生成步骤以获取耗时信息
    const reportStep = analysisSteps.find(step => step.id === 'report_generated');
    const reportExecutionTime = toolResults.find(result => result.step_id === 'report_generation')?.execution_time;

    // 处理报告交互事件
    const handleReportInteraction = (componentType: string, action: string, data: any) => {
      console.log('Report interaction:', { componentType, action, data });
      
      // 根据不同的交互类型执行相应操作
      switch (action) {
        case 'metric_click':
          // KPI指标点击 - 可以触发详细分析
          message.info(`点击了指标: ${data.label}`);
          break;
        case 'chart_click':
          // 图表点击 - 可以显示详细数据
          message.info('图表交互功能');
          break;
        case 'insight_click':
          // 洞察点击 - 可以展开详细解释
          message.info(`查看洞察: ${data.insight.type}`);
          break;
        case 'recommendation_click':
          // 建议点击 - 可以展开实施方案
          message.info(`查看建议: ${data.recommendation.area}`);
          break;
        case 'download':
          // 下载操作
          message.success('下载完成');
          break;
        default:
          console.log('未处理的交互:', { componentType, action, data });
      }
    };

    // 导出整个报告
    const handleExportReport = () => {
      // 这里可以实现PDF导出功能
      message.info('报告导出功能开发中...');
    };
    
    // 检查是否为结构化报告数据
    const isStructuredReport = (data: any): boolean => {
      // 如果是字符串，尝试解析为JSON
      if (typeof data === 'string') {
        try {
          const parsed = JSON.parse(data);
          return parsed && 
            typeof parsed === 'object' && 
            (
              (Array.isArray(parsed.components) && parsed.components.length > 0) ||
              (Array.isArray(parsed) && parsed.length > 0 && parsed[0].type)
            );
        } catch {
          return false;
        }
      }
      
      // 如果已经是对象，直接检查
      return data && 
        typeof data === 'object' && 
        (
          (Array.isArray(data.components) && data.components.length > 0) ||
          (Array.isArray(data) && data.length > 0 && data[0].type)
        );
    };

    // 解析报告数据
    const parseReportData = (data: any) => {
      if (typeof data === 'string') {
        try {
          return JSON.parse(data);
        } catch {
          return data; // 如果解析失败，返回原字符串
        }
      }
      return data;
    };

    // 如果是结构化报告，使用新的渲染器
    if (isStructuredReport(reportToRender)) {
      const parsedData = parseReportData(reportToRender);
      return (
        <div style={{ marginTop: 16, marginLeft: 24 }}>
          {/* 报告头部信息 */}
          <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography.Title level={3} style={{ margin: 0 }}>
              {t('analysis:results.analysisReport')}
            </Typography.Title>
            {reportExecutionTime && (
              <Text type="secondary">
                {t('analysis:tools.executionTime', { 
                  time: typeof reportExecutionTime === 'number' 
                    ? reportExecutionTime.toFixed(3) 
                    : parseFloat(String(reportExecutionTime)).toFixed(3) 
                })}
              </Text>
            )}
          </div>
          
          {/* 使用新的动态报告渲染器 */}
          <DynamicReportRenderer
            reportData={parsedData}
            onExport={handleExportReport}
            onComponentInteraction={handleReportInteraction}
            className="analysis-structured-report"
          />
        </div>
      );
    }

    // 兼容旧的Markdown报告格式
    return (
      <Card 
        bordered={false} 
        style={{ marginTop: 16, marginLeft: 24 }}
        size="small"
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span>{t('analysis:results.analysisReport')}</span>
            {reportExecutionTime && <Text type="secondary">{t('analysis:tools.executionTime', { time: typeof reportExecutionTime === 'number' ? reportExecutionTime.toFixed(3) : parseFloat(String(reportExecutionTime)).toFixed(3) })}</Text>}
          </div>
        }
        className="report-card"
      >
        <ReactMarkdown
          children={reportToRender}
          remarkPlugins={[remarkGfm]}
          rehypePlugins={[rehypeRaw]}
          components={{
            // 自定义表格渲染
            table: ({...props}) => (
              <div style={{ overflowX: 'auto', marginBottom: '16px' }}>
                <table className="markdown-table" {...props} />
              </div>
            ),
            // 自定义表格头部渲染
            thead: ({...props}) => (
              <thead className="markdown-thead" {...props} />
            ),
            // 自定义表格行渲染
            tr: ({...props}) => (
              <tr className="markdown-tr" {...props} />
            ),
            // 自定义表格单元格渲染
            td: ({...props}) => (
              <td className="markdown-td" {...props} />
            ),
            // 自定义表格头部单元格渲染
            th: ({...props}) => (
              <th className="markdown-th" {...props} />
            ),
            // 自定义h4标题渲染 - 图表标题
            h4: ({...props}) => (
              <h4 style={{ 
                margin: '0 0 12px 0', 
                fontSize: '16px', 
                fontWeight: 600, 
                color: '#262626',
                textAlign: 'center'
              }} {...props} />
            ),
            // 🔥 自定义div渲染 - 处理图表容器
            div: ({className, ...props}: any) => {
              // 检查是否是图表占位符容器（内层div）
              if (className === 'chart-placeholder' && props['data-chart-config']) {
                try {
                  const chartConfig = JSON.parse(props['data-chart-config']);
                  console.log('🔥 渲染报告中的图表占位符:', chartConfig);

                  return (
                    <div style={{ height: '400px', width: '100%', marginTop: '8px' }}>
                      <ReactECharts
                        option={reviveEchartsFunctions(chartConfig)}
                        style={{ height: '100%', width: '100%' }}
                        opts={{ renderer: 'canvas' }}
                      />
                    </div>
                  );
                } catch (error) {
                  console.error('🔥 解析图表配置失败:', error);
                  return <div {...props} className={className} />;
                }
              }

              // 检查是否是图表容器（外层div）
              if (className === 'chart-container') {
                console.log('🔥 渲染报告中的图表容器:', props);
                return (
                  <div 
                    {...props} 
                    className={className}
                    style={{ 
                      margin: '20px 0', 
                      padding: '16px', 
                      border: '1px solid #f0f0f0', 
                      borderRadius: '8px',
                      backgroundColor: '#fafafa'
                    }}
                  />
                );
              }

              // 兼容旧版本：检查是否是图表容器且有配置
              if (className === 'chart-container' && props['data-chart-config']) {
                try {
                  const chartConfig = JSON.parse(props['data-chart-config']);
                  console.log('🔥 渲染报告中的图表（兼容模式）:', chartConfig);

                  return (
                    <div style={{ margin: '20px 0', padding: '16px', border: '1px solid #f0f0f0', borderRadius: '8px' }}>
                      <div style={{ height: '400px', width: '100%' }}>
                        <ReactECharts
                          option={reviveEchartsFunctions(chartConfig)}
                          style={{ height: '100%', width: '100%' }}
                          opts={{ renderer: 'canvas' }}
                        />
                      </div>
                      <div style={{ marginTop: '8px', fontSize: '12px', color: '#666', textAlign: 'center' }}>
                        <p><strong>{t('analysis:tools.chartType')}</strong>: echarts</p>
                        <p><strong>{t('analysis:tools.dataSource')}</strong>: {t('analysis:tools.intelligentChartGeneration')}</p>
                        <p>交互式图表将在前端界面中显示</p>
                      </div>
                    </div>
                  );
                } catch (error) {
                  console.error('🔥 解析图表配置失败:', error);
                  return <div {...props} className={className} />;
                }
              }

              // 普通div正常渲染
              return <div {...props} className={className} />;
            },
            
            // 🔥 自定义代码块渲染 - 处理chart类型
            code: ({node, inline, className, children, ...props}: any) => {
              const match = /language-(\w+)/.exec(className || '');
              const language = match ? match[1] : '';
              
              // 处理chart代码块
              if (language === 'chart') {
                try {
                  const chartDataStr = String(children).replace(/\n$/, '');
                  const chartData = JSON.parse(chartDataStr);
                  
                  console.log('🔥 渲染报告中的chart代码块:', chartData);
                  
                  // 检查是否是多图表结构
                  const isMultiChart = chartData && chartData.data && (
                    chartData.data.chart_type === 'multi_charts' ||
                    chartData.data.display_format === 'multi_chart' ||
                    (chartData.data.total_charts && chartData.data.total_charts > 1)
                  ) && chartData.data.charts;
                  
                  if (isMultiChart) {
                    // 渲染多图表
                    return (
                      <div style={{ margin: '20px 0' }}>
                        {chartData.data.charts.map((chart: any, index: number) => {
                          const chartConfig = chart.config || chart.chart_config; // 支持新旧格式
                          const chartTitle = chart.title || `图表 ${index + 1}`;

                          if (!chartConfig || !chartConfig.series || !Array.isArray(chartConfig.series) || chartConfig.series.length === 0) {
                            return (
                              <div key={`chart-error-${index}`} style={{ margin: '10px 0', padding: '16px', border: '1px solid #ff7875', borderRadius: '8px', backgroundColor: '#fff2f0' }}>
                                <Text type="danger">{t('analysis:tools.chartConfigInvalidWithIndex', { index: index + 1 })}</Text>
                              </div>
                            );
                          }
                          
                          return (
                            <div key={`chart-${index}`} style={{ margin: '20px 0', padding: '16px', border: '1px solid #f0f0f0', borderRadius: '8px', backgroundColor: '#fafafa' }}>
                              <h4 style={{ margin: '0 0 16px 0', fontSize: '16px', fontWeight: 600, color: '#262626', textAlign: 'center' }}>
                                {chartTitle}
                              </h4>
                              <div style={{ height: '400px', width: '100%' }}>
                                <ReactECharts
                                  option={reviveEchartsFunctions(chartConfig)}
                                  style={{ height: '100%', width: '100%' }}
                                  opts={{ renderer: 'canvas' }}
                                  notMerge={true}
                                  lazyUpdate={true}
                                />
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    );
                  } else if (chartData && chartData.data && (chartData.data.chart_config || (chartData.data.total_charts === 1 && chartData.data.charts?.[0]?.config))) {
                    // 渲染单图表 - 支持新旧格式
                    const chartConfig = chartData.data.chart_config || chartData.data.charts?.[0]?.config;

                    if (!chartConfig.series || !Array.isArray(chartConfig.series) || chartConfig.series.length === 0) {
                      return (
                        <div style={{ margin: '20px 0', padding: '16px', border: '1px solid #ff7875', borderRadius: '8px', backgroundColor: '#fff2f0' }}>
                          <Text type="danger">{t('analysis:tools.chartConfigInvalid')}</Text>
                        </div>
                      );
                    }
                    
                    return (
                      <div style={{ margin: '20px 0', padding: '16px', border: '1px solid #f0f0f0', borderRadius: '8px', backgroundColor: '#fafafa' }}>
                        <div style={{ height: '400px', width: '100%' }}>
                          <ReactECharts
                            option={reviveEchartsFunctions(chartConfig)}
                            style={{ height: '100%', width: '100%' }}
                            opts={{ renderer: 'canvas' }}
                            notMerge={true}
                            lazyUpdate={true}
                          />
                        </div>
                      </div>
                    );
                  } else {
                    return (
                      <div style={{ margin: '20px 0', padding: '16px', border: '1px solid #ff7875', borderRadius: '8px', backgroundColor: '#fff2f0' }}>
                        <Text type="danger">{t('analysis:errors.chartDataFormatIncorrect')}</Text>
                        <pre style={{ marginTop: '8px', fontSize: '12px', background: '#f5f5f5', padding: '8px', borderRadius: '4px' }}>
                          {chartDataStr}
                        </pre>
                      </div>
                    );
                  }
                } catch (error) {
                  console.error('🔥 解析chart代码块失败:', error);
                  return (
                    <div style={{ margin: '20px 0', padding: '16px', border: '1px solid #ff7875', borderRadius: '8px', backgroundColor: '#fff2f0' }}>
                      <Text type="danger">{t('analysis:errors.parseChartDataFailed', { error: String(error) })}</Text>
                      <pre style={{ marginTop: '8px', fontSize: '12px', background: '#f5f5f5', padding: '8px', borderRadius: '4px' }}>
                        {String(children)}
                      </pre>
                    </div>
                  );
                }
              }

              // 处理table代码块
              if (language === 'table') {
                try {
                  const tableDataStr = String(children).replace(/\n$/, '');
                  const tableData = JSON.parse(tableDataStr);

                  console.log('🔥 渲染报告中的table代码块:', tableData);

                  // 检查表格数据结构（支持MoE和传统格式）
                  let columns, dataSource, title = '数据表格';

                  if (tableData?.data?.table_config) {
                    // MoE生成的表格数据结构
                    const tableConfig = tableData.data.table_config;
                    columns = tableConfig.columns || [];
                    dataSource = tableConfig.dataSource || [];
                    title = tableData.data.title || tableConfig.title || '数据表格';
                    console.log('🔥 使用MoE表格数据结构');
                  } else if (tableData?.data?.columns && tableData?.data?.dataSource) {
                    // 传统表格数据结构
                    columns = tableData.data.columns;
                    dataSource = tableData.data.dataSource;
                    title = tableData.data.title || '数据表格';
                    console.log('🔥 使用传统表格数据结构');
                  }

                  if (columns && dataSource) {
                    // 转换为Antd Table格式
                    const antdColumns = columns.map((col: any, index: number) => {
                      if (typeof col === 'string') {
                        return {
                          title: col,
                          dataIndex: col,
                          key: col,
                          ellipsis: { showTitle: false },
                          render: (text: any) => (
                            <Typography.Paragraph
                              ellipsis={{ rows: 3, expandable: true, symbol: '更多' }}
                              style={{ marginBottom: 0 }}
                            >
                              {text === null || text === undefined ? '' : String(text)}
                            </Typography.Paragraph>
                          )
                        };
                      } else {
                        const dataIndex = col.dataIndex || col.key || `column_${index}`;
                        let columnConfig: any = {
                          title: col.title || col.dataIndex || `column_${index}`,
                          dataIndex: dataIndex,
                          key: col.key || col.dataIndex || `column_${index}`,
                          ellipsis: { showTitle: false },
                          ...col
                        };

                        // 处理字符串格式的render函数
                        if (columnConfig.render && typeof columnConfig.render === 'string') {
                          try {
                            // 处理不同格式的函数字符串
                            let renderFunction;
                            const renderStr = columnConfig.render;

                            if (renderStr.includes('=>')) {
                              // 箭头函数格式
                              const simpleArrowMatch = renderStr.match(/^(\w+)\s*=>\s*(.+)$/);
                              if (simpleArrowMatch) {
                                const param = simpleArrowMatch[1];
                                const body = simpleArrowMatch[2];
                                renderFunction = new Function(param, `return ${body}`);
                              } else {
                                renderFunction = eval(`(${renderStr})`);
                              }
                            } else {
                              renderFunction = eval(`(${renderStr})`);
                            }

                            columnConfig.render = renderFunction;
                          } catch (error) {
                            console.warn('转换render函数失败:', error, '原始函数:', columnConfig.render);
                            columnConfig.render = (text: any) => (
                              <Typography.Paragraph
                                ellipsis={{ rows: 3, expandable: true, symbol: '更多' }}
                                style={{ marginBottom: 0 }}
                              >
                                {text === null || text === undefined ? '' : String(text)}
                              </Typography.Paragraph>
                            );
                          }
                        }

                        // 🎨 新增：处理 anomaly_detection 配置（报告中的表格）
                        if (columnConfig.anomaly_detection && columnConfig.anomaly_detection.enabled) {
                          const anomalyConfig = columnConfig.anomaly_detection;
                          const rules = anomalyConfig.rules || [];
                          const highlightStyle = anomalyConfig.highlight_style || {};

                          console.log('报告表格检测到异常检测配置:', anomalyConfig);

                          // 创建异常检测render函数
                          columnConfig.render = (value: any, record: any, index: number) => {
                            // 检测异常类型
                            let anomalyType = null;
                            let displayValue = value;

                            // 数值转换（处理字符串格式的数值）
                            const numericValue = typeof value === 'string' ? parseFloat(value) : value;
                            const isNumeric = !isNaN(numericValue) && isFinite(numericValue);

                            // 1. 缺失值检测
                            if (rules.includes('null_check') && (value === null || value === undefined || value === '')) {
                              anomalyType = 'missing';
                              displayValue = 'N/A';
                            }
                            // 2. 负值检测
                            else if (rules.includes('negative_check') && isNumeric && numericValue < 0) {
                              anomalyType = 'critical';
                            }
                            // 3. 零值检测
                            else if (rules.includes('zero_check') && isNumeric && numericValue === 0) {
                              anomalyType = 'warning';
                            }
                            // 4. 离群值检测（简单实现）
                            else if (rules.includes('outlier_check') && isNumeric) {
                              // 这里可以实现更复杂的离群值检测逻辑
                              // 暂时跳过，因为需要整个数据集的统计信息
                            }

                            // 应用样式和图标
                            if (anomalyType && highlightStyle[anomalyType]) {
                              const style = highlightStyle[anomalyType];
                              const icon = style.icon || '';

                              return (
                                <span
                                  style={{
                                    backgroundColor: style.backgroundColor,
                                    color: style.color,
                                    padding: '2px 6px',
                                    borderRadius: '4px',
                                    fontSize: '12px',
                                    fontWeight: 'bold'
                                  }}
                                  title={`异常检测: ${anomalyType}`}
                                >
                                  {icon && <span style={{ marginRight: '4px' }}>{icon}</span>}
                                  {displayValue}
                                </span>
                              );
                            }

                            // 正常值：检查是否需要正向突出显示
                            if (isNumeric && numericValue > 0 && highlightStyle.positive) {
                              const style = highlightStyle.positive;
                              const icon = style.icon || '';

                              return (
                                <span
                                  style={{
                                    backgroundColor: style.backgroundColor,
                                    color: style.color,
                                    padding: '2px 6px',
                                    borderRadius: '4px',
                                    fontSize: '12px',
                                    fontWeight: 'bold'
                                  }}
                                  title="正常值"
                                >
                                  {icon && <span style={{ marginRight: '4px' }}>{icon}</span>}
                                  {value}
                                </span>
                              );
                            }

                            // 默认显示
                            return value;
                          };

                          // 移除anomaly_detection配置，避免传递给Antd Table
                          delete columnConfig.anomaly_detection;
                        }

                        // 保留排序功能（报告中也支持交互） - 支持多种格式
                        if (columnConfig.sorter) {
                          if (columnConfig.sorter === true) {
                            // 当sorter为true时，根据数据类型自动生成排序函数
                            columnConfig.sorter = (a: any, b: any) => {
                              const aVal = a[columnConfig.dataIndex];
                              const bVal = b[columnConfig.dataIndex];
                              
                              // 处理null/undefined值
                              if (aVal == null && bVal == null) return 0;
                              if (aVal == null) return -1;
                              if (bVal == null) return 1;
                              
                              // 尝试数值比较
                              const aNum = Number(aVal);
                              const bNum = Number(bVal);
                              if (!isNaN(aNum) && !isNaN(bNum)) {
                                return aNum - bNum;
                              }
                              
                              // 字符串比较
                              return String(aVal).localeCompare(String(bVal));
                            };
                            console.log('报告中自动生成排序函数:', columnConfig.dataIndex);
                          } else if (typeof columnConfig.sorter === 'object' && columnConfig.sorter.compare) {
                            if (typeof columnConfig.sorter.compare === 'string') {
                              try {
                                // 转换字符串格式的比较函数
                                const compareStr = columnConfig.sorter.compare;
                                if (compareStr.includes('localeCompare')) {
                                  // 字符串比较
                                  columnConfig.sorter = (a: any, b: any) => {
                                    const aVal = a[columnConfig.dataIndex] || '';
                                    const bVal = b[columnConfig.dataIndex] || '';
                                    return aVal.toString().localeCompare(bVal.toString());
                                  };
                                } else if (compareStr.includes(' - ')) {
                                  // 数值比较
                                  columnConfig.sorter = (a: any, b: any) => {
                                    const aVal = a[columnConfig.dataIndex] || 0;
                                    const bVal = b[columnConfig.dataIndex] || 0;
                                    return aVal - bVal;
                                  };
                                } else {
                                  // 尝试eval转换
                                  columnConfig.sorter = eval(`(a, b) => ${compareStr}`);
                                }
                                console.log('报告中sorter函数转换成功');
                              } catch (error) {
                                console.warn('转换sorter函数失败:', error);
                                // 转换失败时使用默认排序
                                columnConfig.sorter = (a: any, b: any) => {
                                  const aVal = a[columnConfig.dataIndex];
                                  const bVal = b[columnConfig.dataIndex];
                                  if (aVal == null && bVal == null) return 0;
                                  if (aVal == null) return -1;
                                  if (bVal == null) return 1;
                                  return String(aVal).localeCompare(String(bVal));
                                };
                              }
                            }
                          }
                        }

                        return columnConfig;
                      }
                    });

                    // 确保数据源中每行都有key字段
                    const antdDataSource = dataSource.map((row: any, index: number) => ({
                      key: row.key || index,
                      ...row
                    }));

                    // 使用MoE原始配置或默认配置
                    const originalTableConfig = tableData?.data?.table_config || {};

                    // 分页配置：优先使用原始配置，但需要处理函数字符串
                    let paginationConfig;
                    if (originalTableConfig.pagination !== undefined) {
                      paginationConfig = { ...originalTableConfig.pagination };
                      
                      // 处理pagination中的函数字符串，特别是showTotal
                      if (paginationConfig.showTotal && typeof paginationConfig.showTotal === 'string') {
                        try {
                          console.log('🔥 转换报告中的 pagination.showTotal 函数:', paginationConfig.showTotal);
                          
                          // 处理箭头函数格式：total => ... 或 (total) => ...
                          const simpleArrowMatch = paginationConfig.showTotal.match(/^(\w+)\s*=>\s*(.+)$/);
                          const complexArrowMatch = paginationConfig.showTotal.match(/^\(([^)]*)\)\s*=>\s*(.+)$/);
                          
                          if (simpleArrowMatch) {
                            // 简单箭头函数：total => expression
                            const param = simpleArrowMatch[1];
                            const body = simpleArrowMatch[2];
                            paginationConfig.showTotal = new Function(param, `return ${body}`);
                            console.log('🔥 报告中 pagination.showTotal 函数转换成功（简单箭头函数）');
                          } else if (complexArrowMatch) {
                            // 复杂箭头函数：(total, range) => expression
                            const params = complexArrowMatch[1];
                            const body = complexArrowMatch[2];
                            paginationConfig.showTotal = new Function(...params.split(',').map((p: string) => p.trim()), `return ${body}`);
                            console.log('🔥 报告中 pagination.showTotal 函数转换成功（复杂箭头函数）');
                          } else {
                            // 尝试直接eval
                            paginationConfig.showTotal = eval(`(${paginationConfig.showTotal})`);
                            console.log('🔥 报告中 pagination.showTotal 函数转换成功（eval）');
                          }
                        } catch (error) {
                          console.warn('🔥 转换报告中 pagination.showTotal 函数失败:', error);
                          // 使用默认的showTotal函数
                          paginationConfig.showTotal = (total: number, range: [number, number]) =>
                            `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`;
                        }
                      }
                    } else {
                      paginationConfig = antdDataSource.length > 10 ? {
                        pageSize: 10,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total: number, range: [number, number]) =>
                          `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
                        pageSizeOptions: ['10', '20', '50']
                      } : false;
                    }

                    return (
                      <div style={{ margin: '20px 0', padding: '16px', border: '1px solid #f0f0f0', borderRadius: '8px', backgroundColor: '#fafafa' }}>
                        <h4 style={{ margin: '0 0 16px 0', fontSize: '16px', fontWeight: 600, color: '#262626', textAlign: 'center' }}>
                          {title}
                        </h4>
                        <Table
                          columns={antdColumns}
                          dataSource={antdDataSource}
                          size={originalTableConfig.size || "small"}
                          pagination={paginationConfig}
                          bordered={originalTableConfig.bordered !== false}
                          scroll={originalTableConfig.scroll || { x: true }}
                          rowKey="key"
                        />
                      </div>
                    );
                  } else {
                    return (
                      <div style={{ margin: '20px 0', padding: '16px', border: '1px solid #ff7875', borderRadius: '8px', backgroundColor: '#fff2f0' }}>
                        <Text type="danger">表格数据格式不正确</Text>
                        <pre style={{ marginTop: '8px', fontSize: '12px', background: '#f5f5f5', padding: '8px', borderRadius: '4px' }}>
                          {tableDataStr}
                        </pre>
                      </div>
                    );
                  }
                } catch (error) {
                  console.error('🔥 解析table代码块失败:', error);
                  return (
                    <div style={{ margin: '20px 0', padding: '16px', border: '1px solid #ff7875', borderRadius: '8px', backgroundColor: '#fff2f0' }}>
                      <Text type="danger">解析表格数据失败: {String(error)}</Text>
                      <pre style={{ marginTop: '8px', fontSize: '12px', background: '#f5f5f5', padding: '8px', borderRadius: '4px' }}>
                        {String(children)}
                      </pre>
                    </div>
                  );
                }
              }

              // 处理其他代码块
              if (!inline && language) {
                return (
                  <pre style={{ 
                    background: '#f5f5f5', 
                    padding: '12px', 
                    borderRadius: '6px',
                    border: '1px solid #d9d9d9',
                    overflow: 'auto',
                    margin: '16px 0'
                  }}>
                    <code className={className} {...props}>
                      {children}
                    </code>
                  </pre>
                );
              }
              
              // 内联代码
              return (
                <code 
                  className={className} 
                  style={{
                    backgroundColor: '#f6f8fa',
                    padding: '2px 4px',
                    borderRadius: '3px',
                    fontSize: '0.9em',
                    color: '#d73a49'
                  }}
                  {...props}
                >
                  {children}
                </code>
              );
            }
          }}
        />
        

      </Card>
    );
  };

  // 合并分析步骤和打断信息，按时间顺序排列
  const mergeStepsWithInterrupts = (steps: any[], interruptInfo?: any) => {
    if (!interruptInfo || !interruptInfo.has_interrupts || !interruptInfo.interruptions) {
      return steps.map(step => ({ ...step, type: 'step' }));
    }

    const interruptions = interruptInfo.interruptions;
    const allItems: any[] = [];

    // 将步骤和打断信息合并
    steps.forEach(step => {
      allItems.push({ ...step, type: 'step', timestamp: new Date(step.time || '').getTime() });
    });

    interruptions.forEach((interrupt: any) => {
      allItems.push({ 
        ...interrupt, 
        type: 'interrupt', 
        timestamp: new Date(interrupt.interrupt_timestamp).getTime(),
        id: `interrupt_${interrupt.interrupt_id}`,
        name: '分析被打断'
      });
    });

    // 按时间戳排序
    allItems.sort((a, b) => a.timestamp - b.timestamp);

    return allItems;
  };

  // 渲染当前分析内容
  const renderCurrentAnalysis = () => {
    if (!conversationState || conversationState.rounds.length === 0) {
      return null;
    }

    return (
      <div className="analysis-rounds-container">
        {conversationState.rounds.map((roundData, roundIndex) => {
          const isCurrentRound = roundIndex === conversationState.currentRound;
          // 🔧 修复：增强isActiveRound判断逻辑，解决第一轮分析时loading不显示的问题
          // 原因：React状态更新异步导致第一轮分析时conversationState.currentRound可能还未正确更新
          // 解决：当streaming为true且是最新轮次时，也认为是活跃轮次（适用于第一轮分析场景）
          const isLatestRound = roundIndex === conversationState.rounds.length - 1; // 是否是最新轮次
          const isActiveRound = (isCurrentRound && streaming) || (streaming && isLatestRound && conversationState.rounds.length === 1);
          
          // 获取该轮次的分析步骤和结果
          // 数据来源判断逻辑：
          // 1. 如果是当前轮次且正在进行分析，使用全局状态
          // 2. 如果是当前轮次且刚完成分析，优先使用全局状态（如果有数据）
          // 3. 其他情况使用保存的状态
          // 🔧 修复：同样需要考虑第一轮分析的特殊情况
          const shouldUseGlobalState = (isCurrentRound && streaming) || (streaming && isLatestRound && conversationState.rounds.length === 1);
          
          // 🔧 修复：优化数据来源逻辑，确保历史轮次数据不会丢失
          let roundSteps: any[] = [];
          let roundMcpResults: Record<string, any> = {};
          let roundFinalReport: string = '';
          
          if (shouldUseGlobalState) {
            // 当前轮次且正在分析：使用全局状态
            roundSteps = analysisSteps;
            roundMcpResults = mcpResults;
            roundFinalReport = finalReport;
          } else if (isCurrentRound && !streaming && (analysisSteps.length > 0 || Object.keys(mcpResults).length > 0 || finalReport)) {
            // 当前轮次且刚完成分析：优先使用全局状态（如果有数据）
            roundSteps = analysisSteps;
            roundMcpResults = mcpResults;
            roundFinalReport = finalReport;
          } else {
            // 历史轮次或当前轮次无全局数据：使用保存的状态
            roundSteps = roundData.analysisSteps || [];
            roundMcpResults = roundData.mcpResults || {};
            roundFinalReport = roundData.finalReport || '';
            
            // 如果保存的mcpResults为空，尝试从toolResults重建
            if (Object.keys(roundMcpResults).length === 0 && roundData.toolResults && Array.isArray(roundData.toolResults)) {
              const rebuiltMcpResults: Record<string, any> = {};
              roundData.toolResults.forEach((execution: any) => {
                if (execution.step_id && execution.result) {
                  rebuiltMcpResults[execution.step_id] = execution.result;
                }
              });
              roundMcpResults = { ...roundMcpResults, ...rebuiltMcpResults };
            }
          }
            
          const roundStatus = shouldUseGlobalState ? 
            (streaming ? 'running' : 'running') : 
            (isCurrentRound && streamComplete) ? 'completed' :
            (roundData.status || 'completed');
          
          console.log(`🔍 轮次 ${roundIndex + 1} 数据来源:`, {
            shouldUseGlobalState,
            isActiveRound,
            streaming,
            streamComplete,
            roundStepsLength: roundSteps.length,
            roundMcpResultsKeys: Object.keys(roundMcpResults),
            roundMcpResultsData: roundMcpResults,
            roundFinalReportLength: roundFinalReport.length,
            roundStatus,
            savedStepsLength: roundData.analysisSteps?.length || 0,
            savedMcpResultsKeys: Object.keys(roundData.mcpResults || {}),
            savedToolResultsLength: roundData.toolResults?.length || 0,
            savedFinalReportLength: roundData.finalReport?.length || 0,
            globalFinalReportLength: finalReport.length,
            globalAnalysisStepsLength: analysisSteps.length
          });
          
          // 后端已经处理好reasoning，前端直接显示步骤名称即可
          
          // 确保roundMcpResults包含所有必要的工具结果
          if (!shouldUseGlobalState && !(isCurrentRound && Object.keys(mcpResults).length > 0)) {
            // 如果有保存的MCP结果，也合并到roundMcpResults中
            if (roundData.mcpResults && typeof roundData.mcpResults === 'object') {
              roundMcpResults = { ...roundMcpResults, ...roundData.mcpResults };
            }
          }
          
          console.log(`🔧 轮次 ${roundIndex + 1} 最终MCP结果:`, {
            roundMcpResultsKeys: Object.keys(roundMcpResults),
            roundMcpResultsCount: Object.keys(roundMcpResults).length,
            roundMcpResultsData: roundMcpResults
          });
          
          console.log(`📋 轮次 ${roundIndex + 1} 步骤详情:`, {
            roundStepsCount: roundSteps.length,
            roundSteps: roundSteps.map(step => ({
              id: step.id,
              name: step.name,
              tool_name: step.tool_name,
              hasParameters: !!step.parameters,
              parametersKeys: step.parameters ? Object.keys(step.parameters) : [],
              hasQuestions: !!(step.parameters?.questions),
              questionsCount: step.parameters?.questions?.length || 0,
              status: step.status
            }))
          });
          
          console.log(`🔍 轮次 ${roundIndex + 1} 状态检查:`, {
            roundIndex,
            isCurrentRound,
            isActiveRound,
            streaming,
            timelineLoading,
            roundStepsLength: roundSteps.length,
            shouldShowTimeline: roundSteps.length > 0 || isActiveRound,
            shouldShowLoading: isActiveRound && timelineLoading,
            // 🔧 新增调试信息：帮助诊断第一轮分析loading问题
            conversationCurrentRound: conversationState.currentRound,
            roundsLength: conversationState.rounds.length,
            isLatestRound,
            isFirstRoundSpecialCase: streaming && isLatestRound && conversationState.rounds.length === 1,
            // 详细的isActiveRound计算过程
            isCurrentRoundCalc: roundIndex === conversationState.currentRound,
            streamingAndLatestAndFirst: streaming && isLatestRound && conversationState.rounds.length === 1,
            // 打断步骤直接包含在roundSteps中
            interruptStepsCount: roundSteps.filter(step => step.isInterrupt).length
          });

          // 不再需要合并，直接使用roundSteps（包含打断步骤）
          console.log(`🔀 轮次 ${roundIndex + 1} 步骤（包含打断）:`, {
            roundStepsCount: roundSteps.length,
            interruptStepsCount: roundSteps.filter(step => step.isInterrupt).length,
            roundSteps: roundSteps.map(step => ({
              id: step.id,
              name: step.name,
              isInterrupt: step.isInterrupt
            }))
          });
          
          return (
            <div key={roundData.roundId} className="round-analysis" style={{ marginBottom: 32 }}>
              {/* 用户问题 - 右侧对话气泡 */}
              <div style={{ display: 'flex', justifyContent: 'flex-end', marginBottom: 16 }}>
                <div style={{
                  maxWidth: '70%',
                  background: '#1890ff',
                  color: 'white',
                  padding: '12px 16px',
                  borderRadius: '18px 18px 4px 18px',
                  position: 'relative'
                }}>
                  <div style={{ fontSize: '14px', lineHeight: '1.5' }}>
                    {roundData.query}
                  </div>
                  <div style={{ 
                    fontSize: '11px', 
                    opacity: 0.8, 
                    marginTop: 4,
                    textAlign: 'right',
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center'
                  }}>
                    <div>
                      {t('analysis:multiRound.round', { round: roundIndex + 1, defaultValue: `第 ${roundIndex + 1} 轮提问` })}
                    </div>
                  </div>
                </div>
              </div>

              {/* AI分析回复 - 左侧 */}
              <div style={{ display: 'flex', justifyContent: 'flex-start' }}>
                <div style={{ maxWidth: '85%', width: '100%' }}>
                  <Card style={{ 
                    border: isActiveRound ? '1px solid #d6e4ff' : '1px solid #f0f0f0', 
                    borderRadius: '18px 18px 18px 4px',
                    background: isActiveRound ? '#fff' : '#fafafa',
                    boxShadow: '0 2px 8px rgba(0,0,0,0.06)'
                  }}>
                    {/* 状态标识 */}
                    <div style={{ 
                      marginBottom: 12,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 8
                    }}>
                      <div style={{
                        width: 8,
                        height: 8,
                        borderRadius: '50%',
                        background: isActiveRound ? '#1890ff' : '#52c41a'
                      }} />
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {isActiveRound ? t('analysis:multiRound.analysisRunning') : t('analysis:multiRound.analysisComplete')}
                      </Text>
                    </div>

                    {/* 打断信息将嵌入到时间线中，不再单独显示 */}
                    
                    {/* 分析时间线 */}
                    {(roundSteps.length > 0 || isActiveRound) ? (
                      <div ref={isActiveRound ? timelineRef : undefined} style={{ marginBottom: 20 }}>
                        <Timeline>
                          {roundSteps
                            .filter(step => {
                              // 过滤掉"等待用户提供澄清信息"步骤，避免重复显示
                              if (step.name === '等待用户提供澄清信息' || step.tool_name === '用户澄清回复') {
                                return false;
                              }
                              return true;
                            })
                            .map((step, index) => {
                              // 如果是打断步骤，渲染打断项
                              if (step.isInterrupt) {
                                return (
                                  <Timeline.Item 
                                    key={step.id}
                                    dot={<ExclamationCircleOutlined style={{ color: '#fa8c16', fontSize: '16px' }} />}
                                  >
                                    <InlineInterruptItem interrupt={step.interruptData} />
                                  </Timeline.Item>
                                );
                              }
                              
                              // 如果是分析步骤，渲染原有的步骤逻辑
                              return (
                            <Timeline.Item 
                              key={step.id}
                              dot={
                                // 特殊处理：意图确认请求步骤根据状态显示不同图标
                                step.id === 'intent_confirmation_request' ? (
                                  step.status === 'error' ? (
                                    <CloseCircleOutlined style={{ color: '#f5222d' }} />
                                  ) : (
                                    <CheckCircleOutlined style={{ color: '#52c41a' }} />
                                  )
                                ) : step.status === 'finish' || step.status === 'error' ? (
                                    <CheckCircleOutlined style={{ color: '#52c41a' }} />
                                ) : step.status === 'process' ? (
                                  <LoadingOutlined style={{ fontSize: '16px' }} spin />
                                ) : step.status === 'wait' ? (
                                  <QuestionCircleOutlined style={{ color: '#fa8c16', fontSize: '16px' }} />
                                ) : (
                                  <CloseCircleOutlined style={{ color: '#f5222d' }} />
                                )
                              }
                            >
                              <Text strong={step.status === 'process' || (step.status === 'wait' && step.id !== 'intent_confirmation_request')}>
                                {/* 特殊处理：意图确认请求步骤根据状态显示不同内容 */}
                                {step.id === 'intent_confirmation_request' ? 
                                  (step.status === 'error' ? renderStepName(step.name) : renderStepName(t('analysis:intentConfirmation.completed', { defaultValue: '意图确认完成' }))) : 
                                  renderStepName(step.name)}
                                {step.status === 'process' && !step.progressInfo && <Text type="secondary"> ({t('analysis:steps.executing')})</Text>}
                                {step.status === 'wait' && step.id !== 'intent_confirmation_request' && <Text style={{ color: '#fa8c16' }}> ({t('analysis:steps.waitingForUserInput')})</Text>}
                                
                                {/* 🚀 新增：图表生成进度显示 */}
                                {step.progressInfo && step.progressInfo.type === 'chart_generation' && (
                                  <div style={{ marginTop: 8 }}>
                                    <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                                      <Text type="secondary" style={{ fontSize: '12px' }}>
                                        {step.progressInfo.message}
                                      </Text>
                                      {step.progressInfo.hasError && (
                                        <ExclamationCircleOutlined style={{ color: '#ff4d4f', fontSize: '12px' }} />
                                      )}
                                    </div>
                                    
                                    {step.progressInfo.total > 0 && (
                                      <div style={{ marginTop: 4 }}>
                                        <Progress
                                          percent={Math.round((step.progressInfo.current / step.progressInfo.total) * 100)}
                                          size="small"
                                          status={step.progressInfo.hasError ? 'exception' : 
                                                 step.progressInfo.completed ? 'success' : 'active'}
                                          format={() => `${step.progressInfo.current}/${step.progressInfo.total}`}
                                        />
                                      </div>
                                    )}
                                  </div>
                                )}


                                {step.id === 'context_loaded' && (
                                  <div style={{ marginTop: 8 }}>
                                    <Text type="secondary" style={{ fontSize: '12px' }}>
                                      {t('analysis:steps.contextLoaded', { defaultValue: '已加载历史对话上下文' })}
                                    </Text>
                                  </div>
                                )}
                              </Text>
                              
                              
                              
                              {/* @fixed_implementation_start */}
                              {/* 实现标识: 意图确认步骤渲染顺序优化 */}
                              {/* 功能描述: 该实现已经过确认和优化，是处理意图确认步骤显示顺序的推荐方案。 */}
                              {/* 复用指引: 如需实现类似功能，请优先考虑复用和扩展此代码，以避免不必要的重复实现和维护成本。 */}
                              {/* 状态: 已确认 (CONFIRMED_IMPLEMENTATION) */}
                              {/* @fixed_implementation_end */}
                              
                              {/* 渲染步骤结果 */}
                              {(() => {
                                const shouldRender = step.hasResult ||
                                                   (step.tool_name === '智能交互' && step.parameters?.questions) ||
                                                   step.status === 'error'; // 添加错误步骤的渲染条件
                                // 移除意图确认工具的强制渲染条件，因为已经有专门的格式化显示逻辑
                                console.log('步骤渲染条件检查:', {
                                  stepId: step.id,
                                  stepName: step.name,
                                  toolName: step.tool_name,
                                  hasResult: step.hasResult,
                                  hasParameters: !!step.parameters,
                                  hasQuestions: !!(step.parameters?.questions),
                                  questionsData: step.parameters?.questions,
                                  status: step.status,
                                  shouldRender: shouldRender
                                });
                                if (shouldRender) {
                                  console.log('🎯 即将调用renderStepResult:', step.id, 'status:', step.status);
                                  return renderStepResult(step, roundMcpResults);
                                }
                                return null;
                              })()}

                              {/* SSE期间的意图确认交互 - 保留完整的交互功能 */}
                              {step.id === 'intent_confirmation' && step.status === 'process' && showIntentConfirmation && intentConfirmation && (
                                <div style={{ marginTop: 16, marginLeft: 24 }}>
                                  <Card
                                    bordered={false}
                                    style={{
                                      background: '#ffffff',
                                      border: '1.5px solid #e2e8f0',
                                      borderRadius: 16,
                                      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
                                    }}
                                    size="small"
                                  >
                                    {/* 自定义标题区域 */}
                                    <div style={{
                                      marginBottom: '20px',
                                      paddingBottom: '16px',
                                      borderBottom: '1px solid #f1f5f9'
                                    }}>
                                      <div style={{ 
                                        display: 'flex', 
                                        alignItems: 'center',
                                        marginBottom: '8px'
                                      }}>
                                        <div style={{
                                          background: 'linear-gradient(135deg, #3b82f6 10%, #1e40af 100%)',
                                          borderRadius: '12px',
                                          width: '32px',
                                          height: '32px',
                                          display: 'flex',
                                          alignItems: 'center',
                                          justifyContent: 'center',
                                          marginRight: '12px',
                                          fontSize: '16px'
                                        }}>
                                          💭
                                        </div>
                                        <div>
                                          <Text style={{ 
                                            fontSize: '16px',
                                            fontWeight: '600',
                                            color: '#1f2937'
                                          }}>
                                            {t('analysis:intentConfirmation.title')}
                                          </Text>
                                        </div>
                                      </div>
                                      <Text style={{ 
                                        fontSize: '13px',
                                        color: '#6b7280',
                                        lineHeight: '1.5'
                                      }}>
                                        {t('analysis:intentConfirmation.description')}
                                      </Text>
                                    </div>
                                    {/* 显示原始查询 */}
                                    {intentConfirmation.user_query && (
                                      <div style={{ marginBottom: 20 }}>
                                        <Text style={{ color: '#374151', fontSize: '13px', fontWeight: '500', display: 'block', marginBottom: '8px' }}>
                                          {t('analysis:intentConfirmation.yourQuestion', { number: 1 })}
                                        </Text>
                                        <div style={{
                                          background: '#f8fafc',
                                          padding: '12px 16px',
                                          borderRadius: 8,
                                          marginTop: 4,
                                          border: '1px solid #e2e8f0',
                                          borderLeft: '3px solid #3b82f6'
                                        }}>
                                          <Text style={{ fontSize: '14px', color: '#1f2937', lineHeight: '1.6' }}>
                                            "{intentConfirmation.user_query}"
                                          </Text>
                                        </div>
                                      </div>
                                    )}

                                    {/* 显示意图分析和执行步骤规划 */}
                                    {intentConfirmation.intent_analysis && (
                                      <div style={{ marginBottom: 20 }}>
                                        {/* 意图描述 */}
                                        {intentConfirmation.intent_analysis.intent_description && (
                                          <div style={{ marginBottom: 16 }}>
                                            <Text style={{ color: '#374151', fontSize: '13px', fontWeight: '500', display: 'block', marginBottom: '8px' }}>
                                              {t('analysis:intentConfirmation.analysisIntent')}
                                            </Text>
                                            <div style={{
                                              background: '#f8fafc',
                                              padding: '12px 16px',
                                              borderRadius: 8,
                                              border: '1px solid #e2e8f0',
                                              borderLeft: '3px solid #3b82f6'
                                            }}>
                                              <Text style={{ fontSize: '13px', color: '#1f2937', lineHeight: '1.6' }}>
                                                {intentConfirmation.intent_analysis.intent_description}
                                              </Text>
                                            </div>
                                          </div>
                                        )}

                                        {/* 执行步骤规划 */}
                                        {intentConfirmation.intent_analysis.execution_steps && 
                                         Array.isArray(intentConfirmation.intent_analysis.execution_steps) && 
                                         intentConfirmation.intent_analysis.execution_steps.length > 0 && (
                                          <div style={{ marginBottom: 16 }}>
                                                                                         <Text style={{ color: '#374151', fontSize: '13px', fontWeight: '500', marginBottom: 12, display: 'block' }}>
                                               <CodeOutlined style={{ marginRight: 6, color: '#3b82f6' }} />
                                               {t('analysis:intentConfirmation.executionSteps')}
                                             </Text>
                                            <div style={{ paddingLeft: 8 }}>
                                              {intentConfirmation.intent_analysis.execution_steps.map((step: any, index: number) => (
                                                <Card 
                                                  key={index} 
                                                  size="small"
                                                  style={{
                                                    marginBottom: 12,
                                                    border: '1px solid #e8e8e8',
                                                    borderRadius: 6
                                                  }}
                                                  bodyStyle={{ padding: '12px 16px' }}
                                                >
                                                  <div style={{ display: 'flex', alignItems: 'flex-start' }}>
                                                    {/* 步骤编号 */}
                                                    <div style={{
                                                      backgroundColor: '#3b82f6',
                                                      color: 'white',
                                                      borderRadius: '50%',
                                                      width: 20,
                                                      height: 20,
                                                      display: 'flex',
                                                      alignItems: 'center',
                                                      justifyContent: 'center',
                                                      fontSize: '11px',
                                                      fontWeight: 'bold',
                                                      marginRight: 12,
                                                      flexShrink: 0,
                                                      marginTop: 2
                                                    }}>
                                                      {step.step_number || index + 1}
                                                    </div>

                                                    {/* 步骤内容 */}
                                                    <div style={{ flex: 1 }}>
                                                      {/* 步骤意图 */}
                                                      {step.step_intent && (
                                                        <div style={{ marginBottom: 8 }}>
                                                          <Text strong style={{ fontSize: '12px', color: '#1f2937' }}>
                                                            {step.step_intent}
                                                          </Text>
                                                        </div>
                                                      )}

                                                      {/* 推荐工具和理由 */}
                                                      <div style={{ display: 'flex', gap: 12, alignItems: 'center', flexWrap: 'wrap' }}>
                                                        {step.recommended_tool && (
                                                                                                                     <Tag 
                                                             color="blue" 
                                                             style={{ 
                                                               fontSize: '11px',
                                                               margin: 0
                                                             }}
                                                           >
                                                             <CodeOutlined style={{ marginRight: 4 }} />
                                                             {step.recommended_tool}
                                                           </Tag>
                                                        )}
                                                        
                                                        {step.tool_usage_reason && (
                                                          <Text style={{ 
                                                            fontSize: '11px', 
                                                            color: '#666',
                                                            fontStyle: 'italic'
                                                          }}>
                                                            {step.tool_usage_reason}
                                                          </Text>
                                                        )}
                                                      </div>
                                                    </div>
                                                  </div>
                                                </Card>
                                              ))}
                                            </div>
                                          </div>
                                        )}
                                      </div>
                                    )}

                                    {/* 显示澄清问题和完整的交互选项 */}
                                    {intentConfirmation.intent_analysis?.needs_clarification &&
                                     intentConfirmation.intent_analysis?.clarification_questions &&
                                     Array.isArray(intentConfirmation.intent_analysis.clarification_questions) &&
                                     intentConfirmation.intent_analysis.clarification_questions.length > 0 && (
                                      <div style={{ marginBottom: 12 }}>
                                        {intentConfirmation.intent_analysis.clarification_questions.map((question: any, index: number) => (
                                          <div key={question.id || index} style={{ marginBottom: 12 }}>
                                            <div style={{ 
                                              background: '#f1f5f9', 
                                              padding: '12px 16px', 
                                              borderRadius: 8, 
                                              marginBottom: 12,
                                              border: '1px solid #dbeafe'
                                            }}>
                                              <Text style={{ fontSize: '14px', color: '#1e40af', fontWeight: '600', display: 'block' }}>
                                                {t('analysis:intentConfirmation.yourQuestion')}
                                            </Text>
                                              <Text style={{ fontSize: '13px', color: '#374151', lineHeight: '1.5', marginTop: 4 }}>
                                                {question.question}
                                              </Text>
                                            </div>

                                            {/* 选项展示 - 使用紧凑的选择按钮组 */}
                                            {question.options && Array.isArray(question.options) && question.options.length > 0 && (
                                              <div style={{ marginBottom: 8 }}>
                                                <div style={{ 
                                                  display: 'grid', 
                                                  gridTemplateColumns: 'repeat(auto-fit, minmax(min(100px, 100%), 1fr))',
                                                  gap: '4px',
                                                  marginBottom: 8
                                                }}>
                                                  {question.options.map((option: string, optIndex: number) => {
                                                    const isSelected = clarificationAnswers && clarificationAnswers[question.id] === option;
                                                    return (
                                                      <div
                                                        key={optIndex}
                                                        onClick={() => {
                                                    setClarificationAnswers(prev => ({
                                                      ...prev,
                                                            [question.id]: option
                                                    }));
                                                  }}
                                                        style={{
                                                          padding: '6px 10px',
                                                          borderRadius: 6,
                                                          border: isSelected ? '2px solid #2563eb' : '1px solid #e2e8f0',
                                                          background: isSelected 
                                                            ? 'linear-gradient(135deg, #2563eb 0%, #3b82f6 100%)' 
                                                            : 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
                                                          color: isSelected ? '#ffffff' : '#475569',
                                                          fontSize: '11px',
                                                          fontWeight: isSelected ? '500' : '400',
                                                          textAlign: 'center',
                                                          cursor: 'pointer',
                                                          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                                                          userSelect: 'none',
                                                          minHeight: '30px',
                                                          display: 'flex',
                                                          alignItems: 'center',
                                                          justifyContent: 'center',
                                                          lineHeight: '1.2',
                                                          boxShadow: isSelected 
                                                            ? '0 3px 8px rgba(37, 99, 235, 0.15)' 
                                                            : '0 1px 3px rgba(0, 0, 0, 0.05)'
                                                        }}
                                                        onMouseEnter={(e) => {
                                                          if (!isSelected) {
                                                            e.currentTarget.style.background = 'linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%)';
                                                            e.currentTarget.style.borderColor = '#cbd5e1';
                                                            e.currentTarget.style.transform = 'translateY(-1px)';
                                                            e.currentTarget.style.boxShadow = '0 3px 8px rgba(0, 0, 0, 0.1)';
                                                          }
                                                        }}
                                                        onMouseLeave={(e) => {
                                                          if (!isSelected) {
                                                            e.currentTarget.style.background = 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)';
                                                            e.currentTarget.style.borderColor = '#e2e8f0';
                                                            e.currentTarget.style.transform = 'translateY(0)';
                                                            e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.05)';
                                                          }
                                                        }}
                                                      >
                                                        {option}
                                                      </div>
                                                    );
                                                  })}
                                                </div>
                                              </div>
                                            )}

                                            {/* 自定义输入框 */}
                                            <Input
                                              value={clarificationAnswers ? clarificationAnswers[question.id] || '' : ''}
                                              onChange={(e) => {
                                                setClarificationAnswers(prev => ({
                                                  ...prev,
                                                  [question.id]: e.target.value
                                                }));
                                              }}
                                              placeholder={question.options && question.options.length > 0
                                                ? t('analysis:intentConfirmation.answerOrSelect', { defaultValue: '请输入您的答案或选择上面的选项' })
                                                : t('analysis:intentConfirmation.answerPlaceholder', { defaultValue: '请输入您的答案' })
                                              }
                                              style={{
                                                borderRadius: 4,
                                                fontSize: '12px'
                                              }}
                                            />
                                          </div>
                                        ))}
                                      </div>
                                    )}

                                    {/* 补充说明输入 */}
                                    <div style={{ marginBottom: 20 }}>
                                      <Text style={{ color: '#374151', fontSize: '13px', fontWeight: '500', marginBottom: 8, display: 'block' }}>
                                        {t('analysis:intentConfirmation.additionalNotes')}
                                      </Text>
                                      <Input.TextArea
                                        value={intentAdjustment}
                                        onChange={(e) => setIntentAdjustment(e.target.value)}
                                        placeholder={t('analysis:intentConfirmation.additionalNotesPlaceholder', { defaultValue: '如果您对以上分析有补充或修正，请在此输入...' })}
                                        rows={3}
                                        style={{
                                          borderRadius: 8,
                                          fontSize: '13px',
                                          border: '1px solid #d1d5db',
                                          backgroundColor: '#f9fafb'
                                        }}
                                      />
                                    </div>

                                    {/* 操作按钮 */}
                                    <div style={{ 
                                      display: 'flex', 
                                      justifyContent: 'center', 
                                      gap: '12px',
                                      marginTop: 24,
                                      paddingTop: 16,
                                      borderTop: '1px solid #e5e7eb'
                                    }}>
                                        <Button
                                          onClick={async () => {
                                            setShowIntentConfirmation(false);
                                            setIntentConfirmation(null);
                                            setIntentAdjustment('');
                                            setClarificationAnswers({});

                                            // 更新意图确认步骤状态为已取消
                                            setAnalysisSteps((prev: any[]) =>
                                              prev.map(step =>
                                                (step.id === 'intent_confirmation' || step.id === 'intent_confirmation_request')
                                                  ? { ...step, status: 'error', name: t('analysis:intentConfirmation.waitingForConfirmation') }
                                                  : step
                                              )
                                            );
                                          }}
                                        style={{
                                          borderRadius: 8,
                                          fontSize: '13px',
                                          height: '36px',
                                          paddingLeft: '20px',
                                          paddingRight: '20px',
                                          color: '#6b7280',
                                          borderColor: '#d1d5db'
                                        }}
                                        >
                                          {t('analysis:intentConfirmation.cancel')}
                                        </Button>
                                        <Button
                                          type="primary"
                                          onClick={handleIntentConfirmationSubmit}
                                          icon={<PlayCircleOutlined />}
                                        style={{
                                          borderRadius: 8,
                                          fontSize: '13px',
                                          height: '36px',
                                          paddingLeft: '20px',
                                          paddingRight: '20px',
                                          background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                                          border: 'none',
                                          boxShadow: '0 2px 4px rgba(59, 130, 246, 0.3)'
                                        }}
                                      >
                                        {t('analysis:intentConfirmation.confirmAndContinue')}
                                        </Button>
                                    </div>
                                  </Card>
                                </div>
                              )}
                              
                              {/* 渲染意图确认/调整内容 - 放在步骤结果后面，看起来像工具执行的结果 */}
                              {(step.interactionType === 'intent_confirmation' ||
                                step.interactionType === 'intent_adjustment' ||
                                step.id === 'intent_confirmation_request' ||
                                step.id === 'intent_adjustment_completed') &&
                                step.interactionData && step.status !== 'error' && (
                                <div style={{ marginTop: 16, marginLeft: 24 }}>
                                  <Card
                                    bordered={false}
                                    style={{
                                      background: '#ffffff',
                                      border: '1.5px solid #e2e8f0',
                                      borderRadius: 16,
                                      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
                                    }}
                                    size="small"
                                  >
                                    {/* 自定义标题区域 */}
                                    <div style={{
                                      marginBottom: '16px',
                                      paddingBottom: '12px',
                                      borderBottom: '1px solid #f1f5f9'
                                    }}>
                                      <div style={{ 
                                        display: 'flex', 
                                        alignItems: 'center',
                                        marginBottom: '6px'
                                      }}>
                                        <div style={{
                                          background: '#10b981',
                                          borderRadius: '6px',
                                          width: '20px',
                                          height: '20px',
                                          display: 'flex',
                                          alignItems: 'center',
                                          justifyContent: 'center',
                                          marginRight: '10px',
                                          fontSize: '12px'
                                        }}>
                                          ✓
                                        </div>
                                        <div>
                                          <Text style={{ 
                                            fontSize: '15px',
                                            fontWeight: '600',
                                            color: '#1f2937'
                                          }}>
                                          {t('analysis:intentConfirmation.completed')}
                                          </Text>
                                      </div>
                                      </div>
                                      <Text style={{ 
                                        fontSize: '12px',
                                        color: '#6b7280',
                                        lineHeight: '1.4'
                                      }}>
                                        {t('analysis:intentConfirmation.systemContinueMessage')}
                                      </Text>
                                    </div>

                                    
                                    {/* 显示意图分析和执行步骤规划 */}
                                    {step.interactionData.intent_analysis && (
                                      <div style={{ marginBottom: 16 }}>
                                        {/* 意图描述 */}
                                        {step.interactionData.intent_analysis.intent_description && (
                                          <div style={{ marginBottom: 12 }}>
                                            <Text style={{ color: '#374151', fontSize: '12px', fontWeight: '500', display: 'block', marginBottom: '6px' }}>
                                              {t('analysis:intentConfirmation.analysisIntent')}
                                            </Text>
                                            <div style={{
                                              background: '#f8fafc',
                                              padding: '10px 12px',
                                              borderRadius: 6,
                                              border: '1px solid #e2e8f0',
                                              borderLeft: '3px solid #3b82f6'
                                            }}>
                                              <Text style={{ fontSize: '12px', color: '#1f2937', lineHeight: '1.5' }}>
                                                {step.interactionData.intent_analysis.intent_description}
                                              </Text>
                                            </div>
                                          </div>
                                        )}

                                        {/* 执行步骤规划 */}
                                        {step.interactionData.intent_analysis.execution_steps && 
                                         Array.isArray(step.interactionData.intent_analysis.execution_steps) && 
                                         step.interactionData.intent_analysis.execution_steps.length > 0 && (
                                          <div style={{ marginBottom: 12 }}>
                                            <Text style={{ color: '#374151', fontSize: '12px', fontWeight: '500', marginBottom: 8, display: 'block' }}>
                                              <CodeOutlined style={{ marginRight: 4, color: '#3b82f6' }} />
                                              {t('analysis:intentConfirmation.executionSteps')}
                                            </Text>
                                            <div style={{ paddingLeft: 4 }}>
                                              {step.interactionData.intent_analysis.execution_steps.map((stepPlan: any, index: number) => (
                                                <Card 
                                                  key={index} 
                                                  size="small"
                                                  style={{
                                                    marginBottom: 8,
                                                    border: '1px solid #e8e8e8',
                                                    borderRadius: 4
                                                  }}
                                                  bodyStyle={{ padding: '8px 12px' }}
                                                >
                                                  <div style={{ display: 'flex', alignItems: 'flex-start' }}>
                                                    {/* 步骤编号 */}
                                                    <div style={{
                                                      backgroundColor: '#3b82f6',
                                                      color: 'white',
                                                      borderRadius: '50%',
                                                      width: 16,
                                                      height: 16,
                                                      display: 'flex',
                                                      alignItems: 'center',
                                                      justifyContent: 'center',
                                                      fontSize: '10px',
                                                      fontWeight: 'bold',
                                                      marginRight: 8,
                                                      flexShrink: 0,
                                                      marginTop: 1
                                                    }}>
                                                      {stepPlan.step_number || index + 1}
                                                    </div>

                                                    {/* 步骤内容 */}
                                                    <div style={{ flex: 1 }}>
                                                      {/* 步骤意图 */}
                                                      {stepPlan.step_intent && (
                                                        <div style={{ marginBottom: 6 }}>
                                                          <Text strong style={{ fontSize: '11px', color: '#1f2937' }}>
                                                            {stepPlan.step_intent}
                                                          </Text>
                                                        </div>
                                                      )}

                                                      {/* 推荐工具和理由 */}
                                                      <div style={{ display: 'flex', gap: 8, alignItems: 'center', flexWrap: 'wrap' }}>
                                                        {stepPlan.recommended_tool && (
                                                          <Tag 
                                                            color="blue" 
                                                            style={{ 
                                                              fontSize: '10px',
                                                              margin: 0,
                                                              padding: '2px 6px',
                                                              lineHeight: '1.2'
                                                            }}
                                                          >
                                                            <CodeOutlined style={{ marginRight: 2 }} />
                                                            {stepPlan.recommended_tool}
                                                          </Tag>
                                                        )}
                                                        
                                                        {stepPlan.tool_usage_reason && (
                                                          <Text style={{ 
                                                            fontSize: '10px', 
                                                            color: '#666',
                                                            fontStyle: 'italic'
                                                          }}>
                                                            {stepPlan.tool_usage_reason}
                                                          </Text>
                                                        )}
                                                      </div>
                                                    </div>
                                                  </div>
                                                </Card>
                                              ))}
                                            </div>
                                          </div>
                                        )}
                                      </div>
                                    )}

                                    {/* 🔧 移除重复的问答显示，问答详情将在"您的补充说明"区域统一格式化显示 */}
                                    <div style={{ fontSize: '14px', lineHeight: '1.6' }}>

                                      {/* 显示用户补充说明 - 格式化显示澄清回复和补充说明 */}
                                      {step.interactionData.user_adjustment && step.interactionData.user_adjustment.trim() && (
                                        <div style={{ marginBottom: 12 }}>
                                          {/* 🔧 使用格式化的澄清内容渲染，移除外层重复标题 */}
                                          {renderFormattedClarification(step.interactionData.user_adjustment)}
                                        </div>
                                      )}
                                    </div>


                                  </Card>
                                </div>
                              )}
                              
                              {/* reasoning已经显示在步骤名称中，不需要额外显示 */}

                              {/* 洞察显示 */}
                              {step.hasInsights && step.insightData && (
                                <div style={{ marginTop: '12px', padding: '12px', backgroundColor: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: '6px' }}>
                                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                                    <BulbOutlined style={{ color: '#52c41a', marginRight: '6px' }} />
                                    <Text strong style={{ color: '#389e0d' }}>
                                      {t('analysis:insights.discovered', { defaultValue: '发现洞察' })}
                                    </Text>
                                  </div>

                                  {/* 数据质量指标 */}
                                  {step.insightData.data_quality && (
                                    <div style={{ marginBottom: '8px' }}>
                                      <Text style={{ fontSize: '12px', color: '#666' }}>
                                        {t('analysis:insights.dataQuality', { defaultValue: '数据质量' })}: {(step.insightData.data_quality.overall_score * 100).toFixed(0)}%
                                        {step.insightData.statistical_summary && (
                                          <span style={{ marginLeft: '8px' }}>
                                            | {t('analysis:insights.dataScale', { defaultValue: '数据规模' })}: {step.insightData.statistical_summary.total_rows}{t('analysis:insights.rows', { defaultValue: '行' })}
                                          </span>
                                        )}
                                      </Text>
                                    </div>
                                  )}

                                  {/* 关键洞察 */}
                                  {step.insightData.insights && step.insightData.insights.length > 0 && (
                                    <div style={{ marginBottom: '8px' }}>
                                      <Text type="secondary" style={{ fontSize: '12px', fontWeight: 'bold' }}>
                                        {t('analysis:insights.keyFindings', { defaultValue: '关键洞察' })}:
                                      </Text>
                                      {step.insightData.insights.slice(0, 2).map((insight: any, idx: number) => {
                                        // 🔧 修复：洞察类型多语言支持
                                        const getInsightTypeLabel = (type: string) => {
                                          const typeMap: Record<string, string> = {
                                            'summary': t('analysis:insights.types.summary', { defaultValue: '概览' }),
                                            'pattern': t('analysis:insights.types.pattern', { defaultValue: '模式' }),
                                            'trend': t('analysis:insights.types.trend', { defaultValue: '趋势' }),
                                            'correlation': t('analysis:insights.types.correlation', { defaultValue: '相关性' }),
                                            'anomaly': t('analysis:insights.types.anomaly', { defaultValue: '异常' }),
                                            'distribution': t('analysis:insights.types.distribution', { defaultValue: '分布' }),
                                            'outlier': t('analysis:insights.types.outlier', { defaultValue: '离群值' }),
                                            'seasonal': t('analysis:insights.types.seasonal', { defaultValue: '季节性' }),
                                            'quality': t('analysis:insights.types.quality', { defaultValue: '质量' }),
                                            'business': t('analysis:insights.types.business', { defaultValue: '业务' }),
                                            'computation': t('analysis:insights.types.computation', { defaultValue: '计算分析' }),
                                            'phenomenon': t('analysis:insights.types.phenomenon', { defaultValue: '现象' }),
                                            'causal': t('analysis:insights.types.causal', { defaultValue: '因果' }),
                                            'causal_analysis': t('analysis:insights.types.causal_analysis', { defaultValue: '因果分析' })
                                          };
                                          return typeMap[type] || type;
                                        };

                                        return (
                                          <div key={idx} style={{ marginLeft: '8px', marginTop: '4px' }}>
                                            <div style={{ fontSize: '13px' }}>
                                              <div style={{ marginBottom: '4px' }}>
                                                • <strong>[{getInsightTypeLabel(insight.type)}] {insight.title}</strong>
                                                {insight.confidence && (
                                                  <span style={{ color: '#666', fontSize: '12px', marginLeft: '8px' }}>
                                                    ({t('analysis:insights.confidence', { defaultValue: '置信度' })}: {(insight.confidence * 100).toFixed(0)}%)
                                                  </span>
                                                )}
                                              </div>
                                              {/* 支持markdown渲染的描述 */}
                                              <div style={{ marginLeft: '12px', color: '#666' }}>
                                                <ReactMarkdown
                                                  remarkPlugins={[remarkGfm]}
                                                  rehypePlugins={[rehypeRaw]}
                                                  components={{
                                                    p: ({ children }) => <div style={{ marginBottom: '8px', lineHeight: '1.5' }}>{children}</div>,
                                                    strong: ({ children }) => <strong style={{ color: '#333' }}>{children}</strong>,
                                                    em: ({ children }) => <em style={{ color: '#555' }}>{children}</em>,
                                                    table: ({ children }) => (
                                                      <table style={{
                                                        borderCollapse: 'collapse',
                                                        width: '100%',
                                                        marginTop: '8px',
                                                        marginBottom: '8px',
                                                        fontSize: '12px'
                                                      }}>
                                                        {children}
                                                      </table>
                                                    ),
                                                    thead: ({ children }) => <thead style={{ backgroundColor: '#f5f5f5' }}>{children}</thead>,
                                                    tbody: ({ children }) => <tbody>{children}</tbody>,
                                                    tr: ({ children }) => <tr style={{ borderBottom: '1px solid #e0e0e0' }}>{children}</tr>,
                                                    th: ({ children }) => (
                                                      <th style={{
                                                        padding: '6px 8px',
                                                        textAlign: 'left',
                                                        fontWeight: 'bold',
                                                        border: '1px solid #d0d0d0',
                                                        backgroundColor: '#f8f8f8'
                                                      }}>
                                                        {children}
                                                      </th>
                                                    ),
                                                    td: ({ children }) => (
                                                      <td style={{
                                                        padding: '6px 8px',
                                                        border: '1px solid #d0d0d0',
                                                        verticalAlign: 'top'
                                                      }}>
                                                        {children}
                                                      </td>
                                                    ),
                                                    ul: ({ children }) => <ul style={{ marginLeft: '16px', marginBottom: '8px' }}>{children}</ul>,
                                                    ol: ({ children }) => <ol style={{ marginLeft: '16px', marginBottom: '8px' }}>{children}</ol>,
                                                    li: ({ children }) => <li style={{ marginBottom: '4px' }}>{children}</li>,
                                                    h1: ({ children }) => <h1 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '8px', color: '#333' }}>{children}</h1>,
                                                    h2: ({ children }) => <h2 style={{ fontSize: '15px', fontWeight: 'bold', marginBottom: '6px', color: '#333' }}>{children}</h2>,
                                                    h3: ({ children }) => <h3 style={{ fontSize: '14px', fontWeight: 'bold', marginBottom: '6px', color: '#333' }}>{children}</h3>,
                                                    h4: ({ children }) => <h4 style={{ fontSize: '13px', fontWeight: 'bold', marginBottom: '4px', color: '#333' }}>{children}</h4>,
                                                    blockquote: ({ children }) => (
                                                      <blockquote style={{
                                                        borderLeft: '3px solid #ddd',
                                                        paddingLeft: '12px',
                                                        margin: '8px 0',
                                                        fontStyle: 'italic',
                                                        color: '#666'
                                                      }}>
                                                        {children}
                                                      </blockquote>
                                                    ),
                                                    code: ({ children }) => (
                                                      <code style={{
                                                        backgroundColor: '#f5f5f5',
                                                        padding: '2px 4px',
                                                        borderRadius: '3px',
                                                        fontSize: '11px',
                                                        fontFamily: 'monospace'
                                                      }}>
                                                        {children}
                                                      </code>
                                                    )
                                                  }}
                                                >
                                                  {insight.description}
                                                </ReactMarkdown>
                                              </div>
                                            </div>
                                          </div>
                                        );
                                      })}
                                    </div>
                                  )}

                                  {/* 数据模式 */}
                                  {step.insightData.patterns && step.insightData.patterns.length > 0 && (
                                    <div style={{ marginBottom: '8px' }}>
                                      <Text type="secondary" style={{ fontSize: '12px', fontWeight: 'bold' }}>
                                        {t('analysis:insights.patterns', { defaultValue: '数据模式' })}:
                                      </Text>
                                      {step.insightData.patterns.slice(0, 2).map((pattern: string, idx: number) => (
                                        <div key={idx} style={{ marginLeft: '8px', marginTop: '4px' }}>
                                          <Text style={{ fontSize: '13px' }}>• {pattern}</Text>
                                        </div>
                                      ))}
                                    </div>
                                  )}

                                  {/* 异常检测 */}
                                  {step.insightData.anomalies && step.insightData.anomalies.length > 0 && (
                                    <div style={{ marginBottom: '8px' }}>
                                      <Text type="secondary" style={{ fontSize: '12px', fontWeight: 'bold' }}>
                                        {t('analysis:insights.anomalies', { defaultValue: '异常检测' })}:
                                      </Text>
                                      {step.insightData.anomalies.slice(0, 2).map((anomaly: string, idx: number) => (
                                        <div key={idx} style={{ marginLeft: '8px', marginTop: '4px' }}>
                                          <Text style={{ fontSize: '13px', color: '#ff4d4f' }}>⚠ {anomaly}</Text>
                                        </div>
                                      ))}
                                    </div>
                                  )}

                                  {/* 相关性分析 */}
                                  {step.insightData.correlations && step.insightData.correlations.length > 0 && (
                                    <div style={{ marginBottom: '8px' }}>
                                      <Text type="secondary" style={{ fontSize: '12px', fontWeight: 'bold' }}>
                                        {t('analysis:insights.correlations', { defaultValue: '相关性分析' })}:
                                      </Text>
                                      {step.insightData.correlations.slice(0, 2).map((corr: any, idx: number) => {
                                        // 🔧 修复：相关性强度多语言支持
                                        const getCorrelationStrength = (strength: string) => {
                                          const strengthMap: Record<string, string> = {
                                            'strong': t('analysis:insights.correlationStrength.strong', { defaultValue: '强' }),
                                            'moderate': t('analysis:insights.correlationStrength.moderate', { defaultValue: '中等' }),
                                            'weak': t('analysis:insights.correlationStrength.weak', { defaultValue: '弱' }),
                                            'very_strong': t('analysis:insights.correlationStrength.veryStrong', { defaultValue: '很强' }),
                                            'very_weak': t('analysis:insights.correlationStrength.veryWeak', { defaultValue: '很弱' })
                                          };
                                          return strengthMap[strength] || strength;
                                        };

                                        return (
                                          <div key={idx} style={{ marginLeft: '8px', marginTop: '4px' }}>
                                            <Text style={{ fontSize: '13px' }}>
                                              🔗 {corr.column1} ↔ {corr.column2}: {(corr.correlation * 100).toFixed(0)}% ({getCorrelationStrength(corr.strength)})
                                            </Text>
                                          </div>
                                        );
                                      })}
                                    </div>
                                  )}
                                </div>
                              )}

                              {step.hasReport && renderFinalReport(roundFinalReport)}
                            </Timeline.Item>
                          );
                        })}
                          
                          {/* 时间轴loading步骤 - 智能规划中 */}
                          {(() => {
                            // 检查时间轴前一步（最后一个步骤）的状态
                            const filteredSteps = roundSteps.filter(step => {
                              // 过滤掉"等待用户提供澄清信息"步骤，避免重复显示
                              if (step.name === '等待用户提供澄清信息' || step.tool_name === '用户澄清回复') {
                                return false;
                              }
                              return true;
                            });
                            
                            const lastStep = filteredSteps[filteredSteps.length - 1];
                            const lastStepIsProcessing = lastStep && lastStep.status === 'process';
                            
                            // 🔧 修复：特殊处理第一轮分析开始步骤，允许在"第 1 轮分析开始"步骤时显示loading
                            const isFirstRoundStartStep = lastStep && (lastStep.id === 'start' || lastStep.name === '第 1 轮分析开始');
                            const shouldAllowLoading = !lastStepIsProcessing || isFirstRoundStartStep;
                            
                            // 🔧 修复：第一轮分析特殊处理 - 不等待timelineLoading，直接基于streaming状态显示
                            const isFirstRoundAnalysis = streaming && isLatestRound && conversationState.rounds.length === 1;
                            const shouldShowLoadingNow = isActiveRound && shouldAllowLoading && (timelineLoading || isFirstRoundAnalysis);
                            
                            console.log(`🔍 智能规划loading检查 (轮次${roundIndex + 1}):`, {
                              streaming,
                              timelineLoading,
                              isActiveRound,
                              lastStep: lastStep ? {
                                id: lastStep.id,
                                name: lastStep.name,
                                status: lastStep.status,
                                tool_name: lastStep.tool_name
                              } : null,
                              lastStepIsProcessing,
                              isFirstRoundStartStep,
                              shouldAllowLoading,
                              isFirstRoundAnalysis,
                              shouldShowLoadingNow,
                              oldCondition: isActiveRound && timelineLoading && shouldAllowLoading
                            });
                            
                            // 只有在活跃轮次且满足loading条件时才显示
                            if (shouldShowLoadingNow) {
                              console.log('✅ 显示智能规划loading');
                              return (
                                <Timeline.Item
                                  key="timeline-loading"
                                  dot={<LoadingOutlined style={{ fontSize: '16px' }} spin />}
                                >
                                  <Text type="secondary" style={{ fontStyle: 'italic', color: '#666666' }}>
                                    {t('analysis:steps.intelligentPlanning', { defaultValue: '智能规划中...' })}
                                  </Text>
                                </Timeline.Item>
                              );
                            } else {
                              console.log('❌ 不显示智能规划loading', {
                                reason: shouldAllowLoading ? '其他条件不满足' : '前一步正在执行且非开始步骤',
                                isActiveRound,
                                timelineLoading,
                                isFirstRoundAnalysis,
                                shouldAllowLoading
                              });
                              return null;
                            }
                          })()}
                          

                        </Timeline>
                      </div>
                    ) : (
                      <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>
                        <Text type="secondary">{t('analysis:steps.loadingSteps', { defaultValue: '分析步骤加载中...' })}</Text>
                      </div>
                    )}
                    
                    {/* 继续分析面板 - 只在最后一轮且完成时显示 */}
                    {isLatestRound && roundStatus === 'completed' && !streaming && !showContinueInput && (
                      <ContinueAnalysisPanel
                        onContinue={handleContinueAnalysis}
                        onNewConversation={handleNewConversation}
                        disabled={streaming}
                      />
                    )}
                  </Card>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    );
  };



  // 处理意图确认提交
  const handleIntentConfirmationSubmit = async () => {
    if (!intentConfirmation || !analysisId || !analysisId.trim()) return;
    
    try {
      console.log('提交意图确认:', { intentAdjustment, analysisId, clarificationAnswers });
      
      // 构建完整的意图确认调整内容
      let finalAdjustment = "";
      
      // 如果有澄清问题的答案
      if (intentConfirmation?.intent_analysis?.needs_clarification && 
          clarificationAnswers && 
          Object.keys(clarificationAnswers).length > 0) {
        
                 // 构建澄清回复内容
         const clarificationParts: string[] = [];
         const questions = intentConfirmation.intent_analysis.clarification_questions || [];
        
        questions.forEach((question: any) => {
          const answer = clarificationAnswers[question.id];
          if (answer && answer.trim()) {
            clarificationParts.push(`${question.question}\n${t('analysis:intentConfirmation.answer')}${answer.trim()}`);
          }
        });
        
        if (clarificationParts.length > 0) {
          finalAdjustment += `${t('analysis:intentConfirmation.clarificationReply')}\n` + clarificationParts.join("\n\n");
        }
      }
      
      // 如果有用户的额外补充
      if (intentAdjustment && intentAdjustment.trim()) {
        if (finalAdjustment) {
          finalAdjustment += `\n\n${t('analysis:intentConfirmation.additionalRequirements')}\n` + intentAdjustment.trim();
        } else {
          finalAdjustment = intentAdjustment.trim();
        }
      }
      
      // 如果没有任何内容，使用确认标识
      if (!finalAdjustment) {
        finalAdjustment = t('analysis:intentConfirmation.confirmExecutePlan');
      }
      
      // 立即关闭意图确认弹窗
      setShowIntentConfirmation(false);
      setIntentConfirmation(null);
      setIntentAdjustment('');
      setClarificationAnswers({});
      
      // 关闭当前的EventSource连接
      if (eventSourceRef.current) {
        if ('close' in eventSourceRef.current) {
          eventSourceRef.current.close();
        } else if ('cancel' in eventSourceRef.current) {
          eventSourceRef.current.cancel();
        }
        eventSourceRef.current = null;
      }
      
      // 重新设置streaming状态为true
      setStreaming(true);
      
      // 在多轮分析中，我们需要继续当前轮次的分析
      if (!conversationState) {
        throw new Error('没有会话状态，无法继续分析');
      }
      
      // 🔧 为意图确认继续分析生成唯一连接ID
      const intentConnectionId = `conn_intent_${conversationState.conversationId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      connectionIdRef.current = intentConnectionId;
      setCurrentConnectionId(intentConnectionId);
      console.log(`🔗 创建意图确认连接 (connectionId: ${intentConnectionId})`);
      
      // 继续多轮分析 - 传递原始查询和补充信息作为分离的字段
      eventSourceRef.current = await multiRoundAnalysisApi.executeRoundAnalysis(
        conversationState.conversationId,
        {
          query: conversationState.rounds[conversationState.currentRound - 1]?.query || '', // 保持原始查询
          intent_adjustment: finalAdjustment, // 补充信息作为单独字段
          round_number: conversationState.currentRound + 1,
          max_planning_rounds: 25,
          use_full_context: true,
          context_depth: 5,
          continue_analysis_id: analysisId && analysisId.trim() ? analysisId : undefined // 只有有效的分析ID才传递
        } as any,
        {
          onStart: () => {
            console.log('意图确认后继续分析开始');
          },
          onEvent: (eventType: string, eventData: any) => {
            handleLlmStreamEvent(eventType, eventData, intentConnectionId);
          },
          onError: (error: any) => {
            console.error('意图确认后继续分析出错:', error);
            message.error(t('analysis:errors.continueAnalysisFailed'));
            setStreaming(false);
          },
          onComplete: (analysisId: any) => {
            console.log('意图确认后分析完成:', analysisId);
            setStreaming(false);
            setStreamComplete(true);
            updateCurrentRoundStatus('completed');
          }
        }
      );
    } catch (error: any) {
      console.error('提交意图确认失败:', error);
      message.error(t('analysis:errors.submitIntentConfirmationFailed', { error: formatErrorMessage(error) }));
      setStreaming(false);
      // 如果出错，重新显示意图确认弹窗
      setShowIntentConfirmation(true);
    }
  };



  // 处理选择会话 - 🔧增强连接管理和防抖保护
  const handleSelectConversation = async (conversationId: string) => {
    console.log('MultiRoundAnalysis: 处理会话选择，会话ID:', conversationId);
    
    // 🔧 防抖保护：如果正在切换会话，忽略新的选择请求
    if (isConversationSwitching) {
      console.log('🚫 正在切换会话中，忽略新的选择请求');
      return;
    }
    
    // 检查是否是当前已选中的会话，避免重复加载
    if (conversationState && conversationState.conversationId === conversationId) {
      console.log('选择的会话已经是当前会话，无需切换');
      return;
    }
    
    try {
      // 🔧 设置切换状态保护
      setIsConversationSwitching(true);
      
      // 🔧 立即关闭当前连接，防止事件错乱
      const oldConnectionId = connectionIdRef.current;
      if (eventSourceRef.current) {
        console.log(`🔗 切换前关闭EventSource连接 (connectionId: ${oldConnectionId})`);
        if ('close' in eventSourceRef.current) {
          eventSourceRef.current.close();
        } else if ('cancel' in eventSourceRef.current) {
          eventSourceRef.current.cancel();
        }
        eventSourceRef.current = null;
      }
      
      // 🔧 清理时间轴loading定时器和状态
      if (timelineLoadingTimerRef.current) {
        clearTimeout(timelineLoadingTimerRef.current);
        timelineLoadingTimerRef.current = null;
      }
      setTimelineLoading(false);
      
      // 🔧 重置连接标识符
      connectionIdRef.current = null;
      setCurrentConnectionId(null);
      
      // 简化逻辑：直接导航到新的会话URL，让URL变化驱动状态更新
      navigate(`/analysis/multi-round/${conversationId}`, { replace: true });
      console.log('✅ 会话URL导航完成，等待URL变化触发状态更新');
      
      // 🔧 延迟释放切换状态保护
      setTimeout(() => {
        setIsConversationSwitching(false);
        console.log('✅ handleSelectConversation切换状态保护已释放');
      }, 200);
      
    } catch (error) {
      console.error('会话导航失败:', error);
      message.error(t('analysis:errors.switchConversationFailed'));
      // 🔧 出错时也要释放切换状态保护
      setIsConversationSwitching(false);
    }
  };

  // 处理新建会话（从侧边栏）- 🔧增强连接管理
  const handleNewConversationFromSidebar = () => {
    if (!selectedProjectId) {
      message.warning(t('analysis:errors.pleaseSelectProject'));
      return;
    }
    
    // 🔧 防抖保护：如果正在切换会话，忽略请求
    if (isConversationSwitching) {
      console.log('🚫 正在切换会话中，忽略新建会话请求');
      return;
    }
    
    // 🔧 设置切换状态保护
    setIsConversationSwitching(true);
    
    // 🔧 立即关闭当前连接，防止事件错乱
    const oldConnectionId = connectionIdRef.current;
    if (eventSourceRef.current) {
      console.log(`🔗 新建会话前关闭EventSource连接 (connectionId: ${oldConnectionId})`);
      if ('close' in eventSourceRef.current) {
        eventSourceRef.current.close();
      } else if ('cancel' in eventSourceRef.current) {
        eventSourceRef.current.cancel();
      }
      eventSourceRef.current = null;
    }
    
    // 🔧 清理时间轴loading定时器和状态
    if (timelineLoadingTimerRef.current) {
      clearTimeout(timelineLoadingTimerRef.current);
      timelineLoadingTimerRef.current = null;
    }
    setTimelineLoading(false);
    
    // 🔧 重置连接标识符
    connectionIdRef.current = null;
    setCurrentConnectionId(null);
    
    // 重置所有状态
    setConversationState(null);
    setStreaming(false);
    setError('');
    setAnalysisSteps([]);
    setCurrentStep('');
    setIntentAnalysis(null);
    setToolResults([]);
    setFinalReport('');

    setAnalysisId('');
    setStreamComplete(false);
    setMcpResults({});
    setShowContinueInput(false);
    setContextSummary('');
    setIntentConfirmation(null);
    setShowIntentConfirmation(false);
    setIntentAdjustment('');
    form.resetFields();
    
    // 导航到新的多轮分析页面（不带conversationId）
    navigate('/analysis/multi-round', { replace: true });
    
    message.success(t('analysis:errors.newConversationCreated'));
    
    // 🔧 延迟释放切换状态保护
    setTimeout(() => {
      setIsConversationSwitching(false);
      console.log('✅ handleNewConversationFromSidebar切换状态保护已释放');
    }, 100);
  };







  // 图表下载功能
  const downloadChart = async (chartData: any) => {
    if (!chartData || !chartData.data) {
      message.error(t('analysis:errors.noDownloadableChartData'));
      return;
    }

    try {
      // 检查是否为多图表
      const isMultiChart = (
        chartData.data.chart_type === 'multi_charts' ||
        chartData.data.display_format === 'multi_chart' ||
        (chartData.data.total_charts && chartData.data.total_charts > 1)
      ) && chartData.data.charts;
      
      if (isMultiChart) {
        // 多图表下载
        await downloadMultipleCharts(chartData.data.charts, chartData.data.user_query || '多图表分析');
      } else {
        // 单图表下载
        await downloadSingleChart(chartData.data, chartData.data.user_query || '图表分析');
      }
    } catch (error) {
      console.error('下载图表失败:', error);
      message.error(t('analysis:errors.downloadChartFailed'));
    }
  };

  // 下载单个图表
  const downloadSingleChart = async (chartConfig: any, fileName: string) => {
    try {
      // 创建一个临时的ECharts实例用于导出
      const tempDiv = document.createElement('div');
      tempDiv.style.width = '1200px';
      tempDiv.style.height = '800px';
      tempDiv.style.position = 'absolute';
      tempDiv.style.left = '-9999px';
      tempDiv.style.top = '-9999px';
      document.body.appendChild(tempDiv);

      // 动态导入echarts
      const echarts = await import('echarts');
      const chart = echarts.init(tempDiv);
      
      // 设置图表配置
      chart.setOption({
        ...chartConfig.chart_config,
        animation: false, // 禁用动画以提高导出速度
        backgroundColor: '#ffffff' // 设置白色背景
      });

      // 等待图表渲染完成
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 导出为图片
      const dataURL = chart.getDataURL({
        type: 'png',
        pixelRatio: 2, // 高分辨率
        backgroundColor: '#ffffff'
      });

      // 创建下载链接
      const link = document.createElement('a');
      link.download = `${sanitizeFileName(fileName)}.png`;
      link.href = dataURL;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // 清理临时元素
      chart.dispose();
      document.body.removeChild(tempDiv);

      message.success(t('analysis:errors.chartDownloadSuccess'));
    } catch (error) {
      console.error('下载单个图表失败:', error);
      throw error;
    }
  };

  // 下载多个图表
  const downloadMultipleCharts = async (charts: any[], baseFileName: string) => {
    try {
      message.loading('正在准备下载多个图表...', 0);

      for (let i = 0; i < charts.length; i++) {
        const chart = charts[i];
        const chartTitle = chart.title || `图表${i + 1}`;
        const fileName = `${sanitizeFileName(baseFileName)}_${sanitizeFileName(chartTitle)}`;
        
        await downloadSingleChart(chart, fileName);
        
        // 添加延迟避免浏览器阻止多个下载
        if (i < charts.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      message.destroy();
      message.success(t('analysis:errors.chartsDownloadSuccess', { count: charts.length }));
    } catch (error) {
      message.destroy();
      console.error('下载多个图表失败:', error);
      throw error;
    }
  };



  // 文件名清理函数
  const sanitizeFileName = (fileName: string): string => {
    // 移除或替换不安全的文件名字符
    return fileName
      .replace(/[<>:"/\\|?*]/g, '_') // 替换不安全字符
      .replace(/\s+/g, '_') // 替换空格
      .replace(/_+/g, '_') // 合并多个下划线
      .replace(/^_|_$/g, '') // 移除开头和结尾的下划线
      .substring(0, 100); // 限制文件名长度
  };

  // 在意图确认完成卡片内容渲染前，插入辅助函数
  const extractSupplement = (userAdjustment: string): string | null => {
    if (!userAdjustment) return null;
    
    // 根据当前语言环境获取关键词
    const additionalReq = t('analysis:intentConfirmation.additionalRequirements');
    const supplementNote = t('analysis:intentConfirmation.supplementNote');
    const clarificationReply = t('analysis:intentConfirmation.clarificationReply');
    
    // 构建动态正则表达式，移除末尾冒号
    const reqKeyword = additionalReq.replace(/[：:]\s*$/, '');
    const noteKeyword = supplementNote.replace(/[：:]\s*$/, '');
    const replyKeyword = clarificationReply.replace(/[：:]\s*$/, '');
    
    // 匹配补充需求或补充说明
    const regexPattern = `(?:${reqKeyword}|${noteKeyword})[：:]\\s*([\\s\\S]*)$`;
    const match = userAdjustment.match(new RegExp(regexPattern));
    if (match && match[1]) {
      // 去掉前后空行
      return match[1].replace(/^\s+|\s+$/g, '');
    }
    
    // 如果没有匹配到结构化格式，且不包含"澄清回复"，则将整个内容作为补充说明
    if (!userAdjustment.includes(replyKeyword)) {
      return userAdjustment.trim();
    }
    
    return null;
  };

  // 🔧 新增：解析和格式化澄清回复内容
  const parseClarificationContent = (userAdjustment: string): {
    clarificationQA: Array<{question: string, answer: string}>,
    supplement: string | null
  } => {
    if (!userAdjustment) return { clarificationQA: [], supplement: null };

    const result = { clarificationQA: [] as Array<{question: string, answer: string}>, supplement: null as string | null };

    // 根据当前语言环境获取关键词
    const clarificationReply = t('analysis:intentConfirmation.clarificationReply');
    const additionalReq = t('analysis:intentConfirmation.additionalRequirements');
    const supplementNote = t('analysis:intentConfirmation.supplementNote');
    const answer = t('analysis:intentConfirmation.answer');
    
    // 移除末尾冒号构建关键词
    const replyKeyword = clarificationReply.replace(/[：:]\s*$/, '');
    const reqKeyword = additionalReq.replace(/[：:]\s*$/, '');
    const noteKeyword = supplementNote.replace(/[：:]\s*$/, '');
    const answerKeyword = answer.replace(/[：:]\s*$/, '');

    // 解析澄清回复部分 - 使用动态关键词
    const clarificationPattern = `${replyKeyword}[：:]?\\s*([\\s\\S]*?)(?=\\n\\n(?:${reqKeyword}|${noteKeyword})|$)`;
    const clarificationMatch = userAdjustment.match(new RegExp(clarificationPattern));
    if (clarificationMatch) {
      const clarificationText = clarificationMatch[1];
      
      // 解析问答对，支持多种格式
      const qaMatches = clarificationText.split(/\n\n+/).filter(qa => qa.trim());
      
      qaMatches.forEach(qa => {
        // 匹配格式：问题\n答案：回答 - 使用动态关键词
        const qaPattern = `^(.+?)\\n${answerKeyword}[：:]?\\s*([\\s\\S]+)$`;
        const qaMatch = qa.match(new RegExp(qaPattern));
        if (qaMatch) {
          result.clarificationQA.push({
            question: qaMatch[1].trim(),
            answer: qaMatch[2].trim()
          });
        }
      });
    }

    // 解析补充说明部分
    result.supplement = extractSupplement(userAdjustment);

    return result;
  };

  // 🔧 新增：渲染格式化的澄清内容
  const renderFormattedClarification = (userAdjustment: string): React.ReactNode => {
    const { clarificationQA, supplement } = parseClarificationContent(userAdjustment);

    return (
      <div>
        {/* 澄清问答 */}
        {clarificationQA.length > 0 && (
          <div style={{ marginBottom: supplement ? 16 : 0 }}>
            <Text style={{ color: '#374151', fontSize: '12px', fontWeight: '500', display: 'block', marginBottom: '8px' }}>
              {t('analysis:intentConfirmation.clarificationQA')}
            </Text>
            <div style={{ paddingLeft: 8 }}>
              {clarificationQA.map((qa, index) => (
                <div key={index} style={{ 
                  marginBottom: 12,
                  background: '#f8fafc',
                  padding: '8px 12px',
                  borderRadius: 6,
                  border: '1px solid #e2e8f0'
                }}>
                  <div style={{ marginBottom: 4 }}>
                    <Text style={{ color: '#6366f1', fontSize: '12px', fontWeight: '500' }}>
                      Q{index + 1}:
                    </Text>
                    <Text style={{ color: '#374151', fontSize: '12px', marginLeft: 6 }}>
                      {qa.question}
                    </Text>
                  </div>
                  <div>
                    <Text style={{ color: '#059669', fontSize: '12px', fontWeight: '500' }}>
                      A:
                    </Text>
                    <Text style={{ color: '#1f2937', fontSize: '12px', marginLeft: 6, fontWeight: '500' }}>
                      {qa.answer}
                    </Text>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 补充说明 */}
        {supplement && (
          <div>
            <Text style={{ color: '#374151', fontSize: '12px', fontWeight: '500', display: 'block', marginBottom: '6px' }}>
              {t('analysis:intentConfirmation.supplementNote')}
            </Text>
            <div style={{
              background: '#f0fdf4',
              padding: '8px 12px',
              borderRadius: 6,
              border: '1px solid #dcfce7',
              borderLeft: '3px solid #10b981'
            }}>
              <Text style={{ color: '#1f2937', fontSize: '12px', lineHeight: '1.4', whiteSpace: 'pre-line' }}>
                {supplement}
              </Text>
            </div>
          </div>
        )}
      </div>
    );
  };

  // 添加函数字符串转换功能
  const reviveEchartsFunctions = (obj: any): any => {
    if (Array.isArray(obj)) {
      return obj.map(reviveEchartsFunctions);
    } else if (obj && typeof obj === 'object') {
      // 检查是否是特殊的函数对象格式 {"__function__": "函数体"}
      if (obj.__function__ && typeof obj.__function__ === 'string') {
        try {
          // eslint-disable-next-line no-new-func
          return eval('(' + obj.__function__ + ')');
        } catch (error) {
          console.warn('Failed to convert __function__ object:', error);
          return obj; // 保持原始对象，避免破坏整个配置
        }
      }

      const result = { ...obj };
      for (const key in result) {
        if (
          typeof result[key] === 'string' &&
          result[key].trim().startsWith('function')
        ) {
          try {
            // eslint-disable-next-line no-new-func
            result[key] = eval('(' + result[key] + ')');
          } catch (error) {
            console.warn(`Failed to convert function string for key ${key}:`, error);
            // 保持原始字符串，避免破坏整个配置
          }
        } else if (typeof result[key] === 'object') {
          result[key] = reviveEchartsFunctions(result[key]);
        }
      }
      return result;
    }
    return obj;
  };

    return (
    <div className="analysis-container" style={{ height: '100%', background: '#ffffff', minHeight: '100vh' }}>
      {/* 主要内容区域 */}
      <div 
        ref={analysisContainerRef}
        style={{ 
          height: '100%',
          overflow: 'auto',
          padding: '24px',
          minHeight: 'calc(100vh - 48px)'
        }}
      >
          {!selectedProjectId ? (
            <Alert
              message={t('analysis:errors.noProject')}
              description={t('analysis:errors.noProjectDescription')}
              type="warning"
              showIcon
            />
          ) : (
            <>

              
              {/* 首次分析输入 - 清爽版本 */}
              {(!conversationState || conversationState.rounds.length === 0) && (
                <div style={{
                  maxWidth: '900px',
                  margin: '0 auto',
                  padding: '40px 20px'
                }}>
                  {/* 标题区域 */}
                  <div style={{ textAlign: 'center', marginBottom: 40 }}>
                    <div style={{
                      display: 'inline-flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      width: '80px',
                      height: '80px',
                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                      borderRadius: '20px',
                      marginBottom: 24,
                      boxShadow: '0 8px 32px rgba(102, 126, 234, 0.3)'
                    }}>
                      <RocketOutlined style={{
                        fontSize: 36,
                        color: 'white'
                      }} />
                    </div>
                    <h1 style={{
                      fontSize: '28px',
                      fontWeight: 'bold',
                      color: '#1a202c',
                      marginBottom: 12,
                      lineHeight: 1.2
                    }}>
                      {t('analysis:multiRound.title')}
                    </h1>
                    <p style={{
                      fontSize: 16,
                      color: '#64748b',
                      marginBottom: 0,
                      lineHeight: 1.5
                    }}>
                      {t('analysis:multiRound.welcome')} {userInfo?.username || userInfo?.name || 'User'}，{t('analysis:multiRound.welcomeTitle')}
                    </p>
                  </div>

                  {/* 输入卡片 */}
                  <Card style={{
                    background: 'white',
                    border: '1px solid #e2e8f0',
                    borderRadius: '16px',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                    padding: '8px'
                  }}>
                    <Form
                        form={form}
                        layout="vertical"
                        onFinish={handleAnalysis}
                      >
                        <Form.Item
                          name="query"
                          rules={[{ required: true, message: t('analysis:errors.queryRequired') }]}
                          style={{ marginBottom: 24 }}
                        >
                          <div style={{ position: 'relative' }}>
                            <TextArea
                              placeholder={t('analysis:multiRound.welcomeSubtitle')}
                              autoSize={{ minRows: 6, maxRows: 12 }}
                              disabled={streaming}
                              value={queryValue}
                              onChange={(e) => {
                                setQueryValue(e.target.value);
                                form.setFieldsValue({ query: e.target.value });
                              }}
                              style={{
                                fontSize: '16px',
                                borderRadius: '12px',
                                border: '2px solid #e1e5e9',
                                padding: '16px',
                                lineHeight: '1.6',
                                resize: 'none',
                                transition: 'all 0.3s ease'
                              }}
                              onFocus={(e) => {
                                e.target.style.borderColor = '#667eea';
                                e.target.style.boxShadow = '0 0 0 3px rgba(102, 126, 234, 0.1)';
                              }}
                              onBlur={(e) => {
                                e.target.style.borderColor = '#e1e5e9';
                                e.target.style.boxShadow = 'none';
                              }}
                            />

                          </div>
                        </Form.Item>



                        <div style={{
                          display: 'flex',
                          justifyContent: 'center',
                          gap: '16px',
                          alignItems: 'center'
                        }}>
                          <Button
                            type="primary"
                            icon={<RocketOutlined />}
                            htmlType="submit"
                            loading={loading}
                            disabled={streaming}
                            size="large"
                            style={{
                              minWidth: 140,
                              height: 48,
                              borderRadius: '12px',
                              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                              border: 'none',
                              fontSize: '16px',
                              fontWeight: 'bold',
                              boxShadow: '0 4px 16px rgba(102, 126, 234, 0.4)'
                            }}
                          >
                            {t('analysis:multiRound.startAnalysis')}
                          </Button>
                          
                          {/* 测试结构化报告按钮 */}
                          <Button
                            type="default"
                            onClick={() => {
                              const testReport = {
                                "components": [
                                  {
                                    "type": "header",
                                    "data": {
                                      "title": "测试结构化报告",
                                      "subtitle": "验证新的报告渲染系统",
                                      "generated_at": new Date().toLocaleString('zh-CN'),
                                      "tags": ["测试", "结构化报告"]
                                    }
                                  },
                                  {
                                    "type": "kpi_grid",
                                    "data": {
                                      "title": "核心指标",
                                      "metrics": [
                                        {
                                          "label": "总收入",
                                          "value": "¥ 100万",
                                          "change": "+10%",
                                          "change_type": "increase"
                                        },
                                        {
                                          "label": "用户数",
                                          "value": "5,000",
                                          "change": "+5%",
                                          "change_type": "increase"
                                        }
                                      ]
                                    }
                                  },
                                  {
                                    "type": "executive_summary",
                                    "data": {
                                      "title": "执行摘要",
                                      "content": "这是一个测试报告，用于验证新的结构化报告渲染系统是否正常工作。"
                                    }
                                  }
                                ]
                              };
                              setFinalReport(JSON.stringify(testReport));
                              message.success('已生成测试结构化报告');
                            }}
                            style={{
                              marginLeft: '12px',
                              borderRadius: '12px',
                              border: '1px solid #52c41a',
                              color: '#52c41a',
                              fontWeight: '500'
                            }}
                          >
                            📊 测试新报告
                          </Button>
                        </div>


                      </Form>
                    </Card>

                    {/* 新的模板区域 */}
                    <Card style={{ marginTop: 24, borderRadius: '16px', border: '1px solid #e2e8f0' }}>
                      <div style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        marginBottom: '16px'
                      }}>
                        <div style={{
                          fontSize: '18px',
                          fontWeight: '600',
                          color: '#1f2937'
                        }}>
                          📝 {t('analysis:templates.title')}
                        </div>
                        <Button
                          type="default"
                          icon={<StarOutlined />}
                          onClick={() => setShowTemplatePanel(true)}
                          style={{
                            borderRadius: '8px',
                            border: '1px solid #667eea',
                            color: '#667eea',
                            fontSize: '14px',
                            fontWeight: '500'
                          }}
                        >
                          {t('analysis:templates.moreTemplates')}
                        </Button>
                      </div>
                      <Tabs
                        defaultActiveKey="personal"
                        centered
                        items={[
                          {
                            key: 'personal',
                            label: (
                              <span style={{ fontSize: '16px', fontWeight: '600' }}>
                                <UserOutlined style={{ marginRight: 8, color: '#10b981' }} />
                                {t('analysis:templates.personalTemplates')}
                              </span>
                            ),
                            children: (
                              <div style={{ padding: '20px 0' }}>
                                {personalTemplates.length > 0 ? (
                                  <div style={{
                                    display: 'grid',
                                    gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
                                    gap: '16px',
                                    maxWidth: '1200px',
                                    margin: '0 auto'
                                  }}>
                                    {personalTemplates.map((template) => (
                                      <div
                                        key={template.templateId}
                                        onClick={async () => {
                                          console.log('点击个人模板:', template.templateName, template.templateContent);

                                          // 记录模板使用（本地存储）
                                          const userId = userInfo?.id?.toString() || userInfo?.username || 'anonymous';
                                          personalTemplateStorage.recordTemplateUsage(
                                            userId,
                                            selectedProjectId!,
                                            template.templateId,
                                            template.templateName,
                                            template.templateContent,
                                            template.templateDescription,
                                            template.category
                                          );

                                          // 记录模板使用（数据库统计）- 只有非默认模板才记录到数据库
                                          if (!template.templateId.startsWith('default_')) {
                                            try {
                                              await TemplateApi.recordTemplateUsage(template.templateId);
                                            } catch (error) {
                                              console.error('记录数据库使用次数失败:', error);
                                            }
                                          }

                                          // 直接更新状态
                                          setQueryValue(template.templateContent);
                                          form.setFieldsValue({ query: template.templateContent });

                                          console.log('设置表单值后:', form.getFieldsValue());
                                          message.success(t('analysis:templates.templateApplied', { name: template.templateName }));

                                          // 刷新个人模板列表
                                          await refreshPersonalTemplates();

                                          // 聚焦到输入框
                                          setTimeout(() => {
                                            const textarea = document.querySelector('textarea[placeholder*="请输入您的分析问题"]') as HTMLTextAreaElement;
                                            if (textarea) {
                                              textarea.focus();
                                              textarea.setSelectionRange(textarea.value.length, textarea.value.length);
                                            }
                                          }, 100);
                                        }}
                                        style={{
                                          padding: '20px',
                                          borderRadius: '12px',
                                          border: '1px solid #e2e8f0',
                                          background: 'white',
                                          cursor: 'pointer',
                                          transition: 'all 0.3s ease',
                                          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)'
                                        }}
                                        onMouseEnter={(e) => {
                                          e.currentTarget.style.borderColor = '#667eea';
                                          e.currentTarget.style.transform = 'translateY(-2px)';
                                          e.currentTarget.style.boxShadow = '0 8px 25px rgba(102, 126, 234, 0.15)';
                                        }}
                                        onMouseLeave={(e) => {
                                          e.currentTarget.style.borderColor = '#e2e8f0';
                                          e.currentTarget.style.transform = 'translateY(0)';
                                          e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.06)';
                                        }}
                                      >
                                        <div style={{
                                          fontSize: '18px',
                                          fontWeight: '600',
                                          color: '#1f2937',
                                          marginBottom: '8px'
                                        }}>
                                          {template.templateName}
                                        </div>
                                        <div style={{
                                          fontSize: '14px',
                                          color: '#6b7280',
                                          marginBottom: '12px',
                                          lineHeight: '1.5'
                                        }}>
                                          {template.templateDescription || '个人常用模板'}
                                        </div>
                                        <div style={{
                                          display: 'flex',
                                          justifyContent: 'space-between',
                                          alignItems: 'center'
                                        }}>
                                          <div style={{
                                            display: 'flex',
                                            gap: '8px',
                                            alignItems: 'center'
                                          }}>
                                            {template.category && (
                                              <span style={{
                                                fontSize: '12px',
                                                color: '#667eea',
                                                background: 'rgba(102, 126, 234, 0.1)',
                                                padding: '2px 8px',
                                                borderRadius: '6px'
                                              }}>
                                                {template.category}
                                              </span>
                                            )}
                                          </div>
                                          <span style={{
                                            fontSize: '12px',
                                            color: '#9ca3af'
                                          }}>
                                            📊 {template.usageCount} 次使用
                                          </span>
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                ) : (
                                  <div style={{
                                    textAlign: 'center',
                                    color: '#6b7280',
                                    fontSize: '16px',
                                    padding: '40px 0'
                                  }}>
                                    <div style={{ marginBottom: '16px', fontSize: '48px' }}>📝</div>
                                    <div>{t('analysis:templates.noPersonalTemplates')}</div>
                                    <div style={{ fontSize: '14px', marginTop: '8px' }}>
                                      {t('analysis:templates.noPersonalTemplatesDesc')}
                                    </div>
                                  </div>
                                )}
                              </div>
                            )
                          },
                          {
                            key: 'hot',
                            label: (
                              <span style={{ fontSize: '16px', fontWeight: '600' }}>
                                <StarOutlined style={{ marginRight: 8, color: '#f59e0b' }} />
                                {t('analysis:templates.hotTemplates')}
                              </span>
                            ),
                            children: (
                              <div style={{ padding: '20px 0' }}>
                                {hotTemplates.length > 0 ? (
                                  <div style={{
                                    display: 'grid',
                                    gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
                                    gap: '16px',
                                    maxWidth: '1200px',
                                    margin: '0 auto'
                                  }}>
                                    {hotTemplates.slice(0, 6).map((template) => (
                                      <div
                                        key={template.id}
                                        onClick={async () => {
                                          console.log('点击模板:', template.name, template.content);

                                          // 记录模板使用（本地存储）
                                          const userId = userInfo?.id?.toString() || userInfo?.username || 'anonymous';
                                          personalTemplateStorage.recordTemplateUsage(
                                            userId,
                                            selectedProjectId!,
                                            template.id,
                                            template.name,
                                            template.content,
                                            template.description,
                                            template.category
                                          );

                                          // 记录模板使用（数据库统计）
                                          try {
                                            await TemplateApi.recordTemplateUsage(template.id);
                                          } catch (error) {
                                            console.error('记录数据库使用次数失败:', error);
                                          }

                                          // 直接更新状态
                                          setQueryValue(template.content);
                                          form.setFieldsValue({ query: template.content });

                                          console.log('设置表单值后:', form.getFieldsValue());
                                          message.success(t('analysis:templates.templateApplied', { name: template.name }));

                                          // 刷新个人模板列表
                                          await refreshPersonalTemplates();

                                          // 聚焦到输入框
                                          setTimeout(() => {
                                            const textarea = document.querySelector('textarea[placeholder*="请输入您的分析问题"]') as HTMLTextAreaElement;
                                            if (textarea) {
                                              textarea.focus();
                                              textarea.setSelectionRange(textarea.value.length, textarea.value.length);
                                            }
                                          }, 100);
                                        }}
                                        style={{
                                          padding: '20px',
                                          borderRadius: '12px',
                                          border: '1px solid #e2e8f0',
                                          background: 'white',
                                          cursor: 'pointer',
                                          transition: 'all 0.3s ease',
                                          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)'
                                        }}
                                        onMouseEnter={(e) => {
                                          e.currentTarget.style.borderColor = '#10b981';
                                          e.currentTarget.style.transform = 'translateY(-2px)';
                                          e.currentTarget.style.boxShadow = '0 8px 25px rgba(16, 185, 129, 0.15)';
                                        }}
                                        onMouseLeave={(e) => {
                                          e.currentTarget.style.borderColor = '#e2e8f0';
                                          e.currentTarget.style.transform = 'translateY(0)';
                                          e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.06)';
                                        }}
                                      >
                                        <div style={{
                                          fontSize: '18px',
                                          fontWeight: '600',
                                          color: '#1f2937',
                                          marginBottom: '8px'
                                        }}>
                                          {template.name}
                                        </div>
                                        <div style={{
                                          fontSize: '14px',
                                          color: '#6b7280',
                                          marginBottom: '12px',
                                          lineHeight: '1.5'
                                        }}>
                                          {template.description}
                                        </div>
                                        <div style={{
                                          display: 'flex',
                                          justifyContent: 'space-between',
                                          alignItems: 'center'
                                        }}>
                                          <div style={{
                                            display: 'flex',
                                            gap: '8px',
                                            alignItems: 'center'
                                          }}>
                                            {template.category && (
                                              <span style={{
                                                fontSize: '12px',
                                                color: '#10b981',
                                                background: 'rgba(16, 185, 129, 0.1)',
                                                padding: '2px 8px',
                                                borderRadius: '6px'
                                              }}>
                                                {template.category}
                                              </span>
                                            )}
                                            {template.category && (
                                              <span style={{
                                                fontSize: '12px',
                                                color: '#667eea',
                                                background: 'rgba(102, 126, 234, 0.1)',
                                                padding: '2px 8px',
                                                borderRadius: '6px'
                                              }}>
                                                {template.category}
                                              </span>
                                            )}
                                            {template.complexity_level && (
                                              <span style={{
                                                fontSize: '12px',
                                                color: '#f59e0b',
                                                background: 'rgba(245, 158, 11, 0.1)',
                                                padding: '2px 8px',
                                                borderRadius: '6px'
                                              }}>
                                                {template.complexity_level === 'basic' ? '基础' :
                                                 template.complexity_level === 'intermediate' ? '中级' : '高级'}
                                              </span>
                                            )}
                                          </div>
                                          <span style={{
                                            fontSize: '12px',
                                            color: '#9ca3af'
                                          }}>
                                            🔥 {template.usage_count} 次使用
                                          </span>
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                ) : (
                                  <div style={{
                                    textAlign: 'center',
                                    color: '#6b7280',
                                    fontSize: '16px',
                                    padding: '40px 0'
                                  }}>
                                    <div style={{ marginBottom: '16px', fontSize: '48px' }}>🔥</div>
                                    <div>{t('analysis:templates.noHotTemplates')}</div>
                                    <div style={{ fontSize: '14px', marginTop: '8px' }}>
                                      {t('analysis:templates.noHotTemplatesDesc')}
                                    </div>
                                  </div>
                                )}
                              </div>
                            )
                          }
                        ]}
                      />
                    </Card>
                  </div>
              )}
              
              {/* 当前分析内容 */}
              {renderCurrentAnalysis()}
              
              {/* 错误信息 */}
              {error && (
                <Alert 
                  message={t('analysis:errors.analysisError', { defaultValue: '分析出错' })} 
                  description={error} 
                  type="error" 
                  showIcon 
                  style={{ marginTop: 16 }}
                />
              )}
              
              {/* 继续分析输入 - 美化版本 */}
              {showContinueInput && (
                <Card
                  className="continue-input-card"
                  style={{
                    marginTop: 24,
                    position: 'sticky',
                    bottom: 20,
                    zIndex: 100,
                  }}
                  title={
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <div style={{
                          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                          borderRadius: '8px',
                          width: '32px',
                          height: '32px',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          marginRight: 12
                        }}>
                          <SendOutlined style={{ color: 'white', fontSize: '16px' }} />
                        </div>
                        <div>
                          <div style={{ fontSize: '16px', fontWeight: 'bold', color: '#333' }}>
                            继续对话
                          </div>
                          <div style={{ fontSize: '12px', color: '#666', marginTop: 2 }}>
                            基于前面的分析结果继续提问
                          </div>
                        </div>
                      </div>
                      {/* 显示会话上下文信息 */}
                      {conversationState && (
                        <div style={{
                          background: 'rgba(102, 126, 234, 0.1)',
                          padding: '4px 12px',
                          borderRadius: '12px',
                          fontSize: '12px',
                          color: '#667eea',
                          fontWeight: '500'
                        }}>
                          第 {conversationState.rounds.length + 1} 轮对话
                        </div>
                      )}
                    </div>
                  }
                  extra={
                    conversationState && conversationState.rounds.length > 0 && (
                      <span style={{
                        color: '#52c41a',
                        fontSize: '12px',
                        background: 'rgba(82, 196, 26, 0.1)',
                        padding: '2px 8px',
                        borderRadius: '8px',
                        fontWeight: '500'
                      }}>
                        🧠 智能上下文感知
                      </span>
                    )
                  }
                >
                  <Form
                    form={form}
                    layout="vertical"
                    onFinish={handleAnalysis}
                  >
                    <Form.Item
                      name="query"
                      rules={[{ required: true, message: '请输入您的问题' }]}
                      style={{ marginBottom: 16 }}
                    >
                      <div style={{ position: 'relative' }}>
                        <TextArea
                          placeholder="基于前面的分析结果，您还想了解什么？AI会智能关联历史上下文..."
                          autoSize={{ minRows: 3, maxRows: 6 }}
                          disabled={streaming}
                          style={{
                            fontSize: '15px',
                            borderRadius: '12px',
                            border: '2px solid #e1e5e9',
                            padding: '12px 16px',
                            lineHeight: '1.6',
                            resize: 'none',
                            transition: 'all 0.3s ease'
                          }}
                          onFocus={(e) => {
                            e.target.style.borderColor = '#667eea';
                            e.target.style.boxShadow = '0 0 0 3px rgba(102, 126, 234, 0.1)';
                          }}
                          onBlur={(e) => {
                            e.target.style.borderColor = '#e1e5e9';
                            e.target.style.boxShadow = 'none';
                          }}
                        />
                        {/* 继续对话的模板入口 */}
                        <div style={{
                          position: 'absolute',
                          top: '8px',
                          right: '8px',
                          display: 'flex',
                          gap: '6px'
                        }}>
                          <Button
                            type="text"
                            size="small"
                            icon={<span>📝</span>}
                            onClick={() => setShowTemplatePanel(true)}
                            style={{
                              background: 'rgba(102, 126, 234, 0.1)',
                              color: '#667eea',
                              border: '1px solid rgba(102, 126, 234, 0.2)',
                              borderRadius: '6px',
                              fontSize: '11px',
                              height: '24px',
                              padding: '0 6px'
                            }}
                            title="使用分析模板"
                          >
                            模板
                          </Button>
                        </div>
                      </div>
                    </Form.Item>
                    
                    <Form.Item style={{ marginBottom: 0 }}>
                      <div style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        padding: '16px 0 8px 0'
                      }}>
                        <Space size="middle">
                          <Button
                            type="primary"
                            icon={<SendOutlined />}
                            htmlType="submit"
                            loading={streaming}
                            disabled={streaming}
                            size="large"
                            style={{
                              borderRadius: '10px',
                              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                              border: 'none',
                              fontWeight: 'bold',
                              minWidth: '120px',
                              height: '42px',
                              boxShadow: '0 4px 12px rgba(102, 126, 234, 0.3)'
                            }}
                          >
                            {t('analysis:multiRound.continueAnalysis', { defaultValue: '继续分析' })}
                          </Button>
                          <Button
                            onClick={() => setShowContinueInput(false)}
                            disabled={streaming}
                            size="large"
                            style={{
                              borderRadius: '10px',
                              border: '2px solid #e1e5e9',
                              color: '#666',
                              fontWeight: '500',
                              minWidth: '80px',
                              height: '42px'
                            }}
                          >
                            {t('common:buttons.cancel', { defaultValue: '取消' })}
                          </Button>

                        </Space>
                        <div style={{
                          color: '#999',
                          fontSize: '13px',
                          background: 'rgba(102, 126, 234, 0.05)',
                          padding: '6px 12px',
                          borderRadius: '8px',
                          display: 'flex',
                          alignItems: 'center',
                          gap: '6px'
                        }}>
                          <BulbOutlined style={{ color: '#667eea' }} />
                          AI会智能关联历史上下文
                        </div>
                      </div>
                    </Form.Item>
                  </Form>
                </Card>
              )}
            </>
          )}
      </div>
      

      
      {/* 图表展示弹窗 */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <EyeOutlined style={{ marginRight: 8, color: '#1890ff' }} />
            <span>
              {(() => {
                // 🔥 智能检测标题类型
                const charts = chartData?.data?.charts || [];
                const isMultiChart = chartData?.data?.chart_type === 'multi_charts' || 
                                   chartData?.data?.display_format === 'multi_chart' ||
                                   chartData?.display_format === 'multi_chart' ||
                                   charts.length > 1;
                
                if (isMultiChart) {
                  return t('analysis:charts.multipleCharts', { 
                    count: charts.length || 0, 
                    defaultValue: `图表展示 - 共 ${charts.length || 0} 个图表` 
                  });
                } else {
                  // 检查是否为表格
                  const isTable = chartData?.data?.chart_type === 'table' || 
                                 chartData?.data?.charts?.[0]?.chart_type === 'table' ||
                                 (chartData?.data?.chart_config || chartData?.data?.charts?.[0]?.config)?.columns;
                  
                  return isTable 
                    ? t('analysis:charts.tableDisplay', { defaultValue: '表格展示' })
                    : t('analysis:charts.chartDisplay', { defaultValue: '图表展示' });
                }
              })()}
            </span>
          </div>
        }
        width="95%"
        style={{ top: 20 }}
        open={chartModalVisible}
        onCancel={() => setChartModalVisible(false)}
        footer={(() => {
          // 🔥 智能检测是否为表格，表格不显示下载按钮
          const isTableOnly = (() => {
            // 检查是否为单表格
            if (chartData?.data?.chart_type === 'table' || 
                chartData?.data?.charts?.[0]?.chart_type === 'table') {
              return true;
            }
            
            // 检查是否为多图表中只有一个表格
            const charts = chartData?.data?.charts || [];
            if (charts.length === 1 && charts[0]?.chart_type === 'table') {
              return true;
            }
            
            // 检查配置中是否只有表格字段
            const chartConfig = chartData?.data?.chart_config || chartData?.data?.charts?.[0]?.config;
            if (chartConfig?.columns && !chartConfig?.series && !chartConfig?.dataset) {
              return true;
            }
            
            return false;
          })();
          
          const buttons = [
            <Button key="close" onClick={() => setChartModalVisible(false)}>
              {t('common:buttons.close', { defaultValue: '关闭' })}
            </Button>
          ];
          
          // 只有非表格类型才显示下载按钮
          if (!isTableOnly) {
            buttons.push(
              <Button 
                key="download" 
                type="primary" 
                onClick={() => downloadChart(chartData)}
              >
                {t('analysis:charts.downloadChart', { defaultValue: '下载图表' })}
              </Button>
            );
          }
          
          return buttons;
        })()}
        bodyStyle={{ padding: '24px' }}
      >
        {chartData && (
          <div style={{ minHeight: '500px' }}>
            {/* 🔥 调试：弹窗数据结构检测 */}
            {(() => {
              const chartType = chartData?.data?.chart_type || chartData?.data?.charts?.[0]?.chart_type;
              const chartConfig = chartData?.data?.chart_config || chartData?.data?.charts?.[0]?.config;
              const isTable = chartType === 'table' || !!chartConfig?.columns;
              
              console.log('🔍 弹窗数据结构检测:', {
                chartType,
                hasColumns: !!chartConfig?.columns,
                hasSeries: !!chartConfig?.series,
                hasDataset: !!chartConfig?.dataset,
                isTable,
                shouldShowDownload: !isTable,
                chartData: chartData
              });
              
              return null;
            })()}

            {/* 检查是否为多图表结构 - 支持新的MoE多图表格式 */}
            {((chartData.data?.chart_type === 'multi_charts' && chartData.data?.charts) ||
              (chartData.data?.display_format === 'multi_chart' && chartData.data?.charts) ||
              (chartData.data?.total_charts && chartData.data?.total_charts > 1 && chartData.data?.charts) ||
              // 🔥 新增：支持display_format在chartData根级别（修复后的数据结构）
              (chartData.display_format === 'multi_chart' && chartData.data?.charts)) ? (
              <Space direction="vertical" style={{ width: '100%' }} size="large">
                {/* 多图表总体信息 */}
                <Card 
                  size="small"
                  style={{ 
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', 
                    border: 'none',
                    borderRadius: 12,
                    color: 'white'
                  }}
                  bodyStyle={{ padding: '20px' }}
                >
                  <Row gutter={[24, 16]}>
                    <Col xs={24} md={12}>
                      <div>
                        <Text strong style={{ fontSize: '18px', color: 'white', display: 'block', marginBottom: 8 }}>
                          📊 多图表分析
                        </Text>
                        <Text style={{ fontSize: '14px', color: 'rgba(255,255,255,0.9)' }}>
                          {t('analysis:tools.chartsGenerated', { count: chartData.data.charts.length })}
                        </Text>
                      </div>
                    </Col>
                    <Col xs={24} md={12}>
                      <div style={{ textAlign: 'right' }}>
                        {chartData.data.user_query && (
                          <div>
                            <Text style={{ fontSize: '13px', color: 'rgba(255,255,255,0.8)', display: 'block' }}>
                              查询：{chartData.data.user_query}
                            </Text>
                          </div>
                        )}
                        {chartData.data.generated_reason && (
                          <div>
                            <Text style={{ fontSize: '12px', color: 'rgba(255,255,255,0.7)' }}>
                              {chartData.data.generated_reason}
                            </Text>
                          </div>
                        )}
                      </div>
                    </Col>
                  </Row>
                </Card>

                {/* 多图表展示 */}
                <div style={{ display: 'grid', gap: '24px' }}>
                  {chartData.data.charts.map((chart: any, index: number) => {
                    // 支持新的MoE格式：chart.config 或 chart.chart_config
                    const chartConfig = chart.config || chart.chart_config;
                    const chartType = chart.chart_type || 'unknown';
                    const chartTitle = chart.title || `图表 ${index + 1}`;
                    const chartDescription = chart.description || '';
                    const chartPriority = chart.priority || (index + 1);
                    
                    // 🔥 调试日志：表格渲染详情
                    console.log(`🔍 模态框图表 ${index + 1} 渲染详情:`, {
                      chartType,
                      hasChartConfig: !!chartConfig,
                      hasColumns: !!chartConfig?.columns,
                      hasDataSource: !!chartConfig?.dataSource,
                      columnsLength: chartConfig?.columns?.length || 0,
                      dataSourceLength: chartConfig?.dataSource?.length || 0,
                      canRenderTable: chartType === 'table' && chartConfig && chartConfig.columns
                    });

                    return (
                      <Card 
                        key={`modal-chart-${index}`}
                        title={
                          <Space>
                            <Tag color="blue">图表 {index + 1}</Tag>
                            <Text strong style={{ fontSize: '16px' }}>{chartTitle}</Text>
                            <Tag color="green" style={{ fontSize: '11px' }}>{chartType}</Tag>
                            {chartPriority && (
                              <Tag
                                color={chartPriority === 1 ? 'gold' : chartPriority === 2 ? 'orange' : 'default'}
                                style={{ fontSize: '11px' }}
                              >
                                优先级 {chartPriority}
                              </Tag>
                            )}
                          </Space>
                        }
                        style={{ 
                          borderRadius: 12,
                          boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                          border: '1px solid #f0f0f0'
                        }}
                        bodyStyle={{ padding: '20px' }}
                      >
                        {/* 图表描述 */}
                        {chartDescription && (
                          <div style={{ marginBottom: '16px', padding: '12px', background: '#f8f9fa', borderRadius: 8 }}>
                            <Text style={{ fontSize: '14px', color: '#666' }}>
                              {chartDescription}
                            </Text>
                          </div>
                        )}

                        {/* 图表显示区域 */}
                        <div style={{ 
                          background: 'white',
                          borderRadius: 8,
                          padding: '16px',
                          minHeight: '500px',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          border: '1px solid #f0f0f0'
                        }}>
                          {chartType === 'table' && chartConfig && chartConfig.columns ? (
                            // 渲染表格
                            (() => {
                              console.log(`✅ 模态框表格 ${index + 1} 开始渲染:`, {
                                columns: chartConfig.columns,
                                dataSource: chartConfig.dataSource,
                                columnsCount: chartConfig.columns?.length,
                                dataCount: chartConfig.dataSource?.length
                              });
                              return (
                                <div style={{ width: '100%' }}>
                                  {/* 🔥 多图表中的表格标题显示 */}
                                  <div style={{ 
                                    marginBottom: '16px', 
                                    textAlign: 'center',
                                    borderBottom: '2px solid #f0f0f0',
                                    paddingBottom: '12px'
                                  }}>
                                    <Text strong style={{ 
                                      fontSize: '18px', 
                                      color: '#2c3e50',
                                      display: 'block'
                                    }}>
                                      {chartTitle}
                                    </Text>
                                    {chart.description && (
                                      <Text type="secondary" style={{ 
                                        fontSize: '13px',
                                        marginTop: '4px',
                                        display: 'block'
                                      }}>
                                        {chart.description}
                                      </Text>
                                    )}
                                  </div>
                                  
                                  <Table
                                    columns={chartConfig.columns}
                                    dataSource={chartConfig.dataSource || []}
                                    pagination={chartConfig.pagination || false}
                                    size={chartConfig.size || 'small'}
                                    bordered={chartConfig.bordered !== false}
                                    scroll={chartConfig.scroll || { x: true }}
                                    style={{ width: '100%' }}
                                  />
                                </div>
                              );
                            })()
                          
                          ) : chartConfig && (chartConfig.series || chartConfig.dataset) ? (
                            <ReactECharts 
                              option={reviveEchartsFunctions({
                                ...chartConfig,
                                // 通用的响应式和优化配置
                                grid: {
                                  ...chartConfig?.grid,
                                  left: '10%',
                                  right: '10%',
                                  top: '20%',
                                  bottom: '20%',
                                  containLabel: true
                                },
                                legend: {
                                  ...chartConfig?.legend,
                                  top: chartConfig?.legend?.top || '12%',
                                  type: 'scroll',
                                  orient: 'horizontal'
                                },
                                tooltip: {
                                  ...chartConfig?.tooltip,
                                  trigger: chartConfig?.tooltip?.trigger || 'item',
                                  confine: true,
                                  backgroundColor: 'rgba(0,0,0,0.8)',
                                  borderColor: 'rgba(0,0,0,0.8)',
                                  textStyle: {
                                    color: '#fff',
                                    fontSize: 12
                                  }
                                },
                                title: {
                                  ...chartConfig?.title,
                                  left: 'center',
                                  top: '2%',
                                  textStyle: {
                                    fontSize: 16,
                                    fontWeight: 'bold',
                                    ...chartConfig?.title?.textStyle
                                  }
                                },
                                toolbox: {
                                  ...chartConfig?.toolbox,
                                  show: chartConfig?.toolbox?.show !== false,
                                  orient: 'horizontal',
                                  left: 'center',
                                  bottom: '2%',
                                  feature: {
                                    saveAsImage: { 
                                      show: true,
                                      title: '保存为图片'
                                    },
                                    magicType: {
                                      show: true,
                                      title: {
                                        line: '切换为折线图',
                                        bar: '切换为柱状图'
                                      },
                                      type: ['line', 'bar']
                                    },
                                    restore: { 
                                      show: true,
                                      title: '还原'
                                    },
                                    ...chartConfig?.toolbox?.feature
                                  }
                                }
                              })}
                              style={{ 
                                height: '500px', 
                                width: '100%',
                                minHeight: '400px'
                              }}
                              opts={{ 
                                renderer: 'canvas'
                              }}
                              notMerge={true}
                              lazyUpdate={true}
                              onChartReady={(chart: any) => {
                                const resizeChart = () => {
                                  if (chart && !chart.isDisposed()) {
                                    chart.resize();
                                  }
                                };
                                window.addEventListener('resize', resizeChart);
                                setTimeout(resizeChart, 100);
                                
                                return () => {
                                  window.removeEventListener('resize', resizeChart);
                                };
                              }}
                            />
                          ) : (
                            (() => {
                              console.log(`❌ 模态框图表 ${index + 1} 渲染失败，原因分析:`, {
                                chartType,
                                hasChartConfig: !!chartConfig,
                                hasColumns: !!chartConfig?.columns,
                                hasDataSource: !!chartConfig?.dataSource,
                                hasSeries: !!chartConfig?.series,
                                hasDataset: !!chartConfig?.dataset,
                                tableCondition: chartType === 'table' && chartConfig && chartConfig.columns,
                                echartsCondition: chartConfig && (chartConfig.series || chartConfig.dataset)
                              });
                              return (
                                <Alert 
                                  message={t('analysis:tools.chartConfigInvalidWithIndex', { index: index + 1 })}
                                  description={`缺少有效的图表配置数据 - 类型: ${chartType}, 配置: ${!!chartConfig}, 列: ${!!chartConfig?.columns}`}
                                  type="warning" 
                                  showIcon 
                                />
                              );
                            })()
                          )}
                        </div>

                        {/* 配置详情（可折叠） */}
                        <div style={{ marginTop: '16px' }}>
                          <Collapse 
                            ghost 
                            size="small"
                            style={{ background: 'transparent' }}
                          >
                            <Collapse.Panel 
                              header={
                                <Space>
                                  <CodeOutlined style={{ color: '#1890ff' }} />
                                  <Text style={{ fontSize: '13px', fontWeight: 500 }}>
                                    {t('analysis:tools.viewChartConfig', { index: index + 1 })}
                                  </Text>
                                </Space>
                              } 
                              key={`modal-config-${index}`}
                            >
                              <div style={{ 
                                background: '#f5f5f5', 
                                padding: 12, 
                                borderRadius: 6,
                                border: '1px solid #d9d9d9'
                              }}>
                                <pre style={{ 
                                  margin: 0, 
                                  fontSize: '11px',
                                  maxHeight: '300px',
                                  overflow: 'auto',
                                  whiteSpace: 'pre-wrap',
                                  lineHeight: 1.3
                                }}>
                                  {JSON.stringify(chartConfig, null, 2)}
                                </pre>
                              </div>
                            </Collapse.Panel>
                          </Collapse>
                        </div>
                      </Card>
                    );
                  })}
                </div>
              </Space>
            ) : (chartData.data?.chart_config || (chartData.data?.total_charts === 1 && chartData.data?.charts?.[0]?.config)) ? (
              // 单图表展示

              <Space direction="vertical" style={{ width: '100%' }} size="large">
                {/* 图表基本信息 */}
                <Card 
                  size="small"
                  style={{ 
                    background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)', 
                    border: '1px solid #e8eaec',
                    borderRadius: 8
                  }}
                >
                  <Row gutter={[24, 16]}>
                    <Col xs={24} md={8}>
                      <div style={{ textAlign: 'center' }}>
                        <Text strong style={{ fontSize: '14px', display: 'block', marginBottom: 8 }}>
                          {t('analysis:tools.chartType')}
                        </Text>
                        <Tag 
                          color="blue" 
                          style={{ 
                            fontSize: '13px', 
                            padding: '4px 12px',
                            borderRadius: 6
                          }}
                        >
                          {(() => {
                            // 🔥 智能检测图表类型
                            const detectedType = chartData.data.chart_type || chartData.data.charts?.[0]?.chart_type;
                            const chartConfig = chartData.data.chart_config || chartData.data.charts?.[0]?.config;
                            
                            // 如果明确标记为table或者有columns字段，显示为表格
                            if (detectedType === 'table' || chartConfig?.columns) {
                              return '表格';
                            }
                            
                            // 否则显示为图表
                            return '图表';
                          })()}
                        </Tag>
                      </div>
                    </Col>
                    {chartData.data.data_source_info && (
                      <Col xs={24} md={8}>
                        <div style={{ textAlign: 'center' }}>
                          <Text strong style={{ fontSize: '14px', display: 'block', marginBottom: 8 }}>
                            {t('analysis:tools.dataSourceInfo')}
                          </Text>
                          <Space direction="vertical" size="small">
                            <Text style={{ fontSize: '13px' }}>{chartData.data.data_source_info.type}</Text>
                            <Text type="secondary" style={{ fontSize: '12px' }}>
                              记录数: {chartData.data.data_source_info.records_count}
                            </Text>
                          </Space>
                        </div>
                      </Col>
                    )}
                    {chartData.data.generated_at && (
                      <Col xs={24} md={8}>
                        <div style={{ textAlign: 'center' }}>
                          <Text strong style={{ fontSize: '14px', display: 'block', marginBottom: 8 }}>
                            {t('analysis:tools.generationTime')}
                          </Text>
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            {new Date(chartData.data.generated_at).toLocaleString()}
                          </Text>
                        </div>
                      </Col>
                    )}
                  </Row>
                </Card>

                {/* 图表渲染区域 */}
                <Card 
                  style={{ 
                    borderRadius: 8,
                    boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                    border: '1px solid #f0f0f0'
                  }}
                  bodyStyle={{ padding: '20px' }}
                >
                  <div style={{ 
                    background: 'white',
                    borderRadius: 6,
                    minHeight: '500px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    position: 'relative'
                  }}>
                    {/* 🔥 修复：单图表分支支持表格渲染 */}
                    {(() => {
                      const chartType = chartData.data.chart_type || chartData.data.charts?.[0]?.chart_type;
                      const chartConfig = chartData.data.chart_config || chartData.data.charts?.[0]?.config;
                      console.log('📊 单图表分支最终渲染:', {
                        chartType,
                        isTable: chartType === 'table',
                        hasColumns: !!chartConfig?.columns,
                        willRenderTable: chartType === 'table' && !!chartConfig?.columns
                      });
                      return null;
                    })()}
                    {((chartData.data.chart_type || chartData.data.charts?.[0]?.chart_type) === 'table' && 
                      (chartData.data.chart_config || chartData.data.charts?.[0]?.config)?.columns) ? (
                      <div style={{ width: '100%' }}>
                                                 {/* 🔥 表格标题显示 */}
                         {(() => {
                           const chartConfig = chartData.data.chart_config || chartData.data.charts?.[0]?.config;
                           const tableTitle = chartData.data.charts?.[0]?.title || 
                                            chartConfig?.title || 
                                            chartData.data.title || 
                                            '数据表格';
                           
                           console.log('🔍 弹窗表格标题提取:', {
                             chartTitle: chartData.data.charts?.[0]?.title,
                             configTitle: chartConfig?.title,
                             dataTitle: chartData.data.title,
                             finalTitle: tableTitle,
                             chartData: chartData.data
                           });
                          
                          return (
                            <div style={{ 
                              marginBottom: '16px', 
                              textAlign: 'center',
                              borderBottom: '2px solid #f0f0f0',
                              paddingBottom: '12px'
                            }}>
                              <Text strong style={{ 
                                fontSize: '18px', 
                                color: '#2c3e50',
                                display: 'block'
                              }}>
                                {tableTitle}
                              </Text>
                              {chartData.data.charts?.[0]?.description && (
                                <Text type="secondary" style={{ 
                                  fontSize: '13px',
                                  marginTop: '4px',
                                  display: 'block'
                                }}>
                                  {chartData.data.charts?.[0]?.description}
                                </Text>
                              )}
                            </div>
                          );
                        })()}
                        
                        <Table
                          columns={(chartData.data.chart_config || chartData.data.charts?.[0]?.config)?.columns}
                          dataSource={(chartData.data.chart_config || chartData.data.charts?.[0]?.config)?.dataSource || []}
                          pagination={(chartData.data.chart_config || chartData.data.charts?.[0]?.config)?.pagination || false}
                          size={(chartData.data.chart_config || chartData.data.charts?.[0]?.config)?.size || 'small'}
                          bordered={(chartData.data.chart_config || chartData.data.charts?.[0]?.config)?.bordered !== false}
                          scroll={(chartData.data.chart_config || chartData.data.charts?.[0]?.config)?.scroll || { x: true }}
                          style={{ width: '100%' }}
                        />
                      </div>
                    ) : (
                      <ReactECharts
                      option={reviveEchartsFunctions({
                        ...(chartData.data.chart_config || chartData.data.charts?.[0]?.config),
                        grid: {
                          ...(chartData.data.chart_config || chartData.data.charts?.[0]?.config)?.grid,
                          left: '10%',
                          right: '10%',
                          top: '20%',
                          bottom: '20%',
                          containLabel: true
                        },
                        legend: {
                          ...(chartData.data.chart_config || chartData.data.charts?.[0]?.config)?.legend,
                          top: (chartData.data.chart_config || chartData.data.charts?.[0]?.config)?.legend?.top || '12%',
                          type: 'scroll',
                          orient: 'horizontal'
                        },
                        tooltip: {
                          ...(chartData.data.chart_config || chartData.data.charts?.[0]?.config)?.tooltip,
                          trigger: (chartData.data.chart_config || chartData.data.charts?.[0]?.config)?.tooltip?.trigger || 'item',
                          confine: true,
                          backgroundColor: 'rgba(0,0,0,0.8)',
                          borderColor: 'rgba(0,0,0,0.8)',
                          textStyle: {
                            color: '#fff',
                            fontSize: 12
                          }
                        },
                        title: {
                          ...(chartData.data.chart_config || chartData.data.charts?.[0]?.config)?.title,
                          left: 'center',
                          top: '2%',
                          textStyle: {
                            fontSize: 16,
                            fontWeight: 'bold',
                            ...(chartData.data.chart_config || chartData.data.charts?.[0]?.config)?.title?.textStyle
                          }
                        },
                        toolbox: {
                          ...(chartData.data.chart_config || chartData.data.charts?.[0]?.config)?.toolbox,
                          show: (chartData.data.chart_config || chartData.data.charts?.[0]?.config)?.toolbox?.show !== false,
                          orient: 'horizontal',
                          left: 'center',
                          bottom: '2%',
                          feature: {
                            saveAsImage: { 
                              show: true,
                              title: '保存为图片'
                            },
                            magicType: {
                              show: true,
                              title: {
                                line: '切换为折线图',
                                bar: '切换为柱状图'
                              },
                              type: ['line', 'bar']
                            },
                            restore: { 
                              show: true,
                              title: '还原'
                            },
                            ...(chartData.data.chart_config || chartData.data.charts?.[0]?.config)?.toolbox?.feature
                          }
                        }
                      })}
                      style={{ 
                        height: '600px', 
                        width: '100%',
                        minHeight: '500px'
                      }}
                      opts={{ 
                        renderer: 'canvas'
                      }}
                      notMerge={true}
                      lazyUpdate={true}
                      onChartReady={(chart: any) => {
                        const resizeChart = () => {
                          if (chart && !chart.isDisposed()) {
                            chart.resize();
                          }
                        };
                        window.addEventListener('resize', resizeChart);
                        setTimeout(resizeChart, 100);
                        
                        return () => {
                          window.removeEventListener('resize', resizeChart);
                        };
                      }}
                    />
                    )}
                  </div>
                </Card>

                {/* 图表配置展示（可折叠） */}
                <Card>
                  <Collapse 
                    ghost 
                    size="small"
                    style={{ background: 'transparent' }}
                  >
                    <Collapse.Panel 
                      header={
                        <Space>
                          <CodeOutlined style={{ color: '#1890ff' }} />
                          <Text style={{ fontSize: '14px', fontWeight: 500 }}>
                            {t('analysis:tools.viewChartConfigDetails')}
                          </Text>
                        </Space>
                      } 
                      key="config"
                    >
                      <div style={{ 
                        background: 'white', 
                        padding: 16, 
                        borderRadius: 6,
                        border: '1px solid #e8e8e8',
                        boxShadow: 'inset 0 1px 3px rgba(0,0,0,0.1)'
                      }}>
                        <pre style={{ 
                          margin: 0, 
                          fontSize: '12px',
                          maxHeight: '400px',
                          overflow: 'auto',
                          whiteSpace: 'pre-wrap',
                          lineHeight: 1.5,
                          color: '#333'
                        }}>
                          {JSON.stringify(chartData.data.chart_config, null, 2)}
                        </pre>
                      </div>
                    </Collapse.Panel>
                  </Collapse>
                </Card>
              </Space>
            ) : (
              (() => {
                console.log('❌ 进入最后else分支 - 无法识别图表类型:', {
                  chartData,
                  hasData: !!chartData.data,
                  hasCharts: !!chartData.data?.charts,
                  hasChartConfig: !!chartData.data?.chart_config,
                  multiChartConditions: {
                    condition1: chartData.data?.chart_type === 'multi_charts' && chartData.data?.charts,
                    condition2: chartData.data?.display_format === 'multi_chart' && chartData.data?.charts,
                    condition3: chartData.data?.total_charts && chartData.data?.total_charts > 1 && chartData.data?.charts,
                    condition4: chartData.display_format === 'multi_chart' && chartData.data?.charts
                  },
                  singleChartConditions: {
                    hasChartConfig: !!chartData.data?.chart_config,
                    hasSingleChart: chartData.data?.total_charts === 1 && chartData.data?.charts?.[0]?.config
                  }
                });
                return null;
              })(),
              <div style={{ 
                textAlign: 'center', 
                padding: 60,
                background: '#fafafa',
                borderRadius: 8,
                border: '2px dashed #d9d9d9'
              }}>
                <Alert 
                  message={t('analysis:errors.chartConfigDataInvalid')}
                  type="warning" 
                  showIcon 
                  style={{ marginBottom: 24 }}
                />
                <div>
                  <Text strong style={{ fontSize: '14px', marginBottom: 16, display: 'block' }}>
                    {t('analysis:tools.rawData')}
                  </Text>
                  <pre style={{ 
                    background: 'white', 
                    padding: 16, 
                    borderRadius: 6,
                    border: '1px solid #ffd591',
                    fontSize: '12px',
                    textAlign: 'left',
                    maxHeight: '300px',
                    overflow: 'auto'
                  }}>
                    {JSON.stringify(chartData, null, 2)}
                  </pre>
                </div>
              </div>
            )}
          </div>
        )}
      </Modal>
      

      
      {/* 悬浮的分析控制按钮 */}
      {streaming && (() => {
        // 判断是否应该显示打断按钮
        let shouldShowInterruptButton = true;
        
        // 检查是否是第一次分析（没有continue_analysis_id）
        const isFirstTimeAnalysis = !analysisSteps.some(step => 
          step.id === 'analysis_continued' || 
          step.id === 'continue_analysis' ||
          step.id === 'context_restored'
        );
        
        // 只有在第一次分析时才检查意图分析阶段
        if (isFirstTimeAnalysis) {
          // 定义意图分析阶段的步骤
          const intentAnalysisSteps = [
            'start',
            'analysis_created', 
            'tools_loaded',
            'schema_loaded',
            'intent_analysis_started',
            'intent_analyzed',
            'intent_confirmation'
          ];
          
          // 检查是否还在意图分析阶段
          const isInIntentPhase = intentAnalysisSteps.includes(currentStep) || 
            analysisSteps.some(step => step.tool_name === '意图分析' || step.tool_name === '意图确认');
          
          // 检查是否已经有实际的分析步骤（非意图分析相关）
          const hasRealAnalysisSteps = analysisSteps.some(step => 
            !intentAnalysisSteps.includes(step.id) && 
            step.tool_name !== '意图分析' && 
            step.tool_name !== '意图确认' &&
            !step.isInterrupt &&
            step.id !== 'waiting_for_planning' &&
            step.id !== 'planning' &&
            step.id !== 'planning_decision'
          );
          
          // 如果还在意图分析阶段且没有实际分析步骤，则不显示打断按钮
          shouldShowInterruptButton = !isInIntentPhase || hasRealAnalysisSteps;
        }
        
        return (
          <div 
            className="floating-control-buttons"
            style={{
              position: 'fixed',
              left: '50%',
              transform: 'translateX(-50%)',
              bottom: showContinueInput ? 200 : 24, // 如果显示继续输入框，则调整位置
              zIndex: 99, // 比继续输入框的zIndex低一点
              display: 'flex',
              gap: '16px',
              alignItems: 'center'
            }}
          >
            {/* 打断按钮 - 只在非意图分析阶段显示 */}
            {shouldShowInterruptButton && (
              <Button 
                type="primary"
                size="large"
                onClick={showInterruptModal}
                icon={<ExclamationCircleOutlined />}
                loading={isInterrupting}
                disabled={waitingForFeedback}
                style={{
                  minWidth: '120px',
                  height: '48px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontWeight: 500,
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                  backgroundColor: waitingForFeedback ? '#faad14' : '#1890ff',
                  borderColor: waitingForFeedback ? '#faad14' : '#1890ff'
                }}
              >
                {waitingForFeedback ? t('analysis:multiRound.waitingForFeedback') : t('analysis:multiRound.interruptAnalysis')}
              </Button>
            )}
            
            {/* 取消按钮 */}
            <Button 
              type="default" 
              danger
              size="large"
              onClick={cancelAnalysis}
              icon={<CloseCircleOutlined />}
              style={{
                minWidth: '120px',
                height: '48px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontWeight: 500,
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
              }}
            >
              {t('analysis:multiRound.cancelAnalysis')}
            </Button>
        </div>
        );
      })()}



      {/* 打断分析反馈弹窗（优化后：直接打断+反馈） */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <ExclamationCircleOutlined style={{ color: '#fa8c16', marginRight: 8 }} />
            {t('analysis:multiRound.interruptAnalysisAndFeedback')}
          </div>
        }
        open={feedbackModalVisible}
        onOk={submitUserFeedback}
        onCancel={async () => {
          // 优化后：取消反馈时调用取消打断API，让分析继续进行
          try {
            if (analysisId) {
              await llmAnalysisApiExtended.cancelInterruptContinueAnalysis(analysisId);
              // 静默处理，不显示成功消息
            }
          } catch (error) {
            console.error('取消打断失败:', error);
            message.warning(t('analysis:errors.operationFailed'));
          }
          
          // 重置UI状态
          setFeedbackModalVisible(false);
          setWaitingForFeedback(false);
          setCurrentInterruptId(null);
          setIsInterrupting(false);
          // 重置反馈表单
          setInterruptFeedback({
            adjustmentType: 'strategy',
            feedback: '',
            resetPlanning: false
          });
        }}
        okText={t('analysis:multiRound.submitFeedback')}
        cancelText={t('analysis:multiRound.cancel')}
        confirmLoading={loading}
        width={600}
        destroyOnClose
      >
        <div style={{ padding: '16px 0' }}>
          <Form layout="vertical">
            <Form.Item label={t('analysis:multiRound.feedbackLabel')} required>
              <TextArea
                value={interruptFeedback.feedback}
                onChange={(e) => setInterruptFeedback(prev => ({ ...prev, feedback: e.target.value }))}
                placeholder={t('analysis:errors.feedbackPlaceholder')}
                rows={4}
                maxLength={500}
                showCount
              />
            </Form.Item>
          </Form>
          
          <Alert
            message={t('analysis:multiRound.feedbackTip')}
            description={t('analysis:multiRound.feedbackDescription')}
            type="info"
            showIcon
          />
        </div>
      </Modal>

      {/* 模板选择面板 */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <FileTextOutlined style={{ color: '#667eea', marginRight: 8 }} />
            <span>{t('analysis:multiRound.selectTemplate')}</span>
          </div>
        }
        open={showTemplatePanel}
        onCancel={() => setShowTemplatePanel(false)}
        footer={null}
        width={900}
        style={{ top: 20 }}
        destroyOnClose
      >
        <TemplatePanel
          projectId={selectedProjectId}
          onSelectTemplate={handleTemplateSelect}
          onClose={() => setShowTemplatePanel(false)}
          visible={showTemplatePanel}
        />
      </Modal>

      {/* 参数化模板面板 */}
      <TemplateParameterPanel
        visible={showParameterPanel}
        template={selectedTemplate}
        onApply={handleParameterTemplateApply}
        onCancel={handleParameterTemplateCancel}
      />


    </div>
  );
};

export default MultiRoundAnalysis; 