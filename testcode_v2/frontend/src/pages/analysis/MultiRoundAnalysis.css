/* 多轮分析页面样式 */
.analysis-container {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: 100vh;
}

/* 美化输入框焦点效果 */
.ant-input:focus,
.ant-input-focused {
  border-color: #667eea !important;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2) !important;
}

/* 美化按钮悬停效果 */
.template-quick-btn {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.template-quick-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

/* 美化卡片样式 */
.analysis-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: none;
  overflow: hidden;
  transition: all 0.3s ease;
}

.analysis-card:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

/* 美化时间线样式 */
.ant-timeline-item-head {
  border-radius: 50%;
  border-width: 2px;
}

.ant-timeline-item-content {
  margin-left: 8px;
}

/* 美化步骤状态 */
.step-process {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

/* 骨架屏动画 */
@keyframes skeleton-pulse {
  0% {
    background-color: #f1f5f9;
  }
  50% {
    background-color: #e2e8f0;
  }
  100% {
    background-color: #f1f5f9;
  }
}

/* 美化模态框 */
.ant-modal-content {
  border-radius: 16px;
  overflow: hidden;
  border: 1px solid #e2e8f0;
}

.ant-modal-header {
  border-radius: 16px 16px 0 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-bottom: none;
  padding: 20px 24px;
}

.ant-modal-title {
  color: white;
  font-weight: bold;
}

.ant-modal-close {
  color: white;
}

.ant-modal-close:hover {
  color: rgba(255, 255, 255, 0.8);
}

/* 美化表单项 */
.ant-form-item-label > label {
  font-weight: 600;
  color: #374151;
}

/* 美化标签 */
.ant-tag {
  border-radius: 6px;
  font-weight: 500;
  padding: 2px 8px;
}

/* 美化Alert */
.ant-alert {
  border-radius: 12px;
  border: 1px solid;
}

.ant-alert-warning {
  background: #fefce8;
  border-color: #fde047;
}

.ant-alert-error {
  background: #fef2f2;
  border-color: #fca5a5;
}

.ant-alert-success {
  background: #f0fdf4;
  border-color: #86efac;
}

.ant-alert-info {
  background: #eff6ff;
  border-color: #93c5fd;
}

/* 美化加载状态 */
.ant-spin-dot-item {
  background-color: #667eea;
}

/* 美化滚动条 */
.analysis-container::-webkit-scrollbar {
  width: 8px;
}

.analysis-container::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.analysis-container::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
}

.analysis-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .analysis-container {
    padding: 16px;
  }
  
  .template-quick-btn {
    min-width: 60px;
    font-size: 12px;
  }
  
  .analysis-card {
    margin: 8px 0;
  }
}

/* 美化继续输入框 */
.continue-input-card {
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  background: white;
}

/* 美化浮动按钮 */
.floating-control-buttons {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
  padding: 8px;
}

.floating-control-buttons .ant-btn {
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.floating-control-buttons .ant-btn:hover {
  transform: translateY(-2px);
}

/* 美化图表容器 */
.chart-container {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

/* 美化代码块 */
pre {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

/* 美化表格 */
.ant-table {
  border-radius: 12px;
  overflow: hidden;
}

.ant-table-thead > tr > th {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
  border: none;
}

.ant-table-tbody > tr:hover > td {
  background: rgba(102, 126, 234, 0.05);
}

/* 美化步骤卡片 */
.step-card {
  border-radius: 12px;
  border: 1px solid #e1e5e9;
  background: white;
  transition: all 0.3s ease;
}

.step-card:hover {
  border-color: #667eea;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.1);
}

/* 美化意图确认卡片 */
.intent-confirmation-card {
  border-radius: 16px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
  border: 2px solid #e1e5e9;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
}

/* 美化选项按钮 */
.option-button {
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  user-select: none;
}

.option-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.option-button.selected {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: #667eea;
}

/* 美化进度指示器 */
.progress-indicator {
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
  height: 4px;
  animation: progress 2s ease-in-out infinite;
}

@keyframes progress {
  0% {
    width: 0%;
  }
  50% {
    width: 70%;
  }
  100% {
    width: 100%;
  }
}

/* 热门模板按钮样式 */
.hot-template-btn {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 12px 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.hot-template-btn:hover {
  border-color: #667eea;
  background: #f8faff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.hot-template-btn:active {
  transform: translateY(0);
}

/* 输入框焦点样式优化 */
.analysis-input:focus {
  border-color: #667eea !important;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
  outline: none;
}

/* 主要按钮样式 */
.primary-gradient-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 12px;
  color: white;
  font-weight: bold;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4);
}

.primary-gradient-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.5);
}

.primary-gradient-btn:active {
  transform: translateY(0);
}

/* 卡片阴影优化 */
.clean-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.clean-card:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

/* 结构化报告样式 */
.analysis-structured-report {
  background: transparent;
  border-radius: 0;
  box-shadow: none;
}

.analysis-structured-report .report-toolbar {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px 8px 0 0;
}

.analysis-structured-report .report-content {
  background: white;
  border: 1px solid #e9ecef;
  border-top: none;
  border-radius: 0 0 8px 8px;
}

/* 报告组件在分析页面中的特殊样式 */
.analysis-structured-report .report-component-header {
  margin-bottom: 32px;
}

.analysis-structured-report .report-component-kpi_grid {
  margin-bottom: 32px;
}

.analysis-structured-report .report-component-chart {
  margin-bottom: 32px;
}
