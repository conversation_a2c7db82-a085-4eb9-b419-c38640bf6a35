import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, Button, Descriptions, Typography, Tabs, Timeline, Tag, Spin, message, Collapse, Space, Divider, Alert, Table, Empty, List, Modal } from 'antd';
import { ArrowLeftOutlined, CheckCircleOutlined, CloseCircleOutlined, ClockCircleOutlined, FileTextOutlined, AimOutlined, CodeOutlined } from '@ant-design/icons';
import { analysisApi } from '../../services/api';
import ReactMarkdown from 'react-markdown';
// @ts-ignore
import remarkGfm from 'remark-gfm';  // 添加 GFM 支持
// @ts-ignore
import rehypeRaw from 'rehype-raw';  // 添加 HTML 支持
import { formatDateTime } from '../../utils/dateUtils';
import { JsonView } from 'react-json-view-lite';
import 'react-json-view-lite/dist/index.css';
import ToolResultRenderer from '../../components/ToolResults';
import ReactECharts from 'echarts-for-react';
import { getToolTypeDisplay, getToolTypeColor } from '../../utils/toolUtils';
// 添加 markdown 样式
import './AnalysisDetail.css';

const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;
const { Panel } = Collapse;

// 工具执行状态标签
const ExecutionStatusTag = ({ result }: { result: any }) => {
  if (!result) {
    return <Tag color="default">未执行</Tag>;
  }
  
  if (result.success === false) {
    return <Tag color="error">失败</Tag>;
  }
  
  return <Tag color="success">成功</Tag>;
};

const AnalysisDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  
  const [loading, setLoading] = useState(true);
  const [analysis, setAnalysis] = useState<any>(null);
  const [executions, setExecutions] = useState<any[]>([]);
  const [activeTabKey, setActiveTabKey] = useState('1');

  // 获取分析记录详情
  useEffect(() => {
    const fetchAnalysisDetail = async () => {
      setLoading(true);
      try {
        if (!id) return;
        const response = await analysisApi.getAnalysis(id);
        if (response.data.code === 0) {
          const analysisData = response.data.data;
          
          // 规范化工具类型，确保大写
          if (analysisData.intent_analysis && analysisData.intent_analysis.analysis_steps) {
            analysisData.intent_analysis.analysis_steps = analysisData.intent_analysis.analysis_steps.map((step: any) => {
              if (step.tool && step.tool.tool_type && typeof step.tool.tool_type === 'string') {
                step.tool.tool_type = step.tool.tool_type.toUpperCase();
              }
              return step;
            });
          }
          
          setAnalysis(analysisData);
        } else {
          message.error('获取分析详情失败: ' + response.data.message);
        }
      } catch (error: any) {
        message.error('获取分析详情出错: ' + (error.message || '未知错误'));
      } finally {
        setLoading(false);
      }
    };

    // 获取工具执行记录
    const fetchExecutions = async () => {
      try {
        if (!id) return;
        const response = await analysisApi.getToolExecutions(id);
        if (response.data.code === 0) {
          const executionData = response.data.data || [];
          console.log('执行记录：', executionData);
          
          // 规范化工具类型，确保大写
          const normalizedExecutions = executionData.map((exec: any) => {
            if (exec.tool && exec.tool.tool_type && typeof exec.tool.tool_type === 'string') {
              exec.tool.tool_type = exec.tool.tool_type.toUpperCase();
            }
            return exec;
          });
          
          // 根据step_id排序执行记录
          const sortedExecutions = [...normalizedExecutions].sort((a, b) => {
            // 如果没有step_id，默认放到最后
            if (!a.step_id) return 1;
            if (!b.step_id) return -1;
            
            // 提取步骤数字部分进行排序（step_1 => 1）
            const aStepNum = parseInt(a.step_id.replace(/\D/g, '')) || 0;
            const bStepNum = parseInt(b.step_id.replace(/\D/g, '')) || 0;
            
            return aStepNum - bStepNum;
          });
          
          console.log('排序后的执行记录：', sortedExecutions);
          setExecutions(sortedExecutions);
        } else {
          console.error('获取执行记录失败:', response.data.message);
        }
      } catch (error: any) {
        console.error('获取执行记录出错:', error.message || '未知错误');
      }
    };

    if (id) {
      fetchAnalysisDetail();
      fetchExecutions();
    }
  }, [id]);

  // 返回分析列表
  const goBack = () => {
    navigate('/analysis/history');
  };

  // 处理标签页切换
  const handleTabChange = (key: string) => {
    setActiveTabKey(key);
  };

  // 渲染加载状态
  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <Spin size="large" />
      </div>
    );
  }

  // 渲染未找到分析记录的状态
  if (!analysis) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
          <Text type="danger" style={{ fontSize: '16px' }}>未找到分析记录</Text>
          <div style={{ marginTop: '20px' }}>
            <Button type="primary" onClick={goBack}>返回列表</Button>
          </div>
        </div>
      </Card>
    );
  }

  // 从意图分析结果中提取分析步骤
  const analysisSteps = analysis.intent_analysis?.analysis_steps || [];

  // 渲染意图分析内容
  const renderIntentAnalysis = () => {
    const intentAnalysis = analysis.intent_analysis;
    if (!intentAnalysis) return null;
    
    return (
      <Card 
        bordered={false} 
        style={{ background: '#f9f9f9', marginTop: 16, marginLeft: 24 }}
        size="small"
      >
        <Paragraph>
          <Text strong>问题理解：</Text> {intentAnalysis.problem_understanding || '未识别'}
        </Paragraph>
        {intentAnalysis.analysis_steps && (
          <>
            <Text strong>分析步骤：</Text>
            <ul style={{ paddingLeft: 20 }}>
              {intentAnalysis.analysis_steps.map((item: any, index: number) => (
                <li key={index}>
                  <Text>{item.step_id}. {item.tool_name}: {item.description || item.purpose}</Text>
                </li>
              ))}
            </ul>
          </>
        )}
        {intentAnalysis.generated_sql && (
          <div style={{ marginTop: 8 }}>
            <Text strong>生成的SQL：</Text>
            <div style={{ 
              whiteSpace: 'pre-wrap', 
              fontFamily: 'monospace', 
              background: '#f0f5ff', 
              padding: 8, 
              borderRadius: 4,
              border: '1px solid #d6e4ff',
              marginTop: 4
            }}>
              {intentAnalysis.generated_sql.sql}
            </div>
          </div>
        )}
      </Card>
    );
  };

  // 判断是否为SQL查询结果
  const isSQLQueryResult = (result: any): boolean => {
    try {
      console.log('检查是否为SQL查询结果:', result);
      
      // 检查是否含有SQL专用的results和columns字段
      if (result.result?.results && Array.isArray(result.result.results) && 
          (result.result?.columns || result.result?.results.length > 0)) {
        console.log('SQL检测：检测到results字段');
        return true;
      }
      
      // 检查工具名称是否包含SQL或查询关键词
      const toolName = (result.tool_name || "").toLowerCase();
      if (toolName.includes("sql") || toolName.includes("查询") || toolName.includes("query") || 
          toolName.includes("自动sql")) {
        console.log('SQL检测：工具名称匹配');
        return true;
      }
      
      // 检查是否有SQL字段
      if (result.result?.sql && typeof result.result.sql === 'string') {
        console.log('SQL检测：发现SQL语句字段');
        return true;
      }
      
      // 检查数据显示格式
      const displayFormat = (result.result?.display_format || "").toLowerCase();
      if (displayFormat === "sql" || displayFormat === "db" || displayFormat === "database" || displayFormat === "table") {
        console.log('SQL检测：显示格式匹配');
        return true;
      }
      
      // 检查数据内容特征
      const data = result.result?.data;
      if (data) {
        if (typeof data === 'string' && (
          data.includes('SELECT') && data.includes('FROM') ||
          data.includes('select') && data.includes('from')
        )) {
          console.log('SQL检测：SQL语句匹配');
          return true;
        }
        
        // 检查是否为典型的SQL错误
        if (typeof data === 'string' && (
          data.includes('ORA-') || 
          data.includes('SQL error') || 
          data.includes('SQL Error') ||
          data.includes('SQL执行')
        )) {
          console.log('SQL检测：SQL错误匹配');
          return true;
        }
        
        // 检查是否为典型的SQL结果结构
        if (typeof data === 'object') {
          if (Array.isArray(data) && data.length > 0 && typeof data[0] === 'object') {
            console.log('SQL检测：数据是记录数组');
            return true;
          }
          
          if (data.rows && Array.isArray(data.rows)) {
            console.log('SQL检测：数据有rows属性');
            return true;
          }
          
          if (data.columns && Array.isArray(data.columns)) {
            console.log('SQL检测：数据有columns属性');
            return true;
          }
          
          if (data.results && Array.isArray(data.results)) {
            console.log('SQL检测：数据有results属性');
            return true;
          }
        }
      }
      
      // 检查工具执行结果的类型
      if (result.result?.type === 'sql' || result.result?.type === 'database' || result.result?.type === 'table') {
        console.log('SQL检测：结果类型匹配');
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('判断SQL查询结果时出错：', error);
      return false;
    }
  };

  // 判断数据是否符合表格格式
  const isTableData = (data: any): boolean => {
    if (!data) return false;
    
    try {
      // 检查是否具有表格所需的所有字段
      return (
        data.columns && Array.isArray(data.columns) && data.columns.length > 0 &&
        data.data && Array.isArray(data.data) &&
        typeof data.count !== 'undefined'
      );
    } catch (error) {
      console.error('判断表格数据时出错：', error);
      return false;
    }
  };

  // 尝试解析JSON_DOC字段
  const tryParseJsonDoc = (data: any): any => {
    if (!data || !data.data || !Array.isArray(data.data) || data.data.length === 0) {
      return null;
    }

    // 检查是否是JSON_DOC格式
    if (data.columns && data.columns.includes('JSON_DOC') && data.data[0] && data.data[0][0]) {
      try {
        const jsonContent = data.data[0][0];
        if (typeof jsonContent === 'string' && (jsonContent.startsWith('[') || jsonContent.startsWith('{'))) {
          return JSON.parse(jsonContent);
        }
      } catch (e) {
        console.error('解析JSON_DOC失败:', e);
      }
    }
    return null;
  };

  // 渲染公司信息卡片
  const renderCompanyInfoCard = (companyData: any[]): React.ReactNode => {
    return (
      <>
        {companyData.map((company, index) => {
          // 提取相关文件数据
          const relatedFiles = company["相关文件"] || [];
          // 创建一个不包含相关文件的公司信息副本，用于基本信息展示
          const basicCompanyInfo = { ...company };
          delete basicCompanyInfo["相关文件"];
          
          return (
            <div key={index} style={{ marginBottom: 16 }}>
              <Descriptions 
                title={company["公司名称"] || "公司详情"} 
                bordered 
                column={{ xxl: 4, xl: 3, lg: 3, md: 2, sm: 2, xs: 1 }}
                size="small"
              >
                {Object.entries(basicCompanyInfo).map(([key, value]) => (
                  <Descriptions.Item 
                    key={key} 
                    label={<strong>{key}</strong>}
                    labelStyle={{ fontWeight: 'bold' }}
                  >
                    {value !== null && value !== undefined ? String(value) : '-'}
                  </Descriptions.Item>
                ))}
              </Descriptions>
              
              {/* 相关文件展示区域 */}
              {relatedFiles && Array.isArray(relatedFiles) && relatedFiles.length > 0 && (
                <div style={{ marginTop: 16 }}>
                  <Divider orientation="left" style={{ margin: '16px 0 12px 0' }}>
                    <Text strong>相关文件 ({relatedFiles.length})</Text>
                  </Divider>
                  <div style={{ 
                    background: '#fafafa', 
                    padding: 12, 
                    borderRadius: 6,
                    border: '1px solid #f0f0f0'
                  }}>
                    <List
                      size="small"
                      dataSource={relatedFiles}
                      renderItem={(file: any, fileIndex: number) => (
                        <List.Item
                          key={fileIndex}
                          style={{ 
                            padding: '8px 12px',
                            background: '#fff',
                            marginBottom: 8,
                            borderRadius: 4,
                            border: '1px solid #e8e8e8'
                          }}
                          actions={[
                            <Button
                              key="download"
                              type="link"
                              size="small"
                              icon={<FileTextOutlined />}
                              onClick={() => {
                                if (file.文件链接) {
                                  window.open(file.文件链接, '_blank');
                                }
                              }}
                              disabled={!file.文件链接}
                            >
                              下载
                            </Button>,
                            <Button
                              key="preview"
                              type="link"
                              size="small"
                              onClick={() => {
                                if (file.文件链接) {
                                  // 创建预览模态框
                                  const fileName = file.文件名 || '未知文件';
                                  const fileUrl = file.文件链接;
                                  
                                  // 判断文件类型
                                  const fileExtension = fileName.split('.').pop()?.toLowerCase();
                                  const isImage = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(fileExtension || '');
                                  const isPdf = fileExtension === 'pdf';
                                  const isOfficeDoc = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'].includes(fileExtension || '');
                                  
                                  let previewContent;
                                  var officeViewerUrl = '';
                                  if (isImage) {
                                    previewContent = (
                                      <div style={{ textAlign: 'center' }}>
                                        <img 
                                          src={fileUrl} 
                                          alt={fileName}
                                          style={{ 
                                            maxWidth: '100%', 
                                            maxHeight: '70vh',
                                            objectFit: 'contain'
                                          }}
                                          onError={(e) => {
                                            (e.target as HTMLImageElement).style.display = 'none';
                                            const errorDiv = document.createElement('div');
                                            errorDiv.innerHTML = '图片加载失败';
                                            errorDiv.style.color = '#999';
                                            errorDiv.style.textAlign = 'center';
                                            errorDiv.style.padding = '20px';
                                            (e.target as HTMLImageElement).parentNode?.appendChild(errorDiv);
                                          }}
                                        />
                                      </div>
                                    );
                                  } else if (isPdf) {
                                    previewContent = (
                                      <div style={{ height: '70vh' }}>
                                        <iframe
                                          src={`${fileUrl}#toolbar=1&navpanes=1&scrollbar=1`}
                                          style={{ 
                                            width: '100%', 
                                            height: '100%',
                                            border: 'none'
                                          }}
                                          title={fileName}
                                        />
                                      </div>
                                    );
                                  } else if (isOfficeDoc) {
                                    // Office文档使用微软在线预览服务
                                    officeViewerUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(fileUrl)}`;
                                    previewContent = (
                                      <div style={{ height: '70vh' }}>
                                        <iframe
                                          src={officeViewerUrl}
                                          style={{ 
                                            width: '100%', 
                                            height: '100%',
                                            border: 'none'
                                          }}
                                          title={fileName}
                                        />
                                      </div>
                                    );
                                  } else {
                                    previewContent = (
                                      <div style={{ 
                                        textAlign: 'center', 
                                        padding: '40px',
                                        color: '#666'
                                      }}>
                                        <FileTextOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
                                        <div>此文件类型不支持在线预览</div>
                                        <div style={{ marginTop: '8px', fontSize: '12px' }}>
                                          文件类型: {fileExtension?.toUpperCase() || '未知'}
                                        </div>
                                        <Button 
                                          type="primary" 
                                          style={{ marginTop: '16px' }}
                                          onClick={() => window.open(fileUrl, '_blank')}
                                        >
                                          下载文件
                                        </Button>
                                      </div>
                                    );
                                  }
                                  
                                  Modal.info({
                                    title: (
                                      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                        <span>文件预览: {fileName}</span>
                                        <Button 
                                          type="link" 
                                          size="small"
                                          onClick={() => window.open(officeViewerUrl || fileUrl, '_blank')}
                                        >
                                          在新窗口打开
                                        </Button>
                                      </div>
                                    ),
                                    content: previewContent,
                                    width: '80vw',
                                    style: { 
                                      top: '8vh'
                                    },
                                    okText: '关闭',
                                    icon: null
                                  });
                                }
                              }}
                              disabled={!file.文件链接}
                            >
                              预览
                            </Button>
                          ]}
                        >
                          <List.Item.Meta
                            avatar={
                              <div style={{ 
                                width: 32, 
                                height: 32, 
                                background: '#1890ff', 
                                borderRadius: 4,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                color: '#fff',
                                fontSize: '12px',
                                fontWeight: 'bold'
                              }}>
                                {file.文件名 ? file.文件名.split('.').pop()?.toUpperCase().slice(0, 3) || 'DOC' : 'DOC'}
                              </div>
                            }
                            title={
                              <div style={{ display: 'flex', alignItems: 'center' }}>
                                <Text strong style={{ marginRight: 8 }}>
                                  {file.文件名 || '未知文件名'}
                                </Text>
                                {file.文件链接 && (
                                  <Tag color="green">可访问</Tag>
                                )}
                              </div>
                            }
                            description={
                              <div style={{ fontSize: '12px', color: '#666' }}>
                                {file.文件链接 ? (
                                  <div>
                                    <Text copyable={{ text: file.文件链接 }} style={{ fontSize: '11px' }}>
                                      {file.文件链接.length > 60 ? 
                                        `${file.文件链接.substring(0, 60)}...` : 
                                        file.文件链接
                                      }
                                    </Text>
                                  </div>
                                ) : (
                                  <Text type="secondary">无可用链接</Text>
                                )}
                              </div>
                            }
                          />
                        </List.Item>
                      )}
                    />
                  </div>
                </div>
              )}
              
              {index < companyData.length - 1 && <Divider />}
            </div>
          );
        })}
        <div style={{ textAlign: 'right', color: '#999', fontSize: '12px' }}>
          共 {companyData.length} 条记录
        </div>
      </>
    );
  };

  // 渲染资金流转图表
  const renderFundTransferGraph = (data: any): React.ReactNode => {
    try {
      const resultData = data;
      
      if (!resultData || !resultData.data || !Array.isArray(resultData.data) || resultData.data.length === 0) {
        return <Empty description="资金流转数据为空" />;
      }

      // 获取列名和数据
      const columns = resultData.columns || [];
      const tableData = resultData.data;
      
      // 计算最大交易金额，用于边的宽度缩放
      let maxAmount = 0;
      tableData.forEach((row: any[]) => {
        const amount = row[6]; // 总金额列索引
        if (typeof amount === 'number' && amount > maxAmount) {
          maxAmount = amount;
        }
      });
      
      // 提取所有公司名称，构建节点
      const companySet = new Set<string>();
      tableData.forEach((row: any[]) => {
        companySet.add(row[1]); // 源公司
        companySet.add(row[3]); // 目标公司
      });
      
      const nodes = Array.from(companySet).map((company, index) => ({
        id: company,
        name: company,
        symbolSize: 30,
        itemStyle: {
          color: '#5470c6'
        },
        label: {
          show: true,
          position: 'right',
          formatter: '{b}'
        }
      }));
      
      // 构建边
      const links = tableData.map((row: any[]) => {
        // 获取风险等级
        const riskLevel = row[7]; // 风险等级列索引
        let lineColor = '#91cc75'; // 默认绿色，低风险
        
        if (riskLevel === '高风险') {
          lineColor = '#ee6666'; // 红色
        } else if (riskLevel === '中风险') {
          lineColor = '#fac858'; // 黄色
        }
        
        // 计算边的宽度，基于交易金额比例
        const amount = row[6]; // 总金额列索引
        const lineWidth = 1 + (amount / maxAmount) * 5; // 宽度范围：1-6
        
        return {
          source: row[1], // 源公司
          target: row[3], // 目标公司
          value: amount, // 总金额
          lineStyle: {
            width: lineWidth,
            color: lineColor,
            curveness: 0.3 // 添加一点曲率，避免重叠
          },
          label: {
            show: true,
            formatter: `{c} 元`
          },
          tooltip: {
            formatter: ({ data }: any) => {
              const sourceCompany = data.source;
              const targetCompany = data.target;
              const amount = data.value;
              
              // 查找对应的行以获取更多详情
              const rowData = tableData.find((r: any[]) => 
                r[1] === sourceCompany && r[3] === targetCompany
              );
              
              if (rowData) {
                const sourceAccount = rowData[2];
                const targetAccount = rowData[4];
                const txCount = rowData[5];
                const riskLevel = rowData[7];
                
                return `
                  <div style="font-weight:bold;margin-bottom:5px;">
                    ${sourceCompany} → ${targetCompany}
                  </div>
                  <div>源账号: ${sourceAccount}</div>
                  <div>目标账号: ${targetAccount}</div>
                  <div>交易次数: ${txCount}</div>
                  <div>总金额: ¥${amount}</div>
                  <div>风险等级: ${riskLevel}</div>
                `;
              }
              
              return `${sourceCompany} → ${targetCompany}: ¥${amount}`;
            }
          }
        };
      });
      
      // 构建图表配置
      const graphOption = {
        title: {
          text: '资金流转关系图',
          subtext: `共 ${nodes.length} 家公司，${links.length} 条资金流向`,
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          confine: true
        },
        legend: {
          data: ['公司', '资金流向'],
          orient: 'vertical',
          right: 10,
          top: 'center'
        },
        series: [
          {
            name: '资金流转',
            type: 'graph',
            layout: 'force',
            data: nodes,
            links: links,
            roam: true,
            draggable: true,
            edgeSymbol: ['none', 'arrow'],  // 添加箭头指向配置，起点无箭头，终点有箭头
            edgeSymbolSize: [0, 10],        // 箭头大小配置
            label: {
              position: 'right'
            },
            force: {
              repulsion: 100,
              gravity: 0.1,
              edgeLength: 150
            },
            lineStyle: {
              opacity: 0.8,
              color: '#91cc75',
              width: 2,
              type: 'solid',
              curveness: 0.2  // 增加曲率以便更好地显示箭头
            },
            emphasis: {
              focus: 'adjacency',
              lineStyle: {
                width: 5
              }
            }
          }
        ]
      };
      
      return (
        <div>
          <Tabs defaultActiveKey="graph">
            <Tabs.TabPane tab="关系图" key="graph">
              <div style={{ height: 500, marginBottom: 16 }}>
                <ReactECharts 
                  option={graphOption} 
                  style={{ height: '100%', width: '100%' }} 
                  opts={{ renderer: 'canvas' }}
                />
              </div>
              <div>
                <Alert 
                  message="使用提示" 
                  description={
                    <ul style={{ paddingLeft: 20, marginBottom: 0 }}>
                      <li>鼠标滚轮：缩放图表</li>
                      <li>拖动：平移图表</li>
                      <li>拖动节点：调整节点位置</li>
                      <li>悬停：查看详细信息</li>
                    </ul>
                  }
                  type="info" 
                  showIcon 
                />
              </div>
            </Tabs.TabPane>
            <Tabs.TabPane tab="数据表格" key="table">
              <div>
                <Table 
                  columns={resultData.columns.map((col: string, index: number) => ({
                    title: col,
                    dataIndex: index.toString(),
                    key: index.toString(),
                    ellipsis: true,
                    render: (text: any) => {
                      if (col === '风险等级') {
                        let color = 'green';
                        if (text === '高风险') color = 'red';
                        else if (text === '中风险') color = 'orange';
                        
                        return <Tag color={color}>{text}</Tag>;
                      }
                      
                      return (
                        <Typography.Paragraph 
                          ellipsis={{ rows: 3, expandable: true, symbol: '更多' }}
                          style={{ marginBottom: 0 }}
                        >
                          {text === null || text === undefined ? '' : String(text)}
                        </Typography.Paragraph>
                      );
                    }
                  }))} 
                  dataSource={resultData.data.map((row: any[], rowIndex: number) => {
                    const rowData: any = { key: rowIndex };
                    row.forEach((cell, cellIndex) => {
                      rowData[cellIndex.toString()] = cell;
                    });
                    return rowData;
                  })}
                  size="small"
                  pagination={{ pageSize: 5 }}
                  bordered
                />
              </div>
            </Tabs.TabPane>
          </Tabs>
        </div>
      );
    } catch (error) {
      console.error('渲染资金流转图表时出错:', error);
      return (
        <Alert
          message="渲染资金流转图表时出错"
          description={String(error)}
          type="error"
          showIcon
        />
      );
    }
  };

  // 渲染SQL查询结果为表格
  const renderSQLResultTable = (data: any): React.ReactNode => {
    try {
      console.log('渲染SQL结果：', data);
      
      // 支持原始结果对象中含有 results 和 columns 字段
      if (data && typeof data === 'object' && Array.isArray(data.results)) {
        // 构建表头
        const cols = (data.columns || (data.results.length > 0 ? Object.keys(data.results[0]) : [])).map((col: string) => ({
          title: col,
          dataIndex: col,
          key: col,
          ellipsis: { showTitle: false },
          render: (text: any) => (
            <Typography.Paragraph ellipsis={{ rows: 3, expandable: true, symbol: '更多' }} style={{ marginBottom: 0 }}>
              {text === null || text === undefined ? '' : String(text)}
            </Typography.Paragraph>
          )
        }));
        // 构建数据
        const tableData = data.results.map((row: any, index: number) => ({ ...row, key: index }));
        
        // 根据数据量自动配置分页
        const paginationConfig = tableData.length > 10 ? {
          pageSize: 10,
          showSizeChanger: true,
          showTotal: (total: number) => `共 ${total} 条记录`,
          pageSizeOptions: ['10', '20', '50', '100'],
        } : false;
        
        return (
          <>
            <Table 
              columns={cols} 
              dataSource={tableData} 
              size="small" 
              pagination={paginationConfig} 
              bordered 
            />
            {!paginationConfig && (
              <div style={{ marginTop: 8, textAlign: 'right' }}>
                <Text type="secondary">共 {tableData.length} 条记录</Text>
              </div>
            )}
          </>
        );
      }
       // 如果数据是对象而不是字符串，直接处理为表格
       if (data && typeof data === 'object' && !Array.isArray(data)) {
         // 如果有rows属性，可能是标准SQL结果格式
         if (data.rows && Array.isArray(data.rows)) {
           console.log('检测到标准SQL结果格式 (rows)');
           const columns = data.columns || Object.keys(data.rows[0] || {}).map(k => ({
             title: k,
             dataIndex: k,
             key: k
           }));
          
           // 根据数据量自动配置分页
           const rowsData = data.rows.map((row: any, index: number) => ({
             ...row,
             key: index
           }));
           
           const paginationConfig = rowsData.length > 10 ? {
             pageSize: 10,
             showSizeChanger: true,
             showTotal: (total: number) => `共 ${total} 条记录`,
             pageSizeOptions: ['10', '20', '50', '100'],
           } : false;
           
           return (
             <>
               <Table 
                 columns={columns.map((col: any) => ({
                   title: typeof col === 'string' ? col : col.title || col.name,
                   dataIndex: typeof col === 'string' ? col : col.dataIndex || col.name,
                   key: typeof col === 'string' ? col : col.key || col.name,
                   ellipsis: { showTitle: false },
                   render: (text: any) => (
                     <Typography.Paragraph 
                       ellipsis={{ rows: 3, expandable: true, symbol: '更多' }}
                       style={{ marginBottom: 0 }}
                     >
                       {text === null || text === undefined ? '' : String(text)}
                     </Typography.Paragraph>
                   )
                 }))} 
                 dataSource={rowsData}
                 size="small"
                 pagination={paginationConfig}
                 bordered
               />
               {!paginationConfig && (
                 <div style={{ marginTop: 8, textAlign: 'right' }}>
                   <Text type="secondary">共 {rowsData.length} 条记录</Text>
                 </div>
               )}
             </>
           );
         }
         
         // 如果有data属性，也可能是标准结果格式
         if (data.data && Array.isArray(data.data)) {
           console.log('检测到标准SQL结果格式 (data)');
           return renderDataTable(data);
         }
         
         // 如果是普通对象但不是特定格式，尝试展示为JSON
         return (
           <div style={{ 
             whiteSpace: 'pre-wrap', 
             fontFamily: 'monospace', 
             background: '#f5f5f5', 
             padding: 8, 
             borderRadius: 4, 
             maxHeight: '300px', 
             overflow: 'auto'
           }}>
             {JSON.stringify(data, null, 2)}
           </div>
         );
       }
       
       // 如果数据是数组，可能是记录列表
       if (Array.isArray(data)) {
         console.log('检测到数组数据，尝试渲染为表格');
         if (data.length > 0) {
           // 提取列名（使用第一行的键）
           const firstRow = data[0];
           const columns = Object.keys(firstRow).map(col => ({
             title: col,
             dataIndex: col,
             key: col,
             ellipsis: { showTitle: false },
             render: (text: any) => (
               <Typography.Paragraph 
                 ellipsis={{ rows: 3, expandable: true, symbol: '更多' }}
                 style={{ marginBottom: 0 }}
               >
                 {text === null || text === undefined ? '' : String(text)}
               </Typography.Paragraph>
             )
           }));
           
           // 为每行添加key
           const tableData = data.map((row: any, index: number) => ({
             ...row,
             key: index
           }));
           
           // 根据数据量自动配置分页
           const paginationConfig = tableData.length > 10 ? {
             pageSize: 10,
             showSizeChanger: true,
             showTotal: (total: number) => `共 ${total} 条记录`,
             pageSizeOptions: ['10', '20', '50', '100'],
           } : false;
           
           return (
             <>
               <Table 
                 columns={columns} 
                 dataSource={tableData}
                 size="small"
                 pagination={paginationConfig}
                 bordered
               />
               {!paginationConfig && (
                 <div style={{ marginTop: 8, textAlign: 'right' }}>
                   <Text type="secondary">共 {data.length} 条记录</Text>
                 </div>
               )}
             </>
           );
         } else {
           return <Empty description="查询结果为空" />;
         }
       }
       
       // 如果数据是字符串，尝试解析
       if (typeof data === 'string') {
         // 尝试解析为JSON
         try {
           if (data.trim().startsWith('[') || data.trim().startsWith('{')) {
             const parsedData = JSON.parse(data);
             console.log('成功将字符串解析为JSON：', parsedData);
             
             // 递归调用以处理解析后的结果
             return renderSQLResultTable(parsedData);
           }
         } catch (e) {
           console.log('JSON解析失败，尝试其他方式', e);
         }
         
         // 如果是表格形式的文本（例如CSV格式）
         const lines = data.split('\n').filter(line => line.trim());
         if (lines.length > 1) {
           const separator = lines[0].includes(',') ? ',' : 
                           lines[0].includes('\t') ? '\t' : 
                           lines[0].includes('|') ? '|' : null;
           
           if (separator) {
             console.log('检测到分隔符文本数据');
             // 解析列和数据
             const columns = lines[0].split(separator).map((col, index) => ({
               title: col.trim(),
               dataIndex: index.toString(),
               key: index.toString(),
               ellipsis: { showTitle: false },
               render: (text: any) => (
                 <Typography.Paragraph 
                   ellipsis={{ rows: 3, expandable: true, symbol: '更多' }}
                   style={{ marginBottom: 0 }}
                 >
                   {text === null || text === undefined ? '' : text}
                 </Typography.Paragraph>
               )
             }));
             
             const tableData = lines.slice(1).map((line, rowIndex) => {
               const cells = line.split(separator);
               const rowData: any = { key: rowIndex };
               cells.forEach((cell, cellIndex) => {
                 rowData[cellIndex.toString()] = cell.trim();
               });
               return rowData;
             });
             
             return (
               <>
                 <Table 
                   columns={columns} 
                   dataSource={tableData}
                   size="small"
                   pagination={false}
                   bordered
                 />
                 <div style={{ marginTop: 8, textAlign: 'right' }}>
                   <Text type="secondary">共 {tableData.length} 条记录</Text>
                 </div>
               </>
             );
           }
         }
         
         // 如果没有特定格式，显示原始内容
         return (
           <div style={{ 
             whiteSpace: 'pre-wrap', 
             fontFamily: 'monospace', 
             background: '#f5f5f5', 
             padding: 8, 
             borderRadius: 4 
           }}>
             {data}
           </div>
         );
       }
       
       // 对于null或undefined，显示提示
       if (data === null || data === undefined) {
         return <Empty description="查询结果为空" />;
       }
       
       // 其他类型，显示JSON字符串
       return (
         <div style={{ 
           whiteSpace: 'pre-wrap', 
           fontFamily: 'monospace', 
           background: '#f5f5f5', 
           padding: 8, 
           borderRadius: 4 
         }}>
           {JSON.stringify(data, null, 2)}
         </div>
       );
     } catch (error) {
       console.error('渲染SQL结果表格时出错：', error);
       // 出错时，显示错误信息和原始数据
       return (
         <>
           <Alert 
             message="渲染SQL结果时发生错误" 
             description={String(error)}
             type="error" 
             showIcon 
             style={{ marginBottom: 8 }}
           />
           <div style={{ 
             whiteSpace: 'pre-wrap', 
             fontFamily: 'monospace', 
             background: '#f5f5f5', 
             padding: 8, 
             borderRadius: 4 
           }}>
             {typeof data === 'string' ? data : JSON.stringify(data, null, 2)}
           </div>
         </>
       );
     }
   };

  // 使用Ant Design Table渲染标准表格数据
  const renderDataTable = (data: any): React.ReactNode => {
    if (!data || !data.columns || !data.data) return null;
    
    const columns = data.columns.map((col: string, index: number) => ({
      title: col,
      dataIndex: index.toString(),
      key: index.toString(),
      ellipsis: { showTitle: false },
      render: (text: any) => (
        <Typography.Paragraph 
          ellipsis={{ rows: 3, expandable: true, symbol: '更多' }}
          style={{ marginBottom: 0 }}
        >
          {text === null || text === undefined ? '' : text}
        </Typography.Paragraph>
      )
    }));
    
    const tableData = data.data.map((row: any[], rowIndex: number) => {
      const rowData: any = { key: rowIndex };
      row.forEach((cell, cellIndex) => {
        rowData[cellIndex.toString()] = cell;
      });
      return rowData;
    });
    
    return (
      <>
        <Table 
          columns={columns} 
          dataSource={tableData} 
          size="small" 
          pagination={false}
          bordered
        />
        <div style={{ marginTop: 8, textAlign: 'right' }}>
          <Text type="secondary">共 {data.count} 条记录</Text>
        </div>
      </>
    );
  };

  // 渲染文本类型的结果
  const renderTextResult = (data: any): React.ReactNode => {
    if (!data || !data.data) return <Empty description="结果为空" />;
    
    // 如果数据有特定结构
    if (data.data && data.columns && data.data.length > 0) {
      // 提取文本内容 - 通常在第一行第一列
      const textContent = data.data[0][0];
      
      if (typeof textContent === 'string') {
        return (
          <div style={{ 
            padding: '16px', 
            background: '#fafafa', 
            border: '1px solid #f0f0f0',
            borderRadius: '4px',
            whiteSpace: 'pre-wrap'
          }}>
            {textContent}
          </div>
        );
      }
    }
    
    // 如果找不到特定结构，尝试以不同方式输出
    return (
      <div style={{ 
        padding: '16px', 
        background: '#fafafa', 
        border: '1px solid #f0f0f0',
        borderRadius: '4px',
        whiteSpace: 'pre-wrap'
      }}>
        {typeof data === 'string' 
          ? data 
          : typeof data.data === 'string' 
            ? data.data 
            : JSON.stringify(data.data, null, 2)}
      </div>
    );
  };

  // 在展示工具类型的地方加入映射
  const renderToolInfo = (tool: any) => {
    if (!tool) return null;
    
    return (
      <div style={{ marginBottom: 8 }}>
        <Space>
          <Text strong>工具:</Text>
          <Text>{tool.name}</Text>
          {tool.tool_type && (
            <Tag color={getToolTypeColor(tool.tool_type)}>
              {getToolTypeDisplay(tool.tool_type)}
            </Tag>
          )}
        </Space>
      </div>
    );
  };

  // 渲染工具执行结果
  const renderToolResult = (execution: any) => {
    if (!execution || !execution.result) return null;
    
    // 添加更详细的日志，帮助调试
    console.log('工具结果完整数据：', execution);
    
    const result = {
      tool_name: execution.tool?.name || '',
      result: execution.result,
      execution_time: execution.execution_time,
      display_format: execution.result?.display_format || 'json'
    };
    
    // 处理错误情况
    if (result.result?.error || (typeof result.result?.data === 'string' && result.result.data.includes('ORA-'))) {
      return (
        <Card 
          bordered={false} 
          style={{ marginTop: 16, marginLeft: 24 }}
          size="small"
        >
          <div style={{ marginBottom: 8 }}>
            <Text type="danger" strong>错误:</Text>
            <div style={{ 
              whiteSpace: 'pre-wrap', 
              fontFamily: 'monospace', 
              background: '#fff2f0', 
              padding: 8, 
              borderRadius: 4,
              border: '1px solid #ffccc7',
              marginTop: 8
            }}>
              {result.result?.error || result.result?.data}
            </div>
          </div>
        </Card>
      );
    }
    
    // 是否是自动SQL查询工具（特殊处理）
    const isAutoSqlTool = result.tool_name && result.tool_name.includes("自动SQL查询");
    
    // 根据display_format处理
    const content = (
      <Card 
        bordered={false} 
        style={{ marginTop: 16, marginLeft: 24 }}
        size="small"
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Text strong>{result.tool_name || "执行结果"}</Text>
            <Text type="secondary">执行时间: {result.execution_time?.toFixed(3) || '未知'}ms</Text>
          </div>
        }
      >
        {(() => {
          // 处理自动SQL查询工具
          if (isAutoSqlTool) {
            // 获取SQL语句和执行结果
            const sql = result.result?.sql || '';
            
            return (
              <div>
                <Space direction="vertical" style={{ width: '100%' }}>
                  {sql && (
                    <div style={{ marginBottom: 8 }}>
                      <Text strong>执行的SQL:</Text>
                      <div style={{ 
                        whiteSpace: 'pre-wrap', 
                        fontFamily: 'monospace', 
                        background: '#f0f5ff', 
                        padding: 8, 
                        borderRadius: 4,
                        border: '1px solid #d6e4ff',
                        marginTop: 4
                      }}>
                        {sql}
                      </div>
                    </div>
                  )}
                  
                  {result.result.parameters && Object.keys(result.result.parameters).length > 0 && (
                    <div style={{ marginBottom: 8 }}>
                      <Text strong>参数:</Text>
                      <div style={{ 
                        whiteSpace: 'pre-wrap', 
                        fontFamily: 'monospace', 
                        background: '#f9f0ff', 
                        padding: 8, 
                        borderRadius: 4,
                        border: '1px solid #efdbff',
                        marginTop: 4
                      }}>
                        {JSON.stringify(result.result.parameters, null, 2)}
                      </div>
                    </div>
                  )}
                  
                  <div style={{ marginTop: 8 }}>
                    {renderSQLResultTable(result.result?.data || result.result)}
                  </div>
                </Space>
              </div>
            );
          }
          
          // 处理JSON_DOC类型的特殊情况
          const parsedJsonDoc = tryParseJsonDoc(result.result?.data);
          if (parsedJsonDoc) {
            return renderCompanyInfoCard(Array.isArray(parsedJsonDoc) ? parsedJsonDoc : [parsedJsonDoc]);
          }

          // 按照display_format处理不同类型
          switch(result.display_format.toLowerCase()) {
            case 'text':
              return renderTextResult(result.result?.data);
            
            case 'table':
              if (isTableData(result.result?.data)) {
                return renderDataTable(result.result.data);
              } else {
                return renderSQLResultTable(result.result?.data);
              }
            
            case 'graph':
              if (result.result?.data && isTableData(result.result.data)) {
                return renderFundTransferGraph(result.result.data);
              }
              break;
              
            case 'json':
            default:
              // 检查是否为SQL查询结果
              if (isSQLQueryResult(result)) {
                return renderSQLResultTable(result.result?.data || result.result);
              }
              
              if (isTableData(result.result?.data)) {
                return renderDataTable(result.result.data);
              }
              
              return (
                <JsonView
                  data={result.result?.data || result.result}
                  shouldExpandNode={() => true}
                />
              );
          }
        })()}
      </Card>
    );
    
    return content;
  };

  return (
    <div className="analysis-detail-container">
      <Card
        title={
          <Space>
            <Button type="link" icon={<ArrowLeftOutlined />} onClick={goBack} style={{ paddingLeft: 0 }}>
              返回
            </Button>
            <span>分析详情</span>
          </Space>
        }
      >
        <Descriptions bordered column={2} size="small" style={{ marginBottom: 20 }}>
          <Descriptions.Item label="查询内容" span={2}>{analysis.query}</Descriptions.Item>
          <Descriptions.Item label="创建时间">{formatDateTime(analysis.created_at)}</Descriptions.Item>
          <Descriptions.Item label="分析ID">{analysis.id}</Descriptions.Item>
        </Descriptions>

        <Tabs activeKey={activeTabKey} onChange={handleTabChange}>
          <TabPane tab="分析报告" key="1">
            <Card bordered={false} className="report-card">
              <ReactMarkdown
                children={analysis.result || "未生成分析报告"}
                remarkPlugins={[remarkGfm]}
                rehypePlugins={[rehypeRaw]}
                components={{
                  // 自定义表格渲染
                  table: ({...props}) => (
                    <div style={{ overflowX: 'auto', marginBottom: '16px' }}>
                      <table className="markdown-table" {...props} />
                    </div>
                  ),
                  // 自定义表格头部渲染
                  thead: ({...props}) => (
                    <thead className="markdown-thead" {...props} />
                  ),
                  // 自定义表格行渲染
                  tr: ({...props}) => (
                    <tr className="markdown-tr" {...props} />
                  ),
                  // 自定义表格单元格渲染
                  td: ({...props}) => (
                    <td className="markdown-td" {...props} />
                  ),
                  // 自定义表格头部单元格渲染
                  th: ({...props}) => (
                    <th className="markdown-th" {...props} />
                  )
                }}
              />
            </Card>
          </TabPane>
          
          <TabPane tab="执行过程" key="2">
            <div className="execution-process">
              <Timeline>
                {/* 意图分析步骤 */}
                <Timeline.Item 
                  dot={<AimOutlined style={{ color: '#1890ff' }} />}
                >
                  <div style={{ marginBottom: 8 }}>
                    <Text strong>意图分析</Text>
                  </div>
                  {renderIntentAnalysis()}
                </Timeline.Item>
                
                {/* 工具执行步骤 */}
                {executions.map((step: any, index: number) => {
                  return (
                    <Timeline.Item 
                      key={index}
                      dot={
                        step ? (
                          step.result?.success === false ? 
                            <CloseCircleOutlined style={{ color: '#ff4d4f' }} /> : 
                            <CheckCircleOutlined style={{ color: '#52c41a' }} />
                        ) : (
                          <ClockCircleOutlined style={{ color: '#1890ff' }} />
                        )
                      }
                    >
                      <div style={{ marginBottom: 8 }}>
                        <Space>
                          <Text strong>{step.step_id}: {step.tool.name}</Text>
                          {step && <ExecutionStatusTag result={step.result} />}
                        </Space>
                      </div>
                      <div style={{ marginBottom: 8 }}>
                        <Text>{step.description || step.purpose}</Text>
                      </div>
                      {step && step.tool && renderToolInfo(step.tool)}
                      {step.tool.name !== '自动SQL查询' && (
                      <div style={{ marginBottom: 8 }}>
                        <Text type="secondary">参数:</Text>
                        <div style={{ padding: '8px', background: '#f9f9f9', borderRadius: '4px', marginTop: '4px' }}>
                          <JsonView
                            data={step.parameters}
                            shouldExpandNode={() => true}
                          />
                        </div>
                      </div>
                      )}
                      {step && (
                        renderToolResult(step)
                      )}
                    </Timeline.Item>
                  );
                })}
                
                {/* 报告生成步骤 */}
                <Timeline.Item 
                  dot={<FileTextOutlined style={{ color: '#52c41a' }} />}
                >
                  <div style={{ marginBottom: 8 }}>
                    <Text strong>生成分析报告</Text>
                  </div>
                </Timeline.Item>
              </Timeline>
            </div>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default AnalysisDetail; 