/* Markdown 表格样式 */
.markdown-table {
  border-collapse: collapse;
  width: 100%;
  margin: 16px 0;
  font-size: 14px;
  line-height: 1.5;
  border: 1px solid #e8e8e8;
}

.markdown-thead {
  background-color: #fafafa;
}

.markdown-th {
  padding: 12px;
  text-align: left;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
  border-bottom: 1px solid #e8e8e8;
  white-space: nowrap;
}

.markdown-tr {
  border-bottom: 1px solid #e8e8e8;
}

.markdown-tr:hover {
  background-color: #fafafa;
}

.markdown-td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #e8e8e8;
}

/* 其他 Markdown 样式 */
.report-card {
  font-size: 14px;
  line-height: 1.8;
}

.report-card h1,
.report-card h2,
.report-card h3,
.report-card h4,
.report-card h5,
.report-card h6 {
  margin-top: 24px;
  margin-bottom: 16px;
}

.report-card p {
  margin-bottom: 16px;
}

.report-card ul,
.report-card ol {
  margin-bottom: 16px;
  padding-left: 24px;
}

.report-card code {
  background-color: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
}

.report-card pre {
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 6px;
  overflow: auto;
  margin-bottom: 16px;
}

.report-card blockquote {
  margin: 16px 0;
  padding: 0 16px;
  color: #666;
  border-left: 4px solid #ddd;
}

/* 响应式表格 */
@media screen and (max-width: 768px) {
  .markdown-table {
    display: block;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  .markdown-td,
  .markdown-th {
    white-space: nowrap;
  }
}

/* 悬浮取消按钮动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  50% {
    box-shadow: 0 4px 20px rgba(255, 77, 79, 0.3);
  }
  100% {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

/* 悬浮按钮容器样式 */
.floating-cancel-button {
  position: fixed;
  bottom: 100px;
  left: 50%;
  z-index: 1000;
  animation: fadeInUp 0.3s ease-out, pulse 2s ease-in-out infinite;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .floating-cancel-button {
    bottom: 15px;
    left: 50%;
    transform: translateX(-50%);
  }
}

/* 澄清过程相关动画 */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 澄清卡片动画 */
.clarification-card {
  animation: fadeInScale 0.3s ease-out;
}

.clarification-question {
  animation: slideInFromLeft 0.4s ease-out;
}

.clarification-answer {
  animation: slideInFromLeft 0.5s ease-out;
}

/* 智能交互状态指示器 */
.smart-interaction-indicator {
  animation: pulse 1.5s infinite;
}

/* 澄清过程渐变背景 */
.clarification-gradient {
  background: linear-gradient(135deg, #fff7e6 0%, #fff2d9 100%);
  transition: all 0.3s ease;
}

.clarification-gradient:hover {
  background: linear-gradient(135deg, #fff2d9 0%, #ffe7ba 100%);
}

.user-response-gradient {
  background: linear-gradient(135deg, #f6ffed 0%, #e6f7ff 100%);
  transition: all 0.3s ease;
}

.user-response-gradient:hover {
  background: linear-gradient(135deg, #e6f7ff 0%, #d9f7be 100%);
}