/* 完全重置所有元素的默认样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

*::before,
*::after {
  box-sizing: border-box;
}
/* 禁用过度滚动和下拉刷新 */
html {
  overscroll-behavior: none; /* 禁用所有过度滚动行为 */
  overscroll-behavior-y: none; /* 禁用垂直过度滚动 */
  overscroll-behavior-x: none; /* 禁用水平过度滚动 */
  height: 100vh;
}
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  min-height: 100vh;

  /* 添加这些属性来禁用过度滚动 */
  overscroll-behavior: none;
  overscroll-behavior-y: none;
  overscroll-behavior-x: none;
  
  /* 针对 iOS Safari 的特殊处理 */
  -webkit-overflow-scrolling: touch;
  
  /* 禁用下拉刷新 */
  touch-action: pan-x pan-y;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

.app-container {
  min-height: 100vh;
}

/* 新的侧边栏样式 - 统一登录页风格 */
.app-sidebar {
  background: #ffffff !important;
  border-right: 1px solid rgba(59, 130, 246, 0.15) !important;
  box-shadow: 2px 0 8px rgba(59, 130, 246, 0.08) !important;
  display: flex;
  flex-direction: column;
  height: 100vh; /* 确保侧边栏占满整个视口高度 */
  overflow: hidden; /* 防止整个侧边栏滚动 */
  position: relative; /* 为绝对定位的底部区域提供定位上下文 */
}

.sidebar-header {
  padding: 16px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.02) 0%, rgba(255, 255, 255, 0.05) 100%);
  flex-shrink: 0; /* 确保头部区域不会被压缩 */
}

.sidebar-header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.sidebar-header-icon {
  width: 32px;
  height: 32px;
  object-fit: contain;
}

.sidebar-company-brand {
  display: flex;
  align-items: center;
  gap: 2px;
}

.company-prefix {
  font-size: 18px;
  font-weight: 800;
  letter-spacing: 0px;
  text-transform: uppercase;
  background: linear-gradient(45deg, #00d4ff, #ff0080, #7928ca, #ff6b35);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  padding: 0px 3px;
  animation: tech-glow 2.5s ease-in-out infinite;
  cursor: default;
  position: relative;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
}

.company-prefix::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff);
  background-size: 400% 400%;
  border-radius: 3px;
  opacity: 0.1;
  animation: rainbow-flow 2s ease-in-out infinite;
  z-index: -1;
}

@keyframes rainbow-flow {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.company-name {
  font-size: 18px;
  font-weight: 800;
  letter-spacing: 1px;
  background: linear-gradient(135deg, #00d4ff 0%, #0099ff 25%, #7928ca 50%, #ff0080 75%, #00d4ff 100%);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;
  position: relative;
  animation: cyber-wave 3s ease-in-out infinite;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.3), 0 0 20px rgba(121, 40, 202, 0.2);
}

@keyframes tech-glow {
  0%, 100% {
    background-position: 0% 50%;
    filter: brightness(1);
  }
  25% {
    background-position: 50% 0%;
    filter: brightness(1.2);
  }
  50% {
    background-position: 100% 50%;
    filter: brightness(1.1);
  }
  75% {
    background-position: 50% 100%;
    filter: brightness(1.3);
  }
}

@keyframes cyber-wave {
  0%, 100% {
    background-position: 0% 50%;
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.3), 0 0 20px rgba(121, 40, 202, 0.2);
  }
  25% {
    background-position: 25% 25%;
    text-shadow: 0 0 15px rgba(0, 153, 255, 0.4), 0 0 25px rgba(255, 0, 128, 0.3);
  }
  50% {
    background-position: 100% 50%;
    text-shadow: 0 0 20px rgba(121, 40, 202, 0.5), 0 0 30px rgba(0, 212, 255, 0.4);
  }
  75% {
    background-position: 75% 75%;
    text-shadow: 0 0 15px rgba(255, 0, 128, 0.4), 0 0 25px rgba(0, 212, 255, 0.3);
  }
}

.sidebar-collapse-btn {
  color: #64748b !important;
  border: 1px solid rgba(59, 130, 246, 0.2) !important;
  border-radius: 6px !important;
  width: 32px !important;
  height: 32px !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.3s ease !important;
}

.sidebar-collapse-btn:hover {
  color: #3b82f6 !important;
  border-color: rgba(59, 130, 246, 0.4) !important;
  background: rgba(59, 130, 246, 0.05) !important;
}

.sidebar-project-section {
  padding: 16px 20px;
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
  flex-shrink: 0; /* 确保项目选择区域不会被压缩 */
}

.sidebar-project-title {
  color: #64748b;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
}

.sidebar-conversations-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
}

/* 对话容器样式 */
.sidebar-conversations-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 240px); /* 减去头部、项目选择和底部区域的高度 */
}

/* 对话标题区域样式 */
.sidebar-conversations-header {
  padding: 12px 20px;
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.new-conversation-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
  border: none !important;
  border-radius: 50% !important;
  font-weight: 500 !important;
  width: 24px !important;
  height: 24px !important;
  padding: 0 !important;
  font-size: 12px !important;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1) !important;
  transition: all 0.2s ease !important;
  color: white !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.new-conversation-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.2) !important;
  color: white !important;
}

.sidebar-conversations-list {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 16px 20px 30px 20px; /* 底部增加padding，为管理工具留空间 */
  min-height: 0;
  max-height: calc(100vh - 300px); /* 确保不会超出可用空间 */
}

/* 自定义滚动条 */
.sidebar-conversations-list::-webkit-scrollbar {
  width: 6px;
}

.sidebar-conversations-list::-webkit-scrollbar-track {
  background: rgba(59, 130, 246, 0.05);
  border-radius: 3px;
}

.sidebar-conversations-list::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.3);
  border-radius: 3px;
}

.sidebar-conversations-list::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.5);
}

.conversation-item {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
  border-radius: 8px;
  background: rgba(59, 130, 246, 0.02);
  border: 1px solid rgba(59, 130, 246, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.conversation-item:hover {
  background: rgba(59, 130, 246, 0.08);
  border-color: rgba(59, 130, 246, 0.2);
  transform: translateY(-1px);
}

.conversation-item.active {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-color: #3b82f6;
  color: white;
}

.conversation-item.active:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
}

.conversation-content {
  flex: 1;
  padding: 12px 16px;
  cursor: pointer;
  min-width: 0; /* 确保可以收缩 */
}

.conversation-title {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.conversation-actions {
  padding: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.conversation-item:hover .conversation-actions {
  opacity: 1;
}

.conversation-delete-btn {
  color: #ef4444 !important;
  border: none !important;
  background: transparent !important;
  width: 24px !important;
  height: 24px !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 4px !important;
  transition: all 0.3s ease !important;
}

.conversation-delete-btn:hover {
  background: rgba(239, 68, 68, 0.1) !important;
  color: #dc2626 !important;
}

.conversation-item.active .conversation-delete-btn {
  color: rgba(255, 255, 255, 0.8) !important;
}

.conversation-item.active .conversation-delete-btn:hover {
  color: white !important;
  background: rgba(255, 255, 255, 0.2) !important;
}

/* 加载更多按钮样式 */
.load-more-container {
  padding: 12px 0;
  border-top: 1px solid rgba(59, 130, 246, 0.1);
  margin-top: 8px;
}

.load-more-btn {
  color: #64748b !important;
  font-size: 13px !important;
  height: 32px !important;
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
  border: 1px solid rgba(59, 130, 246, 0.1) !important;
  background: transparent !important;
}

.load-more-btn:hover {
  color: #3b82f6 !important;
  background: rgba(59, 130, 246, 0.05) !important;
  border-color: rgba(59, 130, 246, 0.2) !important;
}

.sidebar-section-title {
  color: #64748b;
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 0.3px;
  margin-bottom: 0;
  display: flex;
  align-items: center;
}

.sidebar-project-select .ant-select-selector {
  border: 1px solid rgba(59, 130, 246, 0.2) !important;
  border-radius: 8px !important;
  background: rgba(255, 255, 255, 0.8) !important;
  transition: all 0.3s ease !important;
}

.sidebar-project-select .ant-select-selector:hover {
  border-color: rgba(59, 130, 246, 0.4) !important;
  background: rgba(255, 255, 255, 0.95) !important;
}

.sidebar-project-select.ant-select-focused .ant-select-selector {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
}

.sidebar-bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px 20px 18px 20px;
  border-top: 1px solid rgba(59, 130, 246, 0.12);
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.98));
  backdrop-filter: blur(12px);
  z-index: 10;
  box-shadow: 0 -4px 16px rgba(59, 130, 246, 0.06);
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.sidebar-bottom-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 4px;
}

.sidebar-icon-btn {
  width: 38px !important;
  height: 38px !important;
  border-radius: 11px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  font-size: 16px !important;
  border: 1px solid transparent !important;
  position: relative !important;
  overflow: hidden !important;
}

.sidebar-icon-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 11px;
  padding: 1px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: exclude;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* 管理功能按钮样式 */
.sidebar-management-btn {
  color: #64748b !important;
  background: linear-gradient(135deg, rgba(100, 116, 139, 0.08), rgba(100, 116, 139, 0.04)) !important;
  box-shadow: 
    0 2px 6px rgba(100, 116, 139, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
  border: 1px solid rgba(100, 116, 139, 0.15) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.sidebar-management-btn:hover {
  background: linear-gradient(135deg, rgba(100, 116, 139, 0.12), rgba(100, 116, 139, 0.08)) !important;
  color: #475569 !important;
  transform: translateY(-1px) scale(1.02) !important;
  box-shadow: 
    0 4px 12px rgba(100, 116, 139, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.25) !important;
  border-color: rgba(100, 116, 139, 0.25) !important;
}

.sidebar-management-btn:hover::before {
  opacity: 1;
}

/* 选中状态样式 */
.sidebar-management-btn.active {
  color: #3b82f6 !important;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(59, 130, 246, 0.1)) !important;
  box-shadow: 
    0 3px 12px rgba(59, 130, 246, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    inset 0 0 0 1px rgba(59, 130, 246, 0.2) !important;
  border: 1px solid rgba(59, 130, 246, 0.3) !important;
  transform: translateY(-1px) !important;
}

.sidebar-management-btn.active:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(59, 130, 246, 0.15)) !important;
  color: #1d4ed8 !important;
  transform: translateY(-1px) scale(1.02) !important;
  box-shadow: 
    0 4px 16px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.4),
    inset 0 0 0 1px rgba(59, 130, 246, 0.3) !important;
  border-color: rgba(59, 130, 246, 0.4) !important;
}

.sidebar-logout-btn {
  color: #64748b !important;
  background: linear-gradient(135deg, rgba(100, 116, 139, 0.12), rgba(100, 116, 139, 0.08)) !important;
  box-shadow: 
    0 2px 8px rgba(100, 116, 139, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
  border: 1px solid rgba(100, 116, 139, 0.2) !important;
}

.sidebar-logout-btn:hover {
  background: linear-gradient(135deg, rgba(100, 116, 139, 0.18), rgba(100, 116, 139, 0.12)) !important;
  color: #475569 !important;
  transform: translateY(-1px) scale(1.02) !important;
  box-shadow: 
    0 4px 16px rgba(100, 116, 139, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
  border-color: rgba(100, 116, 139, 0.3) !important;
}

.sidebar-logout-btn:hover::before {
  opacity: 1;
}

/* 管理功能标签区域 */
.sidebar-management-labels {
  padding: 0px 4px 0 4px;
  margin-top: -4px;
}

.management-label-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
}

.management-label {
  font-size: 10px;
  color: #94a3b8;
  font-weight: 500;
  text-align: center;
  width: 38px;
  height: 14px;
  line-height: 14px;
  transition: all 0.3s ease;
  letter-spacing: 0.3px;
}

.management-label.active {
  color: #3b82f6;
  font-weight: 600;
}

.management-label.logout-label {
  color: #94a3b8;
}



.app-main-layout {
  background: #ffffff;
}

.app-content {
  background: #ffffff;
  height: calc(100vh - 64px);
  overflow: auto;
  padding: 24px;
  box-sizing: border-box;
}

/* 侧边栏折叠状态样式 */
.app-sidebar.ant-layout-sider-collapsed .sidebar-header {
  padding: 16px 12px;
  justify-content: center;
  flex-direction: column;
  gap: 8px;
}

.app-sidebar.ant-layout-sider-collapsed .sidebar-header-left {
  flex-direction: column;
  gap: 4px;
}

.app-sidebar.ant-layout-sider-collapsed .sidebar-project-section,
.app-sidebar.ant-layout-sider-collapsed .sidebar-conversations-section,
.app-sidebar.ant-layout-sider-collapsed .sidebar-section-title {
  display: none;
}

.app-sidebar.ant-layout-sider-collapsed .sidebar-bottom {
  padding: 16px 8px 18px 8px;
  left: 0;
  right: 0;
  width: 80px; /* 折叠状态下的宽度 */
}

.app-sidebar.ant-layout-sider-collapsed .sidebar-bottom-actions {
  flex-direction: column;
  justify-content: center;
  gap: 12px;
  padding: 4px 0;
}

.app-sidebar.ant-layout-sider-collapsed .sidebar-management-labels {
  display: none;
}

.app-sidebar.ant-layout-sider-collapsed .sidebar-icon-btn {
  width: 34px !important;
  height: 34px !important;
  font-size: 15px !important;
  border-radius: 9px !important;
}

.app-sidebar.ant-layout-sider-collapsed .sidebar-icon-btn::before {
  border-radius: 9px;
}

.page-header {
  margin-bottom: 20px;
}

.action-buttons {
  margin-bottom: 20px;
}

.card-list {
  margin-bottom: 20px;
}

.tool-form-item {
  margin-bottom: 20px;
}

.code-editor {
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  min-height: 200px;
}

.analysis-result {
  margin-top: 20px;
  white-space: pre-wrap;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
}

.dashboard-cards {
  margin-bottom: 24px;
}

.dashboard-card {
  cursor: pointer;
}

.dashboard-stat {
  font-size: 24px;
  font-weight: bold;
}

/* 顶部导航栏样式 */
.app-header {
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  padding: 0 24px;
  height: 64px;
  line-height: 64px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.app-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.app-header-left {
  flex: 1;
}

.app-header-right {
  display: flex;
  align-items: center;
}

.current-user {
  color: #666;
  font-size: 14px;
  display: flex;
  align-items: center;
  padding: 0 8px;
}

.logout-btn {
  color: #666;
  transition: all 0.3s;
}

.logout-btn:hover {
  color: #1890ff;
}

/* 自定义退出确认弹框样式 */
.custom-logout-modal {
  top: 0 !important;
  padding-bottom: 0 !important;
}

.custom-logout-modal .ant-modal-wrap {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-height: 100vh !important;
}

.custom-logout-modal .ant-modal {
  top: 0 !important;
  margin: 0 !important;
  padding-bottom: 0 !important;
}

.custom-logout-modal .ant-modal-content {
  border-radius: 16px !important;
  overflow: hidden !important;
  box-shadow: 0 20px 40px rgba(59, 130, 246, 0.15) !important;
  border: 1px solid rgba(59, 130, 246, 0.1) !important;
  margin: 0 auto !important;
}

.custom-logout-modal .ant-modal-header {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(59, 130, 246, 0.02)) !important;
  border-bottom: 1px solid rgba(59, 130, 246, 0.1) !important;
  padding: 24px 24px 20px 24px !important;
  text-align: center !important;
}

.custom-logout-modal .ant-modal-title {
  color: #1e293b !important;
  font-size: 18px !important;
  font-weight: 600 !important;
  text-align: center !important;
  margin: 0 !important;
}

.custom-logout-modal .ant-modal-body {
  padding: 24px !important;
  background: #ffffff !important;
  text-align: center !important;
}

.custom-logout-modal .ant-modal-body .ant-modal-confirm-body {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  gap: 20px !important;
  padding: 8px 0 !important;
}

.custom-logout-modal .ant-modal-body .ant-modal-confirm-body .anticon {
  font-size: 32px !important;
  color: #3b82f6 !important;
  margin-bottom: 12px !important;
  padding: 16px !important;
  background: rgba(59, 130, 246, 0.1) !important;
  border-radius: 50% !important;
  width: 64px !important;
  height: 64px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.custom-logout-modal .ant-modal-body .ant-modal-confirm-content {
  color: #64748b !important;
  font-size: 15px !important;
  line-height: 1.6 !important;
  margin: 0 !important;
  text-align: center !important;
}

.custom-logout-modal .ant-modal-footer {
  background: rgba(248, 250, 252, 0.8) !important;
  border-top: 1px solid rgba(59, 130, 246, 0.08) !important;
  padding: 20px 24px 24px 24px !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  gap: 16px !important;
  text-align: center !important;
}

.custom-logout-modal .ant-modal-confirm-btns {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  gap: 16px !important;
  margin: 0 !important;
  flex-direction: row-reverse !important;
}

.custom-logout-modal .ant-btn {
  height: 42px !important;
  border-radius: 10px !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  padding: 0 28px !important;
  min-width: 110px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  border: none !important;
  flex-shrink: 0 !important;
}

.custom-logout-modal .ant-btn-default {
  background: rgba(100, 116, 139, 0.08) !important;
  color: #64748b !important;
  box-shadow: 0 2px 4px rgba(100, 116, 139, 0.1) !important;
}

.custom-logout-modal .ant-btn-default:hover {
  background: rgba(100, 116, 139, 0.12) !important;
  color: #475569 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(100, 116, 139, 0.15) !important;
}

.custom-logout-modal .ant-btn-primary {
  background: linear-gradient(135deg, #3b82f6, #2563eb) !important;
  color: #ffffff !important;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3) !important;
}

.custom-logout-modal .ant-btn-primary:hover {
  background: linear-gradient(135deg, #2563eb, #1d4ed8) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4) !important;
}

.custom-logout-modal .ant-modal-close {
  top: 16px !important;
  right: 16px !important;
}

.custom-logout-modal .ant-modal-close-x {
  width: 32px !important;
  height: 32px !important;
  line-height: 32px !important;
  color: #94a3b8 !important;
  font-size: 16px !important;
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
}

.custom-logout-modal .ant-modal-close-x:hover {
  background: rgba(100, 116, 139, 0.1) !important;
  color: #64748b !important;
}

/* 丝滑动画效果 - 用于对话列表刷新 */
@keyframes slideInFromTop {
  0% {
    opacity: 0;
    transform: translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

/* 优化对话列表项的过渡效果 */
.conversation-item {
  transition: all 0.2s ease-in-out !important;
}

.conversation-item:hover {
  transform: translateX(2px) !important;
} 