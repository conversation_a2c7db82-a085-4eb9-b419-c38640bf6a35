// 项目权限接口
export interface ProjectPermissions {
  can_edit: boolean;
  can_delete: boolean;
  can_manage_members: boolean;
}

// 项目接口
export interface Project {
  id: string;
  name: string;
  description: string;
  user_id: number;
  owner_id: number;
  owner_username?: string; // 项目所有者用户名
  visibility: 'PRIVATE' | 'PUBLIC';
  org_id: number;
  report_prompt?: string; // 项目自定义报告生成提示词
  created_at: string;
  updated_at: string;
  permissions?: ProjectPermissions; // 可选的权限信息
}

// 创建项目请求
export interface CreateProjectRequest {
  name: string;
  description: string;
  visibility?: 'PRIVATE' | 'PUBLIC';
}

// 数据源类型枚举
export enum DataSourceType {
  ORACLE = 'ORACLE',
  MYSQL = 'MYSQL',
  POSTGRESQL = 'POSTGRESQL',
  MSSQL = 'MSSQL',
  HTTP_API = 'HTTP_API',
}

// 数据源接口
export interface DataSource {
  id: string;
  project_id: string;
  name: string;
  description: string;
  type: DataSourceType;
  config: Record<string, any>;
  db_version?: string; // 数据库版本信息
  created_at: string;
  updated_at: string;
}

// 创建数据源请求
export interface CreateDataSourceRequest {
  project_id: string;
  name: string;
  description: string;
  type: DataSourceType;
  config: Record<string, any>;
}

// 工具类型枚举
export enum ToolType {
  SQL = 'SQL',
  API = 'API',
  PYTHON = 'PYTHON',
  GRAPH = 'GRAPH',
  VECTOR = 'VECTOR',
  CUSTOM = 'CUSTOM',
  AUTO_SQL = 'AUTO_SQL',
  CHART = 'CHART'
}

// 工具接口
export interface Tool {
  id: string;
  data_source_id: string;
  name: string;
  description: string;
  tool_type: ToolType;
  template: string;
  parameters: Record<string, any>;
  created_at: string;
  updated_at: string;
}

// 创建工具请求
export interface CreateToolRequest {
  data_source_id: string;
  name: string;
  description: string;
  tool_type: ToolType;
  template: string;
  parameters: Record<string, any>;
}

// 分析记录接口
export interface Analysis {
  id: string;
  project_id: string;
  query: string;
  intent_analysis: Record<string, any>;
  result: string;
  created_at: string;
}

// 创建分析请求
export interface CreateAnalysisRequest {
  project_id: string;
  query: string;
}

// 工具执行记录接口
export interface ToolExecution {
  id: string;
  analysis_id: string;
  tool_id: string;
  parameters: Record<string, any>;
  result: string;
  execution_time: number;
  created_at: string;
  step_id?: string;  // 添加可选的步骤ID字段
}

// 分页请求接口
export interface PaginationRequest {
  page?: number;
  limit?: number;
  search?: string;
}

// 分页响应接口
export interface PaginationResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
}

// API响应接口
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

// 测试数据源连接请求
export interface TestConnectionRequest {
  type: DataSourceType;
  config: Record<string, any>;
}

// 测试工具请求
export interface TestToolRequest {
  parameters: Record<string, any>;
}

// 用户接口
export interface User {
  id: number;
  username: string;
  email?: string;
  is_active: boolean;
  is_superuser: boolean;
  pid: number;
  role_id: number;
  org_id: number;
  can_create_project: boolean;
  created_at: string;
  updated_at: string;
}

// 企业组织接口
export interface Organization {
  id: number;
  name: string;
  code: string;
  description?: string;
  status: 'ACTIVE' | 'INACTIVE';
  created_at: string;
  updated_at: string;
}

// 系统角色接口
export interface SystemRole {
  id: number;
  name: string;
  code: string;
  description?: string;
  level: number;
  created_at: string;
}

// 项目角色接口
export interface ProjectRole {
  id: number;
  name: string;
  code: string;
  description?: string;
  permissions: string[];
  created_at: string;
}

// 项目成员接口
export interface ProjectMember {
  id: number;
  project_id: string;
  user_id: number;
  project_role_id: number;
  invited_by?: number;
  joined_at: string;
  status: 'ACTIVE' | 'INACTIVE';
  username?: string;
  email?: string;
  role_name?: string;
  invited_by_username?: string;
}

// 项目成员列表响应
export interface ProjectMemberList {
  total: number;
  members: ProjectMember[];
}

// 邀请成员请求
export interface InviteMemberRequest {
  user_id: number;
  project_role_id: number;
}

// 更新成员请求
export interface UpdateMemberRequest {
  project_role_id?: number;
  status?: string;
}

// 创建用户请求
export interface CreateUserRequest {
  username: string;
  email?: string;
  password: string;
  is_active?: boolean;
  can_create_project?: boolean;
}

// 更新用户请求
export interface UpdateUserRequest {
  username?: string;
  email?: string;
  password?: string;
  is_active?: boolean;
  can_create_project?: boolean;
} 