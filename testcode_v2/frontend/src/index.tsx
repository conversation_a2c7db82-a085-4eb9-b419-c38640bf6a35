import React from 'react';
import ReactDOM from 'react-dom/client';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import enUS from 'antd/locale/en_US';
import { useTranslation } from 'react-i18next';
import App from './App';
import './index.css';
import './i18n'; // 导入i18n配置

// Ant Design 国际化配置
const getAntdLocale = (language: string) => {
  switch (language) {
    case 'zh-CN': return zhCN;
    case 'en-US': return enUS;
    default: return zhCN;
  }
};

// 包装App组件以支持国际化
const AppWithI18n: React.FC = () => {
  const { i18n } = useTranslation();
  
  return (
    <ConfigProvider locale={getAntdLocale(i18n.language)}>
      <App />
    </ConfigProvider>
  );
};

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

root.render(<AppWithI18n />); 