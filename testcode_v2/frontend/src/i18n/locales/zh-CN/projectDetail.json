{"title": "项目详情", "tabs": {"overview": "项目概览", "basicInfo": "基本信息", "dataSource": "数据源信息", "dataTables": "数据表信息", "memberManagement": "成员管理", "reportSettings": "报告设置"}, "buttons": {"backToList": "返回项目列表", "editProject": "编辑项目", "deleteProject": "删除项目", "addDataSource": "添加数据源", "editDataSource": "编辑数据源", "testConnection": "测试连接", "confirmDelete": "确认删除", "selectDataTable": "选择数据表", "save": "保存", "cancel": "取消", "resetToDefault": "重置为默认", "editTables": "编辑表", "editTableDescriptions": "编辑表描述", "editPrompt": "编辑提示词", "selectTablesFor": "为 {{name}} 选择表"}, "modals": {"editProject": "编辑项目", "confirmDelete": "确认删除", "deleteMessage": "确定要删除这个项目吗？此操作无法恢复。", "editDataSource": "编辑数据源", "addDataSource": "添加数据源", "selectDataTable": "选择数据表", "selectTables": "选择数据表", "editReportPrompt": "编辑报告生成提示词"}, "labels": {"projectName": "项目名称", "projectDescription": "项目描述", "visibility": "可见性", "owner": "所有者", "createdTime": "创建时间", "dataSourceName": "数据源名称", "dataSourceType": "数据源类型", "dataSourceDescription": "数据源描述", "connectionConfig": "连接配置", "hostAddress": "主机地址", "port": "端口", "serviceName": "服务名", "username": "用户名", "password": "密码", "databaseName": "数据库名", "baseUrl": "基础URL", "requestHeaders": "请求头", "reportPrompt": "报告生成提示词", "projectId": "项目ID", "projectOwner": "项目所有者", "projectVisibility": "项目可见性", "userId": "用户ID", "noDataSourceConfigured": "未配置数据源", "noTablesSelected": "未选择数据表", "noTablesSelectedForDataSource": "该数据源暂无选择的表", "currentPrompt": "当前提示词", "usingDefaultPrompt": "使用默认提示词", "description": "说明", "reportPromptDescription": "自定义提示词将与系统默认提示词拼接使用，影响AI生成的分析报告格式和内容重点。", "connectionSuccess": "连接成功", "tablesSelected": "已选择 {{count}} 个表"}, "dataSourceTypes": {"oracle": "Oracle 数据库", "mysql": "MySQL 数据库", "postgresql": "PostgreSQL 数据库", "mssql": "SQL Server 数据库", "httpApi": "HTTP API"}, "messages": {"projectUpdateSuccess": "项目更新成功", "projectUpdateFailed": "更新项目失败", "projectDeleteSuccess": "项目删除成功", "projectDeleteFailed": "删除项目失败", "dataSourceAddSuccess": "数据源添加成功", "dataSourceAddFailed": "添加数据源失败", "dataSourceUpdateSuccess": "数据源更新成功", "dataSourceUpdateFailed": "更新数据源失败", "dataSourceDeleteSuccess": "数据源删除成功", "dataSourceDeleteFailed": "删除数据源失败", "connectionSuccess": "连接成功", "connectionFailed": "连接失败", "fetchProjectFailed": "获取项目信息失败", "fetchDataSourceFailed": "获取数据源信息失败", "fetchTablesFailed": "获取数据表信息失败", "saveTableSelectionSuccess": "数据表选择保存成功", "saveTableSelectionFailed": "保存数据表选择失败", "tablesSaveSuccess": "表保存成功", "reportPromptUpdateSuccess": "报告提示词更新成功", "reportPromptUpdateFailed": "更新报告提示词失败", "testConnectionFirst": "请先成功测试连接"}, "placeholders": {"enterProjectName": "请输入项目名称", "enterProjectDescription": "请输入项目描述", "enterDataSourceName": "请输入数据源名称", "enterDataSourceDescription": "请输入数据源描述", "selectDataSourceType": "请选择数据源类型", "enterHostAddress": "如：localhost或IP地址", "enterPort": "如：3306", "enterServiceName": "如：ORCL", "enterUsername": "如：system", "enterPassword": "输入密码", "enterDatabaseName": "请输入数据库名", "enterBaseUrl": "如：https://api.example.com/v1", "enterRequestHeaders": "以JSON格式输入请求头信息", "requestHeadersExample": "如：{\n  \"Content-Type\": \"application/json\",\n  \"Authorization\": \"Bearer token\"\n}", "selectProjectVisibility": "选择项目可见性", "enterDataSourceNameHere": "输入数据源名称", "enterDataSourceDescriptionOptional": "输入数据源描述（可选）", "selectDataSourceTypeHere": "选择数据源类型", "enterCustomReportPrompt": "请输入自定义的报告生成提示词..."}, "validation": {"projectNameRequired": "项目名称为必填项", "dataSourceNameRequired": "数据源名称为必填项", "dataSourceTypeRequired": "数据源类型为必填项", "hostRequired": "请输入主机地址", "portRequired": "请输入端口", "serviceNameRequired": "请输入服务名", "usernameRequired": "请输入用户名", "passwordRequired": "请输入密码", "databaseRequired": "请输入数据库名", "baseUrlRequired": "请输入API基础URL", "projectVisibilityRequired": "请选择项目可见性", "pleaseEnterDataSourceName": "请输入数据源名称", "pleaseSelectDataSourceType": "请选择数据源类型", "pleaseEnterReportPrompt": "请输入报告生成提示词"}, "noDescription": "暂无描述", "visibility": {"allMembersVisible": "企业内所有成员可见", "invitedMembersOnly": "仅邀请成员可见"}, "dividers": {"connectionConfig": "连接配置"}, "prompts": {"customPromptDescription": "自定义提示词将与系统默认提示词拼接使用，影响AI生成的分析报告格式和内容重点。系统会自动提供分析结果和图表信息，您只需定义报告结构和关注点。留空则仅使用系统默认模板。"}, "defaultReportPrompt": "请根据分析结果生成一份专业的数据分析报告，包含以下部分：\n\n## 1. 执行摘要\n- 简要概述分析目的和主要发现\n- 突出关键指标和重要结论\n\n## 2. 数据分析详述\n- 详细分析各项数据指标\n- 识别数据中的趋势、模式和异常\n- 提供数据支撑的洞察\n- 在合适的位置智能插入相关图表来支撑分析\n\n## 3. 关键发现\n- 基于数据分析得出的核心发现\n- 突出重要的业务洞察\n- 识别潜在的机会和风险点\n\n## 4. 结论与建议\n- 基于分析得出的核心结论\n- 提供具体可行的业务建议\n- 说明建议的优先级和预期效果\n\n请确保报告内容客观、准确，使用清晰的结构和专业的语言。在描述数据趋势和关键发现时，适当引用相关图表来增强说服力。"}