{"title": "工具管理", "edit": {"createTool": "创建工具", "editTool": "编辑工具", "backToList": "返回列表", "saveChanges": "保存修改", "selectProjectFirst": "请先选择项目", "selectProjectDescription": "请在顶部导航栏选择一个项目，然后再创建工具", "fetchDataError": "未能获取工具数据", "fetchDetailError": "获取工具详情失败，请稍后重试", "updateSuccess": "工具更新成功", "createSuccess": "工具创建成功", "updateError": "更新工具失败，请稍后重试", "createError": "创建工具失败，请稍后重试"}, "form": {"name": "工具名称", "nameRequired": "请输入工具名称", "namePlaceholder": "请输入工具名称，例如：公司信息查询", "description": "工具描述", "descriptionPlaceholder": "请输入工具详细描述...", "type": "工具类型", "typeRequired": "请选择工具类型", "template": "工具模板", "templateRequired": "请输入工具模板", "templatePlaceholder": "请输入SQL查询语句或API调用模板...", "parameters": "参数设置", "paramName": "参数名称", "paramNameRequired": "请输入参数名称", "paramNamePlaceholder": "参数名称", "paramType": "参数类型", "paramTypeRequired": "请选择参数类型", "paramDescription": "参数描述", "paramDescriptionPlaceholder": "参数描述", "paramRequired": "是否必填", "paramDefault": "默认值", "paramDefaultPlaceholder": "默认值", "addParameter": "添加参数"}, "type": {"sql": "SQL查询", "api": "API调用", "python": "Python脚本", "graph": "图数据查询", "vector": "向量检索", "custom": "自定义工具", "autoSql": "自动SQL", "chart": "图表工具"}, "paramType": {"string": "字符串", "number": "数字", "boolean": "布尔值", "array": "数组", "object": "对象"}, "detail": {"title": "工具详情", "loading": "加载中...", "error": "错误", "fetchError": "未能获取工具信息", "fetchDetailError": "获取工具详情失败", "fetchDetailErrorRetry": "获取工具详情失败，请稍后重试", "deleteSuccess": "工具删除成功", "deleteError": "删除工具失败", "deleteErrorRetry": "删除工具失败，请稍后重试", "breadcrumb": {"home": "首页", "toolManagement": "工具管理", "toolDetail": "工具详情"}, "noDescription": "无描述", "backToList": "返回列表", "edit": "编辑", "delete": "删除", "confirmDelete": "确定要删除这个工具吗？", "confirmDeleteDescription": "此操作不可逆，删除后数据将无法恢复。", "confirm": "确定", "cancel": "取消", "basicInfo": "基本信息", "toolId": "工具ID", "toolName": "工具名称", "toolDescription": "工具描述", "none": "无", "toolType": "工具类型", "dataSource": "数据源", "createdTime": "创建时间", "updatedTime": "更新时间", "template": "模板", "parameters": "参数定义", "parameter": "参数", "unnamed": "未命名", "type": "类型:", "unspecified": "未指定", "description": "描述:", "required": "必填:", "yes": "是", "no": "否", "defaultValue": "默认值:", "noParameters": "无参数定义", "testTool": "测试工具"}, "test": {"title": "测试模式", "description": "在这里可以通过填写参数来测试工具的执行效果"}}