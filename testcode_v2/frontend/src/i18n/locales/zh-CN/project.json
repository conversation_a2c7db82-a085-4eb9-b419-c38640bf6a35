{"title": "项目管理", "list": {"title": "项目列表", "createProject": "创建项目", "name": "项目名称", "description": "描述", "visibility": "可见性", "createdAt": "创建时间", "updatedAt": "更新时间", "status": "状态", "owner": "负责人", "members": "成员", "createdTime": "创建时间", "actions": "操作", "edit": "编辑", "delete": "删除", "view": "查看", "viewDetail": "查看详情", "settings": "设置", "editProject": "编辑项目", "deleteConfirm": "确定要删除此项目吗？删除后无法恢复。", "deleteSuccess": "项目删除成功", "deleteError": "项目删除失败", "updateSuccess": "项目更新成功", "createSuccess": "项目创建成功", "nameExists": "项目名称已存在，请使用其他名称"}, "wizard": {"title": "创建项目向导", "step1": "项目信息", "step2": "数据源配置", "step3": "表格选择", "step4": "表格描述", "step5": "智能检测", "next": "下一步", "steps": {"project": "项目信息", "dataSource": "数据源配置", "tables": "表格选择", "complete": "完成"}, "memberManagement": {"title": "项目成员管理", "addMember": "添加成员", "noInvitableUsers": "暂无可邀请用户", "username": "用户名", "selectUser": "选择用户", "selectUserRequired": "请选择要添加的用户", "selectUserPlaceholder": "请选择用户", "projectRole": "项目角色", "projectRoleRequired": "请选择项目角色", "selectRole": "请选择角色", "collaborator": "协作者", "collaboratorDescription": "协作者 - 可操作+管理工具", "observerDescription": "观察者 - 可查看+执行分析", "addSuccess": "成员添加成功", "addFailed": "添加成员失败", "removeSuccess": "成员移除成功"}, "dataSource": {"oracle": "Oracle 数据库", "mysql": "MySQL 数据库", "postgresql": "PostgreSQL 数据库", "mssql": "SQL Server 数据库", "httpApi": "HTTP API"}, "form": {"projectName": "项目名称", "projectNameRequired": "请输入项目名称", "projectNamePlaceholder": "请输入项目名称", "projectDescription": "项目描述", "projectDescriptionPlaceholder": "请输入项目描述", "projectVisibility": "项目可见性", "projectVisibilityRequired": "请选择项目可见性", "visibilityPrivate": "私有项目", "visibilityPublic": "公开项目"}, "validation": {"hostRequired": "请输入主机地址", "portRequired": "请输入端口", "serviceNameRequired": "请输入服务名", "usernameRequired": "请输入用户名", "passwordRequired": "请输入密码", "databaseRequired": "请输入数据库名", "baseUrlRequired": "请输入API基础URL"}, "placeholders": {"hostExample": "如：localhost或IP地址", "oraclePort": "如：1521", "mysqlPort": "如：3306 (MySQL) / 5432 (PostgreSQL) / 1433 (MSSQL)", "serviceNameExample": "如：ORCL", "oracleUsername": "如：system", "mysqlUsername": "如：root", "databaseExample": "如：example_db", "baseUrlExample": "如：https://api.example.com/v1", "headersExample": "如：{\"Content-Type\": \"application/json\", \"Authorization\": \"Bearer token\"}"}, "labels": {"baseUrl": "基础URL"}, "tooltips": {"requestHeaders": "以JSON格式输入请求头信息"}, "messages": {"projectCreated": "项目创建成功", "projectCreateFailed": "项目创建失败", "projectNameExists": "项目名称已存在，请使用其他名称", "projectCompleted": "项目创建完成", "projectChangeNotSupported": "检测到项目信息变更，当前版本不支持修改已创建的项目信息。请创建新项目。", "testConnectionFirst": "请先测试连接成功后再提交"}, "tableSelection": {"title": "选择分析表", "dataSourceIdMissing": "数据源ID不存在，请重新配置数据源", "noTablesAvailable": "该数据源下暂无可用表格", "refreshSuccess": "刷新成功，获取到 {{count}} 个表格", "invalidTableListFormat": "获取到的表格列表格式不正确", "fetchTablesFailed": "获取数据表列表失败", "requestTimeout": "请求超时，请检查网络连接或稍后重试", "selectAtLeastOneTable": "请至少选择一个表格", "readingTables": "正在读取数据库表格信息...", "pleaseWait": "请稍候，这可能需要几秒钟时间", "unableToFetchTables": "无法获取数据表信息，请检查数据源配置或网络连接", "refetch": "重新获取", "description": "请从以下列表中选择需要进行分析的数据表。系统将自动提取表结构和样本数据，用于后续的分析和SQL生成。", "tableCount": "共 {{total}} 个表，已选择 {{selected}} 个表", "usingCachedData": "使用缓存数据", "selectAll": "全选", "deselectAll": "取消全选", "refreshTableList": "刷新表格列表", "tableName": "表名", "paginationInfo": "第 {{start}}-{{end}} 条，共 {{total}} 条", "refreshList": "刷新列表", "backToDataSource": "返回数据源配置", "nextStep": "下一步（{{status}}）", "selectedCount": "已选{{count}}个表", "noTableSelected": "未选择表格"}, "tableDescription": {"title": "补充表描述（可选）", "saveSelectedTablesSuccess": "已选择的表保存成功", "saveFailed": "保存失败", "saveTablesFailed": "保存表格失败", "fetchSelectedTablesFailed": "获取选定表格失败", "descriptionSaved": "表 {{tableName}} 的描述已保存", "saveDescriptionFailed": "保存表描述失败", "fieldName": "字段名", "dataType": "数据类型", "nullable": "是否可为空", "savingAndLoading": "正在保存选定的表格并读取表结构...", "loadingTableInfo": "加载表信息...", "processWaitMessage": "这个过程可能需要1-3分钟，请耐心等待", "description": "为选定的数据表添加业务描述可以帮助系统更好地理解您的数据结构和业务含义。您可以选择跳过此步骤，之后随时可以在项目管理中添加表描述。", "hasDescription": "已添加描述", "noDescription": "未添加描述", "tableTitle": "表: {{tableName}}", "schemaOverview": "表结构概览", "tableDescriptionLabel": "表描述", "descriptionPlaceholder": "可选：请输入该表的业务描述，包括表的用途、主要字段的含义、与其他表的关系等", "saveDescription": "保存描述", "selectTableFromLeft": "请从左侧选择一个表", "enterSelfCheck": "进入自检"}}, "form": {"name": "项目名称", "nameRequired": "请输入项目名称", "namePlaceholder": "请输入项目名称", "description": "项目描述", "descriptionPlaceholder": "请输入项目描述", "visibility": "项目可见性", "visibilityRequired": "请选择项目可见性", "visibilityPlaceholder": "选择项目可见性"}, "visibility": {"public": "公开", "private": "私有", "publicProject": "公开项目", "privateProject": "私有项目"}, "roles": {"collaborator": "项目协作者", "observer": "项目观察者"}, "create": {"title": "创建项目", "basicInfo": "基本信息", "projectName": "项目名称", "projectNamePlaceholder": "请输入项目名称", "description": "项目描述", "descriptionPlaceholder": "请输入项目描述", "visibility": "可见性", "public": "公开", "private": "私有", "dataSource": "数据源配置", "selectDataSource": "选择数据源", "databaseType": "数据库类型", "connectionInfo": "连接信息", "testConnection": "测试连接", "connectionSuccess": "连接成功", "connectionFailed": "连接失败", "tableSelection": "表选择", "selectTables": "选择数据表", "selectedTables": "已选择 {{count}} 张表"}, "detail": {"title": "项目详情", "overview": "概览", "dataSource": "数据源", "tables": "数据表", "members": "项目成员", "analysis": "分析记录", "settings": "项目设置", "statistics": {"totalTables": "数据表总数", "totalColumns": "字段总数", "totalAnalysis": "分析次数", "lastAnalysis": "最近分析"}}, "intelligence": {"title": "智能项目理解", "businessUnderstanding": "业务理解", "questions": "智能问答", "scenarios": "场景预测", "summary": "总结", "steps": {"readinessAssessment": "就绪评估", "readinessAssessmentDesc": "评估项目数据准备情况", "scenarioValidation": "场景验证", "scenarioValidationDesc": "确认分析场景预测"}, "analyzing": {"evaluating": "正在评估项目就绪情况...", "analyzingData": "分析数据结构和业务场景", "pleaseWait": "请稍候，这可能需要1-2分钟"}, "businessDomain": "业务领域", "businessType": "业务类型", "keyEntities": "关键实体", "coreMetrics": "核心指标", "dataRelationships": "数据关联", "noIdentified": "暂无识别", "needsFurtherAnalysis": "需要进一步分析", "exampleQuestions": "可以回答的问题示例", "analysisBoundaries": "分析边界说明", "additionalInfoNeeded": "需要您补充的信息", "tip": "提示", "nextStepDescription": "接下来系统会针对这些问题向您收集补充信息，以便更好地理解您的业务需求。", "priorityRequired": "必答", "priorityImportant": "重要", "priorityOptional": "可选", "priorityNormal": "普通", "generatingQuestions": "智能问题生成中...", "generatingPersonalizedQuestions": "正在为您生成个性化问题...", "projectInfoComplete": "项目信息已完善", "excellentProjectInfo": "🎉 太棒了！根据分析，您的项目信息已经足够完善，无需额外补充信息。", "canProceedToScenarioValidation": "您可以直接进入场景验证阶段，系统将为您生成个性化的数据分析场景。", "systemAnalysisComplete": "系统已经分析了您的数据结构、表关系和业务信息，认为当前信息足以生成高质量的分析场景。如果后续发现需要补充信息，您随时可以在项目设置中进行调整。", "redQuestionDescription": "红色标记的问题为必答项，会影响后续的数据分析效果", "orangeQuestionDescription": "橙色标记的问题为重要项，建议填写以获得更好的服务", "blueQuestionDescription": "蓝色标记的问题为可选项，可以帮助个性化优化", "processingStages": {"analyzing": "智能分析中", "analyzingDesc": "正在分析您的反馈信息，识别有价值的业务补充内容...", "converting": "内容转换中", "convertingDesc": "正在将您的反馈转换为专业的业务描述，优化数据问答效果...", "saving": "保存优化中", "savingDesc": "正在将优化后的业务理解保存到数据源描述中...", "completed": "完成处理", "completedDesc": "智能理解优化完成，您现在可以开始使用数据问答功能了！"}, "confirmationCorrect": "✅ 理解正确", "confirmationNeedsRevision": "🔄 需要修正", "confirmationRejected": "❌ 理解错误", "confirmationPending": "待确认", "generatingScenarios": "场景预测生成中...", "generatingScenariosDesc": "正在基于您的回答预测常见使用场景...", "scenarioTitle": "基于我的理解，您可能会经常询问以下问题", "pleaseConfirmUnderstanding": "请确认我的理解是否正确", "scenarioNumber": "场景 {{number}}", "confidence": "置信度", "predictedQuestion": "📝 预测问题", "myUnderstandingAnalysis": "🎯 我的理解分析", "usingDefaultAnalysis": "⚠️ 使用默认分析", "plannedSteps": "已规划 {{count}} 个执行步骤", "agentAnalysis": "Agent分析", "agentAnalysisFailed": "⚠️ Agent分析失败：{{error}}", "usingDefaultUnderstanding": "已使用默认理解要点", "pleaseConfirmMyUnderstanding": "请确认我的理解", "understandingCorrect": "✅ 理解正确", "understandingNeedsAdjustment": "🔄 基本正确，但需要一些调整", "understandingCompletelyWrong": "❌ 理解完全错误", "feedbackPlaceholder": "请说明需要如何调整或为什么理解有误...", "processing": "处理中...", "intelligenceComplete": "智能理解完成！", "intelligenceCompleteDesc": "我已经充分理解了您的业务需求，现在可以为您提供更准确的数据分析服务", "startUsingProject": "开始使用项目", "understandingSummary": "理解总结", "generalBusiness": "通用业务", "dataAnalysis": "数据分析", "answeredQuestions": "回答问题", "confirmedScenarios": "确认场景", "coreBusinessProcesses": "🎯 核心业务流程", "keyBusinessEntities": "🔑 关键业务实体", "mainBusinessMetrics": "📊 主要业务指标", "nextStepsTitle": "🚀 接下来您可以：", "nextStep1": "直接开始与AI助手对话，询问数据相关问题", "nextStep2": "我会基于刚才的理解，为您提供更准确的分析", "nextStep3": "如果我的理解有偏差，您可以随时在对话中纠正我", "nextStep4": "我会在使用过程中不断学习和优化理解", "enterScenarioValidation": "进入场景验证", "explanation": "说明", "scenarioExplanation": "每个场景包含一个具体的业务问题，以及我对该问题的理解分析", "agentAnalysisExplanation": "Agent分析显示了我认为回答这个问题需要用到的数据表和字段", "agentAnalysisDetail": "这有助于您评估我的理解是否准确，以及数据是否充分", "understandingPoints": "请对每个场景确认我的理解是否正确", "confirmationHelp": "您的确认将帮助我提供更准确的数据分析服务", "intelligentOptimization": "基于您的确认，系统会智能优化后续的分析效果", "analysisComplete": "🎉 分析完成！您可以："}}