{"title": "数据分析", "chat": {"title": "智能问答", "placeholder": "你想了解什么？", "send": "开始分析", "thinking": "思考中...", "analyzing": "分析中...", "generating": "生成中...", "noConversation": "暂无对话记录", "newConversation": "新建对话", "conversationList": "对话列表", "rename": "重命名", "deleteConversation": "删除对话"}, "steps": {"start": "分析开始", "analysisCreated": "创建分析记录", "toolsLoaded": "已加载 {{count}} 个分析工具", "sqlExecutorInitialized": "初始化SQL执行器", "schemaLoaded": "数据库结构已加载", "schemaLoadFailed": "数据库结构加载失败", "intentAnalysisStarted": "正在分析用户意图", "intentAnalyzed": "分析用户意图", "executeTool": "执行工具: {{toolName}}", "waitingForPlanning": "正在分析结果并规划下一步...", "generatingReport": "正在生成分析报告", "reportGenerated": "生成分析报告", "insightDiscovered": "{{toolName}} - 发现洞察", "completed": "分析完成", "waitingForInput": "等待用户提供更多信息...", "waitingForClarification": "等待用户提供更多信息", "clarificationDescription": "请在弹出的对话框中补充必要信息", "analysisProgress": "分析进度", "llmAnalysisRunning": "LLM分析正在进行中...", "executing": "正在执行...", "waitingForUserInput": "等待用户输入...", "analysisError": "分析出错", "continueAnalysis": "已基于用户补充信息继续分析", "contextLoaded": "已加载历史对话上下文", "aiAnalysisComplete": "AI分析完成", "roundAnalysisStart": "第 {{round}} 轮分析开始", "contextLoadedWithSummary": "已加载对话上下文：{{summary}}", "planningRound": "**第{{round}}轮规划:** {{reasoning}}", "intelligentEvaluation": "**智能评估:** {{reasoning}}", "intelligentPlanning": "智能规划中...", "loadingSteps": "分析步骤加载中...", "clarificationRequest": "执行工具: 智能交互", "reportGenerationStarted": "正在生成分析报告", "analysisCompleted": "分析完成"}, "results": {"title": "分析结果", "analysisReport": "分析报告", "summary": "结果摘要", "details": "详细信息", "charts": "图表展示", "data": "数据表格", "export": "导出结果", "share": "分享结果", "save": "保存结果"}, "insights": {"title": "分析洞察", "discovered": "发现洞察", "keyFindings": "关键发现", "hypotheses": "分析假设", "patterns": "分析要点", "anomalies": "异常发现", "recommendations": "建议步骤", "dataQuality": "数据质量", "dataScale": "数据规模", "rows": "行", "confidence": "置信度", "completeness": "完整度", "correlations": "相关性分析", "types": {"summary": "概览", "pattern": "模式", "trend": "趋势", "correlation": "相关性", "anomaly": "异常", "distribution": "分布", "outlier": "离群值", "seasonal": "季节性", "quality": "质量", "business": "业务", "computation": "计算分析", "phenomenon": "现象", "causal": "因果", "causal_analysis": "因果分析"}, "correlationStrength": {"strong": "强", "moderate": "中等", "weak": "弱", "veryStrong": "很强", "veryWeak": "很弱"}, "summary": {"foundInsights": "发现 {{count}} 个关键洞察", "dataPatterns": "{{count}} 个分析要点", "anomalies": "{{count}} 个异常", "separator": "，", "defaultDescription": "发现数据洞察"}}, "history": {"title": "分析历史", "question": "问题", "result": "结果", "time": "分析时间", "duration": "耗时", "status": "状态", "successful": "成功", "failed": "失败", "processing": "处理中"}, "tools": {"sqlQuery": "SQL查询", "chartGeneration": "图表生成", "dataExport": "数据导出", "reportGeneration": "报告生成", "apiCall": "API调用", "executeTool": "执行工具", "autoSqlQuery": "自动SQL查询", "executionResult": "执行结果", "toolExecutionResult": "工具执行结果: {{toolName}}", "resourceResult": "获取资源结果: {{resourcePath}}", "executionTime": "耗时: {{time}} ms", "unknownResource": "未知资源", "unknownTool": "未知工具", "intelligentEvaluation": "智能评估", "intentConfirmation": "意图确认", "intelligentChartGeneration": "智能图表生成", "intentAnalysis": "意图分析", "executionParameters": "执行参数:", "executedSQL": "执行的SQL:", "recordCount": "共 {{count}} 条记录", "totalRecords": "共 {{total}} 条记录", "chartAnalysis": "智能图表分析", "multiChartAnalysis": "智能多图表分析", "chartGenerationComplete": "图表生成完成", "chartsGenerated": "共生成 {{count}} 个图表", "singleChartGenerated": "单个图表已生成", "viewChart": "查看图表", "viewAllCharts": "查看所有图表", "viewChartConfig": "查看图表 {{index}} 配置", "viewChartConfigDetails": "查看图表配置详情", "chartConfigInvalid": "图表配置无效", "chartConfigInvalidWithIndex": "图表 {{index}} 配置无效", "chartConfigInvalidOrEmpty": "图表配置无效或为空", "generationReason": "生成原因：", "generationTime": "生成时间", "dataSourceInfo": "数据源信息", "rawData": "原始数据：", "dataSource": "数据来源", "chartType": "图表类型", "chartGenerated": "图表已生成", "multiChartsGenerated": "已生成 {{count}} 个图表"}, "multiRound": {"title": "多轮智能分析", "subtitle": "亲爱的{{username}}，请随意提问...", "welcome": "欢迎", "welcomeTitle": "开始您的数据分析之旅", "welcomeSubtitle": "输入您的分析问题，支持多轮对话", "placeholder": "请输入您的分析问题，支持多轮连续对话...", "startAnalysis": "开始分析", "round": "第 {{round}} 轮提问", "continueQuestion": "继续提问", "continueAnalysis": "继续分析", "newConversation": "新建对话", "newConversationCreated": "已创建新会话，请开始您的第一个问题", "analysisComplete": "分析完成", "analysisRunning": "分析中...", "analysisFailed": "分析失败", "continueDialog": {"title": "继续对话", "subtitle": "基于前面的分析结果继续提问", "roundIndicator": "第 {{round}} 轮对话", "smartContextAware": "🧠 智能上下文感知", "placeholder": "基于前面的分析结果，您还想了解什么？AI会智能关联历史上下文...", "validationMessage": "请输入您的问题", "templateButton": "模板", "templateTooltip": "使用分析模板", "aiContextTip": "AI会智能关联历史上下文"}, "conversationLoadSuccess": "会话加载成功", "smartContextAware": "智能上下文感知", "contextBasedQuestion": "基于前面 {{count}} 轮分析结果，您还想了解什么？AI会智能关联历史上下文...", "fallbackContextQuestion": "基于前面的分析结果，您还想了解什么？", "tip": "提示：输入新问题继续多轮对话", "waitingForFeedback": "等待反馈", "interruptAnalysis": "打断分析", "cancelAnalysis": "取消分析", "interruptAnalysisAndFeedback": "打断分析并提供反馈", "selectTemplate": "选择分析模板", "submitFeedback": "提交反馈", "cancel": "取消", "feedbackLabel": "请描述您希望如何调整分析", "feedbackTip": "提示", "feedbackDescription": "系统将基于您的反馈调整分析方向后继续执行，不会丢失已有的分析进度。"}, "intentConfirmation": {"title": "意图确认", "description": "请确认我对您问题的理解，以便提供更准确的分析", "yourQuestion": "您的问题", "analysisIntent": "分析意图", "executionSteps": "执行步骤规划", "additionalNotes": "补充说明", "additionalNotesPlaceholder": "如果您对以上分析有补充或修正，请在此输入...", "answerPlaceholder": "请输入您的答案", "answerOrSelect": "请输入您的答案或选择上面的选项", "confirmAndContinue": "确认并继续分析", "cancel": "取消", "completed": "意图确认完成", "completedAdjusted": "意图确认完成 (已调整)", "systemContinueMessage": "系统将基于此信息继续分析", "waitingForConfirmation": "等待用户确认意图", "clarificationQA": "澄清问答", "supplementNote": "补充说明", "confirmExecutePlan": "确认按照计划执行分析", "clarificationReply": "澄清回复：", "additionalRequirements": "补充需求：", "answer": "答案："}, "status": {"pending": "等待反馈", "adjusted": "已调整", "timeout": "超时"}, "messages": {"executionFailed": "分析执行失败"}, "errors": {"noProject": "请先选择项目", "noProjectDescription": "请在顶部导航栏选择一个项目，然后再进行数据分析", "queryRequired": "请输入分析问题", "analysisError": "分析出错", "multiRoundAnalysisError": "多轮分析出错: {{error}}", "startAnalysisFailed": "启动分析失败: {{error}}", "conversationLoadFailed": "会话加载失败", "analysisExecutionFailed": "分析执行失败: {{error}}", "analysisDataFormatError": "分析数据格式错误，期望数组格式", "unknownError": "未知错误", "analysisFailed": "分析失败: {{error}}", "error": "出错", "stepExecutionFailed": "步骤执行失败", "stepExecutionError": "执行步骤出错", "analysisStopped": "分析停止: {{error}}", "errorDetails": "错误详情：", "noIntentAnalysisData": "暂无意图分析数据", "userIntentAnalysisResult": "用户意图分析结果", "chartDataFormatIncorrect": "图表数据格式不正确", "parseChartDataFailed": "解析图表数据失败: {{error}}", "chartConfigDataInvalid": "图表配置数据无效", "noDownloadableChartData": "没有可下载的图表数据", "operationFailed": "操作失败，请重试", "sqlExecutionFailed": "SQL执行失败", "queryExecutionFailed": "查询执行失败", "errorType": "错误类型: {{type}}", "queryResultEmpty": "查询结果为空", "renderSQLResultError": "渲染SQL结果时发生错误", "expandMore": "更多", "jsonParseFailed": "JSON解析失败，尝试其他方式", "feedbackPlaceholder": "例如：改用其他分析方法、调整查询条件、关注不同的数据维度等...", "interruptAnalysisFailedRetry": "打断分析失败，请重试", "noAnalysisInProgress": "当前没有正在进行的分析", "waitingForFeedbackWithTime": "等待用户反馈中... 剩余时间: {{minutes}}分钟", "getProjectListFailed": "获取项目列表失败", "analysisCancelled": "分析已取消", "missingInterruptInfo": "缺少必要的打断信息", "pleaseFillFeedback": "请填写反馈内容", "feedbackSubmitted": "反馈已提交，分析将继续", "submitFeedbackFailed": "提交反馈失败", "submitFeedbackFailedRetry": "提交反馈失败，请重试", "noAnalysisRunning": "没有正在进行的分析", "analysisInterrupted": "分析已打断，请提供反馈", "chartDataFormatAbnormal": "图表数据格式异常", "processChartDataFailed": "处理图表数据失败", "feedbackApplied": "用户反馈已应用，分析继续进行", "feedbackTimeout": "用户反馈超时，分析继续进行", "continueAnalysisFailed": "继续分析失败，请重试", "submitIntentConfirmationFailed": "提交意图确认失败: {{error}}", "switchConversationFailed": "切换会话失败", "pleaseSelectProject": "请先选择项目", "newConversationCreated": "已创建新会话，请开始您的第一个问题", "downloadChartFailed": "下载图表失败，请重试", "chartDownloadSuccess": "图表下载成功", "chartsDownloadSuccess": "成功下载 {{count}} 个图表"}, "interrupt": {"title": "分析调整", "interruptedTitle": "分析被打断", "description": "您可以随时调整分析方向或提供额外信息", "adjustmentType": "调整类型", "feedback": "调整建议", "submit": "提交调整", "cancel": "取消", "strategy": "策略调整", "parameter": "参数调整", "direction": "方向调整", "feedbackApplied": "用户反馈已应用", "feedbackTimeout": "用户反馈超时"}, "charts": {"chartDisplay": "图表展示", "multipleCharts": "图表展示 - 共 {{count}} 个图表", "downloadChart": "下载图表"}, "templates": {"title": "分析模板", "hotTemplates": "热门模板推荐", "personalTemplates": "个人常用模板", "moreTemplates": "更多模板", "saveTemplate": "保存模板", "useTemplate": "使用模板", "templateApplied": "已应用模板：{{name}}", "quickStart": "快速开始", "usageCount": "使用次数", "personalTemplate": "个人模板", "systemTemplate": "系统模板", "noPersonalTemplates": "暂无个人模板", "noPersonalTemplatesDesc": "您还没有创建任何个人模板。使用系统模板开始分析，或创建您自己的模板。", "noHotTemplates": "暂无热门模板", "noHotTemplatesDesc": "当有模板被使用后，热门模板将会在这里显示", "createFirstTemplate": "创建第一个模板", "defaultTemplates": {"dataOverview": {"name": "数据概览分析", "description": "分析当前数据的基本情况，包括数据量、主要字段、数据质量等概览信息", "content": "请帮我分析当前数据的基本情况，包括数据量、主要字段、数据质量等概览信息。"}, "trendAnalysis": {"name": "趋势分析", "description": "分析数据的时间趋势，识别关键的变化模式和异常点", "content": "请帮我分析数据的时间趋势，识别关键的变化模式和异常点。"}, "comparisonAnalysis": {"name": "对比分析", "description": "进行数据对比分析，找出不同维度间的差异和关联性", "content": "请帮我进行数据对比分析，找出不同维度间的差异和关联性。"}, "deepInsights": {"name": "深度洞察", "description": "基于数据进行深度分析，提供业务洞察和改进建议", "content": "请基于数据进行深度分析，提供业务洞察和改进建议。"}}}, "templateManagement": {"title": "分析模板管理", "createTemplate": "创建模板", "editTemplate": "编辑模板", "previewTemplate": "预览模板", "deleteTemplate": "删除模板", "templateName": "模板名称", "templateDescription": "描述", "templateContent": "模板内容", "usageCount": "使用次数", "creator": "创建者", "actions": "操作", "isPublic": "是否公开", "publicTemplate": "公开模板（其他用户可以使用）", "systemTemplate": "系统", "publicTag": "公开", "preview": "预览", "edit": "编辑", "delete": "删除", "close": "关闭", "confirm": "确定", "cancel": "取消", "noDescription": "无描述", "placeholders": {"templateName": "请输入模板名称", "templateDescription": "请输入模板描述", "templateContent": "请输入分析提示词模板..."}, "messages": {"templateCreated": "模板创建成功", "templateUpdated": "模板更新成功", "templateDeleted": "模板删除成功", "createFailed": "模板创建失败", "updateFailed": "模板更新失败", "deleteFailed": "模板删除失败", "fetchFailed": "获取模板列表失败", "deleteConfirm": "确定要删除这个模板吗？"}, "validation": {"nameRequired": "请输入模板名称", "contentRequired": "请输入模板内容"}, "pagination": {"total": "共 {{total}} 个模板"}, "complexity": {"basic": "基础", "intermediate": "中级", "advanced": "高级", "general": "通用"}, "noProject": {"title": "请先选择项目", "description": "请在顶部选择一个项目后再管理模板"}, "search": {"placeholder": "搜索模板名称、描述..."}, "templatePanel": {"usageCount": "次使用", "preview": "预览", "collapse": "收起", "useTemplate": "使用模板", "templateContent": "模板内容", "parameterizedUse": "参数化使用", "parameterizedUseTitle": "使用参数化模板", "category": "分类", "complexity": "复杂度", "allCategories": "全部分类", "allComplexity": "全部复杂度", "loading": "加载中...", "empty": "暂无模板", "fetchFailed": "获取模板列表失败", "useFailed": "使用模板失败"}}, "taxInfo": {"notFound": "未找到纳税信息", "notFoundDescription": "未能从向量数据库中检索到相关的纳税信息。"}, "news": {"notFound": "未找到负面新闻数据", "analysisTitle": "负面新闻分析", "foundCount": "共找到 {{count}} 条相关新闻，按时间倒序排列", "keywords": {"negative": "负面", "risk": "风险", "warning": "警告", "violation": "违规", "penalty": "处罚", "lawsuit": "诉讼", "prosecution": "起诉", "defendant": "被告", "plaintiff": "原告", "bankruptcy": "破产", "debt": "欠款", "taxDebt": "欠税", "layoff": "裁员", "loss": "亏损", "downgrade": "降级", "decrease": "降低", "alert": "警示", "investigation": "调查", "neutral": "中性", "positive": "正面", "commendation": "表彰", "award": "获奖", "growth": "增长", "profit": "盈利", "success": "成功"}}, "insightDiscovered": "发现洞察: {{title}}", "multiChartsGenerated": "生成了 {{count}} 个图表", "chartGenerated": "图表生成成功"}