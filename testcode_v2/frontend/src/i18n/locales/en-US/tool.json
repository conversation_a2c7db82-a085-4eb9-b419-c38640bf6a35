{"title": "Tool Management", "edit": {"createTool": "Create Tool", "editTool": "<PERSON>l", "backToList": "Back to List", "saveChanges": "Save Changes", "selectProjectFirst": "Please select a project first", "selectProjectDescription": "Please select a project from the top navigation bar before creating tools", "fetchDataError": "Failed to fetch tool data", "fetchDetailError": "Failed to fetch tool details, please try again later", "updateSuccess": "Tool updated successfully", "createSuccess": "Tool created successfully", "updateError": "Failed to update tool, please try again later", "createError": "Failed to create tool, please try again later"}, "form": {"name": "Tool Name", "nameRequired": "Please enter tool name", "namePlaceholder": "Please enter tool name, e.g., Company Information Query", "description": "Tool Description", "descriptionPlaceholder": "Please enter detailed tool description...", "type": "Tool Type", "typeRequired": "Please select tool type", "template": "Tool Template", "templateRequired": "Please enter tool template", "templatePlaceholder": "Please enter SQL query or API call template...", "parameters": "Parameter Settings", "paramName": "Parameter Name", "paramNameRequired": "Please enter parameter name", "paramNamePlaceholder": "Parameter name", "paramType": "Parameter Type", "paramTypeRequired": "Please select parameter type", "paramDescription": "Parameter Description", "paramDescriptionPlaceholder": "Parameter description", "paramRequired": "Required", "paramDefault": "Default Value", "paramDefaultPlaceholder": "Default value", "addParameter": "Add Parameter"}, "type": {"sql": "SQL Query", "api": "API Call", "python": "Python Script", "graph": "Graph Data Query", "vector": "Vector Search", "custom": "Custom Tool", "autoSql": "Auto SQL", "chart": "Chart Tool"}, "paramType": {"string": "String", "number": "Number", "boolean": "Boolean", "array": "Array", "object": "Object"}, "detail": {"title": "Tool Details", "loading": "Loading...", "error": "Error", "fetchError": "Unable to get tool information", "fetchDetailError": "Failed to get tool details", "fetchDetailErrorRetry": "Failed to get tool details, please try again later", "deleteSuccess": "Tool deleted successfully", "deleteError": "Failed to delete tool", "deleteErrorRetry": "Failed to delete tool, please try again later", "breadcrumb": {"home": "Home", "toolManagement": "Tool Management", "toolDetail": "Tool Details"}, "noDescription": "No description", "backToList": "Back to List", "edit": "Edit", "delete": "Delete", "confirmDelete": "Are you sure you want to delete this tool?", "confirmDeleteDescription": "This operation is irreversible, data will not be recoverable after deletion.", "confirm": "Confirm", "cancel": "Cancel", "basicInfo": "Basic Information", "toolId": "Tool ID", "toolName": "Tool Name", "toolDescription": "Tool Description", "none": "None", "toolType": "Tool Type", "dataSource": "Data Source", "createdTime": "Created Time", "updatedTime": "Updated Time", "template": "Template", "parameters": "Parameter Definition", "parameter": "Parameter", "unnamed": "Unnamed", "type": "Type:", "unspecified": "Unspecified", "description": "Description:", "required": "Required:", "yes": "Yes", "no": "No", "defaultValue": "Default Value:", "noParameters": "No parameter definition", "testTool": "Test Tool"}, "test": {"title": "Test Mode", "description": "You can test the tool execution by filling in parameters here"}}