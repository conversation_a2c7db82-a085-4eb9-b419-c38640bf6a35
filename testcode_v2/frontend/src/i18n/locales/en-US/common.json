{"buttons": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "create": "Create", "add": "Add", "search": "Search", "refresh": "Refresh", "reset": "Reset", "submit": "Submit", "confirm": "Confirm", "back": "Back", "next": "Next", "previous": "Previous", "close": "Close", "view": "View", "download": "Download", "upload": "Upload", "copy": "Copy", "export": "Export", "import": "Import", "test": "Test", "execute": "Execute", "start": "Start", "stop": "Stop", "pause": "Pause", "resume": "Resume", "retry": "Retry", "continueQuestion": "Continue Question", "startNewConversation": "Start New Conversation", "enable": "Enable", "disable": "Disable", "ok": "OK", "yes": "Yes", "no": "No", "remove": "Remove", "downloadFile": "Download File", "openInNewWindow": "Open in New Window", "preview": "Preview", "skip": "<PERSON><PERSON>"}, "navigation": {"dashboard": "Dashboard", "projects": "Projects", "tools": "Tools", "analysis": "Analysis", "templates": "Templates", "models": "Models", "users": "Users", "admin": "Admin", "settings": "Settings", "profile": "Profile", "logout": "Logout"}, "sidebar": {"chatHistory": "Chat History", "noConversations": "No conversations", "deleteConversationConfirm": "Are you sure you want to delete this conversation?", "deleteSuccess": "Conversation deleted successfully", "deleteError": "Failed to delete conversation", "loadMore": "More...", "projects": "Projects", "tools": "Tools", "templates": "Templates", "models": "Models", "users": "Users", "logout": "Logout"}, "messages": {"success": "Operation successful", "error": "Operation failed", "loading": "Loading...", "noData": "No data available", "confirm": "Confirm operation", "deleteConfirm": "Are you sure you want to delete?", "saveSuccess": "Saved successfully", "deleteSuccess": "Deleted successfully", "updateSuccess": "Updated successfully", "createSuccess": "Created successfully", "networkError": "Network error, please retry", "permissionDenied": "Permission denied", "sessionExpired": "Session expired, please login again", "selectProjectFirst": "Please select a project first", "noProjectsCreateFirst": "No projects available, please create a project first", "deleteToolConfirm": "Are you sure you want to delete tool \"{{name}}\"? This action cannot be undone.", "deleteError": "Failed to delete, please try again later", "fetchFailed": "Failed to fetch data", "operationFailed": "Operation failed, please retry", "interfaceResponseError": "Interface response format error", "getProjectFailed": "Failed to get project", "getDataFailed": "Failed to get data", "unknownError": "Unknown error", "cannotGetToolInfo": "Unable to get tool information", "testMode": "Test Mode", "testModeDescription": "You can test the tool execution by filling in parameters here", "noParametersDefined": "No parameters defined", "parameterNotDefined": "This tool has no parameters defined", "testExecuting": "Test executing...", "testResult": "Test Result", "downloadResult": "Download Result", "parameterSettings": "Parameter Settings", "executeTest": "Execute Test", "testing": "Testing...", "startTest": "Start Test", "pleaseProvideModelConfig": "Please provide model configuration", "modelTest": "Model Test", "testModelConfig": "Click test button to verify model configuration (sends fixed 'hello' message)", "toolTestFailed": "Tool test failed, please try again later", "toolTestResult": "tool-test-result", "totalRecords": "Total {{count}} records", "queryResultEmpty": "Query result is empty", "errorRenderingSQLResult": "Error occurred while rendering SQL result", "resultEmpty": "Result is empty", "companyDetails": "Company Details", "relatedFiles": "Related Files ({{count}})", "unknownFileName": "Unknown file", "imageLoadFailed": "Image failed to load", "fileTypeNotSupported": "This file type does not support online preview", "fileType": "File type: {{type}}", "unknown": "Unknown", "doc": "DOC", "accessible": "Accessible", "noAvailableLink": "No available link", "filePreview": "File Preview: {{fileName}}", "formattedView": "Formatted View", "jsonText": "JSON Text"}, "status": {"active": "Active", "inactive": "Inactive", "pending": "Pending", "completed": "Completed", "failed": "Failed", "processing": "Processing", "draft": "Draft", "published": "Published", "enabled": "Enabled", "disabled": "Disabled"}, "labels": {"name": "Name", "description": "Description", "type": "Type", "actions": "Actions", "createdTime": "Created Time", "updatedTime": "Updated Time", "none": "None", "unknown": "Unknown Type", "basicInfo": "Basic Information", "toolName": "Tool Name", "toolDescription": "Tool Description", "toolType": "Tool Type", "dataSource": "Data Source", "dataSourceType": "Data Source Type", "testTool": "Test Tool", "modelName": "Model Name", "enabledStatus": "Enabled Status", "connectionConfig": "Connection Configuration", "projectOwner": "Project Owner", "projectVisibility": "Project Visibility", "editProject": "Edit Project", "dataSourceName": "Data Source Name", "dataSourceDescription": "Data Source Description", "selectDataTable": "Select Data Table", "editReportPrompt": "Edit Report Generation Prompt", "reportPrompt": "Report Generation Prompt", "hostAddress": "Host Address", "port": "Port", "serviceName": "Service Name", "username": "Username", "password": "Password", "databaseName": "Database Name", "requestHeaders": "Request Headers", "projectMemberManagement": "Project Member Management", "configureDataSource": "Configure Data Source", "addProjectMember": "Add Project Member", "selectUser": "Select User", "projectRole": "Project Role", "selectAnalysisTable": "Select Analysis Table", "getTableListFailed": "Failed to get table list", "refreshTableList": "Refresh Table List", "toolCount": "Tool Count", "userIdPrefix": "User ID: ", "inputPassword": "Enter password", "inputDataSourceName": "Enter data source name", "selectDataSourceType": "Select data source type", "pleaseSelectUser": "Please select user", "pleaseSelectRole": "Please select role", "inputProjectName": "Enter project name", "selectProjectVisibility": "Select project visibility", "testModel": "Test Model", "deleteTooltip": "Delete", "email": "Email", "role": "Role"}, "form": {"required": "This field is required", "invalidEmail": "Please enter a valid email address", "invalidPhone": "Please enter a valid phone number", "minLength": "Minimum {{count}} characters required", "maxLength": "Maximum {{count}} characters allowed", "passwordMismatch": "Passwords do not match", "selectOption": "Please select an option", "enterValue": "Please enter a value", "yes": "Yes", "no": "No", "pleaseInput": "Please enter {{field}}", "pleaseInputNumber": "Please enter {{field}}", "pleaseInputDescription": "Please describe your thoughts in detail...", "criticalQuestionRequired": "This question is required"}, "time": {"createdAt": "Created At", "updatedAt": "Updated At", "startTime": "Start Time", "endTime": "End Time", "duration": "Duration", "today": "Today", "yesterday": "Yesterday", "thisWeek": "This Week", "thisMonth": "This Month", "lastMonth": "Last Month"}, "units": {"count": "{{count}} items", "items": "{{count}} records", "pages": "{{count}} pages", "bytes": "Bytes", "kb": "KB", "mb": "MB", "gb": "GB", "seconds": "Seconds", "minutes": "Minutes", "hours": "Hours", "days": "Days"}, "pagination": {"total": "Total {{total}} items"}, "table": {"actions": "Actions"}, "button": {"edit": "Edit", "delete": "Delete", "confirm": "Confirm", "cancel": "Cancel"}, "confirm": {"delete": "Confirm Delete"}, "message": {"error": {"fetchFailed": "Failed to fetch data", "operationFailed": "Operation failed, please retry", "networkError": "Network connection failed, please check network settings", "loginFailed": "<PERSON><PERSON> failed, please check username and password", "permissionDenied": "Insufficient permissions", "dataNotFound": "Data not found", "validationFailed": "Data validation failed"}, "success": {"loginSuccess": "Login successful!", "saveSuccess": "Save successful", "deleteSuccess": "Delete successful", "updateSuccess": "Update successful", "createSuccess": "Create successful", "operationSuccess": "Operation successful"}, "warning": {"unsavedChanges": "There are unsaved changes", "confirmAction": "Please confirm this action"}}, "timeRelative": {"justNow": "Just now", "minutesAgo": "{{count}} minutes ago", "hoursAgo": "{{count}} hours ago", "daysAgo": "{{count}} days ago", "yesterday": "Yesterday", "today": "Today", "thisWeek": "This week", "thisMonth": "This month", "lastMonth": "Last month"}, "conversation": {"title": "Conversation List", "new": "New", "search": "Search conversations...", "rename": "<PERSON><PERSON>", "delete": "Delete", "confirmDelete": "Confirm Delete Conversation", "confirmDeleteMessage": "This action cannot be undone. Are you sure you want to delete this conversation?", "renameTitle": "Rename Conversation", "renameSuccess": "Conversation renamed successfully", "renameFailed": "<PERSON><PERSON> failed", "deleteSuccess": "Conversation deleted successfully", "deleteFailed": "Failed to delete conversation", "rounds": "Rounds", "current": "Current", "noMatchingConversations": "No matching conversations found", "noConversationRecords": "No conversation records", "createFirstConversation": "Create first conversation", "conversationTitle": "Conversation Title", "enterTitle": "Please enter conversation title", "enterNewTitle": "Please enter new conversation title", "untitled": "Untitled conversation", "enterConversation": "Enter conversation", "time": "Time", "lastQuestion": "Last question"}, "admin": {"login": {"title": "Administrator <PERSON><PERSON>", "subtitle": "System Management Backend", "username": "Administrator <PERSON><PERSON><PERSON>", "password": "Password", "loginButton": "<PERSON><PERSON>", "loggingIn": "Logging in...", "footer": "* For system administrators only", "validation": {"usernameRequired": "Please enter administrator username", "usernameMinLength": "Username must be at least 3 characters", "passwordRequired": "Please enter password", "passwordMinLength": "Password must be at least 6 characters"}, "errors": {"loginFailed": "<PERSON><PERSON> failed, please check username and password", "invalidResponse": "Invalid login response format", "networkError": "Network connection failed, please check network settings", "serverError": "Server error, please try again later", "unknownError": "Unknown error, please contact administrator"}}}, "dataSource": {"title": "Data Source Management", "add": "Add Data Source", "edit": "Edit Data Source", "delete": "Delete Data Source", "testConnection": "Test Connection", "name": "Data Source Name", "description": "Data Source Description", "type": "Data Source Type", "config": "Configuration", "createdAt": "Created At", "actions": "Actions", "loading": "Loading...", "noData": "No data", "pleaseSelectProject": "Please select a project first", "fetchFailed": "Failed to fetch data source list", "createSuccess": "Data source created successfully", "updateSuccess": "Data source updated successfully", "deleteSuccess": "Data source deleted successfully", "deleteFailed": "Failed to delete data source", "connectionTestSuccess": "Connection test successful", "connectionTestFailed": "Connection test failed", "confirmDelete": "Confirm Delete", "deleteMessage": "Are you sure you want to delete this data source? This action cannot be undone.", "configFormatError": "Invalid configuration format, please enter valid JSON", "configHelp": "Please enter configuration information in JSON format", "types": {"oracle": "Oracle Database", "mysql": "MySQL Database", "postgresql": "PostgreSQL Database", "mssql": "SQL Server Database", "httpApi": "HTTP API"}, "placeholders": {"enterName": "Enter data source name", "enterDescription": "Enter data source description", "selectType": "Select data source type", "enterConfig": "Enter configuration information in JSON format"}, "validation": {"nameRequired": "Please enter data source name", "typeRequired": "Please select data source type", "configRequired": "Please enter configuration information"}}, "dashboard": {"title": "Dashboard", "selectProjectFirst": "Please select a project first"}, "intelligence": {"supplementInfo": "Please provide the following information to help me better understand your business", "criticalCount": "{{count}} required", "importantCount": "{{count}} important", "optionalCount": "{{count}} optional", "enterScenarioValidation": "Enter Scenario Validation", "intelligentProcessing": "Intelligent processing...", "completeIntelligentUnderstanding": "Complete Intelligent Understanding", "remainingScenarios": "{{count}} scenarios remaining to complete", "completeAllScenarios": "Please complete confirmation for all scenarios", "explanation": "Note:", "scenarioExplanation": "These are common analysis scenarios I predicted based on my understanding of your business", "agentAnalysisTag": "Based on Real Agent Analysis", "agentAnalysisExplanation": "Items marked with \"✨ Based on Real Agent Analysis\" are understanding analyses obtained by calling real Agent capabilities", "agentAnalysisDetail": "Agent analysis includes complete intent recognition and execution planning processes, closer to actual usage scenarios", "understandingPoints": "Understanding points directly show core analysis results, concise and clear", "confirmationHelp": "Your confirmation will help me understand your needs more accurately", "intelligentOptimization": "Intelligent Optimization: Your effective feedback will be intelligently converted into professional business descriptions to improve subsequent Q&A quality"}, "login": {"companyInfo": "© 2025 Professional Data Analysis Solutions"}, "projectMember": {"title": "Project Member Management", "inviteMember": "Invite Member", "editMemberRole": "Edit Member Role", "selectUser": "Select User", "projectRole": "Project Role", "username": "Username", "email": "Email", "role": "Role", "invitedBy": "Invited By", "joinedAt": "Joined At", "status": "Status", "actions": "Actions", "active": "Active", "inactive": "Inactive", "noPermission": "No Permission", "editRole": "Edit Role", "removeMember": "Remove Member", "confirmRemove": "Are you sure you want to remove this member?", "invite": "Invite", "update": "Update", "totalRecords": "Total {{total}} records", "selectUserToInvite": "Please select user to invite", "selectProjectRole": "Please select project role", "pleaseSelectUser": "Please select user", "pleaseSelectRole": "Please select role", "inviteSuccess": "Member invited successfully", "inviteFailed": "Failed to invite member", "updateSuccess": "Member role updated successfully", "updateFailed": "Failed to update member role", "removeSuccess": "Member removed successfully", "removeFailed": "Failed to remove member", "fetchMembersFailed": "Failed to fetch project members", "fetchInvitableUsersFailed": "Failed to fetch invitable users", "roles": {"projectOwner": "Project Owner", "enterpriseAdmin": "Enterprise Admin", "collaborator": "Collaborator", "viewer": "Viewer", "collaboratorDesc": "Can operate + manage tools", "viewerDesc": "Can view + execute analysis"}}, "tableDescription": {"title": "Edit Table Description", "loading": "Loading table information...", "noTables": "This project has no data tables", "pleaseSelectTable": "Please select a table from the left", "tablePrefix": "Table: ", "schemaOverview": "Schema Overview", "tableDescription": "Table Description", "saveDescription": "Save Description", "descriptionAdded": "Description added", "noDescription": "No description added", "fieldName": "Field Name", "dataType": "Data Type", "nullable": "Nullable", "yes": "Yes", "no": "No", "descriptionPlaceholder": "Please enter the business description of this table, including the purpose of the table, the meaning of main fields, relationships with other tables, etc.", "fetchTablesFailed": "Failed to fetch selected tables", "descriptionSaved": "Description for table {{tableName}} has been saved", "saveDescriptionFailed": "Failed to save table description"}}