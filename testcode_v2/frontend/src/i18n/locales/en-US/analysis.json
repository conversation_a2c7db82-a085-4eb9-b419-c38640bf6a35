{"title": "Data Analysis", "chat": {"title": "Intelligent Q&A", "placeholder": "What would you like to know?", "send": "Start Analysis", "thinking": "Thinking...", "analyzing": "Analyzing...", "generating": "Generating...", "noConversation": "No conversation records", "newConversation": "New Conversation", "conversationList": "Conversation List", "rename": "<PERSON><PERSON>", "deleteConversation": "Delete Conversation"}, "steps": {"start": "Analysis Started", "analysisCreated": "Analysis Record Created", "toolsLoaded": "Loaded {{count}} analysis tools", "sqlExecutorInitialized": "SQL Executor Initialized", "schemaLoaded": "Database Schema Loaded", "schemaLoadFailed": "Failed to Load Database Schema", "intentAnalysisStarted": "Analyzing User Intent", "intentAnalyzed": "User Intent Analyzed", "executeTool": "Execute Tool: {{toolName}}", "waitingForPlanning": "Analyzing results and planning next steps...", "generatingReport": "Generating Analysis Report", "reportGenerated": "Analysis Report Generated", "insightDiscovered": "{{toolName}} - Insights Discovered", "completed": "Analysis Completed", "waitingForInput": "Waiting for user input...", "waitingForClarification": "Waiting for user clarification", "clarificationDescription": "Please provide necessary information in the popup dialog", "analysisProgress": "Analysis Progress", "llmAnalysisRunning": "LLM Analysis in Progress...", "executing": "Executing...", "waitingForUserInput": "Waiting for user input...", "analysisError": "Analysis Error", "continueAnalysis": "Continued analysis based on user input", "contextLoaded": "Historical conversation context loaded", "aiAnalysisComplete": "AI Analysis Complete", "roundAnalysisStart": "Round {{round}} analysis started", "contextLoadedWithSummary": "Conversation context loaded: {{summary}}", "planningRound": "**Round {{round}} Planning:** {{reasoning}}", "intelligentEvaluation": "**Intelligent Evaluation:** {{reasoning}}", "intelligentPlanning": "Intelligent planning...", "loadingSteps": "Loading analysis steps...", "clarificationRequest": "Execute Tool: Intelligent Interaction", "reportGenerationStarted": "Generating Analysis Report", "analysisCompleted": "Analysis Completed"}, "results": {"title": "Analysis Results", "analysisReport": "Analysis Report", "summary": "Summary", "details": "Details", "charts": "Charts", "data": "Data Table", "export": "Export Results", "share": "Share Results", "save": "Save Results"}, "insights": {"title": "Analysis Insights", "discovered": "Insights Discovered", "keyFindings": "Key Findings", "hypotheses": "Analysis Hypotheses", "patterns": "Analysis Highlights", "anomalies": "Anomaly Detection", "recommendations": "Recommended Steps", "dataQuality": "Data Quality", "dataScale": "Data Scale", "rows": " rows", "confidence": "Confidence", "completeness": "Completeness", "correlations": "Correlation Analysis", "types": {"summary": "Summary", "pattern": "Pattern", "trend": "Trend", "correlation": "Correlation", "anomaly": "Anomaly", "distribution": "Distribution", "outlier": "Outlier", "seasonal": "Seasonal", "quality": "Quality", "business": "Business", "computation": "Computation", "phenomenon": "<PERSON><PERSON><PERSON><PERSON>", "causal": "Causal", "causal_analysis": "Causal Analysis"}, "correlationStrength": {"strong": "Strong", "moderate": "Moderate", "weak": "Weak", "veryStrong": "Very Strong", "veryWeak": "Very Weak"}, "summary": {"foundInsights": "Found {{count}} key insights", "dataPatterns": "{{count}} analysis highlights", "anomalies": "{{count}} anomalies", "separator": ", ", "defaultDescription": "Data insights discovered"}}, "history": {"title": "Analysis History", "question": "Question", "result": "Result", "time": "Analysis Time", "duration": "Duration", "status": "Status", "successful": "Successful", "failed": "Failed", "processing": "Processing"}, "tools": {"sqlQuery": "SQL Query", "chartGeneration": "Chart Generation", "dataExport": "Data Export", "reportGeneration": "Report Generation", "apiCall": "API Call", "executeTool": "Execute Tool", "autoSqlQuery": "Auto SQL Query", "executionResult": "Execution Result", "toolExecutionResult": "Tool Execution Result: {{toolName}}", "resourceResult": "Resource Result: {{resourcePath}}", "executionTime": "Duration: {{time}} ms", "unknownResource": "Unknown Resource", "unknownTool": "Unknown <PERSON>l", "intelligentEvaluation": "Intelligent Evaluation", "intentConfirmation": "Intent Confirmation", "intelligentChartGeneration": "Intelligent Chart Generation", "intentAnalysis": "Intent Analysis", "executionParameters": "Execution Parameters:", "executedSQL": "Executed SQL:", "recordCount": "Total {{count}} records", "totalRecords": "Total {{total}} records", "chartAnalysis": "Intelligent Chart Analysis", "multiChartAnalysis": "Intelligent Multi-Chart Analysis", "chartGenerationComplete": "Chart Generation Complete", "chartsGenerated": "Generated {{count}} charts", "singleChartGenerated": "Single chart generated", "viewChart": "View Chart", "viewAllCharts": "View All Charts", "viewChartConfig": "View Chart {{index}} Config", "viewChartConfigDetails": "View Chart Configuration Details", "chartConfigInvalid": "Chart configuration invalid", "chartConfigInvalidWithIndex": "Chart {{index}} configuration invalid", "chartConfigInvalidOrEmpty": "Chart configuration invalid or empty", "generationReason": "Generation Reason:", "generationTime": "Generation Time", "dataSourceInfo": "Data Source Info", "rawData": "Raw Data:", "dataSource": "Data Source", "chartType": "Chart Type", "chartGenerated": "Chart generated successfully", "multiChartsGenerated": "{{count}} charts generated successfully"}, "multiRound": {"title": "Multi-Round Intelligent Analysis", "subtitle": "Dear {{username}} partner, ask me anything...", "welcome": "welcome", "welcomeTitle": "Start Your Data Analysis Journey", "welcomeSubtitle": "Enter your analysis questions, supports multi-round conversation", "placeholder": "Please enter your analysis question, supports multi-round continuous conversation...", "startAnalysis": "Start Analysis", "continue": "Continue Analysis", "context": "Context", "steps": "Analysis Steps", "currentStep": "Current Step", "nextStep": "Next Step", "interrupt": "Interrupt Analysis", "resume": "Resume Analysis", "round": "Round {{round}} Question", "continueQuestion": "Continue Questioning", "continueAnalysis": "Continue Analysis", "newConversation": "New Conversation", "newConversationCreated": "New conversation created, please start with your first question", "analysisComplete": "Analysis Complete", "analysisRunning": "Analyzing...", "analysisFailed": "Analysis Failed", "continueDialog": {"title": "Continue Conversation", "subtitle": "Continue asking questions based on previous analysis results", "roundIndicator": "Round {{round}} Conversation", "smartContextAware": "🧠 Smart Context Aware", "placeholder": "Based on the previous analysis results, what else would you like to know? AI will intelligently correlate historical context...", "validationMessage": "Please enter your question", "templateButton": "Template", "templateTooltip": "Use analysis template", "aiContextTip": "AI will intelligently correlate historical context"}, "conversationLoadSuccess": "Conversation loaded successfully", "smartContextAware": "Smart Context Aware", "contextBasedQuestion": "Based on the previous {{count}} rounds of analysis results, what else would you like to know? AI will intelligently correlate historical context...", "fallbackContextQuestion": "Based on the previous analysis results, what else would you like to know?", "tip": "Tip: Enter new questions to continue multi-round conversation", "waitingForFeedback": "Waiting for <PERSON><PERSON><PERSON>", "interruptAnalysis": "Interrupt Analysis", "cancelAnalysis": "Cancel Analysis", "interruptAnalysisAndFeedback": "Interrupt Analysis and Provide Feedback", "selectTemplate": "Select Analysis Template", "submitFeedback": "Submit <PERSON>", "cancel": "Cancel", "feedbackLabel": "Please describe how you would like to adjust the analysis", "feedbackTip": "Tip", "feedbackDescription": "The system will adjust the analysis direction based on your feedback and continue execution without losing existing analysis progress."}, "intentConfirmation": {"title": "Intent Confirmation", "description": "Please confirm my understanding of your question for more accurate analysis", "yourQuestion": "Your Question", "analysisIntent": "Analysis Intent", "executionSteps": "Execution Plan", "additionalNotes": "Additional Notes", "additionalNotesPlaceholder": "If you have any additions or corrections to the above analysis, please enter them here...", "answerPlaceholder": "Please enter your answer", "answerOrSelect": "Please enter your answer or select from the options above", "confirmAndContinue": "Confirm and Continue Analysis", "cancel": "Cancel", "completed": "Intent Confirmation Completed", "completedAdjusted": "Intent Confirmation Completed (Adjusted)", "systemContinueMessage": "The system will continue analysis based on this information", "waitingForConfirmation": "Waiting for user intent confirmation", "clarificationQA": "Clarification Q&A", "supplementNote": "Additional Notes", "confirmExecutePlan": "Confirm to execute analysis according to plan", "clarificationReply": "Clarification Reply:", "additionalRequirements": "Additional Requirements:", "answer": "Answer:"}, "status": {"pending": "Waiting for <PERSON><PERSON><PERSON>", "adjusted": "Adjusted", "timeout": "Timeout"}, "messages": {"executionFailed": "Analysis execution failed"}, "errors": {"noProject": "Please select a project first", "noProjectDescription": "Please select a project from the top navigation bar before proceeding with data analysis", "queryRequired": "Please enter analysis question", "analysisError": "Analysis Error", "multiRoundAnalysisError": "Multi-round analysis error: {{error}}", "startAnalysisFailed": "Failed to start analysis: {{error}}", "conversationLoadFailed": "Failed to load conversation", "analysisExecutionFailed": "Analysis execution failed: {{error}}", "analysisDataFormatError": "Analysis data format error, expected array format", "unknownError": "Unknown error", "analysisFailed": "Analysis failed: {{error}}", "error": "Error", "stepExecutionFailed": "Step execution failed", "stepExecutionError": "Step execution error", "analysisStopped": "Analysis stopped: {{error}}", "errorDetails": "Error Details:", "noIntentAnalysisData": "No intent analysis data available", "userIntentAnalysisResult": "User Intent Analysis Result", "chartDataFormatIncorrect": "Chart data format incorrect", "parseChartDataFailed": "Failed to parse chart data: {{error}}", "chartConfigDataInvalid": "Chart configuration data invalid", "noDownloadableChartData": "No downloadable chart data available", "operationFailed": "Operation failed, please try again", "sqlExecutionFailed": "SQL Execution Failed", "queryExecutionFailed": "Query execution failed", "errorType": "Error Type: {{type}}", "queryResultEmpty": "Query result is empty", "renderSQLResultError": "Error occurred while rendering SQL result", "expandMore": "More", "jsonParseFailed": "JSON parsing failed, trying alternative approach", "feedbackPlaceholder": "For example: use different analysis methods, adjust query conditions, focus on different data dimensions, etc...", "interruptAnalysisFailedRetry": "Failed to interrupt analysis, please try again", "noAnalysisInProgress": "No analysis currently in progress", "waitingForFeedbackWithTime": "Waiting for user feedback... Remaining time: {{minutes}} minutes", "getProjectListFailed": "Failed to get project list", "analysisCancelled": "Analysis cancelled", "missingInterruptInfo": "Missing necessary interrupt information", "pleaseFillFeedback": "Please fill in feedback content", "feedbackSubmitted": "Feedback submitted, analysis will continue", "submitFeedbackFailed": "Failed to submit feedback", "submitFeedbackFailedRetry": "Failed to submit feedback, please try again", "noAnalysisRunning": "No analysis currently running", "analysisInterrupted": "Analysis interrupted, please provide feedback", "chartDataFormatAbnormal": "Chart data format abnormal", "processChartDataFailed": "Failed to process chart data", "feedbackApplied": "User feedback applied, analysis continues", "feedbackTimeout": "User feedback timeout, analysis continues", "continueAnalysisFailed": "Failed to continue analysis, please try again", "submitIntentConfirmationFailed": "Failed to submit intent confirmation: {{error}}", "switchConversationFailed": "Failed to switch conversation", "pleaseSelectProject": "Please select a project first", "newConversationCreated": "New conversation created, please start your first question", "downloadChartFailed": "Failed to download chart, please try again", "chartDownloadSuccess": "Chart downloaded successfully", "chartsDownloadSuccess": "Successfully downloaded {{count}} charts"}, "interrupt": {"title": "Analysis Adjustment", "interruptedTitle": "Analysis Interrupted", "description": "You can adjust the analysis direction or provide additional information at any time", "adjustmentType": "Adjustment Type", "feedback": "Adjustment Suggestions", "submit": "Submit Adjustment", "cancel": "Cancel", "strategy": "Strategy Adjustment", "parameter": "Parameter Adjustment", "direction": "Direction Adjustment", "feedbackApplied": "User feedback applied", "feedbackTimeout": "User feedback timeout"}, "charts": {"chartDisplay": "Chart Display", "multipleCharts": "Chart Display - Total {{count}} charts", "downloadChart": "Download Chart"}, "templates": {"title": "Analysis Templates", "hotTemplates": "Popular Templates", "personalTemplates": "Personal Templates", "moreTemplates": "More Templates", "saveTemplate": "Save Template", "useTemplate": "Use Template", "templateApplied": "Template applied: {{name}}", "quickStart": "Quick Start", "usageCount": "Usage Count", "personalTemplate": "Personal Template", "systemTemplate": "System Template", "noPersonalTemplates": "No Personal Templates", "noPersonalTemplatesDesc": "You haven't created any personal templates yet. Start with system templates or create your own.", "noHotTemplates": "No Hot Templates", "noHotTemplatesDesc": "Hot templates will appear here when templates are used", "createFirstTemplate": "Create First Template", "defaultTemplates": {"dataOverview": {"name": "Data Overview Analysis", "description": "Analyze the basic situation of current data, including data volume, main fields, data quality and other overview information", "content": "Please help me analyze the basic situation of current data, including data volume, main fields, data quality and other overview information."}, "trendAnalysis": {"name": "Trend Analysis", "description": "Analyze data trends over time, identify key change patterns and anomalies", "content": "Please help me analyze data trends over time, identify key change patterns and anomalies."}, "comparisonAnalysis": {"name": "Comparison Analysis", "description": "Perform comparative data analysis to find differences and correlations between different dimensions", "content": "Please help me perform comparative data analysis to find differences and correlations between different dimensions."}, "deepInsights": {"name": "Deep Insights", "description": "Conduct in-depth analysis based on data, provide business insights and improvement suggestions", "content": "Please conduct in-depth analysis based on data, provide business insights and improvement suggestions."}}}, "templateManagement": {"title": "Analysis Template Management", "createTemplate": "Create Template", "editTemplate": "Edit Template", "previewTemplate": "Preview Template", "deleteTemplate": "Delete Template", "templateName": "Template Name", "templateDescription": "Description", "templateContent": "Template Content", "usageCount": "Usage Count", "creator": "Creator", "actions": "Actions", "isPublic": "Public", "publicTemplate": "Public template (available to other users)", "systemTemplate": "System", "publicTag": "Public", "preview": "Preview", "edit": "Edit", "delete": "Delete", "close": "Close", "confirm": "Confirm", "cancel": "Cancel", "noDescription": "No description", "placeholders": {"templateName": "Please enter template name", "templateDescription": "Please enter template description", "templateContent": "Please enter analysis prompt template..."}, "messages": {"templateCreated": "Template created successfully", "templateUpdated": "Template updated successfully", "templateDeleted": "Template deleted successfully", "createFailed": "Failed to create template", "updateFailed": "Failed to update template", "deleteFailed": "Failed to delete template", "fetchFailed": "Failed to fetch template list", "deleteConfirm": "Are you sure you want to delete this template?"}, "validation": {"nameRequired": "Please enter template name", "contentRequired": "Please enter template content"}, "pagination": {"total": "Total {{total}} templates"}, "complexity": {"basic": "Basic", "intermediate": "Intermediate", "advanced": "Advanced", "general": "General"}, "noProject": {"title": "Please select a project first", "description": "Please select a project from the top navigation before managing templates"}, "search": {"placeholder": "Search template name, description..."}, "templatePanel": {"usageCount": "times used", "preview": "Preview", "collapse": "Collapse", "useTemplate": "Use Template", "templateContent": "Template Content", "parameterizedUse": "Parameterized Use", "parameterizedUseTitle": "Use parameterized template", "category": "Category", "complexity": "Complexity", "allCategories": "All Categories", "allComplexity": "All Complexity", "loading": "Loading...", "empty": "No templates", "fetchFailed": "Failed to fetch template list", "useFailed": "Failed to use template"}}, "taxInfo": {"notFound": "Tax Information Not Found", "notFoundDescription": "Unable to retrieve relevant tax information from the vector database."}, "news": {"notFound": "No negative news data found", "analysisTitle": "Negative News Analysis", "foundCount": "Found {{count}} related news items, sorted by time in descending order", "keywords": {"negative": "Negative", "risk": "Risk", "warning": "Warning", "violation": "Violation", "penalty": "Penalty", "lawsuit": "Lawsuit", "prosecution": "Prosecution", "defendant": "Defendant", "plaintiff": "Plaintiff", "bankruptcy": "Bankruptcy", "debt": "Debt", "taxDebt": "Tax Debt", "layoff": "Layoff", "loss": "Loss", "downgrade": "Downgrade", "decrease": "Decrease", "alert": "<PERSON><PERSON>", "investigation": "Investigation", "neutral": "Neutral", "positive": "Positive", "commendation": "Commendation", "award": "Award", "growth": "Growth", "profit": "Profit", "success": "Success"}}, "insightDiscovered": "Insight Discovered: {{title}}", "multiChartsGenerated": "Generated {{count}} charts", "chartGenerated": "Chart generated successfully"}