{"title": "Project Management", "list": {"title": "Project List", "createProject": "Create Project", "name": "Project Name", "description": "Description", "visibility": "Visibility", "createdAt": "Created At", "updatedAt": "Updated At", "status": "Status", "owner": "Owner", "members": "Members", "createdTime": "Created Time", "actions": "Actions", "edit": "Edit", "delete": "Delete", "view": "View", "viewDetail": "View Details", "settings": "Settings", "editProject": "Edit Project", "deleteConfirm": "Are you sure you want to delete this project? This action cannot be undone.", "deleteSuccess": "Project deleted successfully", "deleteError": "Failed to delete project", "updateSuccess": "Project updated successfully", "createSuccess": "Project created successfully", "nameExists": "Project name already exists, please use a different name"}, "wizard": {"title": "Create Project Wizard", "step1": "Project Information", "step2": "Data Source Configuration", "step3": "Table Selection", "step4": "Table Description", "step5": "Intelligent Detection", "next": "Next", "steps": {"project": "Project Info", "dataSource": "Data Source Configuration", "tables": "Table Selection", "complete": "Complete"}, "form": {"projectName": "Project Name", "projectNameRequired": "Please enter project name", "projectNamePlaceholder": "Enter project name", "projectDescription": "Project Description", "projectDescriptionPlaceholder": "Enter project description", "projectVisibility": "Project Visibility", "projectVisibilityRequired": "Please select project visibility", "visibilityPrivate": "Private Project", "visibilityPublic": "Public Project"}, "dataSource": {"oracle": "Oracle", "mysql": "MySQL", "postgresql": "PostgreSQL", "mssql": "SQL Server"}, "validation": {"hostRequired": "Please enter host address", "portRequired": "Please enter port", "serviceNameRequired": "Please enter service name", "usernameRequired": "Please enter username", "passwordRequired": "Please enter password", "databaseRequired": "Please enter database name", "baseUrlRequired": "Please enter API base URL"}, "placeholders": {"hostExample": "e.g.: localhost or IP address", "oraclePort": "e.g.: 1521", "mysqlPort": "e.g.: 3306 (MySQL) / 5432 (PostgreSQL) / 1433 (MSSQL)", "serviceNameExample": "e.g.: ORCL", "oracleUsername": "e.g.: system", "mysqlUsername": "e.g.: root", "databaseExample": "e.g.: example_db", "baseUrlExample": "e.g.: https://api.example.com/v1", "headersExample": "e.g.: {\"Content-Type\": \"application/json\", \"Authorization\": \"Bearer token\"}"}, "labels": {"baseUrl": "Base URL"}, "tooltips": {"requestHeaders": "Enter request headers in JSON format"}, "messages": {"projectCreated": "Project created successfully", "projectCreateFailed": "Failed to create project", "projectNameExists": "Project name already exists, please use a different name", "projectCompleted": "Project creation completed", "projectChangeNotSupported": "Project information changes detected. Current version does not support modifying existing project information. Please create a new project.", "testConnectionFirst": "Please test connection successfully before submitting"}, "tableSelection": {"title": "Select Analysis Tables", "dataSourceIdMissing": "Data source ID does not exist, please reconfigure the data source", "noTablesAvailable": "No tables available in this data source", "refreshSuccess": "Refresh successful, found {{count}} tables", "invalidTableListFormat": "Invalid table list format received", "fetchTablesFailed": "Failed to fetch data table list", "requestTimeout": "Request timeout, please check network connection or try again later", "selectAtLeastOneTable": "Please select at least one table", "readingTables": "Reading database table information...", "pleaseWait": "Please wait, this may take a few seconds", "unableToFetchTables": "Unable to fetch table information, please check data source configuration or network connection", "refetch": "Refetch", "description": "Please select the data tables you want to analyze from the list below. The system will automatically extract table structure and sample data for subsequent analysis and SQL generation.", "tableCount": "Total {{total}} tables, {{selected}} selected", "usingCachedData": "using cached data", "selectAll": "Select All", "deselectAll": "Deselect All", "refreshTableList": "Refresh table list", "tableName": "Table Name", "paginationInfo": "{{start}}-{{end}} of {{total}} items", "refreshList": "Refresh List", "backToDataSource": "Back to Data Source Configuration", "nextStep": "Next Step ({{status}})", "selectedCount": "{{count}} tables selected", "noTableSelected": "No table selected"}, "tableDescription": {"title": "Add Table Descriptions (Optional)", "saveSelectedTablesSuccess": "Selected tables saved successfully", "saveFailed": "Save failed", "saveTablesFailed": "Failed to save tables", "fetchSelectedTablesFailed": "Failed to fetch selected tables", "descriptionSaved": "Description for table {{tableName}} has been saved", "saveDescriptionFailed": "Failed to save table description", "fieldName": "Field Name", "dataType": "Data Type", "nullable": "Nullable", "savingAndLoading": "Saving selected tables and reading table structure...", "loadingTableInfo": "Loading table information...", "processWaitMessage": "This process may take 1-3 minutes, please be patient", "description": "Adding business descriptions to selected data tables can help the system better understand your data structure and business meaning. You can choose to skip this step and add table descriptions in project management later.", "hasDescription": "Description added", "noDescription": "No description added", "tableTitle": "Table: {{tableName}}", "schemaOverview": "Schema Overview", "tableDescriptionLabel": "Table Description", "descriptionPlaceholder": "Optional: Please enter the business description of this table, including the purpose of the table, the meaning of main fields, relationships with other tables, etc.", "saveDescription": "Save Description", "selectTableFromLeft": "Please select a table from the left", "enterSelfCheck": "Enter Self-Check"}, "memberManagement": {"title": "Project Member Management", "addMember": "Add Member", "noInvitableUsers": "No invitable users available", "username": "Username", "selectUser": "Select User", "selectUserRequired": "Please select a user to add", "selectUserPlaceholder": "Please select user", "projectRole": "Project Role", "projectRoleRequired": "Please select project role", "selectRole": "Please select role", "collaborator": "Collaborator", "collaboratorDescription": "Collaborator - Can operate + manage tools", "observerDescription": "Observer - Can view + execute analysis", "addSuccess": "Member added successfully", "addFailed": "Failed to add member", "removeSuccess": "Member removed successfully"}}, "form": {"name": "Project Name", "nameRequired": "Please enter project name", "namePlaceholder": "Enter project name", "description": "Project Description", "descriptionPlaceholder": "Enter project description", "visibility": "Project Visibility", "visibilityRequired": "Please select project visibility", "visibilityPlaceholder": "Select project visibility"}, "visibility": {"public": "Public", "private": "Private", "publicProject": "Public Project", "privateProject": "Private Project"}, "roles": {"collaborator": "Project Collaborator", "observer": "Project Observer"}, "detail": {"title": "Project Details", "overview": "Overview", "dataSource": "Data Source", "tables": "Data Tables", "members": "Project Members", "analysis": "Analysis Records", "settings": "Project Settings", "statistics": {"totalTables": "Total Tables", "totalColumns": "Total Columns", "totalAnalysis": "Total Analysis", "lastAnalysis": "Last Analysis"}}, "intelligence": {"title": "Intelligent Project Understanding", "businessUnderstanding": "Business Understanding", "questions": "Intelligent Q&A", "scenarios": "Scenario Prediction", "summary": "Summary", "steps": {"readinessAssessment": "Readiness Assessment", "readinessAssessmentDesc": "Evaluate project data readiness", "scenarioValidation": "Scenario Validation", "scenarioValidationDesc": "Confirm analysis scenario predictions"}, "analyzing": {"evaluating": "Evaluating project readiness...", "analyzingData": "Analyzing data structure and business scenarios", "pleaseWait": "Please wait, this may take 1-2 minutes"}, "businessDomain": "Business Domain", "businessType": "Business Type", "keyEntities": "Key Entities", "coreMetrics": "Core Metrics", "dataRelationships": "Data Relationships", "noIdentified": "Not identified", "needsFurtherAnalysis": "Needs further analysis", "exampleQuestions": "Example Questions", "analysisBoundaries": "Analysis Boundaries", "additionalInfoNeeded": "Additional Information Needed", "tip": "Tip", "nextStepDescription": "Next, the system will collect additional information from you on these questions to better understand your business needs.", "priorityRequired": "Required", "priorityImportant": "Important", "priorityOptional": "Optional", "priorityNormal": "Normal", "generatingQuestions": "Generating Intelligent Questions...", "generatingPersonalizedQuestions": "Generating personalized questions for you...", "projectInfoComplete": "Project Information Complete", "excellentProjectInfo": "🎉 Excellent! Based on analysis, your project information is already comprehensive and no additional information is needed.", "canProceedToScenarioValidation": "You can proceed directly to the scenario validation stage, where the system will generate personalized data analysis scenarios for you.", "systemAnalysisComplete": "The system has analyzed your data structure, table relationships, and business information, and believes that the current information is sufficient to generate high-quality analysis scenarios. If you find that additional information is needed later, you can make adjustments in project settings at any time.", "redQuestionDescription": "Questions marked in red are required and will affect subsequent data analysis results", "orangeQuestionDescription": "Questions marked in orange are important and it is recommended to fill them out for better service", "blueQuestionDescription": "Questions marked in blue are optional and can help with personalized optimization", "processingStages": {"analyzing": "Intelligent Analysis", "analyzingDesc": "Analyzing your feedback information and identifying valuable business supplementary content...", "converting": "Content Conversion", "convertingDesc": "Converting your feedback into professional business descriptions to optimize data Q&A effectiveness...", "saving": "Saving Optimization", "savingDesc": "Saving optimized business understanding to data source description...", "completed": "Processing Complete", "completedDesc": "Intelligent understanding optimization complete, you can now start using the data Q&A feature!"}, "confirmationCorrect": "✅ Understanding Correct", "confirmationNeedsRevision": "🔄 Needs Revision", "confirmationRejected": "❌ Understanding Error", "confirmationPending": "Pending Confirmation", "generatingScenarios": "Generating Scenario Predictions...", "generatingScenariosDesc": "Predicting common usage scenarios based on your answers...", "scenarioTitle": "Based on my understanding, you might frequently ask the following questions", "pleaseConfirmUnderstanding": "Please confirm if my understanding is correct", "scenarioNumber": "<PERSON><PERSON><PERSON> {{number}}", "confidence": "Confidence", "predictedQuestion": "📝 Predicted Question", "myUnderstandingAnalysis": "🎯 My Understanding Analysis", "usingDefaultAnalysis": "⚠️ Using Default Analysis", "plannedSteps": "Planned {{count}} execution steps", "agentAnalysis": "Agent Analysis", "agentAnalysisFailed": "⚠️ Agent analysis failed: {{error}}", "usingDefaultUnderstanding": "Using default understanding points", "pleaseConfirmMyUnderstanding": "Please confirm my understanding", "understandingCorrect": "✅ Understanding correct", "understandingNeedsAdjustment": "🔄 Basically correct, but needs some adjustments", "understandingCompletelyWrong": "❌ Understanding completely wrong", "feedbackPlaceholder": "Please explain how to adjust or why the understanding is incorrect...", "processing": "Processing...", "intelligenceComplete": "Intelligence Understanding Complete!", "intelligenceCompleteDesc": "I have fully understood your business requirements and can now provide more accurate data analysis services for you", "startUsingProject": "Start Using Project", "understandingSummary": "Understanding Summary", "generalBusiness": "General Business", "dataAnalysis": "Data Analysis", "answeredQuestions": "Answered Questions", "confirmedScenarios": "Confirmed <PERSON><PERSON><PERSON>s", "coreBusinessProcesses": "🎯 Core Business Processes", "keyBusinessEntities": "🔑 Key Business Entities", "mainBusinessMetrics": "📊 Main Business Metrics", "nextStepsTitle": "🚀 What you can do next:", "nextStep1": "Start conversing directly with the AI assistant and ask data-related questions", "nextStep2": "I will provide more accurate analysis based on my understanding", "nextStep3": "If my understanding is biased, you can correct me at any time during the conversation", "nextStep4": "I will continuously learn and optimize my understanding during use", "enterScenarioValidation": "Enter Scenario Validation", "explanation": "Explanation", "scenarioExplanation": "Each scenario contains a specific business question and my understanding analysis of that question", "agentAnalysisExplanation": "Agent analysis shows the data tables and fields I think are needed to answer this question", "agentAnalysisDetail": "This helps you evaluate whether my understanding is accurate and whether the data is sufficient", "understandingPoints": "Please confirm whether my understanding is correct for each scenario", "confirmationHelp": "Your confirmation will help me provide more accurate data analysis services", "intelligentOptimization": "Based on your confirmation, the system will intelligently optimize subsequent analysis effectiveness", "analysisComplete": "🎉 Analysis Complete! You can:"}}