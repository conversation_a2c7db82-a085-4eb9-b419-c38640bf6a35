{"tableSelection": {"title": "Select Analysis Tables", "loading": "Reading database table information...", "loadingDescription": "Please wait, this may take a few seconds", "error": "Failed to get table list", "retry": "Retry", "noDataError": "Unable to get data table information, please check data source configuration or network connection", "refreshData": "Refresh Data", "description": "Please select the data tables needed for analysis from the list below. The system will automatically extract table structure and sample data for subsequent analysis and SQL generation.", "statistics": {"total": "Total", "selected": "tables, selected", "tables": "tables"}, "cached": "(using cached data)", "selectAll": "Select All", "deselectAll": "Deselect All", "refresh": "Refresh", "tableName": "Table Name", "pagination": {"showing": "Showing", "to": "to", "items": "items"}, "noTables": "No available tables under this data source", "refreshList": "Refresh List", "backToDataSource": "Back to Data Source Configuration", "nextStep": "Next Step (", "selected": "selected", "noSelection": "No tables selected"}, "validation": {"dataSourceIdRequired": "Data source ID does not exist, please reconfigure the data source", "noTablesAvailable": "No available tables under this data source", "invalidTableFormat": "Invalid table list format received", "fetchTablesFailed": "Failed to fetch table list", "fetchTablesError": "Failed to get data table list", "requestTimeout": "Request timeout, please check network connection or try again later", "selectAtLeastOne": "Please select at least one table"}}