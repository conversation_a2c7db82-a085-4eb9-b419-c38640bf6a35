{"title": "Administration", "users": {"title": "User Management", "list": "User List", "create": "Create User", "edit": "Edit User", "delete": "Delete User", "enable": "Enable", "disable": "Disable", "resetPassword": "Reset Password", "username": "Username", "email": "Email", "role": "Role", "status": "Status", "lastLogin": "Last Login", "createdAt": "Created At", "subAccountManagement": "Sub Account Management", "createSubAccount": "Create Sub Account", "editSubAccount": "Edit Sub Account", "deleteSubAccount": "Delete Sub Account", "systemRole": "System Role", "projectPermission": "Project Creation Permission", "superAdmin": "Super Admin", "enterpriseAdmin": "Enterprise Admin", "regularUser": "Regular User", "unknownRole": "Unknown Role", "enabled": "Enabled", "disabled": "Disabled", "canCreate": "Can Create", "cannotCreate": "Cannot Create", "allow": "Allow", "forbid": "Forbid", "newPassword": "New Password (leave blank to keep unchanged)", "accountStatus": "Account Status", "deleteConfirmTitle": "Are you sure you want to delete this sub account?", "deleteConfirmDescription": "This action cannot be undone, please proceed with caution.", "toggleStatusTooltip": "Click to {{action}} user", "enableUser": "enable", "disableUser": "disable", "createSuccess": "Sub account created successfully", "updateSuccess": "Sub account updated successfully", "deleteSuccess": "Sub account deleted successfully", "createFailed": "Creation failed", "updateFailed": "Update failed", "deleteFailed": "Deletion failed", "toggleStatusFailed": "Status toggle failed", "fetchFailed": "Failed to fetch user list", "operationFailed": "Operation failed", "usernameRequired": "Please enter username", "usernameMinLength": "Username must be at least 3 characters", "usernameMaxLength": "<PERSON><PERSON><PERSON> must be at most 20 characters", "usernamePattern": "Username can only contain letters, numbers and underscores", "emailInvalid": "Please enter a valid email address", "passwordRequired": "Please enter password", "passwordMinLength": "Password must be at least 6 characters", "passwordPlaceholder": "Leave blank to keep unchanged", "projectPermissionTooltip": "Control whether this user can create new projects", "refresh": "Refresh", "paginationTotal": "{{start}}-{{end}} of {{total}} items"}, "roles": {"title": "Role Management", "list": "Role List", "create": "Create Role", "edit": "Edit Role", "delete": "Delete Role", "permissions": "Permissions", "roleName": "Role Name", "description": "Description", "admin": "Administrator", "user": "User", "viewer": "Viewer"}, "organizations": {"title": "Organization Management", "list": "Organization List", "create": "Create Organization", "edit": "Edit Organization", "delete": "Delete Organization", "orgName": "Organization Name", "parentOrg": "Parent Organization", "members": "Member Count", "administrator": "Administrator"}, "settings": {"title": "System Settings", "general": "General Settings", "security": "Security Settings", "email": "<PERSON><PERSON>s", "backup": "Backup Settings", "logs": "Log Settings", "systemInfo": "System Information", "version": "System Version", "database": "Database Information", "storage": "Storage Information"}, "statistics": {"title": "Statistics", "dashboard": "Data Overview", "userCount": "Total Users", "projectCount": "Total Projects", "analysisCount": "Analysis Count", "storageUsed": "Storage Used", "activeUsers": "Active Users", "todayAnalysis": "Today's Analysis", "systemLoad": "System Load"}, "logs": {"title": "Operation Logs", "userLogs": "User Logs", "systemLogs": "System Logs", "errorLogs": "<PERSON><PERSON><PERSON>", "timestamp": "Timestamp", "user": "User", "action": "Action", "resource": "Resource", "result": "Result", "ipAddress": "IP Address"}}