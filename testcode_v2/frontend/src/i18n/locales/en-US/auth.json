{"login": {"title": "User Login", "subtitle": "AI-Driven Decisions, Data Insights for the Future", "username": "Username", "usernamePlaceholder": "Enter username", "password": "Password", "passwordPlaceholder": "Enter password", "rememberMe": "Remember me", "forgotPassword": "Forgot password?", "loginButton": "<PERSON><PERSON>", "noAccount": "Don't have an account?", "register": "Register now", "loginSuccess": "Login successful", "loginFailed": "<PERSON><PERSON> failed", "invalidCredentials": "Invalid username or password", "accountLocked": "Account is locked", "pleaseLogin": "Please login first", "features": {"aiAnalysis": {"title": "AI Smart Analysis", "description": "Intelligent data analysis based on large language models, automatically identifying data patterns and trends"}, "conversational": {"title": "Conversational Interaction", "description": "Natural language Q&A, multi-round dialogue maintaining context, making data analysis simpler"}, "visualization": {"title": "Visual Insights", "description": "Rich chart types, real-time generation of visual reports, insights at a glance"}, "smartRecommendation": {"title": "Smart Recommendations", "description": "AI-driven analysis suggestions, automatically discovering key information and anomalies in data"}, "multiSource": {"title": "Multi-Source Integration", "description": "Support for multiple data source connections, unified management, one-stop data analysis platform"}, "realTimeMonitoring": {"title": "Real-Time Monitoring", "description": "Real-time data monitoring and alerts, timely discovery of business changes and risk points"}}}, "register": {"title": "User Registration", "subtitle": "Create your account", "email": "Email Address", "confirmPassword": "Confirm Password", "agreement": "I have read and agree to", "termsOfService": "Terms of Service", "privacyPolicy": "Privacy Policy", "registerButton": "Register", "hasAccount": "Already have an account?", "loginNow": "Login now", "registerSuccess": "Registration successful", "registerFailed": "Registration failed", "emailExists": "Email already exists"}, "profile": {"title": "Profile", "basicInfo": "Basic Information", "avatar": "Avatar", "nickname": "Nickname", "realName": "Real Name", "phone": "Phone Number", "department": "Department", "position": "Position", "updateProfile": "Update Profile", "changePassword": "Change Password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password"}, "permissions": {"accessDenied": "Access Denied", "insufficientPermissions": "Insufficient Permissions", "contactAdmin": "Please contact administrator", "loginRequired": "<PERSON><PERSON> Required", "sessionExpired": "Session Expired", "logout": "Logout", "logoutConfirm": "Are you sure you want to logout?"}}