{"title": "Project Details", "tabs": {"overview": "Project Overview", "basicInfo": "Basic Information", "dataSource": "Data Source Information", "dataTables": "Data Tables Information", "memberManagement": "Member Management", "reportSettings": "Report Settings"}, "buttons": {"backToList": "Back to Project List", "editProject": "Edit Project", "deleteProject": "Delete Project", "addDataSource": "Add Data Source", "editDataSource": "Edit Data Source", "testConnection": "Test Connection", "confirmDelete": "Confirm Delete", "selectDataTable": "Select Data Table", "save": "Save", "cancel": "Cancel", "resetToDefault": "Reset to De<PERSON>ult", "editTables": "Edit Tables", "editTableDescriptions": "Edit Table Descriptions", "editPrompt": "Edit Prompt", "selectTablesFor": "Select tables for {{name}}"}, "modals": {"editProject": "Edit Project", "confirmDelete": "Confirm Delete", "deleteMessage": "Are you sure you want to delete this project? This action cannot be undone.", "editDataSource": "Edit Data Source", "addDataSource": "Add Data Source", "selectDataTable": "Select Data Table", "selectTables": "Select Data Tables", "editReportPrompt": "Edit Report Generation Prompt"}, "labels": {"projectName": "Project Name", "projectDescription": "Project Description", "visibility": "Visibility", "owner": "Owner", "createdTime": "Created Time", "dataSourceName": "Data Source Name", "dataSourceType": "Data Source Type", "dataSourceDescription": "Data Source Description", "connectionConfig": "Connection Configuration", "hostAddress": "Host Address", "port": "Port", "serviceName": "Service Name", "username": "Username", "password": "Password", "databaseName": "Database Name", "baseUrl": "Base URL", "requestHeaders": "Request Headers", "reportPrompt": "Report Generation Prompt", "projectId": "Project ID", "projectOwner": "Project Owner", "projectVisibility": "Project Visibility", "userId": "User ID", "noDataSourceConfigured": "No data source configured", "noTablesSelected": "No tables selected", "noTablesSelectedForDataSource": "No tables selected for this data source", "currentPrompt": "Current Prompt", "usingDefaultPrompt": "Using default prompt", "description": "Description", "reportPromptDescription": "Custom prompts will be concatenated with system default prompts to influence the format and content focus of AI-generated analysis reports.", "connectionSuccess": "Connection successful", "tablesSelected": "{{count}} tables selected"}, "dataSourceTypes": {"oracle": "Oracle Database", "mysql": "MySQL Database", "postgresql": "PostgreSQL Database", "mssql": "SQL Server Database", "httpApi": "HTTP API"}, "messages": {"projectUpdateSuccess": "Project updated successfully", "projectUpdateFailed": "Failed to update project", "projectDeleteSuccess": "Project deleted successfully", "projectDeleteFailed": "Failed to delete project", "dataSourceAddSuccess": "Data source added successfully", "dataSourceAddFailed": "Failed to add data source", "dataSourceUpdateSuccess": "Data source updated successfully", "dataSourceUpdateFailed": "Failed to update data source", "dataSourceDeleteSuccess": "Data source deleted successfully", "dataSourceDeleteFailed": "Failed to delete data source", "connectionSuccess": "Connection successful", "connectionFailed": "Connection failed", "fetchProjectFailed": "Failed to fetch project information", "fetchDataSourceFailed": "Failed to fetch data source information", "fetchTablesFailed": "Failed to fetch data tables information", "saveTableSelectionSuccess": "Data table selection saved successfully", "saveTableSelectionFailed": "Failed to save data table selection", "tablesSaveSuccess": "Tables saved successfully", "reportPromptUpdateSuccess": "Report prompt updated successfully", "reportPromptUpdateFailed": "Failed to update report prompt", "testConnectionFirst": "Please test connection successfully first"}, "placeholders": {"enterProjectName": "Enter project name", "enterProjectDescription": "Enter project description", "enterDataSourceName": "Enter data source name", "enterDataSourceDescription": "Enter data source description", "selectDataSourceType": "Select data source type", "enterHostAddress": "e.g., localhost or IP address", "enterPort": "e.g., 3306", "enterServiceName": "e.g., ORCL", "enterUsername": "e.g., system", "enterPassword": "Enter password", "enterDatabaseName": "Enter database name", "enterBaseUrl": "e.g., https://api.example.com/v1", "enterRequestHeaders": "Enter request headers in JSON format", "requestHeadersExample": "e.g., {\n  \"Content-Type\": \"application/json\",\n  \"Authorization\": \"Bearer token\"\n}", "selectProjectVisibility": "Select project visibility", "enterDataSourceNameHere": "Enter data source name", "enterDataSourceDescriptionOptional": "Enter data source description (optional)", "selectDataSourceTypeHere": "Select data source type", "enterCustomReportPrompt": "Enter custom report generation prompt..."}, "validation": {"projectNameRequired": "Project name is required", "dataSourceNameRequired": "Data source name is required", "dataSourceTypeRequired": "Data source type is required", "hostRequired": "Please enter host address", "portRequired": "Please enter port", "serviceNameRequired": "Please enter service name", "usernameRequired": "Please enter username", "passwordRequired": "Please enter password", "databaseRequired": "Please enter database name", "baseUrlRequired": "Please enter API base URL", "projectVisibilityRequired": "Please select project visibility", "pleaseEnterDataSourceName": "Please enter data source name", "pleaseSelectDataSourceType": "Please select data source type", "pleaseEnterReportPrompt": "Please enter report generation prompt"}, "noDescription": "No description", "visibility": {"allMembersVisible": "Visible to all company members", "invitedMembersOnly": "Visible to invited members only"}, "dividers": {"connectionConfig": "Connection Configuration"}, "prompts": {"customPromptDescription": "Custom prompts will be concatenated with system default prompts to influence the format and content focus of AI-generated analysis reports. The system will automatically provide analysis results and chart information, you only need to define the report structure and focus points. Leave blank to use only the system default template."}, "defaultReportPrompt": "Please generate a professional data analysis report based on the analysis results, including the following sections:\n\n## 1. Executive Summary\n- Brief overview of analysis objectives and key findings\n- Highlight key metrics and important conclusions\n\n## 2. Detailed Data Analysis\n- Detailed analysis of various data indicators\n- Identify trends, patterns and anomalies in the data\n- Provide data-supported insights\n- Intelligently insert relevant charts at appropriate positions to support analysis\n\n## 3. Key Findings\n- Core findings based on data analysis\n- Highlight important business insights\n- Identify potential opportunities and risk points\n\n## 4. Conclusions and Recommendations\n- Core conclusions based on analysis\n- Provide specific actionable business recommendations\n- Explain the priority and expected effects of recommendations\n\nPlease ensure the report content is objective and accurate, using clear structure and professional language. When describing data trends and key findings, appropriately reference relevant charts to enhance persuasiveness."}