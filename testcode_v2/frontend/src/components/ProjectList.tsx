import React, { useEffect, useState } from 'react';
import { Button, Table, Space, Modal, Form, Input, message, Tag, Select } from 'antd';
import { EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';
import { Project, CreateProjectRequest } from '../types';
import { projectApi, authApi } from '../services/api';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

const ProjectList: React.FC = () => {
  const { t } = useTranslation();
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [editingProject, setEditingProject] = useState<Project | null>(null);
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 10,
    total: 0,
  });
  const [form] = Form.useForm();
  const navigate = useNavigate();

  const fetchProjects = async (page: number = 1, pageSize: number = 10) => {
    setLoading(true);
    try {
      // 转换前端分页参数为后端期望的参数
      const skip = (page - 1) * pageSize;
      const limit = pageSize;
      

      const response = await projectApi.getProjects({ skip, limit });
      const { items, page_info } = response.data.data;
      setProjects(items);
      setPagination({
        page: page_info.page,
        pageSize: page_info.page_size,
        total: page_info.total,
      });
    } catch (error) {
      message.error(t('common:message.error.fetchFailed'));
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // 获取当前用户信息
  const fetchCurrentUser = async () => {
    try {
      const response = await authApi.getCurrentUser();
      setCurrentUser(response.data.data);
    } catch (error) {
      console.error('Failed to get user info:', error);
    }
  };

  // 检查用户是否有项目编辑权限
  const canEditProject = (project: Project) => {
    if (!currentUser || !project) {
      return false;
    }
    
    // 优先使用后端返回的权限信息（如果项目详情中有权限信息）
    if (project.permissions && typeof project.permissions.can_edit === 'boolean') {
      return project.permissions.can_edit;
    }
    
    // 兜底逻辑：超级管理员、企业管理员（同企业）、项目所有者
    return (
      currentUser.role_id === 1 || // 超级管理员
      (currentUser.role_id === 2 && currentUser.org_id === project.org_id) || // 企业管理员
      project.owner_id === currentUser.id || // 项目所有者
      currentUser.is_superuser // 兼容旧字段
    );
  };

  // 检查用户是否有项目删除权限
  const canDeleteProject = (project: Project) => {
    if (!currentUser || !project) {
      return false;
    }
    
    // 优先使用后端返回的权限信息（如果项目详情中有权限信息）
    if (project.permissions && typeof project.permissions.can_delete === 'boolean') {
      return project.permissions.can_delete;
    }
    
    // 兜底逻辑：超级管理员、企业管理员（同企业）、项目所有者
    return (
      currentUser.role_id === 1 || // 超级管理员
      (currentUser.role_id === 2 && currentUser.org_id === project.org_id) || // 企业管理员
      project.owner_id === currentUser.id || // 项目所有者
      currentUser.is_superuser // 兼容旧字段
    );
  };

  useEffect(() => {
    fetchProjects(pagination.page, pagination.pageSize);
  }, [pagination.page, pagination.pageSize]);

  useEffect(() => {
    fetchCurrentUser();
  }, []);

  const handleAddProject = () => {
    setEditingProject(null);
    form.resetFields();
    // 设置新项目的默认可见性
    form.setFieldsValue({
      visibility: 'PRIVATE'
    });
    setModalVisible(true);
  };

  const handleEditProject = (project: Project) => {
    setEditingProject(project);
    form.setFieldsValue({
      name: project.name,
      description: project.description,
      visibility: project.visibility || 'PRIVATE',
    });
    setModalVisible(true);
  };

  const handleDeleteProject = async (id: string) => {
    Modal.confirm({
      title: t('common:confirm.delete'),
      content: t('project:list.deleteConfirm'),
      okText: t('common:button.confirm'),
      cancelText: t('common:button.cancel'),
      onOk: async () => {
        try {
          await projectApi.deleteProject(id);
          message.success(t('project:list.deleteSuccess'));
          // 如果删除后当前页没有数据了，则跳转到前一页
          if (projects.length === 1 && pagination.page > 1) {
            setPagination(prev => ({ ...prev, page: prev.page - 1 }));
          } else {
            fetchProjects(pagination.page, pagination.pageSize);
          }
        } catch (error) {
          message.error(t('project:list.deleteError'));
          console.error(error);
        }
      },
    });
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      
      if (editingProject) {
        const response = await projectApi.updateProject(editingProject.id, values);
        if (response.data.code === 400 && response.data.message.includes(t('project:list.nameExists'))) {
          message.error(t('project:list.nameExists'));
          form.getFieldInstance('name')?.focus();
          return;
        }
        message.success(t('project:list.updateSuccess'));
      } else {
        const response = await projectApi.createProject(values as CreateProjectRequest);
        if (response.data.code === 400 && response.data.message.includes(t('project:list.nameExists'))) {
          message.error(t('project:list.nameExists'));
          form.getFieldInstance('name')?.focus();
          return;
        }
        message.success(t('project:list.createSuccess'));
      }
      
      setModalVisible(false);
      fetchProjects(pagination.page, pagination.pageSize);
    } catch (error: any) {
      // 处理API错误
      if (error.response?.data?.message?.includes(t('project:list.nameExists'))) {
        message.error(t('project:list.nameExists'));
        form.getFieldInstance('name')?.focus();
      } else {
        console.error('Operation failed:', error);
        message.error(t('common:message.error.operationFailed'));
      }
    }
  };

  const validateProjectName = async (_: any, value: string) => {
    if (!value) {
      return Promise.reject(new Error(t('project:form.nameRequired')));
    }
    
    // 如果是编辑模式并且名称没有修改，则不需要验证
    if (editingProject && editingProject.name === value) {
      return Promise.resolve();
    }
    
    try {
      // 获取所有项目检查名称是否存在
      const response = await projectApi.getProjects({ skip: 0, limit: 1000 }); // 验证时获取所有项目
      const projects = response.data.data.items || [];
      const existingProject = projects.find((p: Project) => p.name === value);
      
      if (existingProject) {
        return Promise.reject(new Error(t('project:list.nameExists')));
      }
      
      return Promise.resolve();
    } catch (error) {
      // 如果验证请求失败，允许用户继续，后端会再次验证
      return Promise.resolve();
    }
  };

  const columns = [
    {
      title: t('project:list.name'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('project:list.description'),
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: t('project:list.visibility'),
      dataIndex: 'visibility',
      key: 'visibility',
      render: (visibility: string) => (
        <Tag color={visibility === 'PUBLIC' ? 'blue' : 'geekblue'}>
          {visibility === 'PUBLIC' ? t('project:visibility.public') : t('project:visibility.private')}
        </Tag>
      ),
    },
    {
      title: t('project:list.createdAt'),
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text: string) => new Date(text).toLocaleString(),
    },
    {
      title: t('project:list.updatedAt'),
      dataIndex: 'updated_at',
      key: 'updated_at',
      render: (text: string) => new Date(text).toLocaleString(),
    },
    {
      title: t('common:labels.actions'),
      key: 'action',
      render: (_: any, record: Project) => (
        <Space size="middle">
          <Button 
            type="link" 
            icon={<EyeOutlined />} 
            onClick={() => navigate(`/project-detail/${record.id}`)}
          >
            {t('project:list.viewDetail')}
          </Button>
          {canEditProject(record) && (
            <Button 
              type="link" 
              icon={<EditOutlined />} 
              onClick={() => handleEditProject(record)}
            >
              {t('common:button.edit')}
            </Button>
          )}
          {canDeleteProject(record) && (
            <Button 
              type="link" 
              danger 
              icon={<DeleteOutlined />} 
              onClick={() => handleDeleteProject(record.id)}
            >
              {t('common:button.delete')}
            </Button>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div>
      {/* <div style={{ marginBottom: 16 }}>
        <Button 
          type="primary" 
          icon={<PlusOutlined />} 
          onClick={handleAddProject}
        >
          {t('project:list.createProject')}
        </Button>
      </div> */}

      <Table 
        columns={columns} 
        dataSource={projects} 
        rowKey="id" 
        loading={loading} 
        pagination={{
          current: pagination.page,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showTotal: (total, range) => t('common:pagination.total', { total }),
          onChange: (page, pageSize) => {
            setPagination(prev => ({ 
              ...prev, 
              page, 
              pageSize: pageSize || 10 
            }));
          }
        }}
      />

      <Modal
        title={editingProject ? t('project:list.editProject') : t('project:list.createProject')}
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={() => setModalVisible(false)}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label={t('project:form.name')}
            rules={[
              { required: true, message: t('project:form.nameRequired') },
              { validator: validateProjectName }
            ]}
          >
            <Input placeholder={t('project:form.namePlaceholder')} />
          </Form.Item>
          <Form.Item
            name="description"
            label={t('project:form.description')}
          >
            <Input.TextArea placeholder={t('project:form.descriptionPlaceholder')} rows={4} />
          </Form.Item>
          <Form.Item
            name="visibility"
            label={t('project:form.visibility')}
            rules={[{ required: true, message: t('project:form.visibilityRequired') }]}
          >
            <Select placeholder={t('project:form.visibilityPlaceholder')}>
              <Select.Option value="PRIVATE">{t('project:visibility.privateProject')}</Select.Option>
              <Select.Option value="PUBLIC">{t('project:visibility.publicProject')}</Select.Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ProjectList; 