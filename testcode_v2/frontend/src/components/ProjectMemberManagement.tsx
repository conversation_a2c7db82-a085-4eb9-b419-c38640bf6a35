import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Select,
  message,
  Popconfirm,
  Tag,
  Space,
  Typography,
  Tooltip,
  Spin
} from 'antd';
import { PlusOutlined, UserDeleteOutlined, EditOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { projectMemberApi } from '../services/api';
import type { ProjectMember, ProjectRole, User } from '../types';

const { Title } = Typography;
const { Option } = Select;

interface ProjectMemberManagementProps {
  projectId: string;
  currentUser: User;
  project?: any;  // 添加项目信息
  onMemberChange?: () => void;
}

const ProjectMemberManagement: React.FC<ProjectMemberManagementProps> = ({
  projectId,
  currentUser,
  project,
  onMemberChange
}) => {
  const { t } = useTranslation();
  const [members, setMembers] = useState<ProjectMember[]>([]);
  const [invitableUsers, setInvitableUsers] = useState<User[]>([]);
  const [projectRoles, setProjectRoles] = useState<ProjectRole[]>([]);
  const [loading, setLoading] = useState(false);
  const [inviteModalVisible, setInviteModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [selectedMember, setSelectedMember] = useState<ProjectMember | null>(null);
  const [form] = Form.useForm();
  const [editForm] = Form.useForm();

  // 获取项目成员列表
  const fetchMembers = async () => {
    try {
      setLoading(true);
      const response = await projectMemberApi.getProjectMembers(projectId);
      setMembers(response.data.members || []);
    } catch (error) {
      message.error(t('common:projectMember.fetchMembersFailed'));
      console.error('Fetch project members failed:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取可邀请用户列表
  const fetchInvitableUsers = async () => {
    try {
      const response = await projectMemberApi.getInvitableUsers(projectId);
      setInvitableUsers(response.data || []);
    } catch (error) {
      message.error(t('common:projectMember.fetchInvitableUsersFailed'));
      console.error('Fetch invitable users failed:', error);
    }
  };

  // Mock get project roles list (should be fetched from API in practice)
  const fetchProjectRoles = () => {
    setProjectRoles([
      { id: 2, name: t('common:projectMember.roles.collaborator'), code: 'COLLABORATOR', description: t('common:projectMember.roles.collaboratorDesc'), permissions: [], created_at: '' },
      { id: 3, name: t('common:projectMember.roles.viewer'), code: 'VIEWER', description: t('common:projectMember.roles.viewerDesc'), permissions: [], created_at: '' }
    ]);
  };

  useEffect(() => {
    fetchMembers();
    fetchProjectRoles();
  }, [projectId]);

  // 邀请成员
  const handleInvite = async (values: { user_id: number; project_role_id: number }) => {
    try {
      await projectMemberApi.inviteMember(projectId, values);
      message.success(t('common:projectMember.inviteSuccess'));
      setInviteModalVisible(false);
      form.resetFields();
      fetchMembers();
      onMemberChange?.();
    } catch (error: any) {
      message.error(error.response?.data?.detail || t('common:projectMember.inviteFailed'));
    }
  };

  // 更新成员角色
  const handleUpdateMember = async (values: { project_role_id: number }) => {
    if (!selectedMember) return;
    
    try {
      await projectMemberApi.updateMember(projectId, selectedMember.user_id, values);
      message.success(t('common:projectMember.updateSuccess'));
      setEditModalVisible(false);
      editForm.resetFields();
      setSelectedMember(null);
      fetchMembers();
      onMemberChange?.();
    } catch (error: any) {
      message.error(error.response?.data?.detail || t('common:projectMember.updateFailed'));
    }
  };

  // 移除成员
  const handleRemoveMember = async (userId: number) => {
    try {
      await projectMemberApi.removeMember(projectId, userId);
      message.success(t('common:projectMember.removeSuccess'));
      fetchMembers();
      onMemberChange?.();
    } catch (error: any) {
      message.error(error.response?.data?.detail || t('common:projectMember.removeFailed'));
    }
  };

  // 打开邀请弹窗
  const openInviteModal = () => {
    fetchInvitableUsers();
    setInviteModalVisible(true);
  };

  // 打开编辑弹窗
  const openEditModal = (member: ProjectMember) => {
    setSelectedMember(member);
    editForm.setFieldsValue({
      project_role_id: member.project_role_id
    });
    setEditModalVisible(true);
  };

  // Get role tag color
  const getRoleTagColor = (roleName: string) => {
    if (roleName === t('common:projectMember.roles.projectOwner')) return 'red';
    if (roleName === t('common:projectMember.roles.enterpriseAdmin')) return 'orange';
    if (roleName === t('common:projectMember.roles.collaborator')) return 'blue';
    if (roleName === t('common:projectMember.roles.viewer')) return 'green';
    return 'default';
  };

  // Check if can manage members (project owner or enterprise admin)
  const canManageMembers = () => {
    if (!currentUser) return false;
    
    // Super admin can always manage
    if (currentUser.role_id === 1) return true;
    
    // Enterprise admin can manage enterprise projects
    if (currentUser.role_id === 2 && project && currentUser.org_id === project.org_id) {
      return true;
    }
    
    // Project owner can manage members
    if (project && project.owner_id === currentUser.id) {
      return true;
    }
    
    // Compatible with old permission fields
    if (currentUser.is_superuser) return true;
    
    return false;
  };

  const columns = [
    {
      title: t('common:projectMember.username'),
      dataIndex: 'username',
      key: 'username',
    },
    {
      title: t('common:projectMember.email'),
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: t('common:projectMember.role'),
      dataIndex: 'role_name',
      key: 'role_name',
      render: (roleName: string, record: ProjectMember) => (
        <Tag color={getRoleTagColor(record.role_name || '')}>
          {roleName}
        </Tag>
      ),
    },
    {
      title: t('common:projectMember.invitedBy'),
      dataIndex: 'invited_by_username',
      key: 'invited_by_username',
      render: (invitedBy: string) => invitedBy || '-',
    },
    {
      title: t('common:projectMember.joinedAt'),
      dataIndex: 'joined_at',
      key: 'joined_at',
      render: (joinedAt: string) => new Date(joinedAt).toLocaleString(),
    },
    {
      title: t('common:projectMember.status'),
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'ACTIVE' ? 'green' : 'red'}>
          {status === 'ACTIVE' ? t('common:projectMember.active') : t('common:projectMember.inactive')}
        </Tag>
      ),
    },
    {
      title: t('common:projectMember.actions'),
      key: 'action',
      render: (_: any, record: ProjectMember) => {
        // Cannot operate on project owner and enterprise admin
        if (record.role_name === t('common:projectMember.roles.projectOwner') || record.role_name === t('common:projectMember.roles.enterpriseAdmin')) {
          return <span style={{ color: '#999' }}>-</span>;
        }

        if (!canManageMembers()) {
          return <span style={{ color: '#999' }}>{t('common:projectMember.noPermission')}</span>;
        }

        return (
          <Space>
            <Tooltip title={t('common:projectMember.editRole')}>
              <Button
                type="link"
                size="small"
                icon={<EditOutlined />}
                onClick={() => openEditModal(record)}
              />
            </Tooltip>
            <Popconfirm
              title={t('common:projectMember.confirmRemove')}
              onConfirm={() => handleRemoveMember(record.user_id)}
              okText={t('common:buttons.confirm')}
              cancelText={t('common:buttons.cancel')}
            >
              <Tooltip title={t('common:projectMember.removeMember')}>
                <Button
                  type="link"
                  size="small"
                  danger
                  icon={<UserDeleteOutlined />}
                />
              </Tooltip>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  return (
    <Card>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
        <Title level={4} style={{ margin: 0 }}>{t('common:projectMember.title')}</Title>
        {canManageMembers() && (
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={openInviteModal}
          >
            {t('common:projectMember.inviteMember')}
          </Button>
        )}
      </div>

      <Spin spinning={loading}>
        <Table
          columns={columns}
          dataSource={members}
          rowKey="id"
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => t('common:projectMember.totalRecords', { total }),
          }}
        />
      </Spin>

      {/* 邀请成员弹窗 */}
      <Modal
        title={t('common:projectMember.inviteMember')}
        open={inviteModalVisible}
        onCancel={() => {
          setInviteModalVisible(false);
          form.resetFields();
        }}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleInvite}
        >
          <Form.Item
            name="user_id"
            label={t('common:projectMember.selectUser')}
            rules={[{ required: true, message: t('common:projectMember.selectUserToInvite') }]}
          >
            <Select
              placeholder={t('common:projectMember.pleaseSelectUser')}
              showSearch
              optionFilterProp="children"
            >
              {invitableUsers.map(user => (
                <Option key={user.id} value={user.id}>
                  {user.username}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="project_role_id"
            label={t('common:projectMember.projectRole')}
            rules={[{ required: true, message: t('common:projectMember.selectProjectRole') }]}
          >
            <Select placeholder={t('common:projectMember.pleaseSelectRole')}>
              {projectRoles.map(role => (
                <Option key={role.id} value={role.id}>
                  {role.name} - {role.description}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {t('common:projectMember.invite')}
              </Button>
              <Button onClick={() => {
                setInviteModalVisible(false);
                form.resetFields();
              }}>
                {t('common:buttons.cancel')}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑成员弹窗 */}
      <Modal
        title={t('common:projectMember.editMemberRole')}
        open={editModalVisible}
        onCancel={() => {
          setEditModalVisible(false);
          editForm.resetFields();
          setSelectedMember(null);
        }}
        footer={null}
      >
        <Form
          form={editForm}
          layout="vertical"
          onFinish={handleUpdateMember}
        >
          <Form.Item
            name="project_role_id"
            label={t('common:projectMember.projectRole')}
            rules={[{ required: true, message: t('common:projectMember.selectProjectRole') }]}
          >
            <Select placeholder={t('common:projectMember.pleaseSelectRole')}>
              {projectRoles.map(role => (
                <Option key={role.id} value={role.id}>
                  {role.name} - {role.description}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {t('common:projectMember.update')}
              </Button>
              <Button onClick={() => {
                setEditModalVisible(false);
                editForm.resetFields();
                setSelectedMember(null);
              }}>
                {t('common:buttons.cancel')}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  );
};

export default ProjectMemberManagement; 