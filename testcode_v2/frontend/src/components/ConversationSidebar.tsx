import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import {
  Card, List, Button, Input, Space, Typography, Tag, Dropdown, Modal, Form,
  message, Empty, Spin, Tooltip, Badge, Divider
} from 'antd';
import {
  PlusOutlined, SearchOutlined, MessageOutlined, MoreOutlined,
  EditOutlined, DeleteOutlined, ClockCircleOutlined, UserOutlined,
  ExclamationCircleOutlined, FolderOpenOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { conversationApi } from '../services/api';
import { getRelativeTime } from '../utils/dateUtils';
import './ConversationSidebar.css';

const { Text, Title } = Typography;
const { Search } = Input;

interface Conversation {
  id: string;
  title: string;
  project_id: string;
  created_at: string;
  updated_at: string;
  status: 'active' | 'archived';
  rounds_count?: number;
  last_query?: string;
  last_analysis_at?: string;
}

interface ConversationSidebarProps {
  projectId: string;
  currentConversationId?: string;
  onSelectConversation: (conversationId: string) => void;
  onNewConversation: () => void;
  collapsed?: boolean;
  onToggleCollapse?: () => void;
  refreshTrigger?: number; // 新增：刷新触发器
}

// 定义暴露给父组件的方法接口
export interface ConversationSidebarRef {
  addConversation: (conversation: Conversation) => void;
  updateConversation: (conversationId: string, updates: Partial<Conversation>) => void;
  removeConversation: (conversationId: string) => void;
}

const ConversationSidebar = forwardRef<ConversationSidebarRef, ConversationSidebarProps>(({
  projectId,
  currentConversationId,
  onSelectConversation,
  onNewConversation,
  collapsed = false,
  onToggleCollapse,
  refreshTrigger
}, ref) => {
  const { t } = useTranslation();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [filteredConversations, setFilteredConversations] = useState<Conversation[]>([]);
  const [editingConversation, setEditingConversation] = useState<Conversation | null>(null);
  const [renameForm] = Form.useForm();
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  
  // 分页状态
  const [pagination, setPagination] = useState({
    page: 1,
    page_size: 20,
    total_count: 0,
    total_pages: 0,
    has_next: false,
    has_prev: false
  });

  // 加载会话列表
  const loadConversations = async (page: number = 1, append: boolean = false) => {
    if (!projectId) return;
    
    if (append) {
      setLoadingMore(true);
    } else if (isInitialLoad) {
      setLoading(true);
    }
    
    try {
      const response = await conversationApi.getProjectConversations(projectId, {
        page,
        page_size: pagination.page_size
      });
      
      // 解析响应数据
      let conversationList: Conversation[] = [];
      let paginationInfo = {
        page: 1,
        page_size: 20,
        total_count: 0,
        total_pages: 0,
        has_next: false,
        has_prev: false
      };
      
      if (response.data) {
        if (response.data.data && response.data.data.conversations && Array.isArray(response.data.data.conversations)) {
          // 新的API响应格式：{ data: { conversations: [...], pagination: {...} } }
          conversationList = response.data.data.conversations;
          if (response.data.data.pagination) {
            paginationInfo = response.data.data.pagination;
          }
        } else if (response.data.conversations && Array.isArray(response.data.conversations)) {
          // 直接的conversations格式：{ conversations: [...], pagination: {...} }
          conversationList = response.data.conversations;
          if (response.data.pagination) {
            paginationInfo = response.data.pagination;
          }
        } else if (Array.isArray(response.data)) {
          // 兼容旧版本API响应格式：直接是数组
          conversationList = response.data;
        } else if (response.data.data && Array.isArray(response.data.data)) {
          // 兼容格式：{ data: [...] }
          conversationList = response.data.data;
        }
      }
      
      console.log('解析后的会话列表:', conversationList); // 调试日志
      console.log('分页信息:', paginationInfo); // 调试日志
      
      if (append) {
        // 追加模式：将新数据添加到现有列表
        setConversations(prev => [...prev, ...conversationList]);
      } else {
        // 替换模式：替换整个列表
        setConversations(conversationList);
      }
      
      setPagination(paginationInfo);
      
      // 如果没有搜索，更新过滤后的列表
      if (!searchText.trim()) {
        if (append) {
          setFilteredConversations(prev => [...prev, ...conversationList]);
        } else {
          setFilteredConversations(conversationList);
        }
      }
      
    } catch (error: any) {
      console.error('加载会话列表失败:', error);
      
      // 如果是网络错误或服务器未启动，使用模拟数据进行测试
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage.includes('Network Error') || errorMessage.includes('ERR_CONNECTION_REFUSED')) {
        console.log('使用模拟数据进行测试');
        const mockConversations = [
          {
            id: 'conv_1',
            title: t('common:conversation.untitled') + ' 1',
            project_id: projectId,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            status: 'active' as const,
            rounds_count: 3,
            last_query: '这是一个测试查询'
          },
          {
            id: 'conv_2', 
            title: t('common:conversation.untitled') + ' 2',
            project_id: projectId,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            status: 'active' as const,
            rounds_count: 1,
            last_query: '另一个测试查询'
          }
        ];
        
        if (!append) {
          setConversations(mockConversations);
          setFilteredConversations(mockConversations);
        }
        message.warning(t('common:message.error.networkError'));
      } else {
        message.error(t('common:message.error.fetchFailed'));
        if (!append) {
          // 确保在错误情况下也设置为空数组
          setConversations([]);
          setFilteredConversations([]);
        }
      }
    } finally {
      if (append) {
        setLoadingMore(false);
      } else {
        setLoading(false);
      }
      if (isInitialLoad) {
        setIsInitialLoad(false);
      }
    }
  };

  // 加载更多会话
  const loadMoreConversations = async () => {
    if (loadingMore || !pagination.has_next) return;
    
    console.log('加载更多会话，当前页:', pagination.page, '下一页:', pagination.page + 1);
    await loadConversations(pagination.page + 1, true);
  };

  // 处理滚动事件，实现上划加载更多
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    
    // 当滚动到底部附近时（距离底部50px以内），加载更多
    if (scrollHeight - scrollTop - clientHeight < 50 && pagination.has_next && !loadingMore) {
      loadMoreConversations();
    }
  };

  // 新增：本地添加新会话到列表
  const addConversationToList = (newConversation: Conversation) => {
    console.log('本地添加新会话到列表:', newConversation);
    
    // 将新会话添加到列表顶部
    const updatedConversations = [newConversation, ...conversations];
    setConversations(updatedConversations);
    
    // 更新分页信息
    setPagination(prev => ({
      ...prev,
      total_count: prev.total_count + 1
    }));
    
    // 如果当前没有搜索，也更新过滤后的列表
    if (!searchText.trim()) {
      setFilteredConversations([newConversation, ...filteredConversations]);
    } else {
      // 如果有搜索，重新过滤
      handleSearch(searchText);
    }
  };

  // 新增：本地更新会话信息
  const updateConversationInList = (conversationId: string, updates: Partial<Conversation>) => {
    console.log('本地更新会话信息:', conversationId, updates);
    
    const updatedConversations = conversations.map(conv => 
      conv.id === conversationId ? { ...conv, ...updates } : conv
    );
    setConversations(updatedConversations);
    
    // 重新过滤
    const filtered = updatedConversations.filter(conv =>
      conv.title.toLowerCase().includes(searchText.toLowerCase()) ||
      (conv.last_query && conv.last_query.toLowerCase().includes(searchText.toLowerCase()))
    );
    setFilteredConversations(filtered);
  };

  // 新增：本地从列表中移除会话
  const removeConversationFromList = (conversationId: string) => {
    console.log('本地从列表中移除会话:', conversationId);
    
    const updatedConversations = conversations.filter(conv => conv.id !== conversationId);
    setConversations(updatedConversations);
    
    // 更新分页信息
    setPagination(prev => ({
      ...prev,
      total_count: Math.max(0, prev.total_count - 1)
    }));
    
    setFilteredConversations(updatedConversations.filter(conv =>
      conv.title.toLowerCase().includes(searchText.toLowerCase()) ||
      (conv.last_query && conv.last_query.toLowerCase().includes(searchText.toLowerCase()))
    ));
  };

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    addConversation: addConversationToList,
    updateConversation: updateConversationInList,
    removeConversation: removeConversationFromList
  }), [conversations, searchText]);

  // 搜索过滤
  const handleSearch = (value: string) => {
    setSearchText(value);
    
    // 确保conversations是数组
    const conversationList = Array.isArray(conversations) ? conversations : [];
    
    if (!value.trim()) {
      setFilteredConversations(conversationList);
    } else {
      const filtered = conversationList.filter(conv =>
        conv.title.toLowerCase().includes(value.toLowerCase()) ||
        (conv.last_query && conv.last_query.toLowerCase().includes(value.toLowerCase()))
      );
      setFilteredConversations(filtered);
    }
  };

  // 重命名会话
  const handleRename = async (conversation: Conversation) => {
    setEditingConversation(conversation);
    renameForm.setFieldsValue({ title: conversation.title });
  };

  // 确认重命名
  const confirmRename = async () => {
    try {
      const values = await renameForm.validateFields();
      await conversationApi.updateConversation(editingConversation!.id, {
        title: values.title
      });
      
      message.success(t('common:conversation.renameSuccess'));
      setEditingConversation(null);
      
      // 使用本地更新而不是重新加载
      updateConversationInList(editingConversation!.id, { 
        title: values.title,
        updated_at: new Date().toISOString()
      });
    } catch (error) {
      console.error('重命名失败:', error);
      message.error(t('common:conversation.renameFailed'));
    }
  };

  // 删除会话
  const handleDelete = (conversation: Conversation) => {
    Modal.confirm({
      title: '确认删除会话',
      icon: <ExclamationCircleOutlined />,
      content: `确定要删除会话"${conversation.title}"吗？此操作不可恢复。`,
      okText: '确认删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await conversationApi.deleteConversation(conversation.id);
          message.success(t('common:conversation.deleteSuccess'));
          
          // 使用本地更新而不是重新加载
          removeConversationFromList(conversation.id);
          
          // 如果删除的是当前会话，则创建新会话
          if (conversation.id === currentConversationId) {
            onNewConversation();
          }
        } catch (error) {
          console.error('删除会话失败:', error);
          message.error(t('common:conversation.deleteFailed'));
        }
      }
    });
  };

  // 格式化时间
  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    } else if (diffDays === 1) {
      return '昨天';
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else {
      return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });
    }
  };

  // 获取会话菜单
  const getConversationMenu = (conversation: Conversation) => ({
    items: [
      {
        key: 'rename',
        label: '重命名',
        icon: <EditOutlined />,
        onClick: () => handleRename(conversation)
      },
      {
        key: 'delete',
        label: '删除',
        icon: <DeleteOutlined />,
        danger: true,
        onClick: () => handleDelete(conversation)
      }
    ]
  });

  useEffect(() => {
    // 重置分页状态并加载第一页
    setPagination(prev => ({
      ...prev,
      page: 1
    }));
    setIsInitialLoad(true);
    loadConversations(1, false);
  }, [projectId, refreshTrigger]); // 添加 refreshTrigger 依赖

  useEffect(() => {
    // 当conversations变化时，重新应用搜索过滤
    const conversationList = Array.isArray(conversations) ? conversations : [];
    
    if (!searchText.trim()) {
      setFilteredConversations(conversationList);
    } else {
      const filtered = conversationList.filter(conv =>
        conv.title.toLowerCase().includes(searchText.toLowerCase()) ||
        (conv.last_query && conv.last_query.toLowerCase().includes(searchText.toLowerCase()))
      );
      setFilteredConversations(filtered);
    }
  }, [conversations, searchText]); // 添加searchText依赖，避免调用handleSearch

  if (collapsed) {
    return (
      <Card 
        className="conversation-sidebar collapsed"
        size="small" 
        bodyStyle={{ padding: '12px 8px', textAlign: 'center' }}
      >
        <div className="collapsed-sidebar">
          <Space direction="vertical" size="large">
            <Tooltip title={t('common:conversation.title')} placement="right">
              <Button 
                type="text" 
                icon={<FolderOpenOutlined />}
                onClick={onToggleCollapse}
              />
            </Tooltip>
            
            <Tooltip title={t('common:conversation.new')} placement="right">
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={onNewConversation}
              />
            </Tooltip>
            
            <Divider style={{ margin: '8px 0' }} />
            
            <div className="conversation-count">
              <Badge count={pagination.total_count} size="small">
                <MessageOutlined />
              </Badge>
            </div>
          </Space>
        </div>
      </Card>
    );
  }

  return (
    <Card 
      className="conversation-sidebar expanded"
      size="small" 
      title={
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Space>
            <MessageOutlined />
            <span>会话列表</span>
            <Badge count={pagination.total_count} size="small" />
          </Space>
          {onToggleCollapse && (
            <Button 
              type="text" 
              size="small"
              onClick={onToggleCollapse}
              style={{ padding: '4px' }}
            >
              ←
            </Button>
          )}
        </div>
      }
      extra={
        <Button 
          type="primary" 
          icon={<PlusOutlined />}
          size="small"
          onClick={onNewConversation}
        >
          新建
        </Button>
      }
    >
      <Space direction="vertical" style={{ width: '100%', height: '100%' }}>
        {/* 搜索框 */}
        <div className="conversation-search">
          <Search
            placeholder={t('common:conversation.search')}
            allowClear
            value={searchText}
            onChange={(e) => handleSearch(e.target.value)}
          />
        </div>

        {/* 会话列表 */}
        <div 
          className="conversation-list"
          onScroll={handleScroll}
          style={{ 
            maxHeight: 'calc(100vh - 200px)', 
            overflowY: 'auto',
            paddingRight: '4px'
          }}
        >
          {loading && isInitialLoad ? (
            <div className="conversation-loading">
              <Spin size="large" />
            </div>
          ) : !Array.isArray(filteredConversations) || filteredConversations.length === 0 ? (
            <div className="conversation-empty">
              <Empty 
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description={
                  searchText ? '没有找到匹配的会话' : '暂无会话记录'
                }
              >
                {!searchText && (
                  <Button 
                    type="primary" 
                    icon={<PlusOutlined />}
                    onClick={onNewConversation}
                  >
                    创建第一个会话
                  </Button>
                )}
              </Empty>
            </div>
          ) : (
            <>
              {(Array.isArray(filteredConversations) ? filteredConversations : []).map((conversation) => (
                <div
                  key={conversation.id}
                  className={`conversation-item ${conversation.id === currentConversationId ? 'active' : ''}`}
                  onClick={() => onSelectConversation(conversation.id)}
                >
                  <div className="conversation-item-header">
                    <Text 
                      className={`conversation-item-title ${conversation.id === currentConversationId ? 'active' : ''}`}
                      ellipsis={{ tooltip: conversation.title }}
                    >
                      {conversation.title}
                    </Text>
                    
                    <div className="conversation-item-menu">
                      <Dropdown 
                        menu={getConversationMenu(conversation)}
                        trigger={['click']}
                        placement="bottomRight"
                      >
                        <Button 
                          type="text" 
                          size="small"
                          icon={<MoreOutlined />}
                          onClick={(e) => e.stopPropagation()}
                          style={{ padding: '2px 4px' }}
                        />
                      </Dropdown>
                    </div>
                  </div>
                  
                  {conversation.last_query && (
                    <div className="conversation-item-query">
                      {conversation.last_query}
                    </div>
                  )}
                  
                  <div className="conversation-item-footer">
                    <div className="conversation-item-time">
                      <ClockCircleOutlined />
                      {formatTime(conversation.updated_at)}
                    </div>
                    
                    <div className="conversation-item-tags">
                      {conversation.rounds_count && (
                        <Tag color="blue">
                          {conversation.rounds_count}轮
                        </Tag>
                      )}
                      {conversation.id === currentConversationId && (
                        <Tag color="green">当前</Tag>
                      )}
                    </div>
                  </div>
                </div>
              ))}
              
              {/* 加载更多指示器 */}
              {loadingMore && (
                <div style={{ textAlign: 'center', padding: '16px' }}>
                  <Spin size="small" />
                  <div style={{ marginTop: '8px', color: '#666', fontSize: '12px' }}>
                    加载更多会话...
                  </div>
                </div>
              )}
              
              {/* 分页信息 */}
              {!searchText && pagination.total_count > 0 && (
                <div style={{ 
                  textAlign: 'center', 
                  padding: '8px', 
                  color: '#999', 
                  fontSize: '12px',
                  borderTop: '1px solid #f0f0f0',
                  marginTop: '8px'
                }}>
                  {pagination.has_next ? (
                    <span>已显示 {filteredConversations.length} / {pagination.total_count} 个会话，上划加载更多</span>
                  ) : (
                    <span>已显示全部 {pagination.total_count} 个会话</span>
                  )}
                </div>
              )}
            </>
          )}
        </div>
      </Space>

      {/* 重命名对话框 */}
      <Modal
        title={t('common:conversation.renameTitle')}
        open={!!editingConversation}
        onOk={confirmRename}
        onCancel={() => setEditingConversation(null)}
        okText="确认"
        cancelText="取消"
        destroyOnClose
      >
        <Form form={renameForm} layout="vertical">
          <Form.Item
            name="title"
            label={t('common:conversation.conversationTitle')}
            rules={[
              { required: true, message: '请输入会话标题' },
              { max: 100, message: '标题不能超过100个字符' }
            ]}
          >
            <Input placeholder={t('common:conversation.enterNewTitle')} />
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  );
});

export default ConversationSidebar; 