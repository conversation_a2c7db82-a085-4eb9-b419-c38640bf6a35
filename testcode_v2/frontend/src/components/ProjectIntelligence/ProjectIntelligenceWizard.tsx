import React from 'react';
import { <PERSON>, Card, <PERSON><PERSON>, Spin, Alert } from 'antd';
import { useTranslation } from 'react-i18next';
import { useProjectIntelligence } from '../../hooks/useProjectIntelligence';
import ProjectReadinessStep from './BusinessUnderstandingStep';
import IntelligentQuestionStep from './IntelligentQuestionStep';
import ScenarioPredictionStep from './ScenarioPredictionStep';
import CompletionSummaryStep from './CompletionSummaryStep';

interface Props {
  projectId: string;
  onComplete: () => void;
  onCancel?: () => void;
}

const ProjectIntelligenceWizard: React.FC<Props> = ({ projectId, onComplete, onCancel }) => {
  const { t } = useTranslation();
  const {
    session,
    startIntelligenceCheck,
    submitAnswers,
    completeIntelligence,
    clearError
  } = useProjectIntelligence();

  React.useEffect(() => {
    startIntelligenceCheck(projectId);
  }, [projectId, startIntelligenceCheck]);

  const getCurrentStep = () => {
    switch (session.status) {
      case 'analyzing':
      case 'questioning':
        return 0;
      case 'predicting':
      case 'completed':
        return 1;
      default:
        return 0;
    }
  };

  const handleComplete = async (scenarioConfirmations?: Record<string, any>) => {
    const summary = await completeIntelligence(scenarioConfirmations);
    if (summary) {
      onComplete();
    }
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
  };

  return (
    <div className="project-intelligence-wizard" style={{ maxWidth: 1000, margin: '0 auto' }}>
      <Card 
        title={t('project:intelligence.title')}
        extra={
          onCancel && (
            <Button onClick={handleCancel} disabled={session.loading}>
              {t('common:buttons.skip')}
            </Button>
          )
        }
      >
        <Steps current={getCurrentStep()} style={{ marginBottom: 24 }}>
          <Steps.Step title={t('project:intelligence.steps.readinessAssessment')} description={t('project:intelligence.steps.readinessAssessmentDesc')} />
          <Steps.Step title={t('project:intelligence.steps.scenarioValidation')} description={t('project:intelligence.steps.scenarioValidationDesc')} />
        </Steps>

        {session.error && (
          <Alert
            message={t('common:messages.error')}
            description={session.error}
            type="error"
            closable
            onClose={clearError}
            style={{ marginBottom: 16 }}
          />
        )}

        <div style={{ minHeight: 400 }}>
          <Spin spinning={session.loading}>
            {session.status === 'analyzing' && (
              <div style={{ textAlign: 'center', padding: '60px 20px' }}>
                <Spin size="large" />
                <div style={{ marginTop: 16, fontSize: 16 }}>
                  {t('project:intelligence.analyzing.evaluating')}
                </div>
                <div style={{ marginTop: 8, color: '#666' }}>
                  {t('project:intelligence.analyzing.analyzingData')}
                </div>
                <div style={{ marginTop: 12, color: '#999', fontSize: 12 }}>
                  {t('project:intelligence.analyzing.pleaseWait')}
                </div>
              </div>
            )}

            {session.status === 'questioning' && session.readinessEvaluation && (
              <div>
                <ProjectReadinessStep 
                  evaluation={session.readinessEvaluation}
                />
                <IntelligentQuestionStep
                  questions={session.questions}
                  onSubmit={submitAnswers}
                  loading={session.loading}
                />
              </div>
            )}

            {session.status === 'predicting' && (
              <ScenarioPredictionStep
                predictions={session.scenarioPredictions}
                onComplete={handleComplete}
                loading={session.loading}
              />
            )}

            {session.status === 'completed' && (
              <CompletionSummaryStep
                summary={session}
                onFinish={onComplete}
              />
            )}
          </Spin>
        </div>
      </Card>
    </div>
  );
};

export default ProjectIntelligenceWizard; 