import React from 'react';
import { Card, Row, Col, Tag, Alert, List, Divider } from 'antd';
import { CheckCircleOutlined, ExclamationCircleOutlined, InfoCircleOutlined, BulbOutlined, ShopOutlined, BarChartOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

interface Props {
  evaluation: {
    ready?: 'yes' | 'no';
    reason_if_no?: string;
    project_understanding?: {
    business_domain?: string;
    business_type?: string;
    key_entities?: string[];
      core_metrics?: string[];
      data_relationships?: string;
    };
    can_answer_examples?: string[];
    boundaries?: string[];
    missing_info?: string[];
  };
}

const ProjectReadinessStep: React.FC<Props> = ({ evaluation }) => {
  const { t } = useTranslation();
  const isReady = evaluation.ready === 'yes';
  const understanding = evaluation.project_understanding || {};

  return (
    <div style={{ marginBottom: 24 }}>
      <Card title={`🔍 ${t('project:intelligence.steps.readinessAssessment')}`} size="small">
        {/* 项目理解分析 */}
        <Card type="inner" title={`📊 ${t('project:intelligence.businessUnderstanding')}`} size="small" style={{ marginBottom: 16 }}>
          {/* 第一行：业务领域和业务类型 */}
          <Row gutter={[24, 16]} style={{ marginBottom: 16 }}>
          <Col xs={24} md={12}>
              <div>
                <strong style={{ color: '#1890ff', marginRight: 8 }}>
                  <ShopOutlined style={{ marginRight: 4 }} />
                  {t('project:intelligence.businessDomain')}：
                </strong>
                <Tag color="blue">
                  {understanding.business_domain || t('common:labels.unknown')}
                </Tag>
              </div>
            </Col>
            <Col xs={24} md={12}>
              <div>
                <strong style={{ color: '#52c41a', marginRight: 8 }}>
                  <BarChartOutlined style={{ marginRight: 4 }} />
                  {t('project:intelligence.businessType')}：
                </strong>
                <Tag color="green">
                  {understanding.business_type || t('common:labels.unknown')}
                </Tag>
              </div>
          </Col>
          </Row>

          {/* 第二行：关键实体 */}
          <Row style={{ marginBottom: 16 }}>
            <Col span={24}>
              <div>
                <strong style={{ color: '#722ed1', marginRight: 8 }}>{t('project:intelligence.keyEntities')}：</strong>
                <div style={{ marginTop: 8 }}>
                  {understanding.key_entities && understanding.key_entities.length > 0 ? (
                    understanding.key_entities.map((entity, index) => (
                      <Tag key={index} color="purple" style={{ marginBottom: 4, marginRight: 4 }}>
                        {entity}
                      </Tag>
                    ))
                  ) : (
                    <span style={{ color: '#999' }}>{t('project:intelligence.noIdentified')}</span>
                  )}
                </div>
              </div>
            </Col>
          </Row>

          {/* 第三行：核心指标 */}
          <Row style={{ marginBottom: 16 }}>
            <Col span={24}>
              <div>
                <strong style={{ color: '#fa8c16', marginRight: 8 }}>{t('project:intelligence.coreMetrics')}：</strong>
                <div style={{ marginTop: 8 }}>
                  {understanding.core_metrics && understanding.core_metrics.length > 0 ? (
                    understanding.core_metrics.map((metric, index) => (
                      <Tag key={index} color="orange" style={{ marginBottom: 4, marginRight: 4 }}>
                        {metric}
                      </Tag>
                    ))
                  ) : (
                    <span style={{ color: '#999' }}>{t('project:intelligence.noIdentified')}</span>
                  )}
                </div>
              </div>
            </Col>
          </Row>

          {/* 第四行：数据关联 */}
          <Row>
            <Col span={24}>
              <div>
                <strong style={{ color: '#13c2c2', marginRight: 8 }}>{t('project:intelligence.dataRelationships')}：</strong>
                <div style={{ marginTop: 8, color: '#666', lineHeight: '1.6' }}>
                  {understanding.data_relationships || t('project:intelligence.needsFurtherAnalysis')}
                </div>
              </div>
          </Col>
        </Row>
        </Card>

        {/* 可以回答的问题示例 - 始终显示 */}
        {evaluation.can_answer_examples && evaluation.can_answer_examples.length > 0 && (
          <Card type="inner" title={`💡 ${t('project:intelligence.exampleQuestions')}`} size="small" style={{ marginBottom: 16 }}>
            <List
              size="small"
              dataSource={evaluation.can_answer_examples}
              renderItem={(example, index) => (
                <List.Item>
                  <Tag icon={<BulbOutlined />} color="success" style={{ marginRight: 8 }}>
                    {index + 1}
                  </Tag>
                  {example}
                </List.Item>
              )}
            />
            </Card>
        )}

        {/* 分析边界说明 - 始终显示 */}
        {evaluation.boundaries && evaluation.boundaries.length > 0 && (
          <Card type="inner" title={`📋 ${t('project:intelligence.analysisBoundaries')}`} size="small" style={{ marginBottom: 16 }}>
            <List
              size="small"
              dataSource={evaluation.boundaries}
              renderItem={(boundary, index) => (
                <List.Item>
                  <Tag icon={<InfoCircleOutlined />} color="blue" style={{ marginRight: 8 }}>
                    {index + 1}
                  </Tag>
                  {boundary}
                </List.Item>
              )}
            />
            </Card>
        )}

        {/* 需要补充信息时显示的内容 */}
        {!isReady && evaluation.missing_info && evaluation.missing_info.length > 0 && (
          <Card type="inner" title={`❓ ${t('project:intelligence.additionalInfoNeeded')}`} size="small">
            <List
              size="small"
              dataSource={evaluation.missing_info}
              renderItem={(question, index) => (
                <List.Item>
                  <Tag icon={<ExclamationCircleOutlined />} color="orange" style={{ marginRight: 8 }}>
                    Q{index + 1}
                  </Tag>
                  {question}
                </List.Item>
              )}
            />
            <Alert
              message={`💡 ${t('project:intelligence.tip')}`}
              description={t('project:intelligence.nextStepDescription')}
              type="info"
              showIcon
              style={{ marginTop: 12 }}
            />
            </Card>
        )}
      </Card>
    </div>
  );
};

export default ProjectReadinessStep; 