import React, { useState } from 'react';
import { Card, Form, Input, Radio, Checkbox, InputNumber, Button, Space, Tag, Divider } from 'antd';
import { QuestionCircleOutlined, ExclamationCircleOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

interface Question {
  id: string;
  question: string;
  priority: 'critical' | 'important' | 'optional';
  type: 'single_choice' | 'multiple_choice' | 'text' | 'numeric';
  options?: string[];
}

interface Props {
  questions: Question[];
  onSubmit: (answers: Record<string, any>) => void;
  loading?: boolean;
}

const IntelligentQuestionStep: React.FC<Props> = ({ questions, onSubmit, loading }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [answers, setAnswers] = useState<Record<string, any>>({});

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'critical':
        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      case 'important':
        return <QuestionCircleOutlined style={{ color: '#faad14' }} />;
      case 'optional':
        return <InfoCircleOutlined style={{ color: '#1890ff' }} />;
      default:
        return <QuestionCircleOutlined />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical':
        return 'red';
      case 'important':
        return 'orange';
      case 'optional':
        return 'blue';
      default:
        return 'default';
    }
  };

  const getPriorityText = (priority: string) => {
    switch (priority) {
      case 'critical':
        return t('project:intelligence.priorityRequired');
      case 'important':
        return t('project:intelligence.priorityImportant');
      case 'optional':
        return t('project:intelligence.priorityOptional');
      default:
        return t('project:intelligence.priorityNormal');
    }
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      await onSubmit(values);
    } catch (error) {
      console.error('Form validation failed:', error);
    }
  };

  const handleFormChange = (changedValues: any, allValues: any) => {
    setAnswers(allValues);
  };

  const renderQuestionField = (question: Question) => {
    const rules = question.priority === 'critical' ? [{ required: true, message: t('common:form.criticalQuestionRequired') }] : [];

    switch (question.type) {
      case 'single_choice':
        return (
          <Radio.Group>
            {question.options?.map((option, index) => (
              <Radio key={index} value={option} style={{ display: 'block', marginBottom: 8 }}>
                {option}
              </Radio>
            ))}
          </Radio.Group>
        );

      case 'multiple_choice':
        return (
          <Checkbox.Group>
            {question.options?.map((option, index) => (
              <Checkbox key={index} value={option} style={{ display: 'block', marginBottom: 8 }}>
                {option}
              </Checkbox>
            ))}
          </Checkbox.Group>
        );

      case 'numeric':
        return <InputNumber style={{ width: '100%' }} placeholder={t('common:form.pleaseInputNumber', { field: '' })} />;

      case 'text':
      default:
        return (
          <Input.TextArea 
            rows={3} 
            placeholder={t('common:form.pleaseInputDescription')} 
            showCount
            maxLength={500}
          />
        );
    }
  };

  if (!questions || questions.length === 0) {
    if (loading) {
    return (
      <Card title={`🤔 ${t('project:intelligence.generatingQuestions')}`} loading={true}>
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          {t('project:intelligence.generatingPersonalizedQuestions')}
        </div>
      </Card>
    );
    } else {
      return (
        <Card title={`✅ ${t('project:intelligence.projectInfoComplete')}`}>
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <div style={{ fontSize: 16, marginBottom: 16 }}>
              {t('project:intelligence.excellentProjectInfo')}
            </div>
            <div style={{ color: '#666', marginBottom: 24 }}>
              {t('project:intelligence.canProceedToScenarioValidation')}
            </div>
            
            <Button 
              type="primary" 
              size="large"
              onClick={() => onSubmit({})}
              loading={loading}
              style={{ minWidth: 120 }}
            >
              {t('common:intelligence.enterScenarioValidation')}
            </Button>
          </div>
          
          <div style={{ marginTop: 24, padding: 12, backgroundColor: '#f6f8fa', borderRadius: 6 }}>
            <div style={{ fontSize: 12, color: '#666' }}>
              💡 <strong>{t('common:intelligence.explanation')}:</strong>
              {t('project:intelligence.systemAnalysisComplete')}
            </div>
          </div>
        </Card>
      );
    }
  }

  return (
    <Card 
      title={`💬 ${t('common:intelligence.supplementInfo')}`}
      extra={
        <Tag color="blue">
          {t('common:intelligence.criticalCount', { count: questions.filter(q => q.priority === 'critical').length })}，
          {t('common:intelligence.importantCount', { count: questions.filter(q => q.priority === 'important').length })}，
          {t('common:intelligence.optionalCount', { count: questions.filter(q => q.priority === 'optional').length })}
        </Tag>
      }
    >
      <Form
        form={form}
        layout="vertical"
        onValuesChange={handleFormChange}
      >
        {questions.map((question, index) => (
          <div key={question.id}>
            <Form.Item
              name={question.id}
              label={
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  {getPriorityIcon(question.priority)}
                  <span>{question.question}</span>
                  <Tag color={getPriorityColor(question.priority)}>
                    {getPriorityText(question.priority)}
                  </Tag>
                </div>
              }
              rules={question.priority === 'critical' ? [{ required: true, message: t('common:form.criticalQuestionRequired') }] : []}
            >
              {renderQuestionField(question)}
            </Form.Item>
            
            {index < questions.length - 1 && <Divider />}
          </div>
        ))}

        <div style={{ textAlign: 'center', marginTop: 24 }}>
          <Button 
            type="primary" 
            size="large"
            onClick={handleSubmit}
            loading={loading}
            style={{ minWidth: 120 }}
          >
            进入场景验证
          </Button>
        </div>
      </Form>

      <div style={{ marginTop: 16, padding: 12, backgroundColor: '#f6f8fa', borderRadius: 6 }}>
        <div style={{ fontSize: 12, color: '#666' }}>
          💡 <strong>{t('project:intelligence.tip')}:</strong>
          <ul style={{ margin: '4px 0', paddingLeft: 16 }}>
            <li>{t('project:intelligence.redQuestionDescription')}</li>
            <li>{t('project:intelligence.orangeQuestionDescription')}</li>
            <li>{t('project:intelligence.blueQuestionDescription')}</li>
          </ul>
        </div>
      </div>
    </Card>
  );
};

export default IntelligentQuestionStep; 