import React, { useState, useEffect } from 'react';
import { <PERSON>, Button, List, Tag, Progress, Space, Radio, Input, Divider, Spin, Alert, Modal } from 'antd';
import { CheckCircleOutlined, EditOutlined, QuestionCircleOutlined, LoadingOutlined, BulbOutlined, DatabaseOutlined, CheckOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

interface Scenario {
  id: string;
  title: string;
  description: string;
  predicted_question: string;
  understanding_points: string[];
  solution_approach: string;
  confidence_score: number;
  agent_analysis?: {
    success: boolean;
    reasoning?: string;
    planning_steps?: any[];
    error?: string;
    fallback_used?: boolean;
    token_usage?: any;
    analysis_time?: string;
  };
}

interface Props {
  predictions: Scenario[];
  onComplete: (confirmations?: Record<string, any>) => void;
  loading?: boolean;
}

const ScenarioPredictionStep: React.FC<Props> = ({ predictions, onComplete, loading }) => {
  const { t } = useTranslation();
  const [confirmations, setConfirmations] = useState<Record<string, 'confirmed' | 'needs_revision' | 'rejected'>>({});
  const [feedback, setFeedback] = useState<Record<string, string>>({});
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingStage, setProcessingStage] = useState(0);
  const [processingText, setProcessingText] = useState('');

  // Processing stages configuration
  const processingStages = [
    {
      icon: <BulbOutlined style={{ fontSize: 20, color: '#1890ff' }} />,
      title: t('project:intelligence.processingStages.analyzing'),
      description: t('project:intelligence.processingStages.analyzingDesc'),
      duration: 2000
    },
    {
      icon: <EditOutlined style={{ fontSize: 20, color: '#52c41a' }} />,
      title: t('project:intelligence.processingStages.converting'), 
      description: t('project:intelligence.processingStages.convertingDesc'),
      duration: 3000
    },
    {
      icon: <DatabaseOutlined style={{ fontSize: 20, color: '#722ed1' }} />,
      title: t('project:intelligence.processingStages.saving'),
      description: t('project:intelligence.processingStages.savingDesc'),
      duration: 1500
    },
    {
      icon: <CheckOutlined style={{ fontSize: 20, color: '#52c41a' }} />,
      title: t('project:intelligence.processingStages.completed'),
      description: t('project:intelligence.processingStages.completedDesc'),
      duration: 1000
    }
  ];

  // 处理阶段切换效果
  useEffect(() => {
    if (!isProcessing) return;

    const currentStage = processingStages[processingStage];
    if (!currentStage) return;

    setProcessingText(currentStage.description);

    const timer = setTimeout(() => {
      if (processingStage < processingStages.length - 1) {
        setProcessingStage(prev => prev + 1);
      }
    }, currentStage.duration);

    return () => clearTimeout(timer);
  }, [isProcessing, processingStage]);

  const handleConfirmationChange = (scenarioId: string, value: 'confirmed' | 'needs_revision' | 'rejected') => {
    setConfirmations(prev => ({
      ...prev,
      [scenarioId]: value
    }));
  };

  const handleFeedbackChange = (scenarioId: string, value: string) => {
    setFeedback(prev => ({
      ...prev,
      [scenarioId]: value
    }));
  };

  const getConfidenceColor = (score: number) => {
    if (score >= 0.8) return '#52c41a';
    if (score >= 0.6) return '#faad14';
    return '#ff4d4f';
  };

  const getConfirmationColor = (confirmation: string) => {
    switch (confirmation) {
      case 'confirmed':
        return 'green';
      case 'needs_revision':
        return 'orange';
      case 'rejected':
        return 'red';
      default:
        return 'default';
    }
  };

  const getConfirmationText = (confirmation: string) => {
    switch (confirmation) {
      case 'confirmed':
        return t('project:intelligence.confirmationCorrect');
      case 'needs_revision':
        return t('project:intelligence.confirmationNeedsRevision');
      case 'rejected':
        return t('project:intelligence.confirmationRejected');
      default:
        return t('project:intelligence.confirmationPending');
    }
  };

  // 检查每个场景是否都已完成
  const isScenarioComplete = (scenario: Scenario) => {
    const confirmation = confirmations[scenario.id];
    if (!confirmation) return false; // 未选择
    if (confirmation === 'confirmed') return true; // 选择理解正确，完成
    // 选择了需要修正或理解错误，必须填写反馈内容
    return feedback[scenario.id] && feedback[scenario.id].trim().length > 0;
  };
  
  // 检查是否所有场景都已完成
  const allScenariosComplete = predictions.length > 0 && predictions.every(isScenarioComplete);
  // 按钮是否可点击
  const canComplete = allScenariosComplete && !isProcessing;
  
  // 获取未完成的场景数量
  const incompleteCount = predictions.filter(p => !isScenarioComplete(p)).length;

  // 检查是否有有价值的反馈信息
  const hasValuableFeedback = () => {
    // 检查是否有需要调整或理解错误的场景，且有有效反馈
    for (const scenarioId of Object.keys(confirmations)) {
      const status = confirmations[scenarioId];
      const feedbackText = feedback[scenarioId];
      
      if (status === 'needs_revision' || status === 'rejected') {
        if (feedbackText && feedbackText.trim().length > 3) {
          return true;
        }
      }
    }
    return false;
  };

  const handleComplete = async () => {
    setIsProcessing(true);
    setProcessingStage(0);
    
    try {
      // 构建场景确认数据
      const confirmationData: Record<string, any> = {};
      Object.keys(confirmations).forEach(scenarioId => {
        const status = confirmations[scenarioId];
        const scenarioFeedback = feedback[scenarioId];
        
        confirmationData[scenarioId] = {
          status: status,
          feedback: scenarioFeedback || ''
        };
      });
      
      await onComplete(confirmationData);
    } finally {
      setIsProcessing(false);
      setProcessingStage(0);
    }
  };

  if (!predictions || predictions.length === 0) {
    return (
      <Card title={`🔮 ${t('project:intelligence.generatingScenarios')}`} loading={true}>
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          {t('project:intelligence.generatingScenariosDesc')}
        </div>
      </Card>
    );
  }

  return (
    <>
      <Card title={`🔮 ${t('project:intelligence.scenarioTitle')}`}>
        <div style={{ marginBottom: 16 }}>
          <Tag color="blue">{t('project:intelligence.pleaseConfirmUnderstanding')}</Tag>
        </div>

        <List
          itemLayout="vertical"
          dataSource={predictions}
          renderItem={(scenario, index) => (
            <List.Item key={scenario.id}>
              <Card 
                type="inner" 
                title={
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <span>{t('project:intelligence.scenarioNumber', { number: index + 1 })}: {scenario.title}</span>
                    <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                      <span style={{ fontSize: 12, color: '#666' }}>{t('project:intelligence.confidence')}</span>
                      <Progress 
                        type="circle" 
                        size={40}
                        percent={Math.round(scenario.confidence_score * 100)}
                        strokeColor={getConfidenceColor(scenario.confidence_score)}
                        format={percent => `${percent}%`}
                      />
                    </div>
                  </div>
                }
              >
                <div style={{ marginBottom: 16 }}>
                  <div style={{ marginBottom: 8 }}>
                    <strong>{t('project:intelligence.predictedQuestion')}:</strong>
                    <div style={{ 
                      marginTop: 4, 
                      padding: 8, 
                      backgroundColor: '#f6f8fa', 
                      borderRadius: 4,
                      fontStyle: 'italic'
                    }}>
                      "{scenario.predicted_question}"
                    </div>
                  </div>

                  <div style={{ marginBottom: 16 }}>
                    <div style={{ display: 'flex', alignItems: 'center', marginBottom: 12 }}>
                      <strong style={{ fontSize: 16 }}>{t('project:intelligence.myUnderstandingAnalysis')}</strong>
                      {scenario.agent_analysis?.success && (
                        <Tag color="green" style={{ marginLeft: 8, fontSize: 11 }}>
                          {t('common:intelligence.agentAnalysisTag')}
                        </Tag>
                      )}
                      {scenario.agent_analysis?.fallback_used && (
                        <Tag color="orange" style={{ marginLeft: 8, fontSize: 11 }}>
                          {t('project:intelligence.usingDefaultAnalysis')}
                        </Tag>
                      )}
                    </div>

                    {/* 理解要点 - 直接展示 */}
                    <div style={{ 
                      padding: 16, 
                      backgroundColor: scenario.agent_analysis?.success ? '#f6ffed' : '#f0f9ff', 
                      borderRadius: 8, 
                      border: `1px solid ${scenario.agent_analysis?.success ? '#b7eb8f' : '#91d5ff'}`,
                      marginBottom: 12
                    }}>
                      {scenario.understanding_points.map((point, idx) => (
                        <div key={idx} style={{ 
                          display: 'flex', 
                          alignItems: 'flex-start', 
                          marginBottom: idx === scenario.understanding_points.length - 1 ? 0 : 12,
                          fontSize: 14,
                          lineHeight: '1.6'
                        }}>
                          <span style={{ 
                            color: scenario.agent_analysis?.success ? '#52c41a' : '#1890ff', 
                            marginRight: 8,
                            fontWeight: 'bold',
                            fontSize: 16
                          }}>
                            •
                          </span>
                          <span style={{ color: '#333', flex: 1 }}>{point}</span>
                        </div>
                      ))}
                      
                      {/* 执行计划信息 - 紧凑展示 */}
                      {scenario.agent_analysis?.success && scenario.agent_analysis.planning_steps && scenario.agent_analysis.planning_steps.length > 0 && (
                        <div style={{ 
                          marginTop: 16, 
                          paddingTop: 12, 
                          borderTop: '1px solid #e8f5e8',
                          display: 'flex', 
                          alignItems: 'center',
                          fontSize: 12,
                          color: '#666'
                        }}>
                          <span style={{ marginRight: 8 }}>📋</span>
                          <span>{t('project:intelligence.plannedSteps', { count: scenario.agent_analysis.planning_steps.length })}</span>
                          <Tag color="green" style={{ marginLeft: 8, fontSize: 10 }}>
                            {t('project:intelligence.agentAnalysis')}
                          </Tag>
                        </div>
                      )}
                    </div>

                    {/* 错误状态 */}
                    {scenario.agent_analysis && !scenario.agent_analysis.success && (
                      <div style={{ 
                        padding: 10, 
                        backgroundColor: '#fff7e6', 
                        borderRadius: 4, 
                        border: '1px solid #ffd591',
                        marginBottom: 12
                      }}>
                        <div style={{ fontSize: 13, color: '#d46b08' }}>
                          {t('project:intelligence.agentAnalysisFailed', { error: scenario.agent_analysis.error })}
                        </div>
                        <div style={{ fontSize: 12, color: '#8c8c8c', marginTop: 4 }}>
                          {t('project:intelligence.usingDefaultUnderstanding')}
                  </div>
                    </div>
                    )}
                  </div>
                </div>

                <Divider />

                <div>
                  <div style={{ marginBottom: 12 }}>
                    <strong>{t('project:intelligence.pleaseConfirmMyUnderstanding')}:</strong>
                  </div>
                  <Radio.Group
                    value={confirmations[scenario.id]}
                    onChange={(e) => handleConfirmationChange(scenario.id, e.target.value)}
                    disabled={isProcessing}
                  >
                    <Space direction="vertical">
                      <Radio value="confirmed">{t('project:intelligence.understandingCorrect')}</Radio>
                      <Radio value="needs_revision">{t('project:intelligence.understandingNeedsAdjustment')}</Radio>
                      <Radio value="rejected">{t('project:intelligence.understandingCompletelyWrong')}</Radio>
                    </Space>
                  </Radio.Group>

                  {confirmations[scenario.id] && confirmations[scenario.id] !== 'confirmed' && (
                    <div style={{ marginTop: 12 }}>
                      <Input.TextArea
                        placeholder={t('project:intelligence.feedbackPlaceholder')}
                        value={feedback[scenario.id] || ''}
                        onChange={(e) => handleFeedbackChange(scenario.id, e.target.value)}
                        rows={3}
                        showCount
                        maxLength={300}
                        disabled={isProcessing}
                      />
                    </div>
                  )}

                  {confirmations[scenario.id] && (
                    <div style={{ marginTop: 8 }}>
                      <Tag color={getConfirmationColor(confirmations[scenario.id])}>
                        {getConfirmationText(confirmations[scenario.id])}
                      </Tag>
                    </div>
                  )}
                </div>
              </Card>
            </List.Item>
          )}
        />

        <div style={{ textAlign: 'center', marginTop: 24 }}>
          <Button 
            type="primary" 
            size="large"
            onClick={handleComplete}
            disabled={!canComplete}
            loading={loading || isProcessing}
            style={{ minWidth: 120 }}
            icon={isProcessing ? <LoadingOutlined /> : undefined}
          >
            {isProcessing ? t('project:intelligence.intelligentProcessing') : t('common:intelligence.completeIntelligentUnderstanding')}
          </Button>
        </div>

        {!canComplete && !isProcessing && (
          <div style={{ textAlign: 'center', marginTop: 12, color: '#666', fontSize: 12 }}>
            {incompleteCount > 0 ? (
              t('common:intelligence.remainingScenarios', { count: incompleteCount })
            ) : (
              t('common:intelligence.completeAllScenarios')
            )}
          </div>
        )}

        <div style={{ marginTop: 16, padding: 12, backgroundColor: '#f6f8fa', borderRadius: 6 }}>
          <div style={{ fontSize: 12, color: '#666' }}>
            💡 <strong>{t('common:intelligence.explanation')}:</strong>
            <ul style={{ margin: '4px 0', paddingLeft: 16 }}>
              <li>{t('common:intelligence.scenarioExplanation')}</li>
              <li>{t('common:intelligence.agentAnalysisExplanation')}</li>
              <li>{t('common:intelligence.agentAnalysisDetail')}</li>
              <li>{t('common:intelligence.understandingPoints')}</li>
              <li>{t('common:intelligence.confirmationHelp')}</li>
              <li><strong>{t('common:intelligence.intelligentOptimization')}</strong></li>
            </ul>
          </div>
        </div>
      </Card>

      {/* 智能处理模态框 */}
      <Modal
        open={isProcessing}
        footer={null}
        closable={false}
        centered
        width={480}
        styles={{
          body: { padding: '32px 24px' }
        }}
      >
        <div style={{ textAlign: 'center' }}>
          {/* 当前阶段图标和动画 */}
          <div style={{ 
            marginBottom: 24,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: 80
          }}>
            <div style={{
              padding: 20,
              borderRadius: '50%',
              backgroundColor: '#f0f9ff',
              border: '2px solid #91d5ff',
              animation: 'pulse 2s infinite'
            }}>
              {processingStages[processingStage]?.icon || <LoadingOutlined style={{ fontSize: 24 }} />}
            </div>
          </div>

          {/* 当前阶段标题 */}
          <div style={{ 
            fontSize: 18, 
            fontWeight: 600, 
            color: '#1f2937',
            marginBottom: 12
          }}>
            {processingStages[processingStage]?.title || t('project:intelligence.processing')}
          </div>

          {/* 当前阶段描述 */}
          <div style={{ 
            fontSize: 14, 
            color: '#6b7280',
            lineHeight: '1.6',
            marginBottom: 24
          }}>
            {processingText}
          </div>

          {/* 进度条 */}
          <Progress 
            percent={Math.round(((processingStage + 1) / processingStages.length) * 100)}
            strokeColor={{
              '0%': '#1890ff',
              '100%': '#52c41a',
            }}
            style={{ marginBottom: 16 }}
          />

          {/* 阶段指示器 */}
          <div style={{ 
            display: 'flex', 
            justifyContent: 'center', 
            gap: 8,
            marginTop: 16
          }}>
            {processingStages.map((stage, index) => (
              <div
                key={index}
                style={{
                  width: 8,
                  height: 8,
                  borderRadius: '50%',
                  backgroundColor: index <= processingStage ? '#52c41a' : '#d1d5db',
                  transition: 'background-color 0.3s ease'
                }}
              />
            ))}
          </div>
        </div>

        {/* 添加CSS动画 */}
        <style>{`
          @keyframes pulse {
            0% {
              transform: scale(1);
              box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.7);
            }
            70% {
              transform: scale(1.05);
              box-shadow: 0 0 0 10px rgba(24, 144, 255, 0);
            }
            100% {
              transform: scale(1);
              box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
            }
          }
        `}</style>
      </Modal>
    </>
  );
};

export default ScenarioPredictionStep; 