import React from 'react';
import { Card, Button, Result, Tag, Divider, Space } from 'antd';
import { CheckCircleOutlined, RocketOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

interface Props {
  summary: {
    businessUnderstanding?: any;
    userAnswers?: Record<string, any>;
    scenarioPredictions?: any[];
  };
  onFinish: () => void;
}

const CompletionSummaryStep: React.FC<Props> = ({ summary, onFinish }) => {
  const { t } = useTranslation();
  const getAnswerCount = () => {
    return Object.keys(summary.userAnswers || {}).length;
  };

  const getConfirmedScenariosCount = () => {
    return summary.scenarioPredictions?.length || 0;
  };

  return (
    <div>
      <Result
        icon={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
        title={`🎉 ${t('project:intelligence.intelligenceComplete')}`}
        subTitle={t('project:intelligence.intelligenceCompleteDesc')}
        extra={
          <Button 
            type="primary" 
            size="large" 
            icon={<RocketOutlined />}
            onClick={onFinish}
            style={{ minWidth: 160 }}
          >
            {t('project:intelligence.startUsingProject')}
          </Button>
        }
      />

      <Card title={`📋 ${t('project:intelligence.understandingSummary')}`} style={{ marginTop: 24 }}>
        <div style={{ marginBottom: 16 }}>
          <Space wrap>
            <Tag color="blue" icon={<CheckCircleOutlined />}>
              {t('project:intelligence.businessDomain')}：{summary.businessUnderstanding?.business_domain || t('project:intelligence.generalBusiness')}
            </Tag>
            <Tag color="green" icon={<CheckCircleOutlined />}>
              {t('project:intelligence.businessType')}：{summary.businessUnderstanding?.business_type || t('project:intelligence.dataAnalysis')}
            </Tag>
            <Tag color="orange" icon={<CheckCircleOutlined />}>
              {t('project:intelligence.answeredQuestions')}：{getAnswerCount()} {t('common:units.count', { count: getAnswerCount() })}
            </Tag>
            <Tag color="purple" icon={<CheckCircleOutlined />}>
              {t('project:intelligence.confirmedScenarios')}：{getConfirmedScenariosCount()} {t('common:units.count', { count: getConfirmedScenariosCount() })}
            </Tag>
          </Space>
        </div>

        <Divider />

        <div style={{ marginBottom: 16 }}>
          <h4>{t('project:intelligence.coreBusinessProcesses')}</h4>
          <div>
            {summary.businessUnderstanding?.core_processes?.map((process: string, index: number) => (
              <Tag key={index} color="processing" style={{ marginBottom: 4 }}>
                {process}
              </Tag>
            )) || <span style={{ color: '#999' }}>{t('project:intelligence.noIdentified')}</span>}
          </div>
        </div>

        <div style={{ marginBottom: 16 }}>
          <h4>{t('project:intelligence.keyBusinessEntities')}</h4>
          <div>
            {summary.businessUnderstanding?.key_entities?.map((entity: string, index: number) => (
              <Tag key={index} color="cyan" style={{ marginBottom: 4 }}>
                {entity}
              </Tag>
            )) || <span style={{ color: '#999' }}>{t('project:intelligence.noIdentified')}</span>}
          </div>
        </div>

        <div style={{ marginBottom: 16 }}>
          <h4>{t('project:intelligence.mainBusinessMetrics')}</h4>
          <div>
            {summary.businessUnderstanding?.business_metrics?.map((metric: string, index: number) => (
              <Tag key={index} color="orange" style={{ marginBottom: 4 }}>
                {metric}
              </Tag>
            )) || <span style={{ color: '#999' }}>{t('project:intelligence.noIdentified')}</span>}
          </div>
        </div>

        <Divider />

        <div style={{ padding: 16, backgroundColor: '#f6f8fa', borderRadius: 6 }}>
          <h4 style={{ margin: '0 0 12px 0' }}>{t('project:intelligence.nextStepsTitle')}</h4>
          <ul style={{ margin: 0, paddingLeft: 20 }}>
            <li>{t('project:intelligence.nextStep1')}</li>
            <li>{t('project:intelligence.nextStep2')}</li>
            <li>{t('project:intelligence.nextStep3')}</li>
            <li>{t('project:intelligence.nextStep4')}</li>
          </ul>
        </div>
      </Card>
    </div>
  );
};

export default CompletionSummaryStep; 