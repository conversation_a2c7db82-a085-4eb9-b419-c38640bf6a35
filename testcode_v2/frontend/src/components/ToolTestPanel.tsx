import React, { useState, useEffect } from 'react';
import { Form, Input, InputNumber, Select, Button, Alert, Card, Table, Typography, Space, Spin, Divider, Descriptions, List, Empty, Modal, message, Tag, Tabs } from 'antd';
import { SendOutlined, SyncOutlined, DownloadOutlined, FileTextOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { toolApi } from '../services/api';
import ToolResultRenderer from './ToolResults';

const { Option } = Select;
const { Text } = Typography;

interface ToolParameter {
  name: string;
  type: string;
  description?: string;
  required?: boolean;
  default?: any;
}

interface ToolTestPanelProps {
  toolId: string;
  toolParameters: ToolParameter[];
  toolType?: string; // 添加工具类型属性
}

const ToolTestPanel: React.FC<ToolTestPanelProps> = ({ toolId, toolParameters = [], toolType = '' }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<any>(null);
  const [rawResponse, setRawResponse] = useState<string>('');
  
  // 执行工具测试
  const handleTest = async (values: any) => {
    setLoading(true);
    setError(null);
    setResult(null);
    setRawResponse('');
    console.log('values', values);
    try {
      console.log('表单原始值:', values);
      console.log('工具参数定义:', toolParameters);
      console.log('工具类型:', toolType);
      
      // 构建参数对象，明确指定类型
      const testParameters: Record<string, any> = {};
      
      // 检查是否为API类型工具
      const isApiTool = toolType.toLowerCase() === 'api';
      console.log('是否为API工具:', isApiTool);
      
      // 将表单值转换为正确的类型
      Object.keys(values).forEach(key => {
        const param = toolParameters.find(p => p.name === key);
        if (!param) {
          console.warn(`未找到参数定义: ${key}，使用原始值`);
          testParameters[key] = values[key];
          return;
        }
        
        let value = values[key];
        
        // 确保值不为undefined
        if (value === undefined || value === null || value === '') {
          if (param.default !== undefined) {
            value = param.default;
          } else if (param.required) {
            console.warn(`必填参数 ${key} 的值为空`);
          }
        }
        
        // API工具的参数处理方式不同，保持字符串格式
        if (value !== undefined && value !== null && value !== '' && !isApiTool) {
          try {
            if (param.type === 'number' || param.type === 'integer') {
              value = Number(value);
            } else if (param.type === 'boolean') {
              if (typeof value === 'string') {
                value = value.toLowerCase() === 'true';
              }
            } else if (param.type === 'array' && typeof value === 'string') {
              // 尝试解析数组
              try {
                value = JSON.parse(value);
              } catch (e) {
                // 如果解析失败，尝试使用逗号分隔
                value = value.split(',').map((item: string) => item.trim());
              }
            } else if (param.type === 'object' && typeof value === 'string') {
              // 尝试将字符串解析为对象
              try {
                value = JSON.parse(value);
              } catch (e) {
                console.error(`无法将参数 ${key} 解析为对象:`, e);
              }
            }
          } catch (e) {
            console.error(`转换参数 ${key} 时出错:`, e);
          }
        }
        
        testParameters[key] = value;
      });
      
      console.log('发送测试请求:', toolId, testParameters);
      const response = await toolApi.testTool(toolId, { parameters: testParameters });
      console.log('测试响应:', response);
      
      // 保存原始响应用于导出
      try {
        setRawResponse(JSON.stringify(response, null, 2));
      } catch (e) {
        console.error('保存原始响应出错:', e);
      }
      
      // 处理不同的响应格式
      if (response?.data?.data) {
        setResult(response.data.data);
      } else if (response?.data?.result) {
        setResult(response.data.result);
      } else {
        setResult(response.data);
      }
    } catch (error: any) { // 明确指定error类型为any
      console.error('工具测试失败:', error);
      let errorMessage = t('common:messages.toolTestFailed');
      
      // 尝试从错误响应中提取更具体的错误信息
      if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error?.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error?.message) {
        errorMessage = error.message;
      }
      
      // 尝试从错误对象上获取更多信息
      console.log('错误对象:', error);
      if (error?.response?.data) {
        console.log('响应数据:', error.response.data);
      }
      
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };
  
  // 重置表单和结果
  const handleReset = () => {
    form.resetFields();
    setResult(null);
    setError(null);
    setRawResponse('');
  };
  
  // 下载测试结果
  const handleDownloadResult = () => {
    if (!result && !rawResponse) return;
    
    const content = rawResponse || JSON.stringify(result, null, 2);
    const blob = new Blob([content], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `${t('common:messages.toolTestResult')}-${toolId}-${new Date().getTime()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };
  
  // 单个字段变化时触发表单验证
  const handleFieldChange = (changedFields: any, allFields: any) => {
    form.validateFields([changedFields.name]);
  };
  
  // 自定义表单字段验证函数
  const validateField = (rule: any, value: any) => {
    if (rule.required && (value === undefined || value === null || value === '')) {
      return Promise.reject(t('common:form.pleaseInput', { field: rule.field }));
    }
    return Promise.resolve();
  };
  
  // 渲染参数输入表单
  const renderParameterFields = () => {
    if (!Array.isArray(toolParameters) || toolParameters.length === 0) {
      return <Alert message={t('common:messages.parameterNotDefined')} type="info" showIcon />;
    }
    
    // 检查是否为API工具
    const isApiTool = toolType.toLowerCase() === 'api';
    
    return toolParameters.map((param, index) => {
      const { name, type, description, required, default: defaultValue } = param;
      
      // 根据参数类型选择不同的表单控件
      let formElement;
      switch (type) {
        case 'number':
          formElement = (
            <InputNumber 
              style={{ width: '100%' }} 
              placeholder={t('common:form.pleaseInputNumber', { field: description || name })}
              defaultValue={defaultValue}
            />
          );
          break;
        case 'boolean':
          formElement = (
            <Select defaultValue={defaultValue?.toString() || 'false'}>
              <Option value="true">{t('common:form.yes')}</Option>
              <Option value="false">{t('common:form.no')}</Option>
            </Select>
          );
          break;
        case 'string':
        default:
          formElement = (
            <Input 
              placeholder={t('common:form.pleaseInput', { field: description || name })} 
              defaultValue={defaultValue}
            />
          );
          break;
      }
      
      return (
        <Form.Item
          key={index}
          name={name}
          label={`${name}${required ? ' *' : ''}${description ? ` (${description})` : ''}`}
          rules={[
            { 
              required: !!required, 
              message: t('common:form.pleaseInput', { field: name }),
              validator: isApiTool ? validateField : undefined  
            }
          ]}
          tooltip={description}
          validateTrigger={['onChange', 'onBlur']}
        >
          {formElement}
        </Form.Item>
      );
    });
  };
  
  // 判断是否为SQL查询结果
  const isSQLQueryResult = (result: any): boolean => {
    try {
      // 检查是否含有SQL专用的results和columns字段
      if (result?.results && Array.isArray(result.results) && 
          (result?.columns || result?.results.length > 0)) {
        return true;
      }
      
      // 检查是否有rows属性
      if (result?.rows && Array.isArray(result.rows)) {
        return true;
      }
      
      // 检查是否有data和columns属性
      if (result?.data && Array.isArray(result.data) && result?.columns) {
        return true;
      }
      
      // 检查是否有SQL字段
      if (result?.sql && typeof result.sql === 'string') {
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('判断SQL查询结果时出错：', error);
      return false;
    }
  };

  // 判断数据是否符合表格格式
  const isTableData = (data: any): boolean => {
    if (!data) return false;
    
    try {
      // 检查是否具有表格所需的所有字段
      return (
        data.columns && Array.isArray(data.columns) && data.columns.length > 0 &&
        data.data && Array.isArray(data.data)
      );
    } catch (error) {
      console.error('判断表格数据时出错：', error);
      return false;
    }
  };

  // 渲染SQL查询结果为表格
  const renderSQLResultTable = (data: any): React.ReactNode => {
    try {
      // 如果数据是对象而不是字符串，直接处理为表格
      if (data && typeof data === 'object' && !Array.isArray(data)) {
        // 如果有rows属性，可能是标准SQL结果格式
        if (data.rows && Array.isArray(data.rows)) {
          const columns = data.columns || Object.keys(data.rows[0] || {}).map(k => ({
            title: k,
            dataIndex: k,
            key: k
          }));
          
          return (
            <>
              <Table 
                columns={columns.map((col: any) => ({
                  title: typeof col === 'string' ? col : col.title || col.name,
                  dataIndex: typeof col === 'string' ? col : col.dataIndex || col.name,
                  key: typeof col === 'string' ? col : col.key || col.name,
                  ellipsis: { showTitle: false },
                  render: (text: any) => (
                    <Typography.Paragraph 
                      ellipsis={{ rows: 3, expandable: true, symbol: '更多' }}
                      style={{ marginBottom: 0 }}
                    >
                      {text === null || text === undefined ? '' : String(text)}
                    </Typography.Paragraph>
                  )
                }))} 
                dataSource={data.rows.map((row: any, index: number) => ({
                  ...row,
                  key: index
                }))}
                size="small"
                pagination={false}
                bordered
              />
              <div style={{ marginTop: 8, textAlign: 'right' }}>
                <Text type="secondary">{t('common:messages.totalRecords', { count: data.rows.length })}</Text>
              </div>
            </>
          );
        }
        
        // 如果有data属性，也可能是标准结果格式
        if (data.data && Array.isArray(data.data)) {
          return renderDataTable(data);
        }
        
        // 如果是普通对象但不是特定格式，尝试展示为JSON
        return (
          <div style={{ 
            whiteSpace: 'pre-wrap', 
            fontFamily: 'monospace', 
            background: '#f5f5f5', 
            padding: 8, 
            borderRadius: 4, 
            maxHeight: '300px', 
            overflow: 'auto'
          }}>
            {JSON.stringify(data, null, 2)}
          </div>
        );
      }
      
      // 如果数据是数组，可能是记录列表
      if (Array.isArray(data)) {
        if (data.length > 0) {
          // 提取列名（使用第一行的键）
          const firstRow = data[0];
          const columns = Object.keys(firstRow).map(col => ({
            title: col,
            dataIndex: col,
            key: col,
            ellipsis: { showTitle: false },
            render: (text: any) => (
              <Typography.Paragraph 
                ellipsis={{ rows: 3, expandable: true, symbol: '更多' }}
                style={{ marginBottom: 0 }}
              >
                {text === null || text === undefined ? '' : String(text)}
              </Typography.Paragraph>
            )
          }));
          
          // 为每行添加key
          const tableData = data.map((row: any, index: number) => ({
            ...row,
            key: index
          }));
          
          return (
            <>
              <Table 
                columns={columns} 
                dataSource={tableData}
                size="small"
                pagination={false}
                bordered
              />
              <div style={{ marginTop: 8, textAlign: 'right' }}>
                <Text type="secondary">{t('common:messages.totalRecords', { count: data.length })}</Text>
              </div>
            </>
          );
        } else {
          return <Empty description={t('common:messages.queryResultEmpty')} />;
        }
      }
      
      // 对于null或undefined，显示提示
      if (data === null || data === undefined) {
        return <Empty description={t('common:messages.queryResultEmpty')} />;
      }
      
      // 其他类型，显示JSON字符串
      return (
        <div style={{ 
          whiteSpace: 'pre-wrap', 
          fontFamily: 'monospace', 
          background: '#f5f5f5', 
          padding: 8, 
          borderRadius: 4 
        }}>
          {JSON.stringify(data, null, 2)}
        </div>
      );
    } catch (error) {
      console.error('渲染SQL结果表格时出错：', error);
      // 出错时，显示错误信息和原始数据
      return (
        <>
          <Alert 
            message={t('common:messages.errorRenderingSQLResult')} 
            description={String(error)}
            type="error" 
            showIcon 
            style={{ marginBottom: 8 }}
          />
          <div style={{ 
            whiteSpace: 'pre-wrap', 
            fontFamily: 'monospace', 
            background: '#f5f5f5', 
            padding: 8, 
            borderRadius: 4 
          }}>
            {typeof data === 'string' ? data : JSON.stringify(data, null, 2)}
          </div>
        </>
      );
    }
  };

  // 使用Ant Design Table渲染标准表格数据
  const renderDataTable = (data: any): React.ReactNode => {
    if (!data || !data.columns || !data.data) return null;
    
    const columns = data.columns.map((col: string, index: number) => ({
      title: col,
      dataIndex: index.toString(),
      key: index.toString(),
      ellipsis: { showTitle: false },
      render: (text: any) => (
        <Typography.Paragraph 
          ellipsis={{ rows: 3, expandable: true, symbol: '更多' }}
          style={{ marginBottom: 0 }}
        >
          {text === null || text === undefined ? '' : text}
        </Typography.Paragraph>
      )
    }));
    
    const tableData = data.data.map((row: any[], rowIndex: number) => {
      const rowData: any = { key: rowIndex };
      row.forEach((cell, cellIndex) => {
        rowData[cellIndex.toString()] = cell;
      });
      return rowData;
    });
    
    return (
      <>
        <Table 
          columns={columns} 
          dataSource={tableData} 
          size="small" 
          pagination={false}
          bordered
        />
        <div style={{ marginTop: 8, textAlign: 'right' }}>
          <Text type="secondary">{t('common:messages.totalRecords', { count: data.count || data.data.length })}</Text>
        </div>
      </>
    );
  };

  // 渲染文本类型的结果
  const renderTextResult = (data: any): React.ReactNode => {
    if (!data) return <Empty description={t('common:messages.resultEmpty')} />;
    
    // 如果数据有特定结构
    if (data.data && data.columns && data.data.length > 0) {
      // 提取文本内容 - 通常在第一行第一列
      const textContent = data.data[0][0];
      
      if (typeof textContent === 'string') {
        return (
          <div style={{ 
            padding: '16px', 
            background: '#fafafa', 
            border: '1px solid #f0f0f0',
            borderRadius: '4px',
            whiteSpace: 'pre-wrap'
          }}>
            {textContent}
          </div>
        );
      }
    }
    
    // 如果找不到特定结构，尝试以不同方式输出
    return (
      <div style={{ 
        padding: '16px', 
        background: '#fafafa', 
        border: '1px solid #f0f0f0',
        borderRadius: '4px',
        whiteSpace: 'pre-wrap'
      }}>
        {typeof data === 'string' 
          ? data 
          : typeof data.data === 'string' 
            ? data.data 
            : JSON.stringify(data, null, 2)}
      </div>
    );
  };
  
  // 尝试解析JSON_DOC字段
  const tryParseJsonDoc = (data: any): any => {
    if (!data || !data.data || !Array.isArray(data.data) || data.data.length === 0) {
      return null;
    }

    // 检查是否是JSON_DOC格式
    if (data.columns && data.columns.includes('JSON_DOC') && data.data[0] && data.data[0][0]) {
      try {
        const jsonContent = data.data[0][0];
        if (typeof jsonContent === 'string' && (jsonContent.startsWith('[') || jsonContent.startsWith('{'))) {
          return JSON.parse(jsonContent);
        }
      } catch (e) {
        console.error('解析JSON_DOC失败:', e);
      }
    }
    return null;
  };

  // 渲染公司信息卡片
  const renderCompanyInfoCard = (companyData: any[]): React.ReactNode => {
    return (
      <>
        {companyData.map((company, index) => {
          // 提取相关文件数据
          const relatedFiles = company["相关文件"] || [];
          // 创建一个不包含相关文件的公司信息副本，用于基本信息展示
          const basicCompanyInfo = { ...company };
          delete basicCompanyInfo["相关文件"];
          
          return (
            <div key={index} style={{ marginBottom: 16 }}>
              <Descriptions 
                title={company["公司名称"] || t('common:messages.companyDetails')} 
                bordered 
                column={{ xxl: 4, xl: 3, lg: 3, md: 2, sm: 2, xs: 1 }}
                size="small"
              >
                {Object.entries(basicCompanyInfo).map(([key, value]) => (
                  <Descriptions.Item 
                    key={key} 
                    label={<strong>{key}</strong>}
                    labelStyle={{ fontWeight: 'bold' }}
                  >
                    {value !== null && value !== undefined ? String(value) : '-'}
                  </Descriptions.Item>
                ))}
              </Descriptions>
              
              {/* 相关文件展示区域 */}
              {relatedFiles && Array.isArray(relatedFiles) && relatedFiles.length > 0 && (
                <div style={{ marginTop: 16 }}>
                  <Divider orientation="left" style={{ margin: '16px 0 12px 0' }}>
                    <Text strong>{t('common:messages.relatedFiles', { count: relatedFiles.length })}</Text>
                  </Divider>
                  <div style={{ 
                    background: '#fafafa', 
                    padding: 12, 
                    borderRadius: 6,
                    border: '1px solid #f0f0f0'
                  }}>
                    <List
                      size="small"
                      dataSource={relatedFiles}
                      renderItem={(file: any, fileIndex: number) => (
                        <List.Item
                          key={fileIndex}
                          style={{ 
                            padding: '8px 12px',
                            background: '#fff',
                            marginBottom: 8,
                            borderRadius: 4,
                            border: '1px solid #e8e8e8'
                          }}
                          actions={[
                            <Button
                              key="download"
                              type="link"
                              size="small"
                              icon={<FileTextOutlined />}
                              onClick={() => {
                                if (file.文件链接) {
                                  window.open(file.文件链接, '_blank');
                                }
                              }}
                              disabled={!file.文件链接}
                            >
                              {t('common:buttons.download')}
                            </Button>,
                            <Button
                              key="preview"
                              type="link"
                              size="small"
                              onClick={() => {
                                if (file.文件链接) {
                                  // 创建预览模态框
                                  const fileName = file.文件名 || t('common:messages.unknownFileName');
                                  const fileUrl = file.文件链接;
                                  
                                  // 判断文件类型
                                  const fileExtension = fileName.split('.').pop()?.toLowerCase();
                                  const isImage = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(fileExtension || '');
                                  const isPdf = fileExtension === 'pdf';
                                  const isOfficeDoc = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'].includes(fileExtension || '');
                                  
                                  let previewContent;
                                  var officeViewerUrl = '';
                                  if (isImage) {
                                    previewContent = (
                                      <div style={{ textAlign: 'center' }}>
                                        <img 
                                          src={fileUrl} 
                                          alt={fileName}
                                          style={{ 
                                            maxWidth: '100%', 
                                            maxHeight: '70vh',
                                            objectFit: 'contain'
                                          }}
                                          onError={(e) => {
                                            (e.target as HTMLImageElement).style.display = 'none';
                                            const errorDiv = document.createElement('div');
                                            errorDiv.innerHTML = t('common:messages.imageLoadFailed');
                                            errorDiv.style.color = '#999';
                                            errorDiv.style.textAlign = 'center';
                                            errorDiv.style.padding = '20px';
                                            (e.target as HTMLImageElement).parentNode?.appendChild(errorDiv);
                                          }}
                                        />
                                      </div>
                                    );
                                  } else if (isPdf) {
                                    previewContent = (
                                      <div style={{ height: '70vh' }}>
                                        <iframe
                                          src={`${fileUrl}#toolbar=1&navpanes=1&scrollbar=1`}
                                          style={{ 
                                            width: '100%', 
                                            height: '100%',
                                            border: 'none'
                                          }}
                                          title={fileName}
                                        />
                                      </div>
                                    );
                                  } else if (isOfficeDoc) {
                                    // Office文档使用微软在线预览服务
                                    officeViewerUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(fileUrl)}`;
                                    previewContent = (
                                      <div style={{ height: '70vh' }}>
                                        <iframe
                                          src={officeViewerUrl}
                                          style={{ 
                                            width: '100%', 
                                            height: '100%',
                                            border: 'none'
                                          }}
                                          title={fileName}
                                        />
                                      </div>
                                    );
                                  } else {
                                    previewContent = (
                                      <div style={{ 
                                        textAlign: 'center', 
                                        padding: '40px',
                                        color: '#666'
                                      }}>
                                        <FileTextOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
                                        <div>{t('common:messages.fileTypeNotSupported')}</div>
                                        <div style={{ marginTop: '8px', fontSize: '12px' }}>
                                          {t('common:messages.fileType', { type: fileExtension?.toUpperCase() || t('common:messages.unknown') })}
                                        </div>
                                        <Button 
                                          type="primary" 
                                          style={{ marginTop: '16px' }}
                                          onClick={() => window.open(fileUrl, '_blank')}
                                        >
                                          {t('common:buttons.downloadFile')}
                                        </Button>
                                      </div>
                                    );
                                  }
                                  
                                  Modal.info({
                                    title: (
                                      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                        <span>{t('common:messages.filePreview', { fileName: fileName })}</span>
                                        <Button 
                                          type="link" 
                                          size="small"
                                          onClick={() => window.open(officeViewerUrl || fileUrl, '_blank')}
                                        >
                                          {t('common:buttons.openInNewWindow')}
                                        </Button>
                                      </div>
                                    ),
                                    content: previewContent,
                                    width: '80vw',
                                    style: { 
                                      top: '8vh'
                                    },
                                    okText: t('common:buttons.close'),
                                    icon: null
                                  });
                                }
                              }}
                              disabled={!file.文件链接}
                            >
                              {t('common:buttons.preview')}
                            </Button>
                          ]}
                        >
                          <List.Item.Meta
                            avatar={
                              <div style={{ 
                                width: 32, 
                                height: 32, 
                                background: '#1890ff', 
                                borderRadius: 4,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                color: '#fff',
                                fontSize: '12px',
                                fontWeight: 'bold'
                              }}>
                                {file.文件名 ? file.文件名.split('.').pop()?.toUpperCase().slice(0, 3) || t('common:messages.doc') : t('common:messages.doc')}
                              </div>
                            }
                            title={
                              <div style={{ display: 'flex', alignItems: 'center' }}>
                                <Text strong style={{ marginRight: 8 }}>
                                  {file.文件名 || t('common:messages.unknownFileName')}
                                </Text>
                                {file.文件链接 && (
                                  <Tag color="green">{t('common:messages.accessible')}</Tag>
                                )}
                              </div>
                            }
                            description={
                              <div style={{ fontSize: '12px', color: '#666' }}>
                                {file.文件链接 ? (
                                  <div>
                                    <Text copyable={{ text: file.文件链接 }} style={{ fontSize: '11px' }}>
                                      {file.文件链接.length > 60 ? 
                                        `${file.文件链接.substring(0, 60)}...` : 
                                        file.文件链接
                                      }
                                    </Text>
                                  </div>
                                ) : (
                                  <Text type="secondary">{t('common:messages.noAvailableLink')}</Text>
                                )}
                              </div>
                            }
                          />
                        </List.Item>
                      )}
                    />
                  </div>
                </div>
              )}
              
              {index < companyData.length - 1 && <Divider />}
            </div>
          );
        })}
        <div style={{ textAlign: 'right', color: '#999', fontSize: '12px' }}>
          {t('common:messages.totalRecords', { count: companyData.length })}
        </div>
      </>
    );
  };

  // 渲染测试结果
  const renderTestResult = () => {
    if (error) {
      return <Alert message={t('common:messages.error')} description={error} type="error" showIcon />;
    }
    
    if (!result) return null;
    
    // 添加日志帮助调试
    console.log('测试结果完整数据：', result);
    
    // 提取显示格式，默认为json
    const displayFormat = result?.display_format || '';
    
    // 处理多级嵌套数据结构 - 针对API通用返回格式
    // 例如 {code: 0, message: "success", data: {success: true, data: {...}}}
    if (result?.data?.data && typeof result.data.data === 'object') {
      const innerData = result.data.data;
      
      // 处理JSON_DOC特殊格式
      const parsedJsonDoc = tryParseJsonDoc(innerData);
      if (parsedJsonDoc) {
        console.log('检测到JSON_DOC数据格式，已解析为对象', parsedJsonDoc);
        return (
          <Card 
            title={t('common:messages.testResult')} 
            extra={
              <Button 
                type="primary" 
                icon={<DownloadOutlined />} 
                onClick={handleDownloadResult}
                size="small"
              >
                {t('common:messages.downloadResult')}
              </Button>
            }
          >
            {renderCompanyInfoCard(Array.isArray(parsedJsonDoc) ? parsedJsonDoc : [parsedJsonDoc])}
          </Card>
        );
      }
      
      // 如果是普通表格数据
      if (innerData.columns && Array.isArray(innerData.columns) &&
          innerData.data && Array.isArray(innerData.data)) {
        console.log('检测到多级嵌套表格数据结构');
        return (
          <Card 
            title={t('common:messages.testResult')} 
            extra={
              <Button 
                type="primary" 
                icon={<DownloadOutlined />} 
                onClick={handleDownloadResult}
                size="small"
              >
                {t('common:messages.downloadResult')}
              </Button>
            }
          >
            {renderDataTable(innerData)}
          </Card>
        );
      }
    }
    
    // 根据displayFormat或数据特征选择渲染方式
    let formattedResult;
    
    // 处理嵌套数据结构 - 检查result.data是否包含表格数据
    if (result?.data && typeof result.data === 'object') {
      // 处理JSON_DOC特殊格式
      const parsedJsonDoc = tryParseJsonDoc(result.data);
      if (parsedJsonDoc) {
        console.log('检测到JSON_DOC数据格式，已解析为对象', parsedJsonDoc);
        return (
          <Card 
            title={t('common:messages.testResult')} 
            extra={
              <Button 
                type="primary" 
                icon={<DownloadOutlined />} 
                onClick={handleDownloadResult}
                size="small"
              >
                {t('common:messages.downloadResult')}
              </Button>
            }
          >
            {renderCompanyInfoCard(Array.isArray(parsedJsonDoc) ? parsedJsonDoc : [parsedJsonDoc])}
          </Card>
        );
      }
      
      // 如果result.data包含columns和data属性，直接作为表格数据处理
      if (result.data.columns && Array.isArray(result.data.columns) && 
          result.data.data && Array.isArray(result.data.data)) {
        console.log('检测到嵌套表格数据结构');
        return (
          <Card 
            title={t('common:messages.testResult')} 
            extra={
              <Button 
                type="primary" 
                icon={<DownloadOutlined />} 
                onClick={handleDownloadResult}
                size="small"
              >
                {t('common:messages.downloadResult')}
              </Button>
            }
          >
            {renderDataTable(result.data)}
          </Card>
        );
      }
      
      // 如果result.data包含SQL查询结果
      if (isSQLQueryResult(result.data)) {
        console.log('检测到嵌套SQL查询结果');
        return (
          <Card 
            title={t('common:messages.testResult')} 
            extra={
              <Button 
                type="primary" 
                icon={<DownloadOutlined />} 
                onClick={handleDownloadResult}
                size="small"
              >
                {t('common:messages.downloadResult')}
              </Button>
            }
          >
            {renderSQLResultTable(result.data)}
          </Card>
        );
      }
    }
    
    if (typeof result === 'object') {
      // 检查是否有display_format字段
      switch(displayFormat.toLowerCase()) {
        case 'text':
          formattedResult = renderTextResult(result);
          break;
          
        case 'table':
          if (isTableData(result)) {
            formattedResult = renderDataTable(result);
          } else if (isSQLQueryResult(result)) {
            formattedResult = renderSQLResultTable(result);
          } else {
            formattedResult = renderDataTable(result);
          }
          break;
          
        case 'json':
        default:
          // 检查是否为SQL查询结果
          if (isSQLQueryResult(result)) {
            formattedResult = renderSQLResultTable(result);
          } else if (isTableData(result)) {
            formattedResult = renderDataTable(result);
          } else {
            // 使用Tabs组件提供JSON和格式化视图之间的切换
            formattedResult = (
              <Tabs defaultActiveKey="formatted">
                                 <Tabs.TabPane tab={t('common:messages.formattedView')} key="formatted">
                   <ToolResultRenderer toolName="" toolType={toolType} data={result} />
                 </Tabs.TabPane>
                <Tabs.TabPane tab={t('common:messages.jsonText')} key="raw">
                  <div style={{ 
                    whiteSpace: 'pre-wrap', 
                    fontFamily: 'monospace', 
                    background: '#f5f5f5', 
                    padding: 8, 
                    borderRadius: 4,
                    maxHeight: '400px',
                    overflow: 'auto'
                  }}>
                    {JSON.stringify(result, null, 2)}
                  </div>
                </Tabs.TabPane>
              </Tabs>
            );
          }
          break;
      }
    } else {
      // 字符串或其他类型
      formattedResult = (
        <div style={{ 
          whiteSpace: 'pre-wrap', 
          wordBreak: 'break-all',
          padding: '16px', 
          background: '#fafafa', 
          border: '1px solid #f0f0f0',
          borderRadius: '4px'
        }}>
          {String(result)}
        </div>
      );
    }
    
    return (
      <Card 
        title={t('common:messages.testResult')} 
        extra={
          <Button 
            type="primary" 
            icon={<DownloadOutlined />} 
            onClick={handleDownloadResult}
            size="small"
          >
            {t('common:messages.downloadResult')}
          </Button>
        }
      >
        {formattedResult}
      </Card>
    );
  };
  
  return (
    <div>
      <Card title={t('common:messages.parameterSettings')}>
        <Form 
          form={form} 
          layout="vertical" 
          onFinish={handleTest}
          initialValues={{}}
        >
          {renderParameterFields()}
          
          <Form.Item>
            <Space>
              <Button 
                type="primary" 
                htmlType="submit" 
                loading={loading}
                icon={<SendOutlined />}
              >
                {t('common:messages.executeTest')}
              </Button>
              <Button 
                onClick={handleReset}
                icon={<SyncOutlined />}
              >
                {t('common:buttons.reset')}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>
      
      <Divider />
      
      {loading ? (
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <Spin tip={t('common:messages.testExecuting')} />
        </div>
      ) : (
        renderTestResult()
      )}
    </div>
  );
};

export default ToolTestPanel; 