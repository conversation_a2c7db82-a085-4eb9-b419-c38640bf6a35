.conversation-sidebar {
  height: 100%;
  border-radius: 8px 0 0 8px;
  border-right: none;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.06);
}

.conversation-sidebar.collapsed {
  width: 60px;
}

.conversation-sidebar.expanded {
  width: 320px;
}

.conversation-sidebar .ant-card-head {
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);
}

.conversation-sidebar .ant-card-body {
  padding: 12px;
  height: calc(100% - 57px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.conversation-list {
  flex: 1;
  overflow: auto;
  margin-top: 8px;
  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #d9d9d9 transparent;
}

.conversation-list::-webkit-scrollbar {
  width: 6px;
}

.conversation-list::-webkit-scrollbar-track {
  background: transparent;
}

.conversation-list::-webkit-scrollbar-thumb {
  background-color: #d9d9d9;
  border-radius: 3px;
}

.conversation-list::-webkit-scrollbar-thumb:hover {
  background-color: #bfbfbf;
}

.conversation-item {
  padding: 12px;
  margin: 0 0 8px 0;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
  background: #fff;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  margin-bottom: 8px;
}

.conversation-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.conversation-item.active {
  background: #e6f7ff;
  border-color: #91d5ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}

.conversation-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: #1890ff;
  border-radius: 0 2px 2px 0;
}

.conversation-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.conversation-item-title {
  font-size: 14px;
  font-weight: 500;
  flex: 1;
  margin-right: 8px;
  line-height: 1.4;
}

.conversation-item-title.active {
  color: #1890ff;
}

.conversation-item-menu {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.conversation-item:hover .conversation-item-menu {
  opacity: 1;
}

.conversation-item-query {
  font-size: 12px;
  color: #8c8c8c;
  line-height: 1.4;
  margin-bottom: 6px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.conversation-item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.conversation-item-time {
  font-size: 11px;
  color: #bfbfbf;
  display: flex;
  align-items: center;
}

.conversation-item-time .anticon {
  margin-right: 2px;
}

.conversation-item-tags {
  display: flex;
  align-items: center;
  gap: 4px;
}

.conversation-item-tags .ant-tag {
  font-size: 11px;
  padding: 0 6px;
  height: 18px;
  line-height: 16px;
  border-radius: 9px;
  margin: 0;
}

.conversation-search {
  margin-bottom: 8px;
}

.conversation-search .ant-input {
  border-radius: 6px;
  border-color: #e8e8e8;
}

.conversation-search .ant-input:focus,
.conversation-search .ant-input-focused {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.conversation-empty {
  text-align: center;
  padding: 40px 20px;
}

.conversation-empty .ant-empty-image {
  margin-bottom: 16px;
}

.conversation-empty .ant-empty-description {
  color: #8c8c8c;
  margin-bottom: 16px;
}

.collapsed-sidebar {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  height: 100%;
}

.collapsed-sidebar .ant-space {
  width: 100%;
}

.collapsed-sidebar .ant-btn {
  width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
}

.collapsed-sidebar .conversation-count {
  font-size: 12px;
  color: #666;
  text-align: center;
}

.conversation-badge {
  position: relative;
}

.conversation-badge .ant-badge-count {
  background: #1890ff;
  border-color: #1890ff;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .conversation-sidebar.expanded {
    width: 280px;
  }
}

@media (max-width: 768px) {
  .conversation-sidebar.expanded {
    width: 260px;
  }
  
  .conversation-item {
    padding: 10px;
  }
  
  .conversation-item-title {
    font-size: 13px;
  }
  
  .conversation-item-query {
    font-size: 11px;
  }
}

/* 动画效果 */
.conversation-sidebar {
  transition: width 0.3s ease;
}

.conversation-item {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.conversation-list .ant-spin-container {
  animation: fadeIn 0.3s ease;
}

/* 加载状态 */
.conversation-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
}

.conversation-loading .ant-spin {
  color: #1890ff;
}

/* 分页信息样式 */
.conversation-pagination-info {
  animation: fadeIn 0.3s ease;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 4px;
  margin: 8px 4px 0 4px;
}

/* 响应式优化 */
@media (max-height: 600px) {
  .conversation-list {
    max-height: calc(100vh - 150px) !important;
  }
}

@media (max-height: 500px) {
  .conversation-list {
    max-height: calc(100vh - 120px) !important;
  }
} 