import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { authApi } from '../services/api';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const location = useLocation();
  const isAuthenticated = authApi.isLoggedIn();
  
  if (!isAuthenticated) {
    // 重定向到登录页面，并记录当前位置
    return <Navigate to="/login" state={{ from: location.pathname }} replace />;
  }
  
  return <>{children}</>;
};

export default ProtectedRoute; 