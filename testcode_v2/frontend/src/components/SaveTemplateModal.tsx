import React from 'react';
import { Modal, Form, Input, Select, Switch, message } from 'antd';

const { TextArea } = Input;
const { Option } = Select;

interface SaveTemplateModalProps {
  visible: boolean;
  content: string;
  onSave: (templateData: any) => void;
  onCancel: () => void;
}

const SaveTemplateModal: React.FC<SaveTemplateModalProps> = ({
  visible,
  content,
  onSave,
  onCancel
}) => {
  const [form] = Form.useForm();

  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      onSave(values);
      form.resetFields();
    } catch (error) {
      message.error('请填写完整信息');
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <span style={{ marginRight: 8 }}>💾</span>
          <span>保存为模板</span>
        </div>
      }
      open={visible}
      onOk={handleSave}
      onCancel={handleCancel}
      okText="保存"
      cancelText="取消"
      width={600}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          template_type: 'query',
          complexity_level: 'basic',
          is_public: false
        }}
      >
        <Form.Item
          name="name"
          label="模板名称"
          rules={[{ required: true, message: '请输入模板名称' }]}
        >
          <Input placeholder="请输入模板名称" />
        </Form.Item>

        <Form.Item
          name="description"
          label="模板描述"
        >
          <TextArea 
            placeholder="请输入模板描述（可选）" 
            autoSize={{ minRows: 2, maxRows: 4 }}
          />
        </Form.Item>

        <Form.Item
          name="category"
          label="模板分类"
        >
          <Select placeholder="请选择分类">
            <Option value="销售分析">销售分析</Option>
            <Option value="用户分析">用户分析</Option>
            <Option value="财务分析">财务分析</Option>
            <Option value="数据质量">数据质量</Option>
            <Option value="运营分析">运营分析</Option>
            <Option value="通用分析">通用分析</Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="tags"
          label="标签"
        >
          <Select
            mode="tags"
            placeholder="请输入标签（按回车添加）"
            style={{ width: '100%' }}
          />
        </Form.Item>

        <Form.Item
          name="complexity_level"
          label="复杂度"
        >
          <Select>
            <Option value="basic">基础</Option>
            <Option value="intermediate">中级</Option>
            <Option value="advanced">高级</Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="is_public"
          label="公开模板"
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>

        <Form.Item label="模板内容预览">
          <div style={{
            background: '#f8fafc',
            padding: '12px',
            borderRadius: '8px',
            border: '1px solid #e2e8f0',
            maxHeight: '200px',
            overflow: 'auto',
            fontSize: '14px',
            lineHeight: '1.6',
            color: '#374151'
          }}>
            {content}
          </div>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default SaveTemplateModal;
