import React, { useState, useEffect } from 'react';
import { Card, Descriptions, Button, message } from 'antd';
import { authApi } from '../services/api';

const UserDebugInfo: React.FC = () => {
  const [userInfo, setUserInfo] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const fetchUserInfo = async () => {
    setLoading(true);
    try {
      const response = await authApi.getCurrentUser();
      console.log('用户信息API响应:', response.data);
      // 从标准响应格式中提取实际的用户数据
      setUserInfo(response.data.data);
    } catch (error) {
      console.error('获取用户信息失败:', error);
      message.error('获取用户信息失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUserInfo();
  }, []);

  const localUserInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');

  const clearCache = () => {
    localStorage.removeItem('userInfo');
    localStorage.removeItem('token');
    message.success('缓存已清除，请重新登录');
    setTimeout(() => {
      window.location.href = '/login';
    }, 1000);
  };

  return (
    <Card title="用户调试信息" style={{ margin: '16px 0' }}>
      <div style={{ marginBottom: 16 }}>
        <Button onClick={fetchUserInfo} loading={loading} style={{ marginRight: 8 }}>
          刷新用户信息
        </Button>
        <Button onClick={clearCache} danger>
          清除缓存并重新登录
        </Button>
      </div>
      
      <div style={{ display: 'flex', gap: '16px' }}>
        <div style={{ flex: 1 }}>
          <h4>API返回的用户信息:</h4>
          <Descriptions column={1} size="small" bordered>
            <Descriptions.Item label="ID">{userInfo?.id}</Descriptions.Item>
            <Descriptions.Item label="用户名">{userInfo?.username}</Descriptions.Item>
            <Descriptions.Item label="邮箱">{userInfo?.email || '无'}</Descriptions.Item>
            <Descriptions.Item label="系统角色ID">{userInfo?.role_id}</Descriptions.Item>
            <Descriptions.Item label="企业ID">{userInfo?.org_id}</Descriptions.Item>
            <Descriptions.Item label="是否超级用户">{userInfo?.is_superuser ? '是' : '否'}</Descriptions.Item>
            <Descriptions.Item label="是否可创建项目">{userInfo?.can_create_project ? '是' : '否'}</Descriptions.Item>
            <Descriptions.Item label="是否启用">{userInfo?.is_active ? '是' : '否'}</Descriptions.Item>
          </Descriptions>
        </div>
        
        <div style={{ flex: 1 }}>
          <h4>localStorage中的用户信息:</h4>
          <Descriptions column={1} size="small" bordered>
            <Descriptions.Item label="ID">{localUserInfo?.id}</Descriptions.Item>
            <Descriptions.Item label="用户名">{localUserInfo?.username}</Descriptions.Item>
            <Descriptions.Item label="邮箱">{localUserInfo?.email || '无'}</Descriptions.Item>
            <Descriptions.Item label="系统角色ID">{localUserInfo?.role_id}</Descriptions.Item>
            <Descriptions.Item label="企业ID">{localUserInfo?.org_id}</Descriptions.Item>
            <Descriptions.Item label="是否超级用户">{localUserInfo?.is_superuser ? '是' : '否'}</Descriptions.Item>
            <Descriptions.Item label="是否可创建项目">{localUserInfo?.can_create_project ? '是' : '否'}</Descriptions.Item>
            <Descriptions.Item label="是否启用">{localUserInfo?.is_active ? '是' : '否'}</Descriptions.Item>
          </Descriptions>
        </div>
      </div>
    </Card>
  );
};

export default UserDebugInfo; 