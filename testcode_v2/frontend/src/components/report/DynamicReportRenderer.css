/* 动态报告渲染器样式 */
.dynamic-report-container {
  width: 100%;
  background: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.dynamic-report-empty {
  padding: 40px 20px;
  text-align: center;
}

/* 报告工具栏 */
.report-toolbar {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 16px 24px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  gap: 12px;
}

.export-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.export-button:hover {
  background: #40a9ff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

/* 报告内容区域 */
.report-content {
  padding: 24px;
}

/* 报告组件通用样式 */
.report-component {
  margin-bottom: 32px;
  transition: all 0.3s ease;
}

.report-component:last-child {
  margin-bottom: 0;
}

.report-component:hover {
  transform: translateY(-2px);
}

/* 特定组件类型的样式 */
.report-component-header {
  margin-bottom: 40px;
}

.report-component-kpi_grid {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 24px;
  color: white;
  margin-bottom: 40px;
}

.report-component-executive_summary {
  background: #f8f9ff;
  border-left: 4px solid #1890ff;
  border-radius: 8px;
  padding: 24px;
}

.report-component-chart {
  background: white;
  border: 1px solid #f0f0f0;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.report-component-insight_list {
  background: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 8px;
  padding: 24px;
}

.report-component-recommendation_list {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 8px;
  padding: 24px;
}

.report-component-data_table {
  background: white;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
}

.report-component-text_section {
  background: white;
  padding: 20px 0;
}

/* 未知组件警告样式 */
.report-component-unknown {
  margin: 16px 0;
}

/* 报告元数据（开发模式） */
.report-metadata {
  margin-top: 40px;
  padding: 20px;
  background: #f5f5f5;
  border-top: 1px solid #e8e8e8;
  font-size: 12px;
}

.report-metadata details {
  cursor: pointer;
}

.report-metadata summary {
  font-weight: 600;
  color: #666;
  margin-bottom: 10px;
}

.report-metadata pre {
  background: white;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #ddd;
  overflow-x: auto;
  font-size: 11px;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .report-content {
    padding: 16px;
  }
  
  .report-component {
    margin-bottom: 24px;
  }
  
  .report-toolbar {
    padding: 12px 16px;
  }
  
  .export-button {
    padding: 6px 12px;
    font-size: 13px;
  }
}

/* 打印样式 */
@media print {
  .report-toolbar {
    display: none;
  }
  
  .report-metadata {
    display: none;
  }
  
  .report-component {
    break-inside: avoid;
    margin-bottom: 20px;
  }
  
  .dynamic-report-container {
    box-shadow: none;
    border: none;
  }
}