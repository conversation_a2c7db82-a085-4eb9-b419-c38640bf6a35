/**
 * 动态报告渲染器
 * 根据后端返回的结构化JSON数据动态渲染报告组件
 */
import React from 'react';
import { Alert } from 'antd';
import { HeaderComponent } from './components/HeaderComponent';
import { KpiGrid } from './components/KpiGrid';
import { ExecutiveSummary } from './components/ExecutiveSummary';
import { ChartCard } from './components/ChartCard';
import { InsightList } from './components/InsightList';
import { RecommendationList } from './components/RecommendationList';
import { DataTable } from './components/DataTable';
import { TextSection } from './components/TextSection';
import { Divider } from './components/Divider';
import './DynamicReportRenderer.css';

// 组件映射表
const componentMap = {
  header: HeaderComponent,
  kpi_grid: KpiGrid,
  executive_summary: ExecutiveSummary,
  chart: ChartCard,
  insight_list: InsightList,
  recommendation_list: RecommendationList,
  data_table: DataTable,
  text_section: TextSection,
  divider: Divider,
};

interface ReportComponent {
  type: string;
  data: any;
}

interface StructuredReport {
  components: ReportComponent[];
  metadata?: {
    generated_at?: string;
    project_id?: string;
    language?: string;
    total_components?: number;
  };
  version?: string;
}

interface DynamicReportRendererProps {
  reportData: StructuredReport | ReportComponent[];
  className?: string;
  onExport?: () => void;
  onComponentInteraction?: (componentType: string, action: string, data: any) => void;
}

const DynamicReportRenderer: React.FC<DynamicReportRendererProps> = ({
  reportData,
  className = '',
  onExport,
  onComponentInteraction
}) => {
  // 处理不同的数据格式
  const getComponents = (): ReportComponent[] => {
    if (!reportData) return [];
    
    // 如果是StructuredReport格式
    if ('components' in reportData && Array.isArray(reportData.components)) {
      return reportData.components;
    }
    
    // 如果直接是组件数组
    if (Array.isArray(reportData)) {
      return reportData;
    }
    
    return [];
  };

  const components = getComponents();

  if (components.length === 0) {
    return (
      <div className="dynamic-report-empty">
        <Alert 
          message="暂无报告数据" 
          description="报告正在生成中，请稍后刷新查看。"
          type="info" 
          showIcon 
        />
      </div>
    );
  }

  const handleComponentInteraction = (componentType: string, action: string, data: any) => {
    if (onComponentInteraction) {
      onComponentInteraction(componentType, action, data);
    }
  };

  return (
    <div className={`dynamic-report-container ${className}`}>
      {/* 报告工具栏 */}
      <div className="report-toolbar">
        {onExport && (
          <button 
            className="export-button"
            onClick={onExport}
            title="导出报告"
          >
            📄 导出报告
          </button>
        )}
      </div>

      {/* 渲染报告组件 */}
      <div className="report-content">
        {components.map((component, index) => {
          const ComponentToRender = componentMap[component.type as keyof typeof componentMap];
          
          if (ComponentToRender) {
            return (
              <div key={index} className={`report-component report-component-${component.type}`}>
                <ComponentToRender 
                  data={component.data}
                  onInteraction={(action: string, data: any) => 
                    handleComponentInteraction(component.type, action, data)
                  }
                />
              </div>
            );
          }
          
          // 未知组件类型的警告
          return (
            <div key={index} className="report-component-unknown">
              <Alert 
                message={`未知报告组件: ${component.type}`} 
                type="warning" 
                showIcon 
              />
            </div>
          );
        })}
      </div>

      {/* 报告元数据（调试用） */}
      {process.env.NODE_ENV === 'development' && 'metadata' in reportData && (
        <div className="report-metadata">
          <details>
            <summary>报告元数据</summary>
            <pre>{JSON.stringify(reportData.metadata, null, 2)}</pre>
          </details>
        </div>
      )}
    </div>
  );
};

export default DynamicReportRenderer;