/* 图表卡片组件样式 */
.chart-card-container {
  position: relative;
}

.chart-card {
  background: white !important;
  border: 1px solid #f0f0f0 !important;
  border-radius: 16px !important;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08) !important;
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.chart-card:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12) !important;
  transform: translateY(-2px);
}

.chart-card .ant-card-head {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
  border-bottom: 1px solid #e8e8e8 !important;
  padding: 0 24px !important;
  min-height: 64px;
}

.chart-card .ant-card-head-title {
  padding: 16px 0 !important;
}

.chart-card .ant-card-extra {
  padding: 16px 0 !important;
}

.chart-card .ant-card-body {
  padding: 24px !important;
}

/* 卡片头部 */
.chart-card-header {
  width: 100%;
}

.chart-icon {
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.chart-title-info {
  flex: 1;
}

.chart-title {
  color: #333 !important;
  margin: 0 !important;
  font-size: 1.2rem !important;
  font-weight: 600 !important;
  line-height: 1.3;
}

.chart-type-badge {
  display: inline-block;
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  margin-top: 4px;
  text-transform: uppercase;
}

/* 操作按钮 */
.chart-action-btn {
  color: #666 !important;
  border: none !important;
  background: transparent !important;
  transition: all 0.3s ease;
}

.chart-action-btn:hover {
  color: #1890ff !important;
  background: rgba(24, 144, 255, 0.1) !important;
  transform: scale(1.05);
}

/* 图表解读 */
.chart-interpretation {
  margin-bottom: 24px;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  border-left: 4px solid #1890ff;
}

.interpretation-text {
  color: #555 !important;
  font-size: 0.95rem !important;
  line-height: 1.6 !important;
  margin: 0 !important;
  font-weight: 400;
}

/* 图表容器 */
.chart-container {
  position: relative;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #f5f5f5;
}

.chart-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, 
    transparent 0%, 
    rgba(24, 144, 255, 0.02) 25%, 
    transparent 50%, 
    rgba(24, 144, 255, 0.02) 75%, 
    transparent 100%
  );
  pointer-events: none;
  z-index: 0;
}

.chart-container > div {
  position: relative;
  z-index: 1;
}

/* 图表底部信息 */
.chart-footer {
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
  text-align: right;
}

.data-source {
  color: #999;
  font-size: 0.8rem;
  font-style: italic;
}

/* 装饰性元素 */
.chart-decoration {
  position: absolute;
  top: 0;
  right: 0;
  width: 60px;
  height: 60px;
  overflow: hidden;
  pointer-events: none;
}

.decoration-corner {
  position: absolute;
  top: -30px;
  right: -30px;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.1) 0%, transparent 50%);
  border-radius: 50%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-card .ant-card-head {
    padding: 0 16px !important;
    min-height: 56px;
  }
  
  .chart-card .ant-card-body {
    padding: 20px 16px !important;
  }
  
  .chart-title {
    font-size: 1.1rem !important;
  }
  
  .chart-icon {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }
  
  .chart-container {
    height: 300px;
  }
  
  .interpretation-text {
    font-size: 0.9rem !important;
  }
  
  .chart-interpretation {
    padding: 12px;
    margin-bottom: 20px;
  }
}

@media (max-width: 480px) {
  .chart-card .ant-card-head {
    padding: 0 12px !important;
  }
  
  .chart-card .ant-card-body {
    padding: 16px 12px !important;
  }
  
  .chart-title {
    font-size: 1rem !important;
  }
  
  .chart-container {
    height: 250px;
  }
  
  .chart-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .chart-action-btn {
    padding: 4px 8px !important;
  }
}

/* 打印样式 */
@media print {
  .chart-card {
    border: 1px solid #ddd !important;
    box-shadow: none !important;
    break-inside: avoid;
  }
  
  .chart-card .ant-card-head {
    background: #f9f9f9 !important;
  }
  
  .chart-card .ant-card-extra {
    display: none;
  }
  
  .chart-container {
    height: 300px !important;
  }
  
  .decoration-corner {
    display: none;
  }
}

/* 图表加载状态 */
.chart-container .echarts-loading {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
}

/* 图表交互提示 */
.chart-container:hover::after {
  content: '点击图表元素查看详情';
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.7rem;
  opacity: 0.8;
  pointer-events: none;
}