/* KPI网格组件样式 */
.kpi-grid-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 32px;
  color: white;
  position: relative;
  overflow: hidden;
}

.kpi-grid-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.kpi-grid-title {
  color: white !important;
  text-align: center;
  margin-bottom: 32px !important;
  font-size: 1.8rem !important;
  font-weight: 600 !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  position: relative;
  z-index: 1;
}

.kpi-metrics-row {
  position: relative;
  z-index: 1;
}

.kpi-metric-col {
  display: flex;
}

.kpi-metric-card {
  width: 100%;
  height: 140px;
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 12px !important;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.kpi-metric-card:hover {
  background: rgba(255, 255, 255, 1) !important;
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

.kpi-metric-card .ant-card-body {
  padding: 20px !important;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.kpi-metric-content {
  text-align: center;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.metric-label {
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
  margin-bottom: 8px;
  line-height: 1.3;
}

.metric-value-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 6px;
}

.metric-value {
  font-size: 1.8rem;
  font-weight: 700;
  color: #1a1a1a;
  line-height: 1;
}

.metric-change {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.85rem;
  font-weight: 600;
}

.change-text {
  font-weight: 600;
}

/* 趋势指示器 */
.trend-indicator {
  position: absolute;
  top: 0;
  right: 0;
  width: 4px;
  height: 100%;
  border-radius: 0 12px 12px 0;
}

.trend-increase {
  background: linear-gradient(180deg, #52c41a 0%, #389e0d 100%);
}

.trend-decrease {
  background: linear-gradient(180deg, #ff4d4f 0%, #cf1322 100%);
}

.trend-stable {
  background: linear-gradient(180deg, #faad14 0%, #d48806 100%);
}

/* 变化类型样式 */
.change-increase .metric-value {
  color: #389e0d;
}

.change-decrease .metric-value {
  color: #cf1322;
}

.change-stable .metric-value {
  color: #d48806;
}

/* 装饰元素 */
.kpi-grid-decoration {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 32px;
  position: relative;
  z-index: 1;
}

.decoration-line {
  flex: 1;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
}

.decoration-dots {
  display: flex;
  gap: 8px;
  margin: 0 16px;
}

.dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  animation: pulse 2s infinite;
}

.dot:nth-child(2) {
  animation-delay: 0.5s;
}

.dot:nth-child(3) {
  animation-delay: 1s;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .kpi-grid-container {
    padding: 24px 16px;
  }
  
  .kpi-grid-title {
    font-size: 1.5rem !important;
    margin-bottom: 24px !important;
  }
  
  .kpi-metric-card {
    height: 120px;
  }
  
  .metric-value {
    font-size: 1.5rem;
  }
  
  .metric-label {
    font-size: 0.8rem;
  }
  
  .metric-change {
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .kpi-grid-container {
    padding: 20px 12px;
  }
  
  .kpi-metric-card {
    height: 100px;
  }
  
  .kpi-metric-card .ant-card-body {
    padding: 16px !important;
  }
  
  .metric-value {
    font-size: 1.3rem;
  }
  
  .metric-label {
    font-size: 0.75rem;
    margin-bottom: 4px;
  }
}

/* 打印样式 */
@media print {
  .kpi-grid-container {
    background: #f5f5f5 !important;
    color: #333 !important;
    border: 1px solid #ddd;
  }
  
  .kpi-grid-title {
    color: #333 !important;
  }
  
  .kpi-metric-card {
    background: white !important;
    border: 1px solid #ddd !important;
  }
}