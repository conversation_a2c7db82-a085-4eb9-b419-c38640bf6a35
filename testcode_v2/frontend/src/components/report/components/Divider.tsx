/**
 * 分割线组件
 * 在报告中添加视觉分割
 */
import React from 'react';
import { Divider as AntDivider } from 'antd';

interface DividerData {
  style?: 'solid' | 'dashed' | 'dotted';
  margin?: string;
}

interface DividerProps {
  data: DividerData;
  onInteraction?: (action: string, data: any) => void;
}

const Divider: React.FC<DividerProps> = ({ data }) => {
  const { style = 'solid', margin = '24px' } = data;

  const dividerStyle = {
    margin: `${margin} 0`,
    borderTopStyle: style,
  };

  return <AntDivider style={dividerStyle} />;
};

export { Divider };