/**
 * KPI网格组件
 * 显示关键业绩指标的网格布局
 */
import React from 'react';
import { Card, Col, Row, Statistic, Tooltip, Typography } from 'antd';
import { ArrowUpOutlined, ArrowDownOutlined, MinusOutlined } from '@ant-design/icons';
import './KpiGrid.css';

const { Title } = Typography;

interface MetricData {
  label: string;
  value: string;
  change?: string;
  change_type?: 'increase' | 'decrease' | 'stable';
  description?: string;
}

interface KpiGridData {
  title: string;
  metrics: MetricData[];
  layout?: Record<string, any>;
}

interface KpiGridProps {
  data: KpiGridData;
  onInteraction?: (action: string, data: any) => void;
}

const KpiGrid: React.FC<KpiGridProps> = ({ data, onInteraction }) => {
  const { title, metrics, layout } = data;

  // 获取变化图标
  const getChangeIcon = (changeType?: string) => {
    switch (changeType) {
      case 'increase':
        return <ArrowUpOutlined />;
      case 'decrease':
        return <ArrowDownOutlined />;
      case 'stable':
        return <MinusOutlined />;
      default:
        return null;
    }
  };

  // 获取变化颜色
  const getChangeColor = (changeType?: string) => {
    switch (changeType) {
      case 'increase':
        return '#52c41a';
      case 'decrease':
        return '#ff4d4f';
      case 'stable':
        return '#faad14';
      default:
        return '#666';
    }
  };

  // 处理指标点击
  const handleMetricClick = (metric: MetricData) => {
    if (onInteraction) {
      onInteraction('metric_click', metric);
    }
  };

  // 计算网格列数
  const getColSpan = () => {
    const count = metrics.length;
    if (count <= 2) return 12;
    if (count <= 4) return 6;
    return 4;
  };

  return (
    <div className="kpi-grid-container">
      <Title level={3} className="kpi-grid-title">
        {title}
      </Title>
      
      <Row gutter={[24, 24]} className="kpi-metrics-row">
        {metrics.map((metric, index) => (
          <Col 
            key={index} 
            xs={24} 
            sm={12} 
            md={getColSpan()} 
            lg={getColSpan()}
            className="kpi-metric-col"
          >
            <Card 
              className={`kpi-metric-card ${metric.change_type ? `change-${metric.change_type}` : ''}`}
              hoverable
              onClick={() => handleMetricClick(metric)}
            >
              <div className="kpi-metric-content">
                {/* 指标标签 */}
                <div className="metric-label">
                  {metric.description ? (
                    <Tooltip title={metric.description}>
                      <span>{metric.label}</span>
                    </Tooltip>
                  ) : (
                    <span>{metric.label}</span>
                  )}
                </div>

                {/* 指标值 */}
                <div className="metric-value-container">
                  <div className="metric-value">
                    {metric.value}
                  </div>
                  
                  {/* 变化指示器 */}
                  {metric.change && (
                    <div 
                      className="metric-change"
                      style={{ color: getChangeColor(metric.change_type) }}
                    >
                      {getChangeIcon(metric.change_type)}
                      <span className="change-text">{metric.change}</span>
                    </div>
                  )}
                </div>

                {/* 趋势指示器 */}
                {metric.change_type && (
                  <div className={`trend-indicator trend-${metric.change_type}`} />
                )}
              </div>
            </Card>
          </Col>
        ))}
      </Row>

      {/* 网格底部装饰 */}
      <div className="kpi-grid-decoration">
        <div className="decoration-line" />
        <div className="decoration-dots">
          <span className="dot" />
          <span className="dot" />
          <span className="dot" />
        </div>
        <div className="decoration-line" />
      </div>
    </div>
  );
};

export { KpiGrid };