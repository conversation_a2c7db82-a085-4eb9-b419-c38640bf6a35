/**
 * 图表卡片组件
 * 显示图表和对应的解读文本
 */
import React, { useRef } from 'react';
import { Card, Typography, Button, Space, message } from 'antd';
import { DownloadOutlined, FullscreenOutlined, BarChartOutlined } from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
// import html2canvas from 'html2canvas'; // TODO: 需要安装 html2canvas 依赖
import './ChartCard.css';

const { Title, Paragraph } = Typography;

interface ChartData {
  chart_id: string;
  title: string;
  interpretation: string;
  chart_config: Record<string, any>;
  chart_type?: string;
  data_source?: string;
}

interface ChartCardProps {
  data: ChartData;
  onInteraction?: (action: string, data: any) => void;
}

const ChartCard: React.FC<ChartCardProps> = ({ data, onInteraction }) => {
  const { chart_id, title, interpretation, chart_config, chart_type, data_source } = data;
  const chartRef = useRef<any>(null);
  const cardRef = useRef<HTMLDivElement>(null);

  // 下载图表为PNG
  const downloadChart = async () => {
    try {
      // TODO: 实现图表下载功能，需要安装 html2canvas 依赖
      message.info('图表下载功能开发中...');
      
      if (onInteraction) {
        onInteraction('download', { chart_id, title });
      }
    } catch (error) {
      console.error('下载图表失败:', error);
      message.error('下载失败，请重试');
    }
  };

  // 全屏查看
  const viewFullscreen = () => {
    if (onInteraction) {
      onInteraction('fullscreen', { chart_id, title, chart_config });
    }
  };

  // 图表点击事件
  const onChartClick = (params: any) => {
    if (onInteraction) {
      onInteraction('chart_click', { chart_id, click_data: params });
    }
  };

  // 处理图表配置
  const getChartOption = () => {
    const option = { ...chart_config };
    
    // 确保图表有点击事件
    if (option.series) {
      option.series = option.series.map((series: any) => ({
        ...series,
        cursor: 'pointer'
      }));
    }
    
    // 添加响应式配置
    option.responsive = true;
    
    return option;
  };

  return (
    <div className="chart-card-container" ref={cardRef}>
      <Card 
        className="chart-card"
        title={
          <div className="chart-card-header">
            <Space align="center" size="middle">
              <div className="chart-icon">
                <BarChartOutlined />
              </div>
              <div className="chart-title-info">
                <Title level={4} className="chart-title">
                  {title}
                </Title>
                {chart_type && (
                  <span className="chart-type-badge">
                    {chart_type}
                  </span>
                )}
              </div>
            </Space>
          </div>
        }
        extra={
          <Space size="small">
            <Button 
              type="text" 
              icon={<FullscreenOutlined />}
              onClick={viewFullscreen}
              title="全屏查看"
              className="chart-action-btn"
            />
            <Button 
              type="text" 
              icon={<DownloadOutlined />}
              onClick={downloadChart}
              title="下载图表"
              className="chart-action-btn"
            />
          </Space>
        }
        bordered={false}
      >
        {/* 图表解读 */}
        <div className="chart-interpretation">
          <Paragraph className="interpretation-text">
            {interpretation}
          </Paragraph>
        </div>

        {/* 图表容器 */}
        <div className="chart-container">
          <ReactECharts
            ref={chartRef}
            option={getChartOption()}
            style={{ height: '400px', width: '100%' }}
            onEvents={{
              click: onChartClick
            }}
            opts={{ renderer: 'canvas' }}
          />
        </div>

        {/* 数据源信息 */}
        {data_source && (
          <div className="chart-footer">
            <span className="data-source">
              数据来源: {data_source}
            </span>
          </div>
        )}

        {/* 装饰性元素 */}
        <div className="chart-decoration">
          <div className="decoration-corner" />
        </div>
      </Card>
    </div>
  );
};

export { ChartCard };