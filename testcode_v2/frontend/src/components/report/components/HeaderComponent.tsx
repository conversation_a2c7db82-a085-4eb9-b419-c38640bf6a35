/**
 * 报告头部组件
 * 显示报告标题、副标题、生成时间和标签
 */
import React from 'react';
import { Typography, Tag, Space } from 'antd';
import { CalendarOutlined, TagOutlined } from '@ant-design/icons';
import './HeaderComponent.css';

const { Title, Text } = Typography;

interface HeaderData {
  title: string;
  subtitle?: string;
  generated_at: string;
  tags?: string[];
  summary_stats?: Record<string, any>;
}

interface HeaderComponentProps {
  data: HeaderData;
  onInteraction?: (action: string, data: any) => void;
}

const HeaderComponent: React.FC<HeaderComponentProps> = ({ data, onInteraction }) => {
  const { title, subtitle, generated_at, tags = [], summary_stats } = data;

  // 格式化生成时间
  const formatTime = (timeStr: string) => {
    try {
      const date = new Date(timeStr);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return timeStr;
    }
  };

  return (
    <div className="report-header">
      {/* 主标题区域 */}
      <div className="header-main">
        <Title level={1} className="report-title">
          {title}
        </Title>
        
        {subtitle && (
          <Text className="report-subtitle" type="secondary">
            {subtitle}
          </Text>
        )}
      </div>

      {/* 元信息区域 */}
      <div className="header-meta">
        <Space size="large" wrap>
          {/* 生成时间 */}
          <div className="meta-item">
            <CalendarOutlined className="meta-icon" />
            <Text type="secondary">
              生成时间: {formatTime(generated_at)}
            </Text>
          </div>

          {/* 标签 */}
          {tags.length > 0 && (
            <div className="meta-item">
              <TagOutlined className="meta-icon" />
              <Space size={4} wrap>
                {tags.map((tag, index) => (
                  <Tag key={index} color="blue" className="report-tag">
                    {tag}
                  </Tag>
                ))}
              </Space>
            </div>
          )}
        </Space>
      </div>

      {/* 概览统计（如果有） */}
      {summary_stats && Object.keys(summary_stats).length > 0 && (
        <div className="header-stats">
          <div className="stats-grid">
            {Object.entries(summary_stats).map(([key, value]) => (
              <div key={key} className="stat-item">
                <div className="stat-value">{String(value)}</div>
                <div className="stat-label">{key}</div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 装饰性分割线 */}
      <div className="header-divider" />
    </div>
  );
};

export { HeaderComponent };