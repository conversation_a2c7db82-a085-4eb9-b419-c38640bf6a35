/* 建议列表组件样式 */
.recommendation-list-container {
  position: relative;
}

.recommendation-list-card {
  background: #f6ffed !important;
  border: 1px solid #b7eb8f !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 16px rgba(82, 196, 26, 0.1) !important;
}

.recommendation-list-card .ant-card-head {
  background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%) !important;
  border-bottom: 1px solid #b7eb8f !important;
}

.recommendation-list-header {
  width: 100%;
}

.header-icon {
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  box-shadow: 0 2px 8px rgba(82, 196, 26, 0.3);
}

.recommendation-list-title {
  color: #389e0d !important;
  margin: 0 !important;
  font-size: 1.2rem !important;
  font-weight: 600 !important;
}

/* 建议列表 */
.recommendations-list {
  margin: 0;
}

.recommendations-list .ant-list-item {
  border-bottom: 1px solid rgba(183, 235, 143, 0.3) !important;
  padding: 20px 0 !important;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.recommendations-list .ant-list-item:hover {
  background: rgba(82, 196, 26, 0.05);
  border-radius: 8px;
  padding-left: 16px !important;
  padding-right: 16px !important;
}

.recommendations-list .ant-list-item:last-child {
  border-bottom: none !important;
}

/* 建议项 */
.recommendation-item {
  position: relative;
  padding-left: 12px;
}

.recommendation-content {
  width: 100%;
}

.recommendation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  flex-wrap: wrap;
  gap: 8px;
}

.header-left {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.recommendation-number {
  width: 28px;
  height: 28px;
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 0.9rem;
  box-shadow: 0 2px 6px rgba(82, 196, 26, 0.3);
}

.area-tag {
  font-weight: 600;
  border: none;
  color: white;
  background: #1890ff;
}

.priority-tag {
  font-weight: 500;
  border: none;
  color: white;
}

.timeline-info {
  color: #666;
  font-size: 0.85rem;
}

.recommendation-text {
  margin: 12px 0 16px 0;
  line-height: 1.6;
}

.recommendation-text .ant-typography {
  color: #333 !important;
  font-size: 0.95rem;
  margin: 0;
}

.recommendation-actions {
  display: flex;
  justify-content: flex-end;
}

.detail-button {
  color: #52c41a !important;
  font-weight: 500;
  padding: 0;
  height: auto;
}

.detail-button:hover {
  color: #389e0d !important;
}

/* 优先级指示器 */
.priority-indicator {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  border-radius: 2px;
  opacity: 0.8;
}

/* 优先级特定样式 */
.priority-high .priority-indicator {
  background: #ff4d4f;
}

.priority-medium .priority-indicator {
  background: #faad14;
}

.priority-low .priority-indicator {
  background: #52c41a;
}

.priority-default .priority-indicator {
  background: #d9d9d9;
}

/* 统计信息 */
.recommendations-summary {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid rgba(183, 235, 143, 0.3);
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .recommendation-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .header-left {
    width: 100%;
  }
  
  .timeline-info {
    margin-top: 8px;
  }
}

@media (max-width: 480px) {
  .recommendations-list .ant-list-item {
    padding: 16px 0 !important;
  }
  
  .recommendation-text .ant-typography {
    font-size: 0.9rem;
  }
  
  .recommendation-number {
    width: 24px;
    height: 24px;
    font-size: 0.8rem;
  }
  
  .header-left {
    gap: 8px;
  }
}