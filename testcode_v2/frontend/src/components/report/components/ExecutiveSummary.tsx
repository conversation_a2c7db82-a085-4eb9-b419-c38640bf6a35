/**
 * 执行摘要组件
 * 显示报告的核心摘要和关键要点
 */
import React from 'react';
import { Card, Typography, List, Space } from 'antd';
import { BulbOutlined, CheckCircleOutlined } from '@ant-design/icons';
import './ExecutiveSummary.css';

const { Title, Paragraph } = Typography;

interface ExecutiveSummaryData {
  title: string;
  content: string;
  key_points?: string[];
}

interface ExecutiveSummaryProps {
  data: ExecutiveSummaryData;
  onInteraction?: (action: string, data: any) => void;
}

const ExecutiveSummary: React.FC<ExecutiveSummaryProps> = ({ data, onInteraction }) => {
  const { title, content, key_points = [] } = data;

  return (
    <div className="executive-summary-container">
      <Card 
        className="executive-summary-card"
        bordered={false}
      >
        {/* 标题区域 */}
        <div className="summary-header">
          <Space align="center" size="middle">
            <div className="header-icon">
              <BulbOutlined />
            </div>
            <Title level={3} className="summary-title">
              {title}
            </Title>
          </Space>
        </div>

        {/* 摘要内容 */}
        <div className="summary-content">
          <Paragraph className="summary-text">
            {content}
          </Paragraph>
        </div>

        {/* 关键要点 */}
        {key_points.length > 0 && (
          <div className="key-points-section">
            <Title level={5} className="key-points-title">
              关键要点
            </Title>
            <List
              className="key-points-list"
              dataSource={key_points}
              renderItem={(point, index) => (
                <List.Item className="key-point-item">
                  <Space align="start" size="middle">
                    <CheckCircleOutlined className="point-icon" />
                    <span className="point-text">{point}</span>
                  </Space>
                </List.Item>
              )}
            />
          </div>
        )}

        {/* 装饰性元素 */}
        <div className="summary-decoration">
          <div className="decoration-pattern" />
        </div>
      </Card>
    </div>
  );
};

export { ExecutiveSummary };