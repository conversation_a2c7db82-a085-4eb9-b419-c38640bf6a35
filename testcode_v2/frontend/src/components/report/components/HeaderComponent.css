/* 报告头部组件样式 */
.report-header {
  text-align: center;
  padding: 40px 0 60px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.report-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.header-main {
  position: relative;
  z-index: 1;
  margin-bottom: 32px;
}

.report-title {
  color: white !important;
  margin-bottom: 16px !important;
  font-size: 2.5rem !important;
  font-weight: 700 !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  line-height: 1.2;
}

.report-subtitle {
  color: rgba(255, 255, 255, 0.9) !important;
  font-size: 1.2rem !important;
  font-weight: 400;
  display: block;
  margin-top: 8px;
}

.header-meta {
  position: relative;
  z-index: 1;
  margin-bottom: 32px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.9);
}

.meta-icon {
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;
}

.report-tag {
  background: rgba(255, 255, 255, 0.2) !important;
  color: white !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  backdrop-filter: blur(10px);
  font-weight: 500;
}

.header-stats {
  position: relative;
  z-index: 1;
  margin-top: 32px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 24px;
  max-width: 600px;
  margin: 0 auto;
}

.stat-item {
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 20px 16px;
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

.stat-value {
  font-size: 1.8rem;
  font-weight: 700;
  color: white;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.header-divider {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(255, 255, 255, 0.3) 20%, 
    rgba(255, 255, 255, 0.6) 50%, 
    rgba(255, 255, 255, 0.3) 80%, 
    transparent 100%
  );
}

/* 响应式设计 */
@media (max-width: 768px) {
  .report-header {
    padding: 32px 16px 48px 16px;
  }
  
  .report-title {
    font-size: 2rem !important;
  }
  
  .report-subtitle {
    font-size: 1rem !important;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 16px;
  }
  
  .stat-item {
    padding: 16px 12px;
  }
  
  .stat-value {
    font-size: 1.5rem;
  }
  
  .stat-label {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .header-meta .ant-space {
    justify-content: center;
  }
  
  .meta-item {
    flex-direction: column;
    text-align: center;
    gap: 4px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}