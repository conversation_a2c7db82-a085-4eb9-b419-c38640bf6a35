/* 执行摘要组件样式 */
.executive-summary-container {
  position: relative;
}

.executive-summary-card {
  background: linear-gradient(135deg, #f8f9ff 0%, #e6f7ff 100%) !important;
  border-left: 6px solid #1890ff !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.1) !important;
  position: relative;
  overflow: hidden;
}

.executive-summary-card .ant-card-body {
  padding: 32px !important;
  position: relative;
  z-index: 1;
}

/* 标题区域 */
.summary-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(24, 144, 255, 0.1);
}

.header-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.summary-title {
  color: #1890ff !important;
  margin: 0 !important;
  font-weight: 600 !important;
  font-size: 1.4rem !important;
}

/* 摘要内容 */
.summary-content {
  margin-bottom: 28px;
}

.summary-text {
  font-size: 1.1rem !important;
  line-height: 1.8 !important;
  color: #333 !important;
  margin: 0 !important;
  text-align: justify;
  font-weight: 400;
}

/* 关键要点 */
.key-points-section {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  padding: 20px;
  border: 1px solid rgba(24, 144, 255, 0.1);
}

.key-points-title {
  color: #1890ff !important;
  margin: 0 0 16px 0 !important;
  font-size: 1.1rem !important;
  font-weight: 600 !important;
  display: flex;
  align-items: center;
  gap: 8px;
}

.key-points-title::before {
  content: '✨';
  font-size: 16px;
}

.key-points-list {
  margin: 0;
}

.key-points-list .ant-list-item {
  border-bottom: 1px solid rgba(24, 144, 255, 0.08) !important;
  padding: 12px 0 !important;
}

.key-points-list .ant-list-item:last-child {
  border-bottom: none !important;
}

.key-point-item {
  transition: all 0.3s ease;
}

.key-point-item:hover {
  background: rgba(24, 144, 255, 0.05);
  border-radius: 6px;
  padding-left: 8px !important;
  padding-right: 8px !important;
}

.point-icon {
  color: #52c41a;
  font-size: 16px;
  margin-top: 2px;
}

.point-text {
  color: #333;
  font-size: 0.95rem;
  line-height: 1.6;
  font-weight: 500;
}

/* 装饰性元素 */
.summary-decoration {
  position: absolute;
  top: 0;
  right: 0;
  width: 120px;
  height: 120px;
  overflow: hidden;
  pointer-events: none;
}

.decoration-pattern {
  position: absolute;
  top: -60px;
  right: -60px;
  width: 120px;
  height: 120px;
  background: radial-gradient(circle, rgba(24, 144, 255, 0.1) 0%, transparent 70%);
  border-radius: 50%;
}

.decoration-pattern::before {
  content: '';
  position: absolute;
  top: 20px;
  left: 20px;
  width: 80px;
  height: 80px;
  background: radial-gradient(circle, rgba(24, 144, 255, 0.05) 0%, transparent 70%);
  border-radius: 50%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .executive-summary-card .ant-card-body {
    padding: 24px 20px !important;
  }
  
  .summary-title {
    font-size: 1.2rem !important;
  }
  
  .summary-text {
    font-size: 1rem !important;
    line-height: 1.7 !important;
  }
  
  .key-points-section {
    padding: 16px;
  }
  
  .header-icon {
    width: 36px;
    height: 36px;
    font-size: 16px;
  }
  
  .decoration-pattern {
    width: 80px;
    height: 80px;
    top: -40px;
    right: -40px;
  }
}

@media (max-width: 480px) {
  .executive-summary-card .ant-card-body {
    padding: 20px 16px !important;
  }
  
  .summary-header {
    margin-bottom: 20px;
    padding-bottom: 12px;
  }
  
  .summary-title {
    font-size: 1.1rem !important;
  }
  
  .summary-text {
    font-size: 0.95rem !important;
  }
  
  .key-points-section {
    padding: 12px;
  }
  
  .key-points-title {
    font-size: 1rem !important;
  }
  
  .point-text {
    font-size: 0.9rem;
  }
}

/* 打印样式 */
@media print {
  .executive-summary-card {
    background: white !important;
    border-left: 4px solid #333 !important;
    box-shadow: none !important;
  }
  
  .summary-title {
    color: #333 !important;
  }
  
  .key-points-title {
    color: #333 !important;
  }
  
  .header-icon {
    background: #f0f0f0 !important;
    color: #333 !important;
  }
  
  .decoration-pattern {
    display: none;
  }
}