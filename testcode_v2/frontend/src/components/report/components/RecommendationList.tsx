/**
 * 建议列表组件
 * 显示行动建议和推荐方案
 */
import React from 'react';
import { Card, Typography, List, Tag, Space, Button } from 'antd';
import { 
  CheckCircleOutlined, 
  ClockCircleOutlined,
  StarOutlined,
  ArrowRightOutlined 
} from '@ant-design/icons';
import './RecommendationList.css';

const { Title, Text } = Typography;

interface RecommendationData {
  area: string;
  text: string;
  priority?: 'high' | 'medium' | 'low';
  timeline?: string;
}

interface RecommendationListData {
  title: string;
  recommendations: RecommendationData[];
}

interface RecommendationListProps {
  data: RecommendationListData;
  onInteraction?: (action: string, data: any) => void;
}

const RecommendationList: React.FC<RecommendationListProps> = ({ data, onInteraction }) => {
  const { title, recommendations } = data;

  // 获取优先级颜色
  const getPriorityColor = (priority?: string) => {
    switch (priority) {
      case 'high':
        return '#ff4d4f';
      case 'medium':
        return '#faad14';
      case 'low':
        return '#52c41a';
      default:
        return '#d9d9d9';
    }
  };

  // 获取优先级标签
  const getPriorityLabel = (priority?: string) => {
    const labels = {
      high: '高优先级',
      medium: '中优先级',
      low: '低优先级'
    };
    return priority ? labels[priority as keyof typeof labels] : '';
  };

  // 处理建议点击
  const handleRecommendationClick = (recommendation: RecommendationData, index: number) => {
    if (onInteraction) {
      onInteraction('recommendation_click', { recommendation, index });
    }
  };

  return (
    <div className="recommendation-list-container">
      <Card 
        className="recommendation-list-card"
        title={
          <div className="recommendation-list-header">
            <Space align="center" size="middle">
              <div className="header-icon">
                <StarOutlined />
              </div>
              <Title level={3} className="recommendation-list-title">
                {title}
              </Title>
            </Space>
          </div>
        }
        bordered={false}
      >
        <List
          className="recommendations-list"
          dataSource={recommendations}
          renderItem={(recommendation, index) => (
            <List.Item 
              className={`recommendation-item priority-${recommendation.priority || 'default'}`}
              onClick={() => handleRecommendationClick(recommendation, index)}
            >
              <div className="recommendation-content">
                {/* 建议头部 */}
                <div className="recommendation-header">
                  <Space align="center" size="middle" className="header-left">
                    <div className="recommendation-number">
                      {index + 1}
                    </div>
                    
                    <Tag color="blue" className="area-tag">
                      {recommendation.area}
                    </Tag>
                    
                    {recommendation.priority && (
                      <Tag 
                        color={getPriorityColor(recommendation.priority)}
                        className="priority-tag"
                      >
                        {getPriorityLabel(recommendation.priority)}
                      </Tag>
                    )}
                  </Space>

                  {recommendation.timeline && (
                    <Space align="center" size="small" className="timeline-info">
                      <ClockCircleOutlined />
                      <Text type="secondary">{recommendation.timeline}</Text>
                    </Space>
                  )}
                </div>

                {/* 建议文本 */}
                <div className="recommendation-text">
                  <Text>{recommendation.text}</Text>
                </div>

                {/* 操作按钮 */}
                <div className="recommendation-actions">
                  <Button 
                    type="link" 
                    size="small"
                    icon={<ArrowRightOutlined />}
                    className="detail-button"
                  >
                    查看详情
                  </Button>
                </div>
              </div>

              {/* 优先级指示器 */}
              <div 
                className="priority-indicator"
                style={{ background: getPriorityColor(recommendation.priority) }}
              />
            </List.Item>
          )}
        />

        {/* 建议统计 */}
        <div className="recommendations-summary">
          <Space size="large" wrap>
            <Text type="secondary">
              总建议数: <strong>{recommendations.length}</strong>
            </Text>
            <Text type="secondary">
              高优先级: <strong>{recommendations.filter(r => r.priority === 'high').length}</strong>
            </Text>
            <Text type="secondary">
              有时间框架: <strong>{recommendations.filter(r => r.timeline).length}</strong>
            </Text>
          </Space>
        </div>
      </Card>
    </div>
  );
};

export { RecommendationList };