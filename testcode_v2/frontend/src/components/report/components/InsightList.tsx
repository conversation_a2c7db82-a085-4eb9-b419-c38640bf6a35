/**
 * 洞察列表组件
 * 显示业务洞察和发现
 */
import React from 'react';
import { Card, Typography, List, Tag, Progress, Space } from 'antd';
import { 
  RiseOutlined, 
  ExclamationCircleOutlined, 
  BulbOutlined, 
  WarningOutlined,
  LinkOutlined 
} from '@ant-design/icons';
import './InsightList.css';

const { Title, Text } = Typography;

interface InsightData {
  type: 'trend' | 'anomaly' | 'opportunity' | 'risk' | 'correlation';
  text: string;
  confidence: number;
  impact?: 'high' | 'medium' | 'low';
  category?: string;
}

interface InsightListData {
  title: string;
  insights: InsightData[];
}

interface InsightListProps {
  data: InsightListData;
  onInteraction?: (action: string, data: any) => void;
}

const InsightList: React.FC<InsightListProps> = ({ data, onInteraction }) => {
  const { title, insights } = data;

  // 获取洞察类型图标
  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'trend':
        return <RiseOutlined />;
      case 'anomaly':
        return <ExclamationCircleOutlined />;
      case 'opportunity':
        return <BulbOutlined />;
      case 'risk':
        return <WarningOutlined />;
      case 'correlation':
        return <LinkOutlined />;
      default:
        return <BulbOutlined />;
    }
  };

  // 获取洞察类型颜色
  const getInsightColor = (type: string) => {
    switch (type) {
      case 'trend':
        return '#1890ff';
      case 'anomaly':
        return '#fa8c16';
      case 'opportunity':
        return '#52c41a';
      case 'risk':
        return '#ff4d4f';
      case 'correlation':
        return '#722ed1';
      default:
        return '#666';
    }
  };

  // 获取洞察类型标签
  const getInsightTypeLabel = (type: string) => {
    const labels = {
      trend: '趋势',
      anomaly: '异常',
      opportunity: '机会',
      risk: '风险',
      correlation: '关联'
    };
    return labels[type as keyof typeof labels] || type;
  };

  // 获取影响程度颜色
  const getImpactColor = (impact?: string) => {
    switch (impact) {
      case 'high':
        return '#ff4d4f';
      case 'medium':
        return '#faad14';
      case 'low':
        return '#52c41a';
      default:
        return '#d9d9d9';
    }
  };

  // 获取影响程度标签
  const getImpactLabel = (impact?: string) => {
    const labels = {
      high: '高影响',
      medium: '中影响',
      low: '低影响'
    };
    return impact ? labels[impact as keyof typeof labels] : '';
  };

  // 处理洞察点击
  const handleInsightClick = (insight: InsightData, index: number) => {
    if (onInteraction) {
      onInteraction('insight_click', { insight, index });
    }
  };

  return (
    <div className="insight-list-container">
      <Card 
        className="insight-list-card"
        title={
          <div className="insight-list-header">
            <Space align="center" size="middle">
              <div className="header-icon">
                <BulbOutlined />
              </div>
              <Title level={3} className="insight-list-title">
                {title}
              </Title>
            </Space>
          </div>
        }
        bordered={false}
      >
        <List
          className="insights-list"
          dataSource={insights}
          renderItem={(insight, index) => (
            <List.Item 
              className={`insight-item insight-${insight.type}`}
              onClick={() => handleInsightClick(insight, index)}
            >
              <div className="insight-content">
                {/* 洞察头部 */}
                <div className="insight-header">
                  <Space align="center" size="middle">
                    <div 
                      className="insight-type-icon"
                      style={{ color: getInsightColor(insight.type) }}
                    >
                      {getInsightIcon(insight.type)}
                    </div>
                    
                    <Space size="small" wrap>
                      <Tag 
                        color={getInsightColor(insight.type)}
                        className="insight-type-tag"
                      >
                        {getInsightTypeLabel(insight.type)}
                      </Tag>
                      
                      {insight.impact && (
                        <Tag 
                          color={getImpactColor(insight.impact)}
                          className="insight-impact-tag"
                        >
                          {getImpactLabel(insight.impact)}
                        </Tag>
                      )}
                      
                      {insight.category && (
                        <Tag className="insight-category-tag">
                          {insight.category}
                        </Tag>
                      )}
                    </Space>
                  </Space>
                </div>

                {/* 洞察文本 */}
                <div className="insight-text">
                  <Text>{insight.text}</Text>
                </div>

                {/* 置信度 */}
                <div className="insight-confidence">
                  <Space align="center" size="small">
                    <Text type="secondary" className="confidence-label">
                      置信度:
                    </Text>
                    <Progress
                      percent={Math.round(insight.confidence * 100)}
                      size="small"
                      strokeColor={
                        insight.confidence >= 0.8 ? '#52c41a' :
                        insight.confidence >= 0.6 ? '#faad14' : '#ff4d4f'
                      }
                      className="confidence-progress"
                    />
                    <Text type="secondary" className="confidence-value">
                      {Math.round(insight.confidence * 100)}%
                    </Text>
                  </Space>
                </div>
              </div>

              {/* 洞察装饰 */}
              <div 
                className="insight-decoration"
                style={{ background: getInsightColor(insight.type) }}
              />
            </List.Item>
          )}
        />

        {/* 统计信息 */}
        <div className="insights-summary">
          <Space size="large" wrap>
            <Text type="secondary">
              总计洞察: <strong>{insights.length}</strong>
            </Text>
            <Text type="secondary">
              高置信度: <strong>{insights.filter(i => i.confidence >= 0.8).length}</strong>
            </Text>
            <Text type="secondary">
              高影响: <strong>{insights.filter(i => i.impact === 'high').length}</strong>
            </Text>
          </Space>
        </div>
      </Card>
    </div>
  );
};

export { InsightList };