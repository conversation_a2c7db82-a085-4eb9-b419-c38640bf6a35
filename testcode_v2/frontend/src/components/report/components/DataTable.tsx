/**
 * 数据表格组件
 * 显示结构化的数据表格
 */
import React from 'react';
import { Card, Table, Typography, Button, Space } from 'antd';
import { DownloadOutlined, TableOutlined } from '@ant-design/icons';

const { Title } = Typography;

interface DataTableData {
  title: string;
  headers: string[];
  rows: string[][];
  description?: string;
}

interface DataTableProps {
  data: DataTableData;
  onInteraction?: (action: string, data: any) => void;
}

const DataTable: React.FC<DataTableProps> = ({ data, onInteraction }) => {
  const { title, headers, rows, description } = data;

  // 构建表格列配置
  const columns = headers.map((header, index) => ({
    title: header,
    dataIndex: `col_${index}`,
    key: `col_${index}`,
    ellipsis: true,
  }));

  // 构建表格数据
  const tableData = rows.map((row, rowIndex) => {
    const rowData: any = { key: rowIndex };
    row.forEach((cell, cellIndex) => {
      rowData[`col_${cellIndex}`] = cell;
    });
    return rowData;
  });

  // 导出CSV
  const exportCSV = () => {
    const csvContent = [headers, ...rows]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `${title.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '_')}.csv`;
    link.click();

    if (onInteraction) {
      onInteraction('export_csv', { title, rows: rows.length });
    }
  };

  return (
    <Card 
      title={
        <Space align="center" size="middle">
          <TableOutlined />
          <Title level={4} style={{ margin: 0 }}>
            {title}
          </Title>
        </Space>
      }
      extra={
        <Button 
          type="text" 
          icon={<DownloadOutlined />}
          onClick={exportCSV}
          title="导出CSV"
        >
          导出
        </Button>
      }
      bordered={false}
    >
      {description && (
        <div style={{ marginBottom: 16, color: '#666' }}>
          {description}
        </div>
      )}
      
      <Table
        columns={columns}
        dataSource={tableData}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条记录`,
        }}
        scroll={{ x: true }}
        size="middle"
      />
    </Card>
  );
};

export { DataTable };