/**
 * 文本段落组件
 * 显示报告中的文本内容
 */
import React from 'react';
import { Typography } from 'antd';

const { Title, Paragraph } = Typography;

interface TextSectionData {
  title?: string;
  content: string;
  level?: number;
}

interface TextSectionProps {
  data: TextSectionData;
  onInteraction?: (action: string, data: any) => void;
}

const TextSection: React.FC<TextSectionProps> = ({ data }) => {
  const { title, content, level = 1 } = data;

  return (
    <div className="text-section">
      {title && (
        <Title level={Math.min(Math.max(level, 1), 5) as 1 | 2 | 3 | 4 | 5}>
          {title}
        </Title>
      )}
      <Paragraph style={{ fontSize: '1rem', lineHeight: 1.7 }}>
        {content}
      </Paragraph>
    </div>
  );
};

export { TextSection };