/* 语言切换组件样式 */
.language-switch {
  .ant-select-selector {
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    transition: all 0.3s;
  }

  .ant-select-selector:hover {
    border-color: #40a9ff;
  }

  .ant-select-focused .ant-select-selector {
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
}

/* 语言选项样式 */
.language-option {
  display: flex;
  align-items: center;
  gap: 6px;
  
  .language-flag {
    font-size: 16px;
  }
  
  .language-label {
    font-weight: 500;
  }
}

/* 顶部导航区域的语言切换器 */
.sidebar-header-right {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .language-switch {
    min-width: 100px;
  }
  
  .user-dropdown {
    .ant-btn {
      border: none;
      box-shadow: none;
      padding: 4px 8px;
      
      &:hover {
        background-color: rgba(0, 0, 0, 0.04);
      }
    }
  }
}

/* 适配小屏幕 */
@media (max-width: 768px) {
  .sidebar-header-right {
    .language-switch {
      min-width: 80px;
    }
    
    .user-dropdown .ant-btn span:not(.anticon) {
      display: none;
    }
  }
}