import React, { useState, useEffect } from 'react';
import { Button, Empty, Spin, message, Popconfirm } from 'antd';
import { PlusOutlined, DeleteOutlined, MessageOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { conversationApi } from '../services/api';

interface Conversation {
  id: string;
  title: string;
  project_id: string;
  created_at: string;
  updated_at: string;
  status: 'active' | 'archived';
  rounds_count?: number;
  last_query?: string;
  last_analysis_at?: string;
}

interface SidebarConversationsProps {
  projectId: string;
  currentConversationId?: string;
  onSelectConversation: (conversationId: string) => void;
  onNewConversation: () => void;
  refreshTrigger?: number;
}

const SidebarConversations: React.FC<SidebarConversationsProps> = ({
  projectId,
  currentConversationId,
  onSelectConversation,
  onNewConversation,
  refreshTrigger
}) => {
  const { t } = useTranslation();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [deletingId, setDeletingId] = useState<string>('');
  const [isSelecting, setIsSelecting] = useState(false); // 新增：防止快速点击的状态锁定
  const [pagination, setPagination] = useState({
    page: 1,
    page_size: 20,
    total_count: 0,
    total_pages: 0,
    has_next: false
  });

  // 加载会话列表
  const loadConversations = async (page: number = 1, append: boolean = false) => {
    if (!projectId) return;
    
    try {
      if (append) {
        setLoadingMore(true);
      } else {
        setLoading(true);
      }
      
      const response = await conversationApi.getProjectConversations(projectId, {
        page,
        page_size: pagination.page_size
      });
      
      console.log('API响应:', response); // 调试日志
      
      // 解析API响应数据
      let conversationList: Conversation[] = [];
      let paginationInfo = {
        page: 1,
        page_size: 20,
        total_count: 0,
        total_pages: 0,
        has_next: false
      };
      
      if (response?.data) {
        if (response.data.data && response.data.data.conversations && Array.isArray(response.data.data.conversations)) {
          // 新的API响应格式：{ data: { conversations: [...], pagination: {...} } }
          conversationList = response.data.data.conversations;
          if (response.data.data.pagination) {
            paginationInfo = response.data.data.pagination;
          }
        } else if (response.data.conversations && Array.isArray(response.data.conversations)) {
          // 直接的conversations格式：{ conversations: [...], pagination: {...} }
          conversationList = response.data.conversations;
          if (response.data.pagination) {
            paginationInfo = response.data.pagination;
          }
        } else if (Array.isArray(response.data)) {
          // 兼容旧版本API响应格式：直接是数组
          conversationList = response.data;
        }
      }
      
      console.log('解析后的会话列表:', conversationList); // 调试日志
      console.log('分页信息:', paginationInfo); // 调试日志
      
      if (append) {
        setConversations(prev => [...prev, ...conversationList]);
      } else {
        setConversations(conversationList);
      }
      
      setPagination(paginationInfo);
    } catch (error) {
      console.error('加载会话列表失败:', error);
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  // 分离项目变化和刷新触发的逻辑
  useEffect(() => {
    // 项目变化时完全重新加载
    setPagination(prev => ({ ...prev, page: 1 }));
    loadConversations(1, false);
  }, [projectId]); // 只监听项目变化

  // 单独处理刷新触发，使用增量更新
  useEffect(() => {
    if (refreshTrigger && refreshTrigger > 0) {
      // 使用增量更新而不是完全重新加载
      refreshConversationsIncrementally();
    }
  }, [refreshTrigger]);

  // 增量刷新会话列表（丝滑更新）
  const refreshConversationsIncrementally = async () => {
    if (!projectId) return;
    
    try {
      // 静默获取最新的会话列表（不显示loading）
      const response = await conversationApi.getProjectConversations(projectId, {
        page: 1,
        page_size: pagination.page_size
      });
      
      // 解析API响应数据
      let latestConversations: Conversation[] = [];
      let paginationInfo = {
        page: 1,
        page_size: 20,
        total_count: 0,
        total_pages: 0,
        has_next: false
      };
      
      if (response?.data) {
        if (response.data.data && response.data.data.conversations && Array.isArray(response.data.data.conversations)) {
          latestConversations = response.data.data.conversations;
          if (response.data.data.pagination) {
            paginationInfo = response.data.data.pagination;
          }
        } else if (response.data.conversations && Array.isArray(response.data.conversations)) {
          latestConversations = response.data.conversations;
          if (response.data.pagination) {
            paginationInfo = response.data.pagination;
          }
        } else if (Array.isArray(response.data)) {
          latestConversations = response.data;
        }
      }
      
      // 智能合并：只添加新的会话，保持现有会话的顺序
      const existingIds = new Set(conversations.map(conv => conv.id));
      const newConversations = latestConversations.filter(conv => !existingIds.has(conv.id));
      
      if (newConversations.length > 0) {
        // 将新会话添加到列表顶部，保持现有会话不变
        setConversations(prev => [...newConversations, ...prev]);
        console.log(`🔄 增量更新：添加了 ${newConversations.length} 个新会话`);
      }
      
      // 更新分页信息
      setPagination(paginationInfo);
      
    } catch (error) {
      console.error('增量刷新会话列表失败:', error);
      // 静默失败，不显示错误信息，避免打断用户体验
    }
  };

  // 加载更多会话
  const loadMoreConversations = () => {
    if (!loadingMore && pagination.has_next) {
      loadConversations(pagination.page + 1, true);
    }
  };

  // 删除会话
  const handleDeleteConversation = async (conversationId: string) => {
    try {
      setDeletingId(conversationId);
      await conversationApi.deleteConversation(conversationId);
      message.success(t('common:sidebar.deleteSuccess'));
      // 重新加载会话列表
      setPagination(prev => ({ ...prev, page: 1 }));
      loadConversations(1, false);
      // 如果删除的是当前会话，清空选中状态
      if (conversationId === currentConversationId) {
        onNewConversation();
      }
    } catch (error) {
      console.error('删除会话失败:', error);
      message.error(t('common:sidebar.deleteError'));
    } finally {
      setDeletingId('');
    }
  };

  // 格式化时间显示
  const formatTime = (timeStr: string) => {
    const time = new Date(timeStr);
    const now = new Date();
    const diffMs = now.getTime() - time.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      return time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    } else if (diffDays === 1) {
      return '昨天';
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else {
      return time.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });
    }
  };

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', padding: '20px' }}>
        <Spin size="small" />
      </div>
    );
  }

  return (
    <div className="sidebar-conversations-container">
      {/* 标题区域 */}
      <div className="sidebar-conversations-header">
        <div className="sidebar-section-title">
          <MessageOutlined style={{ marginRight: 6 }} />
          {t('common:sidebar.chatHistory')}
        </div>
        <Button 
          type="primary" 
          icon={<PlusOutlined />}
          onClick={() => {
            console.log('SidebarConversations: 加号按钮被点击');
            onNewConversation();
          }}
          className="new-conversation-btn"
          size="small"
          shape="circle"
        />
      </div>

      {/* 对话列表 */}
      <div className="sidebar-conversations-list">
        {loading && conversations.length === 0 ? (
          <div style={{ display: 'flex', justifyContent: 'center', padding: '20px' }}>
            <Spin size="small" />
          </div>
        ) : conversations.length === 0 ? (
          <div style={{ textAlign: 'center', padding: '20px 0' }}>
            <Empty 
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={t('common:sidebar.noConversations')}
              style={{ margin: 0 }}
            />
          </div>
        ) : (
          <>
            {conversations.map((conversation, index) => (
              <div
                key={conversation.id}
                className={`conversation-item ${currentConversationId === conversation.id ? 'active' : ''}`}
                style={{
                  animation: index < 3 ? 'slideInFromTop 0.3s ease-out' : 'none',
                  transition: 'all 0.2s ease-in-out'
                }}
              >
                <div 
                  className="conversation-content"
                  onClick={() => {
                    // 防抖保护：如果正在选择会话，忽略新的选择请求
                    if (isSelecting) {
                      console.log('正在选择会话中，忽略新的选择请求');
                      return;
                    }
                    
                    // 如果选择的是当前已经选中的会话，直接返回
                    if (currentConversationId === conversation.id) {
                      console.log('选择的会话已经是当前会话，无需切换');
                      return;
                    }
                    
                    // 设置选择状态锁定
                    setIsSelecting(true);
                    
                    try {
                      console.log('SidebarConversations: 会话被选择:', conversation.id);
                      onSelectConversation(conversation.id);
                    } finally {
                      // 释放选择状态锁定（延迟释放，避免过快的连续点击）
                      setTimeout(() => {
                        setIsSelecting(false);
                      }, 300);
                    }
                  }}
                >
                  <div className="conversation-title">
                    {conversation.title || conversation.last_query || '新对话'}
                  </div>
                </div>
                <div className="conversation-actions">
                  <Popconfirm
                    title={t('common:confirm.delete')}
                    description={t('common:sidebar.deleteConversationConfirm')}
                    onConfirm={() => handleDeleteConversation(conversation.id)}
                    okText={t('common:button.delete')}
                    cancelText={t('common:button.cancel')}
                    okButtonProps={{ danger: true }}
                  >
                    <Button
                      type="text"
                      icon={<DeleteOutlined />}
                      size="small"
                      className="conversation-delete-btn"
                      loading={deletingId === conversation.id}
                      onClick={(e) => e.stopPropagation()}
                    />
                  </Popconfirm>
                </div>
              </div>
            ))}
            
            {/* 加载更多按钮 */}
            {pagination.has_next && (
              <div className="load-more-container">
                <Button
                  type="text"
                  onClick={loadMoreConversations}
                  loading={loadingMore}
                  className="load-more-btn"
                  block
                >
                  {loadingMore ? t('common:messages.loading') : t('common:sidebar.loadMore')}
                </Button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default SidebarConversations; 