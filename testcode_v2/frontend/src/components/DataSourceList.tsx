import React, { useEffect, useState } from 'react';
import { Button, Table, Space, Modal, Form, Input, Select, message, Empty, Spin } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, LinkOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { DataSource, CreateDataSourceRequest, DataSourceType } from '../types';
import { dataSourceApi } from '../services/api';

const { Option } = Select;

interface DataSourceListProps {
  projectId: string;
}

const DataSourceList: React.FC<DataSourceListProps> = ({ projectId }) => {
  const { t } = useTranslation();
  const [dataSources, setDataSources] = useState<DataSource[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [testConnectionModalVisible, setTestConnectionModalVisible] = useState<boolean>(false);
  const [editingDataSource, setEditingDataSource] = useState<DataSource | null>(null);
  const [testConnectionLoading, setTestConnectionLoading] = useState<boolean>(false);
  const [form] = Form.useForm();
  const [testForm] = Form.useForm();

  const fetchDataSources = async () => {
    if (!projectId) {
      setLoading(false);
      return;
    }
    
    setLoading(true);
    try {
      const response = await dataSourceApi.getDataSources({ project_id: projectId });
      console.log('DataSource response:', response);
      
      if (response.data.data.items) {
        // 直接使用response.data，它应当是数组
        setDataSources(Array.isArray(response.data.data.items) ? response.data.data.items : []);
      } else {
        message.error(t('common:dataSource.fetchFailed') + ': ' + t('common:messages.interfaceResponseError'));
      }
    } catch (error) {
      console.error('Failed to fetch data source list', error);
      message.error(t('common:dataSource.fetchFailed'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (projectId) {
      fetchDataSources();
    } else {
      setLoading(false);
    }
  }, [projectId]);

  const handleAddDataSource = () => {
    setEditingDataSource(null);
    form.resetFields();
    form.setFieldsValue({ project_id: projectId });
    setModalVisible(true);
  };

  const handleEditDataSource = (dataSource: DataSource) => {
    setEditingDataSource(dataSource);
    form.setFieldsValue({
      name: dataSource.name,
      description: dataSource.description,
      type: dataSource.type,
      config: typeof dataSource.config === 'string' 
        ? dataSource.config 
        : JSON.stringify(dataSource.config, null, 2),
    });
    setModalVisible(true);
  };

  const handleDeleteDataSource = async (id: string) => {
    Modal.confirm({
      title: t('common:dataSource.confirmDelete'),
      content: t('common:dataSource.deleteMessage'),
      okText: t('common:buttons.confirm'),
      cancelText: t('common:buttons.cancel'),
      onOk: async () => {
        try {
          await dataSourceApi.deleteDataSource(id);
          message.success(t('common:dataSource.deleteSuccess'));
          fetchDataSources();
        } catch (error) {
          message.error(t('common:dataSource.deleteFailed'));
          console.error(error);
        }
      },
    });
  };

  const handleTestConnection = (dataSource: DataSource) => {
    setEditingDataSource(dataSource);
    testForm.setFieldsValue({
      type: dataSource.type,
      config: typeof dataSource.config === 'string' 
        ? dataSource.config 
        : JSON.stringify(dataSource.config, null, 2),
    });
    setTestConnectionModalVisible(true);
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      
      // 确保配置是对象
      try {
        if (typeof values.config === 'string') {
          values.config = JSON.parse(values.config);
        }
      } catch (error) {
        message.error(t('common:dataSource.configFormatError'));
        return;
      }
      
      const payload: CreateDataSourceRequest = {
        ...values,
        project_id: projectId,
      };

      if (editingDataSource) {
        await dataSourceApi.updateDataSource(editingDataSource.id, payload);
        message.success(t('common:dataSource.updateSuccess'));
      } else {
        await dataSourceApi.createDataSource(payload);
        message.success(t('common:dataSource.createSuccess'));
      }
      
      setModalVisible(false);
      fetchDataSources();
    } catch (error) {
      console.error(error);
    }
  };

  const handleTestConnectionOk = async () => {
    try {
      const values = await testForm.validateFields();
      
      // 确保配置是对象
      try {
        if (typeof values.config === 'string') {
          values.config = JSON.parse(values.config);
        }
      } catch (error) {
        message.error(t('common:dataSource.configFormatError'));
        return;
      }
      
      setTestConnectionLoading(true);
      const response = await dataSourceApi.testConnection(editingDataSource?.id || '');
      
      if (response.data && response.data.code === 0) {
        message.success(t('common:dataSource.connectionTestSuccess'));
        setTestConnectionModalVisible(false);
      } else {
        message.error(`${t('common:dataSource.connectionTestFailed')}: ${response.data?.message || t('common:messages.unknownError')}`);
      }
    } catch (error) {
      message.error(t('common:dataSource.connectionTestFailed'));
      console.error(error);
    } finally {
      setTestConnectionLoading(false);
    }
  };

  const columns = [
    {
      title: t('common:dataSource.name'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('common:labels.description'),
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: t('common:dataSource.type'),
      dataIndex: 'type',
      key: 'type',
    },
    {
      title: t('common:dataSource.createdAt'),
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text: string) => text ? new Date(text).toLocaleString() : '-',
    },
    {
      title: t('common:dataSource.actions'),
      key: 'action',
      render: (_: any, record: DataSource) => (
        <Space size="middle">
          <Button 
            type="link" 
            icon={<LinkOutlined />} 
            onClick={() => handleTestConnection(record)}
          >
            {t('common:dataSource.testConnection')}
          </Button>
          <Button 
            type="link" 
            icon={<EditOutlined />} 
            onClick={() => handleEditDataSource(record)}
          >
            {t('common:buttons.edit')}
          </Button>
          <Button 
            type="link" 
            danger 
            icon={<DeleteOutlined />} 
            onClick={() => handleDeleteDataSource(record.id)}
          >
            {t('common:buttons.delete')}
          </Button>
        </Space>
      ),
    },
  ];

  if (!projectId) {
    return <Empty description={t('common:dataSource.pleaseSelectProject')} />;
  }

  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Button 
          type="primary" 
          icon={<PlusOutlined />} 
          onClick={handleAddDataSource}
        >
          {t('common:dataSource.add')}
        </Button>
      </div>

      <Table 
        columns={columns} 
        dataSource={dataSources} 
        rowKey="id" 
        loading={loading}
        locale={{ 
          emptyText: loading ? <Spin tip={t('common:dataSource.loading')} /> : <Empty description={t('common:dataSource.noData')} /> 
        }}
      />

      <Modal
        title={editingDataSource ? t('common:dataSource.edit') : t('common:dataSource.add')}
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={() => setModalVisible(false)}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="project_id"
            hidden
          >
            <Input />
          </Form.Item>
          
          <Form.Item
            name="name"
            label={t('common:dataSource.name')}
            rules={[{ required: true, message: t('common:dataSource.validation.nameRequired') }]}
          >
            <Input placeholder={t('common:dataSource.placeholders.enterName')} />
          </Form.Item>
          
          <Form.Item
            name="description"
            label={t('common:dataSource.description')}
          >
            <Input.TextArea placeholder={t('common:dataSource.placeholders.enterDescription')} rows={2} />
          </Form.Item>
          
          <Form.Item
            name="type"
            label={t('common:dataSource.type')}
            rules={[{ required: true, message: t('common:dataSource.validation.typeRequired') }]}
          >
            <Select placeholder={t('common:dataSource.placeholders.selectType')}>
              <Option value={DataSourceType.MYSQL}>{t('common:dataSource.types.mysql')}</Option>
              <Option value={DataSourceType.POSTGRESQL}>{t('common:dataSource.types.postgresql')}</Option>
              <Option value={DataSourceType.ORACLE}>{t('common:dataSource.types.oracle')}</Option>
              <Option value={DataSourceType.MSSQL}>{t('common:dataSource.types.mssql')}</Option>
              <Option value={DataSourceType.HTTP_API}>{t('common:dataSource.types.httpApi')}</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="config"
            label={t('common:dataSource.config')}
            rules={[{ required: true, message: t('common:dataSource.validation.configRequired') }]}
            help={t('common:dataSource.configHelp')}
          >
            <Input.TextArea placeholder={t('common:dataSource.placeholders.enterConfig')} rows={10} />
          </Form.Item>
        </Form>
      </Modal>

      <Modal
        title={t('common:dataSource.testConnection')}
        open={testConnectionModalVisible}
        onOk={handleTestConnectionOk}
        onCancel={() => setTestConnectionModalVisible(false)}
        confirmLoading={testConnectionLoading}
        width={800}
      >
        <Form
          form={testForm}
          layout="vertical"
        >
          <Form.Item
            name="type"
            label={t('common:dataSource.type')}
            rules={[{ required: true, message: t('common:dataSource.validation.typeRequired') }]}
          >
            <Select placeholder="请选择数据源类型" disabled>
              <Option value={DataSourceType.MYSQL}>MySQL</Option>
              <Option value={DataSourceType.POSTGRESQL}>PostgreSQL</Option>
              <Option value={DataSourceType.ORACLE}>Oracle</Option>
              <Option value={DataSourceType.MSSQL}>MSSQL</Option>
              <Option value={DataSourceType.HTTP_API}>HTTP API</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="config"
            label={t('common:dataSource.config')}
            rules={[{ required: true, message: t('common:dataSource.validation.configRequired') }]}
            help={t('common:dataSource.configHelp')}
          >
            <Input.TextArea placeholder={t('common:dataSource.placeholders.enterConfig')} rows={10} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default DataSourceList; 