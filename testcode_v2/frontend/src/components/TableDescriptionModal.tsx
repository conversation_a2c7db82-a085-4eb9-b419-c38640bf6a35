import React, { useState, useEffect } from 'react';
import { Modal, Table, Input, Button, message, List, Typography, Card, Spin } from 'antd';
import { SaveOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { projectWizardApi } from '../services/api';

const { TextArea } = Input;
const { Title, Text } = Typography;

interface TableInfo {
  id: string;
  table_name: string;
  table_description: string;
  table_schema: Record<string, any>;
  data_source_id: string;
}

interface TableDescriptionModalProps {
  visible: boolean;
  onClose: () => void;
  projectId: string;
  onUpdate?: () => void; // 更新完成后的回调
}

const TableDescriptionModal: React.FC<TableDescriptionModalProps> = ({
  visible,
  onClose,
  projectId,
  onUpdate,
}) => {
  const { t } = useTranslation();
  const [tables, setTables] = useState<TableInfo[]>([]);
  const [currentTable, setCurrentTable] = useState<TableInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (visible && projectId) {
      fetchSelectedTables();
    }
  }, [visible, projectId]);

  useEffect(() => {
    if (tables.length > 0 && !currentTable) {
      setCurrentTable(tables[0]);
    }
  }, [tables]);

  const fetchSelectedTables = async () => {
    try {
      setLoading(true);
      const response = await projectWizardApi.getSelectedTables(projectId);
      setTables(response.data.data);
      setLoading(false);
    } catch (error) {
      console.error('Failed to fetch selected tables:', error);
      message.error(t('common:tableDescription.fetchTablesFailed'));
      setLoading(false);
    }
  };

  const handleDescriptionChange = (description: string) => {
    if (currentTable) {
      setCurrentTable({
        ...currentTable,
        table_description: description,
      });
    }
  };

  const saveDescription = async () => {
    if (!currentTable) return;

    try {
      setSaving(true);
      const response = await projectWizardApi.updateTableDescription(
        projectId, 
        currentTable.id, 
        currentTable.table_description
      );
      
      // 更新本地表列表
      setTables(tables.map(table => 
        table.id === currentTable.id 
          ? { ...table, table_description: currentTable.table_description } 
          : table
      ));
      
      message.success(t('common:tableDescription.descriptionSaved', { tableName: currentTable.table_name }));
      setSaving(false);
      
      // 调用更新回调
      onUpdate?.();
    } catch (error) {
      console.error('Failed to save table description:', error);
      message.error(t('common:tableDescription.saveDescriptionFailed'));
      setSaving(false);
    }
  };

  const selectTable = (table: TableInfo) => {
    setCurrentTable(table);
  };

  const renderSchemaFields = () => {
    if (!currentTable || !currentTable.table_schema) return null;

    const columns = [
      {
        title: t('common:tableDescription.fieldName'),
        dataIndex: 'field',
        key: 'field',
      },
      {
        title: t('common:tableDescription.dataType'),
        dataIndex: 'type',
        key: 'type',
      },
      {
        title: t('common:tableDescription.nullable'),
        dataIndex: 'nullable',
        key: 'nullable',
        render: (text: string) => text === 'Y' ? t('common:tableDescription.yes') : t('common:tableDescription.no')
      }
    ];

    const data = Object.entries(currentTable.table_schema).map(([field, details], index) => ({
      key: index,
      field: field,
      type: (details as any).data_type,
      nullable: (details as any).nullable
    }));

    return (
      <Table 
        columns={columns} 
        dataSource={data} 
        size="small" 
        pagination={false}
        scroll={{ y: 200 }}
      />
    );
  };

  const modalContent = () => {
    if (loading) {
      return <Spin tip={t('common:tableDescription.loading')} />;
    }

    if (tables.length === 0) {
      return <div>{t('common:tableDescription.noTables')}</div>;
    }

    return (
      <div style={{ display: 'flex', height: '680px' }}>
        {/* 左侧表格列表 */}
        <div style={{ width: '30%', marginRight: '20px', overflowY: 'auto' }}>
          <List
            itemLayout="horizontal"
            dataSource={tables}
            renderItem={table => (
              <List.Item 
                onClick={() => selectTable(table)}
                style={{ 
                  cursor: 'pointer', 
                  backgroundColor: currentTable?.id === table.id ? '#f0f5ff' : 'white',
                  padding: '10px',
                  borderRadius: '4px',
                  marginBottom: '8px'
                }}
              >
                <List.Item.Meta
                  title={table.table_name}
                  description={
                    <div>
                      {table.table_description ? 
                        <Text type="success">{t('common:tableDescription.descriptionAdded')}</Text> : 
                        <Text type="secondary">{t('common:tableDescription.noDescription')}</Text>
                      }
                    </div>
                  }
                />
              </List.Item>
            )}
          />
        </div>
        
        {/* 右侧描述编辑区 */}
        <div style={{ width: '70%', display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
          {currentTable ? (
            <Card title={`${t('common:tableDescription.tablePrefix')}${currentTable.table_name}`} size="small" style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              <div style={{ flex: '1', overflowY: 'auto', paddingRight: '8px' }}>
                <div style={{ marginBottom: '16px' }}>
                  <Title level={5} style={{ marginBottom: '8px' }}>{t('common:tableDescription.schemaOverview')}</Title>
                  {renderSchemaFields()}
                </div>
                
                <div style={{ marginBottom: '16px' }}>
                  <Title level={5} style={{ marginBottom: '8px' }}>{t('common:tableDescription.tableDescription')}</Title>
                  <TextArea
                    rows={8}
                    value={currentTable.table_description || ''}
                    onChange={(e) => handleDescriptionChange(e.target.value)}
                    placeholder={t('common:tableDescription.descriptionPlaceholder')}
                  />
                </div>
              </div>
              
              <div style={{ marginTop: '12px', borderTop: '1px solid #f0f0f0', paddingTop: '12px' }}>
                <Button 
                  type="primary" 
                  icon={<SaveOutlined />} 
                  onClick={saveDescription}
                  loading={saving}
                >
                  {t('common:tableDescription.saveDescription')}
                </Button>
              </div>
            </Card>
          ) : (
            <div>{t('common:tableDescription.pleaseSelectTable')}</div>
          )}
        </div>
      </div>
    );
  };

  return (
    <Modal
      title={t('common:tableDescription.title')}
      open={visible}
      onCancel={onClose}
      width={1000}
      centered
      bodyStyle={{ padding: '16px', overflowY: 'auto' }}
      footer={[
      ]}
    >
      {modalContent()}
    </Modal>
  );
};

export default TableDescriptionModal; 