import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Select, DatePicker, Button, message } from 'antd';
import { Template } from '../services/templateApi';
import './TemplateParameterPanel.css';

const { TextArea } = Input;
const { RangePicker } = DatePicker;
const { Option } = Select;

interface TemplateParameterPanelProps {
  visible: boolean;
  template: Template | null;
  onApply: (content: string) => void;
  onCancel: () => void;
}

interface TemplateParameter {
  name: string;
  type: 'text' | 'select' | 'date' | 'dateRange' | 'number';
  label: string;
  placeholder?: string;
  options?: string[];
  required?: boolean;
  defaultValue?: any;
}

const TemplateParameterPanel: React.FC<TemplateParameterPanelProps> = ({
  visible,
  template,
  onApply,
  onCancel
}) => {
  const [form] = Form.useForm();
  const [parameters, setParameters] = useState<TemplateParameter[]>([]);
  const [previewContent, setPreviewContent] = useState<string>('');

  // 解析模板参数
  useEffect(() => {
    if (template?.content) {
      const paramRegex = /\{\{(\w+)(?::([^}]+))?\}\}/g;
      const foundParams: TemplateParameter[] = [];
      let match;

      while ((match = paramRegex.exec(template.content)) !== null) {
        const paramName = match[1];
        const paramConfig = match[2];
        
        // 避免重复参数
        if (foundParams.find(p => p.name === paramName)) {
          continue;
        }

        let param: TemplateParameter = {
          name: paramName,
          type: 'text',
          label: paramName,
          placeholder: `请输入${paramName}`,
          required: true
        };

        // 解析参数配置
        if (paramConfig) {
          const configs = paramConfig.split('|');
          configs.forEach(config => {
            const [key, value] = config.split('=');
            switch (key) {
              case 'type':
                param.type = value as any;
                break;
              case 'label':
                param.label = value;
                break;
              case 'placeholder':
                param.placeholder = value;
                break;
              case 'options':
                param.options = value.split(',');
                break;
              case 'default':
                param.defaultValue = value;
                break;
              case 'required':
                param.required = value === 'true';
                break;
            }
          });
        }

        foundParams.push(param);
      }

      setParameters(foundParams);
      
      // 设置默认值
      const defaultValues: any = {};
      foundParams.forEach(param => {
        if (param.defaultValue) {
          defaultValues[param.name] = param.defaultValue;
        }
      });
      form.setFieldsValue(defaultValues);
    }
  }, [template, form]);

  // 更新预览内容
  useEffect(() => {
    if (template?.content) {
      const values = form.getFieldsValue();
      let content = template.content;
      
      parameters.forEach(param => {
        const value = values[param.name];
        if (value !== undefined && value !== null) {
          const regex = new RegExp(`\\{\\{${param.name}(?::[^}]+)?\\}\\}`, 'g');
          content = content.replace(regex, String(value));
        }
      });
      
      setPreviewContent(content);
    }
  }, [template, parameters, form]);

  // 监听表单变化
  const handleFormChange = () => {
    if (template?.content) {
      const values = form.getFieldsValue();
      let content = template.content;
      
      parameters.forEach(param => {
        const value = values[param.name];
        if (value !== undefined && value !== null) {
          const regex = new RegExp(`\\{\\{${param.name}(?::[^}]+)?\\}\\}`, 'g');
          content = content.replace(regex, String(value));
        }
      });
      
      setPreviewContent(content);
    }
  };

  // 应用模板
  const handleApply = async () => {
    try {
      await form.validateFields();
      onApply(previewContent);
      message.success('模板已应用');
    } catch (error) {
      message.error('请填写所有必填参数');
    }
  };

  // 渲染参数输入组件
  const renderParameterInput = (param: TemplateParameter) => {
    switch (param.type) {
      case 'select':
        return (
          <Select placeholder={param.placeholder}>
            {param.options?.map(option => (
              <Option key={option} value={option}>{option}</Option>
            ))}
          </Select>
        );
      case 'date':
        return <DatePicker placeholder={param.placeholder} style={{ width: '100%' }} />;
      case 'dateRange':
        return <RangePicker placeholder={['开始日期', '结束日期']} style={{ width: '100%' }} />;
      case 'number':
        return <Input type="number" placeholder={param.placeholder} />;
      default:
        return <Input placeholder={param.placeholder} />;
    }
  };

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <span style={{ marginRight: 8 }}>⚙️</span>
          <span>参数化模板：{template?.name}</span>
        </div>
      }
      open={visible}
      onCancel={onCancel}
      width={800}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button key="apply" type="primary" onClick={handleApply}>
          应用模板
        </Button>
      ]}
    >
      <div className="template-parameter-panel">
        {parameters.length > 0 ? (
          <>
            <div className="parameter-form">
              <h4>📝 参数配置</h4>
              <Form
                form={form}
                layout="vertical"
                onValuesChange={handleFormChange}
              >
                {parameters.map(param => (
                  <Form.Item
                    key={param.name}
                    name={param.name}
                    label={param.label}
                    rules={[
                      { required: param.required, message: `请输入${param.label}` }
                    ]}
                  >
                    {renderParameterInput(param)}
                  </Form.Item>
                ))}
              </Form>
            </div>
            
            <div className="parameter-preview">
              <h4>👀 预览效果</h4>
              <div className="preview-content">
                {previewContent || template?.content}
              </div>
            </div>
          </>
        ) : (
          <div className="no-parameters">
            <p>此模板不包含可配置参数</p>
            <div className="template-content">
              {template?.content}
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default TemplateParameterPanel;
