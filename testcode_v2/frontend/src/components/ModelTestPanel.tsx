import React, { useState } from 'react';
import { 
  Modal, 
  Form, 
  Input, 
  Button, 
  Alert, 
  Typography, 
  Space, 
  Spin,
  Card,
  Divider
} from 'antd';
import { PlayCircleOutlined } from '@ant-design/icons';
import { llmModelApi } from '../services/api';

const { TextArea } = Input;
const { Text, Title } = Typography;

interface ModelTestPanelProps {
  visible: boolean;
  onClose: () => void;
  modelConfig?: any;
  modelId?: string | null;
}

interface TestResult {
  success: boolean;
  response?: string;
  error?: string;
  latency?: number;
}

const ModelTestPanel: React.FC<ModelTestPanelProps> = ({
  visible,
  onClose,
  modelConfig,
  modelId
}) => {
  const [form] = Form.useForm();
  const [testing, setTesting] = useState(false);
  const [testResult, setTestResult] = useState<TestResult | null>(null);

  // 执行测试
  const handleTest = async (values: any) => {
    try {
      setTesting(true);
      setTestResult(null);

      let response;

      if (modelConfig) {
        // 使用配置数据进行测试
        const configData = {
          name: modelConfig.name,
          model_name: modelConfig.model_name,
          api_key: modelConfig.api_key,
          base_url: modelConfig.base_url || 'https://api.openai.com/v1'
        };
        response = await llmModelApi.testModelConfig(configData);
      } else {
        throw new Error('缺少模型配置信息');
      }

      if (response.data?.code === 200) {
        setTestResult(response.data.data);
      }
    } catch (error: any) {
      console.error('测试失败:', error);
      setTestResult({
        success: false,
        error: error.message || '测试失败'
      });
    } finally {
      setTesting(false);
    }
  };

  const handleClose = () => {
    setTestResult(null);
    form.resetFields();
    onClose();
  };

  return (
    <Modal
      title="模型测试"
      open={visible}
      onCancel={handleClose}
      footer={null}
      width={800}
      destroyOnClose
    >
      <Alert
        message="点击测试按钮验证模型配置（固定发送'hello'消息）"
        type="info"
        showIcon
        style={{ marginBottom: '16px' }}
      />

      <div style={{ textAlign: 'center', marginBottom: '16px' }}>
        <Button
          type="primary"
          size="large"
          icon={<PlayCircleOutlined />}
          loading={testing}
          disabled={!modelConfig}
          onClick={() => handleTest({})}
        >
          {testing ? '测试中...' : '开始测试'}
        </Button>
        {!modelConfig && (
          <div style={{ marginTop: '8px' }}>
            <Text type="secondary">
              请提供模型配置信息
            </Text>
          </div>
        )}
      </div>

      {testResult && (
        <>
          <Divider />
          <Title level={5}>测试结果</Title>
          
          {testResult.success ? (
            <div style={{ textAlign: 'center', padding: '20px' }}>
              <div style={{ fontSize: '48px', marginBottom: '16px' }}>✅</div>
              <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#52c41a', marginBottom: '8px' }}>
                调用成功
              </div>
              <div style={{ color: '#666', marginBottom: '16px' }}>
                响应时间: {testResult.latency?.toFixed(3)}秒
              </div>
              <div style={{
                background: '#f6ffed',
                border: '1px solid #b7eb8f',
                padding: '12px',
                borderRadius: '6px',
                textAlign: 'left',
                fontFamily: 'monospace'
              }}>
                {testResult.response}
              </div>
            </div>
          ) : (
            <div style={{ textAlign: 'center', padding: '20px' }}>
              <div style={{ fontSize: '48px', marginBottom: '16px' }}>❌</div>
              <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#ff4d4f', marginBottom: '16px' }}>
                调用失败
              </div>
              <div style={{
                background: '#fff2f0',
                border: '1px solid #ffccc7',
                padding: '12px',
                borderRadius: '6px',
                textAlign: 'left',
                color: '#cf1322',
                fontFamily: 'monospace',
                fontSize: '12px'
              }}>
                {testResult.error}
              </div>
            </div>
          )}
        </>
      )}

      {testing && (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Spin size="large" />
          <div style={{ marginTop: '16px' }}>
            <Text>正在测试模型...</Text>
          </div>
        </div>
      )}
    </Modal>
  );
};

export default ModelTestPanel;
