/* 模板面板样式 */
.template-panel {
  background: white;
  border-radius: 16px;
  max-height: 600px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e1e5e9;
  background: #f8f9fa;
}

.template-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #24292f;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #656d76;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s;
}

.close-btn:hover {
  background: #f6f8fa;
  color: #24292f;
}

.template-filters {
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.search-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  margin-bottom: 16px;
  font-size: 15px;
  transition: all 0.3s ease;
  background: white;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filter-row {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-group label {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  white-space: nowrap;
}

.filter-select {
  padding: 8px 12px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  color: #374151;
  transition: all 0.3s ease;
  min-width: 120px;
}

.filter-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.category-tabs {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.category-tab {
  padding: 6px 12px;
  border: 1px solid #d0d7de;
  border-radius: 16px;
  background: white;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
  white-space: nowrap;
}

.category-tab:hover {
  background: #f6f8fa;
}

.category-tab.active {
  background: #0969da;
  color: white;
  border-color: #0969da;
}

.template-list {
  flex: 1;
  overflow-y: auto;
  padding: 20px 24px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  max-height: 500px;
}

.loading, .empty {
  grid-column: 1 / -1;
  text-align: center;
  padding: 60px 20px;
  color: #64748b;
  font-size: 15px;
}

/* 模板卡片样式 */
.template-card {
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  padding: 20px;
  transition: all 0.3s ease;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  cursor: pointer;
}

.template-card:hover {
  border-color: #667eea;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
  transform: translateY(-2px);
}

.template-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.template-name {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #24292f;
  flex: 1;
}

.system-badge, .public-badge {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: normal;
}

.system-badge {
  background: #dbeafe;
  color: #1e40af;
}

.public-badge {
  background: #dcfce7;
  color: #166534;
}

.template-meta {
  font-size: 12px;
  color: #656d76;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
  text-align: right;
}

.usage-count {
  color: #666;
  font-size: 12px;
}

.creator {
  color: #999;
  font-size: 12px;
}

.visibility {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 3px;
  font-weight: 500;
}

.visibility.public {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.visibility.private {
  background: #f0f0f0;
  color: #666;
  border: 1px solid #d9d9d9;
}

.template-description {
  color: #6b7280;
  font-size: 14px;
  margin: 12px 0;
  line-height: 1.5;
  background: #f8fafc;
  padding: 12px;
  border-radius: 8px;
  border-left: 3px solid #e2e8f0;
}

.template-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 16px;
}

.preview-btn, .use-btn {
  padding: 8px 16px;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid;
}

.preview-btn {
  border-color: #e2e8f0;
  background: white;
  color: #374151;
}

.preview-btn:hover {
  background: #f8fafc;
  border-color: #cbd5e1;
  transform: translateY(-1px);
}

.use-btn.primary {
  border-color: #667eea;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.use-btn.primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.template-preview {
  margin-top: 16px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.preview-header {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.preview-content {
  background: white;
  padding: 16px;
  border-radius: 8px;
  font-size: 14px;
  line-height: 1.6;
  max-height: 200px;
  overflow-y: auto;
  color: #374151;
  border: 1px solid #e2e8f0;
}

.preview-actions {
  display: flex;
  gap: 8px;
}

.parameter-btn {
  padding: 4px 8px;
  border: 1px solid #667eea;
  background: white;
  color: #667eea;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.parameter-btn:hover {
  background: #667eea;
  color: white;
}

/* 滚动条样式 */
.template-list::-webkit-scrollbar,
.template-content::-webkit-scrollbar {
  width: 6px;
}

.template-list::-webkit-scrollbar-track,
.template-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.template-list::-webkit-scrollbar-thumb,
.template-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.template-list::-webkit-scrollbar-thumb:hover,
.template-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .template-panel {
    max-height: 400px;
  }
  
  .template-filters {
    padding: 12px 16px;
  }
  
  .template-list {
    padding: 12px 16px;
  }
  
  .template-card {
    padding: 12px;
  }
  
  .category-tabs {
    gap: 6px;
  }
  
  .category-tab {
    padding: 4px 8px;
    font-size: 13px;
  }
  
  .template-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .template-meta {
    align-items: flex-start;
    text-align: left;
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .filter-group {
    justify-content: space-between;
  }

  .filter-select {
    min-width: auto;
    flex: 1;
  }
}

/* 新增样式：模板卡片增强 */
.template-title-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.template-badges {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.badge {
  font-size: 11px;
  padding: 3px 8px;
  border-radius: 8px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: white;
}

.badge.system {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.badge.public {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.badge.complexity {
  /* 颜色由内联样式设置 */
}

.template-stats {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 13px;
  color: #6b7280;
  margin-top: 8px;
}

/* 分类和标签样式 */
.template-meta.enhanced {
  margin-bottom: 12px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.category {
  font-size: 13px;
  color: #6b7280;
  font-weight: 500;
}

.tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.tag {
  font-size: 11px;
  padding: 2px 6px;
  background: #f3f4f6;
  color: #6b7280;
  border-radius: 6px;
  font-weight: 500;
}

.tag.more {
  background: #e5e7eb;
  color: #9ca3af;
}
