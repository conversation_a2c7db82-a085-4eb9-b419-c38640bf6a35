import React, { useState, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Template, TemplateApi } from '../services/templateApi';
import './TemplatePanel.css';

interface TemplatePanelProps {
  projectId: string;
  onSelectTemplate: (template: Template, withParameters?: boolean) => void;
  onClose: () => void;
  visible: boolean;
}

interface TemplateCardProps {
  template: Template;
  onSelect: (template: Template, withParameters?: boolean) => void;
}

const TemplateCard: React.FC<TemplateCardProps> = ({ template, onSelect }) => {
  const { t } = useTranslation();
  const [showPreview, setShowPreview] = useState(false);

  // 获取复杂度颜色
  const getComplexityColor = (level?: string) => {
    switch (level) {
      case 'basic': return '#52c41a';
      case 'intermediate': return '#faad14';
      case 'advanced': return '#f5222d';
      default: return '#d9d9d9';
    }
  };

  // 获取复杂度文本
  const getComplexityText = (level?: string) => {
    switch (level) {
      case 'basic': return t('analysis:templateManagement.complexity.basic');
      case 'intermediate': return t('analysis:templateManagement.complexity.intermediate');
      case 'advanced': return t('analysis:templateManagement.complexity.advanced');
      default: return t('analysis:templateManagement.complexity.general');
    }
  };

  return (
    <div className="template-card">
      <div className="template-card-header">
        <div className="template-title-section">
          <h5 className="template-name">{template.name}</h5>
          <div className="template-badges">
            {template.is_system && <span className="badge system">{t('analysis:templateManagement.systemTemplate')}</span>}
            {template.is_public && <span className="badge public">{t('analysis:templateManagement.publicTag')}</span>}
            {template.complexity_level && (
              <span
                className="badge complexity"
                style={{ backgroundColor: getComplexityColor(template.complexity_level) }}
              >
                {getComplexityText(template.complexity_level)}
              </span>
            )}
          </div>
        </div>
        <div className="template-stats">
          <span className="usage-count">🔥 {template.usage_count} {t('analysis:templateManagement.templatePanel.usageCount')}</span>
          {template.creator_name && (
            <span className="creator">👤 {template.creator_name}</span>
          )}
        </div>
      </div>

      {/* 分类和标签 */}
      {(template.category || (template.tags && template.tags.length > 0)) && (
        <div className="template-meta enhanced">
          {template.category && (
            <span className="category">📂 {template.category}</span>
          )}
          {template.tags && template.tags.length > 0 && (
            <div className="tags">
              {template.tags.slice(0, 3).map((tag, index) => (
                <span key={index} className="tag">#{tag}</span>
              ))}
              {template.tags.length > 3 && (
                <span className="tag more">+{template.tags.length - 3}</span>
              )}
            </div>
          )}
        </div>
      )}

      {template.description && (
        <p className="template-description">{template.description}</p>
      )}

      <div className="template-actions">
        <button
          className="preview-btn"
          onClick={() => setShowPreview(!showPreview)}
        >
          {showPreview ? t('analysis:templateManagement.templatePanel.collapse') : t('analysis:templateManagement.templatePanel.preview')}
        </button>
        <button
          className="use-btn primary"
          onClick={() => onSelect(template)}
        >
          {t('analysis:templateManagement.templatePanel.useTemplate')}
        </button>
      </div>

      {showPreview && (
        <div className="template-preview">
          <div className="preview-header">
            <span>📄 {t('analysis:templateManagement.templatePanel.templateContent')}</span>
            {/* <div className="preview-actions">
              <button
                className="parameter-btn"
                onClick={(e) => {
                  e.stopPropagation();
                  // 打开参数化面板
                  onSelect(template, true);
                }}
                title={t('analysis:templateManagement.templatePanel.parameterizedUseTitle')}
              >
                <span>⚙️ {t('analysis:templateManagement.templatePanel.parameterizedUse')}</span>
              </button>
            </div> */}
          </div>
          <div className="preview-content">
            {template.content}
          </div>
        </div>
      )}
    </div>
  );
};

const TemplatePanel: React.FC<TemplatePanelProps> = ({
  projectId,
  onSelectTemplate,
  onClose,
  visible
}) => {
  const { t } = useTranslation();
  const [templates, setTemplates] = useState<Template[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedComplexity, setSelectedComplexity] = useState<string>('all');

  // 获取模板列表
  const fetchTemplates = async () => {
    if (!projectId) return;
    
    setLoading(true);
    try {
      const data = await TemplateApi.getTemplates({
        projectId,
        search: searchTerm || undefined
      });
      setTemplates(data);
    } catch (error) {
      console.error(t('analysis:templateManagement.templatePanel.fetchFailed'), error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (visible) {
      fetchTemplates();
    }
  }, [visible, projectId, searchTerm]);

  // 获取所有分类
  const categories = useMemo(() => {
    const cats = new Set<string>();
    templates.forEach(template => {
      if (template.category) {
        cats.add(template.category);
      }
    });
    return Array.from(cats).sort();
  }, [templates]);

  // 过滤模板
  const filteredTemplates = useMemo(() => {
    return templates.filter(template => {
      // 搜索过滤
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        const matchesName = template.name.toLowerCase().includes(searchLower);
        const matchesDescription = template.description?.toLowerCase().includes(searchLower);
        const matchesTags = template.tags?.some(tag => tag.toLowerCase().includes(searchLower));
        if (!matchesName && !matchesDescription && !matchesTags) {
          return false;
        }
      }

      // 分类过滤
      if (selectedCategory !== 'all' && template.category !== selectedCategory) {
        return false;
      }

      // 复杂度过滤
      if (selectedComplexity !== 'all' && template.complexity_level !== selectedComplexity) {
        return false;
      }

      return true;
    });
  }, [templates, searchTerm, selectedCategory, selectedComplexity]);

  // 使用模板
  const handleSelectTemplate = async (template: Template, withParameters: boolean = false) => {
    try {
      // 记录使用次数
      await TemplateApi.recordTemplateUsage(template.id);

      // 回调父组件
      onSelectTemplate(template, withParameters);

      // 刷新模板列表以更新使用次数
      fetchTemplates();
    } catch (error) {
      console.error(t('analysis:templateManagement.templatePanel.useFailed'), error);
      // 即使记录失败也要继续使用模板
      onSelectTemplate(template, withParameters);
    }
  };

  if (!visible) return null;

  return (
    <div className="template-panel">
      {/* 搜索和筛选 */}
      <div className="template-filters">
        <input
          type="text"
          placeholder={t('analysis:templateManagement.search.placeholder')}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="search-input"
        />

        {/* <div className="filter-row">
          <div className="filter-group">
            <label>{t('analysis:templateManagement.templatePanel.category')}：</label>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="filter-select"
            >
              <option value="all">{t('analysis:templateManagement.templatePanel.allCategories')}</option>
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label>{t('analysis:templateManagement.templatePanel.complexity')}：</label>
            <select
              value={selectedComplexity}
              onChange={(e) => setSelectedComplexity(e.target.value)}
              className="filter-select"
            >
              <option value="all">{t('analysis:templateManagement.templatePanel.allComplexity')}</option>
              <option value="basic">{t('analysis:templateManagement.complexity.basic')}</option>
              <option value="intermediate">{t('analysis:templateManagement.complexity.intermediate')}</option>
              <option value="advanced">{t('analysis:templateManagement.complexity.advanced')}</option>
            </select>
          </div>
        </div> */}
      </div>
      
      {/* 模板列表 */}
      <div className="template-list">
        {loading ? (
          <div className="loading">{t('analysis:templateManagement.templatePanel.loading')}</div>
        ) : filteredTemplates.length === 0 ? (
          <div className="empty">{t('analysis:templateManagement.templatePanel.empty')}</div>
        ) : (
          filteredTemplates.map(template => (
            <TemplateCard
              key={template.id}
              template={template}
              onSelect={(template, withParameters) => handleSelectTemplate(template, withParameters)}
            />
          ))
        )}
      </div>
    </div>
  );
};

export default TemplatePanel;
