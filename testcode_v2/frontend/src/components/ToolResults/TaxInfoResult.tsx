import React from 'react';
import { Card, Typography, Alert, Space, Tag } from 'antd';
import ReactMarkdown from 'react-markdown';
import { useTranslation } from 'react-i18next';

const { Title, Text, Paragraph } = Typography;

interface TaxInfoResultProps {
  data: any;
}

const TaxInfoResult: React.FC<TaxInfoResultProps> = ({ data }) => {
  const { t } = useTranslation();
  // 处理数据
  let taxContent = '';
  
  try {
    if (typeof data === 'string') {
      taxContent = data;
    } else if (data && typeof data === 'object' && Array.isArray(data)) {
      // 如果是数组，尝试查找chunk_text字段
      const firstItem = data[0];
      if (firstItem && firstItem.chunk_text) {
        taxContent = firstItem.chunk_text;
      } else {
        taxContent = JSON.stringify(data, null, 2);
      }
    } else if (data && typeof data === 'object') {
      // 如果是对象，尝试查找chunk_text字段
      if (data.chunk_text) {
        taxContent = data.chunk_text;
      } else {
        taxContent = JSON.stringify(data, null, 2);
      }
    }
  } catch (e) {
    console.error('解析税务信息数据失败:', e);
  }

  if (!taxContent) {
    return (
      <Card>
        <Alert
          message={t('analysis:taxInfo.notFound')}
          description={t('analysis:taxInfo.notFoundDescription')}
          type="info"
          showIcon
        />
      </Card>
    );
  }

  // 为税务关键词添加标签样式
  const highLightTaxTerms = (content: string) => {
    // 创建一个带有高亮标签的文本
    const taxTerms = [
      '增值税', '企业所得税', '个人所得税', '消费税', '关税', '印花税',
      '环保税', '土地增值税', '车船税', '房产税', '契税', '税率',
      '税额', '纳税', '缴税', '退税', '免税', '税收优惠', '税务局',
      '税务风险', '税务违规', '纳税信用', '逃税', '漏税', '欠税'
    ];
    
    // 在Markdown内容中保留HTML标签
    return (
      <ReactMarkdown components={{
        // eslint-disable-next-line react/display-name
        p: ({ children }) => {
          let content = children as string;
          if (typeof content !== 'string') {
            // 处理非字符串类型的children
            return <p>{children}</p>;
          }
          
          taxTerms.forEach(term => {
            // 使用正则表达式匹配词语（考虑词语边界）
            const regex = new RegExp(`(${term})`, 'g');
            content = content.replace(regex, `__HIGHLIGHT__$1__HIGHLIGHT__`);
          });
          
          // 分割文本并渲染高亮部分
          const parts = content.split('__HIGHLIGHT__');
          return (
            <p>
              {parts.map((part, index) => {
                // 检查是否是需要高亮的部分
                if (index % 2 === 1) {
                  return <Tag color="blue" key={index}>{part}</Tag>;
                }
                return part;
              })}
            </p>
          );
        }
      }}>
        {taxContent}
      </ReactMarkdown>
    );
  };

  return (
    <Card className="tax-info-result">
      <Space direction="vertical" style={{ width: '100%' }}>
        <Title level={4}>纳税情况分析</Title>
        
        <Alert 
          message="向量检索结果" 
          description="以下信息通过向量相似度检索从文档中提取，展示与纳税情况最相关的内容。" 
          type="info" 
          showIcon 
          style={{ marginBottom: 16 }}
        />
        
        <Card type="inner" title="纳税情况详情">
          {highLightTaxTerms(taxContent)}
        </Card>
      </Space>
    </Card>
  );
};

export default TaxInfoResult;