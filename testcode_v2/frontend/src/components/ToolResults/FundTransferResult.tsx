import React, { useEffect, useRef } from 'react';
import { Card, Typography, Table, Tag, Divider, Empty } from 'antd';
import * as echarts from 'echarts';

const { Title, Text } = Typography;

interface TransferData {
  源公司名称: string;
  源账号: string;
  目标公司名称: string;
  目标账号: string;
  交易次数: number;
  总金额: number;
  风险等级?: string;
  交易描述?: string;
}

interface FundTransferResultProps {
  data: any;
}

const FundTransferResult: React.FC<FundTransferResultProps> = ({ data }) => {
  const chartRef = useRef<HTMLDivElement>(null);
  
  // 处理数据
  let transferData: TransferData[] = [];
  
  try {
    // 尝试解析数据
    if (typeof data === 'string') {
      transferData = JSON.parse(data);
    } else if (data && typeof data === 'object' && Array.isArray(data)) {
      transferData = data;
    } else if (data && typeof data === 'object' && !Array.isArray(data)) {
      // 可能是单条记录
      transferData = [data];
    }
  } catch (e) {
    console.error('解析资金流转数据失败:', e);
  }

  // 初始化图表
  useEffect(() => {
    if (chartRef.current && transferData.length > 0) {
      const chart = echarts.init(chartRef.current);
      
      // 准备图表数据
      const nodes: any[] = [];
      const links: any[] = [];
      const companies = new Set<string>();
      
      // 收集所有公司
      transferData.forEach(item => {
        companies.add(item.源公司名称);
        companies.add(item.目标公司名称);
      });
      
      // 创建节点
      companies.forEach(company => {
        nodes.push({
          name: company,
          symbolSize: 50,  // 节点大小
          itemStyle: {
            color: '#5470c6'  // 默认节点颜色
          }
        });
      });
      
      // 创建连接
      transferData.forEach(item => {
        links.push({
          source: item.源公司名称,
          target: item.目标公司名称,
          value: item.总金额,
          lineStyle: {
            width: Math.log(item.总金额) / 10 + 1,  // 根据金额调整线宽
            color: item.风险等级 === '高风险' ? '#ff4d4f' : 
                  item.风险等级 === '中风险' ? '#faad14' : '#52c41a'
          },
          label: {
            show: true,
            formatter: `${item.交易次数}次 ¥${item.总金额}`
          }
        });
      });
      
      const option = {
        title: {
          text: '资金流转关系图',
          subtext: '节点表示公司，连线表示资金流向',
          top: 'top',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: (params: any) => {
            if (params.dataType === 'edge') {
              const sourceNode = params.data.source;
              const targetNode = params.data.target;
              const value = params.data.value;
              return `${sourceNode} → ${targetNode}<br/>金额: ¥${value}`;
            }
            return params.name;
          }
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: Array.from(companies)
        },
        series: [
          {
            type: 'graph',
            layout: 'force',
            data: nodes,
            links: links,
            roam: true,
            edgeSymbol: ['none', 'arrow'],  // 添加箭头指向配置，起点无箭头，终点有箭头
            edgeSymbolSize: [0, 10],        // 箭头大小配置
            label: {
              show: true,
              position: 'right'
            },
            lineStyle: {
              color: 'source',
              curveness: 0.3
            },
            emphasis: {
              focus: 'adjacency',
              lineStyle: {
                width: 5
              }
            },
            force: {
              repulsion: 1000,
              edgeLength: 200
            }
          }
        ]
      };
      
      chart.setOption(option);
      
      // 窗口大小变化时调整图表大小
      const resizeHandler = () => {
        chart.resize();
      };
      
      window.addEventListener('resize', resizeHandler);
      
      // 清理函数
      return () => {
        window.removeEventListener('resize', resizeHandler);
        chart.dispose();
      };
    }
  }, [transferData]);

  // 表格列定义
  const columns = [
    {
      title: '源公司',
      dataIndex: '源公司名称',
      key: '源公司名称',
    },
    {
      title: '源账号',
      dataIndex: '源账号',
      key: '源账号',
      ellipsis: true,
    },
    {
      title: '目标公司',
      dataIndex: '目标公司名称',
      key: '目标公司名称',
    },
    {
      title: '目标账号',
      dataIndex: '目标账号',
      key: '目标账号',
      ellipsis: true,
    },
    {
      title: '交易次数',
      dataIndex: '交易次数',
      key: '交易次数',
      sorter: (a: TransferData, b: TransferData) => a.交易次数 - b.交易次数,
    },
    {
      title: '总金额',
      dataIndex: '总金额',
      key: '总金额',
      sorter: (a: TransferData, b: TransferData) => a.总金额 - b.总金额,
      render: (value: number) => `¥${value.toLocaleString()}`,
    },
    {
      title: '风险等级',
      dataIndex: '风险等级',
      key: '风险等级',
      render: (value: string) => {
        const color = value === '高风险' ? 'red' : value === '中风险' ? 'orange' : 'green';
        return <Tag color={color}>{value}</Tag>;
      },
    },
  ];

  if (!transferData.length) {
    return (
      <Card>
        <Empty description="未找到资金流转数据" />
      </Card>
    );
  }

  return (
    <Card className="fund-transfer-result">
      <Title level={4}>资金流转分析</Title>
      
      {/* 关系图 */}
      <div 
        ref={chartRef} 
        style={{ width: '100%', height: '500px', marginBottom: '20px' }}
      />
      
      <Divider />
      
      {/* 表格展示详细数据 */}
      <Table 
        dataSource={transferData}
        columns={columns}
        rowKey={(record, index) => `${record.源公司名称}-${record.目标公司名称}-${index}`}
        pagination={{ pageSize: 5 }}
        bordered
        size="small"
      />
    </Card>
  );
};

export default FundTransferResult;