import React from 'react';
import { Card, Typography, Timeline, Tag, Empty, Space, Button } from 'antd';
import { useTranslation } from 'react-i18next';

const { Title, Text, Paragraph } = Typography;

interface NewsItem {
  id: string;
  company_id: string;
  happen_date: string;
  news_content: string;
  tag: string;
  url: string;
}

interface NewsResultProps {
  data: any;
}

// 获取标签颜色
const getTagColor = (tag: string, t: any): string => {
  // 创建关键词到颜色的映射，使用翻译后的关键词
  const keywordColorMap: Record<string, string> = {
    'negative': 'red',
    'risk': 'volcano',
    'warning': 'orange',
    'violation': 'magenta',
    'penalty': 'purple',
    'lawsuit': 'geekblue',
    'prosecution': 'geekblue',
    'defendant': 'geekblue',
    'plaintiff': 'cyan',
    'bankruptcy': 'red',
    'debt': 'orange',
    'taxDebt': 'orange',
    'layoff': 'orange',
    'loss': 'orange',
    'downgrade': 'gold',
    'decrease': 'gold',
    'alert': 'gold',
    'investigation': 'blue',
    'neutral': 'blue',
    'positive': 'green',
    'commendation': 'green',
    'award': 'green',
    'growth': 'green',
    'profit': 'green',
    'success': 'green'
  };

  // 遍历所有关键词，找到第一个匹配的
  for (const [keywordKey, color] of Object.entries(keywordColorMap)) {
    const translatedKeyword = t(`analysis:news.keywords.${keywordKey}`);
    if (tag.includes(translatedKeyword)) {
      return color;
    }
  }

  return 'default';
};

// 根据内容获取可能的标签
const generateTags = (content: string, t: any): string[] => {
  const keywordKeys = [
    'negative', 'risk', 'warning', 'violation', 'penalty', 'lawsuit', 'prosecution', 'defendant',
    'plaintiff', 'bankruptcy', 'debt', 'taxDebt', 'layoff', 'loss', 'downgrade', 'decrease',
    'alert', 'investigation', 'neutral', 'positive', 'commendation', 'award', 'growth', 'profit',
    'success'
  ];

  const translatedKeywords = keywordKeys.map(key => t(`analysis:news.keywords.${key}`));

  return translatedKeywords.filter(keyword => content.includes(keyword));
};

const NewsResult: React.FC<NewsResultProps> = ({ data }) => {
  const { t } = useTranslation();
  // 处理数据
  let newsItems: NewsItem[] = [];
  
  try {
    // 尝试解析数据
    if (typeof data === 'string') {
      newsItems = JSON.parse(data);
    } else if (data && typeof data === 'object' && Array.isArray(data)) {
      newsItems = data;
    } else if (data && typeof data === 'object' && !Array.isArray(data)) {
      // 单条新闻
      newsItems = [data as NewsItem];
    }
    
    // 按日期排序（从新到旧）
    newsItems.sort((a, b) => {
      const dateA = new Date(a.happen_date).getTime();
      const dateB = new Date(b.happen_date).getTime();
      return dateB - dateA;
    });
  } catch (e) {
    console.error('解析新闻数据失败:', e);
  }
  
  if (!newsItems.length) {
    return (
      <Card>
        <Empty description={t('analysis:news.notFound')} />
      </Card>
    );
  }
  
  return (
    <Card className="news-result">
      <Title level={4}>{t('analysis:news.analysisTitle')}</Title>
      <Paragraph type="secondary">
        {t('analysis:news.foundCount', { count: newsItems.length })}
      </Paragraph>
      
      <Timeline
        mode="left"
        items={newsItems.map(item => {
          // 生成标签
          const tags = item.tag ? item.tag.split(',') : generateTags(item.news_content, t);

          return {
            label: item.happen_date,
            color: getTagColor(tags[0] || '', t),
            children: (
              <Card 
                size="small" 
                title={
                  <Space>
                    {tags.map((tag, index) => (
                      <Tag color={getTagColor(tag, t)} key={index}>
                        {tag}
                      </Tag>
                    ))}
                  </Space>
                }
                extra={item.url && (
                  <Button type="link" href={item.url} target="_blank" size="small">
                    查看原文
                  </Button>
                )}
              >
                <Paragraph 
                  ellipsis={{ rows: 3, expandable: true, symbol: '展开' }}
                  style={{ margin: 0 }}
                >
                  {item.news_content}
                </Paragraph>
              </Card>
            )
          };
        })}
      />
    </Card>
  );
};

export default NewsResult;