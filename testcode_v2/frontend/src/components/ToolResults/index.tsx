import React from 'react';
import FundTransferResult from './FundTransferResult';
import TaxInfoResult from './TaxInfoResult';
import NewsResult from './NewsResult';
import { Card, Alert, Typography } from 'antd';
import { JsonView } from 'react-json-view-lite';
import 'react-json-view-lite/dist/index.css';

const { Text } = Typography;

interface ToolResultRendererProps {
  toolName: string;
  toolType: string;
  data: any;
  error?: string;
  executionTime?: number;
}

/**
 * 根据工具名称和类型渲染合适的可视化组件
 */
const ToolResultRenderer: React.FC<ToolResultRendererProps> = ({ 
  toolName, 
  toolType, 
  data, 
  error, 
  executionTime 
}) => {
  // 如果有错误，显示错误信息
  if (error) {
    return (
      <Card bordered={false} style={{ marginTop: 16, marginLeft: 24 }} size="small">
        <Alert message="执行错误" description={error} type="error" showIcon />
      </Card>
    );
  }
  
  // 如果没有数据，显示空结果
  if (!data) {
    return (
      <Card bordered={false} style={{ marginTop: 16, marginLeft: 24 }} size="small">
        <Alert message="无数据" description="工具执行未返回数据" type="info" showIcon />
      </Card>
    );
  }
  
  // 显示执行时间
  const TimeDisplay = executionTime ? (
    <div style={{ marginBottom: 8 }}>
      <Text type="secondary">执行时间: {executionTime.toFixed(2)}秒</Text>
    </div>
  ) : null;
  
  // 根据工具名称和类型选择合适的可视化组件
  const renderResultComponent = () => {
    
    // 资金流转分析
    if (toolName === 'analyze_fund_transfers' || toolName.includes('fund') || toolName.includes('transfer')) {
      return <FundTransferResult data={data} />;
    }
    
    // 纳税情况分析
    if (toolName === 'analyze_tax_info' || toolName.includes('tax')) {
      return <TaxInfoResult data={data} />;
    }
    
    // 负面新闻分析
    if (toolName === 'analyze_negative_news' || toolName.includes('news')) {
      return <NewsResult data={data} />;
    }
    
    // 默认以JSON形式显示数据
    return (
      <Card bordered={false} style={{ marginTop: 16, marginLeft: 24 }} size="small">
        {TimeDisplay}
        
        <JsonView 
          data={data}
          shouldExpandNode={() => true}
        />
      </Card>
    );
  };
  
  return (
    <>
      {renderResultComponent()}
    </>
  );
};

export default ToolResultRenderer;