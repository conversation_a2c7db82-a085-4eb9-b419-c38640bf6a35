import React from 'react';
import { Select } from 'antd';
import { GlobalOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import api from '../services/api';
import './LanguageSwitch.css';

const { Option } = Select;

const languages = [
  { key: 'zh-CN', label: '中文', flag: '🇨🇳' },
  { key: 'en-US', label: 'English', flag: '🇺🇸' },
];

interface LanguageSwitchProps {
  style?: React.CSSProperties;
  size?: 'small' | 'middle' | 'large';
}

export const LanguageSwitch: React.FC<LanguageSwitchProps> = ({ 
  style = {}, 
  size = 'middle' 
}) => {
  const { i18n } = useTranslation();

  const handleLanguageChange = async (language: string) => {
    try {
      // 切换前端语言（i18next 支持动态切换，无需刷新页面）
      await i18n.changeLanguage(language);
      
      // 保存到本地存储
      localStorage.setItem('userLanguage', language);
      
      // 异步同步到后端用户偏好（不阻塞语言切换）
      const token = localStorage.getItem('token');
      if (token) {
        // 使用非阻塞方式同步到后端
        api.put('/users/language-preference', { language }, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        }).catch(error => {
          console.warn('Failed to sync language preference to server:', error);
          // 后端同步失败不影响前端语言切换
        });
      }
      
      // 移除页面刷新 - i18next 会自动重新渲染所有使用翻译的组件
      // window.location.reload(); // 删除这行代码
    } catch (error) {
      console.error('Failed to change language:', error);
    }
  };

  return (
    <Select
      value={i18n.language}
      onChange={handleLanguageChange}
      style={{ width: 120, ...style }}
      size={size}
      suffixIcon={<GlobalOutlined />}
      dropdownMatchSelectWidth={false}
      className="language-switch"
    >
      {languages.map(lang => (
        <Option key={lang.key} value={lang.key}>
          <div className="language-option">
            <span className="language-flag">{lang.flag}</span>
            <span className="language-label">{lang.label}</span>
          </div>
        </Option>
      ))}
    </Select>
  );
};

export default LanguageSwitch;