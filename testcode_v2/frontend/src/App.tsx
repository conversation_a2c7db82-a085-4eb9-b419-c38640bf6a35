import React, { useEffect, useState, useCallback } from 'react';
import { BrowserRouter as Router, Routes, Route, Link, useLocation, Navigate, useNavigate } from 'react-router-dom';
import { Layout, Menu, Select, Space, Button, Tooltip, Dropdown, Modal, message } from 'antd';
import {
  HomeOutlined,
  ProjectOutlined,
  DatabaseOutlined,
  ToolOutlined,
  SearchOutlined,
  PlusOutlined,
  LogoutOutlined,
  MessageOutlined,
  HistoryOutlined,
  RobotOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  SettingOutlined,
  DownOutlined,
  UserOutlined,
  FileTextOutlined,
} from '@ant-design/icons';
import SidebarConversations from './components/SidebarConversations';
import LanguageSwitch from './components/LanguageSwitch';
import { useTranslation } from 'react-i18next';
import i18n from './i18n';
import { useLanguageInit } from './hooks/useLanguageInit';

// 页面组件
import Dashboard from './pages/Dashboard';
import DataSourceList from './pages/datasource/DataSourceList';
import ProjectList from './pages/project/ProjectList';
import ToolList from './pages/tool/ToolList';
import ToolDetail from './pages/tool/ToolDetail';
import ToolEdit from './pages/tool/ToolEdit';
import Analysis from './pages/analysis/Analysis';
import AnalysisHistory from './pages/analysis/AnalysisHistory';
import AnalysisDetail from './pages/analysis/AnalysisDetail';
import MultiRoundAnalysis from './pages/analysis/MultiRoundAnalysis';
import ProjectWizard from './pages/project/ProjectWizard';
import ProjectDetail from './pages/project/ProjectDetail';
import ModelList from './pages/model/ModelList';
import ModelEdit from './pages/model/ModelEdit';
import UserList from './pages/user/UserList';
import TemplateManagement from './pages/templates/TemplateManagement';
import Login from './pages/Login';
import ProtectedRoute from './components/ProtectedRoute';
import { Project } from './types';
import { projectApi, authApi } from './services/api';

// 管理员相关组件
import AdminLogin from './admin/pages/AdminLogin';
import AdminDashboard from './admin/pages/AdminDashboard';
import UserManagement from './admin/pages/UserManagement';
import OrganizationManagement from './admin/pages/OrganizationManagement';
import AdminProtectedRoute from './admin/components/AdminProtectedRoute';

// 临时页面组件
const CreateProject = () => <div>Create Project</div>;
const UpdateProject = () => <div>Update Project</div>;
const DataSourceDetail = () => <div>Data Source Detail</div>;
const CreateDataSource = () => <div>Create Data Source</div>;
const UpdateDataSource = () => <div>Update Data Source</div>;

const { Header, Content, Footer, Sider } = Layout;
const { Option } = Select;

// 创建一个事件总线，用于在登录成功后通知其他组件
export const loginSuccessEvent = new CustomEvent('loginSuccess');

// 创建一个内部组件来处理路由监听
const AppContent = () => {
  const [collapsed, setCollapsed] = React.useState(false);
  const [projects, setProjects] = useState<Project[]>([]);
  const [selectedProjectId, setSelectedProjectId] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(true);
  const [activeMenuKey, setActiveMenuKey] = useState<string>('1');
  const location = useLocation();
  const navigate = useNavigate();
  const [userInfo, setUserInfo] = useState<any>(null);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(authApi.isLoggedIn());
  const [conversationRefreshTrigger, setConversationRefreshTrigger] = useState<number>(0);
  const [isSelectingConversation, setIsSelectingConversation] = useState<boolean>(false);
  const [userProjectRole, setUserProjectRole] = useState<string | null>(null);
  const { t } = useTranslation();
  
  // 初始化语言设置
  useLanguageInit();

  // 新增：从URL参数获取当前会话ID的函数
  const getCurrentConversationId = (): string => {
    const pathParts = location.pathname.split('/');
    if (pathParts.length >= 4 && pathParts[1] === 'analysis' && pathParts[2] === 'multi-round') {
      return pathParts[3] || '';
    }
    return '';
  };

  // 获取当前选中的管理功能
  const getCurrentActiveManagement = (): 'projects' | 'tools' | 'models' | 'users' | 'templates' | null => {
    const path = location.pathname;
    // 只有在管理页面时才返回选中状态，分析页面返回null确保不冲突
    if (path.startsWith('/projects')) return 'projects';
    if (path.startsWith('/tools')) return 'tools';
    if (path.startsWith('/models')) return 'models';
    if (path.startsWith('/users')) return 'users';
    if (path.startsWith('/templates')) return 'templates';
    // 分析页面、数据源页面等其他页面都返回null
    return null;
  };

  // 获取项目列表的函数
  const fetchProjects = useCallback(async () => {
    if (!authApi.isLoggedIn()) {
      return;
    }
    
    try {
      setLoading(true);
      const response = await projectApi.getAllProjects();
      
      if (response && response.data) {
        let projectsData = [];
        
        // 处理API响应的复杂结构
        if (response.data.data && response.data.data.items) {
          projectsData = Array.isArray(response.data.data.items) ? response.data.data.items : [];
        } else if (response.data.items) {
          projectsData = Array.isArray(response.data.items) ? response.data.items : [];
        } else if (Array.isArray(response.data)) {
          projectsData = response.data;
        }
        
        setProjects(projectsData);
        
        // 从localStorage获取上次选择的项目，或使用第一个项目
        const savedProjectId = localStorage.getItem('selectedProjectId');
        const initialProjectId = savedProjectId && projectsData.some((p: Project) => p.id === savedProjectId) 
          ? savedProjectId 
          : (projectsData.length > 0 ? projectsData[0].id : '');
          
        setSelectedProjectId(initialProjectId);
        
        // 保存选择到localStorage
        if (initialProjectId) {
          localStorage.setItem('selectedProjectId', initialProjectId);
        }
      }
    } catch (error) {
      console.error('Failed to fetch projects', error);
    } finally {
      setLoading(false);
    }
  }, []);

  // 加载用户信息和项目列表
  useEffect(() => {
    const loadUserInfo = () => {
      const storedUserInfo = localStorage.getItem('userInfo');
      if (storedUserInfo) {
        try {
          const parsedUserInfo = JSON.parse(storedUserInfo);
          setUserInfo(parsedUserInfo);
        } catch (e) {
          console.error('解析用户信息失败:', e);
        }
      }
      // 更新认证状态
      setIsAuthenticated(authApi.isLoggedIn());
    };

    loadUserInfo();
    
    // 如果已经登录，立即获取项目列表
    if (authApi.isLoggedIn()) {
      console.log('用户已登录，开始获取项目列表');
      fetchProjects();
    }

    // 监听登录成功事件
    const handleLoginSuccess = () => {
      console.log('登录成功事件触发，重新加载用户信息和项目列表');
      loadUserInfo();
      fetchProjects();
    };

    window.addEventListener('loginSuccess', handleLoginSuccess);
    
    return () => {
      window.removeEventListener('loginSuccess', handleLoginSuccess);
    };
  }, [fetchProjects]);

  // 监听认证状态变化，在认证状态变化时重新获取项目列表
  useEffect(() => {
    if (isAuthenticated) {
      fetchProjects();
    }
  }, [isAuthenticated, fetchProjects]);

  // 监听路由变化，更新菜单选中状态和当前会话ID
  useEffect(() => {
    const path = location.pathname;
    if (path.startsWith('/analysis')) {
      setActiveMenuKey('6');
      // 从URL中提取会话ID
      const conversationMatch = path.match(/\/analysis\/multi-round\/(.+)/);
      if (conversationMatch) {
        const newConversationId = conversationMatch[1];
        console.log('从URL提取到会话ID:', newConversationId);
      } else {
        console.log('URL中没有会话ID，清空当前会话ID');
      }
    } else if (path.startsWith('/models')) {
      setActiveMenuKey('models');
      // 在管理页面时清空会话ID，确保对话历史不显示选中状态
      console.log('URL中没有会话ID，清空当前会话ID');
    } else if (path.startsWith('/projects')) {
      // 在管理页面时清空会话ID，确保对话历史不显示选中状态
      console.log('URL中没有会话ID，清空当前会话ID');
    } else {
      // 默认选中数据分析
      setActiveMenuKey('6');
    }
  }, [location]);

  // 监听SSE分析开始事件，触发对话列表刷新
  useEffect(() => {
    const handleAnalysisStarted = (event: any) => {
      console.log('🔄 接收到分析开始事件，触发对话列表刷新');
      setConversationRefreshTrigger(prev => prev + 1);
    };

    const handleConversationCreated = (event: any) => {
      console.log('🔄 接收到会话创建事件，触发对话列表刷新:', event.detail);
      setConversationRefreshTrigger(prev => prev + 1);
      
      // 如果事件中包含新创建的会话ID，直接导航到该会话
      if (event.detail && event.detail.conversationId) {
        const newConversationId = event.detail.conversationId;
        console.log('导航到新创建的会话:', newConversationId);
        navigate(`/analysis/multi-round/${newConversationId}`, { replace: true });
      }
    };

    window.addEventListener('analysisStarted', handleAnalysisStarted);
    window.addEventListener('conversationCreated', handleConversationCreated);
    
    return () => {
      window.removeEventListener('analysisStarted', handleAnalysisStarted);
      window.removeEventListener('conversationCreated', handleConversationCreated);
    };
  }, [navigate]);

  // 监听项目列表刷新事件
  useEffect(() => {
    // 监听项目创建完成事件
    const handleReloadProjects = (event: any) => {
      console.log('接收到重新加载项目列表事件:', event.detail);
      fetchProjects();
      
      // 如果事件中包含新创建的项目ID，则自动选择它
      if (event.detail && event.detail.projectId) {
        const newProjectId = event.detail.projectId;
        setSelectedProjectId(newProjectId);
        localStorage.setItem('selectedProjectId', newProjectId);
      }
    };

    window.addEventListener('reloadProjects', handleReloadProjects);
    
    // 组件卸载时清理
    return () => {
      window.removeEventListener('reloadProjects', handleReloadProjects);
    };
  }, [fetchProjects]);

  // 获取用户在当前项目中的角色
  const fetchUserProjectRole = useCallback(async () => {
    if (!selectedProjectId || !userInfo) {
      setUserProjectRole(null);
      return;
    }
    
    try {
      const { projectMemberApi } = await import('./services/api');
      const response = await projectMemberApi.getProjectMembers(selectedProjectId);
      
      // 处理标准响应格式
      const apiData = response.data?.data || response.data;
      const members = apiData?.members || [];
      
      // 查找当前用户在项目中的角色
      const userMember = members.find((member: any) => member.user_id === userInfo.id);
      if (userMember) {
        setUserProjectRole(userMember.role_name);
      } else {
        setUserProjectRole(null);
      }
    } catch (error) {
      console.error('获取用户项目角色失败:', error);
      setUserProjectRole(null);
    }
  }, [selectedProjectId, userInfo]);

  // 当选择的项目或用户信息变化时，获取用户在项目中的角色
  useEffect(() => {
    if (selectedProjectId && userInfo) {
      fetchUserProjectRole();
    }
  }, [selectedProjectId, userInfo, fetchUserProjectRole]);

  // 检查用户是否有工具管理权限
  const canManageTools = () => {
    console.log('canManageTools', userInfo);
    if (!userInfo) {
      return false;
    }
    
    // 超级管理员总是可以管理工具
    if (userInfo.role_id === 1) {
      return true;
    }
    
    // 企业管理员总是可以管理工具
    if (userInfo.role_id === 2) {
      return true;
    }
    
    // 普通用户需要有选中的项目，并且检查项目角色
    if (userInfo.role_id === 3 && selectedProjectId) {
      // 如果是项目所有者，可以管理工具
      const currentProject = projects.find(p => p.id === selectedProjectId);
      if (currentProject && currentProject.owner_id === userInfo.id) {
        return true;
      }
      
      // If user is project collaborator, can manage tools
      if (userProjectRole === t('project:roles.collaborator')) {
        return true;
      }
      
      // If user is project observer, cannot manage tools
      if (userProjectRole === t('project:roles.observer')) {
        return false;
      }
      
      // 如果还没有获取到项目角色信息，暂时不显示（避免闪烁）
      if (!userProjectRole) {
        return false;
      }
    }
    
    return false;
  };

  // 处理项目选择变化
  const handleProjectChange = (projectId: string) => {
    setSelectedProjectId(projectId);
    localStorage.setItem('selectedProjectId', projectId);
    // 选择项目后刷新当前页面以加载新项目的数据
    window.location.reload();
  };

  // 处理退出登录
  const handleLogout = () => {
    Modal.confirm({
      title: t('auth:permissions.logout'),
      content: t('auth:permissions.logoutConfirm'),
      okText: t('common:buttons.confirm'),
      cancelText: t('common:buttons.cancel'),
      okType: 'primary',
      className: 'custom-logout-modal',
      centered: true,
      width: 420,
      icon: <LogoutOutlined style={{ color: '#3b82f6', fontSize: '24px' }} />,
      maskClosable: false,
      onOk() {
    authApi.logout();
      },
    });
  };

  // 处理会话选择
  const handleSelectConversation = (conversationId: string) => {
    console.log('App.tsx handleSelectConversation 被调用, conversationId:', conversationId);
    
    // 防抖保护：如果正在选择会话，忽略新的选择请求
    if (isSelectingConversation) {
      console.log('正在选择会话中，忽略新的选择请求');
      return;
    }
    
    // 如果选择的是当前已经选中的会话，直接返回
    const currentId = getCurrentConversationId();
    if (currentId === conversationId) {
      console.log('选择的会话已经是当前会话，无需切换');
      return;
    }
    
    // 设置选择状态锁定
    setIsSelectingConversation(true);
    
    try {
      console.log('开始切换会话:', { from: currentId, to: conversationId });
      
      // 清理localStorage中可能存在的会话相关数据
      localStorage.removeItem('currentConversationId');
      localStorage.removeItem('conversationState');
      
      // 导航到新的会话页面
      navigate(`/analysis/multi-round/${conversationId}`, { replace: true });
      
      console.log('App.tsx 导航完成，切换到会话:', conversationId);
      
    } catch (error) {
      console.error('切换会话时发生错误:', error);
      // 使用 i18n 实例获取翻译
      const errorMessage = i18n.t('analysis:errors.switchConversationFailed', 'Failed to switch conversation');
      message.error(errorMessage);
    } finally {
      // 释放选择状态锁定（延迟释放，避免过快的连续点击）
      setTimeout(() => {
        setIsSelectingConversation(false);
      }, 300);
    }
  };

  // 处理新建会话
  const handleNewConversation = () => {
    console.log('App.tsx handleNewConversation 被调用');
    
    // 🔧 修复：简化逻辑，直接处理新建对话
    const isInMultiRoundPage = location.pathname.startsWith('/analysis/multi-round');
    const hasConversationId = !!getCurrentConversationId();
    
    console.log('当前状态检测:', {
      isInMultiRoundPage,
      hasConversationId,
      conversationId: getCurrentConversationId(),
      pathname: location.pathname
    });
    
    // 清理localStorage中可能存在的会话相关数据
    localStorage.removeItem('currentConversationId');
    localStorage.removeItem('conversationState');
    
    // 如果在多轮分析页面，直接导航到新建状态
    if (isInMultiRoundPage) {
      console.log('在多轮分析页面，导航到新建状态');
      // 使用replace确保URL更新，触发组件重新渲染
      navigate('/analysis/multi-round', { replace: true });
      
      // 如果之前有会话ID，额外触发重置事件确保状态清理
      if (hasConversationId) {
        setTimeout(() => {
          window.dispatchEvent(new CustomEvent('resetNewConversation', {
            detail: { timestamp: Date.now() }
          }));
        }, 50);
      }
    } else {
      // 如果不在多轮分析页面，使用正常导航
      console.log('不在多轮分析页面，使用正常导航');
      navigate('/analysis/multi-round', { replace: true });
    }
    
    console.log('App.tsx 导航完成');
    
    // 显示成功消息
    setTimeout(() => {
      // 使用 i18n 实例获取翻译
      const translatedMessage = i18n.t('analysis:multiRound.newConversationCreated', 'New conversation created, please start with your first question');
      message.success(translatedMessage);
    }, 100);
  };

  // 管理员路由 - 完全独立的界面
  if (location.pathname.startsWith('/admin')) {
    return (
      <Routes>
        <Route path="/admin/login" element={<AdminLogin />} />
        <Route path="/admin" element={<AdminProtectedRoute><AdminDashboard /></AdminProtectedRoute>}>
          <Route index element={<Navigate to="users" replace />} />
          <Route path="users" element={<UserManagement />} />
          <Route path="organizations" element={<OrganizationManagement />} />
        </Route>
        <Route path="/admin/*" element={<Navigate to="/admin/login" replace />} />
      </Routes>
    );
  }

  // 登录页面不显示主布局
  if (location.pathname === '/login') {
    return (
      <Routes>
        <Route path="/login" element={<Login />} />
        <Route path="*" element={<Navigate to="/login" replace />} />
      </Routes>
    );
  }

  return (
    <Layout className="app-container">
      <Sider
        className="app-sidebar"
        width={320}
        collapsible={false}
        collapsed={collapsed}
        theme="light"
      >
        {/* 简化的顶部区域 */}
        <div className="sidebar-header">
          <div className="sidebar-header-left">
            <img 
              src={`${process.env.PUBLIC_URL}/logo-icon.png`} 
              alt="DecisionAI Logo" 
              className="sidebar-header-icon"
            />
          </div>
          <div className="sidebar-header-right">
            <Button 
              type="text" 
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              className="sidebar-collapse-btn"
              size="small"
            />
          </div>
        </div>

        {/* 项目选择区域 */}
        {!collapsed && (
          <div className="sidebar-project-section">
            <div className="sidebar-project-title">{t('project:list.title')}</div>
            <Select
              className="sidebar-project-select"
              placeholder={t('project:create.selectDataSource')}
              value={selectedProjectId || undefined}
              onChange={handleProjectChange}
              loading={loading}
              style={{ width: '100%' }}
            >
              {projects.map(project => (
                <Option key={project.id} value={project.id}>{project.name}</Option>
              ))}
            </Select>
            
          </div>
        )}

        {/* 会话列表区域 */}
        {selectedProjectId && (
          <div 
            className="sidebar-conversations-section"
            style={{ display: collapsed ? 'none' : 'block' }}
          >
            <SidebarConversations
              projectId={selectedProjectId}
              currentConversationId={getCurrentConversationId()}
              onSelectConversation={handleSelectConversation}
              onNewConversation={handleNewConversation}
              refreshTrigger={conversationRefreshTrigger}
            />
          </div>
        )}

        {/* 底部管理功能 */}
        <div className="sidebar-bottom">
          <div className="sidebar-bottom-actions">
            {/* 项目管理按钮 - 只有有项目创建权限的用户可见 */}
            {(userInfo?.role_id === 1 || userInfo?.role_id === 2 || (userInfo?.role_id === 3 && userInfo?.can_create_project)) && (
              <Button 
                type="text"
                icon={<ProjectOutlined />}
                onClick={() => navigate('/projects')}
                className={`sidebar-icon-btn sidebar-management-btn ${getCurrentActiveManagement() === 'projects' ? 'active' : ''}`}
                title={collapsed ? t('common:navigation.projects') : undefined}
              />
            )}
            
            {/* 工具管理按钮 - 基于项目角色权限控制 */}
            {canManageTools() && (
              <Button
                type="text"
                icon={<ToolOutlined />}
                onClick={() => navigate('/tools')}
                className={`sidebar-icon-btn sidebar-management-btn ${getCurrentActiveManagement() === 'tools' ? 'active' : ''}`}
                title={collapsed ? t('common:navigation.tools') : undefined}
              />
            )}

            {/* 模板管理按钮 - 所有用户都可以访问 */}
            <Button
              type="text"
              icon={<FileTextOutlined />}
              onClick={() => navigate('/templates')}
              className={`sidebar-icon-btn sidebar-management-btn ${getCurrentActiveManagement() === 'templates' ? 'active' : ''}`}
              title={collapsed ? t('common:navigation.templates') : undefined}
            />


            {/* 模型管理按钮 - 只有 decisionadmin 账号可见 */}
            {userInfo?.role_id === 1 && (
              <Button
                type="text"
                icon={<RobotOutlined />}
                onClick={() => navigate('/models')}
                className={`sidebar-icon-btn sidebar-management-btn ${getCurrentActiveManagement() === 'models' ? 'active' : ''}`}
                title={collapsed ? t('common:navigation.models') : undefined}
              />
            )}
            
            {/* 用户管理按钮 - 只有企业管理员可见 */}
            {userInfo?.role_id === 2 && (
              <Button
                type="text"
                icon={<UserOutlined />}
                onClick={() => navigate('/users')}
                className={`sidebar-icon-btn sidebar-management-btn ${getCurrentActiveManagement() === 'users' ? 'active' : ''}`}
                title={collapsed ? t('common:navigation.users') : undefined}
              />
            )}
            
            {/* 退出登录按钮 */}
            <Button 
              type="text"
              icon={<LogoutOutlined />}
              onClick={handleLogout}
              className="sidebar-icon-btn sidebar-logout-btn"
              title={collapsed ? t('common:navigation.logout') : undefined}
            />
          </div>
          
          {!collapsed && (
            <div className="sidebar-management-labels">
              <div className="management-label-row">
                {(userInfo?.role_id === 1 || userInfo?.role_id === 2 || (userInfo?.role_id === 3 && userInfo?.can_create_project)) && (
                  <span className={`management-label ${getCurrentActiveManagement() === 'projects' ? 'active' : ''}`}>{t('common:sidebar.projects')}</span>
                )}
                {canManageTools() && (
                  <span className={`management-label ${getCurrentActiveManagement() === 'tools' ? 'active' : ''}`}>{t('common:sidebar.tools')}</span>
                )}
                <span className={`management-label ${getCurrentActiveManagement() === 'templates' ? 'active' : ''}`}>{t('common:sidebar.templates')}</span>
                {userInfo?.role_id === 1 && (
                  <span className={`management-label ${getCurrentActiveManagement() === 'models' ? 'active' : ''}`}>{t('common:sidebar.models')}</span>
                )}
                {userInfo?.role_id === 2 && (
                  <span className={`management-label ${getCurrentActiveManagement() === 'users' ? 'active' : ''}`}>{t('common:sidebar.users')}</span>
                )}
                <span className="management-label logout-label">{t('common:sidebar.logout')}</span>
              </div>
            </div>
          )}
          

        </div>
      </Sider>
      
      <Layout className="app-main-layout">
        {/* 顶部导航栏 */}
        <Header className="app-header">
          <div className="app-header-content">
            <div className="app-header-left">
              {/* 左侧可以放面包屑或页面标题 */}
            </div>
            <div className="app-header-right">
              <Space>
                {/* 当前用户信息 - 简化显示 */}
                <span className="current-user">
                  <UserOutlined style={{ marginRight: 4 }} />
                  {userInfo?.username || 'User'}
                </span>
                
                {/* 语言选择器 */}
                <LanguageSwitch size="small" />
              </Space>
            </div>
          </div>
        </Header>
        
        <Content className="app-content">
          <Routes>
            {/* 默认路由直接跳转到数据分析 */}
            <Route path="/" element={<Navigate to="/analysis/multi-round" replace />} />
            
            {/* 项目相关路由 */}
            <Route path="/projects" element={<ProtectedRoute><ProjectList /></ProtectedRoute>} />
            <Route path="/projects/create" element={<ProtectedRoute><CreateProject /></ProtectedRoute>} />
            <Route path="/projects/:id" element={<ProtectedRoute><ProjectDetail /></ProtectedRoute>} />
            <Route path="/projects/:id/edit" element={<ProtectedRoute><UpdateProject /></ProtectedRoute>} />
            <Route path="/project-detail/:id" element={<ProtectedRoute><ProjectDetail /></ProtectedRoute>} />
            
            {/* 数据源相关路由 */}
            <Route path="/datasources" element={<ProtectedRoute><DataSourceList /></ProtectedRoute>} />
            <Route path="/datasources/create" element={<ProtectedRoute><CreateDataSource /></ProtectedRoute>} />
            <Route path="/datasources/:id" element={<ProtectedRoute><DataSourceDetail /></ProtectedRoute>} />
            <Route path="/datasources/:id/edit" element={<ProtectedRoute><UpdateDataSource /></ProtectedRoute>} />
            
            {/* 工具相关路由 */}
            <Route path="/tools" element={<ProtectedRoute><ToolList /></ProtectedRoute>} />
            <Route path="/tools/create" element={<ProtectedRoute><ToolEdit /></ProtectedRoute>} />
            <Route path="/tools/:id" element={<ProtectedRoute><ToolDetail /></ProtectedRoute>} />
            <Route path="/tools/:id/edit" element={<ProtectedRoute><ToolEdit /></ProtectedRoute>} />

            {/* 模型管理相关路由 */}
            <Route path="/models" element={<ProtectedRoute><ModelList /></ProtectedRoute>} />
            <Route path="/models/create" element={<ProtectedRoute><ModelEdit /></ProtectedRoute>} />
            <Route path="/models/:id/edit" element={<ProtectedRoute><ModelEdit /></ProtectedRoute>} />
            
            {/* 用户管理相关路由 */}
            <Route path="/users" element={<ProtectedRoute><UserList /></ProtectedRoute>} />

            {/* 模板管理相关路由 */}
            <Route path="/templates" element={<ProtectedRoute><TemplateManagement /></ProtectedRoute>} />
            
            {/* 分析相关路由 */}
            <Route path="/analysis" element={<ProtectedRoute><Analysis /></ProtectedRoute>} />
            <Route path="/analysis/history" element={<ProtectedRoute><AnalysisHistory /></ProtectedRoute>} />
            <Route path="/analysis/:id" element={<ProtectedRoute><AnalysisDetail /></ProtectedRoute>} />
            <Route path="/analysis/multi-round" element={<ProtectedRoute><MultiRoundAnalysis /></ProtectedRoute>} />
            <Route path="/analysis/multi-round/:conversationId" element={<ProtectedRoute><MultiRoundAnalysis /></ProtectedRoute>} />
            
            {/* 新增项目向导路由 */}
            <Route path="/project-wizard" element={<ProtectedRoute><ProjectWizard /></ProtectedRoute>} />
            
            {/* 登录路由 */}
            <Route path="/login" element={<Login />} />
          </Routes>
        </Content>
      </Layout>
    </Layout>
  );
};

const App: React.FC = () => {
  return (
    <Router>
      <Routes>
        {/* 管理后台路由 */}
        <Route path="/admin/login" element={<AdminLogin />} />
        <Route path="/admin" element={<AdminProtectedRoute><AdminDashboard /></AdminProtectedRoute>}>
          <Route index element={<Navigate to="users" replace />} />
          <Route path="users" element={<UserManagement />} />
          <Route path="organizations" element={<OrganizationManagement />} />
        </Route>
        
        {/* 所有其他路由都由AppContent处理 */}
        <Route path="/*" element={<AppContent />} />
      </Routes>
    </Router>
  );
};

export default App; 
