import { ToolType } from '../types';
import i18n from '../i18n';

/**
 * 确保工具类型使用正确的大写格式
 * 
 * @param toolType 工具类型字符串
 * @returns 规范化后的工具类型枚举值
 */
export function normalizeToolType(toolType: string | undefined | null): ToolType {
  if (!toolType) return ToolType.SQL; // 默认值
  
  // 统一转换为大写处理
  const upperType = toolType.toUpperCase();
  
  // 尝试匹配枚举值
  switch (upperType) {
    case 'SQL':
      return ToolType.SQL;
    case 'API':
      return ToolType.API;
    case 'PYTHON':
      return ToolType.PYTHON;
    case 'GRAPH':
      return ToolType.GRAPH;
    case 'VECTOR':
      return ToolType.VECTOR;
    case 'CUSTOM':
      return ToolType.CUSTOM;
    case 'AUTO_SQL':
      return ToolType.AUTO_SQL;
    case 'CHART':
      return ToolType.CHART;
    default:
      // 如果无法匹配，返回默认值
      console.warn(`未知工具类型: ${toolType}，使用默认值 SQL`);
      return ToolType.SQL;
  }
}

// 工具类型映射为多语言文本
export const getToolTypeDisplay = (toolType: string): string => {
  // 确保处理小写类型
  const normalizedType = String(toolType || '').toUpperCase();
  
  const mapping: Record<string, string> = {
    [ToolType.SQL]: i18n.t('tool:type.sql'),
    [ToolType.API]: i18n.t('tool:type.api'),
    [ToolType.PYTHON]: i18n.t('tool:type.python'),
    [ToolType.GRAPH]: i18n.t('tool:type.graph'),
    [ToolType.VECTOR]: i18n.t('tool:type.vector'),
    [ToolType.CUSTOM]: i18n.t('tool:type.custom', { defaultValue: '自定义工具' }),
    [ToolType.AUTO_SQL]: i18n.t('tool:type.autoSql', { defaultValue: '自动SQL' }),
    [ToolType.CHART]: i18n.t('tool:type.chart', { defaultValue: '图表工具' })
  };
  
  // 如果能够直接匹配，返回映射的翻译文本
  if (mapping[normalizedType]) {
    return mapping[normalizedType];
  }
  
  // 尝试将字符串转换为ToolType键
  try {
    const typeKey = Object.keys(ToolType).find(
      key => ToolType[key as keyof typeof ToolType] === normalizedType
    );
    if (typeKey && mapping[ToolType[typeKey as keyof typeof ToolType]]) {
      return mapping[ToolType[typeKey as keyof typeof ToolType]];
    }
  } catch (e) {
    console.warn('工具类型转换错误:', e);
  }
  
  // 如果找不到映射，尝试处理小写版本
  const lowerType = normalizedType.toLowerCase();
  const lowerCaseMapping: Record<string, string> = {
    'sql': i18n.t('tool:type.sql'),
    'api': i18n.t('tool:type.api'),
    'python': i18n.t('tool:type.python'),
    'graph': i18n.t('tool:type.graph'),
    'vector': i18n.t('tool:type.vector'),
    'custom': i18n.t('tool:type.custom', { defaultValue: '自定义工具' }),
    'auto_sql': i18n.t('tool:type.autoSql', { defaultValue: '自动SQL' }),
    'chart': i18n.t('tool:type.chart', { defaultValue: '图表工具' })
  };
  
  if (lowerCaseMapping[lowerType]) {
    return lowerCaseMapping[lowerType];
  }
  
  // 最后返回原始值，保证总是有显示
  return toolType || i18n.t('common:labels.unknown', { defaultValue: '未知类型' });
};

// 获取工具类型对应的标签颜色
export const getToolTypeColor = (toolType: string): string => {
  // 确保处理小写类型
  const normalizedType = String(toolType || '').toUpperCase();
  
  const mapping: Record<string, string> = {
    [ToolType.SQL]: 'blue',
    [ToolType.API]: 'green',
    [ToolType.PYTHON]: 'purple',
    [ToolType.GRAPH]: 'orange',
    [ToolType.VECTOR]: 'geekblue',
    [ToolType.CUSTOM]: 'cyan',
    [ToolType.AUTO_SQL]: 'magenta',
    [ToolType.CHART]: 'gold'
  };
  
  // 直接匹配
  if (mapping[normalizedType]) {
    return mapping[normalizedType];
  }
  
  // 尝试处理小写版本
  const lowerType = normalizedType.toLowerCase();
  const lowerCaseMapping: Record<string, string> = {
    'sql': 'blue',
    'api': 'green',
    'python': 'purple',
    'graph': 'orange',
    'vector': 'geekblue',
    'custom': 'cyan',
    'auto_sql': 'magenta',
    'chart': 'gold'
  };
  
  if (lowerCaseMapping[lowerType]) {
    return lowerCaseMapping[lowerType];
  }
  
  return 'default';
}; 