import i18n from '../i18n';

/**
 * 格式化日期时间字符串为易读格式
 * 
 * @param dateString 日期时间字符串
 * @returns 格式化后的字符串，格式：YYYY-MM-DD HH:mm:ss
 */
export function formatDateTime(dateString: string | Date | null | undefined): string {
  if (!dateString) return '';
  
  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
  
  // 检查日期是否有效
  if (isNaN(date.getTime())) return '';
  
  // 格式化年月日
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  
  // 格式化时分秒
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

/**
 * 计算相对时间（如：刚刚，5分钟前，1小时前等）
 * 
 * @param dateString 日期时间字符串
 * @returns 相对时间字符串
 */
export function getRelativeTime(dateString: string | Date | null | undefined): string {
  if (!dateString) return '';
  
  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
  
  // 检查日期是否有效
  if (isNaN(date.getTime())) return '';
  
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
  
  // 刚刚（小于1分钟）
  if (diffInSeconds < 60) {
    return i18n.t('common:timeRelative.justNow');
  }
  
  // x分钟前（小于1小时）
  if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return i18n.t('common:timeRelative.minutesAgo', { count: minutes });
  }
  
  // x小时前（小于1天）
  if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return i18n.t('common:timeRelative.hoursAgo', { count: hours });
  }
  
  // x天前（小于1个月，这里简单按30天算）
  if (diffInSeconds < 2592000) {
    const days = Math.floor(diffInSeconds / 86400);
    if (days === 1) {
      return i18n.t('common:timeRelative.yesterday');
    } else {
      return i18n.t('common:timeRelative.daysAgo', { count: days });
    }
  }
  
  // 超过1个月，显示完整日期
  return formatDateTime(date);
}

/**
 * 格式化日期（只显示年月日）
 * 
 * @param dateString 日期时间字符串
 * @returns 格式化后的字符串，格式：YYYY-MM-DD
 */
export function formatDate(dateString: string | Date | null | undefined): string {
  if (!dateString) return '';
  
  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
  
  // 检查日期是否有效
  if (isNaN(date.getTime())) return '';
  
  // 格式化年月日
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  
  return `${year}-${month}-${day}`;
} 