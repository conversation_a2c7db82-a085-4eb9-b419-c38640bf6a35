/**
 * 个人模板使用记录本地存储管理
 * 用于记录每个用户的模板使用历史，实现真正的个性化推荐
 */

export interface PersonalTemplateUsage {
  templateId: string;
  templateName: string;
  templateContent: string;
  templateDescription?: string;
  category?: string;
  lastUsedAt: string;  // ISO时间字符串
  usageCount: number;  // 个人使用次数
}

export interface PersonalTemplateRecord {
  userId: string;
  projectId: string;
  templates: PersonalTemplateUsage[];
  lastUpdated: string;
}

class PersonalTemplateStorageManager {
  private readonly STORAGE_KEY = 'personal_template_usage';
  private readonly MAX_RECORDS_PER_USER = 50; // 每个用户最多记录50个模板
  private readonly MAX_DISPLAY_COUNT = 6;     // 最多显示6个常用模板

  /**
   * 获取存储键，基于用户ID和项目ID
   */
  private getStorageKey(userId: string, projectId: string): string {
    return `${this.STORAGE_KEY}_${userId}_${projectId}`;
  }

  /**
   * 记录模板使用
   */
  recordTemplateUsage(
    userId: string, 
    projectId: string, 
    templateId: string, 
    templateName: string,
    templateContent: string,
    templateDescription?: string,
    category?: string
  ): void {
    try {
      const storageKey = this.getStorageKey(userId, projectId);
      const existingData = localStorage.getItem(storageKey);
      
      let record: PersonalTemplateRecord;
      
      if (existingData) {
        record = JSON.parse(existingData);
      } else {
        record = {
          userId,
          projectId,
          templates: [],
          lastUpdated: new Date().toISOString()
        };
      }

      // 查找是否已存在该模板记录
      const existingTemplateIndex = record.templates.findIndex(
        t => t.templateId === templateId
      );

      if (existingTemplateIndex >= 0) {
        // 更新现有记录
        record.templates[existingTemplateIndex] = {
          ...record.templates[existingTemplateIndex],
          templateName,
          templateContent,
          templateDescription,
          category,
          lastUsedAt: new Date().toISOString(),
          usageCount: record.templates[existingTemplateIndex].usageCount + 1
        };
      } else {
        // 添加新记录
        const newUsage: PersonalTemplateUsage = {
          templateId,
          templateName,
          templateContent,
          templateDescription,
          category,
          lastUsedAt: new Date().toISOString(),
          usageCount: 1
        };
        
        record.templates.push(newUsage);
      }

      // 按最后使用时间排序，保持最新使用的在前面
      record.templates.sort((a, b) => 
        new Date(b.lastUsedAt).getTime() - new Date(a.lastUsedAt).getTime()
      );

      // 限制记录数量，移除最旧的记录
      if (record.templates.length > this.MAX_RECORDS_PER_USER) {
        record.templates = record.templates.slice(0, this.MAX_RECORDS_PER_USER);
      }

      record.lastUpdated = new Date().toISOString();
      
      localStorage.setItem(storageKey, JSON.stringify(record));
      
      console.log(`✅ 记录模板使用: ${templateName} (用户: ${userId}, 项目: ${projectId})`);
    } catch (error) {
      console.error('记录模板使用失败:', error);
    }
  }

  /**
   * 获取个人常用模板列表
   * 按使用频率和最近使用时间综合排序
   */
  getPersonalTemplates(userId: string, projectId: string): PersonalTemplateUsage[] {
    try {
      const storageKey = this.getStorageKey(userId, projectId);
      const existingData = localStorage.getItem(storageKey);
      
      if (!existingData) {
        return [];
      }

      const record: PersonalTemplateRecord = JSON.parse(existingData);
      
      // 综合排序：使用次数权重70%，时间新近度权重30%
      const now = new Date().getTime();
      const sortedTemplates = record.templates
        .map(template => {
          const daysSinceLastUse = (now - new Date(template.lastUsedAt).getTime()) / (1000 * 60 * 60 * 24);
          const timeScore = Math.max(0, 30 - daysSinceLastUse); // 30天内的时间得分
          const usageScore = template.usageCount * 10; // 使用次数得分
          const totalScore = usageScore * 0.7 + timeScore * 0.3;
          
          return {
            ...template,
            score: totalScore
          };
        })
        .sort((a, b) => b.score - a.score)
        .slice(0, this.MAX_DISPLAY_COUNT);

      console.log(`📊 获取个人常用模板: ${sortedTemplates.length}个 (用户: ${userId}, 项目: ${projectId})`);
      return sortedTemplates;
    } catch (error) {
      console.error('获取个人常用模板失败:', error);
      return [];
    }
  }

  /**
   * 获取基于数据库模板过滤的个人常用模板列表
   * 只返回在数据库中实际存在的模板
   */
  getPersonalTemplatesFilteredByDatabase(
    userId: string,
    projectId: string,
    databaseTemplates: Array<{id: string, name: string, content: string, description?: string, category?: string}>
  ): PersonalTemplateUsage[] {
    try {
      // 获取所有个人模板记录
      const personalTemplates = this.getPersonalTemplates(userId, projectId);

      // 创建数据库模板ID的Set，用于快速查找
      const databaseTemplateIds = new Set(databaseTemplates.map(t => t.id));

      // 过滤出在数据库中存在的个人模板
      const filteredTemplates = personalTemplates.filter(personalTemplate => {
        // 检查是否是默认模板（以default_开头）或在数据库中存在
        return personalTemplate.templateId.startsWith('default_') ||
               databaseTemplateIds.has(personalTemplate.templateId);
      });

      // 对于数据库中存在的模板，更新其信息以确保数据一致性
      const updatedTemplates = filteredTemplates.map(personalTemplate => {
        const dbTemplate = databaseTemplates.find(t => t.id === personalTemplate.templateId);
        if (dbTemplate) {
          return {
            ...personalTemplate,
            templateName: dbTemplate.name,
            templateContent: dbTemplate.content,
            templateDescription: dbTemplate.description,
            category: dbTemplate.category
          };
        }
        return personalTemplate;
      });

      console.log(`📊 获取过滤后的个人常用模板: ${updatedTemplates.length}个 (用户: ${userId}, 项目: ${projectId})`);
      return updatedTemplates;
    } catch (error) {
      console.error('获取过滤后的个人常用模板失败:', error);
      return [];
    }
  }

  /**
   * 清除指定用户和项目的模板使用记录
   */
  clearPersonalTemplates(userId: string, projectId: string): void {
    try {
      const storageKey = this.getStorageKey(userId, projectId);
      localStorage.removeItem(storageKey);
      console.log(`🗑️ 清除个人模板记录 (用户: ${userId}, 项目: ${projectId})`);
    } catch (error) {
      console.error('清除个人模板记录失败:', error);
    }
  }

  /**
   * 清除所有个人模板使用记录
   */
  clearAllPersonalTemplates(): void {
    try {
      const keysToRemove: string[] = [];

      // 遍历所有localStorage键，找到个人模板相关的键
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith(this.STORAGE_KEY)) {
          keysToRemove.push(key);
        }
      }

      // 删除找到的键
      keysToRemove.forEach(key => {
        localStorage.removeItem(key);
      });

      console.log(`🗑️ 清除所有个人模板记录，共删除 ${keysToRemove.length} 条记录`);
    } catch (error) {
      console.error('清除所有个人模板记录失败:', error);
    }
  }

  /**
   * 获取模板使用统计信息
   */
  getUsageStats(userId: string, projectId: string): {
    totalTemplates: number;
    totalUsages: number;
    mostUsedTemplate?: PersonalTemplateUsage;
    recentTemplate?: PersonalTemplateUsage;
  } {
    try {
      const templates = this.getPersonalTemplates(userId, projectId);
      const allTemplates = this.getAllPersonalTemplates(userId, projectId);
      
      const totalUsages = allTemplates.reduce((sum, t) => sum + t.usageCount, 0);
      const mostUsedTemplate = allTemplates.reduce((max, t) => 
        t.usageCount > (max?.usageCount || 0) ? t : max, undefined as PersonalTemplateUsage | undefined
      );
      const recentTemplate = allTemplates.sort((a, b) => 
        new Date(b.lastUsedAt).getTime() - new Date(a.lastUsedAt).getTime()
      )[0];

      return {
        totalTemplates: allTemplates.length,
        totalUsages,
        mostUsedTemplate,
        recentTemplate
      };
    } catch (error) {
      console.error('获取使用统计失败:', error);
      return {
        totalTemplates: 0,
        totalUsages: 0
      };
    }
  }

  /**
   * 获取所有个人模板记录（不限制数量）
   */
  private getAllPersonalTemplates(userId: string, projectId: string): PersonalTemplateUsage[] {
    try {
      const storageKey = this.getStorageKey(userId, projectId);
      const existingData = localStorage.getItem(storageKey);
      
      if (!existingData) {
        return [];
      }

      const record: PersonalTemplateRecord = JSON.parse(existingData);
      return record.templates || [];
    } catch (error) {
      console.error('获取所有个人模板记录失败:', error);
      return [];
    }
  }
}

// 导出单例实例
export const personalTemplateStorage = new PersonalTemplateStorageManager();
