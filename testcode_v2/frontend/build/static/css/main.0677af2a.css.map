{"version": 3, "file": "static/css/main.0677af2a.css", "mappings": "AACA,iBACE,qBACE,wBAAyB,CACzB,iBAAkB,CAClB,kBACF,CAEA,2BACE,oBACF,CAEA,yCACE,oBAAqB,CACrB,8BACF,CACF,CAGA,iBAEE,kBAAmB,CADnB,YAAa,CAEb,OAAQ,CAER,eACE,cACF,CAEA,gBACE,eACF,CACF,CAGA,sBAEE,kBAAmB,CADnB,YAAa,CAEb,OAAQ,CAER,iBACE,eACF,CAEA,eACE,SACE,WAAY,CACZ,eAAgB,CAChB,eAAgB,CAEhB,QACE,0BACF,CACF,CACF,CACF,CAGA,yBACE,sBACE,iBACE,cACF,CAEA,2CACE,YACF,CACF,CACF,CCjEA,QAME,oBAAqB,CALrB,eAAgB,CAChB,oBAAqB,CACrB,yBAA0B,CAC1B,qBAAsB,CACtB,uBAEF,CAEA,QAEE,eAAiB,CADjB,gBAEF,CAEA,gBACE,gBACF,CAEA,QACE,cACF,CAEA,QACE,eAAgB,CAChB,gBAAiB,CACjB,wBAAyB,CAEjB,gBACV,CAEA,cACE,eACF,CAEA,cACE,eACF,CAEA,QACE,gBACF,CAEA,cACE,aAAc,CACd,cACF,CAEA,QACE,eACF,CAEA,QACE,aAAc,CACd,SACF,CAGA,QAGE,UAAc,CAFd,eAAgB,CAChB,gBAEF,CAKA,QACE,UACF,CAMA,gBACE,aACF,CAEA,QACE,aACF,CAEA,QACE,aACF,CAEA,QACE,aACF,CAEA,QACE,aACF,CAUA,wBACE,UACF,CAGA,QACE,kBACF,CAcA,gCAHE,aAOF,CAJA,QACE,kBAAmB,CACnB,gBAEF,CAKA,QACE,aACF,CAMA,gBACE,aACF,CAEA,QACE,aACF,CAEA,QACE,aACF,CAEA,QACE,aACF,CAEA,QACE,aACF,CC/JA,gBAME,wBAAyB,CALzB,wBAAyB,CAGzB,cAAe,CACf,eAAgB,CAFhB,aAAc,CADd,UAKF,CAEA,gBACE,wBACF,CAEA,aAIE,eAA0B,CAD1B,eAAgB,CAFhB,YAAa,CACb,eAAgB,CAIhB,kBACF,CAEA,0BAJE,+BAMF,CAEA,mBACE,wBACF,CAEA,aAGE,+BAAgC,CAFhC,YAAa,CACb,eAEF,CAGA,aACE,cAAe,CACf,eACF,CAEA,gGAOE,kBAAmB,CADnB,eAEF,CAEA,eACE,kBACF,CAEA,gCAEE,kBAAmB,CACnB,iBACF,CAEA,kBACE,wBAAyB,CAEzB,iBAAkB,CAClB,2EAAqF,CAFrF,eAGF,CAEA,iBACE,wBAAyB,CAEzB,iBAAkB,CAElB,kBAAmB,CADnB,aAAc,CAFd,YAIF,CAEA,wBAIE,0BAA2B,CAD3B,UAAW,CAFX,aAAc,CACd,cAGF,CAGA,oCACE,gBAGE,gCAAiC,CAFjC,aAAc,CACd,eAEF,CAEA,0BAEE,kBACF,CACF,CAGA,oBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAeA,wBAKE,6DAAgE,CAHhE,YAAa,CACb,QAAS,CAFT,cAAe,CAGf,YAEF,CAGA,oCACE,wBACE,WAAY,CACZ,QAAS,CACT,0BACF,CACF,CAeA,2BACE,GACE,SAAU,CACV,2BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,uBACE,GACE,SAAU,CACV,oBACF,CACA,GACE,SAAU,CACV,kBACF,CACF,CAGA,oBACE,kCACF,CAEA,wBACE,sCACF,CAEA,sBACE,sCACF,CAGA,6BACE,6BACF,CAGA,wBACE,kDAA6D,CAC7D,uBACF,CAEA,8BACE,kDACF,CAEA,wBACE,kDAA6D,CAC7D,uBACF,CAEA,8BACE,kDACF,CClNA,gBACE,eAAiB,CACjB,kBAAmB,CAGnB,YAAa,CACb,qBAAsB,CAHtB,gBAAiB,CACjB,eAGF,CAEA,iBAGE,kBAAmB,CAGnB,kBAAmB,CADnB,+BAAgC,CAJhC,YAAa,CACb,6BAA8B,CAE9B,iBAGF,CAEA,oBAIE,aAAc,CAFd,cAAe,CACf,eAAgB,CAFhB,QAIF,CAEA,WAUE,kBAAmB,CATnB,eAAgB,CAChB,WAAY,CAUZ,iBAAkB,CAPlB,aAAc,CADd,cAAe,CAKf,YAAa,CANb,cAAe,CAKf,WAAY,CAGZ,sBAAuB,CALvB,SAAU,CAOV,kBAAoB,CANpB,UAOF,CAEA,iBACE,kBAAmB,CACnB,aACF,CAEA,kBAGE,kBAAmB,CADnB,+BAAgC,CADhC,iBAGF,CAEA,cAQE,eAAiB,CALjB,wBAAyB,CACzB,kBAAmB,CAEnB,cAAe,CADf,kBAAmB,CAHnB,iBAAkB,CAKlB,uBAAyB,CANzB,UAQF,CAEA,oBAEE,oBAAqB,CACrB,8BAA8C,CAF9C,YAGF,CAEA,YAIE,cAAe,CAFf,QAGF,CAEA,0BAJE,kBAAmB,CAFnB,YAUF,CAJA,cAGE,OACF,CAEA,oBAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,kBACF,CAEA,eAKE,eAAiB,CAHjB,wBAAyB,CACzB,iBAAkB,CAGlB,aAAc,CAFd,cAAe,CAIf,eAAgB,CAPhB,gBAAiB,CAMjB,uBAEF,CAEA,qBAEE,oBAAqB,CACrB,8BAA8C,CAF9C,YAGF,CAEA,eACE,YAAa,CAEb,cAAe,CADf,OAEF,CAEA,cAIE,eAAiB,CAFjB,wBAAyB,CACzB,kBAAmB,CAEnB,cAAe,CACf,cAAe,CALf,gBAAiB,CAMjB,kBAAoB,CACpB,kBACF,CAEA,oBACE,kBACF,CAEA,qBACE,kBAAmB,CAEnB,oBAAqB,CADrB,UAEF,CAEA,eAME,aAAS,CAFT,YAAa,CAHb,QAAO,CAKP,QAAS,CADT,yDAA4D,CAE5D,gBAAiB,CALjB,eAAgB,CAChB,iBAKF,CAEA,gBAIE,aAAc,CACd,cAAe,CAJf,gBAAmB,CAEnB,iBAAkB,CADlB,iBAIF,CAGA,eAKE,eAAiB,CAJjB,wBAAyB,CACzB,kBAAmB,CAInB,8BAAyC,CACzC,cAAe,CAJf,YAAa,CACb,uBAIF,CAEA,qBACE,oBAAqB,CACrB,+BAAgD,CAChD,0BACF,CAEA,sBAGE,sBAAuB,CAFvB,YAAa,CACb,6BAA8B,CAE9B,iBACF,CAEA,eAKE,kBAAmB,CAEnB,aAAc,CAHd,YAAa,CAIb,QAAO,CANP,cAAe,CACf,eAAgB,CAGhB,OAAQ,CALR,QAQF,CAEA,4BAGE,iBAAkB,CAFlB,cAAe,CAGf,eAAmB,CAFnB,eAGF,CAEA,cACE,kBAAmB,CACnB,aACF,CAEA,cACE,kBAAmB,CACnB,aACF,CAEA,eAKE,oBAAqB,CAHrB,aAAc,CACd,YAAa,CACb,qBAAsB,CAHtB,cAAe,CAKf,OAAQ,CACR,gBACF,CAEA,aACE,UAAW,CACX,cACF,CAEA,SACE,UAAW,CACX,cACF,CAEA,YAGE,iBAAkB,CAFlB,cAAe,CAGf,eAAgB,CAFhB,eAGF,CAEA,mBACE,kBAAmB,CAEnB,wBAAyB,CADzB,aAEF,CAEA,oBACE,kBAAmB,CAEnB,wBAAyB,CADzB,UAEF,CAEA,sBAKE,kBAAmB,CAGnB,6BAA8B,CAD9B,iBAAkB,CANlB,aAAc,CACd,cAAe,CAEf,eAAgB,CADhB,aAAc,CAGd,YAGF,CAEA,kBACE,YAAa,CACb,QAAS,CACT,wBAAyB,CACzB,eACF,CAEA,sBAOE,gBAAiB,CALjB,kBAAmB,CAGnB,cAAe,CAFf,cAAe,CACf,eAAgB,CAHhB,gBAAiB,CAKjB,uBAEF,CAEA,aAEE,eAAiB,CADjB,oBAAqB,CAErB,aACF,CAEA,mBACE,kBAAmB,CACnB,oBAAqB,CACrB,0BACF,CAEA,iBAEE,kDAA6D,CAD7D,oBAAqB,CAErB,UACF,CAEA,uBAEE,+BAA+C,CAD/C,0BAEF,CAEA,kBAGE,kBAAmB,CAEnB,wBAAyB,CADzB,kBAAmB,CAHnB,eAAgB,CAChB,YAIF,CAEA,gBAME,kBAAmB,CAHnB,aAAc,CAEd,YAAa,CAJb,cAAe,CACf,eAAgB,CAKhB,OAAQ,CAHR,kBAIF,CAEA,iBACE,eAAiB,CAKjB,gBAIF,CAEA,iBACE,YAAa,CACb,OACF,CAEA,eAGE,eAAiB,CADjB,wBAAyB,CAGzB,iBAAkB,CADlB,aAAc,CAGd,cAAe,CADf,cAAe,CALf,eAAgB,CAOhB,uBACF,CAEA,qBACE,kBAAmB,CACnB,UACF,CAGA,uEAEE,SACF,CAEA,mFAEE,kBAAmB,CACnB,iBACF,CAEA,mFAEE,kBAAmB,CACnB,iBACF,CAEA,+FAEE,kBACF,CAGA,yBACE,gBACE,gBACF,CAMA,iCACE,iBACF,CAEA,eACE,YACF,CAEA,eACE,OACF,CAEA,cAEE,cAAe,CADf,eAEF,CAEA,sBAEE,sBAAuB,CADvB,qBAAsB,CAEtB,OACF,CAEA,eACE,sBAAuB,CACvB,eACF,CAEA,YAEE,mBAAoB,CADpB,qBAAsB,CAEtB,QACF,CAEA,cACE,6BACF,CAEA,eAEE,QAAO,CADP,cAEF,CACF,CAGA,wBAEE,kBAAmB,CADnB,YAAa,CAEb,6BAA8B,CAC9B,iBACF,CAEA,iBACE,YAAa,CAEb,cAAe,CADf,OAEF,CAEA,OAGE,iBAAkB,CAIlB,UAAY,CANZ,cAAe,CAGf,eAAgB,CAEhB,mBAAqB,CAJrB,eAAgB,CAGhB,wBAGF,CAEA,cACE,kDACF,CAEA,cACE,kDACF,CAMA,gBAEE,kBAAmB,CAGnB,aAAc,CAJd,YAAa,CAGb,cAAe,CADf,QAAS,CAGT,cACF,CAGA,wBAEE,YAAa,CACb,qBAAsB,CACtB,OAAQ,CAHR,kBAIF,CAEA,UAEE,aAAc,CADd,cAAe,CAEf,eACF,CAEA,MACE,YAAa,CAEb,cAAe,CADf,OAEF,CAEA,KAGE,kBAAmB,CAEnB,iBAAkB,CADlB,aAAc,CAHd,cAAe,CAKf,eAAgB,CAJhB,eAKF,CAEA,UACE,kBAAmB,CACnB,aACF,CCnfA,0BACE,YAAa,CACb,qBAAsB,CACtB,QAAS,CACT,gBAAiB,CACjB,eACF,CAEA,gBACE,kBAAmB,CAGnB,wBAAyB,CADzB,kBAAmB,CADnB,YAGF,CAEA,mBAME,kBAAmB,CAJnB,aAAc,CAGd,YAAa,CAFb,cAAe,CACf,eAAgB,CAGhB,OAAQ,CANR,eAOF,CAEA,mBACE,eAAiB,CAGjB,wBAAyB,CADzB,kBAAmB,CADnB,YAGF,CAEA,sBAME,kBAAmB,CAJnB,aAAc,CAGd,YAAa,CAFb,cAAe,CACf,eAAgB,CAGhB,OAAQ,CANR,eAOF,CAEA,iBASE,oBAAqB,CARrB,kBAAmB,CAGnB,wBAAyB,CADzB,iBAAkB,CAIlB,aAAc,CAFd,cAAe,CACf,eAAgB,CAIhB,gBAAiB,CACjB,eAAgB,CAThB,YAAa,CAMb,oBAIF,CAEA,eAGE,aAAc,CADd,iBAAkB,CADlB,iBAGF,CAEA,iBACE,cAAe,CACf,kBACF,CAEA,kBASE,oBAAqB,CARrB,kBAAmB,CAGnB,wBAAyB,CADzB,iBAAkB,CAIlB,aAAc,CAFd,cAAe,CACf,eAAgB,CAJhB,YAAa,CAQb,eAAgB,CAFhB,oBAGF,CAQA,4CAIE,wBAAyB,CADzB,iBAAkB,CAElB,uBACF,CAEA,iGAIE,oBAAqB,CACrB,8BACF,CAGA,yBACE,0BACE,QACF,CAEA,mCAEE,YACF,CAEA,iBACE,gBACF,CACF,CCnHA,eAGE,kDAA6D,CAC7D,UAAY,CAEZ,eAAgB,CAJhB,mBAAsB,CAGtB,iBAAkB,CAJlB,iBAMF,CAEA,sBAOE,gWAAsV,CADtV,QAAS,CALT,UAAW,CAGX,MAAO,CAIP,UAAY,CANZ,iBAAkB,CAGlB,OAAQ,CAFR,KAMF,CAEA,aAGE,kBAAmB,CAFnB,iBAAkB,CAClB,SAEF,CAEA,cACE,oBAAuB,CAEvB,0BAA4B,CAC5B,yBAA2B,CAE3B,eAAgB,CAJhB,4BAA8B,CAG9B,2BAEF,CAEA,iBACE,yBAA0C,CAG1C,aAAc,CAFd,0BAA4B,CAC5B,eAAgB,CAEhB,cACF,CAEA,aAGE,kBAAmB,CAFnB,iBAAkB,CAClB,SAEF,CAEA,WAEE,kBAAmB,CAEnB,eAA+B,CAH/B,YAAa,CAEb,OAEF,CAEA,WACE,WAA+B,CAC/B,cACF,CAEA,YAIE,kCAA2B,CAA3B,0BAA2B,CAH3B,0BAA+C,CAE/C,oCAAqD,CADrD,oBAAuB,CAGvB,eACF,CAEA,cAGE,eAAgB,CAFhB,iBAAkB,CAClB,SAEF,CAEA,YAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAG3D,aAAc,CADd,eAEF,CAEA,WAGE,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAoC,CAEpC,sBAA0C,CAC1C,kBAAmB,CACnB,iBAAkB,CALlB,iBAAkB,CAMlB,uBACF,CAEA,iBACE,oBAAqC,CACrC,0BACF,CAEA,YAGE,UAAY,CAFZ,gBAAiB,CACjB,eAAgB,CAEhB,iBACF,CAEA,YAEE,WAA+B,CAD/B,eAAiB,CAEjB,eACF,CAEA,gBAME,mFAMC,CAVD,QAAS,CAGT,UAAW,CAFX,MAAO,CAFP,iBAAkB,CAGlB,OASF,CAGA,yBACE,eACE,sBACF,CAEA,cACE,wBACF,CAEA,iBACE,wBACF,CAEA,YAEE,QAAS,CADT,wDAEF,CAEA,WACE,iBACF,CAEA,YACE,gBACF,CAEA,YACE,eACF,CACF,CAEA,yBACE,wBACE,sBACF,CAEA,WACE,qBAAsB,CAEtB,OAAQ,CADR,iBAEF,CAEA,YACE,mCACF,CACF,CC5KA,oBACE,kDAA6D,CAC7D,kBAAmB,CAEnB,UAAY,CAEZ,eAAgB,CAHhB,YAAa,CAEb,iBAEF,CAEA,2BAOE,4HAC4F,CAF5F,QAAS,CALT,UAAW,CAGX,MAAO,CAKP,mBAAoB,CAPpB,iBAAkB,CAGlB,OAAQ,CAFR,KAOF,CAEA,gBACE,oBAAuB,CAGvB,0BAA4B,CAC5B,yBAA2B,CAF3B,4BAA8B,CAD9B,iBAAkB,CAIlB,2BAGF,CAEA,iCAJE,iBAAkB,CAClB,SAMF,CAEA,gBACE,YACF,CAEA,iBAIE,kCAA2B,CAA3B,0BAA2B,CAD3B,8BAAgD,CAEhD,oCAAqD,CACrD,4BAA8B,CAE9B,cAAe,CANf,YAAa,CAQb,eAAgB,CADhB,iBAAkB,CAFlB,uBAAyB,CANzB,UAUF,CAEA,uBACE,yBAA6C,CAE7C,yCAAqD,CADrD,0BAEF,CAEA,gCAGE,YAAa,CACb,qBAAsB,CAFtB,WAAY,CAGZ,sBAAuB,CAJvB,sBAKF,CAEA,oBAGE,YAAa,CACb,qBAAsB,CAFtB,WAAY,CAGZ,6BAA8B,CAJ9B,iBAKF,CAEA,cAEE,UAAW,CADX,eAAiB,CAEjB,eAAgB,CAEhB,eAAgB,CADhB,iBAEF,CAEA,wBAKE,kBAAmB,CAHnB,YAAa,CADb,QAAO,CAEP,qBAAsB,CAGtB,OAAQ,CAFR,sBAGF,CAEA,cAGE,aAAc,CAFd,gBAAiB,CACjB,eAAgB,CAEhB,aACF,CAEA,eAEE,kBAAmB,CADnB,YAAa,CAGb,gBAAkB,CADlB,OAGF,CAEA,4BAHE,eAKF,CAGA,iBAME,2BAA4B,CAD5B,WAAY,CAJZ,iBAAkB,CAElB,OAAQ,CADR,KAAM,CAEN,SAGF,CAEA,gBACE,kDACF,CAEA,gBACE,kDACF,CAEA,cACE,kDACF,CAGA,+BACE,aACF,CAEA,+BACE,aACF,CAEA,6BACE,aACF,CAGA,qBAEE,kBAAmB,CADnB,YAAa,CAEb,sBAAuB,CACvB,eAAgB,CAChB,iBAAkB,CAClB,SACF,CAEA,iBAGE,2DAAkG,CAFlG,QAAO,CACP,UAEF,CAEA,iBACE,YAAa,CACb,OAAQ,CACR,aACF,CAEA,KAKE,2BAA4B,CAD5B,gBAAoC,CADpC,iBAAkB,CADlB,UAAW,CADX,SAKF,CAEA,kBACE,mBACF,CAEA,kBACE,kBACF,CAcA,yBACE,oBACE,iBACF,CAEA,gBACE,0BAA4B,CAC5B,4BACF,CAEA,iBACE,YACF,CAEA,cACE,gBACF,CAEA,cACE,eACF,CAEA,eACE,gBACF,CACF,CAEA,yBACE,oBACE,iBACF,CAEA,iBACE,YACF,CAEA,gCACE,sBACF,CAEA,cACE,gBACF,CAEA,cACE,gBAAkB,CAClB,iBACF,CACF,CAGA,aACE,oBACE,4BAA8B,CAE9B,qBACF,CAEA,oCAJE,oBAMF,CAEA,iBACE,yBAA4B,CAC5B,+BACF,CACF,CCvQA,6BACE,iBACF,CAEA,wBACE,4DAAwE,CACxE,uCAAyC,CACzC,4BAA8B,CAC9B,yCAAyD,CAEzD,eAAgB,CADhB,iBAEF,CAEA,uCACE,sBAAwB,CACxB,iBAAkB,CAClB,SACF,CAGA,gBAGE,iCAAgD,CAFhD,kBAAmB,CACnB,mBAEF,CAEA,aAGE,kDAA6D,CAC7D,iBAAkB,CAMlB,+BAA8C,CAD9C,cAAe,CAPf,WAAY,CADZ,UAUF,CAEA,eACE,uBAAyB,CAGzB,0BAA4B,CAD5B,yBAA2B,CAD3B,kBAGF,CAGA,iBACE,kBACF,CAEA,cAGE,oBAAsB,CAFtB,0BAA4B,CAK5B,eAAgB,CAJhB,yBAA2B,CAE3B,kBAAoB,CACpB,kBAEF,CAGA,oBACE,gBAAoC,CAGpC,0BAAyC,CAFzC,iBAAkB,CAClB,YAEF,CAEA,kBAME,kBAAmB,CALnB,uBAAyB,CAIzB,YAAa,CAFb,0BAA4B,CAC5B,yBAA2B,CAG3B,OAAQ,CALR,yBAMF,CAEA,yBACE,WAAY,CACZ,cACF,CAEA,iBACE,QACF,CAEA,gCACE,2CAA4D,CAC5D,wBACF,CAEA,2CACE,4BACF,CAEA,gBACE,uBACF,CAEA,sBACE,oBAAoC,CACpC,iBAAkB,CAClB,0BAA4B,CAC5B,2BACF,CAEA,YACE,aAAc,CACd,cAAe,CACf,cACF,CAEA,YACE,UAAW,CACX,gBAAkB,CAElB,eAAgB,CADhB,eAEF,CAGA,oBAME,eAAgB,CAChB,mBAAoB,CAJpB,OAAQ,CADR,KAMF,CAEA,wCALE,YAAa,CAJb,iBAAkB,CAGlB,WAcF,CARA,oBAME,wDAAgF,CAChF,iBAAkB,CAJlB,WAAY,CADZ,SAMF,CAEA,2BAOE,wDAAiF,CACjF,iBAAkB,CAPlB,UAAW,CAKX,WAAY,CAFZ,SAAU,CAFV,iBAAkB,CAClB,QAAS,CAET,UAIF,CAGA,yBACE,uCACE,2BACF,CAEA,eACE,0BACF,CAEA,cACE,wBAA0B,CAC1B,yBACF,CAEA,oBACE,YACF,CAEA,aAGE,cAAe,CADf,WAAY,CADZ,UAGF,CAEA,oBAEE,WAAY,CAEZ,WAAY,CADZ,SAAU,CAFV,UAIF,CACF,CAEA,yBACE,uCACE,2BACF,CAEA,gBACE,kBAAmB,CACnB,mBACF,CAEA,eACE,0BACF,CAEA,cACE,0BACF,CAEA,oBACE,YACF,CAEA,kBACE,wBACF,CAEA,YACE,eACF,CACF,CAGA,aACE,wBACE,yBAA4B,CAC5B,oCAAsC,CACtC,yBACF,CAUA,8CAHE,oBAMF,CAHA,aACE,4BAEF,CAEA,oBACE,YACF,CACF,CC5OA,kCAHE,iBAWF,CARA,YACE,yBAA4B,CAC5B,kCAAoC,CACpC,4BAA8B,CAC9B,yCAAqD,CAErD,eAAgB,CADhB,uBAGF,CAEA,kBACE,yCAAqD,CACrD,0BACF,CAEA,2BACE,4DAAwE,CACxE,yCAA2C,CAE3C,eAAgB,CADhB,wBAEF,CAMA,6DACE,wBACF,CAEA,2BACE,sBACF,CAGA,mBACE,UACF,CAEA,YAME,kBAAmB,CAHnB,kDAA6D,CAC7D,iBAAkB,CAMlB,8BAA6C,CAF7C,UAAY,CAHZ,YAAa,CAIb,cAAe,CAPf,WAAY,CAKZ,sBAAuB,CANvB,UAUF,CAEA,kBACE,QACF,CAEA,aACE,oBAAsB,CAEtB,0BAA4B,CAC5B,yBAA2B,CAC3B,eAAgB,CAHhB,kBAIF,CAEA,kBAEE,oBAAmC,CAGnC,kBAAmB,CAFnB,aAAc,CAFd,oBAAqB,CAKrB,gBAAkB,CAClB,eAAgB,CAChB,cAAe,CAJf,eAAgB,CAKhB,wBACF,CAGA,kBAGE,0BAAkC,CADlC,qBAAuB,CADvB,oBAAsB,CAGtB,uBACF,CAEA,wBAEE,8BAA8C,CAD9C,uBAAyB,CAEzB,qBACF,CAGA,sBAGE,kBAAmB,CAEnB,6BAA8B,CAD9B,iBAAkB,CAHlB,kBAAmB,CACnB,YAIF,CAEA,qBACE,oBAAsB,CACtB,0BAA6B,CAG7B,eAAgB,CAFhB,yBAA2B,CAC3B,kBAEF,CAGA,iBAEE,eAAiB,CAGjB,wBAAyB,CAFzB,iBAAkB,CAFlB,iBAKF,CAEA,wBAOE,mFAMC,CAPD,QAAS,CALT,UAAW,CAGX,MAAO,CAUP,mBAAoB,CAZpB,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CAYN,SACF,CAEA,qBACE,iBAAkB,CAClB,SACF,CAGA,cAGE,4BAA6B,CAF7B,eAAgB,CAChB,gBAAiB,CAEjB,gBACF,CAEA,aACE,UAAW,CACX,eAAiB,CACjB,iBACF,CAGA,kBAME,eAAgB,CAChB,mBAAoB,CAJpB,OAAQ,CADR,KAMF,CAEA,qCALE,WAAY,CAJZ,iBAAkB,CAGlB,UAcF,CARA,mBAME,sDAAgF,CAChF,iBAAkB,CAJlB,WAAY,CADZ,SAMF,CAGA,yBACE,2BAEE,eAAgB,CADhB,wBAEF,CAEA,2BACE,2BACF,CAEA,aACE,0BACF,CAEA,YAGE,cAAe,CADf,WAAY,CADZ,UAGF,CAEA,iBACE,YACF,CAEA,qBACE,yBACF,CAEA,sBAEE,kBAAmB,CADnB,YAEF,CACF,CAEA,yBACE,2BACE,wBACF,CAEA,2BACE,2BACF,CAEA,aACE,wBACF,CAEA,iBACE,YACF,CAEA,mBAEE,sBAAuB,CADvB,qBAAsB,CAEtB,OACF,CAEA,kBACE,yBACF,CACF,CAGA,aACE,YACE,+BAAiC,CACjC,yBAA2B,CAC3B,kBAAmB,CAAnB,uBACF,CAEA,2BACE,4BACF,CAEA,4BACE,YACF,CAEA,iBACE,sBACF,CAEA,mBACE,YACF,CACF,CAGA,kCAEE,iCAA0B,CAA1B,yBAA0B,CAD1B,oBAEF,CAGA,6BAKE,oBAA8B,CAG9B,iBAAkB,CALlB,UAAW,CAGX,UAAY,CALZ,oBAAqB,CAQrB,eAAiB,CACjB,UAAY,CAHZ,eAAgB,CAIhB,mBAAoB,CATpB,iBAAkB,CAElB,SAQF,CC7RA,wBACE,iBACF,CAEA,mBACE,4BAA8B,CAC9B,kCAAoC,CACpC,4BAA8B,CAC9B,yCACF,CAEA,kCACE,4DAAwE,CACxE,yCACF,CAEA,qBACE,UACF,CAEA,aAGE,kDAA6D,CAO7D,8BACF,CAEA,oBACE,uBAAyB,CAEzB,0BAA4B,CAC5B,yBAA2B,CAF3B,kBAGF,CAGA,eACE,QACF,CAEA,8BACE,2CAA0D,CAE1D,cAAe,CADf,wBAA0B,CAG1B,iBAAkB,CADlB,uBAEF,CAEA,oCACE,oBAAmC,CACnC,iBAAkB,CAClB,2BAA6B,CAC7B,4BACF,CAEA,yCACE,4BACF,CAGA,cAEE,iBAAkB,CADlB,iBAEF,CAEA,iBACE,UACF,CAEA,gBAGE,sBAAuB,CAFvB,YAAa,CAIb,cAAe,CACf,OAAQ,CAJR,6BAA8B,CAE9B,kBAGF,CAEA,mBACE,cAAe,CACf,gBACF,CAEA,kBAEE,WAAY,CACZ,UAAY,CAFZ,eAGF,CAEA,oBAEE,WAAY,CACZ,UAAY,CAFZ,eAGF,CAEA,sBACE,oBAA8B,CAE9B,0BAAqC,CADrC,UAAW,CAEX,eACF,CAEA,cAEE,eAAgB,CADhB,aAEF,CAEA,8BACE,oBAAsB,CACtB,gBAAkB,CAClB,QACF,CAGA,oBACE,eACF,CAEA,kBACE,gBAAkB,CAClB,cACF,CAEA,qBACE,QAAO,CACP,eACF,CAEA,kBACE,gBAAkB,CAClB,eAAgB,CAChB,cAAe,CACf,gBACF,CAGA,oBAME,iBAAkB,CAFlB,QAAS,CAFT,MAAO,CAKP,UAAY,CANZ,iBAAkB,CAElB,KAAM,CAEN,SAGF,CAGA,mCACE,kBACF,CAEA,qCACE,kBACF,CAEA,yCACE,kBACF,CAEA,kCACE,kBACF,CAEA,yCACE,kBACF,CAGA,kBAGE,8BAA4C,CAF5C,eAAgB,CAChB,gBAAiB,CAEjB,iBACF,CAGA,yBACE,gBAEE,sBAAuB,CADvB,qBAEF,CAEA,oBACE,eACF,CAEA,qBACE,eACF,CACF,CAEA,yBACE,8BACE,wBACF,CAEA,8BACE,eACF,CAEA,mBACE,cACF,CACF,CChNA,+BACE,iBACF,CAEA,0BACE,4BAA8B,CAC9B,kCAAoC,CACpC,4BAA8B,CAC9B,yCACF,CAEA,yCACE,4DAAwE,CACxE,yCACF,CAEA,4BACE,UACF,CAEA,aAME,kBAAmB,CAHnB,kDAA6D,CAC7D,iBAAkB,CAMlB,8BAA4C,CAF5C,UAAY,CAHZ,YAAa,CAIb,cAAe,CAPf,WAAY,CAKZ,sBAAuB,CANvB,UAUF,CAEA,2BACE,uBAAyB,CAEzB,0BAA4B,CAC5B,yBAA2B,CAF3B,kBAGF,CAGA,sBACE,QACF,CAEA,qCACE,2CAA4D,CAE5D,cAAe,CADf,wBAA0B,CAG1B,iBAAkB,CADlB,uBAEF,CAEA,2CACE,oBAAmC,CACnC,iBAAkB,CAClB,2BAA6B,CAC7B,4BACF,CAEA,gDACE,4BACF,CAGA,qBAEE,iBAAkB,CADlB,iBAEF,CAEA,wBACE,UACF,CAEA,uBAME,OAAQ,CAJR,6BAA8B,CAE9B,kBAGF,CAEA,oCANE,kBAAmB,CAFnB,YAAa,CAIb,cASF,CALA,aAIE,QACF,CAEA,uBAOE,kBAAmB,CAJnB,kDAA6D,CAE7D,iBAAkB,CAMlB,8BAA4C,CAP5C,UAAY,CAEZ,YAAa,CAIb,eAAiB,CADjB,eAAgB,CAPhB,WAAY,CAMZ,sBAAuB,CAPvB,UAWF,CAEA,UAIE,kBAAmB,CAFnB,WAAY,CACZ,UAAY,CAFZ,eAIF,CAEA,cAEE,WAAY,CACZ,UAAY,CAFZ,eAGF,CAEA,eACE,UAAW,CACX,gBACF,CAEA,qBAEE,eAAgB,CADhB,kBAEF,CAEA,qCACE,oBAAsB,CACtB,gBAAkB,CAClB,QACF,CAEA,wBACE,YAAa,CACb,wBACF,CAEA,eACE,uBAAyB,CACzB,eAAgB,CAEhB,WAAY,CADZ,SAEF,CAEA,qBACE,uBACF,CAGA,oBAME,iBAAkB,CAFlB,QAAS,CAFT,MAAO,CAKP,UAAY,CANZ,iBAAkB,CAElB,KAAM,CAEN,SAGF,CAGA,mCACE,kBACF,CAEA,qCACE,kBACF,CAEA,kCACE,kBACF,CAEA,sCACE,kBACF,CAGA,yBAGE,8BAA8C,CAF9C,eAAgB,CAChB,gBAAiB,CAEjB,iBACF,CAGA,yBACE,uBAEE,sBAAuB,CADvB,qBAEF,CAEA,aACE,UACF,CAEA,eACE,cACF,CACF,CAEA,yBACE,qCACE,wBACF,CAEA,qCACE,eACF,CAEA,uBAGE,eAAiB,CADjB,WAAY,CADZ,UAGF,CAEA,aACE,OACF,CACF,CC3NA,0BAEE,eAAmB,CACnB,iBAAkB,CAElB,8BAAyC,CADzC,eAAgB,CAHhB,UAKF,CAEA,sBACE,iBAAkB,CAClB,iBACF,CAGA,gBAKE,kBAAmB,CACnB,+BAAgC,CAChC,QAAS,CALT,wBAAyB,CAEzB,iBAIF,CAEA,+BAPE,kBAAmB,CAFnB,YAqBF,CAZA,eAKE,kBAAmB,CAEnB,WAAY,CACZ,iBAAkB,CAFlB,UAAY,CAGZ,cAAe,CACf,cAAe,CAPf,OAAQ,CACR,gBAAiB,CAOjB,uBACF,CAEA,qBACE,kBAAmB,CAEnB,+BAA8C,CAD9C,0BAEF,CAGA,gBACE,YACF,CAGA,kBACE,kBAAmB,CACnB,uBACF,CAEA,6BACE,eACF,CAEA,wBACE,0BACF,CAGA,yBACE,kBACF,CAEA,2BACE,kDAA6D,CAC7D,kBAAmB,CAEnB,UAAY,CACZ,kBAAmB,CAFnB,YAGF,CAEA,oCACE,kBAAmB,CACnB,6BAA8B,CAC9B,iBAAkB,CAClB,YACF,CAEA,wBACE,eAAiB,CACjB,wBAAyB,CACzB,kBAAmB,CAEnB,+BAA0C,CAD1C,YAEF,CAEA,+BACE,kBAAmB,CACnB,wBAAyB,CACzB,iBAAkB,CAClB,YACF,CAEA,sCACE,kBAAmB,CACnB,wBAAyB,CACzB,iBAAkB,CAClB,YACF,CAEA,6BACE,eAAiB,CACjB,wBAAyB,CACzB,iBAAkB,CAClB,eACF,CAEA,+BACE,eAAiB,CACjB,cACF,CAGA,0BACE,aACF,CAGA,iBAGE,kBAAmB,CACnB,4BAA6B,CAC7B,cAAe,CAJf,eAAgB,CAChB,YAIF,CAEA,yBACE,cACF,CAEA,yBAEE,UAAW,CADX,eAAgB,CAEhB,kBACF,CAEA,qBACE,eAAiB,CAGjB,qBAAsB,CADtB,iBAAkB,CAGlB,cAAe,CACf,eAAgB,CAFhB,eAAgB,CAHhB,YAMF,CAGA,yBACE,gBACE,YACF,CAEA,kBACE,kBACF,CAEA,gBACE,iBACF,CAEA,eAEE,cAAe,CADf,gBAEF,CACF,CAGA,aAKE,iCACE,YACF,CAEA,kBACE,kBAAmB,CACnB,kBAAmB,CADnB,uBAEF,CAEA,0BAEE,WAAY,CADZ,eAEF,CACF,CC9LA,oBACE,kDAA6D,CAC7D,gBACF,CAGA,oCAEE,8BAAgC,CAChC,wCACF,CAGA,oBACE,0CACF,CAEA,0BAEE,+BAAgD,CADhD,0BAEF,CAGA,eAGE,WAAY,CAFZ,kBAAmB,CACnB,+BAA0C,CAE1C,eAAgB,CAChB,uBACF,CAEA,qBACE,+BAA0C,CAC1C,0BACF,CAGA,wBACE,iBAAkB,CAClB,gBACF,CAEA,2BACE,eACF,CAGA,cACE,2BACF,CAEA,iBACE,GACE,SACF,CACA,IACE,UACF,CACA,GACE,SACF,CACF,CAGA,0BACE,GACE,wBACF,CACA,IACE,wBACF,CACA,GACE,wBACF,CACF,CAGA,mBAGE,wBAAyB,CAFzB,kBAAmB,CACnB,eAEF,CAEA,kBAEE,kDAA6D,CAC7D,kBAAmB,CAFnB,2BAA4B,CAG5B,iBACF,CAEA,iBACE,UAAY,CACZ,eACF,CAEA,iBACE,UACF,CAEA,uBACE,WACF,CAGA,2BAEE,aAAc,CADd,eAEF,CAGA,SACE,iBAAkB,CAClB,eAAgB,CAChB,eACF,CAGA,WAEE,gBAAiB,CADjB,kBAEF,CAEA,mBACE,kBAAmB,CACnB,oBACF,CAEA,iBACE,kBAAmB,CACnB,oBACF,CAEA,mBACE,kBAAmB,CACnB,oBACF,CAEA,gBACE,kBAAmB,CACnB,oBACF,CAGA,mBACE,wBACF,CAGA,uCACE,SACF,CAEA,6CACE,oBAA8B,CAC9B,iBACF,CAEA,6CACE,kDAA6D,CAC7D,iBACF,CAEA,mDACE,kDACF,CAGA,yBACE,oBACE,YACF,CAEA,oBAEE,cAAe,CADf,cAEF,CAEA,eACE,YACF,CACF,CAGA,qBAIE,eAAiB,CADjB,wBAAyB,CAFzB,kBAAmB,CACnB,+BAGF,CAGA,0BACE,kCAA2B,CAA3B,0BAA2B,CAC3B,oBAAoC,CACpC,kBAAmB,CACnB,WACF,CAEA,mCACE,kBAAmB,CACnB,eAAgB,CAChB,0CACF,CAEA,yCACE,0BACF,CAGA,iBACE,kBAAmB,CAEnB,+BAA0C,CAD1C,eAEF,CAGA,IACE,kDAA6D,CAE7D,wBAAyB,CADzB,iBAEF,CAGA,WACE,kBAAmB,CACnB,eACF,CAEA,uBACE,kDAA6D,CAG7D,WAAY,CAFZ,UAAY,CACZ,eAEF,CAEA,6BACE,oBACF,CAGA,WAGE,eAAiB,CADjB,wBAAyB,CADzB,kBAAmB,CAGnB,uBACF,CAEA,iBACE,oBAAqB,CACrB,+BACF,CAGA,0BAEE,+CAA6D,CAC7D,wBAAyB,CAFzB,kBAAmB,CAGnB,+BACF,CAGA,eACE,iBAAkB,CAElB,cAAe,CADf,0CAAiD,CAEjD,wBAAiB,CAAjB,gBACF,CAEA,qBAEE,+BAA0C,CAD1C,0BAEF,CAEA,wBACE,kDAA6D,CAE7D,oBAAqB,CADrB,UAEF,CAGA,oBAIE,0CAA2C,CAH3C,iDAA4D,CAC5D,iBAAkB,CAClB,UAEF,CAEA,oBACE,GACE,OACF,CACA,IACE,SACF,CACA,GACE,UACF,CACF,CAGA,kBACE,eAAiB,CACjB,wBAAyB,CACzB,kBAAmB,CAInB,8BAAwC,CADxC,cAAe,CAFf,iBAAkB,CAClB,0CAGF,CAEA,wBAEE,kBAAmB,CADnB,oBAAqB,CAGrB,+BAAgD,CADhD,0BAEF,CAEA,yBACE,uBACF,CAGA,sBACE,8BAAgC,CAChC,wCAAyD,CACzD,YACF,CAGA,sBACE,kDAA6D,CAC7D,WAAY,CACZ,kBAAmB,CAInB,+BAA+C,CAH/C,UAAY,CACZ,eAAiB,CACjB,uBAEF,CAEA,4BAEE,+BAA+C,CAD/C,0BAEF,CAEA,6BACE,uBACF,CAGA,YACE,eAAiB,CACjB,wBAAyB,CACzB,kBAAmB,CACnB,+BAA0C,CAC1C,uBACF,CAEA,kBACE,+BAA0C,CAC1C,0BACF,CAGA,4BACE,gBAAuB,CACvB,eAAgB,CAChB,eACF,CAEA,4CACE,kBAAmB,CACnB,wBAAyB,CACzB,yBACF,CAEA,4CACE,eAAiB,CAEjB,wBAAgB,CAChB,yBAA0B,CAD1B,eAEF,CAWA,gKACE,kBACF,CCnYA,KACE,4FACF,CAGA,2BAME,iCAA0B,CAA1B,yBAA0B,CAD1B,oBAAoC,CAIpC,0BAAyC,CAFzC,iBAAkB,CAGlB,+BAAyC,CAFzC,WAAY,CAPZ,iBAAkB,CAElB,UAAW,CADX,QAAS,CAET,UAOF,CAEA,uCACE,qBAAuB,CACvB,yBACF,CAEA,4DAGE,0BAAkC,CAFlC,qBAAuB,CACvB,yBAEF,CAGA,iBAEE,kBAAmB,CAGnB,eAAmB,CAJnB,YAAa,CAKb,uEAA8E,CAH9E,sBAAuB,CACvB,gBAAiB,CAIjB,eAAgB,CADhB,iBAEF,CAGA,wBAOE,oBAAoC,CANpC,UAAW,CAOX,SACF,CAGA,0CANE,QAAS,CAFT,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAiBF,CARA,kBAOE,mBAAoB,CADpB,SAEF,CAEA,cASE,8CAA+C,CAL/C,oBAAqC,CACrC,0BAA0C,CAC1C,kBAAmB,CACnB,+BAA+C,CAJ/C,YAAa,CAOb,mBAAoB,CATpB,iBAAkB,CAOlB,uBAAyB,CANzB,WASF,CAEA,oBACE,oBAAqC,CACrC,sBAAsC,CAEtC,+BAA+C,CAD/C,0BAEF,CAEA,WAEE,aAAc,CAEd,aAAc,CAHd,cAAe,CAEf,kBAAmB,CAEnB,6BACF,CAEA,+BACE,oBACF,CAEA,iBACE,aAAc,CACd,cAAe,CACf,eAAgB,CAEhB,eAAgB,CADhB,kBAAmB,CAEnB,yBACF,CAEA,uBACE,aACF,CAEA,gBACE,aAAc,CACd,cAAe,CACf,eAAgB,CAChB,QAAS,CACT,yBACF,CAEA,sBACE,aACF,CAGA,QAGE,kBAAmB,CADnB,OAAQ,CADR,MAGF,CAEA,QAGE,kBAAmB,CADnB,QAAS,CADT,MAGF,CAEA,QAGE,kBAAmB,CADnB,OAAQ,CADR,OAGF,CAEA,QAGE,kBAAmB,CADnB,QAAS,CADT,OAGF,CAEA,QAGE,kBAAmB,CAFnB,UAAW,CACX,OAEF,CAEA,QAGE,mBAAoB,CAFpB,UAAW,CACX,QAEF,CAEA,uBACE,MACE,qCACF,CACA,IACE,0CACF,CACA,IACE,2CACF,CACF,CAGA,oBAOE,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAoC,CAEpC,0BAA0C,CAC1C,kBAAmB,CACnB,sDAEiC,CARjC,eAAgB,CAChB,YAAa,CAJb,iBAAkB,CAYlB,iBAAkB,CAClB,uBAAyB,CAXzB,UAAW,CADX,SAaF,CAEA,2BAOE,oEAG+B,CAC/B,kBAAmB,CALnB,QAAS,CALT,UAAW,CAGX,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CASN,UACF,CAGA,cACE,kBACF,CAEA,YAKE,aAAc,CACd,mDAAwD,CAJxD,YAAa,CAEb,kBAAmB,CADnB,kBAAmB,CAInB,uBAAyB,CANzB,WAOF,CAEA,kBAEE,oDAAyD,CADzD,sCAEF,CAEA,aAQE,6BAAoC,CAFpC,kDAA6D,CAC7D,4BAA6B,CAE7B,oBAAqB,CARrB,uBAAyB,CACzB,wBAA0B,CAC1B,yBAA2B,CAE3B,qBAAuB,CADvB,4BAMF,CAEA,gBACE,uBAAyB,CACzB,wBAA0B,CAC1B,yBAA2B,CAC3B,eACF,CAGA,YACE,kBACF,CAEA,2BACE,kBACF,CAEA,aAOE,2CAAqC,CAArC,mCAAqC,CADrC,0BAA+C,CAH/C,oCAAoD,CADpD,4BAA8B,CAE9B,wBAA0B,CAH1B,qBAAuB,CAIvB,iCAGF,CAEA,mBAEE,8BAA+C,CAD/C,gCAEF,CAEA,kDAIE,8BAAgD,CAFhD,8BAAgC,CAChC,wCAEF,CAEA,wBAIE,0BAAkC,CAHlC,qBAAuB,CACvB,yBAA2B,CAC3B,2BAEF,CAEA,sBACE,aAAc,CAEd,cAAe,CADf,gBAEF,CAEA,4BACE,aACF,CAEA,cAGE,4DAAwE,CACxE,qBAAuB,CAFvB,4BAA8B,CAK9B,0CAA0D,CAF1D,wBAA0B,CAC1B,yBAA2B,CAL3B,qBAAuB,CASvB,eAAgB,CADhB,iBAAkB,CADlB,iCAGF,CAEA,qBAOE,mDAAsF,CANtF,UAAW,CAKX,WAAY,CAFZ,UAAW,CAFX,iBAAkB,CAClB,KAAM,CAKN,wBAA0B,CAH1B,UAIF,CAEA,2BACE,SACF,CAEA,oBAEE,0CAA0D,CAD1D,oCAEF,CAEA,qBACE,iCACF,CAGA,cAEE,8BAA8C,CAD9C,gBAEF,CAEA,cACE,uBAAyB,CACzB,wBAA0B,CAC1B,yBACF,CAGA,0BACE,cAEE,YAAa,CADb,WAEF,CAEA,WACE,cACF,CAEA,iBACE,cACF,CAEA,gBACE,cACF,CAGA,QAAmB,OAAQ,CAAjB,MAAmB,CAC7B,QAAmB,QAAS,CAAlB,MAAoB,CAC9B,QAAoB,QAAU,CAApB,OAAsB,CAChC,QAAoB,SAAW,CAArB,OAAuB,CACjC,QAAU,UAAW,CAAE,OAAU,CACjC,QAAU,SAAU,CAAE,QAAW,CACnC,CAEA,0BACE,cAEE,YAAa,CADb,WAEF,CAEA,WACE,cAAe,CACf,kBACF,CAEA,iBACE,cAAe,CACf,kBACF,CAEA,gBACE,cACF,CAEA,oBACE,eAAgB,CAChB,YACF,CAEA,aACE,wBACF,CAGA,gBACE,YACF,CACF,CAEA,yBACE,iBACE,YACF,CAEA,cAEE,YAAa,CADb,WAEF,CAEA,WACE,cAAe,CACf,kBACF,CAEA,iBACE,cAAe,CACf,iBACF,CAEA,gBACE,cAAe,CACf,eACF,CAEA,oBACE,cAAe,CACf,iBACF,CAEA,YAEE,YAAa,CADb,WAEF,CAEA,aACE,wBACF,CAEA,gBACE,wBACF,CAGA,gBACE,YACF,CAGA,QAAmB,QAAU,CAAnB,MAAqB,CAC/B,QAAmB,SAAW,CAApB,MAAsB,CAChC,QAAU,SAAU,CAAE,QAAY,CAClC,QAAU,SAAU,CAAE,SAAa,CACrC,CAEA,yBACE,cAEE,YAAa,CADb,WAEF,CAEA,WACE,cACF,CAEA,iBACE,cACF,CAEA,gBACE,cACF,CAGA,QAAmB,MAAQ,CAAjB,MAAmB,CAC7B,QAAmB,OAAS,CAAlB,MAAoB,CAC9B,QAAU,SAAU,CAAE,MAAU,CAChC,QAAU,SAAU,CAAE,OAAW,CACnC,CAEA,yBACE,cACE,YACF,CAEA,oBAEE,WAAY,CADZ,iBAEF,CAEA,wBACE,oBACF,CACF,CCpfA,YAEE,wBAAyB,CACzB,gBAAiB,CAFjB,YAGF,CAEA,wBAKE,+CAA6D,CAJ7D,kBAKF,CAEA,8CAJE,WAAY,CAFZ,iBAAkB,CAClB,8BASF,CAEA,mBAEE,+BAAgC,CAChC,kBAAmB,CAFnB,mBAGF,CAEA,kBAEE,aAAc,CAEd,cAAe,CADf,eAAgB,CAFhB,QAIF,CAEA,+BACE,iBAAkB,CAClB,eACF,CAEA,2CACE,kBAAmB,CACnB,+BAAgC,CAEhC,aAAc,CADd,eAEF,CC5CA,EACE,QAAS,CACT,SAEF,CAEA,iBAHE,qBAMF,CAEA,KAIE,YACF,CACA,UALE,wBAAyB,CAEzB,0BAA2B,CAD3B,0BAuBF,CAnBA,KAKE,kCAAmC,CACnC,iCAAkC,CASlC,gCAAiC,CAbjC,mIAEY,CAHZ,QAAS,CAMT,gBAAiB,CAWjB,wBACF,CAEA,KACE,uEAEF,CAEA,eACE,gBACF,CAGA,aACE,yBAA8B,CAC9B,0CAA2D,CAC3D,wCAAyD,CACzD,YAAa,CACb,qBAAsB,CACtB,YAAa,CACb,eAAgB,CAChB,iBACF,CAEA,gBAGE,kBAAmB,CAGnB,sDAAgG,CADhG,iCAAgD,CAHhD,YAAa,CAKb,aAAc,CAHd,6BAA8B,CAH9B,iBAOF,CAEA,qBAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAEA,qBAEE,WAAY,CACZ,kBAAmB,CAFnB,UAGF,CAEA,uBAEE,kBAAmB,CADnB,YAAa,CAEb,OACF,CAEA,gBAQE,6BAAoC,CAGpC,6CAA8C,CAN9C,iEAAsE,CAEtE,4BAA6B,CAE7B,oBAAqB,CAHrB,yBAA0B,CAM1B,cAAe,CAEf,sEAA4E,CAb5E,cAAe,CACf,eAAgB,CAChB,gBAAmB,CAOnB,aAAgB,CAGhB,iBAAkB,CATlB,wBAWF,CAEA,uBAWE,8CAA+C,CAJ/C,yFAAiG,CACjG,yBAA0B,CAC1B,iBAAkB,CAHlB,QAAS,CALT,UAAW,CAGX,MAAO,CAMP,UAAY,CARZ,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CASN,UACF,CAEA,wBACE,MACE,yBACF,CACA,IACE,4BACF,CACF,CAEA,cAOE,6BAAoC,CAIpC,4CAA6C,CAP7C,mFAAoG,CAEpG,4BAA6B,CAE7B,oBAAqB,CAHrB,yBAA0B,CAO1B,sEAA4E,CAX5E,cAAe,CACf,eAAgB,CAChB,kBAAmB,CAMnB,eAAgB,CAChB,iBAAkB,CAGlB,iDACF,CAEA,qBACE,MACE,yBAA2B,CAC3B,oBACF,CACA,IACE,yBAA2B,CAC3B,sBACF,CACA,IACE,4BAA6B,CAC7B,sBACF,CACA,IACE,4BAA6B,CAC7B,sBACF,CACF,CAEA,sBACE,MACE,yBAA2B,CAC3B,iDACF,CACA,IACE,2BAA4B,CAC5B,6CACF,CACA,IACE,4BAA6B,CAC7B,iDACF,CACA,IACE,2BAA4B,CAC5B,iDACF,CACF,CAEA,sBAQE,4BAA8B,CAN9B,oCAAoD,CACpD,2BAA6B,CAF7B,uBAAyB,CAMzB,sBAAwB,CAFxB,qBAAuB,CAIvB,gCAAkC,CAHlC,mBAAqB,CAIrB,iCAAoC,CANpC,oBAOF,CAEA,4BAGE,8BAA+C,CAD/C,gCAAgD,CADhD,uBAGF,CAEA,yBAEE,iCAAgD,CAChD,aAAc,CAFd,iBAGF,CAEA,uBACE,aAAc,CACd,cAAe,CACf,eAAgB,CAEhB,mBAAqB,CACrB,iBAAkB,CAFlB,wBAGF,CAEA,+BAEE,YAAa,CADb,QAAO,CAEP,qBAAsB,CACtB,YAAa,CACb,eACF,CAGA,iCACE,YAAa,CACb,qBAAsB,CACtB,0BACF,CAGA,8BAKE,kBAAmB,CAHnB,iCAAgD,CAEhD,YAAa,CADb,aAAc,CAGd,6BAA8B,CAL9B,iBAMF,CAEA,sBAaE,4BAA8B,CAZ9B,4DAAwE,CACxE,qBAAuB,CACvB,2BAA6B,CAM7B,wCAAwD,CAExD,oBAAuB,CACvB,sBAAwB,CAJxB,wBAA0B,CAJ1B,yBAA2B,CAE3B,qBAAuB,CAQvB,gCAAkC,CAPlC,mBAAqB,CAGrB,iCAAoC,CALpC,oBAUF,CAEA,4BACE,4DAAwE,CAExE,wCAAwD,CACxD,oBAAuB,CAFvB,oCAGF,CAEA,4BACE,QAAO,CAKP,8BAA+B,CAD/B,YAAa,CAFb,iBAAkB,CADlB,eAAgB,CAEhB,sBAGF,CAGA,+CACE,SACF,CAEA,qDACE,oBAAoC,CACpC,iBACF,CAEA,qDACE,oBAAmC,CACnC,iBACF,CAEA,2DACE,oBACF,CAEA,mBAEE,kBAAmB,CAGnB,oBAAoC,CACpC,0BAAyC,CAFzC,iBAAkB,CAHlB,YAAa,CAEb,iBAAkB,CAMlB,eAAgB,CADhB,iBAAkB,CADlB,uBAGF,CAEA,yBACE,oBAAoC,CACpC,sBAAqC,CACrC,0BACF,CAEA,0BACE,kDAA6D,CAC7D,oBAAqB,CACrB,UACF,CAEA,gCACE,kDACF,CAEA,sBAGE,cAAe,CAFf,QAAO,CAGP,WAAY,CAFZ,iBAGF,CAEA,oBACE,cAAe,CACf,eAAgB,CAChB,eAAgB,CAChB,eAAgB,CAChB,sBAAuB,CACvB,kBACF,CAEA,sBAEE,SAAU,CADV,WAAY,CAEZ,2BACF,CAEA,+CACE,SACF,CAEA,yBAQE,4BAA8B,CAL9B,0BAAkC,CADlC,qBAAuB,CAQvB,2BAA6B,CAT7B,uBAAyB,CAMzB,sBAAwB,CAFxB,qBAAuB,CAIvB,gCAAkC,CAHlC,mBAAqB,CAKrB,iCAAoC,CAPpC,oBAQF,CAEA,+BACE,8BAA6C,CAC7C,uBACF,CAEA,mDACE,qBACF,CAEA,yDAEE,0BAA+C,CAD/C,oBAEF,CAGA,qBAEE,8BAA6C,CAC7C,cAAe,CAFf,cAGF,CAEA,eAOE,0BAAkC,CADlC,oCAAoD,CAFpD,2BAA6B,CAH7B,uBAAyB,CACzB,wBAA0B,CAC1B,qBAAuB,CAEvB,iCAGF,CAEA,qBAEE,8BAA+C,CAC/C,gCAAgD,CAFhD,uBAGF,CAEA,uBAOE,kBAAmB,CANnB,aAAc,CAKd,YAAa,CAJb,cAAe,CACf,eAAgB,CAChB,mBAAqB,CACrB,eAGF,CAEA,6CAGE,0BAA+C,CAF/C,oCAAoD,CACpD,2BAA6B,CAE7B,iCACF,CAEA,mDAEE,8BAAgD,CADhD,gCAEF,CAEA,gEACE,8BAAgC,CAChC,wCACF,CAEA,gBAQE,kCAA2B,CAA3B,0BAA2B,CAD3B,sDAAyF,CADzF,8BAA8C,CAJ9C,QAAS,CAQT,gCAAgD,CAChD,YAAa,CACb,qBAAsB,CACtB,QAAS,CAVT,MAAO,CAEP,sBAA4B,CAJ5B,iBAAkB,CAGlB,OAAQ,CAKR,UAKF,CAEA,wBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,aACF,CAEA,kBAKE,4BAA8B,CAI9B,gCAAwC,CANxC,4BAA8B,CAC9B,sBAAwB,CAIxB,wBAA0B,CAN1B,qBAAuB,CAIvB,gCAAkC,CAKlC,yBAA2B,CAD3B,2BAA6B,CAH7B,oDAA4D,CAN5D,oBAWF,CAEA,yBASE,sDAAuF,CAFvF,kBAAmB,CADnB,QAAS,CALT,UAAW,CAGX,MAAO,CAOP,8BAA+B,CAC/B,4EAAsE,CAAtE,oEAAsE,CACtE,0BAAuB,CAAvB,sBAAuB,CACvB,SAAU,CANV,WAAY,CANZ,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CAYN,2BACF,CAGA,wBAEE,gEAAoG,CAIpG,oCAAsD,CAHtD,gEAEoD,CAJpD,uBAAyB,CAMzB,oDACF,CAEA,8BACE,gEAAoG,CAMpG,gCAAkD,CAHlD,iEAEoD,CAJpD,uBAAyB,CACzB,gDAKF,CAEA,qCACE,SACF,CAGA,+BAEE,gEAAiG,CAKjG,oCAAoD,CAJpD,2FAGoD,CALpD,uBAAyB,CAOzB,oCACF,CAEA,qCACE,gEAAiG,CAOjG,gCAAgD,CAJhD,uFAGoD,CALpD,uBAAyB,CACzB,gDAMF,CAEA,oBAEE,gEAAoG,CAIpG,oCAAqD,CAHrD,4DAEmD,CAJnD,uBAMF,CAEA,0BACE,gEAAoG,CAMpG,gCAAiD,CAHjD,iEAEmD,CAJnD,uBAAyB,CACzB,gDAKF,CAEA,iCACE,SACF,CAGA,2BAEE,eAAgB,CADhB,aAEF,CAEA,sBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,SACF,CAEA,kBAEE,aAAc,CADd,cAAe,CAEf,eAAgB,CAGhB,WAAY,CAGZ,mBAAqB,CAFrB,gBAAiB,CAHjB,iBAAkB,CAIlB,uBAAyB,CAHzB,UAKF,CAEA,yBACE,aAAc,CACd,eACF,CAEA,+BACE,aACF,CAQA,8BAHE,eASF,CANA,aAKE,qBAAsB,CAHtB,yBAA0B,CAC1B,aAAc,CACd,YAEF,CAGA,wDAGE,qBAAsB,CACtB,OAAQ,CAFR,sBAAuB,CADvB,iBAIF,CAEA,6DACE,qBAAsB,CACtB,OACF,CAEA,uMAGE,YACF,CAEA,wDAEE,MAAO,CADP,qBAA0B,CAE1B,OAAQ,CACR,UACF,CAEA,gEACE,qBAAsB,CAEtB,QAAS,CADT,sBAAuB,CAEvB,aACF,CAEA,mEACE,YACF,CAEA,0DAIE,2BAA6B,CAD7B,wBAA0B,CAD1B,qBAAuB,CADvB,oBAIF,CAEA,iEACE,iBACF,CAcA,wDACE,kBACF,CAEA,aACE,wBAAyB,CACzB,iBAAkB,CAClB,gBACF,CAEA,iBACE,eAAgB,CAChB,oBACF,CAEA,mBAGE,kBAAmB,CAFnB,YAAa,CAGb,WAAY,CAFZ,sBAAuB,CAGvB,UACF,CAEA,iBACE,kBACF,CAEA,gBACE,cACF,CAEA,gBACE,cAAe,CACf,eACF,CAGA,YACE,eAAgB,CAChB,+BAAgC,CAIhC,8BAAyC,CAFzC,WAAY,CACZ,gBAAiB,CAFjB,cAIF,CAEA,oBAGE,kBAAmB,CAFnB,YAAa,CAGb,WAAY,CAFZ,6BAGF,CAEA,iBACE,QACF,CAOA,gCAHE,kBAAmB,CADnB,YAUF,CANA,cACE,UAAW,CACX,cAAe,CAGf,aACF,CAEA,YACE,UAAW,CACX,kBACF,CAEA,kBACE,aACF,CAGA,qBAEE,0BAA4B,CAD5B,eAEF,CAEA,qCAEE,4BAA8B,CAD9B,sBAAwB,CAExB,gCAAkC,CAClC,0BACF,CAEA,gCAEE,kBAAoB,CACpB,0BAA4B,CAF5B,eAGF,CAEA,wCAIE,oCAAoD,CAHpD,4BAA8B,CAE9B,0CAA2D,CAE3D,uBAAyB,CAHzB,yBAIF,CAEA,uCACE,gEAAkG,CAClG,2CAA2D,CAC3D,gCAAuC,CACvC,2BACF,CAEA,sCACE,uBAAyB,CACzB,wBAA0B,CAC1B,yBAA2B,CAE3B,kBAAoB,CADpB,2BAEF,CAEA,qCAEE,yBAA8B,CAD9B,sBAAwB,CAExB,2BACF,CAEA,6DAGE,4BAA8B,CAF9B,sBAAwB,CACxB,+BAAiC,CAEjC,kBAAoB,CACpB,uBACF,CAEA,sEAUE,4BAA8B,CAL9B,8BAA8C,CAC9C,2BAA6B,CAJ7B,uBAAyB,CAOzB,sBAAwB,CARxB,wBAA0B,CAO1B,qBAAuB,CAGvB,gCAAkC,CARlC,4BAA8B,CAC9B,sBAAwB,CAGxB,oBAKF,CAEA,gEACE,uBAAyB,CACzB,wBAA0B,CAC1B,yBAA2B,CAC3B,kBAAoB,CACpB,2BACF,CAEA,uCACE,8BAA+C,CAC/C,wCAAyD,CACzD,gCAAuC,CAKvC,2BACF,CAEA,oFALE,4BAA8B,CAF9B,sBAAwB,CAGxB,kBAAoB,CAFpB,gCAaF,CAPA,6CAME,oCAAsC,CADtC,kBAEF,CAEA,8BAQE,qBAAuB,CANvB,4BAA8B,CAO9B,uBAAyB,CALzB,wBAA0B,CAD1B,yBAA2B,CAF3B,qBAAuB,CAKvB,yBAA2B,CAD3B,wBAA0B,CAE1B,oDAGF,CAEA,sCACE,8BAAgD,CAEhD,wCAAyD,CADzD,uBAEF,CAEA,4CACE,8BAAgD,CAGhD,yCAA2D,CAF3D,uBAAyB,CACzB,oCAEF,CAEA,sCACE,4DAAgE,CAEhE,wCAAwD,CADxD,oBAEF,CAEA,4CACE,4DAAgE,CAEhE,yCAAyD,CADzD,oCAEF,CAEA,sCAEE,oBAAsB,CADtB,kBAEF,CAEA,wCAME,2BAA6B,CAF7B,uBAAyB,CACzB,wBAA0B,CAH1B,qBAAuB,CACvB,0BAA4B,CAI5B,iCAAoC,CANpC,oBAOF,CAEA,8CACE,8BAA+C,CAC/C,uBACF,CAGA,0BACE,GACE,SAAU,CACV,2BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,kBACE,GACE,SACF,CACA,GACE,SACF,CACF,CAGA,mBACE,wCACF,CAEA,yBACE,mCACF", "sources": ["components/LanguageSwitch.css", "../node_modules/react-json-view-lite/dist/index.css", "pages/analysis/AnalysisDetail.css", "components/TemplatePanel.css", "components/TemplateParameterPanel.css", "components/report/components/HeaderComponent.css", "components/report/components/KpiGrid.css", "components/report/components/ExecutiveSummary.css", "components/report/components/ChartCard.css", "components/report/components/InsightList.css", "components/report/components/RecommendationList.css", "components/report/DynamicReportRenderer.css", "pages/analysis/MultiRoundAnalysis.css", "styles/login.css", "admin/components/AdminPage.css", "index.css"], "sourcesContent": ["/* 语言切换组件样式 */\n.language-switch {\n  .ant-select-selector {\n    border: 1px solid #d9d9d9;\n    border-radius: 6px;\n    transition: all 0.3s;\n  }\n\n  .ant-select-selector:hover {\n    border-color: #40a9ff;\n  }\n\n  .ant-select-focused .ant-select-selector {\n    border-color: #40a9ff;\n    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n  }\n}\n\n/* 语言选项样式 */\n.language-option {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  \n  .language-flag {\n    font-size: 16px;\n  }\n  \n  .language-label {\n    font-weight: 500;\n  }\n}\n\n/* 顶部导航区域的语言切换器 */\n.sidebar-header-right {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  \n  .language-switch {\n    min-width: 100px;\n  }\n  \n  .user-dropdown {\n    .ant-btn {\n      border: none;\n      box-shadow: none;\n      padding: 4px 8px;\n      \n      &:hover {\n        background-color: rgba(0, 0, 0, 0.04);\n      }\n    }\n  }\n}\n\n/* 适配小屏幕 */\n@media (max-width: 768px) {\n  .sidebar-header-right {\n    .language-switch {\n      min-width: 80px;\n    }\n    \n    .user-dropdown .ant-btn span:not(.anticon) {\n      display: none;\n    }\n  }\n}", "/* base styles */\n\n._GzYRV {\n  line-height: 1.2;\n  white-space: pre-wrap;\n  white-space: -moz-pre-wrap;\n  white-space: -pre-wrap;\n  white-space: -o-pre-wrap;\n  word-wrap: break-word;\n}\n\n._3eOF8 {\n  margin-right: 5px;\n  font-weight: bold;\n}\n\n._3eOF8 + ._3eOF8 {\n  margin-left: -5px;\n}\n\n._1MFti {\n  cursor: pointer;\n}\n\n._f10Tu {\n  font-size: 1.2em;\n  margin-right: 5px;\n  -webkit-user-select: none;\n     -moz-user-select: none;\n          user-select: none;\n}\n\n._1UmXx::after {\n  content: '\\25B8';\n}\n\n._1LId0::after {\n  content: '\\25BE';\n}\n\n._1pNG9 {\n  margin-right: 5px;\n}\n\n._1pNG9::after {\n  content: '...';\n  font-size: 0.8em;\n}\n\n._2IvMF {\n  background: #eee;\n}\n\n._2bkNM {\n  margin: 0 10px;\n  padding: 0;\n}\n\n/* default light style */\n._1MGIk {\n  font-weight: 600;\n  margin-right: 5px;\n  color: #000000;\n}\n\n._2YKJg {\n}\n\n._3uHL6 {\n  color: #000000;\n}\n\n._2T6PJ {\n  color: #df113a;\n}\n\n._1Gho6 {\n  color: #df113a;\n}\n\n._vGjyY {\n  color: rgb(42, 63, 60);\n}\n\n._1bQdo {\n  color: #0b75f5;\n}\n\n._3zQKs {\n  color: rgb(70, 144, 56);\n}\n\n._1xvuR {\n  color: #43413d;\n}\n\n._oLqym {\n  color: #000000;\n}\n\n._2AXVT {\n  color: #000000;\n}\n\n._2KJWg {\n  color: #000000;\n}\n\n/* default dark style */\n._11RoI {\n  background: rgb(0, 43, 54);\n}\n\n._17H2C {\n  color: rgb(253, 246, 227);\n}\n\n._3QHg2 {\n  color: rgb(253, 246, 227);\n}\n\n._3fDAz {\n  color: rgb(253, 246, 227);\n}\n\n._2bSDX {\n  font-weight: bolder;\n  margin-right: 5px;\n  color: rgb(253, 246, 227);\n}\n\n._1RQEj {\n}\n\n._gsbQL {\n  color: rgb(253, 246, 227);\n}\n\n._LaAZe {\n  color: rgb(129, 181, 172);\n}\n\n._GTKgm {\n  color: rgb(129, 181, 172);\n}\n\n._Chy1W {\n  color: rgb(203, 75, 22);\n}\n\n._2bveF {\n  color: rgb(211, 54, 130);\n}\n\n._2vRm- {\n  color: rgb(174, 129, 255);\n}\n\n._1prJR {\n  color: rgb(38, 139, 210);\n}\n", "/* Markdown 表格样式 */\n.markdown-table {\n  border-collapse: collapse;\n  width: 100%;\n  margin: 16px 0;\n  font-size: 14px;\n  line-height: 1.5;\n  border: 1px solid #e8e8e8;\n}\n\n.markdown-thead {\n  background-color: #fafafa;\n}\n\n.markdown-th {\n  padding: 12px;\n  text-align: left;\n  font-weight: 500;\n  color: rgba(0, 0, 0, 0.85);\n  border-bottom: 1px solid #e8e8e8;\n  white-space: nowrap;\n}\n\n.markdown-tr {\n  border-bottom: 1px solid #e8e8e8;\n}\n\n.markdown-tr:hover {\n  background-color: #fafafa;\n}\n\n.markdown-td {\n  padding: 12px;\n  text-align: left;\n  border-bottom: 1px solid #e8e8e8;\n}\n\n/* 其他 Markdown 样式 */\n.report-card {\n  font-size: 14px;\n  line-height: 1.8;\n}\n\n.report-card h1,\n.report-card h2,\n.report-card h3,\n.report-card h4,\n.report-card h5,\n.report-card h6 {\n  margin-top: 24px;\n  margin-bottom: 16px;\n}\n\n.report-card p {\n  margin-bottom: 16px;\n}\n\n.report-card ul,\n.report-card ol {\n  margin-bottom: 16px;\n  padding-left: 24px;\n}\n\n.report-card code {\n  background-color: #f5f5f5;\n  padding: 2px 4px;\n  border-radius: 3px;\n  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;\n}\n\n.report-card pre {\n  background-color: #f5f5f5;\n  padding: 16px;\n  border-radius: 6px;\n  overflow: auto;\n  margin-bottom: 16px;\n}\n\n.report-card blockquote {\n  margin: 16px 0;\n  padding: 0 16px;\n  color: #666;\n  border-left: 4px solid #ddd;\n}\n\n/* 响应式表格 */\n@media screen and (max-width: 768px) {\n  .markdown-table {\n    display: block;\n    overflow-x: auto;\n    -webkit-overflow-scrolling: touch;\n  }\n  \n  .markdown-td,\n  .markdown-th {\n    white-space: nowrap;\n  }\n}\n\n/* 悬浮取消按钮动画 */\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes pulse {\n  0% {\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  }\n  50% {\n    box-shadow: 0 4px 20px rgba(255, 77, 79, 0.3);\n  }\n  100% {\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  }\n}\n\n/* 悬浮按钮容器样式 */\n.floating-cancel-button {\n  position: fixed;\n  bottom: 100px;\n  left: 50%;\n  z-index: 1000;\n  animation: fadeInUp 0.3s ease-out, pulse 2s ease-in-out infinite;\n}\n\n/* 响应式调整 */\n@media screen and (max-width: 768px) {\n  .floating-cancel-button {\n    bottom: 15px;\n    left: 50%;\n    transform: translateX(-50%);\n  }\n}\n\n/* 澄清过程相关动画 */\n@keyframes pulse {\n  0% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.5;\n  }\n  100% {\n    opacity: 1;\n  }\n}\n\n@keyframes slideInFromLeft {\n  from {\n    opacity: 0;\n    transform: translateX(-20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n@keyframes fadeInScale {\n  from {\n    opacity: 0;\n    transform: scale(0.95);\n  }\n  to {\n    opacity: 1;\n    transform: scale(1);\n  }\n}\n\n/* 澄清卡片动画 */\n.clarification-card {\n  animation: fadeInScale 0.3s ease-out;\n}\n\n.clarification-question {\n  animation: slideInFromLeft 0.4s ease-out;\n}\n\n.clarification-answer {\n  animation: slideInFromLeft 0.5s ease-out;\n}\n\n/* 智能交互状态指示器 */\n.smart-interaction-indicator {\n  animation: pulse 1.5s infinite;\n}\n\n/* 澄清过程渐变背景 */\n.clarification-gradient {\n  background: linear-gradient(135deg, #fff7e6 0%, #fff2d9 100%);\n  transition: all 0.3s ease;\n}\n\n.clarification-gradient:hover {\n  background: linear-gradient(135deg, #fff2d9 0%, #ffe7ba 100%);\n}\n\n.user-response-gradient {\n  background: linear-gradient(135deg, #f6ffed 0%, #e6f7ff 100%);\n  transition: all 0.3s ease;\n}\n\n.user-response-gradient:hover {\n  background: linear-gradient(135deg, #e6f7ff 0%, #d9f7be 100%);\n}", "/* 模板面板样式 */\n.template-panel {\n  background: white;\n  border-radius: 16px;\n  max-height: 600px;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n}\n\n.template-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px 20px;\n  border-bottom: 1px solid #e1e5e9;\n  background: #f8f9fa;\n}\n\n.template-header h4 {\n  margin: 0;\n  font-size: 16px;\n  font-weight: 600;\n  color: #24292f;\n}\n\n.close-btn {\n  background: none;\n  border: none;\n  font-size: 20px;\n  cursor: pointer;\n  color: #656d76;\n  padding: 0;\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 4px;\n  transition: all 0.2s;\n}\n\n.close-btn:hover {\n  background: #f6f8fa;\n  color: #24292f;\n}\n\n.template-filters {\n  padding: 20px 24px;\n  border-bottom: 1px solid #e2e8f0;\n  background: #f8fafc;\n}\n\n.search-input {\n  width: 100%;\n  padding: 12px 16px;\n  border: 2px solid #e2e8f0;\n  border-radius: 12px;\n  margin-bottom: 16px;\n  font-size: 15px;\n  transition: all 0.3s ease;\n  background: white;\n}\n\n.search-input:focus {\n  outline: none;\n  border-color: #667eea;\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n}\n\n.filter-row {\n  display: flex;\n  gap: 16px;\n  align-items: center;\n  flex-wrap: wrap;\n}\n\n.filter-group {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.filter-group label {\n  font-size: 14px;\n  font-weight: 600;\n  color: #374151;\n  white-space: nowrap;\n}\n\n.filter-select {\n  padding: 8px 12px;\n  border: 2px solid #e2e8f0;\n  border-radius: 8px;\n  font-size: 14px;\n  background: white;\n  color: #374151;\n  transition: all 0.3s ease;\n  min-width: 120px;\n}\n\n.filter-select:focus {\n  outline: none;\n  border-color: #667eea;\n  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);\n}\n\n.category-tabs {\n  display: flex;\n  gap: 8px;\n  flex-wrap: wrap;\n}\n\n.category-tab {\n  padding: 6px 12px;\n  border: 1px solid #d0d7de;\n  border-radius: 16px;\n  background: white;\n  cursor: pointer;\n  font-size: 14px;\n  transition: all 0.2s;\n  white-space: nowrap;\n}\n\n.category-tab:hover {\n  background: #f6f8fa;\n}\n\n.category-tab.active {\n  background: #0969da;\n  color: white;\n  border-color: #0969da;\n}\n\n.template-list {\n  flex: 1;\n  overflow-y: auto;\n  padding: 20px 24px;\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 16px;\n  max-height: 500px;\n}\n\n.loading, .empty {\n  grid-column: 1 / -1;\n  text-align: center;\n  padding: 60px 20px;\n  color: #64748b;\n  font-size: 15px;\n}\n\n/* 模板卡片样式 */\n.template-card {\n  border: 1px solid #e2e8f0;\n  border-radius: 16px;\n  padding: 20px;\n  transition: all 0.3s ease;\n  background: white;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);\n  cursor: pointer;\n}\n\n.template-card:hover {\n  border-color: #667eea;\n  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);\n  transform: translateY(-2px);\n}\n\n.template-card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 8px;\n}\n\n.template-name {\n  margin: 0;\n  font-size: 16px;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  color: #24292f;\n  flex: 1;\n}\n\n.system-badge, .public-badge {\n  font-size: 12px;\n  padding: 2px 6px;\n  border-radius: 4px;\n  font-weight: normal;\n}\n\n.system-badge {\n  background: #dbeafe;\n  color: #1e40af;\n}\n\n.public-badge {\n  background: #dcfce7;\n  color: #166534;\n}\n\n.template-meta {\n  font-size: 12px;\n  color: #656d76;\n  display: flex;\n  flex-direction: column;\n  align-items: flex-end;\n  gap: 2px;\n  text-align: right;\n}\n\n.usage-count {\n  color: #666;\n  font-size: 12px;\n}\n\n.creator {\n  color: #999;\n  font-size: 12px;\n}\n\n.visibility {\n  font-size: 11px;\n  padding: 2px 6px;\n  border-radius: 3px;\n  font-weight: 500;\n}\n\n.visibility.public {\n  background: #f6ffed;\n  color: #52c41a;\n  border: 1px solid #b7eb8f;\n}\n\n.visibility.private {\n  background: #f0f0f0;\n  color: #666;\n  border: 1px solid #d9d9d9;\n}\n\n.template-description {\n  color: #6b7280;\n  font-size: 14px;\n  margin: 12px 0;\n  line-height: 1.5;\n  background: #f8fafc;\n  padding: 12px;\n  border-radius: 8px;\n  border-left: 3px solid #e2e8f0;\n}\n\n.template-actions {\n  display: flex;\n  gap: 12px;\n  justify-content: flex-end;\n  margin-top: 16px;\n}\n\n.preview-btn, .use-btn {\n  padding: 8px 16px;\n  border-radius: 10px;\n  font-size: 14px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  border: 2px solid;\n}\n\n.preview-btn {\n  border-color: #e2e8f0;\n  background: white;\n  color: #374151;\n}\n\n.preview-btn:hover {\n  background: #f8fafc;\n  border-color: #cbd5e1;\n  transform: translateY(-1px);\n}\n\n.use-btn.primary {\n  border-color: #667eea;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n}\n\n.use-btn.primary:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\n}\n\n.template-preview {\n  margin-top: 16px;\n  padding: 16px;\n  background: #f8fafc;\n  border-radius: 12px;\n  border: 1px solid #e2e8f0;\n}\n\n.preview-header {\n  font-size: 14px;\n  font-weight: 600;\n  color: #374151;\n  margin-bottom: 12px;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.preview-content {\n  background: white;\n  padding: 16px;\n  border-radius: 8px;\n  font-size: 14px;\n  line-height: 1.6;\n  max-height: 200px;\n  overflow-y: auto;\n  color: #374151;\n  border: 1px solid #e2e8f0;\n}\n\n.preview-actions {\n  display: flex;\n  gap: 8px;\n}\n\n.parameter-btn {\n  padding: 4px 8px;\n  border: 1px solid #667eea;\n  background: white;\n  color: #667eea;\n  border-radius: 6px;\n  font-size: 12px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.parameter-btn:hover {\n  background: #667eea;\n  color: white;\n}\n\n/* 滚动条样式 */\n.template-list::-webkit-scrollbar,\n.template-content::-webkit-scrollbar {\n  width: 6px;\n}\n\n.template-list::-webkit-scrollbar-track,\n.template-content::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 3px;\n}\n\n.template-list::-webkit-scrollbar-thumb,\n.template-content::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 3px;\n}\n\n.template-list::-webkit-scrollbar-thumb:hover,\n.template-content::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .template-panel {\n    max-height: 400px;\n  }\n  \n  .template-filters {\n    padding: 12px 16px;\n  }\n  \n  .template-list {\n    padding: 12px 16px;\n  }\n  \n  .template-card {\n    padding: 12px;\n  }\n  \n  .category-tabs {\n    gap: 6px;\n  }\n  \n  .category-tab {\n    padding: 4px 8px;\n    font-size: 13px;\n  }\n  \n  .template-card-header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 8px;\n  }\n  \n  .template-meta {\n    align-items: flex-start;\n    text-align: left;\n  }\n\n  .filter-row {\n    flex-direction: column;\n    align-items: stretch;\n    gap: 12px;\n  }\n\n  .filter-group {\n    justify-content: space-between;\n  }\n\n  .filter-select {\n    min-width: auto;\n    flex: 1;\n  }\n}\n\n/* 新增样式：模板卡片增强 */\n.template-title-section {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 8px;\n}\n\n.template-badges {\n  display: flex;\n  gap: 6px;\n  flex-wrap: wrap;\n}\n\n.badge {\n  font-size: 11px;\n  padding: 3px 8px;\n  border-radius: 8px;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  color: white;\n}\n\n.badge.system {\n  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);\n}\n\n.badge.public {\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n}\n\n.badge.complexity {\n  /* 颜色由内联样式设置 */\n}\n\n.template-stats {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  font-size: 13px;\n  color: #6b7280;\n  margin-top: 8px;\n}\n\n/* 分类和标签样式 */\n.template-meta.enhanced {\n  margin-bottom: 12px;\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.category {\n  font-size: 13px;\n  color: #6b7280;\n  font-weight: 500;\n}\n\n.tags {\n  display: flex;\n  gap: 6px;\n  flex-wrap: wrap;\n}\n\n.tag {\n  font-size: 11px;\n  padding: 2px 6px;\n  background: #f3f4f6;\n  color: #6b7280;\n  border-radius: 6px;\n  font-weight: 500;\n}\n\n.tag.more {\n  background: #e5e7eb;\n  color: #9ca3af;\n}\n", ".template-parameter-panel {\n  display: flex;\n  flex-direction: column;\n  gap: 24px;\n  max-height: 600px;\n  overflow-y: auto;\n}\n\n.parameter-form {\n  background: #f8fafc;\n  padding: 20px;\n  border-radius: 12px;\n  border: 1px solid #e2e8f0;\n}\n\n.parameter-form h4 {\n  margin: 0 0 16px 0;\n  color: #374151;\n  font-size: 16px;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.parameter-preview {\n  background: white;\n  padding: 20px;\n  border-radius: 12px;\n  border: 1px solid #e2e8f0;\n}\n\n.parameter-preview h4 {\n  margin: 0 0 16px 0;\n  color: #374151;\n  font-size: 16px;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.preview-content {\n  background: #f8fafc;\n  padding: 16px;\n  border-radius: 8px;\n  border: 1px solid #e2e8f0;\n  font-size: 14px;\n  line-height: 1.6;\n  color: #374151;\n  white-space: pre-wrap;\n  word-wrap: break-word;\n  max-height: 300px;\n  overflow-y: auto;\n}\n\n.no-parameters {\n  text-align: center;\n  padding: 40px 20px;\n  color: #6b7280;\n}\n\n.no-parameters p {\n  font-size: 16px;\n  margin-bottom: 20px;\n}\n\n.template-content {\n  background: #f8fafc;\n  padding: 16px;\n  border-radius: 8px;\n  border: 1px solid #e2e8f0;\n  font-size: 14px;\n  line-height: 1.6;\n  color: #374151;\n  white-space: pre-wrap;\n  word-wrap: break-word;\n  text-align: left;\n}\n\n/* 表单样式优化 */\n.ant-form-item-label > label {\n  font-weight: 600;\n  color: #374151;\n}\n\n.ant-input,\n.ant-select-selector,\n.ant-picker {\n  border-radius: 8px;\n  border: 2px solid #e2e8f0;\n  transition: all 0.3s ease;\n}\n\n.ant-input:focus,\n.ant-input-focused,\n.ant-select-focused .ant-select-selector,\n.ant-picker-focused {\n  border-color: #667eea;\n  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .template-parameter-panel {\n    gap: 16px;\n  }\n  \n  .parameter-form,\n  .parameter-preview {\n    padding: 16px;\n  }\n  \n  .preview-content {\n    max-height: 200px;\n  }\n}\n", "/* 报告头部组件样式 */\n.report-header {\n  text-align: center;\n  padding: 40px 0 60px 0;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  position: relative;\n  overflow: hidden;\n}\n\n.report-header::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grid\" width=\"10\" height=\"10\" patternUnits=\"userSpaceOnUse\"><path d=\"M 10 0 L 0 0 0 10\" fill=\"none\" stroke=\"rgba(255,255,255,0.1)\" stroke-width=\"1\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grid)\"/></svg>');\n  opacity: 0.3;\n}\n\n.header-main {\n  position: relative;\n  z-index: 1;\n  margin-bottom: 32px;\n}\n\n.report-title {\n  color: white !important;\n  margin-bottom: 16px !important;\n  font-size: 2.5rem !important;\n  font-weight: 700 !important;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n  line-height: 1.2;\n}\n\n.report-subtitle {\n  color: rgba(255, 255, 255, 0.9) !important;\n  font-size: 1.2rem !important;\n  font-weight: 400;\n  display: block;\n  margin-top: 8px;\n}\n\n.header-meta {\n  position: relative;\n  z-index: 1;\n  margin-bottom: 32px;\n}\n\n.meta-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.meta-icon {\n  color: rgba(255, 255, 255, 0.8);\n  font-size: 16px;\n}\n\n.report-tag {\n  background: rgba(255, 255, 255, 0.2) !important;\n  color: white !important;\n  border: 1px solid rgba(255, 255, 255, 0.3) !important;\n  backdrop-filter: blur(10px);\n  font-weight: 500;\n}\n\n.header-stats {\n  position: relative;\n  z-index: 1;\n  margin-top: 32px;\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\n  gap: 24px;\n  max-width: 600px;\n  margin: 0 auto;\n}\n\n.stat-item {\n  text-align: center;\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 12px;\n  padding: 20px 16px;\n  transition: all 0.3s ease;\n}\n\n.stat-item:hover {\n  background: rgba(255, 255, 255, 0.15);\n  transform: translateY(-2px);\n}\n\n.stat-value {\n  font-size: 1.8rem;\n  font-weight: 700;\n  color: white;\n  margin-bottom: 4px;\n}\n\n.stat-label {\n  font-size: 0.9rem;\n  color: rgba(255, 255, 255, 0.8);\n  font-weight: 500;\n}\n\n.header-divider {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 4px;\n  background: linear-gradient(90deg, \n    transparent 0%, \n    rgba(255, 255, 255, 0.3) 20%, \n    rgba(255, 255, 255, 0.6) 50%, \n    rgba(255, 255, 255, 0.3) 80%, \n    transparent 100%\n  );\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .report-header {\n    padding: 32px 16px 48px 16px;\n  }\n  \n  .report-title {\n    font-size: 2rem !important;\n  }\n  \n  .report-subtitle {\n    font-size: 1rem !important;\n  }\n  \n  .stats-grid {\n    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));\n    gap: 16px;\n  }\n  \n  .stat-item {\n    padding: 16px 12px;\n  }\n  \n  .stat-value {\n    font-size: 1.5rem;\n  }\n  \n  .stat-label {\n    font-size: 0.8rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .header-meta .ant-space {\n    justify-content: center;\n  }\n  \n  .meta-item {\n    flex-direction: column;\n    text-align: center;\n    gap: 4px;\n  }\n  \n  .stats-grid {\n    grid-template-columns: repeat(2, 1fr);\n  }\n}", "/* KPI网格组件样式 */\n.kpi-grid-container {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 16px;\n  padding: 32px;\n  color: white;\n  position: relative;\n  overflow: hidden;\n}\n\n.kpi-grid-container::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),\n              radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);\n  pointer-events: none;\n}\n\n.kpi-grid-title {\n  color: white !important;\n  text-align: center;\n  margin-bottom: 32px !important;\n  font-size: 1.8rem !important;\n  font-weight: 600 !important;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n  position: relative;\n  z-index: 1;\n}\n\n.kpi-metrics-row {\n  position: relative;\n  z-index: 1;\n}\n\n.kpi-metric-col {\n  display: flex;\n}\n\n.kpi-metric-card {\n  width: 100%;\n  height: 140px;\n  background: rgba(255, 255, 255, 0.95) !important;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.3) !important;\n  border-radius: 12px !important;\n  transition: all 0.3s ease;\n  cursor: pointer;\n  position: relative;\n  overflow: hidden;\n}\n\n.kpi-metric-card:hover {\n  background: rgba(255, 255, 255, 1) !important;\n  transform: translateY(-4px);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;\n}\n\n.kpi-metric-card .ant-card-body {\n  padding: 20px !important;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n}\n\n.kpi-metric-content {\n  text-align: center;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n}\n\n.metric-label {\n  font-size: 0.9rem;\n  color: #666;\n  font-weight: 500;\n  margin-bottom: 8px;\n  line-height: 1.3;\n}\n\n.metric-value-container {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  gap: 6px;\n}\n\n.metric-value {\n  font-size: 1.8rem;\n  font-weight: 700;\n  color: #1a1a1a;\n  line-height: 1;\n}\n\n.metric-change {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  font-size: 0.85rem;\n  font-weight: 600;\n}\n\n.change-text {\n  font-weight: 600;\n}\n\n/* 趋势指示器 */\n.trend-indicator {\n  position: absolute;\n  top: 0;\n  right: 0;\n  width: 4px;\n  height: 100%;\n  border-radius: 0 12px 12px 0;\n}\n\n.trend-increase {\n  background: linear-gradient(180deg, #52c41a 0%, #389e0d 100%);\n}\n\n.trend-decrease {\n  background: linear-gradient(180deg, #ff4d4f 0%, #cf1322 100%);\n}\n\n.trend-stable {\n  background: linear-gradient(180deg, #faad14 0%, #d48806 100%);\n}\n\n/* 变化类型样式 */\n.change-increase .metric-value {\n  color: #389e0d;\n}\n\n.change-decrease .metric-value {\n  color: #cf1322;\n}\n\n.change-stable .metric-value {\n  color: #d48806;\n}\n\n/* 装饰元素 */\n.kpi-grid-decoration {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-top: 32px;\n  position: relative;\n  z-index: 1;\n}\n\n.decoration-line {\n  flex: 1;\n  height: 1px;\n  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);\n}\n\n.decoration-dots {\n  display: flex;\n  gap: 8px;\n  margin: 0 16px;\n}\n\n.dot {\n  width: 6px;\n  height: 6px;\n  border-radius: 50%;\n  background: rgba(255, 255, 255, 0.6);\n  animation: pulse 2s infinite;\n}\n\n.dot:nth-child(2) {\n  animation-delay: 0.5s;\n}\n\n.dot:nth-child(3) {\n  animation-delay: 1s;\n}\n\n@keyframes pulse {\n  0%, 100% {\n    opacity: 0.6;\n    transform: scale(1);\n  }\n  50% {\n    opacity: 1;\n    transform: scale(1.2);\n  }\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .kpi-grid-container {\n    padding: 24px 16px;\n  }\n  \n  .kpi-grid-title {\n    font-size: 1.5rem !important;\n    margin-bottom: 24px !important;\n  }\n  \n  .kpi-metric-card {\n    height: 120px;\n  }\n  \n  .metric-value {\n    font-size: 1.5rem;\n  }\n  \n  .metric-label {\n    font-size: 0.8rem;\n  }\n  \n  .metric-change {\n    font-size: 0.75rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .kpi-grid-container {\n    padding: 20px 12px;\n  }\n  \n  .kpi-metric-card {\n    height: 100px;\n  }\n  \n  .kpi-metric-card .ant-card-body {\n    padding: 16px !important;\n  }\n  \n  .metric-value {\n    font-size: 1.3rem;\n  }\n  \n  .metric-label {\n    font-size: 0.75rem;\n    margin-bottom: 4px;\n  }\n}\n\n/* 打印样式 */\n@media print {\n  .kpi-grid-container {\n    background: #f5f5f5 !important;\n    color: #333 !important;\n    border: 1px solid #ddd;\n  }\n  \n  .kpi-grid-title {\n    color: #333 !important;\n  }\n  \n  .kpi-metric-card {\n    background: white !important;\n    border: 1px solid #ddd !important;\n  }\n}", "/* 执行摘要组件样式 */\n.executive-summary-container {\n  position: relative;\n}\n\n.executive-summary-card {\n  background: linear-gradient(135deg, #f8f9ff 0%, #e6f7ff 100%) !important;\n  border-left: 6px solid #1890ff !important;\n  border-radius: 12px !important;\n  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.1) !important;\n  position: relative;\n  overflow: hidden;\n}\n\n.executive-summary-card .ant-card-body {\n  padding: 32px !important;\n  position: relative;\n  z-index: 1;\n}\n\n/* 标题区域 */\n.summary-header {\n  margin-bottom: 24px;\n  padding-bottom: 16px;\n  border-bottom: 1px solid rgba(24, 144, 255, 0.1);\n}\n\n.header-icon {\n  width: 40px;\n  height: 40px;\n  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 18px;\n  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);\n}\n\n.summary-title {\n  color: #1890ff !important;\n  margin: 0 !important;\n  font-weight: 600 !important;\n  font-size: 1.4rem !important;\n}\n\n/* 摘要内容 */\n.summary-content {\n  margin-bottom: 28px;\n}\n\n.summary-text {\n  font-size: 1.1rem !important;\n  line-height: 1.8 !important;\n  color: #333 !important;\n  margin: 0 !important;\n  text-align: justify;\n  font-weight: 400;\n}\n\n/* 关键要点 */\n.key-points-section {\n  background: rgba(255, 255, 255, 0.8);\n  border-radius: 8px;\n  padding: 20px;\n  border: 1px solid rgba(24, 144, 255, 0.1);\n}\n\n.key-points-title {\n  color: #1890ff !important;\n  margin: 0 0 16px 0 !important;\n  font-size: 1.1rem !important;\n  font-weight: 600 !important;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.key-points-title::before {\n  content: '✨';\n  font-size: 16px;\n}\n\n.key-points-list {\n  margin: 0;\n}\n\n.key-points-list .ant-list-item {\n  border-bottom: 1px solid rgba(24, 144, 255, 0.08) !important;\n  padding: 12px 0 !important;\n}\n\n.key-points-list .ant-list-item:last-child {\n  border-bottom: none !important;\n}\n\n.key-point-item {\n  transition: all 0.3s ease;\n}\n\n.key-point-item:hover {\n  background: rgba(24, 144, 255, 0.05);\n  border-radius: 6px;\n  padding-left: 8px !important;\n  padding-right: 8px !important;\n}\n\n.point-icon {\n  color: #52c41a;\n  font-size: 16px;\n  margin-top: 2px;\n}\n\n.point-text {\n  color: #333;\n  font-size: 0.95rem;\n  line-height: 1.6;\n  font-weight: 500;\n}\n\n/* 装饰性元素 */\n.summary-decoration {\n  position: absolute;\n  top: 0;\n  right: 0;\n  width: 120px;\n  height: 120px;\n  overflow: hidden;\n  pointer-events: none;\n}\n\n.decoration-pattern {\n  position: absolute;\n  top: -60px;\n  right: -60px;\n  width: 120px;\n  height: 120px;\n  background: radial-gradient(circle, rgba(24, 144, 255, 0.1) 0%, transparent 70%);\n  border-radius: 50%;\n}\n\n.decoration-pattern::before {\n  content: '';\n  position: absolute;\n  top: 20px;\n  left: 20px;\n  width: 80px;\n  height: 80px;\n  background: radial-gradient(circle, rgba(24, 144, 255, 0.05) 0%, transparent 70%);\n  border-radius: 50%;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .executive-summary-card .ant-card-body {\n    padding: 24px 20px !important;\n  }\n  \n  .summary-title {\n    font-size: 1.2rem !important;\n  }\n  \n  .summary-text {\n    font-size: 1rem !important;\n    line-height: 1.7 !important;\n  }\n  \n  .key-points-section {\n    padding: 16px;\n  }\n  \n  .header-icon {\n    width: 36px;\n    height: 36px;\n    font-size: 16px;\n  }\n  \n  .decoration-pattern {\n    width: 80px;\n    height: 80px;\n    top: -40px;\n    right: -40px;\n  }\n}\n\n@media (max-width: 480px) {\n  .executive-summary-card .ant-card-body {\n    padding: 20px 16px !important;\n  }\n  \n  .summary-header {\n    margin-bottom: 20px;\n    padding-bottom: 12px;\n  }\n  \n  .summary-title {\n    font-size: 1.1rem !important;\n  }\n  \n  .summary-text {\n    font-size: 0.95rem !important;\n  }\n  \n  .key-points-section {\n    padding: 12px;\n  }\n  \n  .key-points-title {\n    font-size: 1rem !important;\n  }\n  \n  .point-text {\n    font-size: 0.9rem;\n  }\n}\n\n/* 打印样式 */\n@media print {\n  .executive-summary-card {\n    background: white !important;\n    border-left: 4px solid #333 !important;\n    box-shadow: none !important;\n  }\n  \n  .summary-title {\n    color: #333 !important;\n  }\n  \n  .key-points-title {\n    color: #333 !important;\n  }\n  \n  .header-icon {\n    background: #f0f0f0 !important;\n    color: #333 !important;\n  }\n  \n  .decoration-pattern {\n    display: none;\n  }\n}", "/* 图表卡片组件样式 */\n.chart-card-container {\n  position: relative;\n}\n\n.chart-card {\n  background: white !important;\n  border: 1px solid #f0f0f0 !important;\n  border-radius: 16px !important;\n  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08) !important;\n  transition: all 0.3s ease;\n  overflow: hidden;\n  position: relative;\n}\n\n.chart-card:hover {\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12) !important;\n  transform: translateY(-2px);\n}\n\n.chart-card .ant-card-head {\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;\n  border-bottom: 1px solid #e8e8e8 !important;\n  padding: 0 24px !important;\n  min-height: 64px;\n}\n\n.chart-card .ant-card-head-title {\n  padding: 16px 0 !important;\n}\n\n.chart-card .ant-card-extra {\n  padding: 16px 0 !important;\n}\n\n.chart-card .ant-card-body {\n  padding: 24px !important;\n}\n\n/* 卡片头部 */\n.chart-card-header {\n  width: 100%;\n}\n\n.chart-icon {\n  width: 36px;\n  height: 36px;\n  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 16px;\n  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);\n}\n\n.chart-title-info {\n  flex: 1;\n}\n\n.chart-title {\n  color: #333 !important;\n  margin: 0 !important;\n  font-size: 1.2rem !important;\n  font-weight: 600 !important;\n  line-height: 1.3;\n}\n\n.chart-type-badge {\n  display: inline-block;\n  background: rgba(24, 144, 255, 0.1);\n  color: #1890ff;\n  padding: 2px 8px;\n  border-radius: 12px;\n  font-size: 0.75rem;\n  font-weight: 500;\n  margin-top: 4px;\n  text-transform: uppercase;\n}\n\n/* 操作按钮 */\n.chart-action-btn {\n  color: #666 !important;\n  border: none !important;\n  background: transparent !important;\n  transition: all 0.3s ease;\n}\n\n.chart-action-btn:hover {\n  color: #1890ff !important;\n  background: rgba(24, 144, 255, 0.1) !important;\n  transform: scale(1.05);\n}\n\n/* 图表解读 */\n.chart-interpretation {\n  margin-bottom: 24px;\n  padding: 16px;\n  background: #fafafa;\n  border-radius: 8px;\n  border-left: 4px solid #1890ff;\n}\n\n.interpretation-text {\n  color: #555 !important;\n  font-size: 0.95rem !important;\n  line-height: 1.6 !important;\n  margin: 0 !important;\n  font-weight: 400;\n}\n\n/* 图表容器 */\n.chart-container {\n  position: relative;\n  background: white;\n  border-radius: 8px;\n  overflow: hidden;\n  border: 1px solid #f5f5f5;\n}\n\n.chart-container::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(45deg, \n    transparent 0%, \n    rgba(24, 144, 255, 0.02) 25%, \n    transparent 50%, \n    rgba(24, 144, 255, 0.02) 75%, \n    transparent 100%\n  );\n  pointer-events: none;\n  z-index: 0;\n}\n\n.chart-container > div {\n  position: relative;\n  z-index: 1;\n}\n\n/* 图表底部信息 */\n.chart-footer {\n  margin-top: 16px;\n  padding-top: 12px;\n  border-top: 1px solid #f0f0f0;\n  text-align: right;\n}\n\n.data-source {\n  color: #999;\n  font-size: 0.8rem;\n  font-style: italic;\n}\n\n/* 装饰性元素 */\n.chart-decoration {\n  position: absolute;\n  top: 0;\n  right: 0;\n  width: 60px;\n  height: 60px;\n  overflow: hidden;\n  pointer-events: none;\n}\n\n.decoration-corner {\n  position: absolute;\n  top: -30px;\n  right: -30px;\n  width: 60px;\n  height: 60px;\n  background: linear-gradient(135deg, rgba(24, 144, 255, 0.1) 0%, transparent 50%);\n  border-radius: 50%;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .chart-card .ant-card-head {\n    padding: 0 16px !important;\n    min-height: 56px;\n  }\n  \n  .chart-card .ant-card-body {\n    padding: 20px 16px !important;\n  }\n  \n  .chart-title {\n    font-size: 1.1rem !important;\n  }\n  \n  .chart-icon {\n    width: 32px;\n    height: 32px;\n    font-size: 14px;\n  }\n  \n  .chart-container {\n    height: 300px;\n  }\n  \n  .interpretation-text {\n    font-size: 0.9rem !important;\n  }\n  \n  .chart-interpretation {\n    padding: 12px;\n    margin-bottom: 20px;\n  }\n}\n\n@media (max-width: 480px) {\n  .chart-card .ant-card-head {\n    padding: 0 12px !important;\n  }\n  \n  .chart-card .ant-card-body {\n    padding: 16px 12px !important;\n  }\n  \n  .chart-title {\n    font-size: 1rem !important;\n  }\n  \n  .chart-container {\n    height: 250px;\n  }\n  \n  .chart-card-header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 8px;\n  }\n  \n  .chart-action-btn {\n    padding: 4px 8px !important;\n  }\n}\n\n/* 打印样式 */\n@media print {\n  .chart-card {\n    border: 1px solid #ddd !important;\n    box-shadow: none !important;\n    break-inside: avoid;\n  }\n  \n  .chart-card .ant-card-head {\n    background: #f9f9f9 !important;\n  }\n  \n  .chart-card .ant-card-extra {\n    display: none;\n  }\n  \n  .chart-container {\n    height: 300px !important;\n  }\n  \n  .decoration-corner {\n    display: none;\n  }\n}\n\n/* 图表加载状态 */\n.chart-container .echarts-loading {\n  background: rgba(255, 255, 255, 0.9);\n  backdrop-filter: blur(4px);\n}\n\n/* 图表交互提示 */\n.chart-container:hover::after {\n  content: '点击图表元素查看详情';\n  position: absolute;\n  bottom: 8px;\n  right: 8px;\n  background: rgba(0, 0, 0, 0.7);\n  color: white;\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-size: 0.7rem;\n  opacity: 0.8;\n  pointer-events: none;\n}", "/* 洞察列表组件样式 */\n.insight-list-container {\n  position: relative;\n}\n\n.insight-list-card {\n  background: #fff7e6 !important;\n  border: 1px solid #ffd591 !important;\n  border-radius: 12px !important;\n  box-shadow: 0 4px 16px rgba(255, 193, 7, 0.1) !important;\n}\n\n.insight-list-card .ant-card-head {\n  background: linear-gradient(135deg, #fff2e8 0%, #fff7e6 100%) !important;\n  border-bottom: 1px solid #ffd591 !important;\n}\n\n.insight-list-header {\n  width: 100%;\n}\n\n.header-icon {\n  width: 36px;\n  height: 36px;\n  background: linear-gradient(135deg, #faad14 0%, #d48806 100%);\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 16px;\n  box-shadow: 0 2px 8px rgba(250, 173, 20, 0.3);\n}\n\n.insight-list-title {\n  color: #d48806 !important;\n  margin: 0 !important;\n  font-size: 1.2rem !important;\n  font-weight: 600 !important;\n}\n\n/* 洞察列表 */\n.insights-list {\n  margin: 0;\n}\n\n.insights-list .ant-list-item {\n  border-bottom: 1px solid rgba(255, 193, 7, 0.2) !important;\n  padding: 20px 0 !important;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  position: relative;\n}\n\n.insights-list .ant-list-item:hover {\n  background: rgba(255, 193, 7, 0.05);\n  border-radius: 8px;\n  padding-left: 16px !important;\n  padding-right: 16px !important;\n}\n\n.insights-list .ant-list-item:last-child {\n  border-bottom: none !important;\n}\n\n/* 洞察项 */\n.insight-item {\n  position: relative;\n  padding-left: 12px;\n}\n\n.insight-content {\n  width: 100%;\n}\n\n.insight-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 12px;\n  flex-wrap: wrap;\n  gap: 8px;\n}\n\n.insight-type-icon {\n  font-size: 18px;\n  margin-right: 4px;\n}\n\n.insight-type-tag {\n  font-weight: 600;\n  border: none;\n  color: white;\n}\n\n.insight-impact-tag {\n  font-weight: 500;\n  border: none;\n  color: white;\n}\n\n.insight-category-tag {\n  background: rgba(0, 0, 0, 0.1);\n  color: #666;\n  border: 1px solid rgba(0, 0, 0, 0.15);\n  font-weight: 500;\n}\n\n.insight-text {\n  margin: 12px 0;\n  line-height: 1.6;\n}\n\n.insight-text .ant-typography {\n  color: #333 !important;\n  font-size: 0.95rem;\n  margin: 0;\n}\n\n/* 置信度 */\n.insight-confidence {\n  margin-top: 16px;\n}\n\n.confidence-label {\n  font-size: 0.85rem;\n  min-width: 50px;\n}\n\n.confidence-progress {\n  flex: 1;\n  max-width: 120px;\n}\n\n.confidence-value {\n  font-size: 0.85rem;\n  font-weight: 600;\n  min-width: 35px;\n  text-align: right;\n}\n\n/* 洞察装饰 */\n.insight-decoration {\n  position: absolute;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  width: 4px;\n  border-radius: 2px;\n  opacity: 0.6;\n}\n\n/* 洞察类型特定样式 */\n.insight-trend .insight-decoration {\n  background: #1890ff;\n}\n\n.insight-anomaly .insight-decoration {\n  background: #fa8c16;\n}\n\n.insight-opportunity .insight-decoration {\n  background: #52c41a;\n}\n\n.insight-risk .insight-decoration {\n  background: #ff4d4f;\n}\n\n.insight-correlation .insight-decoration {\n  background: #722ed1;\n}\n\n/* 统计信息 */\n.insights-summary {\n  margin-top: 24px;\n  padding-top: 16px;\n  border-top: 1px solid rgba(255, 193, 7, 0.2);\n  text-align: center;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .insight-header {\n    flex-direction: column;\n    align-items: flex-start;\n  }\n  \n  .insight-confidence {\n    margin-top: 12px;\n  }\n  \n  .confidence-progress {\n    max-width: 100px;\n  }\n}\n\n@media (max-width: 480px) {\n  .insights-list .ant-list-item {\n    padding: 16px 0 !important;\n  }\n  \n  .insight-text .ant-typography {\n    font-size: 0.9rem;\n  }\n  \n  .insight-type-icon {\n    font-size: 16px;\n  }\n}", "/* 建议列表组件样式 */\n.recommendation-list-container {\n  position: relative;\n}\n\n.recommendation-list-card {\n  background: #f6ffed !important;\n  border: 1px solid #b7eb8f !important;\n  border-radius: 12px !important;\n  box-shadow: 0 4px 16px rgba(82, 196, 26, 0.1) !important;\n}\n\n.recommendation-list-card .ant-card-head {\n  background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%) !important;\n  border-bottom: 1px solid #b7eb8f !important;\n}\n\n.recommendation-list-header {\n  width: 100%;\n}\n\n.header-icon {\n  width: 36px;\n  height: 36px;\n  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 16px;\n  box-shadow: 0 2px 8px rgba(82, 196, 26, 0.3);\n}\n\n.recommendation-list-title {\n  color: #389e0d !important;\n  margin: 0 !important;\n  font-size: 1.2rem !important;\n  font-weight: 600 !important;\n}\n\n/* 建议列表 */\n.recommendations-list {\n  margin: 0;\n}\n\n.recommendations-list .ant-list-item {\n  border-bottom: 1px solid rgba(183, 235, 143, 0.3) !important;\n  padding: 20px 0 !important;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  position: relative;\n}\n\n.recommendations-list .ant-list-item:hover {\n  background: rgba(82, 196, 26, 0.05);\n  border-radius: 8px;\n  padding-left: 16px !important;\n  padding-right: 16px !important;\n}\n\n.recommendations-list .ant-list-item:last-child {\n  border-bottom: none !important;\n}\n\n/* 建议项 */\n.recommendation-item {\n  position: relative;\n  padding-left: 12px;\n}\n\n.recommendation-content {\n  width: 100%;\n}\n\n.recommendation-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n  flex-wrap: wrap;\n  gap: 8px;\n}\n\n.header-left {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 12px;\n}\n\n.recommendation-number {\n  width: 28px;\n  height: 28px;\n  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);\n  color: white;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: 700;\n  font-size: 0.9rem;\n  box-shadow: 0 2px 6px rgba(82, 196, 26, 0.3);\n}\n\n.area-tag {\n  font-weight: 600;\n  border: none;\n  color: white;\n  background: #1890ff;\n}\n\n.priority-tag {\n  font-weight: 500;\n  border: none;\n  color: white;\n}\n\n.timeline-info {\n  color: #666;\n  font-size: 0.85rem;\n}\n\n.recommendation-text {\n  margin: 12px 0 16px 0;\n  line-height: 1.6;\n}\n\n.recommendation-text .ant-typography {\n  color: #333 !important;\n  font-size: 0.95rem;\n  margin: 0;\n}\n\n.recommendation-actions {\n  display: flex;\n  justify-content: flex-end;\n}\n\n.detail-button {\n  color: #52c41a !important;\n  font-weight: 500;\n  padding: 0;\n  height: auto;\n}\n\n.detail-button:hover {\n  color: #389e0d !important;\n}\n\n/* 优先级指示器 */\n.priority-indicator {\n  position: absolute;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  width: 4px;\n  border-radius: 2px;\n  opacity: 0.8;\n}\n\n/* 优先级特定样式 */\n.priority-high .priority-indicator {\n  background: #ff4d4f;\n}\n\n.priority-medium .priority-indicator {\n  background: #faad14;\n}\n\n.priority-low .priority-indicator {\n  background: #52c41a;\n}\n\n.priority-default .priority-indicator {\n  background: #d9d9d9;\n}\n\n/* 统计信息 */\n.recommendations-summary {\n  margin-top: 24px;\n  padding-top: 16px;\n  border-top: 1px solid rgba(183, 235, 143, 0.3);\n  text-align: center;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .recommendation-header {\n    flex-direction: column;\n    align-items: flex-start;\n  }\n  \n  .header-left {\n    width: 100%;\n  }\n  \n  .timeline-info {\n    margin-top: 8px;\n  }\n}\n\n@media (max-width: 480px) {\n  .recommendations-list .ant-list-item {\n    padding: 16px 0 !important;\n  }\n  \n  .recommendation-text .ant-typography {\n    font-size: 0.9rem;\n  }\n  \n  .recommendation-number {\n    width: 24px;\n    height: 24px;\n    font-size: 0.8rem;\n  }\n  \n  .header-left {\n    gap: 8px;\n  }\n}", "/* 动态报告渲染器样式 */\n.dynamic-report-container {\n  width: 100%;\n  background: #ffffff;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n}\n\n.dynamic-report-empty {\n  padding: 40px 20px;\n  text-align: center;\n}\n\n/* 报告工具栏 */\n.report-toolbar {\n  display: flex;\n  justify-content: flex-end;\n  align-items: center;\n  padding: 16px 24px;\n  background: #fafafa;\n  border-bottom: 1px solid #f0f0f0;\n  gap: 12px;\n}\n\n.export-button {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px 16px;\n  background: #1890ff;\n  color: white;\n  border: none;\n  border-radius: 6px;\n  cursor: pointer;\n  font-size: 14px;\n  transition: all 0.3s ease;\n}\n\n.export-button:hover {\n  background: #40a9ff;\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);\n}\n\n/* 报告内容区域 */\n.report-content {\n  padding: 24px;\n}\n\n/* 报告组件通用样式 */\n.report-component {\n  margin-bottom: 32px;\n  transition: all 0.3s ease;\n}\n\n.report-component:last-child {\n  margin-bottom: 0;\n}\n\n.report-component:hover {\n  transform: translateY(-2px);\n}\n\n/* 特定组件类型的样式 */\n.report-component-header {\n  margin-bottom: 40px;\n}\n\n.report-component-kpi_grid {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 12px;\n  padding: 24px;\n  color: white;\n  margin-bottom: 40px;\n}\n\n.report-component-executive_summary {\n  background: #f8f9ff;\n  border-left: 4px solid #1890ff;\n  border-radius: 8px;\n  padding: 24px;\n}\n\n.report-component-chart {\n  background: white;\n  border: 1px solid #f0f0f0;\n  border-radius: 12px;\n  padding: 24px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\n}\n\n.report-component-insight_list {\n  background: #fff7e6;\n  border: 1px solid #ffd591;\n  border-radius: 8px;\n  padding: 24px;\n}\n\n.report-component-recommendation_list {\n  background: #f6ffed;\n  border: 1px solid #b7eb8f;\n  border-radius: 8px;\n  padding: 24px;\n}\n\n.report-component-data_table {\n  background: white;\n  border: 1px solid #f0f0f0;\n  border-radius: 8px;\n  overflow: hidden;\n}\n\n.report-component-text_section {\n  background: white;\n  padding: 20px 0;\n}\n\n/* 未知组件警告样式 */\n.report-component-unknown {\n  margin: 16px 0;\n}\n\n/* 报告元数据（开发模式） */\n.report-metadata {\n  margin-top: 40px;\n  padding: 20px;\n  background: #f5f5f5;\n  border-top: 1px solid #e8e8e8;\n  font-size: 12px;\n}\n\n.report-metadata details {\n  cursor: pointer;\n}\n\n.report-metadata summary {\n  font-weight: 600;\n  color: #666;\n  margin-bottom: 10px;\n}\n\n.report-metadata pre {\n  background: white;\n  padding: 12px;\n  border-radius: 4px;\n  border: 1px solid #ddd;\n  overflow-x: auto;\n  font-size: 11px;\n  line-height: 1.4;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .report-content {\n    padding: 16px;\n  }\n  \n  .report-component {\n    margin-bottom: 24px;\n  }\n  \n  .report-toolbar {\n    padding: 12px 16px;\n  }\n  \n  .export-button {\n    padding: 6px 12px;\n    font-size: 13px;\n  }\n}\n\n/* 打印样式 */\n@media print {\n  .report-toolbar {\n    display: none;\n  }\n  \n  .report-metadata {\n    display: none;\n  }\n  \n  .report-component {\n    break-inside: avoid;\n    margin-bottom: 20px;\n  }\n  \n  .dynamic-report-container {\n    box-shadow: none;\n    border: none;\n  }\n}", "/* 多轮分析页面样式 */\n.analysis-container {\n  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\n  min-height: 100vh;\n}\n\n/* 美化输入框焦点效果 */\n.ant-input:focus,\n.ant-input-focused {\n  border-color: #667eea !important;\n  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2) !important;\n}\n\n/* 美化按钮悬停效果 */\n.template-quick-btn {\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.template-quick-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);\n}\n\n/* 美化卡片样式 */\n.analysis-card {\n  border-radius: 16px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  border: none;\n  overflow: hidden;\n  transition: all 0.3s ease;\n}\n\n.analysis-card:hover {\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\n  transform: translateY(-2px);\n}\n\n/* 美化时间线样式 */\n.ant-timeline-item-head {\n  border-radius: 50%;\n  border-width: 2px;\n}\n\n.ant-timeline-item-content {\n  margin-left: 8px;\n}\n\n/* 美化步骤状态 */\n.step-process {\n  animation: pulse 2s infinite;\n}\n\n@keyframes pulse {\n  0% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.7;\n  }\n  100% {\n    opacity: 1;\n  }\n}\n\n/* 骨架屏动画 */\n@keyframes skeleton-pulse {\n  0% {\n    background-color: #f1f5f9;\n  }\n  50% {\n    background-color: #e2e8f0;\n  }\n  100% {\n    background-color: #f1f5f9;\n  }\n}\n\n/* 美化模态框 */\n.ant-modal-content {\n  border-radius: 16px;\n  overflow: hidden;\n  border: 1px solid #e2e8f0;\n}\n\n.ant-modal-header {\n  border-radius: 16px 16px 0 0;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-bottom: none;\n  padding: 20px 24px;\n}\n\n.ant-modal-title {\n  color: white;\n  font-weight: bold;\n}\n\n.ant-modal-close {\n  color: white;\n}\n\n.ant-modal-close:hover {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n/* 美化表单项 */\n.ant-form-item-label > label {\n  font-weight: 600;\n  color: #374151;\n}\n\n/* 美化标签 */\n.ant-tag {\n  border-radius: 6px;\n  font-weight: 500;\n  padding: 2px 8px;\n}\n\n/* 美化Alert */\n.ant-alert {\n  border-radius: 12px;\n  border: 1px solid;\n}\n\n.ant-alert-warning {\n  background: #fefce8;\n  border-color: #fde047;\n}\n\n.ant-alert-error {\n  background: #fef2f2;\n  border-color: #fca5a5;\n}\n\n.ant-alert-success {\n  background: #f0fdf4;\n  border-color: #86efac;\n}\n\n.ant-alert-info {\n  background: #eff6ff;\n  border-color: #93c5fd;\n}\n\n/* 美化加载状态 */\n.ant-spin-dot-item {\n  background-color: #667eea;\n}\n\n/* 美化滚动条 */\n.analysis-container::-webkit-scrollbar {\n  width: 8px;\n}\n\n.analysis-container::-webkit-scrollbar-track {\n  background: rgba(0, 0, 0, 0.1);\n  border-radius: 4px;\n}\n\n.analysis-container::-webkit-scrollbar-thumb {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 4px;\n}\n\n.analysis-container::-webkit-scrollbar-thumb:hover {\n  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .analysis-container {\n    padding: 16px;\n  }\n  \n  .template-quick-btn {\n    min-width: 60px;\n    font-size: 12px;\n  }\n  \n  .analysis-card {\n    margin: 8px 0;\n  }\n}\n\n/* 美化继续输入框 */\n.continue-input-card {\n  border-radius: 16px;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);\n  border: 1px solid #e2e8f0;\n  background: white;\n}\n\n/* 美化浮动按钮 */\n.floating-control-buttons {\n  backdrop-filter: blur(10px);\n  background: rgba(255, 255, 255, 0.9);\n  border-radius: 16px;\n  padding: 8px;\n}\n\n.floating-control-buttons .ant-btn {\n  border-radius: 12px;\n  font-weight: 600;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.floating-control-buttons .ant-btn:hover {\n  transform: translateY(-2px);\n}\n\n/* 美化图表容器 */\n.chart-container {\n  border-radius: 12px;\n  overflow: hidden;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n}\n\n/* 美化代码块 */\npre {\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\n  border-radius: 8px;\n  border: 1px solid #dee2e6;\n}\n\n/* 美化表格 */\n.ant-table {\n  border-radius: 12px;\n  overflow: hidden;\n}\n\n.ant-table-thead > tr > th {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  font-weight: 600;\n  border: none;\n}\n\n.ant-table-tbody > tr:hover > td {\n  background: rgba(102, 126, 234, 0.05);\n}\n\n/* 美化步骤卡片 */\n.step-card {\n  border-radius: 12px;\n  border: 1px solid #e1e5e9;\n  background: white;\n  transition: all 0.3s ease;\n}\n\n.step-card:hover {\n  border-color: #667eea;\n  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.1);\n}\n\n/* 美化意图确认卡片 */\n.intent-confirmation-card {\n  border-radius: 16px;\n  background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);\n  border: 2px solid #e1e5e9;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);\n}\n\n/* 美化选项按钮 */\n.option-button {\n  border-radius: 8px;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  cursor: pointer;\n  user-select: none;\n}\n\n.option-button:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n.option-button.selected {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border-color: #667eea;\n}\n\n/* 美化进度指示器 */\n.progress-indicator {\n  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);\n  border-radius: 4px;\n  height: 4px;\n  animation: progress 2s ease-in-out infinite;\n}\n\n@keyframes progress {\n  0% {\n    width: 0%;\n  }\n  50% {\n    width: 70%;\n  }\n  100% {\n    width: 100%;\n  }\n}\n\n/* 热门模板按钮样式 */\n.hot-template-btn {\n  background: white;\n  border: 1px solid #e2e8f0;\n  border-radius: 12px;\n  padding: 12px 16px;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  cursor: pointer;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n\n.hot-template-btn:hover {\n  border-color: #667eea;\n  background: #f8faff;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);\n}\n\n.hot-template-btn:active {\n  transform: translateY(0);\n}\n\n/* 输入框焦点样式优化 */\n.analysis-input:focus {\n  border-color: #667eea !important;\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;\n  outline: none;\n}\n\n/* 主要按钮样式 */\n.primary-gradient-btn {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border: none;\n  border-radius: 12px;\n  color: white;\n  font-weight: bold;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4);\n}\n\n.primary-gradient-btn:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.5);\n}\n\n.primary-gradient-btn:active {\n  transform: translateY(0);\n}\n\n/* 卡片阴影优化 */\n.clean-card {\n  background: white;\n  border: 1px solid #e2e8f0;\n  border-radius: 16px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  transition: all 0.3s ease;\n}\n\n.clean-card:hover {\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\n  transform: translateY(-2px);\n}\n\n/* 结构化报告样式 */\n.analysis-structured-report {\n  background: transparent;\n  border-radius: 0;\n  box-shadow: none;\n}\n\n.analysis-structured-report .report-toolbar {\n  background: #f8f9fa;\n  border: 1px solid #e9ecef;\n  border-radius: 8px 8px 0 0;\n}\n\n.analysis-structured-report .report-content {\n  background: white;\n  border: 1px solid #e9ecef;\n  border-top: none;\n  border-radius: 0 0 8px 8px;\n}\n\n/* 报告组件在分析页面中的特殊样式 */\n.analysis-structured-report .report-component-header {\n  margin-bottom: 32px;\n}\n\n.analysis-structured-report .report-component-kpi_grid {\n  margin-bottom: 32px;\n}\n\n.analysis-structured-report .report-component-chart {\n  margin-bottom: 32px;\n}\n", "/* 全局样式 */\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\nbody {\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;\n}\n\n/* 语言切换器 */\n.language-switch-container {\n  position: absolute;\n  top: 20px;\n  right: 20px;\n  z-index: 10;\n  background: rgba(255, 255, 255, 0.9);\n  backdrop-filter: blur(8px);\n  border-radius: 8px;\n  padding: 8px;\n  border: 1px solid rgba(59, 130, 246, 0.2);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.language-switch-container .ant-select {\n  border: none !important;\n  box-shadow: none !important;\n}\n\n.language-switch-container .ant-select .ant-select-selector {\n  border: none !important;\n  box-shadow: none !important;\n  background: transparent !important;\n}\n\n/* 登录页面主容器 */\n.login-container {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 100vh;\n  background: #ffffff;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n  position: relative;\n  overflow: hidden;\n}\n\n/* 去除整体背景虚化 */\n.login-container::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(255, 255, 255, 0.1);\n  z-index: 1;\n}\n\n/* 背景产品介绍卡片 */\n.background-cards {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 0;\n  pointer-events: none; /* 防止卡片阻挡交互 */\n}\n\n.feature-card {\n  position: absolute;\n  width: 280px;\n  padding: 24px;\n  background: rgba(255, 255, 255, 0.85);\n  border: 1px solid rgba(59, 130, 246, 0.15);\n  border-radius: 16px;\n  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.08);\n  transition: all 0.3s ease;\n  animation: gentleFloat 12s ease-in-out infinite;\n  pointer-events: auto; /* 恢复卡片本身的交互 */\n}\n\n.feature-card:hover {\n  background: rgba(255, 255, 255, 0.95);\n  border-color: rgba(59, 130, 246, 0.25);\n  transform: translateY(-2px);\n  box-shadow: 0 8px 28px rgba(59, 130, 246, 0.12);\n}\n\n.card-icon {\n  font-size: 36px;\n  color: #3b82f6;\n  margin-bottom: 16px;\n  display: block;\n  transition: transform 0.3s ease;\n}\n\n.feature-card:hover .card-icon {\n  transform: scale(1.1);\n}\n\n.feature-card h3 {\n  color: #1e293b;\n  font-size: 18px;\n  font-weight: 600;\n  margin-bottom: 12px;\n  line-height: 1.4;\n  transition: color 0.3s ease;\n}\n\n.feature-card:hover h3 {\n  color: #3b82f6;\n}\n\n.feature-card p {\n  color: #64748b;\n  font-size: 15px;\n  line-height: 1.5;\n  margin: 0;\n  transition: color 0.3s ease;\n}\n\n.feature-card:hover p {\n  color: #475569;\n}\n\n/* 卡片位置布局 - 智能定位，避免遮挡中央区域 */\n.card-1 {\n  top: 5%;\n  left: 2%;\n  animation-delay: 0s;\n}\n\n.card-2 {\n  top: 8%;\n  right: 3%;\n  animation-delay: 2s;\n}\n\n.card-3 {\n  top: 35%;\n  left: 1%;\n  animation-delay: 4s;\n}\n\n.card-4 {\n  top: 38%;\n  right: 2%;\n  animation-delay: 6s;\n}\n\n.card-5 {\n  bottom: 15%;\n  left: 3%;\n  animation-delay: 8s;\n}\n\n.card-6 {\n  bottom: 12%;\n  right: 4%;\n  animation-delay: 10s;\n}\n\n@keyframes gentleFloat {\n  0%, 100% {\n    transform: translateY(0px) translateX(0px);\n  }\n  33% {\n    transform: translateY(-3px) translateX(2px);\n  }\n  66% {\n    transform: translateY(-1px) translateX(-1px);\n  }\n}\n\n/* 中央登录卡片 - 增大突出主题 */\n.central-login-card {\n  position: relative;\n  z-index: 2;\n  width: 100%;\n  max-width: 640px;\n  padding: 64px;\n  background: rgba(255, 255, 255, 0.9);\n  backdrop-filter: blur(16px);\n  border: 1px solid rgba(59, 130, 246, 0.25);\n  border-radius: 28px;\n  box-shadow: \n    0 24px 80px rgba(59, 130, 246, 0.15),\n    0 12px 32px rgba(0, 0, 0, 0.06);\n  text-align: center;\n  transition: all 0.3s ease;\n}\n\n.central-login-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(135deg, \n    rgba(59, 130, 246, 0.03) 0%, \n    rgba(255, 255, 255, 0.08) 50%, \n    rgba(29, 78, 216, 0.03) 100%);\n  border-radius: 28px;\n  z-index: -1;\n}\n\n/* 登录头部 - 增大主题元素 */\n.login-header {\n  margin-bottom: 56px;\n}\n\n.login-logo {\n  width: 140px;\n  height: 140px;\n  object-fit: contain;\n  margin: 0 auto 32px;\n  display: block;\n  filter: drop-shadow(0 12px 32px rgba(59, 130, 246, 0.2));\n  transition: all 0.3s ease;\n}\n\n.login-logo:hover {\n  transform: scale(1.05) translateY(-3px);\n  filter: drop-shadow(0 16px 40px rgba(59, 130, 246, 0.25));\n}\n\n.login-title {\n  color: #1e293b !important;\n  font-size: 48px !important;\n  font-weight: 800 !important;\n  margin-bottom: 20px !important;\n  letter-spacing: -0.02em;\n  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n.login-subtitle {\n  color: #64748b !important;\n  font-size: 20px !important;\n  font-weight: 500 !important;\n  line-height: 1.6;\n}\n\n/* 登录表单 */\n.login-form {\n  margin-bottom: 48px;\n}\n\n.login-form .ant-form-item {\n  margin-bottom: 32px;\n}\n\n.login-input {\n  height: 60px !important;\n  border-radius: 18px !important;\n  border: 1px solid rgba(59, 130, 246, 0.2) !important;\n  font-size: 18px !important;\n  transition: all 0.3s ease !important;\n  background: rgba(255, 255, 255, 0.8) !important;\n  backdrop-filter: blur(8px) !important;\n}\n\n.login-input:hover {\n  border-color: rgba(59, 130, 246, 0.4) !important;\n  background: rgba(255, 255, 255, 0.9) !important;\n}\n\n.login-input:focus,\n.login-input.ant-input-focused {\n  border-color: #3b82f6 !important;\n  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1) !important;\n  background: rgba(255, 255, 255, 0.95) !important;\n}\n\n.login-input .ant-input {\n  border: none !important;\n  box-shadow: none !important;\n  padding-left: 20px !important;\n  background: transparent !important;\n}\n\n.login-input .anticon {\n  color: #64748b;\n  margin-left: 24px;\n  font-size: 20px;\n}\n\n.login-input:focus .anticon {\n  color: #3b82f6;\n}\n\n.login-button {\n  height: 60px !important;\n  border-radius: 18px !important;\n  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;\n  border: none !important;\n  font-size: 18px !important;\n  font-weight: 600 !important;\n  box-shadow: 0 12px 32px rgba(59, 130, 246, 0.3) !important;\n  transition: all 0.3s ease !important;\n  position: relative;\n  overflow: hidden;\n}\n\n.login-button::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n  transition: left 0.5s ease;\n}\n\n.login-button:hover::before {\n  left: 100%;\n}\n\n.login-button:hover {\n  transform: translateY(-3px) !important;\n  box-shadow: 0 16px 40px rgba(59, 130, 246, 0.4) !important;\n}\n\n.login-button:active {\n  transform: translateY(0) !important;\n}\n\n/* 登录底部 */\n.login-footer {\n  padding-top: 32px;\n  border-top: 1px solid rgba(59, 130, 246, 0.15);\n}\n\n.company-info {\n  color: #94a3b8 !important;\n  font-size: 16px !important;\n  font-weight: 500 !important;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .feature-card {\n    width: 260px;\n    padding: 20px;\n  }\n  \n  .card-icon {\n    font-size: 32px;\n  }\n  \n  .feature-card h3 {\n    font-size: 16px;\n  }\n  \n  .feature-card p {\n    font-size: 14px;\n  }\n  \n  /* 调整卡片位置，给中央区域更多空间 */\n  .card-1 { top: 3%; left: 1%; }\n  .card-2 { top: 6%; right: 1%; }\n  .card-3 { top: 32%; left: 0.5%; }\n  .card-4 { top: 35%; right: 0.5%; }\n  .card-5 { bottom: 12%; left: 1%; }\n  .card-6 { bottom: 9%; right: 1%; }\n}\n\n@media (max-width: 1024px) {\n  .feature-card {\n    width: 240px;\n    padding: 18px;\n  }\n  \n  .card-icon {\n    font-size: 30px;\n    margin-bottom: 12px;\n  }\n  \n  .feature-card h3 {\n    font-size: 15px;\n    margin-bottom: 10px;\n  }\n  \n  .feature-card p {\n    font-size: 13px;\n  }\n  \n  .central-login-card {\n    max-width: 560px;\n    padding: 56px;\n  }\n  \n  .login-title {\n    font-size: 40px !important;\n  }\n  \n  /* 进一步调整卡片位置 */\n  .card-3, .card-4 {\n    display: none; /* 隐藏中间的卡片，避免遮挡 */\n  }\n}\n\n@media (max-width: 768px) {\n  .login-container {\n    padding: 16px;\n  }\n  \n  .feature-card {\n    width: 200px;\n    padding: 16px;\n  }\n  \n  .card-icon {\n    font-size: 28px;\n    margin-bottom: 10px;\n  }\n  \n  .feature-card h3 {\n    font-size: 14px;\n    margin-bottom: 8px;\n  }\n  \n  .feature-card p {\n    font-size: 12px;\n    line-height: 1.4;\n  }\n  \n  .central-login-card {\n    max-width: 100%;\n    padding: 48px 32px;\n  }\n  \n  .login-logo {\n    width: 120px;\n    height: 120px;\n  }\n  \n  .login-title {\n    font-size: 36px !important;\n  }\n  \n  .login-subtitle {\n    font-size: 18px !important;\n  }\n  \n  /* 只显示顶部和底部的卡片 */\n  .card-3, .card-4 {\n    display: none;\n  }\n  \n  /* 调整剩余卡片位置 */\n  .card-1 { top: 2%; left: 0.5%; }\n  .card-2 { top: 4%; right: 0.5%; }\n  .card-5 { bottom: 8%; left: 0.5%; }\n  .card-6 { bottom: 6%; right: 0.5%; }\n}\n\n@media (max-width: 640px) {\n  .feature-card {\n    width: 180px;\n    padding: 14px;\n  }\n  \n  .card-icon {\n    font-size: 24px;\n  }\n  \n  .feature-card h3 {\n    font-size: 13px;\n  }\n  \n  .feature-card p {\n    font-size: 11px;\n  }\n  \n  /* 只保留角落的卡片 */\n  .card-1 { top: 1%; left: 0%; }\n  .card-2 { top: 2%; right: 0%; }\n  .card-5 { bottom: 6%; left: 0%; }\n  .card-6 { bottom: 4%; right: 0%; }\n}\n\n@media (max-width: 480px) {\n  .feature-card {\n    display: none; /* 小屏幕完全隐藏背景卡片 */\n  }\n  \n  .central-login-card {\n    padding: 40px 28px;\n    margin: 20px;\n  }\n  \n  .login-container::before {\n    background: rgba(255, 255, 255, 0.05); /* 减少背景遮罩 */\n  }\n} ", "/* frontend/src/admin/components/AdminPage.css */\n\n.admin-page {\n  padding: 24px;\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n.admin-page-search-card {\n  margin-bottom: 24px;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n  border: none;\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\n}\n\n.admin-page-main-card {\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n  border: none;\n}\n\n.admin-page-header {\n  padding-bottom: 20px;\n  border-bottom: 1px solid #f0f0f0;\n  margin-bottom: 20px;\n}\n\n.admin-page-title {\n  margin: 0;\n  color: #262626;\n  font-weight: 600;\n  font-size: 20px;\n}\n\n.admin-page-content .ant-table {\n  border-radius: 8px;\n  overflow: hidden;\n}\n\n.admin-page-content .ant-table-thead > tr > th {\n  background: #fafafa;\n  border-bottom: 1px solid #f0f0f0;\n  font-weight: 600;\n  color: #262626;\n} ", "/* 完全重置所有元素的默认样式 */\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\n*::before,\n*::after {\n  box-sizing: border-box;\n}\n/* 禁用过度滚动和下拉刷新 */\nhtml {\n  overscroll-behavior: none; /* 禁用所有过度滚动行为 */\n  overscroll-behavior-y: none; /* 禁用垂直过度滚动 */\n  overscroll-behavior-x: none; /* 禁用水平过度滚动 */\n  height: 100vh;\n}\nbody {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  min-height: 100vh;\n\n  /* 添加这些属性来禁用过度滚动 */\n  overscroll-behavior: none;\n  overscroll-behavior-y: none;\n  overscroll-behavior-x: none;\n  \n  /* 针对 iOS Safari 的特殊处理 */\n  -webkit-overflow-scrolling: touch;\n  \n  /* 禁用下拉刷新 */\n  touch-action: pan-x pan-y;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n\n.app-container {\n  min-height: 100vh;\n}\n\n/* 新的侧边栏样式 - 统一登录页风格 */\n.app-sidebar {\n  background: #ffffff !important;\n  border-right: 1px solid rgba(59, 130, 246, 0.15) !important;\n  box-shadow: 2px 0 8px rgba(59, 130, 246, 0.08) !important;\n  display: flex;\n  flex-direction: column;\n  height: 100vh; /* 确保侧边栏占满整个视口高度 */\n  overflow: hidden; /* 防止整个侧边栏滚动 */\n  position: relative; /* 为绝对定位的底部区域提供定位上下文 */\n}\n\n.sidebar-header {\n  padding: 16px 20px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  border-bottom: 1px solid rgba(59, 130, 246, 0.1);\n  background: linear-gradient(135deg, rgba(59, 130, 246, 0.02) 0%, rgba(255, 255, 255, 0.05) 100%);\n  flex-shrink: 0; /* 确保头部区域不会被压缩 */\n}\n\n.sidebar-header-left {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.sidebar-header-icon {\n  width: 32px;\n  height: 32px;\n  object-fit: contain;\n}\n\n.sidebar-company-brand {\n  display: flex;\n  align-items: center;\n  gap: 2px;\n}\n\n.company-prefix {\n  font-size: 18px;\n  font-weight: 800;\n  letter-spacing: 0px;\n  text-transform: uppercase;\n  background: linear-gradient(45deg, #00d4ff, #ff0080, #7928ca, #ff6b35);\n  background-size: 300% 300%;\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  padding: 0px 3px;\n  animation: tech-glow 2.5s ease-in-out infinite;\n  cursor: default;\n  position: relative;\n  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;\n}\n\n.company-prefix::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff);\n  background-size: 400% 400%;\n  border-radius: 3px;\n  opacity: 0.1;\n  animation: rainbow-flow 2s ease-in-out infinite;\n  z-index: -1;\n}\n\n@keyframes rainbow-flow {\n  0%, 100% {\n    background-position: 0% 50%;\n  }\n  50% {\n    background-position: 100% 50%;\n  }\n}\n\n.company-name {\n  font-size: 18px;\n  font-weight: 800;\n  letter-spacing: 1px;\n  background: linear-gradient(135deg, #00d4ff 0%, #0099ff 25%, #7928ca 50%, #ff0080 75%, #00d4ff 100%);\n  background-size: 300% 300%;\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  line-height: 1.2;\n  position: relative;\n  animation: cyber-wave 3s ease-in-out infinite;\n  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;\n  text-shadow: 0 0 10px rgba(0, 212, 255, 0.3), 0 0 20px rgba(121, 40, 202, 0.2);\n}\n\n@keyframes tech-glow {\n  0%, 100% {\n    background-position: 0% 50%;\n    filter: brightness(1);\n  }\n  25% {\n    background-position: 50% 0%;\n    filter: brightness(1.2);\n  }\n  50% {\n    background-position: 100% 50%;\n    filter: brightness(1.1);\n  }\n  75% {\n    background-position: 50% 100%;\n    filter: brightness(1.3);\n  }\n}\n\n@keyframes cyber-wave {\n  0%, 100% {\n    background-position: 0% 50%;\n    text-shadow: 0 0 10px rgba(0, 212, 255, 0.3), 0 0 20px rgba(121, 40, 202, 0.2);\n  }\n  25% {\n    background-position: 25% 25%;\n    text-shadow: 0 0 15px rgba(0, 153, 255, 0.4), 0 0 25px rgba(255, 0, 128, 0.3);\n  }\n  50% {\n    background-position: 100% 50%;\n    text-shadow: 0 0 20px rgba(121, 40, 202, 0.5), 0 0 30px rgba(0, 212, 255, 0.4);\n  }\n  75% {\n    background-position: 75% 75%;\n    text-shadow: 0 0 15px rgba(255, 0, 128, 0.4), 0 0 25px rgba(0, 212, 255, 0.3);\n  }\n}\n\n.sidebar-collapse-btn {\n  color: #64748b !important;\n  border: 1px solid rgba(59, 130, 246, 0.2) !important;\n  border-radius: 6px !important;\n  width: 32px !important;\n  height: 32px !important;\n  padding: 0 !important;\n  display: flex !important;\n  align-items: center !important;\n  justify-content: center !important;\n  transition: all 0.3s ease !important;\n}\n\n.sidebar-collapse-btn:hover {\n  color: #3b82f6 !important;\n  border-color: rgba(59, 130, 246, 0.4) !important;\n  background: rgba(59, 130, 246, 0.05) !important;\n}\n\n.sidebar-project-section {\n  padding: 16px 20px;\n  border-bottom: 1px solid rgba(59, 130, 246, 0.1);\n  flex-shrink: 0; /* 确保项目选择区域不会被压缩 */\n}\n\n.sidebar-project-title {\n  color: #64748b;\n  font-size: 12px;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  margin-bottom: 8px;\n}\n\n.sidebar-conversations-section {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  min-height: 0;\n  overflow: hidden;\n}\n\n/* 对话容器样式 */\n.sidebar-conversations-container {\n  display: flex;\n  flex-direction: column;\n  height: calc(100vh - 240px); /* 减去头部、项目选择和底部区域的高度 */\n}\n\n/* 对话标题区域样式 */\n.sidebar-conversations-header {\n  padding: 12px 20px;\n  border-bottom: 1px solid rgba(59, 130, 246, 0.1);\n  flex-shrink: 0;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.new-conversation-btn {\n  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;\n  border: none !important;\n  border-radius: 50% !important;\n  font-weight: 500 !important;\n  width: 24px !important;\n  height: 24px !important;\n  padding: 0 !important;\n  font-size: 12px !important;\n  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1) !important;\n  transition: all 0.2s ease !important;\n  color: white !important;\n  display: flex !important;\n  align-items: center !important;\n  justify-content: center !important;\n}\n\n.new-conversation-btn:hover {\n  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%) !important;\n  transform: translateY(-1px) !important;\n  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.2) !important;\n  color: white !important;\n}\n\n.sidebar-conversations-list {\n  flex: 1;\n  overflow-y: auto;\n  overflow-x: hidden;\n  padding: 16px 20px 30px 20px; /* 底部增加padding，为管理工具留空间 */\n  min-height: 0;\n  max-height: calc(100vh - 300px); /* 确保不会超出可用空间 */\n}\n\n/* 自定义滚动条 */\n.sidebar-conversations-list::-webkit-scrollbar {\n  width: 6px;\n}\n\n.sidebar-conversations-list::-webkit-scrollbar-track {\n  background: rgba(59, 130, 246, 0.05);\n  border-radius: 3px;\n}\n\n.sidebar-conversations-list::-webkit-scrollbar-thumb {\n  background: rgba(59, 130, 246, 0.3);\n  border-radius: 3px;\n}\n\n.sidebar-conversations-list::-webkit-scrollbar-thumb:hover {\n  background: rgba(59, 130, 246, 0.5);\n}\n\n.conversation-item {\n  display: flex;\n  align-items: center;\n  margin-bottom: 6px;\n  border-radius: 8px;\n  background: rgba(59, 130, 246, 0.02);\n  border: 1px solid rgba(59, 130, 246, 0.1);\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.conversation-item:hover {\n  background: rgba(59, 130, 246, 0.08);\n  border-color: rgba(59, 130, 246, 0.2);\n  transform: translateY(-1px);\n}\n\n.conversation-item.active {\n  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);\n  border-color: #3b82f6;\n  color: white;\n}\n\n.conversation-item.active:hover {\n  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);\n}\n\n.conversation-content {\n  flex: 1;\n  padding: 12px 16px;\n  cursor: pointer;\n  min-width: 0; /* 确保可以收缩 */\n}\n\n.conversation-title {\n  font-size: 14px;\n  font-weight: 500;\n  line-height: 1.4;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.conversation-actions {\n  padding: 8px;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.conversation-item:hover .conversation-actions {\n  opacity: 1;\n}\n\n.conversation-delete-btn {\n  color: #ef4444 !important;\n  border: none !important;\n  background: transparent !important;\n  width: 24px !important;\n  height: 24px !important;\n  padding: 0 !important;\n  display: flex !important;\n  align-items: center !important;\n  justify-content: center !important;\n  border-radius: 4px !important;\n  transition: all 0.3s ease !important;\n}\n\n.conversation-delete-btn:hover {\n  background: rgba(239, 68, 68, 0.1) !important;\n  color: #dc2626 !important;\n}\n\n.conversation-item.active .conversation-delete-btn {\n  color: rgba(255, 255, 255, 0.8) !important;\n}\n\n.conversation-item.active .conversation-delete-btn:hover {\n  color: white !important;\n  background: rgba(255, 255, 255, 0.2) !important;\n}\n\n/* 加载更多按钮样式 */\n.load-more-container {\n  padding: 12px 0;\n  border-top: 1px solid rgba(59, 130, 246, 0.1);\n  margin-top: 8px;\n}\n\n.load-more-btn {\n  color: #64748b !important;\n  font-size: 13px !important;\n  height: 32px !important;\n  border-radius: 6px !important;\n  transition: all 0.2s ease !important;\n  border: 1px solid rgba(59, 130, 246, 0.1) !important;\n  background: transparent !important;\n}\n\n.load-more-btn:hover {\n  color: #3b82f6 !important;\n  background: rgba(59, 130, 246, 0.05) !important;\n  border-color: rgba(59, 130, 246, 0.2) !important;\n}\n\n.sidebar-section-title {\n  color: #64748b;\n  font-size: 14px;\n  font-weight: 600;\n  letter-spacing: 0.3px;\n  margin-bottom: 0;\n  display: flex;\n  align-items: center;\n}\n\n.sidebar-project-select .ant-select-selector {\n  border: 1px solid rgba(59, 130, 246, 0.2) !important;\n  border-radius: 8px !important;\n  background: rgba(255, 255, 255, 0.8) !important;\n  transition: all 0.3s ease !important;\n}\n\n.sidebar-project-select .ant-select-selector:hover {\n  border-color: rgba(59, 130, 246, 0.4) !important;\n  background: rgba(255, 255, 255, 0.95) !important;\n}\n\n.sidebar-project-select.ant-select-focused .ant-select-selector {\n  border-color: #3b82f6 !important;\n  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;\n}\n\n.sidebar-bottom {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  padding: 16px 20px 18px 20px;\n  border-top: 1px solid rgba(59, 130, 246, 0.12);\n  background: linear-gradient(180deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.98));\n  backdrop-filter: blur(12px);\n  z-index: 10;\n  box-shadow: 0 -4px 16px rgba(59, 130, 246, 0.06);\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.sidebar-bottom-actions {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0 4px;\n}\n\n.sidebar-icon-btn {\n  width: 38px !important;\n  height: 38px !important;\n  border-radius: 11px !important;\n  display: flex !important;\n  align-items: center !important;\n  justify-content: center !important;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;\n  font-size: 16px !important;\n  border: 1px solid transparent !important;\n  position: relative !important;\n  overflow: hidden !important;\n}\n\n.sidebar-icon-btn::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  border-radius: 11px;\n  padding: 1px;\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));\n  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);\n  -webkit-mask-composite: exclude;\n  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);\n  mask-composite: exclude;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n/* 管理功能按钮样式 */\n.sidebar-management-btn {\n  color: #64748b !important;\n  background: linear-gradient(135deg, rgba(100, 116, 139, 0.08), rgba(100, 116, 139, 0.04)) !important;\n  box-shadow: \n    0 2px 6px rgba(100, 116, 139, 0.1),\n    inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;\n  border: 1px solid rgba(100, 116, 139, 0.15) !important;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;\n}\n\n.sidebar-management-btn:hover {\n  background: linear-gradient(135deg, rgba(100, 116, 139, 0.12), rgba(100, 116, 139, 0.08)) !important;\n  color: #475569 !important;\n  transform: translateY(-1px) scale(1.02) !important;\n  box-shadow: \n    0 4px 12px rgba(100, 116, 139, 0.2),\n    inset 0 1px 0 rgba(255, 255, 255, 0.25) !important;\n  border-color: rgba(100, 116, 139, 0.25) !important;\n}\n\n.sidebar-management-btn:hover::before {\n  opacity: 1;\n}\n\n/* 选中状态样式 */\n.sidebar-management-btn.active {\n  color: #3b82f6 !important;\n  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(59, 130, 246, 0.1)) !important;\n  box-shadow: \n    0 3px 12px rgba(59, 130, 246, 0.25),\n    inset 0 1px 0 rgba(255, 255, 255, 0.3),\n    inset 0 0 0 1px rgba(59, 130, 246, 0.2) !important;\n  border: 1px solid rgba(59, 130, 246, 0.3) !important;\n  transform: translateY(-1px) !important;\n}\n\n.sidebar-management-btn.active:hover {\n  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(59, 130, 246, 0.15)) !important;\n  color: #1d4ed8 !important;\n  transform: translateY(-1px) scale(1.02) !important;\n  box-shadow: \n    0 4px 16px rgba(59, 130, 246, 0.3),\n    inset 0 1px 0 rgba(255, 255, 255, 0.4),\n    inset 0 0 0 1px rgba(59, 130, 246, 0.3) !important;\n  border-color: rgba(59, 130, 246, 0.4) !important;\n}\n\n.sidebar-logout-btn {\n  color: #64748b !important;\n  background: linear-gradient(135deg, rgba(100, 116, 139, 0.12), rgba(100, 116, 139, 0.08)) !important;\n  box-shadow: \n    0 2px 8px rgba(100, 116, 139, 0.15),\n    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;\n  border: 1px solid rgba(100, 116, 139, 0.2) !important;\n}\n\n.sidebar-logout-btn:hover {\n  background: linear-gradient(135deg, rgba(100, 116, 139, 0.18), rgba(100, 116, 139, 0.12)) !important;\n  color: #475569 !important;\n  transform: translateY(-1px) scale(1.02) !important;\n  box-shadow: \n    0 4px 16px rgba(100, 116, 139, 0.25),\n    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;\n  border-color: rgba(100, 116, 139, 0.3) !important;\n}\n\n.sidebar-logout-btn:hover::before {\n  opacity: 1;\n}\n\n/* 管理功能标签区域 */\n.sidebar-management-labels {\n  padding: 0px 4px 0 4px;\n  margin-top: -4px;\n}\n\n.management-label-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0;\n}\n\n.management-label {\n  font-size: 10px;\n  color: #94a3b8;\n  font-weight: 500;\n  text-align: center;\n  width: 38px;\n  height: 14px;\n  line-height: 14px;\n  transition: all 0.3s ease;\n  letter-spacing: 0.3px;\n}\n\n.management-label.active {\n  color: #3b82f6;\n  font-weight: 600;\n}\n\n.management-label.logout-label {\n  color: #94a3b8;\n}\n\n\n\n.app-main-layout {\n  background: #ffffff;\n}\n\n.app-content {\n  background: #ffffff;\n  height: calc(100vh - 64px);\n  overflow: auto;\n  padding: 24px;\n  box-sizing: border-box;\n}\n\n/* 侧边栏折叠状态样式 */\n.app-sidebar.ant-layout-sider-collapsed .sidebar-header {\n  padding: 16px 12px;\n  justify-content: center;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.app-sidebar.ant-layout-sider-collapsed .sidebar-header-left {\n  flex-direction: column;\n  gap: 4px;\n}\n\n.app-sidebar.ant-layout-sider-collapsed .sidebar-project-section,\n.app-sidebar.ant-layout-sider-collapsed .sidebar-conversations-section,\n.app-sidebar.ant-layout-sider-collapsed .sidebar-section-title {\n  display: none;\n}\n\n.app-sidebar.ant-layout-sider-collapsed .sidebar-bottom {\n  padding: 16px 8px 18px 8px;\n  left: 0;\n  right: 0;\n  width: 80px; /* 折叠状态下的宽度 */\n}\n\n.app-sidebar.ant-layout-sider-collapsed .sidebar-bottom-actions {\n  flex-direction: column;\n  justify-content: center;\n  gap: 12px;\n  padding: 4px 0;\n}\n\n.app-sidebar.ant-layout-sider-collapsed .sidebar-management-labels {\n  display: none;\n}\n\n.app-sidebar.ant-layout-sider-collapsed .sidebar-icon-btn {\n  width: 34px !important;\n  height: 34px !important;\n  font-size: 15px !important;\n  border-radius: 9px !important;\n}\n\n.app-sidebar.ant-layout-sider-collapsed .sidebar-icon-btn::before {\n  border-radius: 9px;\n}\n\n.page-header {\n  margin-bottom: 20px;\n}\n\n.action-buttons {\n  margin-bottom: 20px;\n}\n\n.card-list {\n  margin-bottom: 20px;\n}\n\n.tool-form-item {\n  margin-bottom: 20px;\n}\n\n.code-editor {\n  border: 1px solid #d9d9d9;\n  border-radius: 2px;\n  min-height: 200px;\n}\n\n.analysis-result {\n  margin-top: 20px;\n  white-space: pre-wrap;\n}\n\n.loading-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100%;\n  width: 100%;\n}\n\n.dashboard-cards {\n  margin-bottom: 24px;\n}\n\n.dashboard-card {\n  cursor: pointer;\n}\n\n.dashboard-stat {\n  font-size: 24px;\n  font-weight: bold;\n}\n\n/* 顶部导航栏样式 */\n.app-header {\n  background: #fff;\n  border-bottom: 1px solid #f0f0f0;\n  padding: 0 24px;\n  height: 64px;\n  line-height: 64px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n}\n\n.app-header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  height: 100%;\n}\n\n.app-header-left {\n  flex: 1;\n}\n\n.app-header-right {\n  display: flex;\n  align-items: center;\n}\n\n.current-user {\n  color: #666;\n  font-size: 14px;\n  display: flex;\n  align-items: center;\n  padding: 0 8px;\n}\n\n.logout-btn {\n  color: #666;\n  transition: all 0.3s;\n}\n\n.logout-btn:hover {\n  color: #1890ff;\n}\n\n/* 自定义退出确认弹框样式 */\n.custom-logout-modal {\n  top: 0 !important;\n  padding-bottom: 0 !important;\n}\n\n.custom-logout-modal .ant-modal-wrap {\n  display: flex !important;\n  align-items: center !important;\n  justify-content: center !important;\n  min-height: 100vh !important;\n}\n\n.custom-logout-modal .ant-modal {\n  top: 0 !important;\n  margin: 0 !important;\n  padding-bottom: 0 !important;\n}\n\n.custom-logout-modal .ant-modal-content {\n  border-radius: 16px !important;\n  overflow: hidden !important;\n  box-shadow: 0 20px 40px rgba(59, 130, 246, 0.15) !important;\n  border: 1px solid rgba(59, 130, 246, 0.1) !important;\n  margin: 0 auto !important;\n}\n\n.custom-logout-modal .ant-modal-header {\n  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(59, 130, 246, 0.02)) !important;\n  border-bottom: 1px solid rgba(59, 130, 246, 0.1) !important;\n  padding: 24px 24px 20px 24px !important;\n  text-align: center !important;\n}\n\n.custom-logout-modal .ant-modal-title {\n  color: #1e293b !important;\n  font-size: 18px !important;\n  font-weight: 600 !important;\n  text-align: center !important;\n  margin: 0 !important;\n}\n\n.custom-logout-modal .ant-modal-body {\n  padding: 24px !important;\n  background: #ffffff !important;\n  text-align: center !important;\n}\n\n.custom-logout-modal .ant-modal-body .ant-modal-confirm-body {\n  display: flex !important;\n  flex-direction: column !important;\n  align-items: center !important;\n  gap: 20px !important;\n  padding: 8px 0 !important;\n}\n\n.custom-logout-modal .ant-modal-body .ant-modal-confirm-body .anticon {\n  font-size: 32px !important;\n  color: #3b82f6 !important;\n  margin-bottom: 12px !important;\n  padding: 16px !important;\n  background: rgba(59, 130, 246, 0.1) !important;\n  border-radius: 50% !important;\n  width: 64px !important;\n  height: 64px !important;\n  display: flex !important;\n  align-items: center !important;\n  justify-content: center !important;\n}\n\n.custom-logout-modal .ant-modal-body .ant-modal-confirm-content {\n  color: #64748b !important;\n  font-size: 15px !important;\n  line-height: 1.6 !important;\n  margin: 0 !important;\n  text-align: center !important;\n}\n\n.custom-logout-modal .ant-modal-footer {\n  background: rgba(248, 250, 252, 0.8) !important;\n  border-top: 1px solid rgba(59, 130, 246, 0.08) !important;\n  padding: 20px 24px 24px 24px !important;\n  display: flex !important;\n  justify-content: center !important;\n  align-items: center !important;\n  gap: 16px !important;\n  text-align: center !important;\n}\n\n.custom-logout-modal .ant-modal-confirm-btns {\n  display: flex !important;\n  justify-content: center !important;\n  align-items: center !important;\n  gap: 16px !important;\n  margin: 0 !important;\n  flex-direction: row-reverse !important;\n}\n\n.custom-logout-modal .ant-btn {\n  height: 42px !important;\n  border-radius: 10px !important;\n  font-weight: 500 !important;\n  font-size: 14px !important;\n  padding: 0 28px !important;\n  min-width: 110px !important;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;\n  border: none !important;\n  flex-shrink: 0 !important;\n}\n\n.custom-logout-modal .ant-btn-default {\n  background: rgba(100, 116, 139, 0.08) !important;\n  color: #64748b !important;\n  box-shadow: 0 2px 4px rgba(100, 116, 139, 0.1) !important;\n}\n\n.custom-logout-modal .ant-btn-default:hover {\n  background: rgba(100, 116, 139, 0.12) !important;\n  color: #475569 !important;\n  transform: translateY(-1px) !important;\n  box-shadow: 0 4px 12px rgba(100, 116, 139, 0.15) !important;\n}\n\n.custom-logout-modal .ant-btn-primary {\n  background: linear-gradient(135deg, #3b82f6, #2563eb) !important;\n  color: #ffffff !important;\n  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3) !important;\n}\n\n.custom-logout-modal .ant-btn-primary:hover {\n  background: linear-gradient(135deg, #2563eb, #1d4ed8) !important;\n  transform: translateY(-1px) !important;\n  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4) !important;\n}\n\n.custom-logout-modal .ant-modal-close {\n  top: 16px !important;\n  right: 16px !important;\n}\n\n.custom-logout-modal .ant-modal-close-x {\n  width: 32px !important;\n  height: 32px !important;\n  line-height: 32px !important;\n  color: #94a3b8 !important;\n  font-size: 16px !important;\n  border-radius: 8px !important;\n  transition: all 0.3s ease !important;\n}\n\n.custom-logout-modal .ant-modal-close-x:hover {\n  background: rgba(100, 116, 139, 0.1) !important;\n  color: #64748b !important;\n}\n\n/* 丝滑动画效果 - 用于对话列表刷新 */\n@keyframes slideInFromTop {\n  0% {\n    opacity: 0;\n    transform: translateY(-10px);\n  }\n  100% {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes fadeIn {\n  0% {\n    opacity: 0;\n  }\n  100% {\n    opacity: 1;\n  }\n}\n\n/* 优化对话列表项的过渡效果 */\n.conversation-item {\n  transition: all 0.2s ease-in-out !important;\n}\n\n.conversation-item:hover {\n  transform: translateX(2px) !important;\n} "], "names": [], "sourceRoot": ""}