{"name": "ai-data-qa-frontend", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/icons": "^5.0.1", "@codemirror/lang-sql": "^6.5.4", "@codemirror/language": "^6.9.1", "@codemirror/legacy-modes": "^6.3.3", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.4", "@mui/material": "^7.0.2", "@uiw/react-codemirror": "^4.21.9", "antd": "^5.10.0", "axios": "^1.5.1", "dayjs": "^1.11.10", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "i18next": "^23.16.8", "i18next-browser-languagedetector": "^8.2.0", "lodash": "^4.17.21", "moment": "^2.29.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^14.1.3", "react-json-view-lite": "^1.0.0", "react-markdown": "^8.0.7", "react-router-dom": "^6.16.0", "react-scripts": "5.0.1", "rehype-raw": "^6.1.1", "remark-gfm": "^3.0.1", "typescript": "^4.9.5"}, "devDependencies": {"@types/node": "^16.18.46", "@types/react": "^18.3.22", "@types/react-dom": "^18.3.7", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "eslint": "^8.49.0", "eslint-config-prettier": "^8.10.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.33.2", "prettier": "^2.8.8"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint 'src/**/*.{js,jsx,ts,tsx}'", "lint:fix": "eslint 'src/**/*.{js,jsx,ts,tsx}' --fix", "dev": "vite", "preview": "vite preview"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}