import uvicorn
from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from sqlalchemy.exc import SQLAlchemyError

from app.api.v1.router import router as api_v1_router
from app.api.admin_router import router as admin_api_router
from app.core.config import settings
from app.core.exceptions import (BaseException, base_exception_handler,
                               sqlalchemy_exception_handler,
                               unhandled_exception_handler,
                               validation_exception_handler)
from app.core.middleware.auth import setup_auth_middleware
from app.core.middleware.cors import setup_cors_middleware
from app.core.middleware.logging import setup_logging_middleware
from app.services.system_tools import SystemTools
from app.utils.oracle_init import init_oracle_client
import logging

# 配置日志
logger = logging.getLogger(__name__)


def create_app() -> FastAPI:
    """
    创建FastAPI应用
    """
    # 创建应用
    app = FastAPI(
        title=settings.APP_NAME,
        description="FastAPI Base Project API",
        version="0.1.0",
        docs_url="/docs",
        redoc_url="/redoc",
        openapi_url="/openapi.json",
    )
    
    # 注册异常处理器
    app.add_exception_handler(BaseException, base_exception_handler)
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(SQLAlchemyError, sqlalchemy_exception_handler)
    app.add_exception_handler(Exception, unhandled_exception_handler)
    
    # 注册路由
    app.include_router(api_v1_router, prefix="/api/v1")
    
    # 注册管理员独立路由
    app.include_router(admin_api_router, prefix="/admin-api")
    
    # 注册中间件 - 从内到外的顺序注册（FastAPI按相反顺序执行）
    # 最内层：日志中间件，记录所有请求
    setup_logging_middleware(app)

    # 认证中间件，验证JWT token
    setup_auth_middleware(app)

    # CORS中间件，必须在认证中间件之前，处理预检请求
    setup_cors_middleware(app)

    # 最外层：安全中间件
    app.add_middleware(TrustedHostMiddleware, allowed_hosts=["*"])
    
    # 应用启动事件
    @app.on_event("startup")
    def startup():
        # 初始化Oracle客户端(全局只需初始化一次)
        try:
            if init_oracle_client():
                logger.info("应用启动时成功初始化Oracle客户端")
            else:
                logger.warning("应用启动时Oracle客户端初始化失败，连接Oracle数据库可能会出错")
        except Exception as e:
            logger.error(f"初始化Oracle客户端时发生错误: {str(e)}")
        
        # 注册系统内置工具
        SystemTools.register_system_tools()

        logger.info("应用启动完成")
    
    return app


app = create_app()


if __name__ == "__main__":
    """
    开发模式启动应用
    生产环境应该使用 uvicorn main:app --host 0.0.0.0 --port 8000
    """
    uvicorn.run(
        "main:app",
        host=settings.APP_HOST,
        port=settings.APP_PORT,
        workers=4
    )
