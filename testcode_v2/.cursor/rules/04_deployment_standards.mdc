---
description: 
globs: 
alwaysApply: false
---
# FastAPI项目部署规范

## Docker规范

项目支持Docker容器化部署，使用多阶段构建优化镜像大小。

### Dockerfile规范

- 使用官方Python基础镜像
- 合理组织Dockerfile指令，减少层数
- 使用.dockerignore排除不必要文件
- 非root用户运行应用

### Docker Compose规范

- 使用docker-compose.yml定义服务组件
- 为不同环境提供不同的配置文件
- 服务之间使用网络隔离
- 使用volumes持久化数据

## 环境配置规范

### 环境变量规范

- 使用.env文件管理环境变量
- 敏感信息不应提交到代码仓库
- 提供.env.example作为示例
- 区分开发、测试和生产环境配置

## 日志配置规范

### 日志文件规范

- 日志应写入磁盘以便问题排查
- 日志应按日期滚动生成新文件
- 应区分不同类型的日志（应用日志和SQL日志）
- 日志目录权限应正确设置

## 性能监控规范

### 性能监控配置

- 应添加健康检查端点
- 应收集关键指标如请求数、响应时间等
- 考虑使用APM工具如Prometheus、Grafana等

## 安全配置规范

### 安全配置要点

- 启用HTTPS
- 设置合适的CORS策略
- 添加安全相关的HTTP头
- 实施速率限制防止滥用
