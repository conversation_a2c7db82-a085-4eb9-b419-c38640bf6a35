---
description: 
globs: 
alwaysApply: false
---
# FastAPI项目框架特性

## 日志系统特性

项目使用自定义日志系统，支持按日期滚动、不同的日志类型、统一的请求ID跟踪等功能。

### 日志级别使用规范

- `log.debug`: 开发调试信息，生产环境通常不输出
- `log.info`: 常规操作信息，如请求处理、业务操作等
- `log.warning`: 可能的问题预警，但不影响系统正常运行
- `log.error`: 运行时错误，影响到当前请求
- `log.critical`: 严重错误，可能影响系统稳定性

## 异常处理特性

项目统一的异常处理机制能够捕获各类异常，并返回一致的响应格式。

### 异常处理规范

- 业务异常应继承自`BaseException`
- 应使用合适的异常类型，如`NotFoundException`、`BadRequestException`等
- API操作应通过异常传递错误，而不是直接返回错误响应

## 响应格式特性

项目定义了统一的响应格式，包含状态码、消息、数据和请求ID。

### 响应格式规范

- 响应应使用`app/schemas/base.py`中定义的基础响应模型
- 成功响应应包含正确的数据和状态码
- 错误响应应包含明确的错误消息和状态码

## 数据库特性

项目支持MySQL数据库，使用SQLAlchemy ORM，并提供连接池管理。

### 数据库操作规范

- 使用SQLAlchemy ORM进行数据库操作，避免原生SQL
- 数据库连接应通过连接池管理
- 事务应正确提交或回滚

## 缓存特性

项目支持Redis缓存，提供了简便的API进行缓存操作。

### 缓存使用规范

- 频繁查询的数据应考虑使用缓存
- 缓存应设置合理的过期时间
- 更新数据时应同步更新或清除相关缓存

 