---
description: 
globs: 
alwaysApply: false
---
# FastAPI项目框架规范

## 项目结构规范

项目应遵循清晰的目录结构，各模块功能明确，便于维护和扩展。

```
fastapi-base-project/
├── app/                           # 主应用目录
│   ├── api/                       # API路由
│   │   ├── v1/                    # API版本1
│   │   │   ├── endpoints/         # 各个端点
│   │   │   └── router.py          # 路由聚合
│   ├── core/                      # 核心功能模块
│   ├── db/                        # 数据库相关
│   ├── models/                    # 数据模型
│   ├── schemas/                   # Pydantic模型
│   ├── services/                  # 业务逻辑层
│   └── utils/                     # 工具函数
```

## 配置管理规范

- 所有配置项应集中在`app/core/config.py`文件中
- 应使用`.env`文件管理环境变量，不同环境可以使用`.env.{环境名}`文件

## 依赖注入规范

- 应使用FastAPI的依赖注入系统进行服务和资源的注入
- 数据库会话应通过依赖函数获取，不应在路由函数中直接创建

## 路由组织规范

- API版本应清晰隔离在不同的模块中
- 路由应按领域/功能组织在`endpoints`目录下
- 路由注册应在`router.py`文件中集中管理

## 中间件规范

- 中间件应保持轻量，不应包含复杂业务逻辑
- 全局中间件应在应用启动时统一注册
- 中间件执行顺序应合理安排
