---
description: 
globs: 
alwaysApply: false
---
# FastAPI项目代码规范

## 代码风格

项目应遵循PEP 8代码风格指南，保持一致的编码风格。

### 命名规范

- **类名**：使用帕斯卡命名法（PascalCase），如`UserService`
- **函数和变量**：使用蛇形命名法（snake_case），如`get_user_by_id`
- **常量**：全大写加下划线，如`MAX_CONNECTIONS`
- **私有变量/方法**：以下划线开头，如`_private_method`

### 格式规范

- 行长度不超过120个字符
- 使用4个空格进行缩进
- 函数和类定义之间空两行，方法定义之间空一行

### 注释和文档

- 所有公共函数、类和模块应有文档字符串
- 文档字符串应说明功能、参数、返回值和异常
- 复杂的代码段应添加注释说明

## 代码结构

### 函数和类结构

- 函数应遵循单一职责原则，只做一件事
- 函数不宜过长，应拆分为多个小函数
- 嵌套不应过深，影响可读性

## 类型标注

项目应使用Python类型标注（Type Hints）增强代码可读性和IDE支持。

### 类型标注规范

- 函数参数和返回值应有类型标注
- 使用`typing`模块的类型，如`List`, `Dict`, `Optional`等
- 复杂类型应使用`TypeVar`和`Generic`

## 安全编码

### 安全编码规范

- 避免SQL注入风险
- 敏感信息不应硬编码
- API应验证输入参数
- 密钥和敏感配置应从安全存储获取

## 性能优化

### 性能优化规范

- 对于频繁请求的API应使用缓存
- 避免N+1查询问题
- 大批量数据应分页处理
- 耗时操作应考虑异步处理
