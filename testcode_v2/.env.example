# 应用环境配置
APP_ENV=dev
DEBUG=true

# 服务配置
APP_NAME=DecisionAI 2.0 Beta
APP_HOST=0.0.0.0
APP_PORT=8000

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=password
DB_NAME=ai_data_qa
DB_POOL_SIZE=5
DB_MAX_OVERFLOW=10
DB_ECHO=true

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# LLM配置
OPENAI_API_KEY=your-api-key
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-3.5-turbo

# 日志配置
LOG_LEVEL=INFO
LOG_DIR=./logs
LOG_APP_DIR=app
LOG_SQL_DIR=sql
LOG_PRINT_SQL=true
LOG_JSON_FORMAT=true

# JWT配置
JWT_SECRET_KEY=your-secret-key
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
