#!/usr/bin/env python3
"""
管理员账户初始化脚本
用于创建第一个管理员账户
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from app.db.session import get_db
from app.services.administrator import create_admin, get_admin_by_username
from app.core.security import pwd_context


def create_default_admin():
    """创建默认管理员账户"""
    db = next(get_db())
    
    try:
        # 检查是否已经存在管理员
        existing_admin = get_admin_by_username(db, "admin")
        if existing_admin:
            print("管理员账户 'admin' 已存在")
            return
        
        # 创建默认管理员
        admin = create_admin(
            db=db,
            username="admin",
            password="2Olr3cV@#($EmR9",  # 默认密码，部署时应修改
            email="<EMAIL>"
        )
        
        print(f"管理员账户创建成功:")
        print(f"用户名: {admin.username}")
        print(f"密码: admin123")
        print(f"邮箱: {admin.email}")
        print(f"请及时修改默认密码！")
        
    except Exception as e:
        print(f"创建管理员账户失败: {e}")
        db.rollback()
    finally:
        db.close()


if __name__ == "__main__":
    print("正在创建默认管理员账户...")
    create_default_admin()
    print("完成！") 