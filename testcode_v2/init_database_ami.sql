-- 创建数据库
CREATE DATABASE IF NOT EXISTS ai_data_qa1;

USE ai_data_qa1;

-- 用户表
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `hashed_password` varchar(100) NOT NULL COMMENT '密码哈希',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `is_superuser` tinyint(1) DEFAULT 0 COMMENT '是否超级用户',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_username` (`username`),
  UNIQUE KEY `idx_email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户表';

-- 插入默认管理员账号
BEGIN;
INSERT INTO `ai_data_qa1`.`users` (`id`, `username`, `email`, `hashed_password`, `is_active`, `is_superuser`, `created_at`, `updated_at`) VALUES (1, 'decisionadmin', '<EMAIL>', '$2b$12$X9YTVAd0KcBaBA.GFcTbkuwtL8AjyiWqqs0Rb4J//E5mH12d/Rg62', 1, 1, '2025-05-23 06:35:53', '2025-05-23 16:11:03');
COMMIT;

-- 项目表
CREATE TABLE `projects` (
  `id` varchar(36) NOT NULL,
  `name` varchar(100) NOT NULL COMMENT '项目名称',
  `description` text COMMENT '项目描述',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_project_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='项目表';


-- 数据源表
CREATE TABLE `data_sources` (
  `id` varchar(36) NOT NULL,
  `name` varchar(100) NOT NULL COMMENT '数据源名称',
  `description` text COMMENT '数据源描述',
  `type` enum('oracle','mysql','postgresql','mssql','http_api','other') NOT NULL COMMENT '数据源类型',
  `config` json NOT NULL COMMENT '数据源配置',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `project_id` varchar(36) NOT NULL COMMENT '所属项目ID',
  PRIMARY KEY (`id`),
  KEY `idx_datasource_name` (`name`),
  KEY `idx_datasource_project` (`project_id`),
  CONSTRAINT `data_sources_ibfk_1` FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='数据源表';

-- ----------------------------
-- Records of data_sources
-- ----------------------------


-- 工具表
CREATE TABLE `tools` (
  `id` varchar(36) NOT NULL,
  `name` varchar(100) NOT NULL COMMENT '工具名称',
  `description` text COMMENT '工具描述',
  `tool_type` enum('sql','api','python','graph','vector','custom') NOT NULL COMMENT '工具类型',
  `template` text NOT NULL COMMENT '工具模板',
  `parameters` json NOT NULL COMMENT '工具参数定义',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `data_source_id` varchar(36) NOT NULL COMMENT '所属数据源ID',
  PRIMARY KEY (`id`),
  KEY `idx_tool_name` (`name`),
  KEY `idx_tool_datasource` (`data_source_id`),
  CONSTRAINT `tools_ibfk_1` FOREIGN KEY (`data_source_id`) REFERENCES `data_sources` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='工具表';

-- ----------------------------
-- Records of tools
-- ----------------------------


-- 分析记录表
CREATE TABLE `analyses` (
  `id` varchar(36) NOT NULL,
  `query` text NOT NULL COMMENT '用户查询',
  `intent_analysis` json DEFAULT NULL COMMENT '意图分析结果',
  `result` text COMMENT '分析报告',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `project_id` varchar(36) NOT NULL COMMENT '所属项目ID',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_analysis_project` (`project_id`),
  KEY `idx_analysis_created` (`created_at`),
  CONSTRAINT `analyses_ibfk_1` FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='分析记录表';

-- 工具执行记录表
CREATE TABLE `tool_executions` (
  `id` varchar(36) NOT NULL,
  `parameters` json NOT NULL COMMENT '执行参数',
  `result` json DEFAULT NULL COMMENT '执行结果',
  `execution_time` float DEFAULT NULL COMMENT '执行时间(秒)',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `analysis_id` varchar(36) NOT NULL COMMENT '所属分析ID',
  `tool_id` varchar(36) NOT NULL COMMENT '工具ID',
  `step_id` varchar(50) DEFAULT NULL COMMENT '分析步骤ID',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_execution_analysis` (`analysis_id`),
  KEY `idx_execution_tool` (`tool_id`),
  CONSTRAINT `tool_executions_ibfk_1` FOREIGN KEY (`analysis_id`) REFERENCES `analyses` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='工具执行记录表';

-- 选定分析表信息
CREATE TABLE `selected_tables` (
  `id` varchar(36) NOT NULL,
  `data_source_id` varchar(36) NOT NULL COMMENT '所属数据源ID',
  `table_name` varchar(100) NOT NULL COMMENT '表名',
  `table_description` text COMMENT '表描述',
  `table_schema` json NOT NULL COMMENT '表结构信息',
  `sample_data` json NOT NULL COMMENT '样例数据(前3条)',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_datasource_table` (`data_source_id`,`table_name`),
  CONSTRAINT `selected_tables_ibfk_1` FOREIGN KEY (`data_source_id`) REFERENCES `data_sources` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='选定分析表信息';


-- LLM模型配置表
CREATE TABLE `llm_models` (
  `id` varchar(36) NOT NULL,
  `name` varchar(100) NOT NULL COMMENT '模型名称',
  `description` text COMMENT '模型描述',
  `model_name` varchar(100) NOT NULL COMMENT '模型标识符',
  `api_key` varchar(500) NOT NULL COMMENT 'API密钥',
  `base_url` varchar(500) DEFAULT 'https://api.openai.com/v1' COMMENT 'API基础URL',
  `config` json DEFAULT NULL COMMENT '额外配置参数',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_llm_model_name` (`name`),
  KEY `idx_llm_model_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='LLM模型配置表';

-- 插入示例模型配置（需要用户自行配置真实的API密钥）

-- 会话表（多轮分析）
CREATE TABLE `conversations` (
  `id` varchar(50) NOT NULL,
  `project_id` varchar(36) NOT NULL,
  `title` varchar(500) DEFAULT NULL COMMENT '会话标题',
  `context_summary` text COMMENT '会话级别的上下文摘要',
  `total_rounds` int DEFAULT 0 COMMENT '总轮次数',
  `total_tokens_used` int DEFAULT 0 COMMENT '总消耗token数',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `status` enum('ACTIVE','ARCHIVED','PAUSED') DEFAULT 'ACTIVE' COMMENT '会话状态',
  PRIMARY KEY (`id`),
  KEY `idx_project_id` (`project_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `conversations_ibfk_1` FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='多轮分析会话表';

-- 会话上下文表（用于存储结构化的上下文信息）
CREATE TABLE `conversation_contexts` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `conversation_id` varchar(50) NOT NULL,
  `round_number` int NOT NULL,
  `context_type` enum('query','result','insight','data_source','tool_usage') NOT NULL COMMENT '上下文类型',
  `context_data` json NOT NULL COMMENT '上下文数据',
  `relevance_score` float DEFAULT '1' COMMENT '上下文相关性评分',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_conversation_round` (`conversation_id`,`round_number`),
  KEY `idx_context_type` (`context_type`),
  KEY `idx_relevance_score` (`relevance_score`),
  CONSTRAINT `conversation_contexts_ibfk_1` FOREIGN KEY (`conversation_id`) REFERENCES `conversations` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=367 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='会话上下文表';

-- 为分析表添加会话相关字段
ALTER TABLE `analyses` ADD COLUMN `conversation_id` varchar(50) DEFAULT NULL COMMENT '所属会话ID';
ALTER TABLE `analyses` ADD COLUMN `round_number` int DEFAULT 1 COMMENT '轮次编号';
ALTER TABLE `analyses` ADD COLUMN `context_tokens_used` int DEFAULT 0 COMMENT '上下文消耗的token数';
ALTER TABLE `analyses` ADD COLUMN `context_summary` text COMMENT '本轮次使用的上下文摘要';
ALTER TABLE `analyses` ADD KEY `idx_conversation_id` (`conversation_id`);
ALTER TABLE `analyses` ADD KEY `idx_round_number` (`round_number`);
ALTER TABLE `analyses` ADD CONSTRAINT `analyses_ibfk_conversation` FOREIGN KEY (`conversation_id`) REFERENCES `conversations` (`id`) ON DELETE SET NULL;

-- 多轮分析功能扩展：为analyses表添加完整状态保存字段
ALTER TABLE `analyses` ADD COLUMN `analysis_steps` json DEFAULT NULL COMMENT '分析步骤详情';
ALTER TABLE `analyses` ADD COLUMN `tool_results` json DEFAULT NULL COMMENT '工具执行结果映射';
ALTER TABLE `analyses` ADD COLUMN `mcp_results` json DEFAULT NULL COMMENT 'MCP执行结果';
ALTER TABLE `analyses` ADD COLUMN `status` enum('running','completed','failed') DEFAULT 'running' COMMENT '分析状态';
ALTER TABLE `analyses` ADD COLUMN `execution_time` float DEFAULT NULL COMMENT '总执行时间(秒)';
ALTER TABLE `analyses` ADD COLUMN `context_relevance` float DEFAULT NULL COMMENT '上下文相关性评分';
ALTER TABLE `analyses` ADD COLUMN `llm_summary` text DEFAULT NULL COMMENT 'LLM生成的本轮次总结';

-- 为tool_executions表添加状态和推理字段
ALTER TABLE `tool_executions` ADD COLUMN `status` enum('running','success','failed') DEFAULT 'running' COMMENT '执行状态';
ALTER TABLE `tool_executions` ADD COLUMN `error_message` text DEFAULT NULL COMMENT '错误信息';
ALTER TABLE `tool_executions` ADD COLUMN `step_order` int DEFAULT NULL COMMENT '步骤顺序';
ALTER TABLE `tool_executions` ADD COLUMN `reasoning` text DEFAULT NULL COMMENT '执行推理过程';

-- 创建分析步骤详情表，用于存储完整的分析时间轴
CREATE TABLE `analysis_step_details` (
  `id` bigint AUTO_INCREMENT PRIMARY KEY,
  `analysis_id` varchar(36) NOT NULL COMMENT '所属分析ID',
  `step_id` varchar(100) NOT NULL COMMENT '步骤ID',
  `step_name` varchar(500) NOT NULL COMMENT '步骤名称',
  `step_type` enum('system','tool','planning','report','error','interaction') NOT NULL COMMENT '步骤类型',
  `status` enum('process','finish','error','wait') NOT NULL COMMENT '步骤状态',
  `step_order` int NOT NULL COMMENT '步骤顺序',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `execution_time` float DEFAULT NULL COMMENT '执行时间(毫秒)',
  `description` text DEFAULT NULL COMMENT '步骤描述',
  `tool_name` varchar(200) DEFAULT NULL COMMENT '工具名称(如果是工具步骤)',
  `parameters` json DEFAULT NULL COMMENT '执行参数(如果是工具步骤)',
  `has_result` tinyint(1) DEFAULT 0 COMMENT '是否有执行结果',
  `result_key` varchar(100) DEFAULT NULL COMMENT '结果键值',
  `has_intent_data` tinyint(1) DEFAULT 0 COMMENT '是否有意图分析数据',
  `has_report` tinyint(1) DEFAULT 0 COMMENT '是否有报告数据',
  `has_chart_data` tinyint(1) DEFAULT 0 COMMENT '是否有图表数据',
  `plan_data` json DEFAULT NULL COMMENT '规划数据',
  `reasoning` text DEFAULT NULL COMMENT '执行推理过程',
  `error_message` text DEFAULT NULL COMMENT '错误信息',
  `interaction_type` varchar(50) DEFAULT NULL COMMENT '交互类型: intent_confirmation/clarification/chart_display',
  `interaction_data` json DEFAULT NULL COMMENT '交互相关数据',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  FOREIGN KEY (`analysis_id`) REFERENCES `analyses`(`id`) ON DELETE CASCADE,
  INDEX `idx_analysis_step_order` (`analysis_id`, `step_order`),
  INDEX `idx_step_type` (`step_type`),
  INDEX `idx_step_status` (`status`),
  INDEX `idx_step_id` (`step_id`),
  INDEX `idx_interaction_type` (`interaction_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='分析步骤详情表';

-- 为analyses表添加性能优化索引
ALTER TABLE `analyses` ADD INDEX `idx_status` (`status`);
ALTER TABLE `analyses` ADD INDEX `idx_context_relevance` (`context_relevance`);
ALTER TABLE `analyses` ADD INDEX `idx_execution_time` (`execution_time`);

-- 为tool_executions表添加性能优化索引
ALTER TABLE `tool_executions` ADD INDEX `idx_status` (`status`);
ALTER TABLE `tool_executions` ADD INDEX `idx_step_order` (`step_order`);
ALTER TABLE `tool_executions` ADD INDEX `idx_step_id` (`step_id`);