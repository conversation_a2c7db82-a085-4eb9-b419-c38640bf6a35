# 多轮分析系统技术文档

## 📋 概述

多轮分析系统是基于LLM的智能数据分析对话平台，支持用户与AI进行连续的、上下文感知的数据分析对话。系统通过智能关联前后轮次，避免重复分析，提供连贯的分析体验。

### 🎯 核心特性

- **🤖 LLM智能上下文筛选**：使用大语言模型智能判断历史数据的相关性
- **🔄 无缝对话体验**：支持连续多轮问答，状态完整保持
- **💾 完整数据持久化**：所有分析步骤和结果完整保存和还原
- **📊 可视化分析结果**：支持图表、表格等多种数据展示
- **⚡ 流式响应**：实时显示分析进度和结果
- **🎯 语义理解**：深度理解查询意图和数据关联性
- **🧠 智能意图分析**：自动识别用户意图并生成执行计划
- **❓ 智能澄清机制**：自动检测歧义并生成澄清问题

---

## 🔄 最新重大更新 (2024年12月)

### ✨ 意图分析与确认机制

**新增核心功能**：
1. **智能意图识别**：每次新分析前自动进行深度意图分析
2. **澄清问题生成**：自动检测查询歧义并生成结构化澄清问题
3. **用户意图确认**：支持用户确认、调整或补充分析意图
4. **执行步骤规划**：基于意图分析自动生成详细的执行步骤计划

**技术实现**：
```python
async def _analyze_user_intent(
    self,
    user_query: str,
    available_tools: List[Dict],
    schema_info: str,
    project_id: str,
    conversation_id: Optional[str] = None,
    context_data: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """分析用户意图并生成详细的分析指导，包含澄清问题识别"""
```

### ✨ 极简化上下文管理重构

**重大架构变更**：
1. **移除复杂相关性计算**：删除了基于规则的相关性算法
2. **纯LLM驱动**：使用单次LLM调用完成上下文筛选和整合
3. **简化数据传递**：直接传递原始查询和上下文摘要
4. **代码大幅精简**：核心逻辑代码量减少约60%

**新的上下文处理流程**：
```python
async def _llm_extract_relevant_context(
    self, 
    current_query: str, 
    historical_data: List[Dict[str, Any]]
) -> Optional[str]:
    """使用LLM生成上下文摘要 - 完全不处理原始数据版本"""
    
    # 直接将完整的historical_data转为字符串传给大模型
    historical_text = json.dumps(historical_data, ensure_ascii=False, indent=2)
    
    # LLM一次性完成相关性判断和摘要生成
    summary_text = await self._call_llm_for_summary(historical_text, current_query)
    
    return summary_text if summary_text != "无相关上下文" else None
```

### ✨ 固化代码保护机制

**代码质量保障**：
- 引入了`@fixed_implementation_start/end`标记保护关键实现
- 防止重要功能被意外修改
- 确保核心逻辑的稳定性

---

## 🏗️ 系统架构

### 核心组件关系

```
┌─────────────────────────────────────────────────────────────┐
│                    前端 (React + TypeScript)                │
├─────────────────────────────────────────────────────────────┤
│ MultiRoundAnalysis.tsx                                      │
│ ├── ConversationState 管理                                  │
│ ├── 实时流式事件处理                                         │
│ ├── 意图确认界面                                            │
│ ├── 分析步骤可视化                                          │
│ └── 上下文相关性显示                                         │
└─────────────────────────────────────────────────────────────┘
                              │ HTTP/SSE
┌─────────────────────────────────────────────────────────────┐
│                    后端 API (FastAPI)                       │
├─────────────────────────────────────────────────────────────┤
│ conversations.py                                            │
│ ├── POST /conversations/{id}/rounds/stream                  │
│ ├── GET  /conversations/{id}/analyses                       │
│ ├── GET  /conversations/{id}/context                        │
│ └── POST /conversations/{id}/intent-confirmation/cancel     │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                 核心分析引擎 (Python)                        │
├─────────────────────────────────────────────────────────────┤
│ LLMStreamAnalyzer (基础分析器)                               │
│ ├── generate_stream() - 核心流式分析                        │
│ ├── _analyze_user_intent() - 意图分析                       │
│ ├── ExecutionContext - 执行上下文管理                       │
│ └── ContextBuilderManager - 上下文构建器管理                │
│                                                             │
│ MultiRoundLLMAnalyzer (多轮专用)                            │
│ ├── generate_conversation_stream() - 多轮流式分析           │
│ ├── _llm_extract_relevant_context() - LLM上下文提取         │
│ └── _get_historical_analysis_data() - 历史数据获取          │
│                                                             │
│ StepInterceptor (步骤拦截器)                                │
│ ├── intercept_event() - 拦截流式事件                        │
│ ├── _save_step_completed() - 保存步骤完成                   │
│ └── _save_intent_confirmation() - 保存意图确认              │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   数据持久化 (PostgreSQL)                    │
├─────────────────────────────────────────────────────────────┤
│ conversations: 会话基本信息                                  │
│ analyses: 分析记录 + 扩展字段                                │
│ tool_executions: 工具执行记录                                │
│ analysis_step_details: 详细步骤记录                         │
└─────────────────────────────────────────────────────────────┘
```

---

## 🔧 核心技术实现

### 1. 意图分析与确认流程

#### 意图分析核心逻辑

```python
async def _analyze_user_intent(
    self,
    user_query: str,
    available_tools: List[Dict],
    schema_info: str,
    project_id: str,
    conversation_id: Optional[str] = None,
    context_data: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """分析用户意图并生成详细的分析指导，包含澄清问题识别"""
    
    # 构建多轮对话上下文信息
    context_section = ""
    if conversation_id and context_data:
        context_summary = context_data.get('context_summary', '')
        if context_summary:
            context_section = f"""
## **历史轮次分析摘要**
**重要：请务必利用以下上下文信息来精确化用户的意图。**

{context_summary}

**指导要求：** 必须基于上述上下文信息理解用户当前查询的深层意图。
"""
    
    # 构建意图分析提示词
    intent_prompt = f"""
你是一位敏锐、细致的数据分析师。你的任务是深度理解用户的查询，制定分析计划，并仅在遇到关键歧义时提出澄清问题。

**核心思维框架：**
1. **意图洞察：** 用户真正想分析什么？结合业务常识，超越字面意思，理解其深层需求。
2. **歧义判断（关键阈值）：**
   * **是否存在重大歧义？** 即查询有两种或以上截然不同、会极大影响分析结果的解读方式。
   * **是否缺失关键业务参数？** 即缺少了分析无法继续进行的必要条件。
3. **规划分析路径：** 将用户的需求拆解为清晰、可执行的步骤。

{context_section}

**用户查询：** {user_query}

**输出格式（严格遵守此JSON结构）：**
{{
    "action_type": "intent_analysis",
    "intent_description": "用一两句话清晰、具体地描述你理解的用户分析意图。",
    "execution_steps": [
        {{
            "step_number": 1,
            "step_intent": "此步骤的核心分析目的。",
            "recommended_tool": "最适合此步骤的工具名称。",
            "tool_usage_reason": "简述为什么选择该工具以实现步骤意图。",
            "expected_outcome": "描述执行此步骤后的预期结果。"
        }}
    ],
    "needs_clarification": false,
    "clarification_prompt": "这里是由你生成的、自然的对话引导语，用于引出下面的澄清问题。",
    "clarification_questions": [
        {{
            "id": "question_1",
            "question": "这里是自然、对话式的澄清问题。",
            "options": ["选项A", "选项B", "选项C"]
        }}
    ]
}}
"""
    
    result = await self._call_llm(intent_prompt, system_prompt, temperature=0.2)
    return result
```

#### 意图确认流程

```python
# 在generate_stream中的意图确认逻辑
if not is_continuing:
    log.info("开始意图分析和确认流程")
    
    # 生成意图分析
    intent_analysis_result = await self._analyze_user_intent(
        context.user_query, 
        formatted_tools, 
        schema_info, 
        project_id,
        conversation_id=conversation_id,
        context_data=context_data
    )
    
    # 保存意图分析结果到上下文
    context.intent_analysis_result = intent_analysis_result
    
    # 发送意图分析结果，等待用户确认
    yield self._format_event("intent_confirmation_request", {
        "message": "请确认分析意图和计划",
        "intent_analysis": intent_analysis_result,
        "user_query": context.user_query,
        "analysis_id": analysis.id
    })
    
    # 保存等待确认状态到缓存
    context_data = {
        **context.__dict__,
        "waiting_for_intent_confirmation": True,
        "original_user_query": context.user_query,
        "intent_analysis_result": intent_analysis_result
    }
    await context_store.set(str(analysis.id), context_data)
    
    # 暂停流程，等待用户确认
    return
```

### 2. 极简化多轮上下文管理

#### 新的上下文处理架构

```python
async def generate_conversation_stream(
    self, 
    conversation_id: str,
    query: str, 
    round_number: int,
    project_id: str, 
    max_planning_rounds: int = 25,
    use_full_context: bool = True,
    context_depth: int = 5,
    continue_analysis_id: Optional[str] = None,
    intent_adjustment: Optional[str] = None
) -> AsyncGenerator[str, None]:
    """生成多轮对话的流式分析响应 - 使用LLM智能上下文整合"""
    
    # 1. 获取并整合历史上下文
    context_summary = ""
    
    if round_number > 1 and use_full_context:
        try:
            # 获取历史分析数据
            historical_data = await self._get_historical_analysis_data(
                conversation_id, max_rounds=context_depth
            )
            
            if historical_data:
                # 使用LLM生成纯文本摘要
                context_summary = await self._llm_extract_relevant_context(
                    current_query=query,
                    historical_data=historical_data
                )
                
                if context_summary:
                    log.info(f"LLM上下文摘要生成完成: 会话{conversation_id}, 摘要长度: {len(context_summary)}")
                else:
                    log.info(f"LLM判断当前查询为新话题，不使用历史上下文")
            
        except Exception as e:
            log.warning(f"获取或整合历史上下文失败: {str(e)}")
    
    # 2. 执行分析（传递原始查询和上下文数据）
    async for event in self.generate_stream(
        query=query,  # 保持原始查询
        project_id=project_id,
        max_planning_rounds=max_planning_rounds,
        conversation_id=conversation_id,
        round_number=round_number,
        context_data={"context_summary": context_summary} if context_summary else None,
        continue_analysis_id=continue_analysis_id,
        intent_adjustment=intent_adjustment
    ):
        yield event
```

#### LLM智能上下文提取

```python
async def _llm_extract_relevant_context(
    self, 
    current_query: str, 
    historical_data: List[Dict[str, Any]]
) -> Optional[str]:
    """使用LLM生成上下文摘要 - 完全不处理原始数据版本"""
    try:
        # 直接将完整的historical_data转为字符串传给大模型
        historical_text = json.dumps(historical_data, ensure_ascii=False, indent=2)
        
        # 系统提示词
        system_prompt = """
# 角色与目标
你是一个具备全局视角的顶级对话分析AI，专注于从用户的多轮分析对话中提炼上下文摘要。

你的任务是：先判断当前用户的问题是否与历史对话分析内容相关；如果相关，则生成一个能够支持下一轮分析推理的**结构化摘要**。

# 判断标准
- "相关"：当前问题在主题、目标或数据维度上与历史分析内容具有延续性或演进关系；
- "不相关"：当前问题完全更换了分析目标、主题或数据主体，与历史内容无直接联系。

# 输出要求

## 如果当前提问与历史上下文不相关，请仅输出：
无相关上下文

## 如果相关，请严格按照以下格式输出：

【用户意图】
xxx

【分析路径】
1. xxx
2. xxx
3. xxx

【最终结论】
xxx
"""

        prompt = f"""
## 完整历史分析数据（包含所有工具执行结果）
{historical_text}

## 当前用户查询
{current_query}
"""
        
        # 调用LLM生成摘要
        completion = await self.llm_client.chat.completions.create(
            model=settings.OPENAI_MODEL,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": prompt}
            ],
            max_tokens=300,
            temperature=0.1
        )
        
        summary_text = completion.choices[0].message.content.strip()
        
        # 直接返回摘要文本
        if summary_text and summary_text != "无相关上下文":
            log.info(f"LLM生成上下文摘要成功，长度：{len(summary_text)}")
            return summary_text
        else:
            log.info("LLM判断当前查询与历史上下文不相关")
            return None
            
    except Exception as e:
        log.error(f"LLM上下文摘要生成失败: {str(e)}")
        return None
```

### 3. 上下文构建器模式

#### 模块化上下文构建

```python
class ContextBuilderManager:
    """上下文构建器管理器"""
    
    def __init__(self):
        self.builders: List[ContextInfoBuilder] = []
        self._register_default_builders()
    
    def _register_default_builders(self):
        """注册默认的构建器"""
        self.register(ContextRecoveryBuilder())
        self.register(IntentAnalysisContextBuilder())
    
    def build_all_context_info(self, context: ExecutionContext) -> str:
        """构建所有上下文信息"""
        context_parts = []
        
        for builder in self.builders:
            try:
                context_info = builder.build(context)
                if context_info.strip():
                    context_parts.append(context_info)
            except Exception as e:
                log.warning(f"上下文构建器 {type(builder).__name__} 执行失败: {str(e)}")
        
        return "".join(context_parts)
```

#### 意图分析上下文构建器

```python
class IntentAnalysisContextBuilder(ContextInfoBuilder):
    """意图分析上下文构建器"""
    
    def build(self, context: ExecutionContext) -> str:
        if not (hasattr(context, 'intent_analysis_result') and context.intent_analysis_result):
            return ""
            
        intent_analysis = context.intent_analysis_result
        intent_description = intent_analysis.get('intent_description', '未生成意图分析')
        execution_steps = intent_analysis.get('execution_steps', [])
        
        # 检查是否包含用户修正信息
        has_user_modification = "**用户修正和补充：**" in intent_description
        
        if has_user_modification:
            # 包含用户补充的情况
            intent_info = f"""
## AI意图识别与分析参考（已整合用户补充）

**重要说明：以下意图分析已整合用户的补充说明，作为数据分析的重要参考依据：**

### 意图分析参考（含用户补充）
{intent_description}

### 分析建议
- **核心目标**：严格围绕原始查询需求进行分析，原始查询是分析的核心目标
- **补充参考**：用户的补充说明主要用于理解数据结构、字段命名或澄清分析细节
- **执行原则**：以原始查询为主线，合理参考补充说明优化分析方法

**注意：原始查询始终是分析的主要目标，补充说明仅起辅助作用。**
"""
        else:
            # 未包含用户修正的情况（用户直接确认）
            intent_info = f"""
## AI意图识别与分析参考（用户已确认）

**重要说明：以下是基于用户查询的深度意图分析结果，已经过用户确认：**

### 意图分析参考
{intent_description}

**注意：此参考已经过用户确认，但实际执行请以数据情况和分析可行性为准。**
"""
        
        return intent_info
    
    def get_priority(self) -> int:
        return 3
```

### 4. 前端意图确认界面

#### 意图确认组件

```typescript
const renderIntentAnalysis = (intentData?: any, clarificationData?: any) => {
  if (!intentData) return null;

  const renderClarificationSection = () => {
    if (!intentData.needs_clarification || !intentData.clarification_questions?.length) {
      return null;
    }

    return (
      <Card className="mt-4" title="需要澄清的问题">
        <div className="mb-4">
          <Text>{intentData.clarification_prompt || "为了更好地帮助您完成分析，我需要确认以下信息："}</Text>
        </div>
        
        {intentData.clarification_questions.map((question: any, index: number) => (
          <div key={question.id || index} className="mb-4">
            <div className="mb-2">
              <Text strong>{question.question}</Text>
            </div>
            
            {question.options && question.options.length > 0 && (
              <Radio.Group
                value={clarificationAnswers[question.id]}
                onChange={(e) => setClarificationAnswers(prev => ({
                  ...prev,
                  [question.id]: e.target.value
                }))}
              >
                <Space direction="vertical">
                  {question.options.map((option: string, optIndex: number) => (
                    <Radio key={optIndex} value={option}>
                      {option}
                    </Radio>
                  ))}
                  <Radio value="custom">
                    其他：
                    <Input
                      style={{ width: 200, marginLeft: 8 }}
                      placeholder="请输入自定义答案"
                      value={customAnswers[question.id] || ''}
                      onChange={(e) => setCustomAnswers(prev => ({
                        ...prev,
                        [question.id]: e.target.value
                      }))}
                      disabled={clarificationAnswers[question.id] !== 'custom'}
                    />
                  </Radio>
                </Space>
              </Radio.Group>
            )}
          </div>
        ))}
      </Card>
    );
  };

  const renderExecutionSteps = () => {
    if (!intentData.execution_steps?.length) return null;

    return (
      <Card className="mt-4" title="执行步骤规划">
        <Timeline>
          {intentData.execution_steps.map((step: any, index: number) => (
            <Timeline.Item
              key={index}
              dot={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
            >
              <div>
                <Text strong>步骤 {step.step_number}：{step.step_intent}</Text>
                <div className="mt-1">
                  <Text type="secondary">推荐工具：</Text>
                  <Tag color="blue">{step.recommended_tool}</Tag>
                </div>
                <div className="mt-1">
                  <Text type="secondary">选择理由：{step.tool_usage_reason}</Text>
                </div>
                <div className="mt-1">
                  <Text type="secondary">预期结果：{step.expected_outcome}</Text>
                </div>
              </div>
            </Timeline.Item>
          ))}
        </Timeline>
      </Card>
    );
  };

  return (
    <Card 
      title={
        <Space>
          <BulbOutlined style={{ color: '#faad14' }} />
          <span>AI意图识别与分析计划</span>
        </Space>
      }
      className="mb-4"
    >
      <div className="mb-4">
        <Text strong>分析意图：</Text>
        <div className="mt-2 p-3 bg-gray-50 rounded">
          <Text>{intentData.intent_description}</Text>
        </div>
      </div>

      {renderExecutionSteps()}
      {renderClarificationSection()}

      <div className="mt-4 flex justify-end space-x-2">
        <Button onClick={() => setShowIntentConfirmation(false)}>
          取消
        </Button>
        <Button 
          type="primary" 
          onClick={handleIntentConfirmationSubmit}
          loading={isAnalyzing}
        >
          确认并开始分析
        </Button>
      </div>
    </Card>
  );
};
```

#### 意图确认提交处理

```typescript
const handleIntentConfirmationSubmit = async () => {
  if (!pendingIntentAnalysis) return;

  try {
    setIsAnalyzing(true);
    setShowIntentConfirmation(false);

    // 构建用户的意图调整信息
    let intentAdjustment = '';
    
    // 处理澄清问题的回答
    if (pendingIntentAnalysis.needs_clarification && Object.keys(clarificationAnswers).length > 0) {
      const clarificationResponses = Object.entries(clarificationAnswers).map(([questionId, answer]) => {
        const question = pendingIntentAnalysis.clarification_questions?.find((q: any) => q.id === questionId);
        const finalAnswer = answer === 'custom' ? customAnswers[questionId] : answer;
        return `${question?.question}: ${finalAnswer}`;
      }).join('\n');
      
      intentAdjustment = clarificationResponses;
    }
    
    // 添加用户的额外补充
    if (userIntentAdjustment.trim()) {
      intentAdjustment = intentAdjustment ? 
        `${intentAdjustment}\n\n用户补充说明：\n${userIntentAdjustment}` : 
        userIntentAdjustment;
    }

    // 继续分析，传递意图调整信息
    await startNewRoundInConversation(intentAdjustment);

  } catch (error) {
    console.error('意图确认提交失败:', error);
    message.error('意图确认失败，请重试');
  } finally {
    setIsAnalyzing(false);
    setPendingIntentAnalysis(null);
    setClarificationAnswers({});
    setCustomAnswers({});
    setUserIntentAdjustment('');
  }
};
```

---

## 💾 数据持久化

### 数据库表结构更新

#### 扩展的 analyses 表
```sql
ALTER TABLE analyses ADD COLUMN (
    conversation_id VARCHAR(255),      -- 会话ID
    round_number INTEGER,              -- 轮次编号
    context_summary TEXT,              -- 上下文摘要
    status VARCHAR(20) DEFAULT 'running',  -- 分析状态
    execution_time FLOAT,              -- 执行时间
    intent_analysis JSON,              -- 意图分析结果
    intent_confirmed BOOLEAN DEFAULT FALSE,  -- 意图是否已确认
    user_intent_adjustment TEXT        -- 用户意图调整信息
);
```

#### analysis_step_details 表（保持不变）
```sql
CREATE TABLE analysis_step_details (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    analysis_id VARCHAR(36) NOT NULL,
    step_id VARCHAR(100) NOT NULL,
    step_name VARCHAR(500) NOT NULL,
    step_type ENUM('system','tool','planning','report','error','intent') NOT NULL,
    status ENUM('process','finish','error','waiting') NOT NULL,
    step_order INT NOT NULL,
    start_time DATETIME NOT NULL,
    end_time DATETIME DEFAULT NULL,
    execution_time FLOAT DEFAULT NULL,
    description TEXT DEFAULT NULL,
    tool_name VARCHAR(200) DEFAULT NULL,
    parameters JSON DEFAULT NULL,
    has_result TINYINT(1) DEFAULT 0,
    result_key VARCHAR(100) DEFAULT NULL,
    has_intent_data TINYINT(1) DEFAULT 0,
    has_report TINYINT(1) DEFAULT 0,
    plan_data JSON DEFAULT NULL,
    error_message TEXT DEFAULT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (analysis_id) REFERENCES analyses(id) ON DELETE CASCADE
);
```

---

## 🎯 关键特性详解

### 1. 智能意图分析

**多维度意图理解**：
- 结合数据库Schema信息进行意图分析
- 支持多种数据库类型的语法适配
- 动态生成执行步骤规划
- 自动检测查询歧义

**澄清问题生成**：
```python
# 澄清问题的结构化格式
"clarification_questions": [
    {
        "id": "question_1",
        "question": "这里是自然、对话式的澄清问题。",
        "options": ["选项A", "选项B", "选项C"]
    }
]
```

### 2. 上下文感知优化

**LLM驱动的相关性判断**：
- 语义层面的深度理解
- 自动识别话题转换
- 智能提取相关历史信息

**结构化上下文摘要**：
```
【用户意图】
分析ABC公司的资金流转情况

【分析路径】
1. 查询公司基本信息
2. 获取资金流转记录
3. 生成流转关系图

【最终结论】
发现异常大额转账，需要进一步调查
```

### 3. 流式事件系统

**新增事件类型**：
- `intent_confirmation_request`: 意图确认请求
- `intent_confirmation_completed`: 意图确认完成
- `analysis_continued`: 分析继续
- `context_loaded`: 上下文加载完成

**事件拦截机制**：
```python
def yield_and_intercept_event(event_type: str, event_data: Dict[str, Any]):
    """发送事件并拦截到步骤拦截器"""
    formatted_event = self._format_event(event_type, event_data)
    
    # 拦截事件
    if step_interceptor:
        try:
            step_interceptor.intercept_event(event_type, event_data)
        except Exception as e:
            log.warning(f"拦截{event_type}事件失败: {str(e)}")
    
    return formatted_event
```

---

## 🚀 部署和配置

### 环境配置更新

```python
# settings.py
MULTI_ROUND_CONFIG = {
    "max_context_rounds": 10,        # 最大上下文轮次
    "context_cache_ttl": 3600,       # 上下文缓存时间
    "llm_summary_enabled": True,     # 启用LLM总结
    "intent_analysis_enabled": True, # 启用意图分析
    "auto_clarification": True,      # 启用自动澄清
    "max_planning_rounds": 25        # 最大规划轮次
}

# LLM配置
LLM_CONFIG = {
    "intent_analysis_model": "gpt-4",
    "context_summary_model": "gpt-3.5-turbo",
    "intent_temperature": 0.2,
    "summary_temperature": 0.1,
    "max_intent_tokens": 1000,
    "max_summary_tokens": 300
}
```

### 数据库迁移脚本

```sql
-- 添加意图分析相关字段
ALTER TABLE analyses ADD COLUMN intent_analysis JSON;
ALTER TABLE analyses ADD COLUMN intent_confirmed BOOLEAN DEFAULT FALSE;
ALTER TABLE analyses ADD COLUMN user_intent_adjustment TEXT;
ALTER TABLE analyses ADD COLUMN context_summary TEXT;

-- 更新step_type枚举
ALTER TABLE analysis_step_details MODIFY COLUMN step_type 
ENUM('system','tool','planning','report','error','intent') NOT NULL;

-- 更新status枚举
ALTER TABLE analysis_step_details MODIFY COLUMN status 
ENUM('process','finish','error','waiting') NOT NULL;

-- 添加新索引
CREATE INDEX idx_analyses_intent_confirmed ON analyses(intent_confirmed);
CREATE INDEX idx_step_details_intent ON analysis_step_details(step_type, has_intent_data);
```

---

## 🔍 故障排查

### 常见问题更新

#### 1. 意图分析失败
**症状**：意图分析步骤卡住或返回错误格式
**解决**：
```python
# 检查LLM响应格式
try:
    result = json.loads(response_content)
    if result.get("action_type") != "intent_analysis":
        raise ValueError("LLM返回格式错误")
except json.JSONDecodeError as e:
    # 使用修复函数
    fixed_content = self._fix_invalid_escapes(response_content)
    result = json.loads(fixed_content)
```

#### 2. 意图确认状态不同步
**症状**：前端显示意图确认界面，但后端已继续执行
**解决**：
```python
# 检查Redis缓存状态
cached_context = await context_store.get(analysis_id)
waiting_for_confirmation = cached_context.get('waiting_for_intent_confirmation', False)

# 清理无效状态
if not waiting_for_confirmation:
    await context_store.remove(analysis_id)
```

#### 3. 上下文摘要生成失败
**症状**：多轮分析时上下文为空或格式错误
**解决**：
```python
# 检查历史数据格式
historical_data = await self._get_historical_analysis_data(conversation_id)
if not historical_data:
    log.warning("历史数据为空，跳过上下文生成")
    return None

# 验证LLM响应
if summary_text == "无相关上下文":
    log.info("LLM判断无相关上下文")
    return None
```

### 调试工具更新

```typescript
// 浏览器控制台调试
window.debugMultiRound = {
  getConversationState: () => conversationState,
  getAnalysisSteps: () => analysisSteps,
  getMcpResults: () => mcpResults,
  getPendingIntent: () => pendingIntentAnalysis,
  getIntentConfirmation: () => showIntentConfirmation,
  testIntentAnalysis: (intentData) => setPendingIntentAnalysis(intentData),
  clearIntentState: () => {
    setPendingIntentAnalysis(null);
    setShowIntentConfirmation(false);
  }
};
```

---

## 📈 性能优化

### 1. LLM调用优化
```python
# 意图分析缓存
@lru_cache(maxsize=50)
def get_intent_analysis_cached(query_hash: str, tools_hash: str):
    return cached_intent_analysis

# 异步并发处理
async def parallel_llm_calls():
    intent_task = asyncio.create_task(analyze_intent())
    context_task = asyncio.create_task(extract_context())
    
    intent_result, context_result = await asyncio.gather(
        intent_task, context_task, return_exceptions=True
    )
```

### 2. 前端渲染优化
```typescript
// 意图分析组件懒加载
const IntentAnalysisPanel = React.lazy(() => import('./IntentAnalysisPanel'));

// 虚拟化长列表
const VirtualizedSteps = React.memo(({ steps }) => (
  <FixedSizeList height={400} itemCount={steps.length} itemSize={80}>
    {StepItem}
  </FixedSizeList>
));

// 防抖用户输入
const debouncedIntentAdjustment = useMemo(
  () => debounce((value) => setUserIntentAdjustment(value), 500),
  []
);
```

---

## 📝 最佳实践

### 开发建议更新
- **意图分析**：确保LLM提示词结构化，避免格式错误
- **状态管理**：意图确认状态要在前后端保持同步
- **错误处理**：LLM调用失败时要有合理的降级策略
- **用户体验**：意图确认界面要简洁明了，避免信息过载

### 意图分析最佳实践
- **提示词设计**：使用结构化的JSON格式要求
- **澄清问题**：只在真正有歧义时才生成澄清问题
- **执行步骤**：步骤规划要具体可执行，避免过于抽象
- **用户反馈**：支持用户调整和补充意图描述

### 多轮上下文最佳实践
- **相关性判断**：依赖LLM的语义理解，不要过度依赖规则
- **摘要生成**：保持摘要简洁有用，突出关键信息
- **数据传递**：直接传递原始查询，避免复杂的查询合并
- **缓存策略**：合理使用缓存，避免重复的LLM调用

---

## 📚 API参考

### 核心接口更新

| 接口 | 方法 | 描述 | 新增参数 |
|------|------|------|----------|
| `/conversations/{id}/rounds/stream` | POST | 执行多轮分析（流式） | `intent_adjustment` |
| `/conversations/{id}/intent-confirmation/cancel` | POST | 取消意图确认 | - |

### 流式事件更新

| 事件类型 | 描述 | 数据字段 |
|----------|------|----------|
| `intent_confirmation_request` | 意图确认请求 | `intent_analysis`, `analysis_id` |
| `intent_confirmation_completed` | 意图确认完成 | `original_query`, `user_adjustment` |
| `analysis_continued` | 分析继续 | `analysis_id`, `message` |

### 请求参数更新

```typescript
interface ConversationRoundRequest {
  query: string;
  round_number: number;
  max_planning_rounds?: number;
  use_full_context?: boolean;
  context_depth?: number;
  continue_analysis_id?: string;  // 新增：继续分析ID
  intent_adjustment?: string;     // 新增：意图调整信息
}
```

---

**文档版本**：v4.0.0  
**最后更新**：2024-12-15  
**维护者**：AI数据分析团队

**主要更新内容**：
- ✅ 新增意图分析与确认机制
- ✅ 重构多轮上下文管理（LLM驱动）
- ✅ 更新流式事件系统
- ✅ 完善错误处理和调试工具
- ✅ 优化性能和用户体验