#!/usr/bin/env python3
import sqlite3
import os

# 数据库文件路径
db_path = "data/analysis.db"

if not os.path.exists(db_path):
    print(f"数据库文件不存在: {db_path}")
    exit(1)

try:
    # 连接数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 检查 analysis_step_details 表结构
    print("=== analysis_step_details 表结构 ===")
    cursor.execute("PRAGMA table_info(analysis_step_details)")
    columns = cursor.fetchall()
    
    if not columns:
        print("❌ analysis_step_details 表不存在")
    else:
        print("✅ analysis_step_details 表存在，字段如下：")
        for col in columns:
            print(f"  {col[1]} ({col[2]}) - {'NOT NULL' if col[3] else 'NULL'}")
        
        # 检查是否有 reasoning 字段
        reasoning_exists = any(col[1] == 'reasoning' for col in columns)
        if reasoning_exists:
            print("✅ reasoning 字段存在")
        else:
            print("❌ reasoning 字段不存在")
    
    conn.close()
    
except Exception as e:
    print(f"检查数据库时出错: {e}") 