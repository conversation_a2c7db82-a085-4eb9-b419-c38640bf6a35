# 数据库表结构说明

## 表: DM$P5BGE_BASE_ZH
### 列信息:
- PNAME (VARCHAR2(128), 可空)
- ONUM (NUMBER(22), 可空)
- PXNO (NUMBER(22), 可空)
- RNUM (NUMBER(22), 可空)
- CKNO (NUMBER(22), 可空)
- CHUNK (RAW(2000), 可空)

### 索引:
- DM$CEBGE_BASE_ZH (非唯一, NORMAL) - 列: PNAME ASC

--------------------------------------------------

## 表: DM$P8BGE_BASE_ZH
### 列信息:
- PNAME (VARCHAR2(128), 可空)
- ONNX_MODEL (BLOB(4000), 可空)
- METADATA (CLOB(4000), 可空)

### 索引:
- SYS_IL0000092678C00002$$ (唯一, LOB) - 列: 
- SYS_IL0000092678C00003$$ (唯一, LOB) - 列: 

--------------------------------------------------

## 表: DM$P9BGE_BASE_ZH
### 列信息:
- PNAME (VARCHAR2(128), 可空)
- NAME (VARCHAR2(4000), 可空)
- VALUE (VARCHAR2(4000), 可空)

--------------------------------------------------

## 表: DM$PABGE_BASE_ZH
### 列信息:
- PNAME (VARCHAR2(128), 可空)
- ATTRIBUTE_NAME (VARCHAR2(4000), 可空)
- INPUT_NAME (VARCHAR2(4000), 可空)
- POSITION (NUMBER(22), 可空)

--------------------------------------------------

## 表: DR$FINRISK_NEWS_ZH_CONTENT_IDX$B
### 列信息:
- MIN_DOCID (NUMBER(22), 可空)
- MAX_DOCID (NUMBER(22), 可空)
- STATUS (NUMBER(22), 可空)

--------------------------------------------------

## 表: DR$FINRISK_NEWS_ZH_CONTENT_IDX$C
### 列信息:
- DML_SCN (NUMBER(22), 可空)
- DML_ID (NUMBER(22), 可空)
- DML_OP (NUMBER(22), 可空)
- DML_RID (ROWID(10), 可空)

--------------------------------------------------

## 表: DR$FINRISK_NEWS_ZH_CONTENT_IDX$I
### 列信息:
- TOKEN_TEXT (VARCHAR2(255), 非空)
- TOKEN_TYPE (NUMBER(10,0), 非空)
- TOKEN_FIRST (NUMBER(10,0), 非空)
- TOKEN_LAST (NUMBER(10,0), 非空)
- TOKEN_COUNT (NUMBER(10,0), 非空)
- TOKEN_INFO (BLOB(4000), 可空)

### 约束:
- SYS_C009129 (CHECK) - 列: TOKEN_TEXT
  条件: "TOKEN_TEXT" IS NOT NULL
- SYS_C009130 (CHECK) - 列: TOKEN_TYPE
  条件: "TOKEN_TYPE" IS NOT NULL
- SYS_C009131 (CHECK) - 列: TOKEN_FIRST
  条件: "TOKEN_FIRST" IS NOT NULL
- SYS_C009132 (CHECK) - 列: TOKEN_LAST
  条件: "TOKEN_LAST" IS NOT NULL
- SYS_C009133 (CHECK) - 列: TOKEN_COUNT
  条件: "TOKEN_COUNT" IS NOT NULL

### 索引:
- SYS_IL0000092326C00006$$ (唯一, LOB) - 列: 
- DR$FINRISK_NEWS_ZH_CONTENT_IDX$X (非唯一, NORMAL) - 列: TOKEN_TEXT ASC, TOKEN_TYPE ASC, TOKEN_FIRST ASC, TOKEN_LAST ASC, TOKEN_COUNT ASC

--------------------------------------------------

## 表: DR$FINRISK_NEWS_ZH_CONTENT_IDX$K
### 列信息:
- DOCID (NUMBER(22), 可空)
- TEXTKEY (ROWID(10), 可空)

### 索引:
- DR$FINRISK_NEWS_ZH_CONTENT_IDX$KD (唯一, NORMAL) - 列: DOCID ASC, TEXTKEY ASC
- DR$FINRISK_NEWS_ZH_CONTENT_IDX$KR (唯一, NORMAL) - 列: TEXTKEY ASC, DOCID ASC

--------------------------------------------------

## 表: DR$FINRISK_NEWS_ZH_CONTENT_IDX$N
### 列信息:
- NLT_DOCID (NUMBER(38,0), 非空)
- NLT_MARK (CHAR(1), 非空)

### 约束:
- SYS_C009134 (CHECK) - 列: NLT_MARK
  条件: "NLT_MARK" IS NOT NULL
- SYS_IOT_TOP_92330 (PRIMARY KEY) - 列: NLT_DOCID

### 索引:
- SYS_IOT_TOP_92330 (唯一, IOT - TOP) - 列: NLT_DOCID ASC

--------------------------------------------------

## 表: DR$FINRISK_NEWS_ZH_CONTENT_IDX$Q
### 列信息:
- DML_ID (NUMBER(22), 可空)
- DML_OP (NUMBER(22), 可空)
- DML_RID (ROWID(10), 可空)

--------------------------------------------------

## 表: DR$FINRISK_NEWS_ZH_CONTENT_IDX$U
### 列信息:
- RID (ROWID(10), 非空)

### 约束:
- SYS_C009136 (PRIMARY KEY) - 列: RID

### 索引:
- SYS_C009136 (唯一, NORMAL) - 列: RID ASC

--------------------------------------------------

## 表: DR$FINRISK_NEWS_ZH_TAG_IDX$B
### 列信息:
- MIN_DOCID (NUMBER(22), 可空)
- MAX_DOCID (NUMBER(22), 可空)
- STATUS (NUMBER(22), 可空)

--------------------------------------------------

## 表: DR$FINRISK_NEWS_ZH_TAG_IDX$C
### 列信息:
- DML_SCN (NUMBER(22), 可空)
- DML_ID (NUMBER(22), 可空)
- DML_OP (NUMBER(22), 可空)
- DML_RID (ROWID(10), 可空)

--------------------------------------------------

## 表: DR$FINRISK_NEWS_ZH_TAG_IDX$I
### 列信息:
- TOKEN_TEXT (VARCHAR2(255), 非空)
- TOKEN_TYPE (NUMBER(10,0), 非空)
- TOKEN_FIRST (NUMBER(10,0), 非空)
- TOKEN_LAST (NUMBER(10,0), 非空)
- TOKEN_COUNT (NUMBER(10,0), 非空)
- TOKEN_INFO (BLOB(4000), 可空)

### 约束:
- SYS_C009137 (CHECK) - 列: TOKEN_TEXT
  条件: "TOKEN_TEXT" IS NOT NULL
- SYS_C009138 (CHECK) - 列: TOKEN_TYPE
  条件: "TOKEN_TYPE" IS NOT NULL
- SYS_C009139 (CHECK) - 列: TOKEN_FIRST
  条件: "TOKEN_FIRST" IS NOT NULL
- SYS_C009140 (CHECK) - 列: TOKEN_LAST
  条件: "TOKEN_LAST" IS NOT NULL
- SYS_C009141 (CHECK) - 列: TOKEN_COUNT
  条件: "TOKEN_COUNT" IS NOT NULL

### 索引:
- SYS_IL0000092341C00006$$ (唯一, LOB) - 列: 
- DR$FINRISK_NEWS_ZH_TAG_IDX$X (非唯一, NORMAL) - 列: TOKEN_TEXT ASC, TOKEN_TYPE ASC, TOKEN_FIRST ASC, TOKEN_LAST ASC, TOKEN_COUNT ASC

--------------------------------------------------

## 表: DR$FINRISK_NEWS_ZH_TAG_IDX$K
### 列信息:
- DOCID (NUMBER(22), 可空)
- TEXTKEY (ROWID(10), 可空)

### 索引:
- DR$FINRISK_NEWS_ZH_TAG_IDX$KD (唯一, NORMAL) - 列: DOCID ASC, TEXTKEY ASC
- DR$FINRISK_NEWS_ZH_TAG_IDX$KR (唯一, NORMAL) - 列: TEXTKEY ASC, DOCID ASC

--------------------------------------------------

## 表: DR$FINRISK_NEWS_ZH_TAG_IDX$N
### 列信息:
- NLT_DOCID (NUMBER(38,0), 非空)
- NLT_MARK (CHAR(1), 非空)

### 约束:
- SYS_C009142 (CHECK) - 列: NLT_MARK
  条件: "NLT_MARK" IS NOT NULL
- SYS_IOT_TOP_92345 (PRIMARY KEY) - 列: NLT_DOCID

### 索引:
- SYS_IOT_TOP_92345 (唯一, IOT - TOP) - 列: NLT_DOCID ASC

--------------------------------------------------

## 表: DR$FINRISK_NEWS_ZH_TAG_IDX$Q
### 列信息:
- DML_ID (NUMBER(22), 可空)
- DML_OP (NUMBER(22), 可空)
- DML_RID (ROWID(10), 可空)

--------------------------------------------------

## 表: DR$FINRISK_NEWS_ZH_TAG_IDX$U
### 列信息:
- RID (ROWID(10), 非空)

### 约束:
- SYS_C009144 (PRIMARY KEY) - 列: RID

### 索引:
- SYS_C009144 (唯一, NORMAL) - 列: RID ASC

--------------------------------------------------

## 表: FINRISK_BANK_ACCOUNTS
### 列信息:
- ID (NUMBER(22), 可空)
- NAME (VARCHAR2(400), 可空)
- ACCOUNT (VARCHAR2(40), 可空)
- BALANCE (NUMBER(20,2), 可空)

--------------------------------------------------

## 表: FINRISK_BANK_TRANSFERS
### 列信息:
- TXN_ID (NUMBER(22), 可空)
- SRC_ACCT_ID (NUMBER(22), 可空)
- DST_ACCT_ID (NUMBER(22), 可空)
- DESCRIPTION (VARCHAR2(400), 可空)
- AMOUNT (NUMBER(22), 可空)

--------------------------------------------------

## 表: FINRISK_BASE_INFO
### 列信息:
- ID (NUMBER(22), 非空, 默认值: "CMCM"."ISEQ$$_92299".nextval)
- COMPANY_NAME (VARCHAR2(400), 可空)
- COMPANY_TYPE (VARCHAR2(100), 可空)
- REG_DATE (DATE(7), 可空)
- REG_CAPITAL (NUMBER(15,2), 可空)
- REG_ADDR (VARCHAR2(400), 可空)
- LEGAL_PERSON (VARCHAR2(100), 可空)
- LEGAL_PHONE (VARCHAR2(100), 可空)
- LSRZZ (VARCHAR2(100), 可空)
- LSRXYDJ (VARCHAR2(100), 可空)
- BIZ_SCOPE (VARCHAR2(4000), 可空)
- BIZ_STATUS (VARCHAR2(100), 可空)
- IS_SXQY (VARCHAR2(10), 可空)
- IS_SXBZXR (VARCHAR2(10), 可空)
- IS_WDQKTGG (VARCHAR2(10), 可空)
- BGSL (VARCHAR2(10), 可空)
- FMYQSL (VARCHAR2(10), 可空)
