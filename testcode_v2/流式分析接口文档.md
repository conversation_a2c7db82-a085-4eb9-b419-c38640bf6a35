# 流式分析接口文档

## 接口概述

流式分析接口允许客户端通过GET请求发起数据分析任务，并以Server-Sent Events (SSE)方式实时接收分析过程中的各个步骤结果。这种方式特别适合长时间运行的分析任务，能够提供更好的用户体验。

## 基本信息

- **接口路径**：`/api/v1/analysis/stream`
- **请求方式**：GET
- **响应格式**：Server-Sent Events (text/event-stream)

## 请求参数

| 参数名称 | 参数类型 | 是否必须 | 说明 |
|---------|---------|---------|------|
| project_id | string | 是 | 项目ID，指定要在哪个项目中执行分析 |
| query | string | 是 | 查询内容，用户的分析请求描述 |

## 请求示例

```
GET /api/v1/analysis/stream?project_id=proj_123&query=近三个月销售额趋势分析
```

## 响应格式

接口以SSE(Server-Sent Events)格式返回数据流，每个事件的格式为：

```
data: {"event": "事件类型", "data": 事件数据}
```

## 详细分析流程

流式分析接口的完整处理流程如下：

### 1. 初始化阶段

1. **接收请求**：系统接收带有project_id和query参数的GET请求
2. **项目验证**：验证project_id对应的项目是否存在
   - 如果项目不存在，返回404错误
   - 如果项目存在，继续执行后续步骤
3. **创建分析服务**：实例化AnalyzerService，准备进行分析
4. **设置HTTP头**：设置适合SSE的HTTP头信息
   - `Cache-Control: no-cache`：防止缓存
   - `Connection: keep-alive`：保持连接
   - `X-Accel-Buffering: no`：禁用Nginx缓冲

### 2. 分析执行阶段

1. **发送开始事件**：
   - 事件类型：`start`
   - 数据内容：包含查询内容和开始信息

2. **创建分析记录**：
   - 在数据库中创建新的分析记录
   - 记录包含查询内容(query)和项目ID(project_id)
   - 事件类型：`analysis_created`
   - 数据内容：包含新创建的分析记录ID、查询内容和项目ID

3. **加载工具**：
   - 获取项目中所有可用的分析工具
   - 事件类型：`tools_loaded`
   - 数据内容：包含加载的工具数量

4. **初始化SQL执行器**：
   - 创建AutoSQLExecutor实例
   - 加载数据库schema信息
   - 事件类型：`sql_executor_initialized`
   - 数据内容：包含schema是否成功加载的状态

5. **分析用户意图**：
   - 调用`analyze_intent_stream`方法解析用户查询
   - 识别用户查询意图和所需的分析步骤
   - 可能生成SQL查询（如果需要）
   - 更新分析记录中的intent_analysis字段
   - 事件类型：`intent_analyzed`
   - 数据内容：完整的意图分析结果对象

### 3. 步骤执行阶段

针对意图分析生成的每个步骤，系统会依次执行：

1. **步骤开始**：
   - 事件类型：`step_started`
   - 数据内容：包含步骤ID和工具名称

2. **处理自动SQL查询**（仅针对SQL类型步骤）：
   - 如果当前步骤是"自动SQL查询"且已生成SQL
   - 将生成的SQL添加到步骤参数中
   - 事件类型：`using_generated_sql`
   - 数据内容：包含步骤ID

3. **执行工具步骤**：
   - 调用`execute_tool_step`方法执行当前步骤
   - 使用对应的工具处理数据
   - 事件类型：`step_completed`
   - 数据内容：包含步骤ID、工具名称和执行结果

4. **暂停处理**：
   - 短暂暂停(0.1秒)，让客户端有时间处理事件

### 4. 报告生成阶段

1. **生成分析报告**：
   - 基于用户查询、意图分析和所有执行结果生成最终报告
   - 调用`generate_report_stream`方法
   - 更新分析记录中的result字段
   - 事件类型：`report_generated`
   - 数据内容：包含生成的报告内容

2. **完成分析**：
   - 事件类型：`completed`
   - 数据内容：包含分析ID和完成信息

### 5. 错误处理阶段

在任何阶段发生异常时：
- 记录错误日志
- 发送错误事件
- 事件类型：`error`
- 数据内容：包含错误信息

## 事件类型详解

### 1. start - 分析开始

标志着分析过程的开始。

```json
{
  "event": "start",
  "data": {
    "message": "分析开始",
    "query": "近三个月销售额趋势分析"
  }
}
```

### 2. analysis_created - 创建分析记录

表示分析记录已在数据库中创建。

```json
{
  "event": "analysis_created",
  "data": {
    "id": "an_12345678",
    "query": "近三个月销售额趋势分析",
    "project_id": "proj_123"
  }
}
```

### 3. tools_loaded - 工具加载完成

表示分析所需的工具已加载完成。

```json
{
  "event": "tools_loaded",
  "data": {
    "tool_count": 5
  }
}
```

### 4. sql_executor_initialized - SQL执行器初始化

表示SQL执行器已初始化，可以执行SQL查询。

```json
{
  "event": "sql_executor_initialized",
  "data": {
    "schema_loaded": true
  }
}
```

### 5. intent_analyzed - 意图分析完成

表示用户查询意图分析已完成，包含详细的意图分析结果。

```json
{
  "event": "intent_analyzed",
  "data": {
    "query_type": "trend_analysis",
    "time_range": "last_3_months",
    "metrics": ["sales_amount"],
    "dimensions": ["date"],
    "analysis_steps": [
      {
        "step_id": "step_1",
        "tool_name": "自动SQL查询",
        "description": "查询销售数据",
        "parameters": {
          "table": "sales",
          "time_column": "created_at"
        }
      },
      {
        "step_id": "step_2",
        "tool_name": "数据聚合",
        "description": "按日期聚合销售额",
        "parameters": {
          "group_by": ["date"],
          "aggregations": [{"column": "amount", "function": "sum"}]
        }
      },
      {
        "step_id": "step_3",
        "tool_name": "趋势分析",
        "description": "分析销售额趋势",
        "parameters": {
          "time_column": "date",
          "value_column": "sum_amount"
        }
      }
    ],
    "generated_sql": {
      "sql": "SELECT DATE(created_at) as date, SUM(amount) as sum_amount FROM sales WHERE created_at >= DATE_SUB(NOW(), INTERVAL 3 MONTH) GROUP BY DATE(created_at) ORDER BY date",
      "parameters": []
    }
  }
}
```

### 6. step_started - 分析步骤开始

表示某个具体分析步骤开始执行。

```json
{
  "event": "step_started",
  "data": {
    "step_id": "step_1",
    "tool_name": "自动SQL查询"
  }
}
```

### 7. using_generated_sql - 使用生成的SQL

表示在自动SQL查询步骤中使用了意图分析阶段生成的SQL。

```json
{
  "event": "using_generated_sql",
  "data": {
    "step_id": "step_1"
  }
}
```

### 8. step_completed - 分析步骤完成

表示某个具体分析步骤已完成执行，包含执行结果。

```json
{
  "event": "step_completed",
  "data": {
    "step_id": "step_1",
    "tool_name": "自动SQL查询",
    "result": {
      "status": "success",
      "data": [
        {"date": "2023-01-01", "sum_amount": 12500},
        {"date": "2023-01-02", "sum_amount": 15200},
        // ... 更多数据
      ],
      "row_count": 90,
      "execution_time": "0.25s"
    }
  }
}
```

### 9. report_generated - 生成最终报告

表示最终分析报告已生成。

```json
{
  "event": "report_generated",
  "data": {
    "report": {
      "title": "近三个月销售额趋势分析",
      "summary": "过去三个月销售额总体呈上升趋势，平均日环比增长2.3%...",
      "key_insights": [
        "2月15日销售额达到峰值，为25,600元",
        "周末销售额普遍高于工作日",
        "3月销售额环比增长15%"
      ],
      "charts": [
        {
          "type": "line",
          "title": "日销售额趋势",
          "data": {
            "x": ["2023-01-01", "2023-01-02", "..."],
            "y": [12500, 15200, "..."],
            "series": "销售额"
          }
        }
      ],
      "data_tables": [
        {
          "title": "月度销售额统计",
          "columns": ["月份", "总销售额", "环比增长"],
          "rows": [
            ["1月", "320,500元", "-"],
            ["2月", "345,200元", "7.7%"],
            ["3月", "396,800元", "15.0%"]
          ]
        }
      ]
    }
  }
}
```

### 10. completed - 分析完成

表示整个分析过程已完成。

```json
{
  "event": "completed",
  "data": {
    "id": "an_12345678",
    "message": "分析已完成"
  }
}
```

### 11. error - 发生错误

表示分析过程中发生错误。

```json
{
  "event": "error",
  "data": {
    "message": "分析执行失败: 数据库连接错误",
    "error_code": "DB_CONNECTION_ERROR"
  }
}
```

## 客户端实现示例

### JavaScript实现

```javascript
// 创建EventSource连接
const projectId = "your_project_id";
const query = "近三个月销售额趋势分析";
const eventSource = new EventSource(`/api/v1/analysis/stream?project_id=${projectId}&query=${encodeURIComponent(query)}`);

// 保存分析状态和结果
const analysisState = {
  analysisId: null,
  steps: {},
  report: null,
  stepResults: {},
  currentStep: null
};

// 处理各类事件
eventSource.addEventListener('message', function(e) {
  const data = JSON.parse(e.data);
  
  switch(data.event) {
    case 'start':
      console.log('分析开始', data.data);
      // 可以在UI上显示分析开始的提示
      updateUI('analysis-status', '分析开始');
      break;
      
    case 'analysis_created':
      console.log('分析记录已创建，ID:', data.data.id);
      analysisState.analysisId = data.data.id;
      // 保存分析ID，可用于后续查询
      updateUI('analysis-id', data.data.id);
      break;
      
    case 'tools_loaded':
      console.log('工具加载完成，共', data.data.tool_count, '个工具');
      updateUI('tools-status', `已加载 ${data.data.tool_count} 个分析工具`);
      break;
      
    case 'sql_executor_initialized':
      console.log('SQL执行器初始化:', data.data);
      const schemaStatus = data.data.schema_loaded ? '已加载' : '未加载';
      updateUI('sql-executor-status', `SQL执行器初始化完成，数据库结构${schemaStatus}`);
      break;
      
    case 'intent_analyzed':
      console.log('意图分析完成', data.data);
      // 显示分析步骤列表
      displayAnalysisSteps(data.data.analysis_steps);
      // 如果有生成的SQL，显示SQL内容
      if (data.data.generated_sql) {
        displayGeneratedSQL(data.data.generated_sql);
      }
      break;
      
    case 'step_started':
      console.log('步骤开始:', data.data.step_id, data.data.tool_name);
      analysisState.currentStep = data.data.step_id;
      // 更新UI，标记当前步骤为"进行中"
      updateStepStatus(data.data.step_id, 'running', data.data.tool_name);
      break;
      
    case 'using_generated_sql':
      console.log('使用生成的SQL，步骤:', data.data.step_id);
      // 可以在UI上高亮显示正在使用的SQL
      highlightSQL(data.data.step_id);
      break;
      
    case 'step_completed':
      console.log('步骤完成:', data.data.step_id, '结果:', data.data.result);
      // 保存步骤结果
      analysisState.stepResults[data.data.step_id] = data.data.result;
      // 更新UI，标记步骤为"已完成"
      updateStepStatus(data.data.step_id, 'completed');
      // 显示步骤结果
      displayStepResult(data.data.step_id, data.data.result);
      break;
      
    case 'report_generated':
      console.log('分析报告:', data.data.report);
      analysisState.report = data.data.report;
      // 显示完整报告
      displayFullReport(data.data.report);
      break;
      
    case 'completed':
      console.log('分析完成');
      updateUI('analysis-status', '分析已完成');
      // 可以启用"导出报告"等功能
      enableReportExport();
      eventSource.close(); // 关闭连接
      break;
      
    case 'error':
      console.error('分析错误:', data.data.message);
      // 显示错误信息
      displayError(data.data.message);
      updateUI('analysis-status', '分析失败');
      eventSource.close(); // 关闭连接
      break;
  }
});

// 处理连接错误
eventSource.onerror = function(e) {
  console.error('SSE连接错误:', e);
  displayError('与服务器连接中断');
  updateUI('analysis-status', '连接中断');
  eventSource.close();
};

// 以下是UI更新相关的辅助函数（示例）
function updateUI(elementId, text) {
  const element = document.getElementById(elementId);
  if (element) {
    element.textContent = text;
  }
}

function displayAnalysisSteps(steps) {
  const stepsContainer = document.getElementById('analysis-steps');
  if (!stepsContainer) return;
  
  stepsContainer.innerHTML = '';
  steps.forEach(step => {
    const stepElement = document.createElement('div');
    stepElement.className = 'analysis-step';
    stepElement.id = `step-${step.step_id}`;
    stepElement.innerHTML = `
      <div class="step-header">
        <span class="step-id">${step.step_id}</span>
        <span class="step-tool">${step.tool_name}</span>
        <span class="step-status">等待中</span>
      </div>
      <div class="step-description">${step.description}</div>
      <div class="step-result"></div>
    `;
    stepsContainer.appendChild(stepElement);
  });
}

function updateStepStatus(stepId, status, toolName = null) {
  const stepElement = document.getElementById(`step-${stepId}`);
  if (!stepElement) return;
  
  const statusText = status === 'running' ? '执行中' : '已完成';
  const statusElement = stepElement.querySelector('.step-status');
  if (statusElement) {
    statusElement.textContent = statusText;
    statusElement.className = `step-status ${status}`;
  }
  
  if (toolName && status === 'running') {
    stepElement.classList.add('active');
  } else if (status === 'completed') {
    stepElement.classList.remove('active');
    stepElement.classList.add('completed');
  }
}

function displayStepResult(stepId, result) {
  const stepElement = document.getElementById(`step-${stepId}`);
  if (!stepElement) return;
  
  const resultElement = stepElement.querySelector('.step-result');
  if (!resultElement) return;
  
  // 根据结果类型显示不同内容
  if (result.status === 'success') {
    if (Array.isArray(result.data)) {
      // 显示表格数据
      resultElement.innerHTML = createDataTable(result.data);
    } else {
      // 显示其他类型结果
      resultElement.innerHTML = `<pre>${JSON.stringify(result.data, null, 2)}</pre>`;
    }
    
    // 添加执行信息
    if (result.execution_time) {
      const infoElement = document.createElement('div');
      infoElement.className = 'execution-info';
      infoElement.textContent = `执行时间: ${result.execution_time}`;
      resultElement.appendChild(infoElement);
    }
  } else {
    // 显示错误信息
    resultElement.innerHTML = `<div class="error-message">${result.message || '执行失败'}</div>`;
  }
}

function displayGeneratedSQL(sqlData) {
  const sqlContainer = document.getElementById('generated-sql');
  if (!sqlContainer) return;
  
  sqlContainer.innerHTML = `
    <h3>生成的SQL查询</h3>
    <pre>${sqlData.sql}</pre>
    ${sqlData.parameters.length > 0 ? `<div>参数: ${JSON.stringify(sqlData.parameters)}</div>` : ''}
  `;
}

function displayFullReport(report) {
  const reportContainer = document.getElementById('analysis-report');
  if (!reportContainer) return;
  
  // 显示报告标题和摘要
  let reportHTML = `
    <h2>${report.title}</h2>
    <div class="report-summary">${report.summary}</div>
  `;
  
  // 显示关键洞察
  if (report.key_insights && report.key_insights.length > 0) {
    reportHTML += `
      <div class="report-insights">
        <h3>关键洞察</h3>
        <ul>
          ${report.key_insights.map(insight => `<li>${insight}</li>`).join('')}
        </ul>
      </div>
    `;
  }
  
  // 显示图表（假设使用某种图表库，如ECharts）
  if (report.charts && report.charts.length > 0) {
    reportHTML += `<div class="report-charts">`;
    report.charts.forEach((chart, index) => {
      reportHTML += `
        <div class="chart-container">
          <h3>${chart.title}</h3>
          <div id="chart-${index}" class="chart" data-chart='${JSON.stringify(chart)}'></div>
        </div>
      `;
    });
    reportHTML += `</div>`;
  }
  
  // 显示数据表格
  if (report.data_tables && report.data_tables.length > 0) {
    reportHTML += `<div class="report-tables">`;
    report.data_tables.forEach(table => {
      reportHTML += `
        <div class="table-container">
          <h3>${table.title}</h3>
          <table class="data-table">
            <thead>
              <tr>
                ${table.columns.map(col => `<th>${col}</th>`).join('')}
              </tr>
            </thead>
            <tbody>
              ${table.rows.map(row => `
                <tr>
                  ${row.map(cell => `<td>${cell}</td>`).join('')}
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>
      `;
    });
    reportHTML += `</div>`;
  }
  
  reportContainer.innerHTML = reportHTML;
  
  // 初始化图表（假设使用ECharts）
  if (window.echarts && report.charts) {
    report.charts.forEach((chartData, index) => {
      const chartElement = document.getElementById(`chart-${index}`);
      if (chartElement) {
        const chart = echarts.init(chartElement);
        // 根据chartData配置图表
        const option = createChartOption(chartData);
        chart.setOption(option);
      }
    });
  }
}

function createDataTable(data) {
  if (!data || data.length === 0) return '<div>无数据</div>';
  
  const columns = Object.keys(data[0]);
  
  let tableHTML = `
    <table class="data-table">
      <thead>
        <tr>
          ${columns.map(col => `<th>${col}</th>`).join('')}
        </tr>
      </thead>
      <tbody>
  `;
  
  // 最多显示10行数据，防止过多
  const displayRows = data.slice(0, 10);
  
  displayRows.forEach(row => {
    tableHTML += `
      <tr>
        ${columns.map(col => `<td>${row[col] !== undefined ? row[col] : ''}</td>`).join('')}
      </tr>
    `;
  });
  
  tableHTML += `</tbody></table>`;
  
  // 如果数据超过10行，显示提示
  if (data.length > 10) {
    tableHTML += `<div class="more-data">显示前10行，共${data.length}行数据</div>`;
  }
  
  return tableHTML;
}

function displayError(message) {
  const errorContainer = document.getElementById('error-message');
  if (errorContainer) {
    errorContainer.textContent = message;
    errorContainer.style.display = 'block';
  }
}

function highlightSQL(stepId) {
  const sqlContainer = document.getElementById('generated-sql');
  if (sqlContainer) {
    sqlContainer.classList.add('active');
    // 5秒后移除高亮
    setTimeout(() => {
      sqlContainer.classList.remove('active');
    }, 5000);
  }
}

function enableReportExport() {
  const exportButton = document.getElementById('export-report');
  if (exportButton) {
    exportButton.disabled = false;
  }
}

// 创建图表配置（示例，假设使用ECharts）
function createChartOption(chartData) {
  if (chartData.type === 'line') {
    return {
      title: {
        text: chartData.title
      },
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: chartData.data.x
      },
      yAxis: {
        type: 'value'
      },
      series: [{
        name: chartData.data.series,
        type: 'line',
        data: chartData.data.y
      }]
    };
  } else if (chartData.type === 'bar') {
    return {
      title: {
        text: chartData.title
      },
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: chartData.data.x
      },
      yAxis: {
        type: 'value'
      },
      series: [{
        name: chartData.data.series,
        type: 'bar',
        data: chartData.data.y
      }]
    };
  } else if (chartData.type === 'pie') {
    // 构建饼图数据
    const pieData = chartData.data.categories.map((cat, index) => ({
      name: cat,
      value: chartData.data.values[index]
    }));
    
    return {
      title: {
        text: chartData.title
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      series: [{
        name: chartData.data.series,
        type: 'pie',
        radius: '50%',
        data: pieData
      }]
    };
  }
  
  // 默认返回空配置
  return {};
}
```

## 意图分析结果结构详解

意图分析(`intent_analyzed`事件)返回的数据结构是整个分析过程的核心，它决定了后续的执行步骤。以下是详细结构说明：

```json
{
  "query_type": "分析类型，如trend_analysis、comparison、correlation等",
  "time_range": "时间范围，如last_3_months、last_year、specific_date_range等",
  "metrics": ["要分析的指标列表，如sales_amount、visitor_count等"],
  "dimensions": ["分析维度列表，如date、product_category、region等"],
  "filters": [
    {
      "column": "过滤列名",
      "operator": "过滤操作符，如eq、gt、lt、in等",
      "value": "过滤值"
    }
  ],
  "analysis_steps": [
    {
      "step_id": "步骤唯一标识符",
      "tool_name": "使用的工具名称",
      "description": "步骤描述",
      "parameters": {
        // 工具特定的参数
      },
      "depends_on": ["依赖的步骤ID列表，可选"]
    }
  ],
  "generated_sql": {
    "sql": "生成的SQL查询语句",
    "parameters": ["SQL参数列表"]
  }
}
```

### 常见分析类型(query_type)

- **trend_analysis**: 趋势分析，观察指标随时间的变化
- **comparison**: 对比分析，比较不同维度间的差异
- **correlation**: 相关性分析，寻找两个或多个指标之间的关系
- **anomaly_detection**: 异常检测，识别数据中的异常点
- **forecasting**: 预测分析，基于历史数据预测未来趋势
- **distribution**: 分布分析，分析数据的分布情况
- **ranking**: 排名分析，对数据进行排序和排名

### 常见分析工具(tool_name)

- **自动SQL查询**: 执行SQL查询获取原始数据
- **数据聚合**: 对数据进行分组和聚合
- **趋势分析**: 分析数据随时间的变化趋势
- **差异分析**: 计算不同组之间的差异
- **相关性分析**: 计算变量之间的相关性
- **数据可视化**: 生成数据可视化图表
- **异常检测**: 识别数据中的异常点
- **预测模型**: 预测未来数据趋势

## 工具执行结果结构

每个工具执行完成后，会返回一个结果对象，通常包含以下字段：

```json
{
  "status": "执行状态，success或error",
  "data": "执行结果数据，可能是数组、对象或简单值",
  "message": "执行结果消息，特别是在错误时",
  "row_count": "结果数据行数，适用于返回表格数据的工具",
  "execution_time": "执行时间",
  "metadata": {
    // 工具特定的元数据
  }
}
```

不同工具可能返回不同格式的数据：

### 自动SQL查询工具

```json
{
  "status": "success",
  "data": [
    {"column1": "value1", "column2": "value2", ...},
    ...
  ],
  "row_count": 100,
  "execution_time": "1.25s",
  "sql": "执行的SQL语句",
  "metadata": {
    "schema": "使用的数据库schema",
    "tables": ["查询涉及的表"]
  }
}
```

### 数据聚合工具

```json
{
  "status": "success",
  "data": [
    {"group_column": "group_value", "aggregated_value": 123, ...},
    ...
  ],
  "row_count": 25,
  "execution_time": "0.5s",
  "metadata": {
    "aggregation_functions": ["使用的聚合函数列表"],
    "group_by_columns": ["分组列列表"]
  }
}
```

### 趋势分析工具

```json
{
  "status": "success",
  "data": {
    "trend_type": "上升/下降/平稳",
    "growth_rate": 0.25,
    "time_series_data": [
      {"time": "2023-01-01", "value": 100},
      {"time": "2023-01-02", "value": 110},
      ...
    ],
    "seasonality": {
      "detected": true,
      "pattern": "weekly",
      "strength": 0.8
    }
  },
  "execution_time": "0.8s"
}
```

### API调用工具(API)

API工具通过调用外部REST API获取数据或执行操作，适用于访问外部系统数据。

**特点**：
- 支持HTTP请求(GET, POST, PUT, DELETE等)
- 可配置请求参数、头信息和认证
- 支持结果映射和转换
- 适合集成外部服务和实时数据获取

**模板格式**：JSON配置，指定URL、方法、参数等HTTP请求信息：
```json
{
  "url": "API端点URL",
  "method": "HTTP方法(GET/POST/PUT/DELETE)",
  "params": {
    "参数名": "参数值或变量引用${parameter_name}"
  },
  "headers": {
    "头名称": "头值"
  },
  "body": "请求体(POST/PUT请求)",
  "auth": {
    "type": "认证类型(basic/bearer/oauth2)",
    "credentials": "凭证信息"
  },
  "result_mapping": {
    "目标字段": "源字段路径"
  },
  "display_format": "结果显示格式(json/table/text)"
}
```

**路由参数支持**：
API工具现在支持URL路径中的路由参数（动态段）。路由参数允许在URL路径中包含动态值，而不仅仅是查询参数。

使用方法：
```json
{
  "url": "https://api.example.com/users/${user_id}/details",
  "method": "GET",
  "route_params": {
    "user_id": "${user_id}"
  },
  "params": {
    "include": "profile"
  }
}
```

在上面的例子中：
- `${user_id}` 是URL路径中的占位符
- `route_params` 对象声明了需要替换的路由参数
- 实际请求会将URL中的 `${user_id}` 替换为参数中提供的值

**实际示例**：
```json
{
  "url": "https://restcountries.com/v3.1/alpha/${country_code}",
  "method": "GET",
  "route_params": {
    "country_code": "${country_code}"
  },
  "params": {
    "fields": "name,capital,population"
  }
}
```

如果 `country_code="CN"`，则实际请求URL为：`https://restcountries.com/v3.1/alpha/CN?fields=name,capital,population`

**结果处理**：
- API返回的数据会根据result_mapping映射进行转换
- 结果会根据display_format决定最终展示方式
- 所有HTTP状态和响应信息会记录在metadata中

**示例工具**：
- 汇率查询接口
- 天气数据获取
- 股票行情查询
- 国家信息查询

### Python脚本工具

```json
{
  "status": "success",
  "data": {
    "data": [
      {
        "公司名称": "ABC公司",
        "报告期": "2023-06-30",
        "流动比率": 1.75,
        "速动比率": 1.45,
        "资产负债率": 45.32,
        "净资产收益率(ROE)": 12.8,
        "总资产收益率(ROA)": 7.2,
        "毛利率": 35.5,
        "净利率": 18.2,
        "风险评级": "低风险"
      },
      {
        "公司名称": "DEF公司",
        "报告期": "2023-06-30",
        "流动比率": 0.95,
        "速动比率": 0.62,
        "资产负债率": 78.5,
        "净资产收益率(ROE)": 3.8,
        "总资产收益率(ROA)": 1.2,
        "毛利率": 20.3,
        "净利率": 5.6,
        "风险评级": "高风险"
      }
    ],
    "display_format": "table"
  },
  "execution_time": "1.15s",
  "metadata": {
    "python_version": "3.8.10",
    "memory_usage": "42MB",
    "packages": ["pandas", "numpy"],
    "records_processed": 15
  }
}
```

## 工具类型详解

系统支持多种类型的工具，每种类型适用于不同的分析场景：

### SQL查询工具

SQL查询工具通过执行SQL语句从数据库查询数据，是最基础和常用的数据获取工具。

**特点**：
- 使用SQL模板，支持参数化查询
- 直接访问数据库，性能高效
- 适合结构化数据查询

**模板格式**：标准SQL查询语句，使用`:parameter_name`方式引用参数

**示例工具**：
- 公司基本信息查询
- 负面新闻分析

### 图数据查询工具(GRAPH)

图数据查询工具针对图数据库进行查询，特别适用于关系分析和网络结构数据。

**特点**：
- 支持节点和边的关系查询
- 适合复杂关系的分析
- 可视化结果通常为网络图

**模板格式**：图查询语言或图模式的SQL扩展

**示例工具**：
- 资金流转分析
- 企业关联关系分析

### 向量检索工具(VECTOR)

向量检索工具利用向量数据库进行相似度检索，适用于非结构化数据分析。

**特点**：
- 支持语义相似度检索
- 适合文档、图像等非结构化数据
- 结果通常为文本或相似度排序列表

**模板格式**：通常是向量数据库的查询语言或扩展SQL

**示例工具**：
- 纳税情况分析
- 文档相似度检索

### Python脚本工具(PYTHON)

Python脚本工具允许执行自定义Python代码进行数据处理和分析，提供最大的灵活性。

**特点**：
- 支持复杂数据处理和分析逻辑
- 可使用各种Python库(pandas, numpy, scikit-learn等)
- 完全可定制的分析流程
- 适合实现自定义算法和模型

**模板格式**：Python代码，接收参数并返回结果：
```python
# 可以导入必要的库
import pandas as pd
import numpy as np

# 函数定义和处理逻辑
def process_data(param1, param2):
    # 数据处理代码
    result = {...}
    return result

# 从数据库获取数据
data = execute_query("SELECT * FROM table WHERE condition = :param", {"param": param_value})

# 执行处理并返回结果
result = process_data(param1, param2)
return {
    "data": result,
    "display_format": "table"  # 指定结果显示格式
}
```

**执行环境**：
- 代码在隔离的Python环境中执行
- 可访问预安装的常用数据分析库
- 可使用`execute_query`函数访问数据库
- 通过return语句返回结果对象

**安全限制**：
- 无法访问文件系统和网络(除非特别授权)
- 限制执行时间和内存使用
- 禁止导入潜在危险的库

**示例工具**：
- 金融数据分析脚本
- 风险评分计算
- 时间序列预测

### 自定义工具(CUSTOM)

自定义工具提供特定功能的封装，通常由系统管理员预先配置。

**特点**：
- 组合多种分析或特殊处理逻辑
- 由系统内部实现，不需要模板
- 一般用于特定且复杂的分析需求

**示例工具**：
- 复杂风险模型
- 多数据源综合分析

## 错误处理

当发生错误时，服务器会返回以下两种形式之一的错误信息：

1. SSE事件流中的error事件：
   ```
   data: {"event": "error", "data": {"message": "错误详情", "error_code": "错误代码"}}
   ```

2. 对于项目不存在等前置检查错误，返回标准HTTP错误响应：
   ```json
   {
     "code": 404,
     "message": "项目不存在",
     "request_id": "请求ID"
   }
   ```

### 常见错误代码及其含义

- **PROJECT_NOT_FOUND**: 项目不存在
- **DB_CONNECTION_ERROR**: 数据库连接错误
- **INVALID_SQL**: SQL语法错误
- **TOOL_EXECUTION_ERROR**: 工具执行错误
- **TIMEOUT_ERROR**: 执行超时
- **PERMISSION_DENIED**: 权限不足
- **INVALID_PARAMETERS**: 参数无效
- **RESOURCE_EXHAUSTED**: 资源耗尽

## 注意事项

1. 客户端需要支持EventSource或相应的SSE处理能力
2. 长时间运行的分析可能会占用服务器资源，建议实现超时机制
3. 分析结果会保存到数据库中，可通过其他API查询历史记录
4. 服务器配置了防止Nginx缓冲的头信息，确保流式响应能正常工作
5. 为防止连接中断导致分析结果丢失，可以使用analysis_created事件中返回的ID查询完整结果
6. 对于复杂查询，系统可能需要较长处理时间，客户端应适当显示加载状态
7. 建议在生产环境中对接口实施请求频率限制，防止资源滥用
8. 分析结果应缓存一段时间，以便用户可以快速查看最近的分析，无需重新计算 