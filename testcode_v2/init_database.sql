-- 创建数据库
CREATE DATABASE IF NOT EXISTS ai_data_qa;

USE ai_data_qa;

-- 用户表
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `hashed_password` varchar(100) NOT NULL COMMENT '密码哈希',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `is_superuser` tinyint(1) DEFAULT 0 COMMENT '是否超级用户',
  `pid` int(11) NOT NULL DEFAULT 0 COMMENT '父级ID',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_username` (`username`),
  UNIQUE KEY `idx_email` (`email`),
  KEY `idx_pid` (`pid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户表';

-- 插入默认管理员账号
BEGIN;
INSERT INTO `ai_data_qa`.`users` (`id`, `username`, `email`, `hashed_password`, `is_active`, `is_superuser`, `created_at`, `updated_at`) VALUES (1, 'decisionadmin', '<EMAIL>', '$2b$12$X9YTVAd0KcBaBA.GFcTbkuwtL8AjyiWqqs0Rb4J//E5mH12d/Rg62', 1, 1, '2025-05-23 06:35:53', '2025-05-23 16:11:03');
COMMIT;

-- 项目表
CREATE TABLE `projects` (
  `id` varchar(36) NOT NULL,
  `name` varchar(100) NOT NULL COMMENT '项目名称',
  `description` text COMMENT '项目描述',
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '创建人ID',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_project_name` (`name`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='项目表';

BEGIN;
INSERT INTO `projects` (`id`, `name`, `description`, `created_at`, `updated_at`) VALUES ('2f3e1718-7a2e-4c67-a573-eb7ae93a7ca9', '金融风险分析', '分析金融数据中的风险指标和模式', '2025-04-22 15:37:14', '2025-04-22 15:37:14');
COMMIT;

-- 数据源表
CREATE TABLE `data_sources` (
  `id` varchar(36) NOT NULL,
  `name` varchar(100) NOT NULL COMMENT '数据源名称',
  `description` text COMMENT '数据源描述',
  `type` enum('oracle','mysql','postgresql','mssql','http_api','other') NOT NULL COMMENT '数据源类型',
  `config` json NOT NULL COMMENT '数据源配置',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `project_id` varchar(36) NOT NULL COMMENT '所属项目ID',
  PRIMARY KEY (`id`),
  KEY `idx_datasource_name` (`name`),
  KEY `idx_datasource_project` (`project_id`),
  CONSTRAINT `data_sources_ibfk_1` FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='数据源表';

-- ----------------------------
-- Records of data_sources
-- ----------------------------
BEGIN;
INSERT INTO `data_sources` (`id`, `name`, `description`, `type`, `config`, `created_at`, `updated_at`, `project_id`) VALUES ('a1b2c3d4-e5f6-4a5b-9c3d-0e9f8a7b6c5d', '金融Oracle数据库', '包含金融交易和公司信息的Oracle数据库', 'oracle', '{\"host\": \"***************\", \"port\": 1521, \"password\": \"CmCm#_123\", \"username\": \"cmcm\", \"service_name\": \"brighten.sub12210155490.ailabvcn.oraclevcn.com\"}', '2025-04-22 15:37:14', '2025-04-24 08:26:19', '2f3e1718-7a2e-4c67-a573-eb7ae93a7ca9');
COMMIT;

-- 工具表
CREATE TABLE `tools` (
  `id` varchar(36) NOT NULL,
  `name` varchar(100) NOT NULL COMMENT '工具名称',
  `description` text COMMENT '工具描述',
  `tool_type` enum('sql','api','python','graph','vector','custom') NOT NULL COMMENT '工具类型',
  `template` text NOT NULL COMMENT '工具模板',
  `parameters` json NOT NULL COMMENT '工具参数定义',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `data_source_id` varchar(36) NOT NULL COMMENT '所属数据源ID',
  PRIMARY KEY (`id`),
  KEY `idx_tool_name` (`name`),
  KEY `idx_tool_datasource` (`data_source_id`),
  CONSTRAINT `tools_ibfk_1` FOREIGN KEY (`data_source_id`) REFERENCES `data_sources` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='工具表';

-- ----------------------------
-- Records of tools
-- ----------------------------
BEGIN;
INSERT INTO `tools` (`id`, `name`, `description`, `tool_type`, `template`, `parameters`, `created_at`, `updated_at`, `data_source_id`) VALUES ('1a2b3c4d-5e6f-7a8b-9c0d-1e2f3a4b5c6d', '公司基本信息查询', '查询公司的基本信息，包括注册信息、经营状态、风险信息等。使用%表示查询所有公司。', 'sql', 'SELECT\n                    JSON_ARRAYAGG(\n                        JSON_OBJECT(\n                            KEY \'公司ID\' VALUE r.id,\n                            KEY \'公司名称\' VALUE r.company_name,\n                            KEY \'公司类型\' VALUE r.company_type,\n                            KEY \'注册日期\' VALUE r.reg_date,\n                            KEY \'注册资本\' VALUE r.reg_capital,\n                            KEY \'注册地址\' VALUE r.reg_addr,\n                            KEY \'公司法人\' VALUE r.legal_person,\n                            KEY \'法人联系电话\' VALUE r.legal_phone,\n                            KEY \'纳税人资质\' VALUE r.lsrzz,\n                            KEY \'纳税信用等级\' VALUE r.lsrxydj,\n                            KEY \'经营范围\' VALUE r.biz_scope,\n                            KEY \'经营状态\' VALUE r.biz_status,\n                            KEY \'是否失信企业\' VALUE r.is_sxqy,\n                            KEY \'是否是失信被执行人\' VALUE r.is_sxbzxr,\n                            KEY \'是否有未到期开庭公告\' VALUE r.is_wdqktgg,\n                            KEY \'近三年做为被告人数量\' VALUE r.bgsl,\n                            KEY \'近一年负面舆情数量\' VALUE r.fmyqsl\n                        )\n                        ORDER BY r.id\n                        RETURNING CLOB\n                    ) AS JSON_DOC\n                FROM FINRISK_base_info r\n                WHERE r.company_name LIKE \n                    CASE \n                        WHEN :company_name = \'%\' THEN \'%\'\n                        ELSE :company_name\n                    END', '[{\"name\": \"company_name\", \"type\": \"string\", \"default\": \"%\", \"required\": false, \"description\": \"公司名称，使用%查询所有公司\"}]', '2025-04-22 15:37:14', '2025-04-27 16:43:40', 'a1b2c3d4-e5f6-4a5b-9c3d-0e9f8a7b6c5d');
INSERT INTO `tools` (`id`, `name`, `description`, `tool_type`, `template`, `parameters`, `created_at`, `updated_at`, `data_source_id`) VALUES ('3f867d99-70c7-40e9-901f-554d01dddba8', '纳税情况分析', '分析公司的纳税情况，使用向量检索从文档中提取相关信息', 'vector', 'SELECT \n                    fe.chunk_text\n                FROM FINRISK_files ff\n                JOIN FINRISK_files_embedding fe ON ff.id = fe.file_id\n                WHERE ff.company_id = (\n                    SELECT id FROM FINRISK_base_info WHERE company_name = :company_name\n                )\n                ORDER BY VECTOR_DISTANCE(\n                    fe.vector_data,\n                    VECTOR_EMBEDDING(bge_base_zh USING \'纳税情况\' as DATA),\n                    COSINE\n                )\n                FETCH FIRST 1 ROWS ONLY', '[{\"name\": \"company_name\", \"type\": \"string\", \"default\": \"%\", \"required\": false, \"description\": \"公司名称\"}]', '2025-04-24 16:40:10', '2025-04-27 17:01:33', 'a1b2c3d4-e5f6-4a5b-9c3d-0e9f8a7b6c5d');
INSERT INTO `tools` (`id`, `name`, `description`, `tool_type`, `template`, `parameters`, `created_at`, `updated_at`, `data_source_id`) VALUES ('f1e2d3c4-b5a6-4b7c-8d9e-0f1a2b3c4d5e', '资金流转分析', '分析公司的资金流转情况，包括交易次数、金额等。使用%表示分析所有公司。', 'graph', 'WITH transfers AS (\n                    SELECT \n                        src_name AS 源公司名称, \n                        source_account AS 源账号,\n                        dst_name AS 目标公司名称, \n                        target_account AS 目标账号,\n                        COUNT(1) AS 交易次数,\n                        SUM(amount) AS 总金额\n                    FROM graph_table (\n                        FINRISK_BANK_GRAPH\n                        MATCH (src) - [t1 IS FINRISK_BANK_TRANSFERS] -> (dst)\n                        COLUMNS (\n                            src.name as src_name,\n                            src.account as source_account,\n                            dst.name AS dst_name,\n                            dst.account as target_account,\n                            t1.amount as amount\n                        )\n                    )\n                    WHERE \n                        CASE \n                            WHEN :company_name = \'%\' THEN 1=1\n                            ELSE src_name = :company_name OR dst_name = :company_name\n                        END\n                    GROUP BY src_name, source_account, dst_name, target_account\n                )\n                SELECT \n                    源公司名称 || \'通过\' || 源账号 || \'账号向\' || 目标公司名称 || \'的\' || 目标账号 || \n                    \'账号交易\' || NVL(交易次数, 0) || \'次，总的交易金额为: ¥\' || NVL(总金额, 0) AS 交易描述,\n                    源公司名称, 源账号, 目标公司名称, 目标账号, 交易次数, 总金额,\n                    CASE \n                        WHEN 总金额 > 1000000 AND 交易次数 > 10 THEN \'高风险\'\n                        WHEN 总金额 > 500000 OR 交易次数 > 5 THEN \'中风险\'\n                        ELSE \'低风险\'\n                    END AS 风险等级\n                FROM transfers\n                ORDER BY 交易次数 DESC', '[{\"name\": \"company_name\", \"type\": \"string\", \"default\": \"%\", \"required\": false, \"description\": \"公司名称，使用%分析所有公司\"}]', '2025-04-22 15:37:14', '2025-04-27 17:01:26', 'a1b2c3d4-e5f6-4a5b-9c3d-0e9f8a7b6c5d');
INSERT INTO `tools` (`id`, `name`, `description`, `tool_type`, `template`, `parameters`, `created_at`, `updated_at`, `data_source_id`) VALUES ('f7e6d5c4-b3a2-41c0-b9d8-e7f6a5d4c3b2', 'IP归属地查询', '获取指定IP的归属地', 'api', '{\n                    \"url\": \"http://ip-api.com/json/${ip}\",\n                    \"method\": \"GET\",\n                    \"route_params\": {\n                        \"ip\": \"${ip}\"\n                    },\n                    \"params\": {\n                    },\n                    \"headers\": {\n                        \"Accept\": \"application/json\"\n                    },\n                    \"result_mapping\": {\n                        \n                    },\n                    \"display_format\": \"json\"\n                }', '[{\"name\": \"ip\", \"type\": \"string\", \"default\": \"\", \"required\": true, \"description\": \"传入你需要查询的IP\"}]', '2025-04-29 09:15:32', '2025-05-06 17:49:49', 'a1b2c3d4-e5f6-4a5b-9c3d-0e9f8a7b6c5d');
INSERT INTO `tools` (`id`, `name`, `description`, `tool_type`, `template`, `parameters`, `created_at`, `updated_at`, `data_source_id`) VALUES ('fa9eea33-2414-46d0-88cf-2cc19843d983', '负面新闻分析', '分析公司的负面新闻', 'sql', 'SELECT\n                    id,\n                    company_id,\n                    TO_CHAR(happen_date, \'YYYY-MM-DD\') as happen_date,\n                    news_content,\n                    tag,\n                    url\n                FROM finrisk_news \n                WHERE CONTAINS(NEWS_CONTENT, :company_name) > 0\n                ORDER BY happen_date DESC', '[{\"name\": \"company_name\", \"type\": \"string\", \"required\": true, \"description\": \"公司名称\"}]', '2025-04-27 15:55:45', '2025-04-27 15:55:45', 'a1b2c3d4-e5f6-4a5b-9c3d-0e9f8a7b6c5d');
COMMIT;

-- 分析记录表
CREATE TABLE `analyses` (
  `id` varchar(36) NOT NULL,
  `query` text NOT NULL COMMENT '用户查询',
  `intent_analysis` json DEFAULT NULL COMMENT '意图分析结果',
  `result` text COMMENT '分析报告',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `project_id` varchar(36) NOT NULL COMMENT '所属项目ID',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_analysis_project` (`project_id`),
  KEY `idx_analysis_created` (`created_at`),
  CONSTRAINT `analyses_ibfk_1` FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='分析记录表';

-- 工具执行记录表
CREATE TABLE `tool_executions` (
  `id` varchar(36) NOT NULL,
  `parameters` json NOT NULL COMMENT '执行参数',
  `result` json DEFAULT NULL COMMENT '执行结果',
  `execution_time` float DEFAULT NULL COMMENT '执行时间(秒)',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `analysis_id` varchar(36) NOT NULL COMMENT '所属分析ID',
  `tool_id` varchar(36) NOT NULL COMMENT '工具ID',
  `step_id` varchar(50) DEFAULT NULL COMMENT '分析步骤ID',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_execution_analysis` (`analysis_id`),
  KEY `idx_execution_tool` (`tool_id`),
  CONSTRAINT `tool_executions_ibfk_1` FOREIGN KEY (`analysis_id`) REFERENCES `analyses` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='工具执行记录表';

-- 选定分析表信息
CREATE TABLE `selected_tables` (
  `id` varchar(36) NOT NULL,
  `data_source_id` varchar(36) NOT NULL COMMENT '所属数据源ID',
  `table_name` varchar(100) NOT NULL COMMENT '表名',
  `table_description` text COMMENT '表描述',
  `table_schema` json NOT NULL COMMENT '表结构信息',
  `sample_data` json NOT NULL COMMENT '样例数据(前3条)',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_datasource_table` (`data_source_id`,`table_name`),
  CONSTRAINT `selected_tables_ibfk_1` FOREIGN KEY (`data_source_id`) REFERENCES `data_sources` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='选定分析表信息';

BEGIN;
INSERT INTO `selected_tables` (`id`, `data_source_id`, `table_name`, `table_description`, `table_schema`, `sample_data`, `created_at`, `updated_at`) VALUES ('2c9414e7-c689-4af7-90af-26637ab0b2e8', 'a1b2c3d4-e5f6-4a5b-9c3d-0e9f8a7b6c5d', 'FINRISK_NEWS', '', '{\"ID\": {\"nullable\": \"N\", \"data_type\": \"NUMBER\", \"data_length\": 22}, \"TAG\": {\"nullable\": \"Y\", \"data_type\": \"VARCHAR2\", \"data_length\": 4000}, \"URL\": {\"nullable\": \"Y\", \"data_type\": \"VARCHAR2\", \"data_length\": 4000}, \"COMPANY_ID\": {\"nullable\": \"Y\", \"data_type\": \"NUMBER\", \"data_length\": 22}, \"HAPPEN_DATE\": {\"nullable\": \"Y\", \"data_type\": \"DATE\", \"data_length\": 7}, \"NEWS_CONTENT\": {\"nullable\": \"Y\", \"data_type\": \"CLOB\", \"data_length\": 4000}}', '[{\"id\": 1, \"tag\": \"负面新闻\", \"url\": null, \"company_id\": 171, \"happen_date\": \"2024-06-19T00:00:00\", \"news_content\": \"2024年6月9日，ABC公司发布《关于签订算力服务合同的公告》。公告显示，ABC公司与山东省计算中心签订了一项高达500万元的算力服务合同。近期，国家超级计算济南中心声明ABC公司发布的《关于签订算力服务合同的公告》属于不实信息\"}]', '2025-05-08 17:53:39', '2025-05-08 17:53:39');
INSERT INTO `selected_tables` (`id`, `data_source_id`, `table_name`, `table_description`, `table_schema`, `sample_data`, `created_at`, `updated_at`) VALUES ('7b062301-1cc7-4d6f-847a-b17cc3335c31', 'a1b2c3d4-e5f6-4a5b-9c3d-0e9f8a7b6c5d', 'FINRISK_BANK_TRANSFERS', '', '{\"AMOUNT\": {\"nullable\": \"Y\", \"data_type\": \"NUMBER\", \"data_length\": 22}, \"TXN_ID\": {\"nullable\": \"Y\", \"data_type\": \"NUMBER\", \"data_length\": 22}, \"DESCRIPTION\": {\"nullable\": \"Y\", \"data_type\": \"VARCHAR2\", \"data_length\": 400}, \"DST_ACCT_ID\": {\"nullable\": \"Y\", \"data_type\": \"NUMBER\", \"data_length\": 22}, \"SRC_ACCT_ID\": {\"nullable\": \"Y\", \"data_type\": \"NUMBER\", \"data_length\": 22}}', '[{\"amount\": 8948, \"txn_id\": 1, \"description\": \"transfer\", \"dst_acct_id\": 180, \"src_acct_id\": 171}, {\"amount\": 6784, \"txn_id\": 2, \"description\": \"transfer\", \"dst_acct_id\": 180, \"src_acct_id\": 171}, {\"amount\": 1006, \"txn_id\": 3, \"description\": \"transfer\", \"dst_acct_id\": 180, \"src_acct_id\": 171}]', '2025-05-08 17:53:32', '2025-05-08 17:53:32');
INSERT INTO `selected_tables` (`id`, `data_source_id`, `table_name`, `table_description`, `table_schema`, `sample_data`, `created_at`, `updated_at`) VALUES ('9b8606a5-be48-4f93-b2f4-a468dd2459df', 'a1b2c3d4-e5f6-4a5b-9c3d-0e9f8a7b6c5d', 'FINRISK_FILES_EMBEDDING', '', '{\"ID\": {\"nullable\": \"N\", \"data_type\": \"NUMBER\", \"data_length\": 22}, \"FILE_ID\": {\"nullable\": \"N\", \"data_type\": \"NUMBER\", \"data_length\": 22}, \"CHUNK_TEXT\": {\"nullable\": \"Y\", \"data_type\": \"VARCHAR2\", \"data_length\": 4000}, \"VECTOR_DATA\": {\"nullable\": \"Y\", \"data_type\": \"VECTOR\", \"data_length\": 8200}, \"CHUNK_LENGTH\": {\"nullable\": \"Y\", \"data_type\": \"NUMBER\", \"data_length\": 22}, \"CHUNK_OFFSET\": {\"nullable\": \"Y\", \"data_type\": \"NUMBER\", \"data_length\": 22}}', '[{\"id\": 7, \"file_id\": 1, \"chunk_text\": \"行业: \\n核准日期: 2019-11-15 登记机关: 海淀分局 \\n营业期限: 2000-08-03 至 纳税状态: 正常 \\n注册地址: 北京市海淀区永信路\", \"vector_data\": \"[8.09560157E-003,-1.58479344E-002,1.2998159E-002,-6.43633008E-002,1.77750904E-002,-2.91072745E-002,-7.00478349E-003,-3.93003516E-004,1.75594091E-002,-2.21600924E-002,-4.78608981E-002,2.18651723E-002,2... [内容过长已截断，完整长度:12593]\", \"chunk_length\": 112, \"chunk_offset\": 1101}, {\"id\": 8, \"file_id\": 1, \"chunk_text\": \"经营范围:\", \"vector_data\": \"[-3.12321354E-002,-2.94058509E-002,1.37349889E-002,1.08085414E-002,-4.15229984E-002,-1.73585792E-003,3.62779796E-002,3.53689343E-002,-5.57911694E-002,4.63532209E-002,-2.41000243E-002,1.84766483E-002,4... [内容过长已截断，完整长度:12597]\", \"chunk_length\": 6, \"chunk_offset\": 1213}, {\"id\": 9, \"file_id\": 1, \"chunk_text\": \"电子产品及通信设备、电器机械、普通机械、仪器仪表、保安器材、计算机软硬件的技术开发、技术服务、技术咨询、销售;承接光纤及数据信号智能布线、楼宇自动化管理工程;承接计算机网络工程;安全技术防范工程的设计、施工;技术进出口、货物进出口、代理进出口。(企业依法自主选择经营项目,开展经营活动;依法须经批准的项目,经相关部门批准后依批准的内容开展经营活动;不得从事本市产业政策禁止和限制类项目的经营活动。)\", \"vector_data\": \"[5.00907563E-002,-4.02165763E-002,7.83675443E-003,-8.60009808E-003,1.50514469E-002,-4.09889827E-003,3.63224484E-002,1.23090427E-002,-1.93317533E-002,-2.50660442E-002,-1.27582522E-002,8.12663045E-003,-... [内容过长已截断，完整长度:12608]\", \"chunk_length\": 203, \"chunk_offset\": 1219}]', '2025-05-08 17:53:29', '2025-05-08 17:53:29');
INSERT INTO `selected_tables` (`id`, `data_source_id`, `table_name`, `table_description`, `table_schema`, `sample_data`, `created_at`, `updated_at`) VALUES ('9e758151-1ca0-4e31-a510-81e09ddc3d6c', 'a1b2c3d4-e5f6-4a5b-9c3d-0e9f8a7b6c5d', 'FINRISK_BASE_INFO', '', '{\"ID\": {\"nullable\": \"N\", \"data_type\": \"NUMBER\", \"data_length\": 22}, \"BGSL\": {\"nullable\": \"Y\", \"data_type\": \"VARCHAR2\", \"data_length\": 10}, \"LSRZZ\": {\"nullable\": \"Y\", \"data_type\": \"VARCHAR2\", \"data_length\": 100}, \"FMYQSL\": {\"nullable\": \"Y\", \"data_type\": \"VARCHAR2\", \"data_length\": 10}, \"IS_SXQY\": {\"nullable\": \"Y\", \"data_type\": \"VARCHAR2\", \"data_length\": 10}, \"LSRXYDJ\": {\"nullable\": \"Y\", \"data_type\": \"VARCHAR2\", \"data_length\": 100}, \"REG_ADDR\": {\"nullable\": \"Y\", \"data_type\": \"VARCHAR2\", \"data_length\": 400}, \"REG_DATE\": {\"nullable\": \"Y\", \"data_type\": \"DATE\", \"data_length\": 7}, \"BIZ_SCOPE\": {\"nullable\": \"Y\", \"data_type\": \"VARCHAR2\", \"data_length\": 4000}, \"IS_SXBZXR\": {\"nullable\": \"Y\", \"data_type\": \"VARCHAR2\", \"data_length\": 10}, \"BIZ_STATUS\": {\"nullable\": \"Y\", \"data_type\": \"VARCHAR2\", \"data_length\": 100}, \"IS_WDQKTGG\": {\"nullable\": \"Y\", \"data_type\": \"VARCHAR2\", \"data_length\": 10}, \"LEGAL_PHONE\": {\"nullable\": \"Y\", \"data_type\": \"VARCHAR2\", \"data_length\": 100}, \"REG_CAPITAL\": {\"nullable\": \"Y\", \"data_type\": \"NUMBER\", \"data_length\": 22}, \"COMPANY_NAME\": {\"nullable\": \"Y\", \"data_type\": \"VARCHAR2\", \"data_length\": 400}, \"COMPANY_TYPE\": {\"nullable\": \"Y\", \"data_type\": \"VARCHAR2\", \"data_length\": 100}, \"LEGAL_PERSON\": {\"nullable\": \"Y\", \"data_type\": \"VARCHAR2\", \"data_length\": 100}}', '[{\"id\": 171, \"bgsl\": \"0\", \"lsrzz\": \"一般纳税人\", \"fmyqsl\": \"0\", \"is_sxqy\": \"否\", \"lsrxydj\": \"A级信用\", \"reg_addr\": \"北京海淀区学院路21号\", \"reg_date\": \"2020-07-23T00:00:00\", \"biz_scope\": \"电子产品及通信设备\", \"is_sxbzxr\": \"否\", \"biz_status\": \"正常\", \"is_wdqktgg\": \"否\", \"legal_phone\": \"139110\", \"reg_capital\": \"11165000\", \"company_name\": \"ABC公司\", \"company_type\": \"有限责任公司(自然人投资或控股)\", \"legal_person\": \"David\"}, {\"id\": 172, \"bgsl\": \"0\", \"lsrzz\": \"一般纳税人\", \"fmyqsl\": \"0\", \"is_sxqy\": \"否\", \"lsrxydj\": \"B级信用\", \"reg_addr\": \"上海\", \"reg_date\": \"2019-08-02T00:00:00\", \"biz_scope\": \"电子元件\", \"is_sxbzxr\": \"否\", \"biz_status\": \"正常\", \"is_wdqktgg\": \"否\", \"legal_phone\": \"139110\", \"reg_capital\": \"2000000\", \"company_name\": \"DEF7公司\", \"company_type\": \"有限责任公司(自然人投资或控股)\", \"legal_person\": \"小王\"}, {\"id\": 173, \"bgsl\": \"0\", \"lsrzz\": \"一般纳税人\", \"fmyqsl\": \"0\", \"is_sxqy\": \"否\", \"lsrxydj\": \"A级信用\", \"reg_addr\": \"河北石家庄\", \"reg_date\": \"2020-09-01T00:00:00\", \"biz_scope\": \"家装\", \"is_sxbzxr\": \"否\", \"biz_status\": \"正常\", \"is_wdqktgg\": \"否\", \"legal_phone\": \"139110\", \"reg_capital\": \"1000000\", \"company_name\": \"DEF6公司\", \"company_type\": \"有限责任公司(自然人投资或控股)\", \"legal_person\": \"小李\"}]', '2025-05-08 17:53:26', '2025-05-08 17:53:26');
INSERT INTO `selected_tables` (`id`, `data_source_id`, `table_name`, `table_description`, `table_schema`, `sample_data`, `created_at`, `updated_at`) VALUES ('b348e84e-079f-42d6-9f0c-4f1e5d1ee1ec', 'a1b2c3d4-e5f6-4a5b-9c3d-0e9f8a7b6c5d', 'FINRISK_FILES', '', '{\"ID\": {\"nullable\": \"N\", \"data_type\": \"NUMBER\", \"data_length\": 22}, \"FILE_VEC\": {\"nullable\": \"Y\", \"data_type\": \"VECTOR\", \"data_length\": 8200}, \"ACTIVE_YN\": {\"nullable\": \"Y\", \"data_type\": \"VARCHAR2\", \"data_length\": 10}, \"FILE_BLOB\": {\"nullable\": \"Y\", \"data_type\": \"BLOB\", \"data_length\": 4000}, \"FILE_NAME\": {\"nullable\": \"Y\", \"data_type\": \"VARCHAR2\", \"data_length\": 4000}, \"COMPANY_ID\": {\"nullable\": \"Y\", \"data_type\": \"NUMBER\", \"data_length\": 22}, \"UPDATED_ON\": {\"nullable\": \"Y\", \"data_type\": \"DATE\", \"data_length\": 7}, \"FILE_CONTENT\": {\"nullable\": \"Y\", \"data_type\": \"CLOB\", \"data_length\": 4000}, \"FILE_MIME_TYPE\": {\"nullable\": \"Y\", \"data_type\": \"VARCHAR2\", \"data_length\": 100}}', '[{\"id\": 1, \"file_vec\": null, \"active_yn\": \"Y\", \"file_blob\": \"b\'PK\\\\x03\\\\x04\\\\x14\\\\x00\\\\x06\\\\x00\\\\x08\\\\x00\\\\x00\\\\x00!\\\\x00\\\\xa2\\\\xb0\\\\x06~|\\\\x01\\\\x00\\\\x00T\\\\x06\\\\x00\\\\x00\\\\x13\\\\x00\\\\x08\\\\x02[Content_Types].xml \\\\xa2\\\\x04\\\\x02(\\\\xa0\\\\x00\\\\x02\\\\x00\\\\x00\\\\x00\\\\x00\\\\x00\\\\x00\\\\x00\\\\x00\\\\x00\\\\x00\\\\x00\\\\x00\\\\x0... [内容过长已截断，完整长度:628736]\", \"file_name\": \"ABC公司企业基础信用报告20240315.docx\", \"company_id\": 171, \"updated_on\": \"2024-06-18T15:59:18\", \"file_content\": \"\\n\\n\\n\\n当前版本：v2.0\\n\\n\\n\\nABC公司2024年3月15日\\n\\n\\n\\n报告生成日期：2024-03-15\\n\\n\\n\\n注：本报告不得以任何形式复制全部或部分内容\\n\\n\\n\\n \\n\\n\\n\\n\\n\\n\\n\\n\\n企业基础信用报告 v2.0\\n\\n\\n\\n声 明\\n\\n\\n\\n 本报告由发现公司（以下简称本公司）提供，仅供特定金融机构使用。本公司恪守公正、独立、可观的原则，为企业提供专业信用评估报告。未经本报告主体用户授权同意，其他用户无法查... [内容过长已截断，完整长度:4305]\", \"file_mime_type\": \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\"}, {\"id\": 21, \"file_vec\": null, \"active_yn\": \"Y\", \"file_blob\": \"b\'PK\\\\x03\\\\x04\\\\x14\\\\x00\\\\x06\\\\x00\\\\x08\\\\x00\\\\x00\\\\x00!\\\\x00\\\\xa2\\\\xb0\\\\x06~|\\\\x01\\\\x00\\\\x00T\\\\x06\\\\x00\\\\x00\\\\x13\\\\x00\\\\x08\\\\x02[Content_Types].xml \\\\xa2\\\\x04\\\\x02(\\\\xa0\\\\x00\\\\x02\\\\x00\\\\x00\\\\x00\\\\x00\\\\x00\\\\x00\\\\x00\\\\x00\\\\x00\\\\x00\\\\x00\\\\x00\\\\x0... [内容过长已截断，完整长度:629846]\", \"file_name\": \"GHI公司企业基础信用报告.docx\", \"company_id\": 180, \"updated_on\": \"2024-06-19T01:53:43\", \"file_content\": \"\\n\\n\\n\\n当前版本：v2.0\\n\\n\\n\\nGHI公司2024年3月15日\\n\\n\\n\\n报告生成日期：2024-03-15\\n\\n\\n\\n注：本报告不得以任何形式复制全部或部分内容\\n\\n\\n\\n \\n\\n\\n\\n\\n\\n\\n\\n\\n企业基础信用报告 v2.0\\n\\n\\n\\n声 明\\n\\n\\n\\n 本报告由发现公司（以下简称本公司）提供，仅供特定金融机构使用。本公司恪守公正、独立、可观的原则，为企业提供专业信用评估报告。未经本报告主体用户授权同意，其他用户无法查... [内容过长已截断，完整长度:4305]\", \"file_mime_type\": \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\"}, {\"id\": 22, \"file_vec\": null, \"active_yn\": \"Y\", \"file_blob\": \"b\'PK\\\\x03\\\\x04\\\\x14\\\\x00\\\\x06\\\\x00\\\\x08\\\\x00\\\\x00\\\\x00!\\\\x00\\\\xa2\\\\xb0\\\\x06~|\\\\x01\\\\x00\\\\x00T\\\\x06\\\\x00\\\\x00\\\\x13\\\\x00\\\\x08\\\\x02[Content_Types].xml \\\\xa2\\\\x04\\\\x02(\\\\xa0\\\\x00\\\\x02\\\\x00\\\\x00\\\\x00\\\\x00\\\\x00\\\\x00\\\\x00\\\\x00\\\\x00\\\\x00\\\\x00\\\\x00\\\\x0... [内容过长已截断，完整长度:630274]\", \"file_name\": \"DEF5公司企业基础信用报告.docx\", \"company_id\": 174, \"updated_on\": \"2024-06-19T01:54:09\", \"file_content\": \"\\n\\n\\n\\n当前版本：v2.0\\n\\n\\n\\nDEF5公司2024年3月17日\\n\\n\\n\\n报告生成日期：2024-03-17\\n\\n\\n\\n注：本报告不得以任何形式复制全部或部分内容\\n\\n\\n\\n \\n\\n\\n\\n\\n\\n\\n\\n\\n企业基础信用报告 v2.0\\n\\n\\n\\n声 明\\n\\n\\n\\n 本报告由发现公司（以下简称本公司）提供，仅供特定金融机构使用。本公司恪守公正、独立、可观的原则，为企业提供专业信用评估报告。未经本报告主体用户授权同意，其他用户无法... [内容过长已截断，完整长度:4306]\", \"file_mime_type\": \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\"}]', '2025-05-08 17:53:37', '2025-05-08 17:53:37');
INSERT INTO `selected_tables` (`id`, `data_source_id`, `table_name`, `table_description`, `table_schema`, `sample_data`, `created_at`, `updated_at`) VALUES ('c1738a18-5dea-4131-b7e1-97eb5a8dcc47', 'a1b2c3d4-e5f6-4a5b-9c3d-0e9f8a7b6c5d', 'FINRISK_BANK_ACCOUNTS', '', '{\"ID\": {\"nullable\": \"Y\", \"data_type\": \"NUMBER\", \"data_length\": 22}, \"NAME\": {\"nullable\": \"Y\", \"data_type\": \"VARCHAR2\", \"data_length\": 400}, \"ACCOUNT\": {\"nullable\": \"Y\", \"data_type\": \"VARCHAR2\", \"data_length\": 40}, \"BALANCE\": {\"nullable\": \"Y\", \"data_type\": \"NUMBER\", \"data_length\": 22}}', '[{\"id\": 171, \"name\": \"ABC公司\", \"account\": \"*********\", \"balance\": \"********.62\"}, {\"id\": 172, \"name\": \"DEF7公司\", \"account\": \"*********\", \"balance\": \"2226627.71\"}, {\"id\": 173, \"name\": \"DEF6公司\", \"account\": \"*********\", \"balance\": \"********.14\"}]', '2025-05-08 17:53:23', '2025-05-08 17:53:23');
COMMIT;

-- LLM模型配置表
CREATE TABLE `llm_models` (
  `id` varchar(36) NOT NULL,
  `name` varchar(100) NOT NULL COMMENT '模型名称',
  `description` text COMMENT '模型描述',
  `model_name` varchar(100) NOT NULL COMMENT '模型标识符',
  `api_key` varchar(500) NOT NULL COMMENT 'API密钥',
  `base_url` varchar(500) DEFAULT 'https://api.openai.com/v1' COMMENT 'API基础URL',
  `config` json DEFAULT NULL COMMENT '额外配置参数',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_llm_model_name` (`name`),
  KEY `idx_llm_model_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='LLM模型配置表';

-- 插入示例模型配置（需要用户自行配置真实的API密钥）



UPDATE `tools` SET `name` = '公司基本信息查询', `description` = '查询公司的基本信息，包括注册信息、经营状态、风险信息等。使用%表示查询所有公司。', `tool_type` = 'sql', `template` = 'SELECT\n    JSON_ARRAYAGG(\n        JSON_OBJECT(\n            KEY \'公司ID\' VALUE r.id,\n            KEY \'公司名称\' VALUE r.company_name,\n            KEY \'公司类型\' VALUE r.company_type,\n            KEY \'注册日期\' VALUE r.reg_date,\n            KEY \'注册资本\' VALUE r.reg_capital,\n            KEY \'注册地址\' VALUE r.reg_addr,\n            KEY \'公司法人\' VALUE r.legal_person,\n            KEY \'法人联系电话\' VALUE r.legal_phone,\n            KEY \'纳税人资质\' VALUE r.lsrzz,\n            KEY \'纳税信用等级\' VALUE r.lsrxydj,\n            KEY \'经营范围\' VALUE r.biz_scope,\n            KEY \'经营状态\' VALUE r.biz_status,\n            KEY \'是否失信企业\' VALUE r.is_sxqy,\n            KEY \'是否是失信被执行人\' VALUE r.is_sxbzxr,\n            KEY \'是否有未到期开庭公告\' VALUE r.is_wdqktgg,\n            KEY \'近三年做为被告人数量\' VALUE r.bgsl,\n            KEY \'近一年负面舆情数量\' VALUE r.fmyqsl,\n            KEY \'相关文件\' VALUE (\n                SELECT JSON_ARRAYAGG(\n                    JSON_OBJECT(\n                        KEY \'文件名\' VALUE f.file_name,\n                        KEY \'文件链接\' VALUE f.file_url\n                    )\n                )\n                FROM FINRISK_FILES f\n                WHERE f.company_id = r.id\n            )\n        )\n        ORDER BY r.id\n        RETURNING CLOB\n    ) AS JSON_DOC\nFROM FINRISK_base_info r\nWHERE r.company_name LIKE \n    CASE \n        WHEN :company_name = \'%\' THEN \'%\'\n        ELSE :company_name\n    END\n', `parameters` = '[{\"name\": \"company_name\", \"type\": \"string\", \"default\": \"%\", \"required\": false, \"description\": \"公司名称，使用%查询所有公司\"}]' WHERE `id` = '1a2b3c4d-5e6f-7a8b-9c0d-1e2f3a4b5c6d';

-- 会话表（多轮分析）
CREATE TABLE `conversations` (
  `id` varchar(50) NOT NULL,
  `project_id` varchar(36) NOT NULL,
  `title` varchar(500) DEFAULT NULL COMMENT '会话标题',
  `context_summary` text COMMENT '会话级别的上下文摘要',
  `total_rounds` int DEFAULT 0 COMMENT '总轮次数',
  `total_tokens_used` int DEFAULT 0 COMMENT '总消耗token数',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `status` enum('ACTIVE','ARCHIVED','PAUSED') DEFAULT 'ACTIVE' COMMENT '会话状态',
  PRIMARY KEY (`id`),
  KEY `idx_project_id` (`project_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `conversations_ibfk_1` FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='多轮分析会话表';

-- 会话上下文表（用于存储结构化的上下文信息）
CREATE TABLE `conversation_contexts` (
  `id` bigint AUTO_INCREMENT PRIMARY KEY,
  `conversation_id` varchar(50) NOT NULL,
  `round_number` int NOT NULL,
  `context_type` enum('query','result','insight','data_source','tool_usage') NOT NULL COMMENT '上下文类型',
  `context_data` json NOT NULL COMMENT '上下文数据',
  `relevance_score` float DEFAULT '1.0' COMMENT '上下文相关性评分',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  KEY `idx_conversation_round` (`conversation_id`,`round_number`),
  KEY `idx_context_type` (`context_type`),
  KEY `idx_relevance_score` (`relevance_score`),
  CONSTRAINT `conversation_contexts_ibfk_1` FOREIGN KEY (`conversation_id`) REFERENCES `conversations` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=367 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='会话上下文表';


-- 为分析表添加会话相关字段
ALTER TABLE `analyses` ADD COLUMN `conversation_id` varchar(50) DEFAULT NULL COMMENT '所属会话ID';
ALTER TABLE `analyses` ADD COLUMN `round_number` int DEFAULT 1 COMMENT '轮次编号';
ALTER TABLE `analyses` ADD COLUMN `context_tokens_used` int DEFAULT 0 COMMENT '上下文消耗的token数';
ALTER TABLE `analyses` ADD COLUMN `context_summary` text COMMENT '本轮次使用的上下文摘要';
ALTER TABLE `analyses` ADD KEY `idx_conversation_id` (`conversation_id`);
ALTER TABLE `analyses` ADD KEY `idx_round_number` (`round_number`);
ALTER TABLE `analyses` ADD CONSTRAINT `analyses_ibfk_conversation` FOREIGN KEY (`conversation_id`) REFERENCES `conversations` (`id`) ON DELETE SET NULL;

-- 多轮分析功能扩展：为analyses表添加完整状态保存字段
ALTER TABLE `analyses` ADD COLUMN `analysis_steps` json DEFAULT NULL COMMENT '分析步骤详情';
ALTER TABLE `analyses` ADD COLUMN `tool_results` json DEFAULT NULL COMMENT '工具执行结果映射';
ALTER TABLE `analyses` ADD COLUMN `mcp_results` json DEFAULT NULL COMMENT 'MCP执行结果';
ALTER TABLE `analyses` ADD COLUMN `status` enum('running','completed','failed') DEFAULT 'running' COMMENT '分析状态';
ALTER TABLE `analyses` ADD COLUMN `execution_time` float DEFAULT NULL COMMENT '总执行时间(秒)';
ALTER TABLE `analyses` ADD COLUMN `context_relevance` float DEFAULT NULL COMMENT '上下文相关性评分';
ALTER TABLE `analyses` ADD COLUMN `llm_summary` text DEFAULT NULL COMMENT 'LLM生成的本轮次总结';

-- 为tool_executions表添加状态和推理字段
ALTER TABLE `tool_executions` ADD COLUMN `status` enum('running','success','failed') DEFAULT 'running' COMMENT '执行状态';
ALTER TABLE `tool_executions` ADD COLUMN `error_message` text DEFAULT NULL COMMENT '错误信息';
ALTER TABLE `tool_executions` ADD COLUMN `step_order` int DEFAULT NULL COMMENT '步骤顺序';
ALTER TABLE `tool_executions` ADD COLUMN `reasoning` text DEFAULT NULL COMMENT '执行推理过程';

-- 创建分析步骤详情表，用于存储完整的分析时间轴
CREATE TABLE `analysis_step_details` (
  `id` bigint AUTO_INCREMENT PRIMARY KEY,
  `analysis_id` varchar(36) NOT NULL COMMENT '所属分析ID',
  `step_id` varchar(100) NOT NULL COMMENT '步骤ID',
  `step_name` varchar(500) NOT NULL COMMENT '步骤名称',
  `step_type` enum('system','tool','planning','report','error','interaction') NOT NULL COMMENT '步骤类型',
  `status` enum('process','finish','error','wait') NOT NULL COMMENT '步骤状态',
  `step_order` int NOT NULL COMMENT '步骤顺序',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `execution_time` float DEFAULT NULL COMMENT '执行时间(毫秒)',
  `description` text DEFAULT NULL COMMENT '步骤描述',
  `tool_name` varchar(200) DEFAULT NULL COMMENT '工具名称(如果是工具步骤)',
  `parameters` json DEFAULT NULL COMMENT '执行参数(如果是工具步骤)',
  `has_result` tinyint(1) DEFAULT 0 COMMENT '是否有执行结果',
  `result_key` varchar(100) DEFAULT NULL COMMENT '结果键值',
  `has_intent_data` tinyint(1) DEFAULT 0 COMMENT '是否有意图分析数据',
  `has_report` tinyint(1) DEFAULT 0 COMMENT '是否有报告数据',
  `has_chart_data` tinyint(1) DEFAULT 0 COMMENT '是否有图表数据',
  `plan_data` json DEFAULT NULL COMMENT '规划数据',
  `reasoning` text DEFAULT NULL COMMENT '执行推理过程',
  `error_message` text DEFAULT NULL COMMENT '错误信息',
  `interaction_type` varchar(50) DEFAULT NULL COMMENT '交互类型: intent_confirmation/clarification/chart_display',
  `interaction_data` json DEFAULT NULL COMMENT '交互相关数据',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  FOREIGN KEY (`analysis_id`) REFERENCES `analyses`(`id`) ON DELETE CASCADE,
  INDEX `idx_analysis_step_order` (`analysis_id`, `step_order`),
  INDEX `idx_step_type` (`step_type`),
  INDEX `idx_step_status` (`status`),
  INDEX `idx_step_id` (`step_id`),
  INDEX `idx_interaction_type` (`interaction_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='分析步骤详情表';

-- 为analyses表添加性能优化索引
ALTER TABLE `analyses` ADD INDEX `idx_status` (`status`);
ALTER TABLE `analyses` ADD INDEX `idx_context_relevance` (`context_relevance`);
ALTER TABLE `analyses` ADD INDEX `idx_execution_time` (`execution_time`);

-- 为tool_executions表添加性能优化索引
ALTER TABLE `tool_executions` ADD INDEX `idx_status` (`status`);
ALTER TABLE `tool_executions` ADD INDEX `idx_step_order` (`step_order`);
ALTER TABLE `tool_executions` ADD INDEX `idx_step_id` (`step_id`);

ALTER TABLE analysis_step_details MODIFY COLUMN step_type ENUM('system','tool','planning','report','error','interaction','evaluation') NOT NULL COMMENT '步骤类型';

CREATE TABLE `analysis_templates` (
  `id` varchar(36) NOT NULL COMMENT '模板唯一标识',
  `name` varchar(100) NOT NULL COMMENT '模板名称',
  `description` text COMMENT '模板描述',
  `content` text NOT NULL COMMENT '模板内容（提示词）',
  `project_id` varchar(36) DEFAULT NULL COMMENT '所属项目ID',
  `created_by` int DEFAULT NULL COMMENT '创建人ID',
  `is_public` tinyint(1) DEFAULT '0' COMMENT '是否公开（0:私有 1:公开）',
  `is_system` tinyint(1) DEFAULT '0' COMMENT '是否系统模板（0:用户模板 1:系统模板）',
  `usage_count` int DEFAULT '0' COMMENT '使用次数',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `category` varchar(50) DEFAULT NULL COMMENT '分类',
  `tags` text COMMENT '标签',
  `template_type` varchar(20) DEFAULT 'query' COMMENT '模板类型',
  `complexity_level` varchar(20) DEFAULT NULL COMMENT '复杂度',
  `applicable_data_types` text COMMENT '适用数据类型',
  PRIMARY KEY (`id`),
  KEY `project_id` (`project_id`),
  KEY `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='分析模板表';

ALTER TABLE ai_data_qa.analysis_step_details MODIFY COLUMN step_type enum('system','tool','planning','report','error','interaction','evaluation','insight') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '步骤类型';
