services:
  app:
    # 使用预构建的统一应用镜像
    image: decisionai-demo:latest
    container_name: decisionai-demo-app
    restart: always
    ports:
      - "8301:8301"  # 后端API端口
      - "9310:9310"  # 前端Web端口
    env_file:
      - .env.prod  # 使用环境变量文件
    volumes:
      - /data/appdata/ai/daidemo/logs:/app/logs
      - /data/appdata/ai/daidemo/nginx-logs:/var/log/nginx
    # 添加extra_hosts帮助容器解析host.docker.internal (在Linux上需要)
    extra_hosts:
      - "host.docker.internal:host-gateway"