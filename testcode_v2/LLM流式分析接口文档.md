# LLM流式分析接口文档

## 接口概述

LLM流式分析接口是在原有流式分析接口基础上，引入基于LLM的任务规划和执行流程的增强版接口。相比于原有的固定流程分析，LLM流式分析接口能够根据用户查询动态规划分析步骤，更好地应对复杂多变的分析需求。

## 核心特性

- **智能规划**：基于LLM动态生成分析步骤，无需预定义流程
- **自适应执行**：执行一步，规划下一步，形成反馈循环
- **澄清机制**：支持智能交互，可在分析过程中请求用户澄清
- **上下文缓存**：支持分析中断后继续，保持分析状态
- **取消控制**：支持实时取消正在进行的分析
- **轮数控制**：可配置最大规划轮数，防止无限循环

## 基本信息

- **接口路径**：`/api/v1/analysis/llm-stream`
- **请求方式**：GET 或 POST
- **响应格式**：Server-Sent Events (text/event-stream)

## 请求参数

| 参数名称 | 参数类型 | 是否必须 | 说明 |
|---------|---------|---------|------|
| project_id | string | 是 | 项目ID，指定要在哪个项目中执行分析 |
| query | string | 是 | 查询内容，用户的分析请求描述 |
| continue_analysis_id | string | 否 | 继续分析的ID（用于澄清后继续） |
| max_planning_rounds | int | 否 | 最大规划轮数，默认25，范围1-50 |

## 请求示例

### GET 请求

```
GET /api/v1/analysis/llm-stream?project_id=proj_123&query=近三个月销售额趋势分析&max_planning_rounds=30
```

### POST 请求

```json
POST /api/v1/analysis/llm-stream
Content-Type: application/json

{
  "project_id": "proj_123",
  "query": "近三个月销售额趋势分析",
  "max_planning_rounds": 30
}
```

### 继续分析请求（澄清后继续）

```json
POST /api/v1/analysis/llm-stream
Content-Type: application/json

{
  "project_id": "proj_123",
  "query": "请详细分析2月份的销售数据",
  "continue_analysis_id": "an_12345678"
}
```

## 响应格式

接口以SSE(Server-Sent Events)格式返回数据流，每个事件的格式为：

```
data: {"event": "事件类型", "data": 事件数据}
```

## 详细分析流程

LLM流式分析接口的完整处理流程如下：

### 1. 初始化阶段

1. **接收请求**：系统接收带有project_id和query参数的请求
2. **项目验证**：验证project_id对应的项目是否存在
3. **上下文恢复**：如果指定了continue_analysis_id，尝试从内存缓存或数据库恢复分析上下文
4. **创建分析上下文**：创建或恢复ExecutionContext，保存用户查询信息
5. **发送开始事件**：
   - 事件类型：`start`
   - 数据内容：包含查询内容、最大规划轮数等信息
6. **创建/恢复分析记录**：
   - 在数据库中创建新的分析记录或恢复现有记录
   - 事件类型：`analysis_created` 或 `analysis_continued`
   - 数据内容：包含分析记录ID、查询内容和项目ID
7. **加载工具**：
   - 获取项目中所有可用的分析工具
   - 事件类型：`tools_loaded`
   - 数据内容：包含加载的工具数量和工具列表
8. **加载Schema**：
   - 加载数据库schema信息
   - 事件类型：`schema_loaded`
   - 数据内容：包含schema信息的状态

### 2. LLM规划阶段

1. **发送规划开始事件**：
   - 事件类型：`planning_started`
   - 数据内容：包含开始规划的消息
2. **规划轮数控制**：
   - 检查是否达到最大规划轮数
   - 事件类型：`planning_round`
   - 数据内容：包含当前轮数和最大轮数
3. **生成规划提示词**：
   - 根据用户查询、执行历史、可用工具和数据库类型生成提示词
4. **调用LLM进行规划**：
   - 调用LLM服务进行规划分析
5. **解析规划结果**：
   - 解析LLM返回的规划结果
   - 事件类型：`plan_created`
   - 数据内容：包含规划的动作类型、推理过程和下一步操作

### 3. 工具执行阶段

如果LLM决定执行工具：

1. **规划决策事件**：
   - 事件类型：`planning_decision`
   - 数据内容：包含选择的工具名称、步骤ID和推理过程
2. **步骤开始**：
   - 事件类型：`step_started`
   - 数据内容：包含步骤ID、工具名称和参数
3. **执行工具步骤**：
   - 调用相应的工具执行操作
   - 支持取消检查机制
4. **记录执行结果**：
   - 将执行结果添加到上下文中
   - 事件类型：`step_completed`
   - 数据内容：包含步骤ID、工具名称和执行结果
5. **特殊处理**：
   - 如果是智能交互工具，发送澄清等待事件
   - 事件类型：`clarification_waiting`
6. **返回规划阶段**：
   - 将执行结果返回给LLM进行下一步规划

### 4. 澄清处理阶段

如果LLM决定需要澄清：

1. **澄清请求**：
   - 事件类型：`clarification`
   - 数据内容：包含澄清问题列表、上下文信息和提示消息
2. **分析暂停**：
   - 保存当前分析状态到缓存
   - 等待用户提供澄清信息
3. **澄清继续**：
   - 用户通过continue_analysis_id参数提供澄清回复
   - 系统合并原始查询和澄清回复
   - 继续分析流程

### 5. 最终结果生成阶段

如果LLM决定生成最终答案或达到最大轮数：

1. **生成分析报告**：
   - 基于执行历史和结果生成最终分析报告
   - 事件类型：`report_generated`
   - 数据内容：包含生成的报告内容
2. **完成分析**：
   - 事件类型：`completed`
   - 数据内容：包含分析ID和完成信息

### 6. 错误处理和取消机制

- **错误处理**：在任何阶段发生异常时发送error事件
- **取消机制**：支持通过API取消正在进行的分析
- **轮数限制**：达到最大规划轮数时强制生成结果

## 事件类型详解

### 1. start - 分析开始

标志着分析过程的开始。

```json
{
  "event": "start",
  "data": {
    "message": "分析开始",
    "query": "近三个月销售额趋势分析",
    "max_planning_rounds": 25
  }
}
```

### 2. analysis_created - 创建分析记录

表示新的分析记录已在数据库中创建。

```json
{
  "event": "analysis_created",
  "data": {
    "id": "an_12345678",
    "query": "近三个月销售额趋势分析",
    "project_id": "proj_123"
  }
}
```

### 3. analysis_continued - 继续分析

表示从现有分析记录继续执行（澄清后继续）。

```json
{
  "event": "analysis_continued",
  "data": {
    "id": "an_12345678",
    "original_query": "近三个月销售额趋势分析",
    "clarification_response": "请详细分析2月份的销售数据",
    "enhanced_query": "近三个月销售额趋势分析，特别是详细分析2月份的销售数据",
    "clarification_rounds": 1,
    "message": "从内存缓存恢复分析上下文，继续执行"
  }
}
```

### 4. tools_loaded - 工具加载完成

表示分析所需的工具已加载完成。

```json
{
  "event": "tools_loaded",
  "data": {
    "tool_count": 5,
    "message": "已加载 5 个分析工具"
  }
}
```

### 5. schema_loaded - Schema加载完成

表示数据库Schema信息已加载完成。

```json
{
  "event": "schema_loaded",
  "data": {
    "schema_info": "Schema加载完成"
  }
}
```

### 6. planning_started - 规划开始

表示LLM规划阶段开始。

```json
{
  "event": "planning_started",
  "data": {
    "message": "开始规划分析步骤"
  }
}
```

### 7. planning_round - 规划轮数

表示当前规划轮数信息。

```json
{
  "event": "planning_round",
  "data": {
    "current_round": 3,
    "max_rounds": 25
  }
}
```

### 8. plan_created - 规划生成

表示LLM已经生成一个规划步骤。

```json
{
  "event": "plan_created",
  "data": {
    "action_type": "tool",
    "tool_name": "自动SQL查询",
    "parameters": {
      "sql": "SELECT DATE(created_at) as date, SUM(amount) as sum_amount FROM sales WHERE created_at >= DATE_SUB(NOW(), INTERVAL 3 MONTH) GROUP BY DATE(created_at) ORDER BY date"
    },
    "reasoning": "需要查询最近三个月的销售数据，按日期分组并求和，这样可以获取销售额的时间趋势。"
  }
}
```

### 9. planning_decision - 规划决策

表示LLM已选择要执行的工具。

```json
{
  "event": "planning_decision",
  "data": {
    "message": "已选择使用 自动SQL查询 工具执行分析",
    "tool_name": "自动SQL查询",
    "step_id": "step_1",
    "reasoning": "根据查询内容选择合适的工具"
  }
}
```

### 10. step_started - 分析步骤开始

表示某个具体分析步骤开始执行。

```json
{
  "event": "step_started",
  "data": {
    "step_id": "step_1",
    "tool_name": "自动SQL查询",
    "parameters": {
      "sql": "SELECT DATE(created_at) as date, SUM(amount) as sum_amount FROM sales WHERE created_at >= DATE_SUB(NOW(), INTERVAL 3 MONTH) GROUP BY DATE(created_at) ORDER BY date"
    }
  }
}
```

### 11. step_completed - 分析步骤完成

表示某个具体分析步骤已完成执行，包含执行结果。

```json
{
  "event": "step_completed",
  "data": {
    "step_id": "step_1",
    "tool_name": "自动SQL查询",
    "result": {
      "status": "success",
      "data": [
        {"date": "2023-01-01", "sum_amount": 12500},
        {"date": "2023-01-02", "sum_amount": 15200}
      ],
      "row_count": 90,
      "execution_time": "0.25s"
    }
  }
}
```

### 12. clarification - 澄清请求

表示LLM需要用户提供澄清信息。

```json
{
  "event": "clarification",
  "data": {
    "questions": [
      {
        "question": "您希望分析哪个具体的时间段？",
        "type": "date_range",
        "options": ["最近一个月", "最近三个月", "最近一年"]
      },
      {
        "question": "您关注的主要指标是什么？",
        "type": "metrics",
        "options": ["销售额", "订单数量", "客户数量"]
      }
    ],
    "context": {
      "current_analysis": "销售趋势分析",
      "available_data": "2023年1月至今的销售数据"
    },
    "message": "需要您提供更多信息以继续分析"
  }
}
```

### 13. clarification_waiting - 澄清等待

表示分析已暂停，等待用户澄清。

```json
{
  "event": "clarification_waiting",
  "data": {
    "message": "分析已暂停，等待用户提供澄清信息",
    "step_id": "step_2",
    "analysis_id": "an_12345678"
  }
}
```

### 14. max_rounds_reached - 达到最大轮数

表示已达到最大规划轮数，强制结束规划。

```json
{
  "event": "max_rounds_reached",
  "data": {
    "message": "已达到最大规划轮数 25，强制结束规划",
    "current_rounds": 25
  }
}
```

### 15. report_generated - 生成最终报告

表示最终分析报告已生成。

```json
{
  "event": "report_generated",
  "data": {
    "report": "# 近三个月销售额趋势分析\n\n## 总体情况\n\n过去三个月销售额总体呈上升趋势，平均日环比增长2.3%...",
    "forced_by_max_rounds": false
  }
}
```

### 16. completed - 分析完成

表示整个分析过程已完成。

```json
{
  "event": "completed",
  "data": {
    "id": "an_12345678",
    "message": "分析已完成",
    "cancelled": false
  }
}
```

### 17. error - 发生错误

表示分析过程中发生错误。

```json
{
  "event": "error",
  "data": {
    "message": "分析执行失败: 数据库连接错误",
    "request_id": "req_12345"
  }
}
```

## 取消分析接口

### 基本信息

- **接口路径**：`/api/v1/analysis/llm/{analysis_id}/cancel`
- **请求方式**：POST
- **响应格式**：JSON

### 请求示例

```
POST /api/v1/analysis/llm/an_12345678/cancel
```

### 响应示例

```json
{
  "code": 0,
  "message": "分析已成功取消",
  "data": {
    "analysis_id": "an_12345678",
    "cancelled_at": "2024-01-01T12:00:00Z"
  },
  "request_id": "req_12345"
}
```

## 上下文缓存管理

系统提供了上下文缓存管理接口，用于调试和监控：

### 1. 获取缓存统计信息

```
GET /api/v1/analysis/context-cache/stats
```

### 2. 获取指定分析的缓存

```
GET /api/v1/analysis/context-cache/{analysis_id}
```

### 3. 清除指定分析的缓存

```
DELETE /api/v1/analysis/context-cache/{analysis_id}
```

### 4. 清空所有缓存

```
DELETE /api/v1/analysis/context-cache
```

## 与传统流式分析接口的区别

| 特性 | 传统流式分析接口 | LLM流式分析接口 |
|------|--------------|---------------|
| 分析流程 | 固定的分析步骤 | 基于LLM动态规划的分析流程 |
| 步骤生成 | 使用固定的模板分析用户意图 | 通过LLM动态分析用户查询并规划步骤 |
| 规划执行循环 | 一次性分析生成所有步骤 | 执行一步，规划下一步，形成反馈循环 |
| 澄清机制 | 不支持 | 支持智能交互和澄清请求 |
| 上下文保持 | 不支持 | 支持分析中断后继续 |
| 取消控制 | 不支持 | 支持实时取消 |
| 轮数控制 | 不需要 | 可配置最大规划轮数 |
| 适应复杂查询 | 对于复杂查询可能效果有限 | 能更好地适应复杂多变的查询需求 |
| 处理异常情况 | 固定的错误处理逻辑 | 可以根据执行结果动态调整策略 |

## 客户端实现建议

### 1. 事件处理

客户端需要处理更多的事件类型，特别是：
- `planning_round` - 显示规划进度
- `planning_decision` - 显示LLM的决策过程
- `clarification` - 处理澄清请求
- `clarification_waiting` - 显示等待状态
- `analysis_continued` - 处理继续分析

### 2. 澄清处理

```javascript
// 处理澄清请求
case 'clarification':
  const questions = data.data.questions;
  const context = data.data.context;
  
  // 显示澄清表单
  showClarificationForm(questions, context, analysisId);
  break;

// 提交澄清回复
function submitClarification(analysisId, response) {
  fetch('/api/v1/analysis/llm-stream', {
    method: 'POST',
    headers: {'Content-Type': 'application/json'},
    body: JSON.stringify({
      project_id: projectId,
      query: response,
      continue_analysis_id: analysisId
    })
  });
}
```

### 3. 取消分析

```javascript
// 取消分析
function cancelAnalysis(analysisId) {
  fetch(`/api/v1/analysis/llm/${analysisId}/cancel`, {
    method: 'POST'
  }).then(response => response.json())
    .then(data => {
      if (data.code === 0) {
        console.log('分析已取消');
      }
    });
}
```

## 注意事项

1. **长时间运行**：LLM规划需要时间，整体分析过程可能比传统接口更长
2. **网络稳定性**：确保客户端能够处理长时间的SSE连接
3. **错误恢复**：如果LLM服务出现问题，可能需要重新开始分析
4. **成本考虑**：多次调用LLM可能会增加API调用成本
5. **缓存策略**：系统会自动缓存分析上下文，支持中断后继续
6. **轮数限制**：合理设置max_planning_rounds参数，避免过度规划
7. **澄清处理**：客户端需要实现澄清请求的UI交互
8. **取消机制**：长时间运行的分析可以通过取消接口中断