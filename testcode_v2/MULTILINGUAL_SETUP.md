# 多语言支持实施指南

## 实施完成的功能

### ✅ 已完成功能

1. **前端多语言框架搭建**
   - 集成 react-i18next 国际化框架
   - 创建完整的翻译资源文件结构
   - 支持中文（zh-CN）和英文（en-US）

2. **语言切换功能**
   - 在顶部导航栏添加语言选择器
   - 支持实时语言切换
   - 自动刷新页面确保所有组件更新

3. **用户语言偏好存储**
   - 数据库模型扩展：用户表添加 language_preference 字段
   - 后端API支持：提供语言偏好的获取和更新接口
   - 前端存储：localStorage + 服务器同步

4. **翻译资源文件**
   - `common.json`: 通用翻译（按钮、导航、消息等）
   - `auth.json`: 认证相关翻译
   - `project.json`: 项目管理翻译
   - `analysis.json`: 数据分析翻译
   - `admin.json`: 系统管理翻译

5. **Ant Design国际化**
   - 自动切换Ant Design组件的语言
   - 支持日期选择器、表格分页等组件国际化

## 使用方法

### 前端开发者

#### 1. 基础使用
```typescript
import { useTranslation } from 'react-i18next';

const MyComponent = () => {
  const { t } = useTranslation();
  
  return (
    <div>
      <h1>{t('common:navigation.dashboard')}</h1>
      <button>{t('common:buttons.save')}</button>
    </div>
  );
};
```

#### 2. 带参数的翻译
```typescript
// 翻译文件中: "welcome": "欢迎，{{name}}！"
const message = t('auth:welcome', { name: 'John' });
```

#### 3. 命名空间使用
```typescript
// 使用多个命名空间
const { t } = useTranslation(['common', 'project']);

// 使用
t('common:buttons.save')    // 通用按钮
t('project:list.title')     // 项目相关
```

### 添加新翻译

#### 1. 在翻译文件中添加键值对
```json
// zh-CN/common.json
{
  "newFeature": {
    "title": "新功能",
    "description": "这是一个新功能的描述"
  }
}

// en-US/common.json  
{
  "newFeature": {
    "title": "New Feature",
    "description": "This is a description of the new feature"
  }
}
```

#### 2. 在组件中使用
```typescript
const title = t('common:newFeature.title');
const desc = t('common:newFeature.description');
```

## 数据库迁移

在生产环境部署前，需要运行数据库迁移：

```sql
-- 执行迁移脚本
mysql -u username -p database_name < migrations/add_user_language_preference.sql
```

## API接口

### 更新语言偏好
```http
PUT /api/v1/users/language-preference
Content-Type: application/json
Authorization: Bearer <token>

{
  "language": "en-US"
}
```

### 获取语言偏好
```http
GET /api/v1/users/language-preference
Authorization: Bearer <token>
```

## 配置说明

### 支持的语言代码
- `zh-CN`: 简体中文
- `en-US`: 美式英语

### 默认语言
- 系统默认：中文（zh-CN）
- 新用户默认：中文（zh-CN）

## 扩展指南

### 添加新语言（如日语）

1. **创建翻译文件**
```bash
mkdir frontend/src/i18n/locales/ja-JP
# 复制并翻译所有JSON文件
```

2. **更新语言配置**
```typescript
// frontend/src/i18n/index.ts
import jaJP from './locales/ja-JP';

const resources = {
  'zh-CN': zhCN,
  'en-US': enUS,
  'ja-JP': jaJP,  // 新增
};
```

3. **更新语言切换器**
```typescript
// frontend/src/components/LanguageSwitch.tsx
const languages = [
  { key: 'zh-CN', label: '中文', flag: '🇨🇳' },
  { key: 'en-US', label: 'English', flag: '🇺🇸' },
  { key: 'ja-JP', label: '日本語', flag: '🇯🇵' },  // 新增
];
```

4. **更新后端验证**
```python
# app/api/v1/endpoints/users.py
valid_languages = ['zh-CN', 'en-US', 'ja-JP']
```

## 注意事项

1. **翻译键命名规范**
   - 使用有意义的层级结构
   - 保持一致的命名风格
   - 避免过深的嵌套

2. **性能优化**
   - 翻译文件会被打包到主bundle中
   - 考虑按需加载大型翻译文件
   - 使用React.memo优化频繁切换语言的组件

3. **测试要点**
   - 测试所有语言的UI布局
   - 验证长文本的显示效果
   - 确保RTL语言的适配（如需要）

4. **SEO考虑**
   - 当前实现是客户端国际化
   - 如需SEO优化，考虑服务端渲染（SSR）

## 部署清单

- [ ] 执行数据库迁移脚本
- [ ] 更新环境变量（如需要）
- [ ] 测试所有语言的功能
- [ ] 验证用户语言偏好同步
- [ ] 检查Ant Design组件国际化
- [ ] 测试语言切换的用户体验

## 故障排除

### 翻译不显示
1. 检查翻译键是否正确
2. 确认命名空间是否加载
3. 查看浏览器控制台错误

### 语言切换不生效
1. 检查i18n配置是否正确
2. 确认localStorage权限
3. 验证API接口连通性

### Ant Design组件未国际化
1. 检查ConfigProvider配置
2. 确认locale文件导入
3. 验证语言代码映射