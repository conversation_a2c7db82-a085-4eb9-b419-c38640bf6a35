# 数据分析流式接口系统（DecisionAI 2.0）详细文档

## 目录

1. [项目概述](#项目概述)
2. [系统架构](#系统架构)
3. [核心功能](#核心功能)
   1. [流式分析接口](#流式分析接口)
   2. [多阶段分析流程](#多阶段分析流程)
   3. [工具系统](#工具系统)
   4. [工具结果展示格式](#工具结果展示格式)
   5. [项目与数据源管理](#项目与数据源管理)
4. [技术栈](#技术栈)
5. [数据模型](#数据模型)
6. [API接口](#api接口)
7. [前端界面](#前端界面)
8. [项目结构](#项目结构)
9. [日志系统](#日志系统)
10. [部署指南](#部署指南)
11. [开发指南](#开发指南)

## 相关文档

- **[流式分析接口文档.md](流式分析接口文档.md)** - 传统流式分析接口的详细文档
- **[LLM流式分析接口文档.md](LLM流式分析接口文档.md)** - LLM智能流式分析接口的详细文档
- **[MODIFICATION_SUMMARY.md](MODIFICATION_SUMMARY.md)** - 自动SQL查询工具的修改记录
- **[DISPLAY_FORMAT_README.md](DISPLAY_FORMAT_README.md)** - 工具结果展示格式说明

## 项目概述

DecisionAI 2.0 是一个基于FastAPI和React开发的数据分析系统，其核心功能是提供流式分析接口。该接口允许用户通过自然语言查询进行数据分析，并通过Server-Sent Events (SSE)方式实时接收分析过程中各个步骤的结果。

系统主要针对金融风险分析场景设计，支持分析公司基本信息、资金流转、风险指标等数据，但其架构具有通用性，可扩展到其他领域的数据分析需求。

主要特点：
- 自然语言输入，智能转换为分析步骤
- 流式返回分析过程，提供实时反馈
- 多种数据展示格式，优化用户体验
- 可扩展的工具体系，支持不同类型的分析需求
- 多项目、多数据源管理，适应不同业务场景

## 系统架构

系统采用前后端分离的现代化架构：

### 后端架构

```
FastAPI Application
├── Core (配置、中间件、异常处理)
├── API Routes (RESTful endpoints)
├── Services
│   ├── 分析服务 (AnalyzerService)
│   ├── LLM服务 (OpenAI)
│   ├── SQL生成器
│   └── 结果格式化器
├── Models (数据库模型)
├── Schemas (请求/响应模式)
└── Database Layer (SQLAlchemy)
```

### 前端架构

```
React Application
├── Pages
│   ├── Dashboard
│   ├── Analysis
│   ├── Projects
│   ├── Data Sources
│   └── Tools
├── Components
│   ├── ToolResultRenderer
│   ├── DataTable
│   └── Charts
├── Services (API调用)
└── Utils (工具函数)
```

### 数据流程

```
用户查询 → 分析意图解析 → 生成分析步骤 → 顺序执行工具 → 实时返回结果 → 生成最终报告
```

## 核心功能

### 流式分析接口

系统提供两种流式分析接口，都允许客户端通过GET或POST请求发起分析任务，并以SSE方式实时接收分析过程中的各个步骤结果。

#### 1. 传统流式分析接口

**接口基本信息**：
- **路径**：`/api/v1/analysis/stream`
- **请求方式**：GET 或 POST
- **响应格式**：Server-Sent Events (text/event-stream)
- **特点**：使用固定的分析步骤，适合标准化的分析需求

#### 2. LLM流式分析接口

**接口基本信息**：
- **路径**：`/api/v1/analysis/llm-stream`
- **请求方式**：GET 或 POST
- **响应格式**：Server-Sent Events (text/event-stream)
- **特点**：基于LLM动态规划的智能分析流程，能够根据用户查询动态规划分析步骤，更好地应对复杂多变的分析需求

**LLM流式分析的优势**：
- **智能规划**：根据用户查询动态生成分析步骤
- **自适应流程**：执行一步，规划下一步，形成反馈循环
- **复杂查询支持**：能更好地适应复杂多变的查询需求
- **异常处理**：可以根据执行结果动态调整策略

**请求参数**：
| 参数名称 | 参数类型 | 是否必须 | 说明 |
|---------|---------|---------|------|
| project_id | string | 是 | 项目ID，指定要在哪个项目中执行分析 |
| query | string | 是 | 查询内容，用户的分析请求描述 |
| continue_analysis_id | string | 否 | 继续分析的ID（用于澄清后继续） |
| max_planning_rounds | int | 否 | 最大规划轮数，默认25，范围1-50 |

**事件类型**：
- `start` - 分析开始
- `analysis_created` - 创建分析记录
- `tools_loaded` - 工具加载完成
- `schema_loaded` - Schema加载完成
- `planning_started` - 规划开始
- `plan_created` - LLM生成分析计划
- `step_started` - 分析步骤开始
- `step_completed` - 分析步骤完成
- `report_generated` - 生成最终报告
- `completed` - 分析完成
- `error` - 发生错误

### 多阶段分析流程

分析过程分为多个阶段，每个阶段都会实时向客户端发送相应事件：

#### 1. 初始化阶段
- 接收请求并验证项目
- 创建分析服务
- 设置HTTP头（适合SSE的配置）

#### 2. 分析执行阶段
- 发送开始事件
- 创建分析记录
- 加载可用工具
- 初始化SQL执行器
- 分析用户意图（识别查询意图和所需分析步骤）

#### 3. 步骤执行阶段
- 对每个步骤发送开始事件
- 处理自动SQL查询（如有）
- 执行工具步骤
- 返回步骤结果

#### 4. 报告生成阶段
- 基于所有步骤结果生成综合报告
- 返回完整报告
- 发送分析完成事件

#### 5. 错误处理阶段
- 记录错误日志
- 发送错误事件

### 工具系统

系统支持多种类型的分析工具，用于执行不同的分析任务：

#### 工具类型
- **SQL查询工具**：执行SQL获取原始数据
- **数据聚合工具**：对数据进行分组和聚合
- **趋势分析工具**：分析数据随时间的变化趋势
- **差异分析工具**：计算不同组之间的差异
- **相关性分析工具**：计算变量之间的相关性
- **数据可视化工具**：生成数据可视化图表
- **异常检测工具**：识别数据中的异常点
- **预测模型工具**：预测未来数据趋势

#### 工具结构
每个工具包含以下信息：
- 工具名称和描述
- 工具类型（SQL、API、Python等）
- 工具模板（如SQL模板）
- 参数定义（名称、类型、描述等）
- 结果展示格式

### 工具结果展示格式

为提高不同类型工具执行结果的可读性，系统支持多种结果展示格式：

1. **JSON格式** (`json`)：默认展示格式，原始JSON数据
2. **表格格式** (`table`)：适用于SQL查询结果，自动提取列名和数据行
3. **图形格式** (`graph`)：适用于图数据查询，自动提取节点和边信息
4. **文本格式** (`text`)：适用于向量检索等返回文本内容的工具
5. **图表格式** (`chart`)：适用于需要绘制图表的数据

各工具类型推荐的展示格式：

| 工具类型 | 推荐展示格式 | 使用场景                           |
|---------|------------|----------------------------------|
| SQL     | table      | 公司基本信息查询、负面新闻分析等     |
| GRAPH   | graph      | 资金流转分析、关系网络分析等        |
| VECTOR  | text       | 纳税情况分析、文档相似度检索等      |
| CUSTOM  | json       | 自定义工具（可根据实际情况选择）    |

### 项目与数据源管理

系统支持多项目、多数据源的管理：

- **项目管理**：创建不同分析项目，如金融风险分析、市场调研等
- **数据源管理**：配置多种类型的数据源
  - 支持Oracle、MySQL、PostgreSQL、SQL Server等数据库
  - 支持HTTP API等非数据库数据源
- **工具管理**：为每个项目配置特定的分析工具

## 技术栈

### 后端技术

- **Web框架**：FastAPI
- **ORM**：SQLAlchemy
- **数据验证**：Pydantic
- **服务器**：Uvicorn
- **AI/LLM**：OpenAI
- **数据处理**：Pandas、NumPy
- **数据库驱动**：
  - cx-Oracle
  - PyMySQL
  - psycopg2
  - pyodbc

### 前端技术

- **框架**：React
- **语言**：TypeScript
- **UI库**：Ant Design
- **路由**：React Router
- **状态管理**：React Context API
- **HTTP客户端**：Axios
- **数据可视化**：ECharts
- **Markdown渲染**：React Markdown

### 数据库支持

- MySQL/MariaDB
- Oracle
- PostgreSQL
- Microsoft SQL Server

## 数据模型

系统的主要数据模型包括：

### 1. 项目(Projects)
```sql
CREATE TABLE IF NOT EXISTS projects (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '项目名称',
    description TEXT COMMENT '项目描述',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 2. 数据源(DataSources)
```sql
CREATE TABLE IF NOT EXISTS data_sources (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '数据源名称',
    description TEXT COMMENT '数据源描述',
    type ENUM('oracle', 'mysql', 'postgresql', 'mssql', 'http_api', 'other') NOT NULL,
    config JSON NOT NULL COMMENT '数据源配置',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    project_id VARCHAR(36) NOT NULL COMMENT '所属项目ID',
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
);
```

### 3. 工具(Tools)
```sql
CREATE TABLE IF NOT EXISTS tools (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '工具名称',
    description TEXT COMMENT '工具描述',
    tool_type ENUM('sql', 'api', 'python', 'graph', 'vector', 'custom') NOT NULL,
    template TEXT NOT NULL COMMENT '工具模板',
    parameters JSON NOT NULL COMMENT '工具参数定义',
    display_format VARCHAR(20) DEFAULT 'json' COMMENT '结果展示格式',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    data_source_id VARCHAR(36) NOT NULL COMMENT '所属数据源ID',
    FOREIGN KEY (data_source_id) REFERENCES data_sources(id) ON DELETE CASCADE
);
```

### 4. 分析记录(Analyses)
```sql
CREATE TABLE IF NOT EXISTS analyses (
    id VARCHAR(36) PRIMARY KEY,
    query TEXT NOT NULL COMMENT '用户查询',
    intent_analysis JSON COMMENT '意图分析结果',
    result TEXT COMMENT '分析报告',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    project_id VARCHAR(36) NOT NULL COMMENT '所属项目ID',
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
);
```

### 5. 工具执行记录(ToolExecutions)
```sql
CREATE TABLE IF NOT EXISTS tool_executions (
    id VARCHAR(36) PRIMARY KEY,
    parameters JSON NOT NULL COMMENT '执行参数',
    result JSON COMMENT '执行结果',
    execution_time FLOAT COMMENT '执行时间(秒)',
    step_id VARCHAR(50) NULL COMMENT '分析步骤ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    analysis_id VARCHAR(36) NOT NULL COMMENT '所属分析ID',
    tool_id VARCHAR(36) NOT NULL COMMENT '工具ID',
    FOREIGN KEY (analysis_id) REFERENCES analyses(id) ON DELETE CASCADE,
    FOREIGN KEY (tool_id) REFERENCES tools(id) ON DELETE CASCADE
);
```

## API接口

### 分析相关

1. **执行流式分析 (GET)**
   - 路径: `/api/v1/analysis/stream`
   - 参数: `project_id`, `query`
   - 响应: Server-Sent Events流
   - 说明: 传统的固定步骤流式分析接口

2. **执行流式分析 (POST)**
   - 路径: `/api/v1/analysis/stream`
   - 请求体: 包含`project_id`和`query`的JSON
   - 响应: Server-Sent Events流

3. **执行LLM流式分析 (GET)**
   - 路径: `/api/v1/analysis/llm-stream`
   - 参数: `project_id`, `query`, `continue_analysis_id` (可选), `max_planning_rounds` (可选)
   - 响应: Server-Sent Events流
   - 说明: 基于LLM动态规划的智能分析接口，支持复杂查询和自适应分析流程

4. **执行LLM流式分析 (POST)**
   - 路径: `/api/v1/analysis/llm-stream`
   - 请求体: 包含`project_id`、`query`等参数的JSON
   - 响应: Server-Sent Events流

5. **取消LLM分析**
   - 路径: `/api/v1/analysis/llm/{analysis_id}/cancel`
   - 方法: POST
   - 响应: 取消操作结果

6. **获取分析记录列表**
   - 路径: `/api/v1/analysis/`
   - 参数: `project_id` (可选), `skip`, `limit`
   - 响应: 分析记录列表

7. **获取分析详情**
   - 路径: `/api/v1/analysis/{analysis_id}`
   - 响应: 分析记录详情

8. **删除分析记录**
   - 路径: `/api/v1/analysis/{analysis_id}`
   - 方法: DELETE
   - 响应: 操作结果

9. **获取工具执行记录**
   - 路径: `/api/v1/analysis/{analysis_id}/executions`
   - 响应: 工具执行记录列表

### 项目管理

1. **获取项目列表**
   - 路径: `/api/v1/projects/`
   - 参数: `skip`, `limit`
   - 响应: 项目列表

2. **创建项目**
   - 路径: `/api/v1/projects/`
   - 方法: POST
   - 请求体: 项目信息
   - 响应: 创建的项目

3. **获取项目详情**
   - 路径: `/api/v1/projects/{project_id}`
   - 响应: 项目详情

4. **更新项目**
   - 路径: `/api/v1/projects/{project_id}`
   - 方法: PUT
   - 请求体: 更新的项目信息
   - 响应: 更新后的项目

5. **删除项目**
   - 路径: `/api/v1/projects/{project_id}`
   - 方法: DELETE
   - 响应: 操作结果

### 工具和数据源相关接口

1. **获取工具列表**
   - 路径: `/api/v1/tools/`
   - 参数: `data_source_id` (可选), `skip`, `limit`
   - 响应: 工具列表

2. **创建工具**
   - 路径: `/api/v1/tools/`
   - 方法: POST
   - 请求体: 工具信息
   - 响应: 创建的工具

3. **获取数据源列表**
   - 路径: `/api/v1/datasources/`
   - 参数: `project_id` (可选), `skip`, `limit`
   - 响应: 数据源列表

4. **创建数据源**
   - 路径: `/api/v1/datasources/`
   - 方法: POST
   - 请求体: 数据源信息
   - 响应: 创建的数据源

## 前端界面

前端界面设计基于React和Ant Design，主要包括以下页面：

### 1. 首页(Dashboard)
- 系统概览信息
- 数据统计和图表
- 快速访问链接

### 2. 数据分析页面(Analysis)
- 查询输入框
- 项目选择下拉框
- 实时分析过程展示区
  - 步骤时间轴
  - 当前执行步骤状态
  - 各步骤执行结果
- 最终报告展示区
  - 文本摘要
  - 数据表格
  - 图表可视化

### 3. 分析历史页面(AnalysisHistory)
- 历史分析记录列表
- 筛选和排序选项
- 查看详情和删除操作

### 4. 项目管理页面(ProjectList)
- 项目列表
- 创建/编辑/删除项目操作
- 项目详情视图

### 5. 数据源管理页面(DataSourceList)
- 数据源列表
- 创建/编辑/删除数据源操作
- 测试连接功能

### 6. 工具管理页面(ToolList)
- 工具列表
- 创建/编辑/删除工具操作
- 工具详情和测试功能

## 项目结构

```
testcode_v2/
├── app/                           # 后端应用
│   ├── api/                       # API路由
│   │   └── v1/                    # v1版本API
│   │       ├── endpoints/         # 各API端点
│   │       │   ├── analysis.py    # 分析相关API
│   │       │   ├── projects.py    # 项目相关API
│   │       │   ├── tools.py       # 工具相关API
│   │       │   └── data_sources.py # 数据源相关API
│   │       └── router.py          # 总路由配置
│   ├── core/                      # 核心配置
│   │   ├── config.py              # 应用配置
│   │   ├── exceptions.py          # 异常处理
│   │   ├── logger.py              # 日志系统
│   │   └── middleware/            # 中间件
│   ├── db/                        # 数据库配置
│   │   └── session.py             # 数据库会话
│   ├── models/                    # 数据库模型
│   ├── schemas/                   # 请求/响应模式
│   ├── services/                  # 业务服务
│   │   ├── analyzer.py            # 分析服务
│   │   ├── llm/                   # LLM服务
│   │   └── executor/              # 工具执行器
│   └── utils/                     # 工具函数
├── frontend/                      # 前端应用
│   ├── public/                    # 静态资源
│   ├── src/                       # 源代码
│   │   ├── components/            # 组件
│   │   ├── pages/                 # 页面
│   │   │   ├── analysis/          # 分析相关页面
│   │   │   ├── project/           # 项目相关页面
│   │   │   ├── tool/              # 工具相关页面
│   │   │   └── datasource/        # 数据源相关页面
│   │   ├── services/              # API服务
│   │   └── utils/                 # 工具函数
│   ├── package.json               # 依赖配置
│   └── tsconfig.json              # TypeScript配置
├── logs/                          # 日志文件
│   ├── app/                       # 应用日志
│   ├── sql/                       # SQL日志
│   └── analysis/                  # 分析日志（按项目ID分组）
├── schema_info.json               # 数据库架构信息
├── schema_description.md          # 数据库描述文档
├── DISPLAY_FORMAT_README.md       # 展示格式说明
├── requirements.txt               # Python依赖
├── init_database.sql              # 数据库初始化脚本
├── main.py                        # 应用入口
└── run.py                         # 启动脚本
```

## 日志系统

系统实现了完善的日志管理机制，包括应用日志、SQL日志和分析日志三个部分。

### 日志架构

#### 1. 应用日志 (AppLogger)
- **位置**: `logs/app/`
- **文件命名**: `app.YYYY-MM-DD.log`
- **内容**: 记录系统运行状态、错误信息、调试信息等
- **轮转策略**: 按天轮转，保留30天

#### 2. SQL日志 (SQLLogger)
- **位置**: `logs/sql/`
- **文件命名**: `sql.YYYY-MM-DD.log`
- **内容**: 记录所有执行的SQL语句和参数
- **轮转策略**: 按天轮转，保留30天
- **配置控制**: 通过`LOG_PRINT_SQL`环境变量控制是否记录

#### 3. 分析日志 (AnalysisLogger) - 新增
- **位置**: `logs/analysis/{project_id}/`
- **文件命名**: `analysis.YYYY-MM-DD.log`
- **内容**: 记录整个分析流程的详细执行过程
- **轮转策略**: 按天轮转，保留30天
- **特点**: 
  - 基于项目ID分隔，每个项目有独立的日志目录
  - 记录分析的完整生命周期
  - 包含执行时间、步骤详情、错误信息等

### 分析日志系统详解

#### 功能特性
1. **项目级隔离**: 每个项目的分析日志存储在独立目录中，避免日志混杂
2. **按天分割**: 自动按日期创建新文件，防止单个日志文件过大导致内存问题
3. **详细记录**: 记录分析的每个步骤，包括：
   - 分析开始和结束
   - 意图分析过程和结果
   - 每个工具执行的详细信息
   - 执行时间统计
   - 错误和异常信息

#### 日志格式
支持两种格式（通过`LOG_JSON_FORMAT`配置）：
- **JSON格式**: 结构化日志，便于程序解析和分析
- **文本格式**: 人类可读格式，便于快速查看

#### 使用示例
```python
from app.core.logger import AnalysisLogger

# 创建分析日志记录器
analysis_logger = AnalysisLogger(project_id="project_123")

# 设置分析ID（可选）
analysis_logger.set_request_id("analysis_456")

# 记录不同级别的日志
analysis_logger.info("开始分析任务", extra={
    "event": "analysis_start",
    "query": "查询内容"
})

# 记录分析步骤
analysis_logger.log_step("步骤名称", {
    "step_id": "step_1",
    "tool_name": "SQL查询",
    "parameters": {...}
})

# 记录错误
analysis_logger.error("执行失败", extra={
    "error": "错误详情"
})
```

### 日志配置

在环境变量文件(`.env`)中配置日志相关参数：

```bash
# 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# 日志根目录
LOG_DIR=./logs

# 是否记录SQL
LOG_PRINT_SQL=true

# 是否使用JSON格式
LOG_JSON_FORMAT=true
```

### 日志查看和分析

#### 查看特定项目的分析日志
```bash
# Windows PowerShell
Get-Content -Path "logs\analysis\{project_id}\analysis.$(Get-Date -Format 'yyyy-MM-dd').log" -Tail 50

# Linux/Mac
tail -f logs/analysis/{project_id}/analysis.$(date +%Y-%m-%d).log
```

#### 搜索特定事件
```bash
# Windows PowerShell
Select-String -Path "logs\analysis\{project_id}\*.log" -Pattern "error"

# Linux/Mac
grep "error" logs/analysis/{project_id}/*.log
```

#### 分析执行时间
通过JSON格式的日志，可以轻松分析各步骤的执行时间：
```python
import json

with open('logs/analysis/project_id/analysis.2024-01-01.log', 'r') as f:
    for line in f:
        log_entry = json.loads(line)
        if 'execution_time' in log_entry:
            print(f"{log_entry['message']}: {log_entry['execution_time']}ms")
```

### 日志维护

#### 自动清理
系统配置了30天的日志保留期，超过30天的日志文件会被自动删除。

#### 手动清理
如需手动清理日志：
```bash
# Windows PowerShell - 删除30天前的日志
Get-ChildItem -Path "logs" -Recurse -Filter "*.log" | 
    Where-Object {$_.LastWriteTime -lt (Get-Date).AddDays(-30)} | 
    Remove-Item

# Linux/Mac - 删除30天前的日志
find logs -name "*.log" -mtime +30 -delete
```

#### 日志归档
对于重要的分析日志，建议定期归档：
```bash
# Windows PowerShell
Compress-Archive -Path "logs\analysis\{project_id}\*.log" -DestinationPath "archive\{project_id}_$(Get-Date -Format 'yyyy-MM').zip"

# Linux/Mac
tar -czf archive/{project_id}_$(date +%Y-%m).tar.gz logs/analysis/{project_id}/*.log
```

## 部署指南

### 环境要求

- Python 3.8+
- Node.js 14+
- MySQL/MariaDB 5.7+
- 根据需要的数据库驱动配置相应环境

### Oracle客户端配置

系统支持Oracle数据库连接，需要安装Oracle Instant Client。

#### Oracle Instant Client安装

##### Windows环境
1. 从[Oracle官网](https://www.oracle.com/database/technologies/instant-client/winx64-64-downloads.html)下载适合的Oracle Instant Client Basic或Basic Light包
2. 解压到指定目录，如`C:\oracle\instantclient_21_x`
3. 将此目录添加到系统环境变量PATH中
4. 重启应用或系统使环境变量生效

##### Linux环境(Ubuntu/Debian)
```bash
# 安装依赖
sudo apt-get update
sudo apt-get install -y libaio1

# 创建安装目录
sudo mkdir -p /opt/oracle

# 下载并解压Oracle Instant Client
cd /opt/oracle
sudo wget https://download.oracle.com/otn_software/linux/instantclient/218000/instantclient-basic-linux.x64-********.0dbru.zip
sudo unzip instantclient-basic-linux.x64-********.0dbru.zip
sudo rm instantclient-basic-linux.x64-********.0dbru.zip

# 配置环境变量
echo "export LD_LIBRARY_PATH=/opt/oracle/instantclient_21_8:$LD_LIBRARY_PATH" >> ~/.bashrc
echo "export ORACLE_HOME=/opt/oracle/instantclient_21_8" >> ~/.bashrc
source ~/.bashrc

# 更新动态链接库配置
sudo sh -c "echo /opt/oracle/instantclient_21_8 > /etc/ld.so.conf.d/oracle-instantclient.conf"
sudo ldconfig
```

#### 全局Oracle客户端初始化

系统实现了Oracle客户端的全局初始化机制，避免在不同模块中重复初始化的问题：

1. **应用启动自动初始化**：在应用启动时自动尝试初始化Oracle客户端库
2. **多路径自动检测**：自动检测和尝试多个常见的Oracle客户端安装路径
3. **全局共享**：所有需要Oracle连接的模块共享同一个已初始化的客户端

##### 自定义Oracle客户端路径

如果Oracle客户端安装在非标准位置，可通过环境变量配置：

```bash
# Linux/macOS
export ORACLE_HOME=/path/to/your/oracle/instantclient

# Windows (PowerShell)
$env:ORACLE_HOME="C:\path\to\your\oracle\instantclient"

# Windows (CMD)
set ORACLE_HOME=C:\path\to\your\oracle\instantclient
```

##### 全局初始化模块API

系统提供了`app/utils/oracle_init.py`模块，包含以下主要函数：

```python
# 初始化Oracle客户端
init_oracle_client()

# 检查Oracle客户端是否已初始化
is_oracle_initialized()

# 获取Oracle客户端版本
get_oracle_client_version()

# 创建Oracle连接(包含初始化和重试机制)
reconnect_oracle(host, port, service_name, username, password)
```

#### Docker环境配置

对于Docker环境，已在Dockerfile中配置了Oracle Instant Client的安装和初始化。如需修改客户端版本，可编辑Dockerfile中的相关部分：

```Dockerfile
# 下载并安装Oracle Instant Client
RUN mkdir -p /opt/oracle && \
    cd /opt/oracle && \
    wget https://download.oracle.com/otn_software/linux/instantclient/2380000/instantclient-basic-linux.x64-*********.04.zip && \
    unzip instantclient-basic-linux.x64-*********.04.zip && \
    rm instantclient-basic-linux.x64-*********.04.zip && \
    ln -s /opt/oracle/instantclient_23_8 /opt/oracle/instantclient && \
    echo /opt/oracle/instantclient > /etc/ld.so.conf.d/oracle-instantclient.conf && \
    ldconfig

# 配置环境变量
ENV ORACLE_HOME=/opt/oracle/instantclient
ENV LD_LIBRARY_PATH=$ORACLE_HOME
ENV PATH=$PATH:$ORACLE_HOME
```

#### 验证Oracle客户端配置

可以通过以下Python代码验证Oracle客户端是否正确配置：

```python
import cx_Oracle
# 显示客户端版本
print("Oracle Client Version:", ".".join(str(i) for i in cx_Oracle.clientversion()))
```

#### 疑难解决

1. **找不到Oracle客户端库错误(DPI-1047)**
   - 确认已正确设置环境变量`ORACLE_HOME`和`LD_LIBRARY_PATH`
   - 检查Oracle Instant Client是否已正确安装
   - 使用`ldconfig -p | grep oracle`(Linux)验证库是否可见

2. **连接测试**
   - 使用SQL*Plus测试连接: `sqlplus username/password@//host:port/service_name`
   - 或通过Python脚本测试: 
     ```python
     import cx_Oracle
     connection = cx_Oracle.connect(user="username", password="password", 
                                   dsn="host:port/service_name")
     print("连接成功!")
     connection.close()
     ```

### 后端部署

1. 创建虚拟环境
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate  # Windows
```

2. 安装依赖
```bash
pip install -r requirements.txt
```

3. 数据库初始化

**注意：从v2.1版本开始，系统已移除自动数据库初始化机制，需要手动执行初始化。**

#### 方法一：使用Python脚本初始化（推荐）
```bash
# 仅创建表结构，不包含示例数据
python init_db_manual.py
```

#### 方法二：使用SQL脚本初始化
```bash
# 创建表结构并导入示例数据
mysql -u root -p < init_database.sql
```

#### 数据库初始化说明
- **Python脚本方式**：仅创建必要的表结构，适合生产环境
- **SQL脚本方式**：包含示例数据和预置工具，适合开发和测试环境
- **手动初始化的优势**：
  - 更好的部署控制
  - 避免意外的数据覆盖
  - 支持自定义初始化流程

4. 配置环境变量
```bash
cp .env.example .env
# 编辑.env文件，配置数据库连接等信息
```

5. 启动应用
```bash
uvicorn main:app --host 0.0.0.0 --port 8000
```

### 前端部署

1. 安装依赖
```bash
cd frontend
npm install
```

2. 配置API地址
```bash
cp .env.local.example .env.local
# 编辑.env.local文件，配置API地址
```

3. 构建生产版本
```bash
npm run build
```

4. 部署静态文件
   - 可以使用Nginx等Web服务器部署`build`目录下的文件
   - 或者使用`serve -s build`临时部署

## 开发指南

### 添加新工具

1. 在`tool_type`枚举中添加新的工具类型
2. 在`app/services/executor`目录下创建新的执行器
3. 在`ExecutorFactory`中注册新的执行器
4. 添加工具结果格式化逻辑（如需要）

### 添加新的前端组件

1. 在`frontend/src/components`目录下创建新组件
2. 在相关页面中导入并使用该组件
3. 如需新的API，在`frontend/src/services/api`中添加相应方法

### 数据库架构更新

1. 编写SQL迁移脚本
2. 更新`schema_info.json`和`schema_description.md`
3. 如果添加了新字段，确保相应的模型和模式也进行了更新

### 添加新的API端点

1. 在`app/api/v1/endpoints`中创建新的端点文件
2. 在`app/api/v1/router.py`中注册新的路由
3. 添加相应的请求和响应模式
4. 实现业务逻辑，并在端点函数中调用

---

## 结论

DecisionAI 2.0是一个功能丰富、架构优良的数据分析系统，其流式分析接口使用户能够实时获取分析执行过程和结果。系统支持多种工具和展示格式，适应不同类型的数据分析需求，特别适用于金融风险分析等复杂场景。

系统采用现代化的技术栈和架构设计，具有良好的可扩展性和可维护性，便于开发团队进行持续迭代和功能扩展。